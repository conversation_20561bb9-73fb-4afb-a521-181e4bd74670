"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx":
/*!***************************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx ***!
  \***************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarEventItem: function() { return /* binding */ CalendarEventItem; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _utils_color__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/color */ \"(app-pages-browser)/./src/utils/color.ts\");\n/* harmony import */ var _utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/dateUtils */ \"(app-pages-browser)/./src/utils/dateUtils.ts\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst CalendarEventItem = (param)=>{\n    let { event, style, onClick, onContextMenu, view = \"month\", isDragging, showTitle = true, isDraggable = true } = param;\n    _s();\n    const dragRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { attributes, listeners, setNodeRef, isDragging: dndIsDragging } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDraggable)({\n        id: \"event-\".concat(event.id),\n        data: {\n            type: \"event\",\n            payload: event\n        },\n        disabled: !isDraggable\n    });\n    // Combine external isDragging with internal dndIsDragging\n    const combinedIsDragging = isDragging || dndIsDragging;\n    // Memoize event calculations\n    const eventDetails = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const start = new Date(event.start);\n        const eventHeight = (style === null || style === void 0 ? void 0 : style.height) ? parseInt(style.height.toString().replace(\"px\", \"\")) : null;\n        return {\n            start,\n            eventSize: (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.getEventSize)(eventHeight),\n            isInstant: (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.isInstantEvent)(event),\n            formattedTime: (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.formatEventTime)(start, view, {\n                shortFormat: true\n            })\n        };\n    }, [\n        event,\n        style,\n        view\n    ]);\n    // Memoize styling\n    const eventStyles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const denimColorInfo = (0,_utils_color__WEBPACK_IMPORTED_MODULE_3__.ColorInfo)(\"Denim\");\n        // Extract RGB values from the rgba string and make it fully opaque\n        const rgbMatch = denimColorInfo.bg.match(/rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)/);\n        const opaqueBackground = rgbMatch ? \"rgb(\".concat(rgbMatch[1], \", \").concat(rgbMatch[2], \", \").concat(rgbMatch[3], \")\") : denimColorInfo.bg;\n        return {\n            ...style,\n            backgroundColor: opaqueBackground,\n            minHeight: view === \"month\" ? \"24px\" : \"30px\",\n            marginBottom: view === \"month\" ? \"4px\" : \"0px\",\n            // Add subtle shadow for better visual depth\n            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)\",\n            opacity: combinedIsDragging ? 0.5 : isDraggable ? 1 : 0.7\n        };\n    }, [\n        style,\n        view,\n        combinedIsDragging,\n        isDraggable\n    ]);\n    // Memoize classes\n    const eventClasses = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        // Month view or small events\n        if (view === \"month\" || eventDetails.eventSize === \"small\") {\n            return {\n                baseClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-md select-none text-black text-xs overflow-hidden\", !combinedIsDragging && isDraggable && \"cursor-pointer\", !combinedIsDragging && !isDraggable && \"cursor-default\", \"p-1\"),\n                containerClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-1\", \"flex-nowrap\"),\n                titleClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-medium truncate leading-tight text-xs overflow-hidden\", \"max-w-[70%]\"),\n                timeClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"opacity-75 text-xs flex-shrink-0\", \"text-[0.65rem]\")\n            };\n        }\n        // Day and Week views for medium/large events\n        return {\n            baseClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-md select-none text-black text-xs overflow-hidden\", !combinedIsDragging && isDraggable && \"cursor-pointer\", !combinedIsDragging && !isDraggable && \"cursor-default\", \"p-2\"),\n            containerClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col\", \"space-y-0.5\"),\n            titleClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-medium truncate leading-tight text-xs overflow-hidden\"),\n            timeClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"opacity-75 text-xs\")\n        };\n    }, [\n        eventDetails,\n        view,\n        combinedIsDragging,\n        isDraggable\n    ]);\n    // Render event content based on view and size\n    const renderEventContent = ()=>{\n        // Month view or small events\n        if (view === \"month\" || eventDetails.eventSize === \"small\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: eventClasses.containerClasses,\n                children: [\n                    showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: eventClasses.titleClasses,\n                        children: event.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 13\n                    }, undefined),\n                    showTitle && eventDetails.formattedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: eventClasses.timeClasses,\n                        children: eventDetails.formattedTime\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, undefined);\n        }\n        // Day and Week views for medium/large events\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: eventClasses.containerClasses,\n            children: [\n                showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: eventClasses.titleClasses,\n                    children: event.title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 11\n                }, undefined),\n                showTitle && eventDetails.formattedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: eventClasses.timeClasses,\n                    children: eventDetails.formattedTime\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n            lineNumber: 145,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"event-\".concat(event.id),\n        ref: (node)=>{\n            setNodeRef(node);\n            dragRef.current = node;\n        },\n        style: {\n            ...eventStyles,\n            pointerEvents: combinedIsDragging ? \"none\" : \"auto\",\n            zIndex: combinedIsDragging ? 1000 : \"auto\" // Ensure dragged item is on top\n        },\n        className: eventClasses.baseClasses,\n        onClick: onClick,\n        onContextMenu: onContextMenu,\n        ...isDraggable ? {\n            ...listeners,\n            ...attributes\n        } : {},\n        children: renderEventContent()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, undefined);\n}; // import React, { useMemo } from 'react';\n // import { cn } from '@/lib/utils';\n // import { CalendarEvent } from '@/typings/page';\n // import { ColorInfo } from '@/utils/color';\n // import { \n //   formatEventTime, \n //   isInstantEvent, \n //   getEventSize \n // } from '@/utils/dateUtils';\n // export const CalendarEventItem = ({\n //   event,\n //   style,\n //   onClick,\n //   onContextMenu,\n //   view = 'month' // Default to month view if not specified\n // }: {\n //   event: CalendarEvent;\n //   style?: React.CSSProperties;\n //   onClick: (e: React.MouseEvent) => void;\n //   onContextMenu?: (e: React.MouseEvent) => void;\n //   view?: 'day' | 'week' | 'month';\n // }) => {\n //   // Memoize event calculations\n //   const eventDetails = useMemo(() => {\n //     const start = new Date(event.start);\n //     const eventHeight = style?.height ? parseInt(style.height.toString().replace('px', '')) : null;\n //     return { \n //       start, \n //       eventSize: getEventSize(eventHeight),\n //       isInstant: isInstantEvent(event),\n //       formattedTime: formatEventTime(start, view, { shortFormat: true })\n //     };\n //   }, [event, style, view]);\n //   // Memoize styling\n //   const eventStyles = useMemo(() => {\n //     const denimColorInfo = ColorInfo('Denim');\n //     return {\n //       ...style,\n //       backgroundColor: denimColorInfo.bg,\n //       minHeight: '30px',\n //     };\n //   }, [style]);\n //   // Memoize classes\n //   const eventClasses = useMemo(() => {\n //     // Month view or small events\n //     if (view === 'month' || eventDetails.eventSize === 'small') {\n //       return {\n //         baseClasses: cn(\n //           \"rounded-md cursor-pointer select-none text-black text-xs overflow-hidden\",\n //           eventDetails.eventSize === 'small' ? \"p-1\" : \"p-2\"\n //         ),\n //         containerClasses: cn(\n //           \"flex items-center space-x-1\",\n //           \"flex-nowrap\"\n //         ),\n //         titleClasses: cn(\n //           \"font-medium truncate leading-tight text-xs overflow-hidden flex-grow\",\n //           \"max-w-[70%]\"\n //         ),\n //         timeClasses: cn(\n //           \"opacity-75 text-xs flex-shrink-0\",\n //           \"text-[0.65rem]\"\n //         )\n //       };\n //     }\n //     // Day and Week views for medium/large events\n //     return {\n //       baseClasses: cn(\n //         \"rounded-md cursor-pointer select-none text-black text-xs overflow-hidden\",\n //         eventDetails.eventSize === 'small' ? \"p-1\" : \"p-2\"\n //       ),\n //       containerClasses: cn(\n //         \"flex flex-col\",\n //         eventDetails.eventSize === 'small' ? \"space-y-0\" : \"space-y-0.5\"\n //       ),\n //       titleClasses: cn(\n //         \"font-medium truncate leading-tight text-xs overflow-hidden\",\n //         eventDetails.eventSize === 'small' ? \"mb-0\" : \"\"\n //       ),\n //       timeClasses: cn(\n //         \"opacity-75 text-xs\",\n //         eventDetails.eventSize === 'small' ? \"text-[0.65rem] leading-none\" : \"\"\n //       )\n //     };\n //   }, [eventDetails, view]);\n //   // Render event content based on view and size\n //   const renderEventContent = () => {\n //     // Month view or small events\n //     if (view === 'month' || eventDetails.eventSize === 'small') {\n //       return (\n //         <div className={eventClasses.containerClasses}>\n //           <span className={eventClasses.titleClasses}>{event.title}</span>\n //           <span className={eventClasses.timeClasses}>\n //             {eventDetails.formattedTime}\n //           </span>\n //         </div>\n //       );\n //     }\n //     // Day and Week views for medium/large events\n //     return (\n //       <div className={eventClasses.containerClasses}>\n //         <div className={eventClasses.titleClasses}>{event.title}</div>\n //         <div className={eventClasses.timeClasses}>\n //           {eventDetails.formattedTime}\n //         </div>\n //       </div>\n //     );\n //   };\n //   return (\n //     <div\n //       style={eventStyles}\n //       className={eventClasses.baseClasses}\n //       onClick={onClick}\n //       onContextMenu={onContextMenu}\n //     >\n //       {renderEventContent()}\n //     </div>\n //   );\n // }; \n_s(CalendarEventItem, \"kiz8nz2pAp5RwnBQHmiQwwayVv8=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDraggable\n    ];\n});\n_c = CalendarEventItem;\nvar _c;\n$RefreshReg$(_c, \"CalendarEventItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\n"));

/***/ })

});