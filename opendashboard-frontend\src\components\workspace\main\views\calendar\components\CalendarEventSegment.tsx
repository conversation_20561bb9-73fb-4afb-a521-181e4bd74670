import React, { useMemo, useRef } from 'react';
import { cn } from '@/lib/utils';
import { CalendarEvent } from '@/typings/page';
import { ColorInfo } from '@/utils/color';
import { 
  formatEventTime, 
  getEventSize 
} from '@/utils/dateUtils';
import { 
  EventSegment, 
  getSegmentStylingClasses, 
  shouldShowTimeInSegment, 
  getSegmentContinuationText 
} from '@/utils/multiDayEventUtils';
import { MultiDayEventBadge, ContinuationArrow } from './MultiDayEventBadge';
import { useDraggable } from '@dnd-kit/core';

export const CalendarEventSegment = ({
  segment,
  style,
  onClick,
  onContextMenu,
  view = 'month',
  isEndOfEvent,
  isDragging
}: {
  segment: EventSegment;
  style?: React.CSSProperties;
  onClick: (e: React.MouseEvent) => void;
  onContextMenu?: (e: React.MouseEvent) => void;
  view?: 'day' | 'week' | 'month';
  isEndOfEvent?: boolean;
  isDragging?: boolean;
}) => {
  const dragRef = useRef<HTMLDivElement>(null);
  const { attributes, listeners, setNodeRef, isDragging: dndIsDragging } = useDraggable({
    id: `segment-${segment.id}`,
    data: {
      type: 'segment',
      payload: segment,
    },
  });

  const combinedIsDragging = isDragging || dndIsDragging;

  // Memoize event calculations
  const eventDetails = useMemo(() => {
    const eventHeight = style?.height ? parseInt(style.height.toString().replace('px', '')) : null;
    const showTime = shouldShowTimeInSegment(segment, view);
    const continuationText = getSegmentContinuationText(segment);
    
    // let formattedTime = null;
    // if (segment.isAllDay) {
    //   formattedTime = 'All day';
    // } else if (showTime) {
    //   formattedTime = formatEventTime(segment.startTime, view, { shortFormat: true });
    // }

    return { 
      eventSize: getEventSize(eventHeight),
      showTime,
      continuationText,
      formattedTime: showTime ? formatEventTime(segment.startTime, view, { shortFormat: true }) : null
    };
  }, [segment, style, view]);

  // Memoize styling
  const eventStyles = useMemo(() => {
    const denimColorInfo = ColorInfo('Denim');
    const stylingClasses = getSegmentStylingClasses(segment);
    
    // Extract RGB values from the rgba string and make it fully opaque
    const rgbMatch = denimColorInfo.bg.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
    const opaqueBackground = rgbMatch 
      ? `rgb(${rgbMatch[1]}, ${rgbMatch[2]}, ${rgbMatch[3]})`
      : denimColorInfo.bg;
    
    return {
      style: {
        ...style,
        backgroundColor: opaqueBackground,
        minHeight: '24px', // Adjusted for consistency
        // Add subtle shadow for better visual depth
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',
        opacity: combinedIsDragging ? 0.5 : 1,
      },
      classes: stylingClasses
    };
  }, [style, segment, combinedIsDragging]);

  // Memoize classes
  const eventClasses = useMemo(() => {
    const baseClasses = cn(
      "select-none text-black text-xs overflow-hidden relative",
      !combinedIsDragging && "cursor-pointer",
      eventStyles.classes.roundedCorners,
      eventStyles.classes.continuationIndicator,
      eventStyles.classes.opacity,
      "p-1",
    );

    // Month view or small events
    if (view === 'month' || eventDetails.eventSize === 'small') {
      return {
        baseClasses,
        containerClasses: cn(
          "flex items-center space-x-1 flex-nowrap"
        ),
        titleClasses: cn(
          "font-medium truncate leading-tight text-xs overflow-hidden",
          segment.isMultiDay ? "max-w-[60%]" : "max-w-[70%]"
        ),
        timeClasses: cn(
          "opacity-75 text-xs flex-shrink-0 text-[0.65rem]"
        ),
        continuationClasses: cn(
          "text-xs opacity-60 flex-shrink-0 text-[0.6rem]"
        )
      };
    }

    // Day and Week views for medium/large events
    return {
      baseClasses: cn(baseClasses, "p-2"),
      containerClasses: cn(
        "flex flex-col",
        "space-y-0.5"
      ),
      titleClasses: cn(
        "font-medium truncate leading-tight text-xs overflow-hidden"
      ),
      timeClasses: cn(
        "opacity-75 text-xs"
      ),
      continuationClasses: cn(
        "text-xs opacity-60"
      )
    };
  }, [eventDetails, view, segment.isMultiDay, eventStyles.classes, combinedIsDragging]);

  // Render event content based on view and size
  const renderEventContent = () => {
    const event = segment.originalEvent;
    
    // Month view or small events - horizontal layout
    if (view === 'month' || eventDetails.eventSize === 'small') {
      return (
        <div className={eventClasses.containerClasses}>
          <span className={eventClasses.titleClasses}>{event.title}</span>
          {eventDetails.showTime && eventDetails.formattedTime && (
            <span className={eventClasses.timeClasses}>
              {eventDetails.formattedTime}
            </span>
          )}
          {segment.isMultiDay && (
            <MultiDayEventBadge
              segment={segment}
              view={view}
              size="small"
              className={eventClasses.continuationClasses}
              isEndOfEvent={isEndOfEvent}
            />
          )}
        </div>
      );
    }

    // Day and Week views - vertical layout
    return (
      <div className={eventClasses.containerClasses}>
        <div className={eventClasses.titleClasses}>
          {event.title}
          {segment.isMultiDay && !eventDetails.showTime && (
            <ContinuationArrow
              direction={segment.isFirstSegment ? 'right' : segment.isLastSegment ? 'left' : 'both'}
              className="ml-1"
            />
          )}
        </div>
        {(eventDetails.showTime || segment.isAllDay) && eventDetails.formattedTime && (
          <div className={eventClasses.timeClasses}>
            {eventDetails.formattedTime}
            {segment.isMultiDay && (
              <ContinuationArrow
                direction={segment.isFirstSegment ? 'right' : segment.isLastSegment ? 'left' : 'both'}
                className="ml-1"
              />
            )}
          </div>
        )}
        {segment.isMultiDay && (
          <div className={eventClasses.continuationClasses}>
            <MultiDayEventBadge
              segment={segment}
              view={view}
              size={eventDetails.eventSize === 'large' ? 'medium' : 'small'}
              isEndOfEvent={isEndOfEvent}
            />
          </div>
        )}
      </div>
    );
  };

  return (
    <div
      id={`event-${segment.originalEvent.id}`}
      ref={(node) => {
        setNodeRef(node);
        (dragRef as React.MutableRefObject<HTMLDivElement | null>).current = node;
      }}
      style={{
        ...eventStyles.style,
        zIndex: combinedIsDragging ? 1000 : 'auto' // Ensure dragged item is on top
      }}
      className={eventClasses.baseClasses}
      onClick={onClick}
      onContextMenu={onContextMenu}
      {...listeners}
      {...attributes}
    >
      {renderEventContent()}
    </div>
  );
}; 