"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-definitions";
exports.ids = ["vendor-chunks/mdast-util-definitions"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-definitions/lib/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/mdast-util-definitions/lib/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   definitions: () => (/* binding */ definitions)\n/* harmony export */ });\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/**\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').Definition} Definition\n */\n\n/**\n * @typedef {Root | Content} Node\n *\n * @callback GetDefinition\n *   Get a definition by identifier.\n * @param {string | null | undefined} [identifier]\n *   Identifier of definition.\n * @returns {Definition | null}\n *   Definition corresponding to `identifier` or `null`.\n */\n\n\n\nconst own = {}.hasOwnProperty\n\n/**\n * Find definitions in `tree`.\n *\n * Uses CommonMark precedence, which means that earlier definitions are\n * preferred over duplicate later definitions.\n *\n * @param {Node} tree\n *   Tree to check.\n * @returns {GetDefinition}\n *   Getter.\n */\nfunction definitions(tree) {\n  /** @type {Record<string, Definition>} */\n  const cache = Object.create(null)\n\n  if (!tree || !tree.type) {\n    throw new Error('mdast-util-definitions expected node')\n  }\n\n  (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_0__.visit)(tree, 'definition', (definition) => {\n    const id = clean(definition.identifier)\n    if (id && !own.call(cache, id)) {\n      cache[id] = definition\n    }\n  })\n\n  return definition\n\n  /** @type {GetDefinition} */\n  function definition(identifier) {\n    const id = clean(identifier)\n    // To do: next major: return `undefined` when not found.\n    return id && own.call(cache, id) ? cache[id] : null\n  }\n}\n\n/**\n * @param {string | null | undefined} [value]\n * @returns {string}\n */\nfunction clean(value) {\n  return String(value || '').toUpperCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-definitions/lib/index.js\n");

/***/ })

};
;