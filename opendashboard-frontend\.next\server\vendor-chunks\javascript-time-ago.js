"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/javascript-time-ago";
exports.ids = ["vendor-chunks/javascript-time-ago"];
exports.modules = {

/***/ "(ssr)/./node_modules/javascript-time-ago/locale/en.json.js":
/*!************************************************************!*\
  !*** ./node_modules/javascript-time-ago/locale/en.json.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n\t\"locale\": \"en\",\n\t\"long\": {\n\t\t\"year\": {\n\t\t\t\"previous\": \"last year\",\n\t\t\t\"current\": \"this year\",\n\t\t\t\"next\": \"next year\",\n\t\t\t\"past\": {\n\t\t\t\t\"one\": \"{0} year ago\",\n\t\t\t\t\"other\": \"{0} years ago\"\n\t\t\t},\n\t\t\t\"future\": {\n\t\t\t\t\"one\": \"in {0} year\",\n\t\t\t\t\"other\": \"in {0} years\"\n\t\t\t}\n\t\t},\n\t\t\"quarter\": {\n\t\t\t\"previous\": \"last quarter\",\n\t\t\t\"current\": \"this quarter\",\n\t\t\t\"next\": \"next quarter\",\n\t\t\t\"past\": {\n\t\t\t\t\"one\": \"{0} quarter ago\",\n\t\t\t\t\"other\": \"{0} quarters ago\"\n\t\t\t},\n\t\t\t\"future\": {\n\t\t\t\t\"one\": \"in {0} quarter\",\n\t\t\t\t\"other\": \"in {0} quarters\"\n\t\t\t}\n\t\t},\n\t\t\"month\": {\n\t\t\t\"previous\": \"last month\",\n\t\t\t\"current\": \"this month\",\n\t\t\t\"next\": \"next month\",\n\t\t\t\"past\": {\n\t\t\t\t\"one\": \"{0} month ago\",\n\t\t\t\t\"other\": \"{0} months ago\"\n\t\t\t},\n\t\t\t\"future\": {\n\t\t\t\t\"one\": \"in {0} month\",\n\t\t\t\t\"other\": \"in {0} months\"\n\t\t\t}\n\t\t},\n\t\t\"week\": {\n\t\t\t\"previous\": \"last week\",\n\t\t\t\"current\": \"this week\",\n\t\t\t\"next\": \"next week\",\n\t\t\t\"past\": {\n\t\t\t\t\"one\": \"{0} week ago\",\n\t\t\t\t\"other\": \"{0} weeks ago\"\n\t\t\t},\n\t\t\t\"future\": {\n\t\t\t\t\"one\": \"in {0} week\",\n\t\t\t\t\"other\": \"in {0} weeks\"\n\t\t\t}\n\t\t},\n\t\t\"day\": {\n\t\t\t\"previous\": \"yesterday\",\n\t\t\t\"current\": \"today\",\n\t\t\t\"next\": \"tomorrow\",\n\t\t\t\"past\": {\n\t\t\t\t\"one\": \"{0} day ago\",\n\t\t\t\t\"other\": \"{0} days ago\"\n\t\t\t},\n\t\t\t\"future\": {\n\t\t\t\t\"one\": \"in {0} day\",\n\t\t\t\t\"other\": \"in {0} days\"\n\t\t\t}\n\t\t},\n\t\t\"hour\": {\n\t\t\t\"current\": \"this hour\",\n\t\t\t\"past\": {\n\t\t\t\t\"one\": \"{0} hour ago\",\n\t\t\t\t\"other\": \"{0} hours ago\"\n\t\t\t},\n\t\t\t\"future\": {\n\t\t\t\t\"one\": \"in {0} hour\",\n\t\t\t\t\"other\": \"in {0} hours\"\n\t\t\t}\n\t\t},\n\t\t\"minute\": {\n\t\t\t\"current\": \"this minute\",\n\t\t\t\"past\": {\n\t\t\t\t\"one\": \"{0} minute ago\",\n\t\t\t\t\"other\": \"{0} minutes ago\"\n\t\t\t},\n\t\t\t\"future\": {\n\t\t\t\t\"one\": \"in {0} minute\",\n\t\t\t\t\"other\": \"in {0} minutes\"\n\t\t\t}\n\t\t},\n\t\t\"second\": {\n\t\t\t\"current\": \"now\",\n\t\t\t\"past\": {\n\t\t\t\t\"one\": \"{0} second ago\",\n\t\t\t\t\"other\": \"{0} seconds ago\"\n\t\t\t},\n\t\t\t\"future\": {\n\t\t\t\t\"one\": \"in {0} second\",\n\t\t\t\t\"other\": \"in {0} seconds\"\n\t\t\t}\n\t\t}\n\t},\n\t\"short\": {\n\t\t\"year\": {\n\t\t\t\"previous\": \"last yr.\",\n\t\t\t\"current\": \"this yr.\",\n\t\t\t\"next\": \"next yr.\",\n\t\t\t\"past\": \"{0} yr. ago\",\n\t\t\t\"future\": \"in {0} yr.\"\n\t\t},\n\t\t\"quarter\": {\n\t\t\t\"previous\": \"last qtr.\",\n\t\t\t\"current\": \"this qtr.\",\n\t\t\t\"next\": \"next qtr.\",\n\t\t\t\"past\": {\n\t\t\t\t\"one\": \"{0} qtr. ago\",\n\t\t\t\t\"other\": \"{0} qtrs. ago\"\n\t\t\t},\n\t\t\t\"future\": {\n\t\t\t\t\"one\": \"in {0} qtr.\",\n\t\t\t\t\"other\": \"in {0} qtrs.\"\n\t\t\t}\n\t\t},\n\t\t\"month\": {\n\t\t\t\"previous\": \"last mo.\",\n\t\t\t\"current\": \"this mo.\",\n\t\t\t\"next\": \"next mo.\",\n\t\t\t\"past\": \"{0} mo. ago\",\n\t\t\t\"future\": \"in {0} mo.\"\n\t\t},\n\t\t\"week\": {\n\t\t\t\"previous\": \"last wk.\",\n\t\t\t\"current\": \"this wk.\",\n\t\t\t\"next\": \"next wk.\",\n\t\t\t\"past\": \"{0} wk. ago\",\n\t\t\t\"future\": \"in {0} wk.\"\n\t\t},\n\t\t\"day\": {\n\t\t\t\"previous\": \"yesterday\",\n\t\t\t\"current\": \"today\",\n\t\t\t\"next\": \"tomorrow\",\n\t\t\t\"past\": {\n\t\t\t\t\"one\": \"{0} day ago\",\n\t\t\t\t\"other\": \"{0} days ago\"\n\t\t\t},\n\t\t\t\"future\": {\n\t\t\t\t\"one\": \"in {0} day\",\n\t\t\t\t\"other\": \"in {0} days\"\n\t\t\t}\n\t\t},\n\t\t\"hour\": {\n\t\t\t\"current\": \"this hour\",\n\t\t\t\"past\": \"{0} hr. ago\",\n\t\t\t\"future\": \"in {0} hr.\"\n\t\t},\n\t\t\"minute\": {\n\t\t\t\"current\": \"this minute\",\n\t\t\t\"past\": \"{0} min. ago\",\n\t\t\t\"future\": \"in {0} min.\"\n\t\t},\n\t\t\"second\": {\n\t\t\t\"current\": \"now\",\n\t\t\t\"past\": \"{0} sec. ago\",\n\t\t\t\"future\": \"in {0} sec.\"\n\t\t}\n\t},\n\t\"narrow\": {\n\t\t\"year\": {\n\t\t\t\"previous\": \"last yr.\",\n\t\t\t\"current\": \"this yr.\",\n\t\t\t\"next\": \"next yr.\",\n\t\t\t\"past\": \"{0}y ago\",\n\t\t\t\"future\": \"in {0}y\"\n\t\t},\n\t\t\"quarter\": {\n\t\t\t\"previous\": \"last qtr.\",\n\t\t\t\"current\": \"this qtr.\",\n\t\t\t\"next\": \"next qtr.\",\n\t\t\t\"past\": \"{0}q ago\",\n\t\t\t\"future\": \"in {0}q\"\n\t\t},\n\t\t\"month\": {\n\t\t\t\"previous\": \"last mo.\",\n\t\t\t\"current\": \"this mo.\",\n\t\t\t\"next\": \"next mo.\",\n\t\t\t\"past\": \"{0}mo ago\",\n\t\t\t\"future\": \"in {0}mo\"\n\t\t},\n\t\t\"week\": {\n\t\t\t\"previous\": \"last wk.\",\n\t\t\t\"current\": \"this wk.\",\n\t\t\t\"next\": \"next wk.\",\n\t\t\t\"past\": \"{0}w ago\",\n\t\t\t\"future\": \"in {0}w\"\n\t\t},\n\t\t\"day\": {\n\t\t\t\"previous\": \"yesterday\",\n\t\t\t\"current\": \"today\",\n\t\t\t\"next\": \"tomorrow\",\n\t\t\t\"past\": \"{0}d ago\",\n\t\t\t\"future\": \"in {0}d\"\n\t\t},\n\t\t\"hour\": {\n\t\t\t\"current\": \"this hour\",\n\t\t\t\"past\": \"{0}h ago\",\n\t\t\t\"future\": \"in {0}h\"\n\t\t},\n\t\t\"minute\": {\n\t\t\t\"current\": \"this minute\",\n\t\t\t\"past\": \"{0}m ago\",\n\t\t\t\"future\": \"in {0}m\"\n\t\t},\n\t\t\"second\": {\n\t\t\t\"current\": \"now\",\n\t\t\t\"past\": \"{0}s ago\",\n\t\t\t\"future\": \"in {0}s\"\n\t\t}\n\t},\n\t\"now\": {\n\t\t\"now\": {\n\t\t\t\"current\": \"now\",\n\t\t\t\"future\": \"in a moment\",\n\t\t\t\"past\": \"just now\"\n\t\t}\n\t},\n\t\"mini\": {\n\t\t\"year\": \"{0}yr\",\n\t\t\"month\": \"{0}mo\",\n\t\t\"week\": \"{0}wk\",\n\t\t\"day\": \"{0}d\",\n\t\t\"hour\": \"{0}h\",\n\t\t\"minute\": \"{0}m\",\n\t\t\"second\": \"{0}s\",\n\t\t\"now\": \"now\"\n\t},\n\t\"short-time\": {\n\t\t\"year\": \"{0} yr.\",\n\t\t\"month\": \"{0} mo.\",\n\t\t\"week\": \"{0} wk.\",\n\t\t\"day\": {\n\t\t\t\"one\": \"{0} day\",\n\t\t\t\"other\": \"{0} days\"\n\t\t},\n\t\t\"hour\": \"{0} hr.\",\n\t\t\"minute\": \"{0} min.\",\n\t\t\"second\": \"{0} sec.\"\n\t},\n\t\"long-time\": {\n\t\t\"year\": {\n\t\t\t\"one\": \"{0} year\",\n\t\t\t\"other\": \"{0} years\"\n\t\t},\n\t\t\"month\": {\n\t\t\t\"one\": \"{0} month\",\n\t\t\t\"other\": \"{0} months\"\n\t\t},\n\t\t\"week\": {\n\t\t\t\"one\": \"{0} week\",\n\t\t\t\"other\": \"{0} weeks\"\n\t\t},\n\t\t\"day\": {\n\t\t\t\"one\": \"{0} day\",\n\t\t\t\"other\": \"{0} days\"\n\t\t},\n\t\t\"hour\": {\n\t\t\t\"one\": \"{0} hour\",\n\t\t\t\"other\": \"{0} hours\"\n\t\t},\n\t\t\"minute\": {\n\t\t\t\"one\": \"{0} minute\",\n\t\t\t\"other\": \"{0} minutes\"\n\t\t},\n\t\t\"second\": {\n\t\t\t\"one\": \"{0} second\",\n\t\t\t\"other\": \"{0} seconds\"\n\t\t}\n\t}\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/locale/en.json.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/LocaleDataStore.js":
/*!*********************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/LocaleDataStore.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addLocaleData: () => (/* binding */ addLocaleData),\n/* harmony export */   getLocaleData: () => (/* binding */ getLocaleData)\n/* harmony export */ });\n// For all locales added\n// their relative time formatter messages will be stored here.\nvar localesData = {};\nfunction getLocaleData(locale) {\n  return localesData[locale];\n}\nfunction addLocaleData(localeData) {\n  if (!localeData) {\n    throw new Error('[javascript-time-ago] No locale data passed.');\n  } // This locale data is stored in a global variable\n  // and later used when calling `.format(time)`.\n\n\n  localesData[localeData.locale] = localeData;\n}\n//# sourceMappingURL=LocaleDataStore.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvamF2YXNjcmlwdC10aW1lLWFnby9tb2R1bGVzL0xvY2FsZURhdGFTdG9yZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBLElBQUk7QUFDSjs7O0FBR0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9qYXZhc2NyaXB0LXRpbWUtYWdvL21vZHVsZXMvTG9jYWxlRGF0YVN0b3JlLmpzP2Y3ZGQiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRm9yIGFsbCBsb2NhbGVzIGFkZGVkXG4vLyB0aGVpciByZWxhdGl2ZSB0aW1lIGZvcm1hdHRlciBtZXNzYWdlcyB3aWxsIGJlIHN0b3JlZCBoZXJlLlxudmFyIGxvY2FsZXNEYXRhID0ge307XG5leHBvcnQgZnVuY3Rpb24gZ2V0TG9jYWxlRGF0YShsb2NhbGUpIHtcbiAgcmV0dXJuIGxvY2FsZXNEYXRhW2xvY2FsZV07XG59XG5leHBvcnQgZnVuY3Rpb24gYWRkTG9jYWxlRGF0YShsb2NhbGVEYXRhKSB7XG4gIGlmICghbG9jYWxlRGF0YSkge1xuICAgIHRocm93IG5ldyBFcnJvcignW2phdmFzY3JpcHQtdGltZS1hZ29dIE5vIGxvY2FsZSBkYXRhIHBhc3NlZC4nKTtcbiAgfSAvLyBUaGlzIGxvY2FsZSBkYXRhIGlzIHN0b3JlZCBpbiBhIGdsb2JhbCB2YXJpYWJsZVxuICAvLyBhbmQgbGF0ZXIgdXNlZCB3aGVuIGNhbGxpbmcgYC5mb3JtYXQodGltZSlgLlxuXG5cbiAgbG9jYWxlc0RhdGFbbG9jYWxlRGF0YS5sb2NhbGVdID0gbG9jYWxlRGF0YTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPUxvY2FsZURhdGFTdG9yZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/LocaleDataStore.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/TimeAgo.js":
/*!*************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/TimeAgo.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TimeAgo)\n/* harmony export */ });\n/* harmony import */ var relative_time_format__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! relative-time-format */ \"(ssr)/./node_modules/relative-time-format/modules/RelativeTimeFormat.js\");\n/* harmony import */ var _cache_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./cache.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/cache.js\");\n/* harmony import */ var _locale_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./locale.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/locale.js\");\n/* harmony import */ var _isStyleObject_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./isStyleObject.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/isStyleObject.js\");\n/* harmony import */ var _steps_getStep_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./steps/getStep.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/steps/getStep.js\");\n/* harmony import */ var _steps_getStepDenominator_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./steps/getStepDenominator.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/steps/getStepDenominator.js\");\n/* harmony import */ var _steps_getTimeToNextUpdate_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./steps/getTimeToNextUpdate.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/steps/getTimeToNextUpdate.js\");\n/* harmony import */ var _LocaleDataStore_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./LocaleDataStore.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/LocaleDataStore.js\");\n/* harmony import */ var _style_roundMinute_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./style/roundMinute.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/roundMinute.js\");\n/* harmony import */ var _style_getStyleByName_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./style/getStyleByName.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/getStyleByName.js\");\n/* harmony import */ var _round_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./round.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/round.js\");\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nfunction _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\n\n\n\n\n\n\n\n\n\n\n // Valid time units.\n\nvar UNITS = ['now', // The rest are the same as in `Intl.RelativeTimeFormat`.\n'second', 'minute', 'hour', 'day', 'week', 'month', 'quarter', 'year'];\n\nvar TimeAgo = /*#__PURE__*/function () {\n  /**\r\n   * @param {(string|string[])} locales=[] - Preferred locales (or locale).\r\n   * @param {boolean} [polyfill] — Pass `false` to use native `Intl.RelativeTimeFormat` and `Intl.PluralRules` instead of the polyfills.\r\n   */\n  function TimeAgo() {\n    var locales = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n\n    var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n        polyfill = _ref.polyfill;\n\n    _classCallCheck(this, TimeAgo);\n\n    // Convert `locales` to an array.\n    if (typeof locales === 'string') {\n      locales = [locales];\n    } // Choose the most appropriate locale\n    // from the list of `locales` added by the user.\n    // For example, new TimeAgo(\"en-US\") -> \"en\".\n\n\n    this.locale = (0,_locale_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(locales.concat(TimeAgo.getDefaultLocale()), _LocaleDataStore_js__WEBPACK_IMPORTED_MODULE_1__.getLocaleData);\n\n    if (typeof Intl !== 'undefined') {\n      // Use `Intl.NumberFormat` for formatting numbers (when available).\n      if (Intl.NumberFormat) {\n        this.numberFormat = new Intl.NumberFormat(this.locale);\n      }\n    } // Some people have requested the ability to use native\n    // `Intl.RelativeTimeFormat` and `Intl.PluralRules`\n    // instead of the polyfills.\n    // https://github.com/catamphetamine/javascript-time-ago/issues/21\n\n\n    if (polyfill === false) {\n      this.IntlRelativeTimeFormat = Intl.RelativeTimeFormat;\n      this.IntlPluralRules = Intl.PluralRules;\n    } else {\n      this.IntlRelativeTimeFormat = relative_time_format__WEBPACK_IMPORTED_MODULE_2__[\"default\"];\n      this.IntlPluralRules = relative_time_format__WEBPACK_IMPORTED_MODULE_2__[\"default\"].PluralRules;\n    } // Cache `Intl.RelativeTimeFormat` instance.\n\n\n    this.relativeTimeFormatCache = new _cache_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"](); // Cache `Intl.PluralRules` instance.\n\n    this.pluralRulesCache = new _cache_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]();\n  }\n  /**\r\n   * Formats relative date/time.\r\n   *\r\n   * @param {(number|Date)} input — A `Date` or a javascript timestamp.\r\n   *\r\n   * @param {(string|object)} style — Date/time formatting style. Either one of the built-in style names or a \"custom\" style definition object having `steps: object[]` and `labels: string[]`.\r\n   *\r\n   * @param {number} [options.now] - Sets the current date timestamp.\r\n   *\r\n   * @param  {boolean} [options.future] — Tells how to format value `0`:\r\n   *         as \"future\" (`true`) or \"past\" (`false`).\r\n   *         Is `false` by default, but should have been `true` actually,\r\n   *         in order to correspond to `Intl.RelativeTimeFormat`\r\n   *         that uses `future` formatting for `0` unless `-0` is passed.\r\n   *\r\n   * @param {string} [options.round] — Rounding method. Overrides the style's one.\r\n   *\r\n   * @param {boolean} [options.getTimeToNextUpdate] — Pass `true` to return `[formattedDate, timeToNextUpdate]` instead of just `formattedDate`.\r\n   *\r\n   * @return {string} The formatted relative date/time. If no eligible `step` is found, then an empty string is returned.\r\n   */\n\n\n  _createClass(TimeAgo, [{\n    key: \"format\",\n    value: function format(input, style, options) {\n      if (!options) {\n        if (style && !isStyle(style)) {\n          options = style;\n          style = undefined;\n        } else {\n          options = {};\n        }\n      }\n\n      if (!style) {\n        style = _style_roundMinute_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n      }\n\n      if (typeof style === 'string') {\n        style = (0,_style_getStyleByName_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(style);\n      }\n\n      var timestamp = getTimestamp(input); // Get locale messages for this type of labels.\n      // \"flavour\" is a legacy name for \"labels\".\n\n      var _this$getLabels = this.getLabels(style.flavour || style.labels),\n          labels = _this$getLabels.labels,\n          labelsType = _this$getLabels.labelsType;\n\n      var now; // Can pass a custom `now`, e.g. for testing purposes.\n      //\n      // Legacy way was passing `now` in `style`.\n      // That way is deprecated.\n\n      if (style.now !== undefined) {\n        now = style.now;\n      } // The new way is passing `now` option to `.format()`.\n\n\n      if (now === undefined && options.now !== undefined) {\n        now = options.now;\n      }\n\n      if (now === undefined) {\n        now = Date.now();\n      } // how much time has passed (in seconds)\n\n\n      var secondsPassed = (now - timestamp) / 1000; // in seconds\n\n      var future = options.future || secondsPassed < 0;\n      var nowLabel = getNowLabel(labels, (0,_LocaleDataStore_js__WEBPACK_IMPORTED_MODULE_1__.getLocaleData)(this.locale).now, (0,_LocaleDataStore_js__WEBPACK_IMPORTED_MODULE_1__.getLocaleData)(this.locale)[\"long\"], future); // `custom` – A function of `{ elapsed, time, date, now, locale }`.\n      //\n      // Looks like `custom` function is deprecated and will be removed\n      // in the next major version.\n      //\n      // If this function returns a value, then the `.format()` call will return that value.\n      // Otherwise the relative date/time is formatted as usual.\n      // This feature is currently not used anywhere and is here\n      // just for providing the ultimate customization point\n      // in case anyone would ever need that. Prefer using\n      // `steps[step].format(value, locale)` instead.\n      //\n\n      if (style.custom) {\n        var custom = style.custom({\n          now: now,\n          date: new Date(timestamp),\n          time: timestamp,\n          elapsed: secondsPassed,\n          locale: this.locale\n        });\n\n        if (custom !== undefined) {\n          // Won't return `timeToNextUpdate` here\n          // because `custom()` seems deprecated.\n          return custom;\n        }\n      } // Get the list of available time interval units.\n\n\n      var units = getTimeIntervalMeasurementUnits( // Controlling `style.steps` through `style.units` seems to be deprecated:\n      // create a new custom `style` instead.\n      style.units, labels, nowLabel); // // If no available time unit is suitable, just output an empty string.\n      // if (units.length === 0) {\n      // \tconsole.error(`None of the \"${units.join(', ')}\" time units have been found in \"${labelsType}\" labels for \"${this.locale}\" locale.`)\n      // \treturn ''\n      // }\n\n      var round = options.round || style.round; // Choose the appropriate time measurement unit\n      // and get the corresponding rounded time amount.\n\n      var _getStep = (0,_steps_getStep_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])( // \"gradation\" is a legacy name for \"steps\".\n      // For historical reasons, \"approximate\" steps are used by default.\n      // In the next major version, there'll be no default for `steps`.\n      style.gradation || style.steps || _style_roundMinute_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].steps, secondsPassed, {\n        now: now,\n        units: units,\n        round: round,\n        future: future,\n        getNextStep: true\n      }),\n          _getStep2 = _slicedToArray(_getStep, 3),\n          prevStep = _getStep2[0],\n          step = _getStep2[1],\n          nextStep = _getStep2[2];\n\n      var formattedDate = this.formatDateForStep(timestamp, step, secondsPassed, {\n        labels: labels,\n        labelsType: labelsType,\n        nowLabel: nowLabel,\n        now: now,\n        future: future,\n        round: round\n      }) || '';\n\n      if (options.getTimeToNextUpdate) {\n        var timeToNextUpdate = (0,_steps_getTimeToNextUpdate_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(timestamp, step, {\n          nextStep: nextStep,\n          prevStep: prevStep,\n          now: now,\n          future: future,\n          round: round\n        });\n        return [formattedDate, timeToNextUpdate];\n      }\n\n      return formattedDate;\n    }\n  }, {\n    key: \"formatDateForStep\",\n    value: function formatDateForStep(timestamp, step, secondsPassed, _ref2) {\n      var _this = this;\n\n      var labels = _ref2.labels,\n          labelsType = _ref2.labelsType,\n          nowLabel = _ref2.nowLabel,\n          now = _ref2.now,\n          future = _ref2.future,\n          round = _ref2.round;\n\n      // If no step matches, then output an empty string.\n      if (!step) {\n        return;\n      }\n\n      if (step.format) {\n        return step.format(timestamp, this.locale, {\n          formatAs: function formatAs(unit, value) {\n            // Mimicks `Intl.RelativeTimeFormat.format()`.\n            return _this.formatValue(value, unit, {\n              labels: labels,\n              future: future\n            });\n          },\n          now: now,\n          future: future\n        });\n      } // \"unit\" is now called \"formatAs\".\n\n\n      var unit = step.unit || step.formatAs;\n\n      if (!unit) {\n        throw new Error(\"[javascript-time-ago] Each step must define either `formatAs` or `format()`. Step: \".concat(JSON.stringify(step)));\n      } // `Intl.RelativeTimeFormat` doesn't operate in \"now\" units.\n      // Therefore, threat \"now\" as a special case.\n\n\n      if (unit === 'now') {\n        return nowLabel;\n      } // Amount in units.\n\n\n      var amount = Math.abs(secondsPassed) / (0,_steps_getStepDenominator_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(step); // Apply granularity to the time amount\n      // (and fallback to the previous step\n      //  if the first level of granularity\n      //  isn't met by this amount)\n      //\n      // `granularity` — (advanced) Time interval value \"granularity\".\n      // For example, it could be set to `5` for minutes to allow only 5-minute increments\n      // when formatting time intervals: `0 minutes`, `5 minutes`, `10 minutes`, etc.\n      // Perhaps this feature will be removed because there seem to be no use cases\n      // of it in the real world.\n      //\n\n      if (step.granularity) {\n        // Recalculate the amount of seconds passed based on granularity\n        amount = (0,_round_js__WEBPACK_IMPORTED_MODULE_9__.getRoundFunction)(round)(amount / step.granularity) * step.granularity;\n      }\n\n      var valueForFormatting = -1 * Math.sign(secondsPassed) * (0,_round_js__WEBPACK_IMPORTED_MODULE_9__.getRoundFunction)(round)(amount); // By default, this library formats a `0` in \"past\" mode,\n      // unless `future: true` option is passed.\n      // This is different to `relative-time-format`'s behavior\n      // which formats a `0` in \"future\" mode by default, unless it's a `-0`.\n      // So, convert `0` to `-0` if `future: true` option wasn't passed.\n      // `=== 0` matches both `0` and `-0`.\n\n      if (valueForFormatting === 0) {\n        if (future) {\n          valueForFormatting = 0;\n        } else {\n          valueForFormatting = -0;\n        }\n      }\n\n      switch (labelsType) {\n        case 'long':\n        case 'short':\n        case 'narrow':\n          // Format the amount using `Intl.RelativeTimeFormat`.\n          return this.getFormatter(labelsType).format(valueForFormatting, unit);\n\n        default:\n          // Format the amount.\n          // (mimicks `Intl.RelativeTimeFormat` behavior for other time label styles)\n          return this.formatValue(valueForFormatting, unit, {\n            labels: labels,\n            future: future\n          });\n      }\n    }\n    /**\r\n     * Mimicks what `Intl.RelativeTimeFormat` does for additional locale styles.\r\n     * @param  {number} value\r\n     * @param  {string} unit\r\n     * @param  {object} options.labels — Relative time labels.\r\n     * @param  {boolean} [options.future] — Tells how to format value `0`: as \"future\" (`true`) or \"past\" (`false`). Is `false` by default, but should have been `true` actually.\r\n     * @return {string}\r\n     */\n\n  }, {\n    key: \"formatValue\",\n    value: function formatValue(value, unit, _ref3) {\n      var labels = _ref3.labels,\n          future = _ref3.future;\n      return this.getFormattingRule(labels, unit, value, {\n        future: future\n      }).replace('{0}', this.formatNumber(Math.abs(value)));\n    }\n    /**\r\n     * Returns formatting rule for `value` in `units` (either in past or in future).\r\n     * @param {object} formattingRules — Relative time labels for different units.\r\n     * @param {string} unit - Time interval measurement unit.\r\n     * @param {number} value - Time interval value.\r\n     * @param  {boolean} [options.future] — Tells how to format value `0`: as \"future\" (`true`) or \"past\" (`false`). Is `false` by default.\r\n     * @return {string}\r\n     * @example\r\n     * // Returns \"{0} days ago\"\r\n     * getFormattingRule(en.long, \"day\", -2, 'en')\r\n     */\n\n  }, {\n    key: \"getFormattingRule\",\n    value: function getFormattingRule(formattingRules, unit, value, _ref4) {\n      var future = _ref4.future;\n      // Passing the language is required in order to\n      // be able to correctly classify the `value` as a number.\n      var locale = this.locale;\n      formattingRules = formattingRules[unit]; // Check for a special \"compacted\" rules case:\n      // if formatting rules are the same for \"past\" and \"future\",\n      // and also for all possible `value`s, then those rules are\n      // stored as a single string.\n\n      if (typeof formattingRules === 'string') {\n        return formattingRules;\n      } // Choose either \"past\" or \"future\" based on time `value` sign.\n      // If \"past\" is same as \"future\" then they're stored as \"other\".\n      // If there's only \"other\" then it's being collapsed.\n\n\n      var pastOrFuture = value === 0 ? future ? 'future' : 'past' : value < 0 ? 'past' : 'future';\n      var quantifierRules = formattingRules[pastOrFuture] || formattingRules; // Bundle size optimization technique.\n\n      if (typeof quantifierRules === 'string') {\n        return quantifierRules;\n      } // Quantify `value`.\n\n\n      var quantifier = this.getPluralRules().select(Math.abs(value)); // \"other\" rule is supposed to always be present.\n      // If only \"other\" rule is present then \"rules\" is not an object and is a string.\n\n      return quantifierRules[quantifier] || quantifierRules.other;\n    }\n    /**\r\n     * Formats a number into a string.\r\n     * Uses `Intl.NumberFormat` when available.\r\n     * @param  {number} number\r\n     * @return {string}\r\n     */\n\n  }, {\n    key: \"formatNumber\",\n    value: function formatNumber(number) {\n      return this.numberFormat ? this.numberFormat.format(number) : String(number);\n    }\n    /**\r\n     * Returns an `Intl.RelativeTimeFormat` for a given `labelsType`.\r\n     * @param {string} labelsType\r\n     * @return {object} `Intl.RelativeTimeFormat` instance\r\n     */\n\n  }, {\n    key: \"getFormatter\",\n    value: function getFormatter(labelsType) {\n      // `Intl.RelativeTimeFormat` instance creation is (hypothetically) assumed\n      // a lengthy operation so the instances are cached and reused.\n      return this.relativeTimeFormatCache.get(this.locale, labelsType) || this.relativeTimeFormatCache.put(this.locale, labelsType, new this.IntlRelativeTimeFormat(this.locale, {\n        style: labelsType\n      }));\n    }\n    /**\r\n     * Returns an `Intl.PluralRules` instance.\r\n     * @return {object} `Intl.PluralRules` instance\r\n     */\n\n  }, {\n    key: \"getPluralRules\",\n    value: function getPluralRules() {\n      // `Intl.PluralRules` instance creation is (hypothetically) assumed\n      // a lengthy operation so the instances are cached and reused.\n      return this.pluralRulesCache.get(this.locale) || this.pluralRulesCache.put(this.locale, new this.IntlPluralRules(this.locale));\n    }\n    /**\r\n     * Gets localized labels for this type of labels.\r\n     *\r\n     * @param {(string|string[])} labelsType - Relative date/time labels type.\r\n     *                                     If it's an array then all label types are tried\r\n     *                                     until a suitable one is found.\r\n     *\r\n     * @returns {Object} Returns an object of shape { labelsType, labels }\r\n     */\n\n  }, {\n    key: \"getLabels\",\n    value: function getLabels() {\n      var labelsType = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n\n      // Convert `labels` to an array.\n      if (typeof labelsType === 'string') {\n        labelsType = [labelsType];\n      } // Supports legacy \"tiny\" and \"mini-time\" label styles.\n\n\n      labelsType = labelsType.map(function (labelsType) {\n        switch (labelsType) {\n          case 'tiny':\n          case 'mini-time':\n            return 'mini';\n\n          default:\n            return labelsType;\n        }\n      }); // \"long\" labels type is the default one.\n      // (it's always present for all languages)\n\n      labelsType = labelsType.concat('long'); // Find a suitable labels type.\n\n      var localeData = (0,_LocaleDataStore_js__WEBPACK_IMPORTED_MODULE_1__.getLocaleData)(this.locale);\n\n      for (var _iterator = _createForOfIteratorHelperLoose(labelsType), _step; !(_step = _iterator()).done;) {\n        var _labelsType = _step.value;\n\n        if (localeData[_labelsType]) {\n          return {\n            labelsType: _labelsType,\n            labels: localeData[_labelsType]\n          };\n        }\n      }\n    }\n  }]);\n\n  return TimeAgo;\n}();\n/**\r\n * Default locale global variable.\r\n */\n\n\n\nvar defaultLocale = 'en';\n/**\r\n * Gets default locale.\r\n * @return  {string} locale\r\n */\n\nTimeAgo.getDefaultLocale = function () {\n  return defaultLocale;\n};\n/**\r\n * Sets default locale.\r\n * @param  {string} locale\r\n */\n\n\nTimeAgo.setDefaultLocale = function (locale) {\n  return defaultLocale = locale;\n};\n/**\r\n * Adds locale data for a specific locale and marks the locale as default.\r\n * @param {Object} localeData\r\n */\n\n\nTimeAgo.addDefaultLocale = function (localeData) {\n  if (defaultLocaleHasBeenSpecified) {\n    return console.error('[javascript-time-ago] `TimeAgo.addDefaultLocale()` can only be called once. To add other locales, use `TimeAgo.addLocale()`.');\n  }\n\n  defaultLocaleHasBeenSpecified = true;\n  TimeAgo.setDefaultLocale(localeData.locale);\n  TimeAgo.addLocale(localeData);\n};\n\nvar defaultLocaleHasBeenSpecified;\n/**\r\n * Adds locale data for a specific locale.\r\n * @param {Object} localeData\r\n */\n\nTimeAgo.addLocale = function (localeData) {\n  (0,_LocaleDataStore_js__WEBPACK_IMPORTED_MODULE_1__.addLocaleData)(localeData);\n  relative_time_format__WEBPACK_IMPORTED_MODULE_2__[\"default\"].addLocale(localeData);\n};\n/**\r\n * (legacy alias)\r\n * Adds locale data for a specific locale.\r\n * @param {Object} localeData\r\n * @deprecated\r\n */\n\n\nTimeAgo.locale = TimeAgo.addLocale;\n/**\r\n * Adds custom labels to locale data.\r\n * @param {string} locale\r\n * @param {string} name\r\n * @param {object} labels\r\n */\n\nTimeAgo.addLabels = function (locale, name, labels) {\n  var localeData = (0,_LocaleDataStore_js__WEBPACK_IMPORTED_MODULE_1__.getLocaleData)(locale);\n\n  if (!localeData) {\n    (0,_LocaleDataStore_js__WEBPACK_IMPORTED_MODULE_1__.addLocaleData)({\n      locale: locale\n    });\n    localeData = (0,_LocaleDataStore_js__WEBPACK_IMPORTED_MODULE_1__.getLocaleData)(locale); // throw new Error(`[javascript-time-ago] No data for locale \"${locale}\"`)\n  }\n\n  localeData[name] = labels;\n}; // Normalizes `.format()` `time` argument.\n\n\nfunction getTimestamp(input) {\n  if (input.constructor === Date || isMockedDate(input)) {\n    return input.getTime();\n  }\n\n  if (typeof input === 'number') {\n    return input;\n  } // For some weird reason istanbul doesn't see this `throw` covered.\n\n  /* istanbul ignore next */\n\n\n  throw new Error(\"Unsupported relative time formatter input: \".concat(_typeof(input), \", \").concat(input));\n} // During testing via some testing libraries `Date`s aren't actually `Date`s.\n// https://github.com/catamphetamine/javascript-time-ago/issues/22\n\n\nfunction isMockedDate(object) {\n  return _typeof(object) === 'object' && typeof object.getTime === 'function';\n} // Get available time interval measurement units.\n\n\nfunction getTimeIntervalMeasurementUnits(allowedUnits, labels, nowLabel) {\n  // Get all time interval measurement units that're available\n  // in locale data for a given time labels style.\n  var units = Object.keys(labels); // `now` unit is handled separately and is shipped in its own `now.json` file.\n  // `now.json` isn't present for all locales, so it could be substituted with\n  // \".second.current\".\n  // Add `now` unit if it's available in locale data.\n\n  if (nowLabel) {\n    units.push('now');\n  } // If only a specific set of available time measurement units can be used\n  // then only those units are allowed (if they're present in locale data).\n\n\n  if (allowedUnits) {\n    units = allowedUnits.filter(function (unit) {\n      return unit === 'now' || units.indexOf(unit) >= 0;\n    });\n  }\n\n  return units;\n}\n\nfunction getNowLabel(labels, nowLabels, longLabels, future) {\n  var nowLabel = labels.now || nowLabels && nowLabels.now; // Specific \"now\" message form extended locale data (if present).\n\n  if (nowLabel) {\n    // Bundle size optimization technique.\n    if (typeof nowLabel === 'string') {\n      return nowLabel;\n    } // Not handling `value === 0` as `localeData.now.current` here\n    // because it wouldn't make sense: \"now\" is a moment,\n    // so one can't possibly differentiate between a\n    // \"previous\" moment, a \"current\" moment and a \"next moment\".\n    // It can only be differentiated between \"past\" and \"future\".\n\n\n    if (future) {\n      return nowLabel.future;\n    } else {\n      return nowLabel.past;\n    }\n  } // Use \".second.current\" as \"now\" message.\n\n\n  if (longLabels && longLabels.second && longLabels.second.current) {\n    return longLabels.second.current;\n  }\n}\n\nfunction isStyle(variable) {\n  return typeof variable === 'string' || (0,_isStyleObject_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(variable);\n}\n//# sourceMappingURL=TimeAgo.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/TimeAgo.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/cache.js":
/*!***********************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/cache.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Cache)\n/* harmony export */ });\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\n/**\r\n * A basic in-memory cache.\r\n *\r\n * import Cache from 'javascript-time-ago/Cache'\r\n * const cache = new Cache()\r\n * const object = cache.get('key1', 'key2', ...) || cache.put('key1', 'key2', ..., createObject())\r\n */\nvar Cache = /*#__PURE__*/function () {\n  function Cache() {\n    _classCallCheck(this, Cache);\n\n    this.cache = {};\n  }\n\n  _createClass(Cache, [{\n    key: \"get\",\n    value: function get() {\n      var cache = this.cache;\n\n      for (var _len = arguments.length, keys = new Array(_len), _key = 0; _key < _len; _key++) {\n        keys[_key] = arguments[_key];\n      }\n\n      for (var _i = 0, _keys = keys; _i < _keys.length; _i++) {\n        var key = _keys[_i];\n\n        if (_typeof(cache) !== 'object') {\n          return;\n        }\n\n        cache = cache[key];\n      }\n\n      return cache;\n    }\n  }, {\n    key: \"put\",\n    value: function put() {\n      for (var _len2 = arguments.length, keys = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        keys[_key2] = arguments[_key2];\n      }\n\n      var value = keys.pop();\n      var lastKey = keys.pop();\n      var cache = this.cache;\n\n      for (var _i2 = 0, _keys2 = keys; _i2 < _keys2.length; _i2++) {\n        var key = _keys2[_i2];\n\n        if (_typeof(cache[key]) !== 'object') {\n          cache[key] = {};\n        }\n\n        cache = cache[key];\n      }\n\n      return cache[lastKey] = value;\n    }\n  }]);\n\n  return Cache;\n}();\n\n\n//# sourceMappingURL=cache.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/cache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/isStyleObject.js":
/*!*******************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/isStyleObject.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ isStyleObject)\n/* harmony export */ });\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nfunction isStyleObject(object) {\n  return isObject(object) && (Array.isArray(object.steps) || // `gradation` property is deprecated: it has been renamed to `steps`.\n  Array.isArray(object.gradation) || // `flavour` property is deprecated: it has been renamed to `labels`.\n  Array.isArray(object.flavour) || typeof object.flavour === 'string' || Array.isArray(object.labels) || typeof object.labels === 'string' || // `units` property is deprecated.\n  Array.isArray(object.units) || // `custom` property is deprecated.\n  typeof object.custom === 'function');\n}\nvar OBJECT_CONSTRUCTOR = {}.constructor;\n\nfunction isObject(object) {\n  return _typeof(object) !== undefined && object !== null && object.constructor === OBJECT_CONSTRUCTOR;\n}\n//# sourceMappingURL=isStyleObject.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/isStyleObject.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/locale.js":
/*!************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/locale.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ chooseLocale),\n/* harmony export */   intlDateTimeFormatSupported: () => (/* binding */ intlDateTimeFormatSupported),\n/* harmony export */   intlDateTimeFormatSupportedLocale: () => (/* binding */ intlDateTimeFormatSupportedLocale)\n/* harmony export */ });\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nfunction _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\n/**\r\n * Chooses the most appropriate locale\r\n * (one of the registered ones)\r\n * based on the list of preferred `locales` supplied by the user.\r\n *\r\n * @param {string[]} locales - the list of preferable locales (in [IETF format](https://en.wikipedia.org/wiki/IETF_language_tag)).\r\n * @param {Function} isLocaleDataAvailable - tests if a locale is available.\r\n *\r\n * @returns {string} The most suitable locale.\r\n *\r\n * @example\r\n * // Returns 'en'\r\n * chooseLocale(['en-US'], undefined, (locale) => locale === 'ru' || locale === 'en')\r\n */\nfunction chooseLocale(locales, isLocaleDataAvailable) {\n  // This is not an intelligent algorithm,\n  // but it will do for this library's case.\n  // `sr-Cyrl-BA` -> `sr-Cyrl` -> `sr`.\n  for (var _iterator = _createForOfIteratorHelperLoose(locales), _step; !(_step = _iterator()).done;) {\n    var locale = _step.value;\n\n    if (isLocaleDataAvailable(locale)) {\n      return locale;\n    }\n\n    var parts = locale.split('-');\n\n    while (parts.length > 1) {\n      parts.pop();\n      locale = parts.join('-');\n\n      if (isLocaleDataAvailable(locale)) {\n        return locale;\n      }\n    }\n  }\n\n  throw new Error(\"No locale data has been registered for any of the locales: \".concat(locales.join(', ')));\n}\n/**\r\n * Whether can use `Intl.DateTimeFormat` for these `locales`.\r\n * Returns the first suitable one.\r\n * @param  {(string|string[])} locales\r\n * @return {?string} The first locale that can be used.\r\n */\n\nfunction intlDateTimeFormatSupportedLocale(locales) {\n  /* istanbul ignore else */\n  if (intlDateTimeFormatSupported()) {\n    return Intl.DateTimeFormat.supportedLocalesOf(locales)[0];\n  }\n}\n/**\r\n * Whether can use `Intl.DateTimeFormat`.\r\n * @return {boolean}\r\n */\n\nfunction intlDateTimeFormatSupported() {\n  // Babel transforms `typeof` into some \"branches\"\n  // so istanbul will show this as \"branch not covered\".\n\n  /* istanbul ignore next */\n  var isIntlAvailable = (typeof Intl === \"undefined\" ? \"undefined\" : _typeof(Intl)) === 'object';\n  return isIntlAvailable && typeof Intl.DateTimeFormat === 'function';\n}\n//# sourceMappingURL=locale.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/locale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/round.js":
/*!***********************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/round.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDiffRatioToNextRoundedNumber: () => (/* binding */ getDiffRatioToNextRoundedNumber),\n/* harmony export */   getRoundFunction: () => (/* binding */ getRoundFunction)\n/* harmony export */ });\nfunction getRoundFunction(round) {\n  switch (round) {\n    case 'floor':\n      return Math.floor;\n\n    default:\n      return Math.round;\n  }\n} // For non-negative numbers.\n\nfunction getDiffRatioToNextRoundedNumber(round) {\n  switch (round) {\n    case 'floor':\n      // Math.floor(x) = x\n      // Math.floor(x + 1) = x + 1\n      return 1;\n\n    default:\n      // Math.round(x) = x\n      // Math.round(x + 0.5) = x + 1\n      return 0.5;\n  }\n}\n//# sourceMappingURL=round.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvamF2YXNjcmlwdC10aW1lLWFnby9tb2R1bGVzL3JvdW5kLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsRUFBRTs7QUFFSztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2phdmFzY3JpcHQtdGltZS1hZ28vbW9kdWxlcy9yb3VuZC5qcz9jYjUyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBnZXRSb3VuZEZ1bmN0aW9uKHJvdW5kKSB7XG4gIHN3aXRjaCAocm91bmQpIHtcbiAgICBjYXNlICdmbG9vcic6XG4gICAgICByZXR1cm4gTWF0aC5mbG9vcjtcblxuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4gTWF0aC5yb3VuZDtcbiAgfVxufSAvLyBGb3Igbm9uLW5lZ2F0aXZlIG51bWJlcnMuXG5cbmV4cG9ydCBmdW5jdGlvbiBnZXREaWZmUmF0aW9Ub05leHRSb3VuZGVkTnVtYmVyKHJvdW5kKSB7XG4gIHN3aXRjaCAocm91bmQpIHtcbiAgICBjYXNlICdmbG9vcic6XG4gICAgICAvLyBNYXRoLmZsb29yKHgpID0geFxuICAgICAgLy8gTWF0aC5mbG9vcih4ICsgMSkgPSB4ICsgMVxuICAgICAgcmV0dXJuIDE7XG5cbiAgICBkZWZhdWx0OlxuICAgICAgLy8gTWF0aC5yb3VuZCh4KSA9IHhcbiAgICAgIC8vIE1hdGgucm91bmQoeCArIDAuNSkgPSB4ICsgMVxuICAgICAgcmV0dXJuIDAuNTtcbiAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cm91bmQuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/round.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/steps/approximate.js":
/*!***********************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/steps/approximate.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _units_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./units.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/steps/units.js\");\n // \"factor\" is a legacy property.\n// Developers shouldn't need to use it in their custom steps.\n// \"threshold\" is a legacy name of \"min\".\n// Developers should use \"min\" property name instead of \"threshold\".\n// \"threshold_for_idOrUnit: value\" is a legacy way of specifying \"min: { id: value }\".\n// Developers should use \"min\" property instead of \"threshold\".\n// just now\n// 1 minute ago\n// 2 minutes ago\n// 5 minutes ago\n// 10 minutes ago\n// 15 minutes ago\n// 20 minutes ago\n// …\n// 50 minutes ago\n// an hour ago\n// 2 hours ago\n// …\n// 20 hours ago\n// a day ago\n// 2 days ago\n// 5 days ago\n// a week ago\n// 2 weeks ago\n// 3 weeks ago\n// a month ago\n// 2 months ago\n// 4 months ago\n// a year ago\n// 2 years ago\n// …\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([{\n  // This step returns the amount of seconds\n  // by dividing the amount of seconds by `1`.\n  factor: 1,\n  // \"now\" labels are used for formatting the output.\n  unit: 'now'\n}, {\n  // When the language doesn't support `now` unit,\n  // the first step is ignored, and it uses this `second` unit.\n  threshold: 1,\n  // `threshold_for_now` should be the same as `threshold` on minutes.\n  threshold_for_now: 45.5,\n  // This step returns the amount of seconds\n  // by dividing the amount of seconds by `1`.\n  factor: 1,\n  // \"second\" labels are used for formatting the output.\n  unit: 'second'\n}, {\n  // `threshold` should be the same as `threshold_for_now` on seconds.\n  threshold: 45.5,\n  // Return the amount of minutes by dividing the amount\n  // of seconds by the amount of seconds in a minute.\n  factor: _units_js__WEBPACK_IMPORTED_MODULE_0__.minute,\n  // \"minute\" labels are used for formatting the output.\n  unit: 'minute'\n}, {\n  // This step is effective starting from 2.5 minutes.\n  threshold: 2.5 * _units_js__WEBPACK_IMPORTED_MODULE_0__.minute,\n  // Allow only 5-minute increments of minutes starting from 2.5 minutes.\n  // `granularity` — (advanced) Time interval value \"granularity\".\n  // For example, it could be set to `5` for minutes to allow only 5-minute increments\n  // when formatting time intervals: `0 minutes`, `5 minutes`, `10 minutes`, etc.\n  // Perhaps this feature will be removed because there seem to be no use cases\n  // of it in the real world.\n  granularity: 5,\n  // Return the amount of minutes by dividing the amount\n  // of seconds by the amount of seconds in a minute.\n  factor: _units_js__WEBPACK_IMPORTED_MODULE_0__.minute,\n  // \"minute\" labels are used for formatting the output.\n  unit: 'minute'\n}, {\n  // This step is effective starting from 22.5 minutes.\n  threshold: 22.5 * _units_js__WEBPACK_IMPORTED_MODULE_0__.minute,\n  // Return the amount of minutes by dividing the amount\n  // of seconds by the amount of seconds in  half-an-hour.\n  factor: 0.5 * _units_js__WEBPACK_IMPORTED_MODULE_0__.hour,\n  // \"half-hour\" labels are used for formatting the output.\n  // (if available, which is no longer the case)\n  unit: 'half-hour'\n}, {\n  // This step is effective starting from 42.5 minutes.\n  threshold: 42.5 * _units_js__WEBPACK_IMPORTED_MODULE_0__.minute,\n  threshold_for_minute: 52.5 * _units_js__WEBPACK_IMPORTED_MODULE_0__.minute,\n  // Return the amount of minutes by dividing the amount\n  // of seconds by the amount of seconds in an hour.\n  factor: _units_js__WEBPACK_IMPORTED_MODULE_0__.hour,\n  // \"hour\" labels are used for formatting the output.\n  unit: 'hour'\n}, {\n  // This step is effective starting from 20.5 hours.\n  threshold: 20.5 / 24 * _units_js__WEBPACK_IMPORTED_MODULE_0__.day,\n  // Return the amount of minutes by dividing the amount\n  // of seconds by the amount of seconds in a day.\n  factor: _units_js__WEBPACK_IMPORTED_MODULE_0__.day,\n  // \"day\" labels are used for formatting the output.\n  unit: 'day'\n}, {\n  // This step is effective starting from 5.5 days.\n  threshold: 5.5 * _units_js__WEBPACK_IMPORTED_MODULE_0__.day,\n  // Return the amount of minutes by dividing the amount\n  // of seconds by the amount of seconds in a week.\n  factor: _units_js__WEBPACK_IMPORTED_MODULE_0__.week,\n  // \"week\" labels are used for formatting the output.\n  unit: 'week'\n}, {\n  // This step is effective starting from 3.5 weeks.\n  threshold: 3.5 * _units_js__WEBPACK_IMPORTED_MODULE_0__.week,\n  // Return the amount of minutes by dividing the amount\n  // of seconds by the amount of seconds in a month.\n  factor: _units_js__WEBPACK_IMPORTED_MODULE_0__.month,\n  // \"month\" labels are used for formatting the output.\n  unit: 'month'\n}, {\n  // This step is effective starting from 10.5 months.\n  threshold: 10.5 * _units_js__WEBPACK_IMPORTED_MODULE_0__.month,\n  // Return the amount of minutes by dividing the amount\n  // of seconds by the amount of seconds in a year.\n  factor: _units_js__WEBPACK_IMPORTED_MODULE_0__.year,\n  // \"year\" labels are used for formatting the output.\n  unit: 'year'\n}]);\n//# sourceMappingURL=approximate.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvamF2YXNjcmlwdC10aW1lLWFnby9tb2R1bGVzL3N0ZXBzL2FwcHJveGltYXRlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWtFLENBQUM7QUFDbkU7QUFDQTtBQUNBO0FBQ0EseUVBQXlFLFdBQVc7QUFDcEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxpRUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLDZDQUFNO0FBQ2hCO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQSxtQkFBbUIsNkNBQU07QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSw2Q0FBTTtBQUNoQjtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0Esb0JBQW9CLDZDQUFNO0FBQzFCO0FBQ0E7QUFDQSxnQkFBZ0IsMkNBQUk7QUFDcEI7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0Esb0JBQW9CLDZDQUFNO0FBQzFCLCtCQUErQiw2Q0FBTTtBQUNyQztBQUNBO0FBQ0EsVUFBVSwyQ0FBSTtBQUNkO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQSx5QkFBeUIsMENBQUc7QUFDNUI7QUFDQTtBQUNBLFVBQVUsMENBQUc7QUFDYjtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0EsbUJBQW1CLDBDQUFHO0FBQ3RCO0FBQ0E7QUFDQSxVQUFVLDJDQUFJO0FBQ2Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBLG1CQUFtQiwyQ0FBSTtBQUN2QjtBQUNBO0FBQ0EsVUFBVSw0Q0FBSztBQUNmO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQSxvQkFBb0IsNENBQUs7QUFDekI7QUFDQTtBQUNBLFVBQVUsMkNBQUk7QUFDZDtBQUNBO0FBQ0EsQ0FBQyxDQUFDLEVBQUM7QUFDSCIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvamF2YXNjcmlwdC10aW1lLWFnby9tb2R1bGVzL3N0ZXBzL2FwcHJveGltYXRlLmpzP2Q3ZDMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbWludXRlLCBob3VyLCBkYXksIHdlZWssIG1vbnRoLCB5ZWFyIH0gZnJvbSAnLi91bml0cy5qcyc7IC8vIFwiZmFjdG9yXCIgaXMgYSBsZWdhY3kgcHJvcGVydHkuXG4vLyBEZXZlbG9wZXJzIHNob3VsZG4ndCBuZWVkIHRvIHVzZSBpdCBpbiB0aGVpciBjdXN0b20gc3RlcHMuXG4vLyBcInRocmVzaG9sZFwiIGlzIGEgbGVnYWN5IG5hbWUgb2YgXCJtaW5cIi5cbi8vIERldmVsb3BlcnMgc2hvdWxkIHVzZSBcIm1pblwiIHByb3BlcnR5IG5hbWUgaW5zdGVhZCBvZiBcInRocmVzaG9sZFwiLlxuLy8gXCJ0aHJlc2hvbGRfZm9yX2lkT3JVbml0OiB2YWx1ZVwiIGlzIGEgbGVnYWN5IHdheSBvZiBzcGVjaWZ5aW5nIFwibWluOiB7IGlkOiB2YWx1ZSB9XCIuXG4vLyBEZXZlbG9wZXJzIHNob3VsZCB1c2UgXCJtaW5cIiBwcm9wZXJ0eSBpbnN0ZWFkIG9mIFwidGhyZXNob2xkXCIuXG4vLyBqdXN0IG5vd1xuLy8gMSBtaW51dGUgYWdvXG4vLyAyIG1pbnV0ZXMgYWdvXG4vLyA1IG1pbnV0ZXMgYWdvXG4vLyAxMCBtaW51dGVzIGFnb1xuLy8gMTUgbWludXRlcyBhZ29cbi8vIDIwIG1pbnV0ZXMgYWdvXG4vLyDigKZcbi8vIDUwIG1pbnV0ZXMgYWdvXG4vLyBhbiBob3VyIGFnb1xuLy8gMiBob3VycyBhZ29cbi8vIOKAplxuLy8gMjAgaG91cnMgYWdvXG4vLyBhIGRheSBhZ29cbi8vIDIgZGF5cyBhZ29cbi8vIDUgZGF5cyBhZ29cbi8vIGEgd2VlayBhZ29cbi8vIDIgd2Vla3MgYWdvXG4vLyAzIHdlZWtzIGFnb1xuLy8gYSBtb250aCBhZ29cbi8vIDIgbW9udGhzIGFnb1xuLy8gNCBtb250aHMgYWdvXG4vLyBhIHllYXIgYWdvXG4vLyAyIHllYXJzIGFnb1xuLy8g4oCmXG5cbmV4cG9ydCBkZWZhdWx0IFt7XG4gIC8vIFRoaXMgc3RlcCByZXR1cm5zIHRoZSBhbW91bnQgb2Ygc2Vjb25kc1xuICAvLyBieSBkaXZpZGluZyB0aGUgYW1vdW50IG9mIHNlY29uZHMgYnkgYDFgLlxuICBmYWN0b3I6IDEsXG4gIC8vIFwibm93XCIgbGFiZWxzIGFyZSB1c2VkIGZvciBmb3JtYXR0aW5nIHRoZSBvdXRwdXQuXG4gIHVuaXQ6ICdub3cnXG59LCB7XG4gIC8vIFdoZW4gdGhlIGxhbmd1YWdlIGRvZXNuJ3Qgc3VwcG9ydCBgbm93YCB1bml0LFxuICAvLyB0aGUgZmlyc3Qgc3RlcCBpcyBpZ25vcmVkLCBhbmQgaXQgdXNlcyB0aGlzIGBzZWNvbmRgIHVuaXQuXG4gIHRocmVzaG9sZDogMSxcbiAgLy8gYHRocmVzaG9sZF9mb3Jfbm93YCBzaG91bGQgYmUgdGhlIHNhbWUgYXMgYHRocmVzaG9sZGAgb24gbWludXRlcy5cbiAgdGhyZXNob2xkX2Zvcl9ub3c6IDQ1LjUsXG4gIC8vIFRoaXMgc3RlcCByZXR1cm5zIHRoZSBhbW91bnQgb2Ygc2Vjb25kc1xuICAvLyBieSBkaXZpZGluZyB0aGUgYW1vdW50IG9mIHNlY29uZHMgYnkgYDFgLlxuICBmYWN0b3I6IDEsXG4gIC8vIFwic2Vjb25kXCIgbGFiZWxzIGFyZSB1c2VkIGZvciBmb3JtYXR0aW5nIHRoZSBvdXRwdXQuXG4gIHVuaXQ6ICdzZWNvbmQnXG59LCB7XG4gIC8vIGB0aHJlc2hvbGRgIHNob3VsZCBiZSB0aGUgc2FtZSBhcyBgdGhyZXNob2xkX2Zvcl9ub3dgIG9uIHNlY29uZHMuXG4gIHRocmVzaG9sZDogNDUuNSxcbiAgLy8gUmV0dXJuIHRoZSBhbW91bnQgb2YgbWludXRlcyBieSBkaXZpZGluZyB0aGUgYW1vdW50XG4gIC8vIG9mIHNlY29uZHMgYnkgdGhlIGFtb3VudCBvZiBzZWNvbmRzIGluIGEgbWludXRlLlxuICBmYWN0b3I6IG1pbnV0ZSxcbiAgLy8gXCJtaW51dGVcIiBsYWJlbHMgYXJlIHVzZWQgZm9yIGZvcm1hdHRpbmcgdGhlIG91dHB1dC5cbiAgdW5pdDogJ21pbnV0ZSdcbn0sIHtcbiAgLy8gVGhpcyBzdGVwIGlzIGVmZmVjdGl2ZSBzdGFydGluZyBmcm9tIDIuNSBtaW51dGVzLlxuICB0aHJlc2hvbGQ6IDIuNSAqIG1pbnV0ZSxcbiAgLy8gQWxsb3cgb25seSA1LW1pbnV0ZSBpbmNyZW1lbnRzIG9mIG1pbnV0ZXMgc3RhcnRpbmcgZnJvbSAyLjUgbWludXRlcy5cbiAgLy8gYGdyYW51bGFyaXR5YCDigJQgKGFkdmFuY2VkKSBUaW1lIGludGVydmFsIHZhbHVlIFwiZ3JhbnVsYXJpdHlcIi5cbiAgLy8gRm9yIGV4YW1wbGUsIGl0IGNvdWxkIGJlIHNldCB0byBgNWAgZm9yIG1pbnV0ZXMgdG8gYWxsb3cgb25seSA1LW1pbnV0ZSBpbmNyZW1lbnRzXG4gIC8vIHdoZW4gZm9ybWF0dGluZyB0aW1lIGludGVydmFsczogYDAgbWludXRlc2AsIGA1IG1pbnV0ZXNgLCBgMTAgbWludXRlc2AsIGV0Yy5cbiAgLy8gUGVyaGFwcyB0aGlzIGZlYXR1cmUgd2lsbCBiZSByZW1vdmVkIGJlY2F1c2UgdGhlcmUgc2VlbSB0byBiZSBubyB1c2UgY2FzZXNcbiAgLy8gb2YgaXQgaW4gdGhlIHJlYWwgd29ybGQuXG4gIGdyYW51bGFyaXR5OiA1LFxuICAvLyBSZXR1cm4gdGhlIGFtb3VudCBvZiBtaW51dGVzIGJ5IGRpdmlkaW5nIHRoZSBhbW91bnRcbiAgLy8gb2Ygc2Vjb25kcyBieSB0aGUgYW1vdW50IG9mIHNlY29uZHMgaW4gYSBtaW51dGUuXG4gIGZhY3RvcjogbWludXRlLFxuICAvLyBcIm1pbnV0ZVwiIGxhYmVscyBhcmUgdXNlZCBmb3IgZm9ybWF0dGluZyB0aGUgb3V0cHV0LlxuICB1bml0OiAnbWludXRlJ1xufSwge1xuICAvLyBUaGlzIHN0ZXAgaXMgZWZmZWN0aXZlIHN0YXJ0aW5nIGZyb20gMjIuNSBtaW51dGVzLlxuICB0aHJlc2hvbGQ6IDIyLjUgKiBtaW51dGUsXG4gIC8vIFJldHVybiB0aGUgYW1vdW50IG9mIG1pbnV0ZXMgYnkgZGl2aWRpbmcgdGhlIGFtb3VudFxuICAvLyBvZiBzZWNvbmRzIGJ5IHRoZSBhbW91bnQgb2Ygc2Vjb25kcyBpbiAgaGFsZi1hbi1ob3VyLlxuICBmYWN0b3I6IDAuNSAqIGhvdXIsXG4gIC8vIFwiaGFsZi1ob3VyXCIgbGFiZWxzIGFyZSB1c2VkIGZvciBmb3JtYXR0aW5nIHRoZSBvdXRwdXQuXG4gIC8vIChpZiBhdmFpbGFibGUsIHdoaWNoIGlzIG5vIGxvbmdlciB0aGUgY2FzZSlcbiAgdW5pdDogJ2hhbGYtaG91cidcbn0sIHtcbiAgLy8gVGhpcyBzdGVwIGlzIGVmZmVjdGl2ZSBzdGFydGluZyBmcm9tIDQyLjUgbWludXRlcy5cbiAgdGhyZXNob2xkOiA0Mi41ICogbWludXRlLFxuICB0aHJlc2hvbGRfZm9yX21pbnV0ZTogNTIuNSAqIG1pbnV0ZSxcbiAgLy8gUmV0dXJuIHRoZSBhbW91bnQgb2YgbWludXRlcyBieSBkaXZpZGluZyB0aGUgYW1vdW50XG4gIC8vIG9mIHNlY29uZHMgYnkgdGhlIGFtb3VudCBvZiBzZWNvbmRzIGluIGFuIGhvdXIuXG4gIGZhY3RvcjogaG91cixcbiAgLy8gXCJob3VyXCIgbGFiZWxzIGFyZSB1c2VkIGZvciBmb3JtYXR0aW5nIHRoZSBvdXRwdXQuXG4gIHVuaXQ6ICdob3VyJ1xufSwge1xuICAvLyBUaGlzIHN0ZXAgaXMgZWZmZWN0aXZlIHN0YXJ0aW5nIGZyb20gMjAuNSBob3Vycy5cbiAgdGhyZXNob2xkOiAyMC41IC8gMjQgKiBkYXksXG4gIC8vIFJldHVybiB0aGUgYW1vdW50IG9mIG1pbnV0ZXMgYnkgZGl2aWRpbmcgdGhlIGFtb3VudFxuICAvLyBvZiBzZWNvbmRzIGJ5IHRoZSBhbW91bnQgb2Ygc2Vjb25kcyBpbiBhIGRheS5cbiAgZmFjdG9yOiBkYXksXG4gIC8vIFwiZGF5XCIgbGFiZWxzIGFyZSB1c2VkIGZvciBmb3JtYXR0aW5nIHRoZSBvdXRwdXQuXG4gIHVuaXQ6ICdkYXknXG59LCB7XG4gIC8vIFRoaXMgc3RlcCBpcyBlZmZlY3RpdmUgc3RhcnRpbmcgZnJvbSA1LjUgZGF5cy5cbiAgdGhyZXNob2xkOiA1LjUgKiBkYXksXG4gIC8vIFJldHVybiB0aGUgYW1vdW50IG9mIG1pbnV0ZXMgYnkgZGl2aWRpbmcgdGhlIGFtb3VudFxuICAvLyBvZiBzZWNvbmRzIGJ5IHRoZSBhbW91bnQgb2Ygc2Vjb25kcyBpbiBhIHdlZWsuXG4gIGZhY3Rvcjogd2VlayxcbiAgLy8gXCJ3ZWVrXCIgbGFiZWxzIGFyZSB1c2VkIGZvciBmb3JtYXR0aW5nIHRoZSBvdXRwdXQuXG4gIHVuaXQ6ICd3ZWVrJ1xufSwge1xuICAvLyBUaGlzIHN0ZXAgaXMgZWZmZWN0aXZlIHN0YXJ0aW5nIGZyb20gMy41IHdlZWtzLlxuICB0aHJlc2hvbGQ6IDMuNSAqIHdlZWssXG4gIC8vIFJldHVybiB0aGUgYW1vdW50IG9mIG1pbnV0ZXMgYnkgZGl2aWRpbmcgdGhlIGFtb3VudFxuICAvLyBvZiBzZWNvbmRzIGJ5IHRoZSBhbW91bnQgb2Ygc2Vjb25kcyBpbiBhIG1vbnRoLlxuICBmYWN0b3I6IG1vbnRoLFxuICAvLyBcIm1vbnRoXCIgbGFiZWxzIGFyZSB1c2VkIGZvciBmb3JtYXR0aW5nIHRoZSBvdXRwdXQuXG4gIHVuaXQ6ICdtb250aCdcbn0sIHtcbiAgLy8gVGhpcyBzdGVwIGlzIGVmZmVjdGl2ZSBzdGFydGluZyBmcm9tIDEwLjUgbW9udGhzLlxuICB0aHJlc2hvbGQ6IDEwLjUgKiBtb250aCxcbiAgLy8gUmV0dXJuIHRoZSBhbW91bnQgb2YgbWludXRlcyBieSBkaXZpZGluZyB0aGUgYW1vdW50XG4gIC8vIG9mIHNlY29uZHMgYnkgdGhlIGFtb3VudCBvZiBzZWNvbmRzIGluIGEgeWVhci5cbiAgZmFjdG9yOiB5ZWFyLFxuICAvLyBcInllYXJcIiBsYWJlbHMgYXJlIHVzZWQgZm9yIGZvcm1hdHRpbmcgdGhlIG91dHB1dC5cbiAgdW5pdDogJ3llYXInXG59XTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcHJveGltYXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/steps/approximate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/steps/getStep.js":
/*!*******************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/steps/getStep.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getStep)\n/* harmony export */ });\n/* harmony import */ var _getStepDenominator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getStepDenominator.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/steps/getStepDenominator.js\");\n/* harmony import */ var _getStepMinTime_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getStepMinTime.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/steps/getStepMinTime.js\");\n/* harmony import */ var _round_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../round.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/round.js\");\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n\n\n\n/**\r\n * Finds an appropriate `step` of `steps` for the time interval (in seconds).\r\n *\r\n * @param {Object[]} steps - Time formatting steps.\r\n *\r\n * @param {number} secondsPassed - Time interval (in seconds).\r\n *                                 `< 0` for past dates and `> 0` for future dates.\r\n *\r\n * @param {number} options.now - Current timestamp.\r\n *\r\n * @param {boolean} [options.future] - Whether the date should be formatted as a future one\r\n *                                     instead of a past one.\r\n *\r\n * @param {string} [options.round] - (undocumented) Rounding mechanism.\r\n *\r\n * @param {string[]} [options.units] - A list of allowed time units.\r\n *                                     (Example: ['second', 'minute', 'hour', …])\r\n *\r\n * @param {boolean} [options.getNextStep] - Pass true to return `[step, nextStep]` instead of just `step`.\r\n *\r\n * @return {Object|Object[]} [step] — Either a `step` or `[prevStep, step, nextStep]`.\r\n */\n\nfunction getStep(steps, secondsPassed, _ref) {\n  var now = _ref.now,\n      future = _ref.future,\n      round = _ref.round,\n      units = _ref.units,\n      getNextStep = _ref.getNextStep;\n  // Ignore steps having not-supported time units in `formatAs`.\n  steps = filterStepsByUnits(steps, units);\n\n  var step = _getStep(steps, secondsPassed, {\n    now: now,\n    future: future,\n    round: round\n  });\n\n  if (getNextStep) {\n    if (step) {\n      var prevStep = steps[steps.indexOf(step) - 1];\n      var nextStep = steps[steps.indexOf(step) + 1];\n      return [prevStep, step, nextStep];\n    }\n\n    return [undefined, undefined, steps[0]];\n  }\n\n  return step;\n}\n\nfunction _getStep(steps, secondsPassed, _ref2) {\n  var now = _ref2.now,\n      future = _ref2.future,\n      round = _ref2.round;\n\n  // If no steps fit the conditions then return nothing.\n  if (steps.length === 0) {\n    return;\n  } // Find the most appropriate step.\n\n\n  var i = getStepIndex(steps, secondsPassed, {\n    now: now,\n    future: future || secondsPassed < 0,\n    round: round\n  }); // If no step is applicable the return nothing.\n\n  if (i === -1) {\n    return;\n  }\n\n  var step = steps[i]; // Apply granularity to the time amount\n  // (and fall back to the previous step\n  //  if the first level of granularity\n  //  isn't met by this amount)\n\n  if (step.granularity) {\n    // Recalculate the amount of seconds passed based on `granularity`.\n    var secondsPassedGranular = (0,_round_js__WEBPACK_IMPORTED_MODULE_0__.getRoundFunction)(round)(Math.abs(secondsPassed) / (0,_getStepDenominator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(step) / step.granularity) * step.granularity; // If the granularity for this step is too high,\n    // then fall back to the previous step.\n    // (if there is any previous step)\n\n    if (secondsPassedGranular === 0 && i > 0) {\n      return steps[i - 1];\n    }\n  }\n\n  return step;\n}\n/**\r\n * Iterates through steps until it finds the maximum one satisfying the `minTime` threshold.\r\n * @param  {Object} steps - Steps.\r\n * @param  {number} secondsPassed - How much seconds have passed since the date till `now`.\r\n * @param  {number} options.now - Current timestamp.\r\n * @param  {boolean} options.future - Whether the time interval should be formatted as a future one.\r\n * @param  {number} [i] - Gradation step currently being tested.\r\n * @return {number} Gradation step index.\r\n */\n\n\nfunction getStepIndex(steps, secondsPassed, options) {\n  var i = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n  var minTime = (0,_getStepMinTime_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(steps[i], _objectSpread({\n    prevStep: steps[i - 1],\n    timestamp: options.now - secondsPassed * 1000\n  }, options)); // If `minTime` isn't defined or deduceable for this step, then stop.\n\n  if (minTime === undefined) {\n    return i - 1;\n  } // If the `minTime` threshold for moving from previous step\n  // to this step is too high then return the previous step.\n\n\n  if (Math.abs(secondsPassed) < minTime) {\n    return i - 1;\n  } // If it's the last step then return it.\n\n\n  if (i === steps.length - 1) {\n    return i;\n  } // Move to the next step.\n\n\n  return getStepIndex(steps, secondsPassed, options, i + 1);\n}\n/**\r\n * Leaves only allowed steps.\r\n * @param  {Object[]} steps\r\n * @param  {string[]} units - Allowed time units.\r\n * @return {Object[]}\r\n */\n\n\nfunction filterStepsByUnits(steps, units) {\n  return steps.filter(function (_ref3) {\n    var unit = _ref3.unit,\n        formatAs = _ref3.formatAs;\n    // \"unit\" is now called \"formatAs\".\n    unit = unit || formatAs; // If this step has a `unit` defined\n    // then this `unit` must be in the list of allowed `units`.\n\n    if (unit) {\n      return units.indexOf(unit) >= 0;\n    } // A step is not required to specify a `unit`:\n    // alternatively, it could specify `format()`.\n    // (see \"twitter\" style for an example)\n\n\n    return true;\n  });\n}\n//# sourceMappingURL=getStep.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/steps/getStep.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/steps/getStepDenominator.js":
/*!******************************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/steps/getStepDenominator.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getStepDenominator)\n/* harmony export */ });\n/* harmony import */ var _units_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./units.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/steps/units.js\");\n\nfunction getStepDenominator(step) {\n  // `factor` is a legacy property.\n  if (step.factor !== undefined) {\n    return step.factor;\n  } // \"unit\" is now called \"formatAs\".\n\n\n  return (0,_units_js__WEBPACK_IMPORTED_MODULE_0__.getSecondsInUnit)(step.unit || step.formatAs) || 1;\n}\n//# sourceMappingURL=getStepDenominator.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvamF2YXNjcmlwdC10aW1lLWFnby9tb2R1bGVzL3N0ZXBzL2dldFN0ZXBEZW5vbWluYXRvci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE4QztBQUMvQjtBQUNmO0FBQ0E7QUFDQTtBQUNBLElBQUk7OztBQUdKLFNBQVMsMkRBQWdCO0FBQ3pCO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2phdmFzY3JpcHQtdGltZS1hZ28vbW9kdWxlcy9zdGVwcy9nZXRTdGVwRGVub21pbmF0b3IuanM/YzUwMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXRTZWNvbmRzSW5Vbml0IH0gZnJvbSAnLi91bml0cy5qcyc7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBnZXRTdGVwRGVub21pbmF0b3Ioc3RlcCkge1xuICAvLyBgZmFjdG9yYCBpcyBhIGxlZ2FjeSBwcm9wZXJ0eS5cbiAgaWYgKHN0ZXAuZmFjdG9yICE9PSB1bmRlZmluZWQpIHtcbiAgICByZXR1cm4gc3RlcC5mYWN0b3I7XG4gIH0gLy8gXCJ1bml0XCIgaXMgbm93IGNhbGxlZCBcImZvcm1hdEFzXCIuXG5cblxuICByZXR1cm4gZ2V0U2Vjb25kc0luVW5pdChzdGVwLnVuaXQgfHwgc3RlcC5mb3JtYXRBcykgfHwgMTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdldFN0ZXBEZW5vbWluYXRvci5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/steps/getStepDenominator.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/steps/getStepMinTime.js":
/*!**************************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/steps/getStepMinTime.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getStepMinTime)\n/* harmony export */ });\n/* harmony import */ var _units_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./units.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/steps/units.js\");\n/* harmony import */ var _round_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../round.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/round.js\");\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\n\n\nfunction getStepMinTime(step, _ref) {\n  var prevStep = _ref.prevStep,\n      timestamp = _ref.timestamp,\n      now = _ref.now,\n      future = _ref.future,\n      round = _ref.round;\n  var minTime; // \"threshold_for_xxx\" is a legacy property.\n\n  if (prevStep) {\n    if (prevStep.id || prevStep.unit) {\n      minTime = step[\"threshold_for_\".concat(prevStep.id || prevStep.unit)];\n    }\n  }\n\n  if (minTime === undefined) {\n    // \"threshold\" is a legacy property.\n    if (step.threshold !== undefined) {\n      // \"threshold\" is a legacy name for \"minTime\".\n      minTime = step.threshold; // \"threshold\" function is deprecated.\n\n      if (typeof minTime === 'function') {\n        minTime = minTime(now, future);\n      }\n    }\n  }\n\n  if (minTime === undefined) {\n    minTime = step.minTime;\n  } // A deprecated way of specifying a different threshold\n  // depending on the previous step's unit.\n\n\n  if (_typeof(minTime) === 'object') {\n    if (prevStep && prevStep.id && minTime[prevStep.id] !== undefined) {\n      minTime = minTime[prevStep.id];\n    } else {\n      minTime = minTime[\"default\"];\n    }\n  }\n\n  if (typeof minTime === 'function') {\n    minTime = minTime(timestamp, {\n      future: future,\n      getMinTimeForUnit: function getMinTimeForUnit(toUnit, fromUnit) {\n        return _getMinTimeForUnit(toUnit, fromUnit || prevStep && prevStep.formatAs, {\n          round: round\n        });\n      }\n    });\n  } // Evaluate the `test()` function.\n  // `test()` function is deprecated.\n\n\n  if (minTime === undefined) {\n    if (step.test) {\n      if (step.test(timestamp, {\n        now: now,\n        future: future\n      })) {\n        // `0` threshold always passes.\n        minTime = 0;\n      } else {\n        // `MAX_SAFE_INTEGER` threshold won't ever pass in real life.\n        minTime = 9007199254740991; // Number.MAX_SAFE_INTEGER\n      }\n    }\n  }\n\n  if (minTime === undefined) {\n    if (prevStep) {\n      if (step.formatAs && prevStep.formatAs) {\n        minTime = _getMinTimeForUnit(step.formatAs, prevStep.formatAs, {\n          round: round\n        });\n      }\n    } else {\n      // The first step's `minTime` is `0` by default.\n      minTime = 0;\n    }\n  } // Warn if no `minTime` was defined or could be deduced.\n\n\n  if (minTime === undefined) {\n    console.warn('[javascript-time-ago] A step should specify `minTime`:\\n' + JSON.stringify(step, null, 2));\n  }\n\n  return minTime;\n}\n\nfunction _getMinTimeForUnit(toUnit, fromUnit, _ref2) {\n  var round = _ref2.round;\n  var toUnitAmount = (0,_units_js__WEBPACK_IMPORTED_MODULE_0__.getSecondsInUnit)(toUnit); // if (!fromUnit) {\n  // \treturn toUnitAmount;\n  // }\n  // if (!fromUnit) {\n  // \tfromUnit = getPreviousUnitFor(toUnit)\n  // }\n\n  var fromUnitAmount;\n\n  if (fromUnit === 'now') {\n    fromUnitAmount = (0,_units_js__WEBPACK_IMPORTED_MODULE_0__.getSecondsInUnit)(toUnit);\n  } else {\n    fromUnitAmount = (0,_units_js__WEBPACK_IMPORTED_MODULE_0__.getSecondsInUnit)(fromUnit);\n  }\n\n  if (toUnitAmount !== undefined && fromUnitAmount !== undefined) {\n    return toUnitAmount - fromUnitAmount * (1 - (0,_round_js__WEBPACK_IMPORTED_MODULE_1__.getDiffRatioToNextRoundedNumber)(round));\n  }\n}\n//# sourceMappingURL=getStepMinTime.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/steps/getStepMinTime.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/steps/getTimeToNextUpdate.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/steps/getTimeToNextUpdate.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INFINITY: () => (/* binding */ INFINITY),\n/* harmony export */   \"default\": () => (/* binding */ getTimeToNextUpdate),\n/* harmony export */   getStepChangesAt: () => (/* binding */ getStepChangesAt),\n/* harmony export */   getTimeToStepChange: () => (/* binding */ getTimeToStepChange)\n/* harmony export */ });\n/* harmony import */ var _getTimeToNextUpdateForUnit_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getTimeToNextUpdateForUnit.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/steps/getTimeToNextUpdateForUnit.js\");\n/* harmony import */ var _getStepMinTime_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getStepMinTime.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/steps/getStepMinTime.js\");\n/* harmony import */ var _round_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../round.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/round.js\");\n\n\n // A thousand years is practically a metaphor for \"infinity\".\n\nvar YEAR = 365 * 24 * 60 * 60 * 1000;\nvar INFINITY = 1000 * YEAR;\n/**\r\n * Gets the time to next update for a date and a step.\r\n * @param  {number} date — The date passed to `.format()`, converted to a timestamp.\r\n * @param  {object} step\r\n * @param  {object} [options.previousStep]\r\n * @param  {object} [options.nextStep]\r\n * @param  {number} options.now\r\n * @param  {boolean} options.future\r\n * @param  {string} [options.round] - (undocumented) Rounding mechanism.\r\n * @return {number} [timeToNextUpdate]\r\n */\n\nfunction getTimeToNextUpdate(date, step, _ref) {\n  var prevStep = _ref.prevStep,\n      nextStep = _ref.nextStep,\n      now = _ref.now,\n      future = _ref.future,\n      round = _ref.round;\n  var timestamp = date.getTime ? date.getTime() : date;\n\n  var getTimeToNextUpdateForUnit = function getTimeToNextUpdateForUnit(unit) {\n    return (0,_getTimeToNextUpdateForUnit_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(unit, timestamp, {\n      now: now,\n      round: round\n    });\n  }; // For future dates, steps move from the last one to the first one,\n  // while for past dates, steps move from the first one to the last one,\n  // due to the fact that time flows in one direction,\n  // and future dates' interval naturally becomes smaller\n  // while past dates' interval naturally grows larger.\n  //\n  // For future dates, it's the transition\n  // from the current step to the previous step,\n  // therefore check the `minTime` of the current step.\n  //\n  // For past dates, it's the transition\n  // from the current step to the next step,\n  // therefore check the `minTime` of the next step.\n  //\n\n\n  var timeToStepChange = getTimeToStepChange(future ? step : nextStep, timestamp, {\n    future: future,\n    now: now,\n    round: round,\n    prevStep: future ? prevStep : step // isFirstStep: future && isFirstStep\n\n  });\n\n  if (timeToStepChange === undefined) {\n    // Can't reliably determine \"time to next update\"\n    // if not all of the steps provide `minTime`.\n    return;\n  }\n\n  var timeToNextUpdate;\n\n  if (step) {\n    if (step.getTimeToNextUpdate) {\n      timeToNextUpdate = step.getTimeToNextUpdate(timestamp, {\n        getTimeToNextUpdateForUnit: getTimeToNextUpdateForUnit,\n        getRoundFunction: _round_js__WEBPACK_IMPORTED_MODULE_1__.getRoundFunction,\n        now: now,\n        future: future,\n        round: round\n      });\n    }\n\n    if (timeToNextUpdate === undefined) {\n      // \"unit\" is now called \"formatAs\".\n      var unit = step.unit || step.formatAs;\n\n      if (unit) {\n        // For some units, like \"now\", there's no defined amount of seconds in them.\n        // In such cases, `getTimeToNextUpdateForUnit()` returns `undefined`,\n        // and the next step's `minTime` could be used to calculate the update interval:\n        // it will just assume that the label never changes for this step.\n        timeToNextUpdate = getTimeToNextUpdateForUnit(unit);\n      }\n    }\n  }\n\n  if (timeToNextUpdate === undefined) {\n    return timeToStepChange;\n  }\n\n  return Math.min(timeToNextUpdate, timeToStepChange);\n}\nfunction getStepChangesAt(currentOrNextStep, timestamp, _ref2) {\n  var now = _ref2.now,\n      future = _ref2.future,\n      round = _ref2.round,\n      prevStep = _ref2.prevStep;\n  // The first step's `minTime` is `0` by default.\n  // It doesn't \"change\" steps at zero point\n  // but it does change the wording when switching\n  // from \"future\" to \"past\": \"in ...\" -> \"... ago\".\n  // Therefore, the label should be updated at zero-point too.\n  var minTime = (0,_getStepMinTime_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(currentOrNextStep, {\n    timestamp: timestamp,\n    now: now,\n    future: future,\n    round: round,\n    prevStep: prevStep\n  });\n\n  if (minTime === undefined) {\n    return;\n  }\n\n  if (future) {\n    // The step changes to the previous step\n    // as soon as `timestamp - now` becomes\n    // less than the `minTime` of the current step:\n    // `timestamp - now === minTime - 1`\n    // => `now === timestamp - minTime + 1`.\n    return timestamp - minTime * 1000 + 1;\n  } else {\n    // The step changes to the next step\n    // as soon as `now - timestamp` becomes\n    // equal to `minTime` of the next step:\n    // `now - timestamp === minTime`\n    // => `now === timestamp + minTime`.\n    // This is a special case when double-update could be skipped.\n    if (minTime === 0 && timestamp === now) {\n      return INFINITY;\n    }\n\n    return timestamp + minTime * 1000;\n  }\n}\nfunction getTimeToStepChange(step, timestamp, _ref3) {\n  var now = _ref3.now,\n      future = _ref3.future,\n      round = _ref3.round,\n      prevStep = _ref3.prevStep;\n\n  if (step) {\n    var stepChangesAt = getStepChangesAt(step, timestamp, {\n      now: now,\n      future: future,\n      round: round,\n      prevStep: prevStep\n    });\n\n    if (stepChangesAt === undefined) {\n      return;\n    }\n\n    return stepChangesAt - now;\n  } else {\n    if (future) {\n      // No step.\n      // Update right after zero point, when it changes from \"future\" to \"past\".\n      return timestamp - now + 1;\n    } else {\n      // The last step doesn't ever change when `date` is in the past.\n      return INFINITY;\n    }\n  }\n}\n//# sourceMappingURL=getTimeToNextUpdate.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/steps/getTimeToNextUpdate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/steps/getTimeToNextUpdateForUnit.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/steps/getTimeToNextUpdateForUnit.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getTimeToNextUpdateForUnit)\n/* harmony export */ });\n/* harmony import */ var _units_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./units.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/steps/units.js\");\n/* harmony import */ var _round_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../round.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/round.js\");\n\n\n/**\r\n * Gets the time to next update for a step with a time unit defined.\r\n * @param  {string} unit\r\n * @param  {number} date — The date passed to `.format()`, converted to a timestamp.\r\n * @param  {number} options.now\r\n * @param  {string} [options.round] — (undocumented) Rounding mechanism.\r\n * @return {number} [timeToNextUpdate]\r\n */\n\nfunction getTimeToNextUpdateForUnit(unit, timestamp, _ref) {\n  var now = _ref.now,\n      round = _ref.round;\n\n  // For some units, like \"now\", there's no defined amount of seconds in them.\n  if (!(0,_units_js__WEBPACK_IMPORTED_MODULE_0__.getSecondsInUnit)(unit)) {\n    // If there's no amount of seconds defined for this unit\n    // then the update interval can't be determined reliably.\n    return;\n  }\n\n  var unitDenominator = (0,_units_js__WEBPACK_IMPORTED_MODULE_0__.getSecondsInUnit)(unit) * 1000;\n  var future = timestamp > now;\n  var preciseAmount = Math.abs(timestamp - now);\n  var roundedAmount = (0,_round_js__WEBPACK_IMPORTED_MODULE_1__.getRoundFunction)(round)(preciseAmount / unitDenominator) * unitDenominator;\n\n  if (future) {\n    if (roundedAmount > 0) {\n      // Amount decreases with time.\n      return preciseAmount - roundedAmount + getDiffToPreviousRoundedNumber(round, unitDenominator);\n    } else {\n      // Refresh right after the zero point,\n      // when \"future\" changes to \"past\".\n      return preciseAmount - roundedAmount + 1;\n    }\n  } // Amount increases with time.\n\n\n  return -(preciseAmount - roundedAmount) + getDiffToNextRoundedNumber(round, unitDenominator);\n}\n\nfunction getDiffToNextRoundedNumber(round, unitDenominator) {\n  return (0,_round_js__WEBPACK_IMPORTED_MODULE_1__.getDiffRatioToNextRoundedNumber)(round) * unitDenominator;\n}\n\nfunction getDiffToPreviousRoundedNumber(round, unitDenominator) {\n  return (1 - (0,_round_js__WEBPACK_IMPORTED_MODULE_1__.getDiffRatioToNextRoundedNumber)(round)) * unitDenominator + 1;\n}\n//# sourceMappingURL=getTimeToNextUpdateForUnit.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/steps/getTimeToNextUpdateForUnit.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/steps/helpers.js":
/*!*******************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/steps/helpers.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDate: () => (/* binding */ getDate)\n/* harmony export */ });\n// Looks like this one's deprecated.\n// /**\n//  * Returns a step corresponding to the unit.\n//  * @param  {Object[]} steps\n//  * @param  {string} unit\n//  * @return {?Object}\n//  */\n// export function getStepForUnit(steps, unit) {\n// \tfor (const step of steps) {\n// \t\tif (step.unit === unit) {\n// \t\t\treturn step\n// \t\t}\n// \t}\n// }\n// Looks like this one won't be used in the next major version.\n\n/**\r\n * Converts value to a `Date`\r\n * @param {(number|Date)} value\r\n * @return {Date}\r\n */\nfunction getDate(value) {\n  return value instanceof Date ? value : new Date(value);\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvamF2YXNjcmlwdC10aW1lLWFnby9tb2R1bGVzL3N0ZXBzL2hlbHBlcnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsVUFBVTtBQUN6QixlQUFlLFFBQVE7QUFDdkIsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsV0FBVyxlQUFlO0FBQzFCLFlBQVk7QUFDWjtBQUNPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9qYXZhc2NyaXB0LXRpbWUtYWdvL21vZHVsZXMvc3RlcHMvaGVscGVycy5qcz9hMTRkIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIExvb2tzIGxpa2UgdGhpcyBvbmUncyBkZXByZWNhdGVkLlxuLy8gLyoqXG4vLyAgKiBSZXR1cm5zIGEgc3RlcCBjb3JyZXNwb25kaW5nIHRvIHRoZSB1bml0LlxuLy8gICogQHBhcmFtICB7T2JqZWN0W119IHN0ZXBzXG4vLyAgKiBAcGFyYW0gIHtzdHJpbmd9IHVuaXRcbi8vICAqIEByZXR1cm4gez9PYmplY3R9XG4vLyAgKi9cbi8vIGV4cG9ydCBmdW5jdGlvbiBnZXRTdGVwRm9yVW5pdChzdGVwcywgdW5pdCkge1xuLy8gXHRmb3IgKGNvbnN0IHN0ZXAgb2Ygc3RlcHMpIHtcbi8vIFx0XHRpZiAoc3RlcC51bml0ID09PSB1bml0KSB7XG4vLyBcdFx0XHRyZXR1cm4gc3RlcFxuLy8gXHRcdH1cbi8vIFx0fVxuLy8gfVxuLy8gTG9va3MgbGlrZSB0aGlzIG9uZSB3b24ndCBiZSB1c2VkIGluIHRoZSBuZXh0IG1ham9yIHZlcnNpb24uXG5cbi8qKlxyXG4gKiBDb252ZXJ0cyB2YWx1ZSB0byBhIGBEYXRlYFxyXG4gKiBAcGFyYW0geyhudW1iZXJ8RGF0ZSl9IHZhbHVlXHJcbiAqIEByZXR1cm4ge0RhdGV9XHJcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldERhdGUodmFsdWUpIHtcbiAgcmV0dXJuIHZhbHVlIGluc3RhbmNlb2YgRGF0ZSA/IHZhbHVlIDogbmV3IERhdGUodmFsdWUpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aGVscGVycy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/steps/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/steps/round.js":
/*!*****************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/steps/round.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// just now\n// 1 second ago\n// 2 seconds ago\n// …\n// 59 seconds ago\n// 1 minute ago\n// 2 minutes ago\n// …\n// 59 minutes ago\n// 1 hour ago\n// 2 hours ago\n// …\n// 24 hours ago\n// 1 day ago\n// 2 days ago\n// …\n// 6 days ago\n// 1 week ago\n// 2 weeks ago\n// …\n// 3 weeks ago\n// 1 month ago\n// 2 months ago\n// …\n// 11 months ago\n// 1 year ago\n// 2 years ago\n// …\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([{\n  formatAs: 'now'\n}, {\n  formatAs: 'second'\n}, {\n  formatAs: 'minute'\n}, {\n  formatAs: 'hour'\n}, {\n  formatAs: 'day'\n}, {\n  formatAs: 'week'\n}, {\n  formatAs: 'month'\n}, {\n  formatAs: 'year'\n}]);\n//# sourceMappingURL=round.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvamF2YXNjcmlwdC10aW1lLWFnby9tb2R1bGVzL3N0ZXBzL3JvdW5kLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlFQUFlO0FBQ2Y7QUFDQSxDQUFDO0FBQ0Q7QUFDQSxDQUFDO0FBQ0Q7QUFDQSxDQUFDO0FBQ0Q7QUFDQSxDQUFDO0FBQ0Q7QUFDQSxDQUFDO0FBQ0Q7QUFDQSxDQUFDO0FBQ0Q7QUFDQSxDQUFDO0FBQ0Q7QUFDQSxDQUFDLENBQUMsRUFBQztBQUNIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9qYXZhc2NyaXB0LXRpbWUtYWdvL21vZHVsZXMvc3RlcHMvcm91bmQuanM/NjVlOCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBqdXN0IG5vd1xuLy8gMSBzZWNvbmQgYWdvXG4vLyAyIHNlY29uZHMgYWdvXG4vLyDigKZcbi8vIDU5IHNlY29uZHMgYWdvXG4vLyAxIG1pbnV0ZSBhZ29cbi8vIDIgbWludXRlcyBhZ29cbi8vIOKAplxuLy8gNTkgbWludXRlcyBhZ29cbi8vIDEgaG91ciBhZ29cbi8vIDIgaG91cnMgYWdvXG4vLyDigKZcbi8vIDI0IGhvdXJzIGFnb1xuLy8gMSBkYXkgYWdvXG4vLyAyIGRheXMgYWdvXG4vLyDigKZcbi8vIDYgZGF5cyBhZ29cbi8vIDEgd2VlayBhZ29cbi8vIDIgd2Vla3MgYWdvXG4vLyDigKZcbi8vIDMgd2Vla3MgYWdvXG4vLyAxIG1vbnRoIGFnb1xuLy8gMiBtb250aHMgYWdvXG4vLyDigKZcbi8vIDExIG1vbnRocyBhZ29cbi8vIDEgeWVhciBhZ29cbi8vIDIgeWVhcnMgYWdvXG4vLyDigKZcbmV4cG9ydCBkZWZhdWx0IFt7XG4gIGZvcm1hdEFzOiAnbm93J1xufSwge1xuICBmb3JtYXRBczogJ3NlY29uZCdcbn0sIHtcbiAgZm9ybWF0QXM6ICdtaW51dGUnXG59LCB7XG4gIGZvcm1hdEFzOiAnaG91cidcbn0sIHtcbiAgZm9ybWF0QXM6ICdkYXknXG59LCB7XG4gIGZvcm1hdEFzOiAnd2Vlaydcbn0sIHtcbiAgZm9ybWF0QXM6ICdtb250aCdcbn0sIHtcbiAgZm9ybWF0QXM6ICd5ZWFyJ1xufV07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yb3VuZC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/steps/round.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/steps/units.js":
/*!*****************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/steps/units.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   day: () => (/* binding */ day),\n/* harmony export */   getSecondsInUnit: () => (/* binding */ getSecondsInUnit),\n/* harmony export */   hour: () => (/* binding */ hour),\n/* harmony export */   minute: () => (/* binding */ minute),\n/* harmony export */   month: () => (/* binding */ month),\n/* harmony export */   week: () => (/* binding */ week),\n/* harmony export */   year: () => (/* binding */ year)\n/* harmony export */ });\nvar minute = 60; // in seconds\n\nvar hour = 60 * minute; // in seconds\n\nvar day = 24 * hour; // in seconds\n\nvar week = 7 * day; // in seconds\n// https://www.quora.com/What-is-the-average-number-of-days-in-a-month\n\nvar month = 30.44 * day; // in seconds\n// \"400 years have 146097 days (taking into account leap year rules)\"\n\nvar year = 146097 / 400 * day; // in seconds\n\nfunction getSecondsInUnit(unit) {\n  switch (unit) {\n    case 'second':\n      return 1;\n\n    case 'minute':\n      return minute;\n\n    case 'hour':\n      return hour;\n\n    case 'day':\n      return day;\n\n    case 'week':\n      return week;\n\n    case 'month':\n      return month;\n\n    case 'year':\n      return year;\n  }\n} // export function getPreviousUnitFor(unit) {\n// \tswitch (unit) {\n// \t\tcase 'second':\n// \t\t\treturn 'now'\n// \t\tcase 'minute':\n// \t\t\treturn 'second'\n// \t\tcase 'hour':\n// \t\t\treturn 'minute'\n// \t\tcase 'day':\n// \t\t\treturn 'hour'\n// \t\tcase 'week':\n// \t\t\treturn 'day'\n// \t\tcase 'month':\n// \t\t\treturn 'week'\n// \t\tcase 'year':\n// \t\t\treturn 'month'\n// \t}\n// }\n//# sourceMappingURL=units.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/steps/units.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/style/approximate.js":
/*!***********************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/style/approximate.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _steps_approximate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../steps/approximate.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/steps/approximate.js\");\n // \"gradation\" is a legacy name for \"steps\".\n// It's here just for legacy compatibility.\n// Use \"steps\" name instead.\n// \"flavour\" is a legacy name for \"labels\".\n// It's here just for legacy compatibility.\n// Use \"labels\" name instead.\n// \"units\" is a legacy property.\n// It's here just for legacy compatibility.\n// Developers shouldn't need to use it in their custom styles.\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  gradation: _steps_approximate_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n  flavour: 'long',\n  units: ['now', 'minute', 'hour', 'day', 'week', 'month', 'year']\n});\n//# sourceMappingURL=approximate.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvamF2YXNjcmlwdC10aW1lLWFnby9tb2R1bGVzL3N0eWxlL2FwcHJveGltYXRlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWtELENBQUM7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxpRUFBZTtBQUNmLGFBQWEsNkRBQVc7QUFDeEI7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9qYXZhc2NyaXB0LXRpbWUtYWdvL21vZHVsZXMvc3R5bGUvYXBwcm94aW1hdGUuanM/NWYyNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXBwcm94aW1hdGUgZnJvbSAnLi4vc3RlcHMvYXBwcm94aW1hdGUuanMnOyAvLyBcImdyYWRhdGlvblwiIGlzIGEgbGVnYWN5IG5hbWUgZm9yIFwic3RlcHNcIi5cbi8vIEl0J3MgaGVyZSBqdXN0IGZvciBsZWdhY3kgY29tcGF0aWJpbGl0eS5cbi8vIFVzZSBcInN0ZXBzXCIgbmFtZSBpbnN0ZWFkLlxuLy8gXCJmbGF2b3VyXCIgaXMgYSBsZWdhY3kgbmFtZSBmb3IgXCJsYWJlbHNcIi5cbi8vIEl0J3MgaGVyZSBqdXN0IGZvciBsZWdhY3kgY29tcGF0aWJpbGl0eS5cbi8vIFVzZSBcImxhYmVsc1wiIG5hbWUgaW5zdGVhZC5cbi8vIFwidW5pdHNcIiBpcyBhIGxlZ2FjeSBwcm9wZXJ0eS5cbi8vIEl0J3MgaGVyZSBqdXN0IGZvciBsZWdhY3kgY29tcGF0aWJpbGl0eS5cbi8vIERldmVsb3BlcnMgc2hvdWxkbid0IG5lZWQgdG8gdXNlIGl0IGluIHRoZWlyIGN1c3RvbSBzdHlsZXMuXG5cbmV4cG9ydCBkZWZhdWx0IHtcbiAgZ3JhZGF0aW9uOiBhcHByb3hpbWF0ZSxcbiAgZmxhdm91cjogJ2xvbmcnLFxuICB1bml0czogWydub3cnLCAnbWludXRlJywgJ2hvdXInLCAnZGF5JywgJ3dlZWsnLCAnbW9udGgnLCAneWVhciddXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwcm94aW1hdGUuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/style/approximate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/style/approximateTime.js":
/*!***************************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/style/approximateTime.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _steps_approximate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../steps/approximate.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/steps/approximate.js\");\n // \"gradation\" is a legacy name for \"steps\".\n// It's here just for legacy compatibility.\n// Use \"steps\" name instead.\n// \"flavour\" is a legacy name for \"labels\".\n// It's here just for legacy compatibility.\n// Use \"labels\" name instead.\n// \"units\" is a legacy property.\n// It's here just for legacy compatibility.\n// Developers shouldn't need to use it in their custom styles.\n// Similar to the default style but with \"ago\" omitted.\n//\n// just now\n// 5 minutes\n// 10 minutes\n// 15 minutes\n// 20 minutes\n// an hour\n// 2 hours\n// …\n// 20 hours\n// 1 day\n// 2 days\n// a week\n// 2 weeks\n// 3 weeks\n// a month\n// 2 months\n// 3 months\n// 4 months\n// a year\n// 2 years\n//\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  gradation: _steps_approximate_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n  flavour: 'long-time',\n  units: ['now', 'minute', 'hour', 'day', 'week', 'month', 'year']\n});\n//# sourceMappingURL=approximateTime.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvamF2YXNjcmlwdC10aW1lLWFnby9tb2R1bGVzL3N0eWxlL2FwcHJveGltYXRlVGltZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrRCxDQUFDO0FBQ25EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGlFQUFlO0FBQ2YsYUFBYSw2REFBVztBQUN4QjtBQUNBO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2phdmFzY3JpcHQtdGltZS1hZ28vbW9kdWxlcy9zdHlsZS9hcHByb3hpbWF0ZVRpbWUuanM/NmY4MCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXBwcm94aW1hdGUgZnJvbSAnLi4vc3RlcHMvYXBwcm94aW1hdGUuanMnOyAvLyBcImdyYWRhdGlvblwiIGlzIGEgbGVnYWN5IG5hbWUgZm9yIFwic3RlcHNcIi5cbi8vIEl0J3MgaGVyZSBqdXN0IGZvciBsZWdhY3kgY29tcGF0aWJpbGl0eS5cbi8vIFVzZSBcInN0ZXBzXCIgbmFtZSBpbnN0ZWFkLlxuLy8gXCJmbGF2b3VyXCIgaXMgYSBsZWdhY3kgbmFtZSBmb3IgXCJsYWJlbHNcIi5cbi8vIEl0J3MgaGVyZSBqdXN0IGZvciBsZWdhY3kgY29tcGF0aWJpbGl0eS5cbi8vIFVzZSBcImxhYmVsc1wiIG5hbWUgaW5zdGVhZC5cbi8vIFwidW5pdHNcIiBpcyBhIGxlZ2FjeSBwcm9wZXJ0eS5cbi8vIEl0J3MgaGVyZSBqdXN0IGZvciBsZWdhY3kgY29tcGF0aWJpbGl0eS5cbi8vIERldmVsb3BlcnMgc2hvdWxkbid0IG5lZWQgdG8gdXNlIGl0IGluIHRoZWlyIGN1c3RvbSBzdHlsZXMuXG4vLyBTaW1pbGFyIHRvIHRoZSBkZWZhdWx0IHN0eWxlIGJ1dCB3aXRoIFwiYWdvXCIgb21pdHRlZC5cbi8vXG4vLyBqdXN0IG5vd1xuLy8gNSBtaW51dGVzXG4vLyAxMCBtaW51dGVzXG4vLyAxNSBtaW51dGVzXG4vLyAyMCBtaW51dGVzXG4vLyBhbiBob3VyXG4vLyAyIGhvdXJzXG4vLyDigKZcbi8vIDIwIGhvdXJzXG4vLyAxIGRheVxuLy8gMiBkYXlzXG4vLyBhIHdlZWtcbi8vIDIgd2Vla3Ncbi8vIDMgd2Vla3Ncbi8vIGEgbW9udGhcbi8vIDIgbW9udGhzXG4vLyAzIG1vbnRoc1xuLy8gNCBtb250aHNcbi8vIGEgeWVhclxuLy8gMiB5ZWFyc1xuLy9cblxuZXhwb3J0IGRlZmF1bHQge1xuICBncmFkYXRpb246IGFwcHJveGltYXRlLFxuICBmbGF2b3VyOiAnbG9uZy10aW1lJyxcbiAgdW5pdHM6IFsnbm93JywgJ21pbnV0ZScsICdob3VyJywgJ2RheScsICd3ZWVrJywgJ21vbnRoJywgJ3llYXInXVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcHJveGltYXRlVGltZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/style/approximateTime.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/style/getStyleByName.js":
/*!**************************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/style/getStyleByName.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getStyleByName)\n/* harmony export */ });\n/* harmony import */ var _round_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./round.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/round.js\");\n/* harmony import */ var _roundMinute_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./roundMinute.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/roundMinute.js\");\n/* harmony import */ var _approximate_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./approximate.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/approximate.js\");\n/* harmony import */ var _approximateTime_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./approximateTime.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/approximateTime.js\");\n/* harmony import */ var _twitter_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./twitter.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/twitter.js\");\n/* harmony import */ var _twitterNow_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./twitterNow.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/twitterNow.js\");\n/* harmony import */ var _twitterMinute_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./twitterMinute.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/twitterMinute.js\");\n/* harmony import */ var _twitterMinuteNow_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./twitterMinuteNow.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/twitterMinuteNow.js\");\n/* harmony import */ var _twitterFirstMinute_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./twitterFirstMinute.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/twitterFirstMinute.js\");\n/* harmony import */ var _mini_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./mini.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/mini.js\");\n/* harmony import */ var _miniNow_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./miniNow.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/miniNow.js\");\n/* harmony import */ var _miniMinute_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./miniMinute.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/miniMinute.js\");\n/* harmony import */ var _miniMinuteNow_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./miniMinuteNow.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/miniMinuteNow.js\");\n\n // `approximate` style is deprecated.\n\n // `approximateTime` style is deprecated.\n\n\n\n\n\n\n\n\n\n\n\nfunction getStyleByName(style) {\n  switch (style) {\n    // \"default\" style name is deprecated.\n    case 'default':\n    case 'round':\n      return _round_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n\n    case 'round-minute':\n      return _roundMinute_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n\n    case 'approximate':\n      return _approximate_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"];\n    // \"time\" style name is deprecated.\n\n    case 'time':\n    case 'approximate-time':\n      return _approximateTime_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n\n    case 'mini':\n      return _mini_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n\n    case 'mini-now':\n      return _miniNow_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n\n    case 'mini-minute':\n      return _miniMinute_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n\n    case 'mini-minute-now':\n      return _miniMinuteNow_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n\n    case 'twitter':\n      return _twitter_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n\n    case 'twitter-now':\n      return _twitterNow_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n\n    case 'twitter-minute':\n      return _twitterMinute_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n\n    case 'twitter-minute-now':\n      return _twitterMinuteNow_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"];\n\n    case 'twitter-first-minute':\n      return _twitterFirstMinute_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n\n    default:\n      // For historical reasons, the default style is \"approximate\".\n      return _approximate_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"];\n  }\n}\n//# sourceMappingURL=getStyleByName.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/style/getStyleByName.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/style/mini.js":
/*!****************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/style/mini.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  steps: [{\n    formatAs: 'second'\n  }, {\n    formatAs: 'minute'\n  }, {\n    formatAs: 'hour'\n  }, {\n    formatAs: 'day'\n  }, {\n    formatAs: 'month'\n  }, {\n    formatAs: 'year'\n  }],\n  labels: [// \"mini\" labels are only defined for a few languages.\n  'mini', // \"short-time\" labels are only defined for a few languages.\n  'short-time', // \"narrow\" and \"short\" labels are defined for all languages.\n  // \"narrow\" labels can sometimes be weird (like \"+5d.\"),\n  // but \"short\" labels have the \" ago\" part, so \"narrow\" seem\n  // more appropriate.\n  // \"short\" labels would have been more appropriate if they\n  // didn't have the \" ago\" part, hence the \"short-time\" above.\n  'narrow', // Since \"narrow\" labels are always present, \"short\" element\n  // of this array can be removed.\n  'short']\n});\n//# sourceMappingURL=mini.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvamF2YXNjcmlwdC10aW1lLWFnby9tb2R1bGVzL3N0eWxlL21pbmkuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlO0FBQ2Y7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLEdBQUc7QUFDSDtBQUNBLEdBQUc7QUFDSDtBQUNBLEdBQUc7QUFDSDtBQUNBLEdBQUc7QUFDSDtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2phdmFzY3JpcHQtdGltZS1hZ28vbW9kdWxlcy9zdHlsZS9taW5pLmpzPzVmYzkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1xuICBzdGVwczogW3tcbiAgICBmb3JtYXRBczogJ3NlY29uZCdcbiAgfSwge1xuICAgIGZvcm1hdEFzOiAnbWludXRlJ1xuICB9LCB7XG4gICAgZm9ybWF0QXM6ICdob3VyJ1xuICB9LCB7XG4gICAgZm9ybWF0QXM6ICdkYXknXG4gIH0sIHtcbiAgICBmb3JtYXRBczogJ21vbnRoJ1xuICB9LCB7XG4gICAgZm9ybWF0QXM6ICd5ZWFyJ1xuICB9XSxcbiAgbGFiZWxzOiBbLy8gXCJtaW5pXCIgbGFiZWxzIGFyZSBvbmx5IGRlZmluZWQgZm9yIGEgZmV3IGxhbmd1YWdlcy5cbiAgJ21pbmknLCAvLyBcInNob3J0LXRpbWVcIiBsYWJlbHMgYXJlIG9ubHkgZGVmaW5lZCBmb3IgYSBmZXcgbGFuZ3VhZ2VzLlxuICAnc2hvcnQtdGltZScsIC8vIFwibmFycm93XCIgYW5kIFwic2hvcnRcIiBsYWJlbHMgYXJlIGRlZmluZWQgZm9yIGFsbCBsYW5ndWFnZXMuXG4gIC8vIFwibmFycm93XCIgbGFiZWxzIGNhbiBzb21ldGltZXMgYmUgd2VpcmQgKGxpa2UgXCIrNWQuXCIpLFxuICAvLyBidXQgXCJzaG9ydFwiIGxhYmVscyBoYXZlIHRoZSBcIiBhZ29cIiBwYXJ0LCBzbyBcIm5hcnJvd1wiIHNlZW1cbiAgLy8gbW9yZSBhcHByb3ByaWF0ZS5cbiAgLy8gXCJzaG9ydFwiIGxhYmVscyB3b3VsZCBoYXZlIGJlZW4gbW9yZSBhcHByb3ByaWF0ZSBpZiB0aGV5XG4gIC8vIGRpZG4ndCBoYXZlIHRoZSBcIiBhZ29cIiBwYXJ0LCBoZW5jZSB0aGUgXCJzaG9ydC10aW1lXCIgYWJvdmUuXG4gICduYXJyb3cnLCAvLyBTaW5jZSBcIm5hcnJvd1wiIGxhYmVscyBhcmUgYWx3YXlzIHByZXNlbnQsIFwic2hvcnRcIiBlbGVtZW50XG4gIC8vIG9mIHRoaXMgYXJyYXkgY2FuIGJlIHJlbW92ZWQuXG4gICdzaG9ydCddXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bWluaS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/style/mini.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/style/miniMinute.js":
/*!**********************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/style/miniMinute.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _mini_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mini.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/mini.js\");\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_objectSpread(_objectSpread({}, _mini_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]), {}, {\n  // Skip \"seconds\".\n  steps: _mini_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].steps.filter(function (step) {\n    return step.formatAs !== 'second';\n  })\n}));\n//# sourceMappingURL=miniMinute.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/style/miniMinute.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/style/miniMinuteNow.js":
/*!*************************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/style/miniMinuteNow.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _miniMinute_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./miniMinute.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/miniMinute.js\");\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_objectSpread(_objectSpread({}, _miniMinute_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]), {}, {\n  // Add \"now\".\n  steps: [{\n    formatAs: 'now'\n  }].concat(_miniMinute_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].steps)\n}));\n//# sourceMappingURL=miniMinuteNow.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/style/miniMinuteNow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/style/miniNow.js":
/*!*******************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/style/miniNow.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _mini_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mini.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/mini.js\");\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_objectSpread(_objectSpread({}, _mini_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]), {}, {\n  // Add \"now\".\n  steps: [{\n    formatAs: 'now'\n  }].concat(_mini_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].steps)\n}));\n//# sourceMappingURL=miniNow.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/style/miniNow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/style/round.js":
/*!*****************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/style/round.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _steps_round_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../steps/round.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/steps/round.js\");\n // just now\n// 1 second ago\n// 2 seconds ago\n// …\n// 59 seconds ago\n// 1 minute ago\n// 2 minutes ago\n// …\n// 59 minutes ago\n// 1 minute ago\n// 2 minutes ago\n// …\n// 59 minutes ago\n// 1 hour ago\n// 2 hours ago\n// …\n// 24 hours ago\n// 1 day ago\n// 2 days ago\n// …\n// 6 days ago\n// 1 week ago\n// 2 weeks ago\n// 3 weeks ago\n// 4 weeks ago\n// 1 month ago\n// 2 months ago\n// …\n// 11 months ago\n// 1 year ago\n// 2 years ago\n// …\n//\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  steps: _steps_round_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n  labels: 'long'\n});\n//# sourceMappingURL=round.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvamF2YXNjcmlwdC10aW1lLWFnby9tb2R1bGVzL3N0eWxlL3JvdW5kLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNDLENBQUM7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxpRUFBZTtBQUNmLFNBQVMsdURBQUs7QUFDZDtBQUNBLENBQUMsRUFBQztBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9qYXZhc2NyaXB0LXRpbWUtYWdvL21vZHVsZXMvc3R5bGUvcm91bmQuanM/YmI5YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgcm91bmQgZnJvbSAnLi4vc3RlcHMvcm91bmQuanMnOyAvLyBqdXN0IG5vd1xuLy8gMSBzZWNvbmQgYWdvXG4vLyAyIHNlY29uZHMgYWdvXG4vLyDigKZcbi8vIDU5IHNlY29uZHMgYWdvXG4vLyAxIG1pbnV0ZSBhZ29cbi8vIDIgbWludXRlcyBhZ29cbi8vIOKAplxuLy8gNTkgbWludXRlcyBhZ29cbi8vIDEgbWludXRlIGFnb1xuLy8gMiBtaW51dGVzIGFnb1xuLy8g4oCmXG4vLyA1OSBtaW51dGVzIGFnb1xuLy8gMSBob3VyIGFnb1xuLy8gMiBob3VycyBhZ29cbi8vIOKAplxuLy8gMjQgaG91cnMgYWdvXG4vLyAxIGRheSBhZ29cbi8vIDIgZGF5cyBhZ29cbi8vIOKAplxuLy8gNiBkYXlzIGFnb1xuLy8gMSB3ZWVrIGFnb1xuLy8gMiB3ZWVrcyBhZ29cbi8vIDMgd2Vla3MgYWdvXG4vLyA0IHdlZWtzIGFnb1xuLy8gMSBtb250aCBhZ29cbi8vIDIgbW9udGhzIGFnb1xuLy8g4oCmXG4vLyAxMSBtb250aHMgYWdvXG4vLyAxIHllYXIgYWdvXG4vLyAyIHllYXJzIGFnb1xuLy8g4oCmXG4vL1xuXG5leHBvcnQgZGVmYXVsdCB7XG4gIHN0ZXBzOiByb3VuZCxcbiAgbGFiZWxzOiAnbG9uZydcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yb3VuZC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/style/round.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/style/roundMinute.js":
/*!***********************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/style/roundMinute.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _round_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./round.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/round.js\");\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n // just now\n// 1 minute ago\n// 2 minutes ago\n// …\n// 59 minutes ago\n// 1 minute ago\n// 2 minutes ago\n// …\n// 59 minutes ago\n// 1 hour ago\n// 2 hours ago\n// …\n// 24 hours ago\n// 1 day ago\n// 2 days ago\n// …\n// 6 days ago\n// 1 week ago\n// 2 weeks ago\n// 3 weeks ago\n// 4 weeks ago\n// 1 month ago\n// 2 months ago\n// …\n// 11 months ago\n// 1 year ago\n// 2 years ago\n// …\n//\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_objectSpread(_objectSpread({}, _round_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]), {}, {\n  // Skip \"seconds\".\n  steps: _round_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].steps.filter(function (step) {\n    return step.formatAs !== 'second';\n  })\n}));\n//# sourceMappingURL=roundMinute.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/style/roundMinute.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/style/twitter.js":
/*!*******************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/style/twitter.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _steps_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../steps/index.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/steps/helpers.js\");\n/* harmony import */ var _locale_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../locale.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/locale.js\");\n\n // For compatibility with the old versions of this library.\n\n // Twitter-style relative date/time formatting.\n// (\"1m\", \"2h\", \"Mar 3\", \"Apr 4, 2012\").\n//\n// Seconds, minutes or hours are shown for shorter intervals,\n// and longer intervals are formatted using full date format.\n\nvar steps = [{\n  formatAs: 'second'\n}, {\n  formatAs: 'minute'\n}, {\n  formatAs: 'hour'\n}]; // A cache for `Intl.DateTimeFormat` formatters\n// for various locales (is a global variable).\n\nvar formatters = {}; // Starting from day intervals, output month and day.\n\nvar monthAndDay = {\n  minTime: function minTime(timestamp, _ref) {\n    var future = _ref.future,\n        getMinTimeForUnit = _ref.getMinTimeForUnit;\n    // Returns `23.5 * 60 * 60` when `round` is \"round\",\n    // and `24 * 60 * 60` when `round` is \"floor\".\n    return getMinTimeForUnit('day');\n  },\n  format: function format(value, locale) {\n    /* istanbul ignore else */\n    if (!formatters[locale]) {\n      formatters[locale] = {};\n    }\n    /* istanbul ignore else */\n\n\n    if (!formatters[locale].dayMonth) {\n      // \"Apr 11\" (MMMd)\n      formatters[locale].dayMonth = new Intl.DateTimeFormat(locale, {\n        month: 'short',\n        day: 'numeric'\n      });\n    } // Output month and day.\n\n\n    return formatters[locale].dayMonth.format((0,_steps_index_js__WEBPACK_IMPORTED_MODULE_0__.getDate)(value));\n  }\n}; // If the `date` happened/happens outside of current year,\n// then output day, month and year.\n// The interval should be such that the `date` lies outside of the current year.\n\nvar yearMonthAndDay = {\n  minTime: function minTime(timestamp, _ref2) {\n    var future = _ref2.future;\n\n    if (future) {\n      // January 1, 00:00, of the `date`'s year is right after\n      // the maximum `now` for formatting a future date:\n      // When `now` is before that date, the `date` is formatted as \"day/month/year\" (this step),\n      // When `now` is equal to or after that date, the `date` is formatted as \"day/month\" (another step).\n      // After that, it's hours, minutes, seconds, and after that it's no longer `future`.\n      // The date is right after the maximum `now` for formatting a future date,\n      // so subtract 1 millisecond from it.\n      var maxFittingNow = new Date(new Date(timestamp).getFullYear(), 0).getTime() - 1; // Return `minTime` (in seconds).\n\n      return (timestamp - maxFittingNow) / 1000;\n    } else {\n      // January 1, 00:00, of the year following the `date`'s year\n      // is the minimum `now` for formatting a past date:\n      // When `now` is before that date, the `date` is formatted as \"day/month\" (another step),\n      // When `now` is equal to or after that date, the `date` is formatted as \"day/month/year\" (this step).\n      // After that, it's hours, minutes, seconds, and after that it's no longer `future`.\n      var minFittingNow = new Date(new Date(timestamp).getFullYear() + 1, 0).getTime(); // Return `minTime` (in seconds).\n\n      return (minFittingNow - timestamp) / 1000;\n    }\n  },\n  format: function format(value, locale) {\n    /* istanbul ignore if */\n    if (!formatters[locale]) {\n      formatters[locale] = {};\n    }\n    /* istanbul ignore else */\n\n\n    if (!formatters[locale].dayMonthYear) {\n      // \"Apr 11, 2017\" (yMMMd)\n      formatters[locale].dayMonthYear = new Intl.DateTimeFormat(locale, {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    } // Output day, month and year.\n\n\n    return formatters[locale].dayMonthYear.format((0,_steps_index_js__WEBPACK_IMPORTED_MODULE_0__.getDate)(value));\n  }\n}; // If `Intl.DateTimeFormat` is supported,\n// then longer time intervals will be formatted as dates.\n\n/* istanbul ignore else */\n\nif ((0,_locale_js__WEBPACK_IMPORTED_MODULE_1__.intlDateTimeFormatSupported)()) {\n  steps.push(monthAndDay, yearMonthAndDay);\n} // Otherwise, if `Intl.DateTimeFormat` is not supported,\n// which could be the case when using Internet Explorer,\n// then simply mimick \"round\" steps.\nelse {\n  steps.push({\n    formatAs: 'day'\n  }, {\n    formatAs: 'week'\n  }, {\n    formatAs: 'month'\n  }, {\n    formatAs: 'year'\n  });\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  steps: steps,\n  labels: [// \"mini\" labels are only defined for a few languages.\n  'mini', // \"short-time\" labels are only defined for a few languages.\n  'short-time', // \"narrow\" and \"short\" labels are defined for all languages.\n  // \"narrow\" labels can sometimes be weird (like \"+5d.\"),\n  // but \"short\" labels have the \" ago\" part, so \"narrow\" seem\n  // more appropriate.\n  // \"short\" labels would have been more appropriate if they\n  // didn't have the \" ago\" part, hence the \"short-time\" above.\n  'narrow', // Since \"narrow\" labels are always present, \"short\" element\n  // of this array can be removed.\n  'short']\n});\n//# sourceMappingURL=twitter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/style/twitter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/style/twitterFirstMinute.js":
/*!******************************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/style/twitterFirstMinute.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _steps_units_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../steps/units.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/steps/units.js\");\n/* harmony import */ var _twitter_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./twitter.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/twitter.js\");\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_objectSpread(_objectSpread({}, _twitter_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]), {}, {\n  // Skip \"seconds\".\n  steps: _twitter_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].steps.filter(function (step) {\n    return step.formatAs !== 'second';\n  }) // Start showing `1m` from the first minute.\n  .map(function (step) {\n    return step.formatAs === 'minute' ? _objectSpread(_objectSpread({}, step), {}, {\n      minTime: _steps_units_js__WEBPACK_IMPORTED_MODULE_1__.minute\n    }) : step;\n  })\n}));\n//# sourceMappingURL=twitterFirstMinute.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/style/twitterFirstMinute.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/style/twitterMinute.js":
/*!*************************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/style/twitterMinute.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _twitter_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./twitter.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/twitter.js\");\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_objectSpread(_objectSpread({}, _twitter_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]), {}, {\n  // Skip \"seconds\".\n  steps: _twitter_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].steps.filter(function (step) {\n    return step.formatAs !== 'second';\n  })\n}));\n//# sourceMappingURL=twitterMinute.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvamF2YXNjcmlwdC10aW1lLWFnby9tb2R1bGVzL3N0eWxlL3R3aXR0ZXJNaW51dGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSwyQ0FBMkMsZ0NBQWdDLG9DQUFvQyxvREFBb0QsNkRBQTZELGlFQUFpRSxzQ0FBc0M7O0FBRXZVLGlDQUFpQyxnQkFBZ0Isc0JBQXNCLE9BQU8sdURBQXVELDZEQUE2RCw0Q0FBNEMsb0tBQW9LLG1GQUFtRixLQUFLOztBQUUxZSw0Q0FBNEMsa0JBQWtCLGtDQUFrQyxvRUFBb0UsS0FBSyxPQUFPLG9CQUFvQjs7QUFFaks7QUFDbkMsaUVBQWUsOEJBQThCLEVBQUUsbURBQU8sS0FBSztBQUMzRDtBQUNBLFNBQVMsbURBQU87QUFDaEI7QUFDQSxHQUFHO0FBQ0gsQ0FBQyxDQUFDLEVBQUM7QUFDSCIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvamF2YXNjcmlwdC10aW1lLWFnby9tb2R1bGVzL3N0eWxlL3R3aXR0ZXJNaW51dGUuanM/MzQxYiJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBvd25LZXlzKG9iamVjdCwgZW51bWVyYWJsZU9ubHkpIHsgdmFyIGtleXMgPSBPYmplY3Qua2V5cyhvYmplY3QpOyBpZiAoT2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scykgeyB2YXIgc3ltYm9scyA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMob2JqZWN0KTsgZW51bWVyYWJsZU9ubHkgJiYgKHN5bWJvbHMgPSBzeW1ib2xzLmZpbHRlcihmdW5jdGlvbiAoc3ltKSB7IHJldHVybiBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKG9iamVjdCwgc3ltKS5lbnVtZXJhYmxlOyB9KSksIGtleXMucHVzaC5hcHBseShrZXlzLCBzeW1ib2xzKTsgfSByZXR1cm4ga2V5czsgfVxuXG5mdW5jdGlvbiBfb2JqZWN0U3ByZWFkKHRhcmdldCkgeyBmb3IgKHZhciBpID0gMTsgaSA8IGFyZ3VtZW50cy5sZW5ndGg7IGkrKykgeyB2YXIgc291cmNlID0gbnVsbCAhPSBhcmd1bWVudHNbaV0gPyBhcmd1bWVudHNbaV0gOiB7fTsgaSAlIDIgPyBvd25LZXlzKE9iamVjdChzb3VyY2UpLCAhMCkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7IF9kZWZpbmVQcm9wZXJ0eSh0YXJnZXQsIGtleSwgc291cmNlW2tleV0pOyB9KSA6IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3JzID8gT2JqZWN0LmRlZmluZVByb3BlcnRpZXModGFyZ2V0LCBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9ycyhzb3VyY2UpKSA6IG93bktleXMoT2JqZWN0KHNvdXJjZSkpLmZvckVhY2goZnVuY3Rpb24gKGtleSkgeyBPYmplY3QuZGVmaW5lUHJvcGVydHkodGFyZ2V0LCBrZXksIE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3Ioc291cmNlLCBrZXkpKTsgfSk7IH0gcmV0dXJuIHRhcmdldDsgfVxuXG5mdW5jdGlvbiBfZGVmaW5lUHJvcGVydHkob2JqLCBrZXksIHZhbHVlKSB7IGlmIChrZXkgaW4gb2JqKSB7IE9iamVjdC5kZWZpbmVQcm9wZXJ0eShvYmosIGtleSwgeyB2YWx1ZTogdmFsdWUsIGVudW1lcmFibGU6IHRydWUsIGNvbmZpZ3VyYWJsZTogdHJ1ZSwgd3JpdGFibGU6IHRydWUgfSk7IH0gZWxzZSB7IG9ialtrZXldID0gdmFsdWU7IH0gcmV0dXJuIG9iajsgfVxuXG5pbXBvcnQgdHdpdHRlciBmcm9tICcuL3R3aXR0ZXIuanMnO1xuZXhwb3J0IGRlZmF1bHQgX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCB0d2l0dGVyKSwge30sIHtcbiAgLy8gU2tpcCBcInNlY29uZHNcIi5cbiAgc3RlcHM6IHR3aXR0ZXIuc3RlcHMuZmlsdGVyKGZ1bmN0aW9uIChzdGVwKSB7XG4gICAgcmV0dXJuIHN0ZXAuZm9ybWF0QXMgIT09ICdzZWNvbmQnO1xuICB9KVxufSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10d2l0dGVyTWludXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/style/twitterMinute.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/style/twitterMinuteNow.js":
/*!****************************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/style/twitterMinuteNow.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _twitterMinute_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./twitterMinute.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/twitterMinute.js\");\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_objectSpread(_objectSpread({}, _twitterMinute_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]), {}, {\n  // Add \"now\".\n  steps: [{\n    formatAs: 'now'\n  }].concat(_twitterMinute_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].steps)\n}));\n//# sourceMappingURL=twitterMinuteNow.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/style/twitterMinuteNow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/javascript-time-ago/modules/style/twitterNow.js":
/*!**********************************************************************!*\
  !*** ./node_modules/javascript-time-ago/modules/style/twitterNow.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _twitter_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./twitter.js */ \"(ssr)/./node_modules/javascript-time-ago/modules/style/twitter.js\");\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_objectSpread(_objectSpread({}, _twitter_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]), {}, {\n  // Add \"now\".\n  steps: [{\n    formatAs: 'now'\n  }].concat(_twitter_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].steps)\n}));\n//# sourceMappingURL=twitterNow.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/javascript-time-ago/modules/style/twitterNow.js\n");

/***/ })

};
;