"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/AllDayRow.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/AllDayRow.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AllDayRow: function() { return /* binding */ AllDayRow; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isSameDay,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isSameDay,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isSameDay,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isSameDay,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/eventCollisionUtils */ \"(app-pages-browser)/./src/utils/eventCollisionUtils.ts\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst AllDayRow = (param)=>{\n    let { selectedDate, segments, selectedEvent, setSelectedEvent, handleEventClick, canEditData, openAddEventForm, view, activeDragData } = param;\n    _s();\n    // Create individual droppable hooks for each possible day\n    // IMPORTANT: All hooks must be called before any conditional returns\n    const dayViewHook = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"yyyy-MM-dd\")),\n        data: {\n            date: selectedDate,\n            type: \"allday-day\"\n        }\n    });\n    // Create hooks for week view days\n    const weekDay1 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 0), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 0),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay2 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 1), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 1),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay3 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 2), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 2),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay4 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 3), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 3),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay5 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 4), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 4),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay6 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 5), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 5),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay7 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 6), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 6),\n            type: \"allday-week\"\n        }\n    });\n    const weekViewHooks = [\n        weekDay1,\n        weekDay2,\n        weekDay3,\n        weekDay4,\n        weekDay5,\n        weekDay6,\n        weekDay7\n    ];\n    if (segments.length === 0 && !activeDragData) {\n        return null;\n    }\n    const renderDayView = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"border-b border-neutral-300 bg-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky left-0 bg-white z-10 w-14 lg:w-20 border-r border-neutral-300 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-neutral-500\",\n                        children: \"All-day\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: dayViewHook.setNodeRef,\n                        className: \"flex-1 relative p-2 space-y-1\",\n                        children: [\n                            segments.slice(0, 3).map((segment)=>{\n                                var _activeDragData_payload;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                    segment: segment,\n                                    style: {\n                                        height: \"24px\",\n                                        width: \"100%\"\n                                    },\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        setSelectedEvent(segment.originalEventId);\n                                        handleEventClick(segment.originalEvent);\n                                    },\n                                    view: \"day\",\n                                    isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === segment.id\n                                }, segment.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, undefined);\n                            }),\n                            segments.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-neutral-600 font-medium cursor-pointer hover:text-neutral-800\",\n                                children: [\n                                    \"+ \",\n                                    segments.length - 3,\n                                    \" more\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n            lineNumber: 117,\n            columnNumber: 5\n        }, undefined);\n    const renderWeekView = ()=>{\n        // Week view specific logic starts here\n        const weekDays = Array.from({\n            length: 7\n        }, (_, i)=>(0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), i));\n        const spanningEvents = (()=>{\n            const eventGroups = new Map();\n            segments.forEach((segment)=>{\n                if (segment.isMultiDay || segment.isAllDay) {\n                    const eventId = segment.originalEventId;\n                    if (!eventGroups.has(eventId)) eventGroups.set(eventId, []);\n                    eventGroups.get(eventId).push(segment);\n                }\n            });\n            const spanning = [];\n            eventGroups.forEach((eventSegments)=>{\n                eventSegments.sort((a, b)=>a.date.getTime() - b.date.getTime());\n                const firstSegmentInWeek = eventSegments[0];\n                const lastSegmentInWeek = eventSegments[eventSegments.length - 1];\n                const startDayIndex = weekDays.findIndex((day)=>(0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, firstSegmentInWeek.date));\n                const endDayIndex = weekDays.findIndex((day)=>(0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, lastSegmentInWeek.date));\n                if (startDayIndex >= 0 && endDayIndex >= 0) {\n                    spanning.push({\n                        segment: firstSegmentInWeek,\n                        startDayIndex,\n                        endDayIndex,\n                        colSpan: endDayIndex - startDayIndex + 1,\n                        isEndOfEvent: lastSegmentInWeek.isLastSegment\n                    });\n                }\n            });\n            return spanning;\n        })();\n        const positionedEvents = (()=>{\n            const positioned = [];\n            const rows = [];\n            const sortedEvents = [\n                ...spanningEvents\n            ].sort((a, b)=>a.startDayIndex - b.startDayIndex || b.colSpan - a.colSpan);\n            sortedEvents.forEach((event)=>{\n                let assigned = false;\n                for(let i = 0; i < rows.length; i++){\n                    const row = rows[i];\n                    if (row.every((rowEvent)=>event.startDayIndex > rowEvent.endDayIndex || event.endDayIndex < rowEvent.startDayIndex)) {\n                        row.push(event);\n                        positioned.push({\n                            ...event,\n                            row: i\n                        });\n                        assigned = true;\n                        break;\n                    }\n                }\n                if (!assigned) {\n                    rows.push([\n                        event\n                    ]);\n                    positioned.push({\n                        ...event,\n                        row: rows.length - 1\n                    });\n                }\n            });\n            return positioned;\n        })();\n        // Use the new layout calculator\n        const { visibleSegments, moreCount } = (0,_utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_4__.calculateAllDayLayout)(positionedEvents.map((e)=>e.segment), 3);\n        const visibleEvents = positionedEvents.filter((p)=>visibleSegments.some((s)=>s.id === p.segment.id));\n        const hasMore = moreCount > 0;\n        const firstEventDayIndex = positionedEvents.length > 0 ? Math.min(...positionedEvents.map((e)=>e.startDayIndex)) : 0;\n        if (positionedEvents.length === 0) return null;\n        const maxRows = positionedEvents.length > 0 ? Math.max(...positionedEvents.map((e)=>e.row)) + 1 : 0;\n        const rowHeight = 28;\n        const displayRows = hasMore ? 3.5 : Math.max(1, maxRows);\n        const totalHeight = displayRows * rowHeight + 16;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            \"data-all-day-row\": \"true\",\n            className: \"border-b border-neutral-300 bg-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky left-0 bg-white z-10 w-14 lg:w-20 border-r border-neutral-300 flex items-center justify-end pr-4 pt-2 text-xs font-medium text-neutral-500\",\n                        children: \"All-day\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative p-2\",\n                        style: {\n                            height: \"\".concat(totalHeight, \"px\")\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-7 gap-1 h-full\",\n                            children: weekDays.map((day, dayIndex)=>{\n                                const hook = weekViewHooks[dayIndex];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: hook.setNodeRef,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative cursor-pointer hover:bg-neutral-50 rounded-sm transition-colors\", hook.isOver && \"bg-blue-50\"),\n                                    onDoubleClick: ()=>{\n                                        if (canEditData) {\n                                            const newDate = new Date(day);\n                                            newDate.setHours(9, 0, 0, 0);\n                                            openAddEventForm(newDate);\n                                        }\n                                    },\n                                    children: [\n                                        visibleEvents.filter((spanningEvent)=>spanningEvent.startDayIndex === dayIndex).map((spanningEvent)=>{\n                                            var _activeDragData_payload;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute z-10\",\n                                                style: {\n                                                    top: \"\".concat(spanningEvent.row * rowHeight + 2, \"px\"),\n                                                    left: \"0px\",\n                                                    width: \"calc(\".concat(spanningEvent.colSpan * 100, \"% + \").concat((spanningEvent.colSpan - 1) * 4, \"px)\"),\n                                                    height: \"24px\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                                    segment: spanningEvent.segment,\n                                                    isEndOfEvent: spanningEvent.isEndOfEvent,\n                                                    style: {\n                                                        height: \"24px\",\n                                                        width: \"100%\"\n                                                    },\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        setSelectedEvent(spanningEvent.segment.originalEventId);\n                                                        handleEventClick(spanningEvent.segment.originalEvent);\n                                                    },\n                                                    view: view,\n                                                    isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === spanningEvent.segment.id\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, spanningEvent.segment.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 23\n                                            }, undefined);\n                                        }),\n                                        hasMore && dayIndex === firstEventDayIndex && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute z-10 cursor-pointer text-xs text-gray-600 hover:underline\",\n                                            style: {\n                                                top: \"\".concat(3 * rowHeight + 2, \"px\"),\n                                                left: \"4px\",\n                                                right: \"4px\"\n                                            },\n                                            onClick: ()=>console.log(\"More clicked\"),\n                                            children: [\n                                                \"+\",\n                                                moreCount,\n                                                \" more\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, dayIndex, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                lineNumber: 231,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n            lineNumber: 227,\n            columnNumber: 5\n        }, undefined);\n    };\n    return view === \"day\" ? renderDayView() : renderWeekView();\n};\n_s(AllDayRow, \"7+zBFPGq0SU8Pt7cRj6jYFJX7rY=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable\n    ];\n});\n_c = AllDayRow;\nvar _c;\n$RefreshReg$(_c, \"AllDayRow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/AllDayRow.tsx\n"));

/***/ })

});