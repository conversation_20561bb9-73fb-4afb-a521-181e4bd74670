"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-to-text";
exports.ids = ["vendor-chunks/hast-util-to-text"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-to-text/lib/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/hast-util-to-text/lib/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toText: () => (/* binding */ toText)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/./node_modules/hast-util-to-text/node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var unist_util_find_after__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-find-after */ \"(ssr)/./node_modules/unist-util-find-after/lib/index.js\");\n/**\n * @typedef {import('hast-util-is-element').TestFunctionAnything} TestFunctionAnything\n * @typedef {import('hast').Content} Content\n * @typedef {import('hast').Text} Text\n * @typedef {import('hast').Comment} Comment\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Element} Element\n */\n\n/**\n * @typedef {Content | Root} Node\n *   Any node.\n * @typedef {Extract<Node, import('unist').Parent>} Parent\n *   Any parent.\n * @typedef {'normal' | 'pre' | 'nowrap' | 'pre-wrap'} Whitespace\n *   Valid and useful whitespace values (from CSS).\n * @typedef {0 | 1 | 2} BreakNumber\n *   Specific break:\n *\n *   *   `0` — space\n *   *   `1` — line ending\n *   *   `2` — blank line\n * @typedef {'\\n'} BreakForce\n *   Forced break.\n * @typedef {boolean} BreakValue\n *   Whether there was a break.\n * @typedef {BreakValue | BreakNumber | undefined} BreakBefore\n *   Any value for a break before.\n * @typedef {BreakValue | BreakNumber | BreakForce | undefined} BreakAfter\n *   Any value for a break after.\n *\n * @typedef CollectionInfo\n *   Info on current collection.\n * @property {Whitespace} whitespace\n *   Current whitespace setting.\n * @property {BreakBefore} breakBefore\n *   Whether there was a break before.\n * @property {BreakAfter} breakAfter\n *   Whether there was a break after.\n *\n * @typedef Options\n *   Configuration.\n * @property {Whitespace | null | undefined} [whitespace='normal']\n *   Initial CSS whitespace setting to use.\n */\n\n\n\n\nconst searchLineFeeds = /\\n/g\nconst searchTabOrSpaces = /[\\t ]+/g\n\nconst br = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('br')\nconst p = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('p')\nconst cell = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)(['th', 'td'])\nconst row = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('tr')\n\n// Note that we don’t need to include void elements here as they don’t have text.\n// See: <https://github.com/wooorm/html-void-elements>\nconst notRendered = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  // List from: <https://html.spec.whatwg.org/#hidden-elements>\n  'datalist',\n  'head',\n  'noembed',\n  'noframes',\n  'noscript', // Act as if we support scripting.\n  'rp',\n  'script',\n  'style',\n  'template',\n  'title',\n  // Hidden attribute.\n  hidden,\n  // From: <https://html.spec.whatwg.org/#flow-content-3>\n  closedDialog\n])\n\n// See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\nconst blockOrCaption = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  'address', // Flow content\n  'article', // Sections and headings\n  'aside', // Sections and headings\n  'blockquote', // Flow content\n  'body', // Page\n  'caption', // `table-caption`\n  'center', // Flow content (legacy)\n  'dd', // Lists\n  'dialog', // Flow content\n  'dir', // Lists (legacy)\n  'dl', // Lists\n  'dt', // Lists\n  'div', // Flow content\n  'figure', // Flow content\n  'figcaption', // Flow content\n  'footer', // Flow content\n  'form,', // Flow content\n  'h1', // Sections and headings\n  'h2', // Sections and headings\n  'h3', // Sections and headings\n  'h4', // Sections and headings\n  'h5', // Sections and headings\n  'h6', // Sections and headings\n  'header', // Flow content\n  'hgroup', // Sections and headings\n  'hr', // Flow content\n  'html', // Page\n  'legend', // Flow content\n  'listing', // Flow content (legacy)\n  'main', // Flow content\n  'menu', // Lists\n  'nav', // Sections and headings\n  'ol', // Lists\n  'p', // Flow content\n  'plaintext', // Flow content (legacy)\n  'pre', // Flow content\n  'section', // Sections and headings\n  'ul', // Lists\n  'xmp' // Flow content (legacy)\n])\n\n/**\n * Get the plain-text value of a node.\n *\n * ###### Algorithm\n *\n * *   if `tree` is a comment, returns its `value`\n * *   if `tree` is a text, applies normal whitespace collapsing to its\n *     `value`, as defined by the CSS Text spec\n * *   if `tree` is a root or element, applies an algorithm similar to the\n *     `innerText` getter as defined by HTML\n *\n * ###### Notes\n *\n * > 👉 **Note**: the algorithm acts as if `tree` is being rendered, and as if\n * > we’re a CSS-supporting user agent, with scripting enabled.\n *\n * *   if `tree` is an element that is not displayed (such as a `head`), we’ll\n *     still use the `innerText` algorithm instead of switching to `textContent`\n * *   if descendants of `tree` are elements that are not displayed, they are\n *     ignored\n * *   CSS is not considered, except for the default user agent style sheet\n * *   a line feed is collapsed instead of ignored in cases where Fullwidth, Wide,\n *     or Halfwidth East Asian Width characters are used, the same goes for a case\n *     with Chinese, Japanese, or Yi writing systems\n * *   replaced elements (such as `audio`) are treated like non-replaced elements\n *\n * @param {Node} tree\n *   Tree to turn into text.\n * @param {Options} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized `tree`.\n */\nfunction toText(tree, options = {}) {\n  const children = 'children' in tree ? tree.children : []\n  const block = blockOrCaption(tree)\n  const whitespace = inferWhitespace(tree, {\n    whitespace: options.whitespace || 'normal',\n    breakBefore: false,\n    breakAfter: false\n  })\n\n  /** @type {Array<string | BreakNumber>} */\n  const results = []\n\n  // Treat `text` and `comment` as having normal white-space.\n  // This deviates from the spec as in the DOM the node’s `.data` has to be\n  // returned.\n  // If you want that behavior use `hast-util-to-string`.\n  // All other nodes are later handled as if they are `element`s (so the\n  // algorithm also works on a `root`).\n  // Nodes without children are treated as a void element, so `doctype` is thus\n  // ignored.\n  if (tree.type === 'text' || tree.type === 'comment') {\n    results.push(\n      ...collectText(tree, {\n        whitespace,\n        breakBefore: true,\n        breakAfter: true\n      })\n    )\n  }\n\n  // 1.  If this element is not being rendered, or if the user agent is a\n  //     non-CSS user agent, then return the same value as the textContent IDL\n  //     attribute on this element.\n  //\n  //     Note: we’re not supporting stylesheets so we’re acting as if the node\n  //     is rendered.\n  //\n  //     If you want that behavior use `hast-util-to-string`.\n  //     Important: we’ll have to account for this later though.\n\n  // 2.  Let results be a new empty list.\n  let index = -1\n\n  // 3.  For each child node node of this element:\n  while (++index < children.length) {\n    // 3.1. Let current be the list resulting in running the inner text\n    //      collection steps with node.\n    //      Each item in results will either be a JavaScript string or a\n    //      positive integer (a required line break count).\n    // 3.2. For each item item in current, append item to results.\n    results.push(\n      // @ts-expect-error Looks like a parent.\n      ...innerTextCollection(children[index], tree, {\n        whitespace,\n        breakBefore: index ? undefined : block,\n        breakAfter:\n          index < children.length - 1 ? br(children[index + 1]) : block\n      })\n    )\n  }\n\n  // 4.  Remove any items from results that are the empty string.\n  // 5.  Remove any runs of consecutive required line break count items at the\n  //     start or end of results.\n  // 6.  Replace each remaining run of consecutive required line break count\n  //     items with a string consisting of as many U+000A LINE FEED (LF)\n  //     characters as the maximum of the values in the required line break\n  //     count items.\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {number | undefined} */\n  let count\n\n  index = -1\n\n  while (++index < results.length) {\n    const value = results[index]\n\n    if (typeof value === 'number') {\n      if (count !== undefined && value > count) count = value\n    } else if (value) {\n      if (count !== undefined && count > -1) {\n        result.push('\\n'.repeat(count) || ' ')\n      }\n\n      count = -1\n      result.push(value)\n    }\n  }\n\n  // 7.  Return the concatenation of the string items in results.\n  return result.join('')\n}\n\n/**\n * <https://html.spec.whatwg.org/#inner-text-collection-steps>\n *\n * @param {Node} node\n * @param {Parent} parent\n * @param {CollectionInfo} info\n * @returns {Array<string | BreakNumber>}\n */\nfunction innerTextCollection(node, parent, info) {\n  if (node.type === 'element') {\n    return collectElement(node, parent, info)\n  }\n\n  if (node.type === 'text') {\n    return info.whitespace === 'normal'\n      ? collectText(node, info)\n      : collectPreText(node)\n  }\n\n  return []\n}\n\n/**\n * Collect an element.\n *\n * @param {Element} node\n *   Element node.\n * @param {Parent} parent\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Array<string | BreakNumber>}\n */\nfunction collectElement(node, parent, info) {\n  // First we infer the `white-space` property.\n  const whitespace = inferWhitespace(node, info)\n  const children = node.children || []\n  let index = -1\n  /** @type {Array<string | BreakNumber>} */\n  let items = []\n\n  // We’re ignoring point 3, and exiting without any content here, because we\n  // deviated from the spec in `toText` at step 3.\n  if (notRendered(node)) {\n    return items\n  }\n\n  /** @type {BreakNumber | undefined} */\n  let prefix\n  /** @type {BreakNumber | BreakForce | undefined} */\n  let suffix\n  // Note: we first detect if there is going to be a break before or after the\n  // contents, as that changes the white-space handling.\n\n  // 2.  If node’s computed value of `visibility` is not `visible`, then return\n  //     items.\n  //\n  //     Note: Ignored, as everything is visible by default user agent styles.\n\n  // 3.  If node is not being rendered, then return items. [...]\n  //\n  //     Note: We already did this above.\n\n  // See `collectText` for step 4.\n\n  // 5.  If node is a `<br>` element, then append a string containing a single\n  //     U+000A LINE FEED (LF) character to items.\n  if (br(node)) {\n    suffix = '\\n'\n  }\n\n  // 7.  If node’s computed value of `display` is `table-row`, and node’s CSS\n  //     box is not the last `table-row` box of the nearest ancestor `table`\n  //     box, then append a string containing a single U+000A LINE FEED (LF)\n  //     character to items.\n  //\n  //     See: <https://html.spec.whatwg.org/#tables-2>\n  //     Note: needs further investigation as this does not account for implicit\n  //     rows.\n  else if (row(node) && (0,unist_util_find_after__WEBPACK_IMPORTED_MODULE_1__.findAfter)(parent, node, row)) {\n    suffix = '\\n'\n  }\n\n  // 8.  If node is a `<p>` element, then append 2 (a required line break count)\n  //     at the beginning and end of items.\n  else if (p(node)) {\n    prefix = 2\n    suffix = 2\n  }\n\n  // 9.  If node’s used value of `display` is block-level or `table-caption`,\n  //     then append 1 (a required line break count) at the beginning and end of\n  //     items.\n  else if (blockOrCaption(node)) {\n    prefix = 1\n    suffix = 1\n  }\n\n  // 1.  Let items be the result of running the inner text collection steps with\n  //     each child node of node in tree order, and then concatenating the\n  //     results to a single list.\n  while (++index < children.length) {\n    items = items.concat(\n      innerTextCollection(children[index], node, {\n        whitespace,\n        breakBefore: index ? undefined : prefix,\n        breakAfter:\n          index < children.length - 1 ? br(children[index + 1]) : suffix\n      })\n    )\n  }\n\n  // 6.  If node’s computed value of `display` is `table-cell`, and node’s CSS\n  //     box is not the last `table-cell` box of its enclosing `table-row` box,\n  //     then append a string containing a single U+0009 CHARACTER TABULATION\n  //     (tab) character to items.\n  //\n  //     See: <https://html.spec.whatwg.org/#tables-2>\n  if (cell(node) && (0,unist_util_find_after__WEBPACK_IMPORTED_MODULE_1__.findAfter)(parent, node, cell)) {\n    items.push('\\t')\n  }\n\n  // Add the pre- and suffix.\n  if (prefix) items.unshift(prefix)\n  if (suffix) items.push(suffix)\n\n  return items\n}\n\n/**\n * 4.  If node is a Text node, then for each CSS text box produced by node,\n *     in content order, compute the text of the box after application of the\n *     CSS `white-space` processing rules and `text-transform` rules, set\n *     items to the list of the resulting strings, and return items.\n *     The CSS `white-space` processing rules are slightly modified:\n *     collapsible spaces at the end of lines are always collapsed, but they\n *     are only removed if the line is the last line of the block, or it ends\n *     with a br element.\n *     Soft hyphens should be preserved.\n *\n *     Note: See `collectText` and `collectPreText`.\n *     Note: we don’t deal with `text-transform`, no element has that by\n *     default.\n *\n * See: <https://drafts.csswg.org/css-text/#white-space-phase-1>\n *\n * @param {Text | Comment} node\n *   Text node.\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Array<string | BreakNumber>}\n *   Result.\n */\nfunction collectText(node, info) {\n  const value = String(node.value)\n  /** @type {Array<string>} */\n  const lines = []\n  /** @type {Array<string | BreakNumber>} */\n  const result = []\n  let start = 0\n\n  while (start <= value.length) {\n    searchLineFeeds.lastIndex = start\n\n    const match = searchLineFeeds.exec(value)\n    const end = match && 'index' in match ? match.index : value.length\n\n    lines.push(\n      // Any sequence of collapsible spaces and tabs immediately preceding or\n      // following a segment break is removed.\n      trimAndCollapseSpacesAndTabs(\n        // […] ignoring bidi formatting characters (characters with the\n        // Bidi_Control property [UAX9]: ALM, LTR, RTL, LRE-RLO, LRI-PDI) as if\n        // they were not there.\n        value\n          .slice(start, end)\n          .replace(/[\\u061C\\u200E\\u200F\\u202A-\\u202E\\u2066-\\u2069]/g, ''),\n        start === 0 ? info.breakBefore : true,\n        end === value.length ? info.breakAfter : true\n      )\n    )\n\n    start = end + 1\n  }\n\n  // Collapsible segment breaks are transformed for rendering according to the\n  // segment break transformation rules.\n  // So here we jump to 4.1.2 of [CSSTEXT]:\n  // Any collapsible segment break immediately following another collapsible\n  // segment break is removed\n  let index = -1\n  /** @type {BreakNumber | undefined} */\n  let join\n\n  while (++index < lines.length) {\n    // *   If the character immediately before or immediately after the segment\n    //     break is the zero-width space character (U+200B), then the break is\n    //     removed, leaving behind the zero-width space.\n    if (\n      lines[index].charCodeAt(lines[index].length - 1) === 0x200b /* ZWSP */ ||\n      (index < lines.length - 1 &&\n        lines[index + 1].charCodeAt(0) === 0x200b) /* ZWSP */\n    ) {\n      result.push(lines[index])\n      join = undefined\n    }\n\n    // *   Otherwise, if the East Asian Width property [UAX11] of both the\n    //     character before and after the segment break is Fullwidth, Wide, or\n    //     Halfwidth (not Ambiguous), and neither side is Hangul, then the\n    //     segment break is removed.\n    //\n    //     Note: ignored.\n    // *   Otherwise, if the writing system of the segment break is Chinese,\n    //     Japanese, or Yi, and the character before or after the segment break\n    //     is punctuation or a symbol (Unicode general category P* or S*) and\n    //     has an East Asian Width property of Ambiguous, and the character on\n    //     the other side of the segment break is Fullwidth, Wide, or Halfwidth,\n    //     and not Hangul, then the segment break is removed.\n    //\n    //     Note: ignored.\n\n    // *   Otherwise, the segment break is converted to a space (U+0020).\n    else if (lines[index]) {\n      if (typeof join === 'number') result.push(join)\n      result.push(lines[index])\n      join = 0\n    } else if (index === 0 || index === lines.length - 1) {\n      // If this line is empty, and it’s the first or last, add a space.\n      // Note that this function is only called in normal whitespace, so we\n      // don’t worry about `pre`.\n      result.push(0)\n    }\n  }\n\n  return result\n}\n\n/**\n * Collect a text node as “pre” whitespace.\n *\n * @param {Text} node\n *   Text node.\n * @returns {Array<string | BreakNumber>}\n *   Result.\n */\nfunction collectPreText(node) {\n  return [String(node.value)]\n}\n\n/**\n * 3.  Every collapsible tab is converted to a collapsible space (U+0020).\n * 4.  Any collapsible space immediately following another collapsible\n *     space—even one outside the boundary of the inline containing that\n *     space, provided both spaces are within the same inline formatting\n *     context—is collapsed to have zero advance width. (It is invisible,\n *     but retains its soft wrap opportunity, if any.)\n *\n * @param {string} value\n *   Value to collapse.\n * @param {BreakBefore} breakBefore\n *   Whether there was a break before.\n * @param {BreakAfter} breakAfter\n *   Whether there was a break after.\n * @returns {string}\n *   Result.\n */\nfunction trimAndCollapseSpacesAndTabs(value, breakBefore, breakAfter) {\n  /** @type {Array<string>} */\n  const result = []\n  let start = 0\n  /** @type {number | undefined} */\n  let end\n\n  while (start < value.length) {\n    searchTabOrSpaces.lastIndex = start\n    const match = searchTabOrSpaces.exec(value)\n    end = match ? match.index : value.length\n\n    // If we’re not directly after a segment break, but there was white space,\n    // add an empty value that will be turned into a space.\n    if (!start && !end && match && !breakBefore) {\n      result.push('')\n    }\n\n    if (start !== end) {\n      result.push(value.slice(start, end))\n    }\n\n    start = match ? end + match[0].length : end\n  }\n\n  // If we reached the end, there was trailing white space, and there’s no\n  // segment break after this node, add an empty value that will be turned\n  // into a space.\n  if (start !== end && !breakAfter) {\n    result.push('')\n  }\n\n  return result.join(' ')\n}\n\n/**\n * Figure out the whitespace of a node.\n *\n * We don’t support void elements here (so `nobr wbr` -> `normal` is ignored).\n *\n * @param {Node} node\n *   Node (typically `Element`).\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Whitespace}\n *   Applied whitespace.\n */\nfunction inferWhitespace(node, info) {\n  if (node.type === 'element') {\n    const props = node.properties || {}\n    switch (node.tagName) {\n      case 'listing':\n      case 'plaintext':\n      case 'xmp': {\n        return 'pre'\n      }\n\n      case 'nobr': {\n        return 'nowrap'\n      }\n\n      case 'pre': {\n        return props.wrap ? 'pre-wrap' : 'pre'\n      }\n\n      case 'td':\n      case 'th': {\n        return props.noWrap ? 'nowrap' : info.whitespace\n      }\n\n      case 'textarea': {\n        return 'pre-wrap'\n      }\n\n      default:\n    }\n  }\n\n  return info.whitespace\n}\n\n/** @type {TestFunctionAnything} */\nfunction hidden(node) {\n  return Boolean((node.properties || {}).hidden)\n}\n\n/** @type {TestFunctionAnything} */\nfunction closedDialog(node) {\n  return node.tagName === 'dialog' && !(node.properties || {}).open\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-text/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-text/node_modules/hast-util-is-element/index.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/hast-util-to-text/node_modules/hast-util-is-element/index.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertElement: () => (/* binding */ convertElement),\n/* harmony export */   isElement: () => (/* binding */ isElement)\n/* harmony export */ });\n/**\n * @typedef {import('unist').Parent} Parent\n * @typedef {import('hast').Element} Element\n */\n\n/**\n * @typedef {null | undefined | string | TestFunctionAnything | Array<string | TestFunctionAnything>} Test\n *   Check for an arbitrary element, unaware of TypeScript inferral.\n *\n * @callback TestFunctionAnything\n *   Check if an element passes a test, unaware of TypeScript inferral.\n * @param {Element} element\n *   An element.\n * @param {number | null | undefined} [index]\n *   The element’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The element’s parent.\n * @returns {boolean | void}\n *   Whether this element passes the test.\n */\n\n/**\n * @template {Element} T\n *   Element type.\n * @typedef {T['tagName'] | TestFunctionPredicate<T> | Array<T['tagName'] | TestFunctionPredicate<T>>} PredicateTest\n *   Check for an element that can be inferred by TypeScript.\n */\n\n/**\n * Check if an element passes a certain node test.\n *\n * @template {Element} T\n *   Element type.\n * @callback TestFunctionPredicate\n *   Complex test function for an element that can be inferred by TypeScript.\n * @param {Element} element\n *   An element.\n * @param {number | null | undefined} [index]\n *   The element’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The element’s parent.\n * @returns {element is T}\n *   Whether this element passes the test.\n */\n\n/**\n * @callback AssertAnything\n *   Check that an arbitrary value is an element, unaware of TypeScript inferral.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is an element and passes a test.\n */\n\n/**\n * Check if a node is an element and passes a certain node test\n *\n * @template {Element} T\n *   Element type.\n * @callback AssertPredicate\n *   Check that an arbitrary value is a specific element, aware of TypeScript.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {node is T}\n *   Whether this is an element and passes a test.\n */\n\n/**\n * Check if `node` is an `Element` and whether it passes the given test.\n *\n * @param node\n *   Thing to check, typically `Node`.\n * @param test\n *   A check for a specific element.\n * @param index\n *   The node’s position in its parent.\n * @param parent\n *   The node’s parent.\n * @returns\n *   Whether `node` is an element and passes a test.\n */\nconst isElement =\n  /**\n   * @type {(\n   *   (() => false) &\n   *   (<T extends Element = Element>(node: unknown, test?: PredicateTest<T>, index?: number, parent?: Parent, context?: unknown) => node is T) &\n   *   ((node: unknown, test: Test, index?: number, parent?: Parent, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [node]\n     * @param {Test | undefined} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parent | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function (node, test, index, parent, context) {\n      const check = convertElement(test)\n\n      if (\n        index !== undefined &&\n        index !== null &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite index for child node')\n      }\n\n      if (\n        parent !== undefined &&\n        parent !== null &&\n        (!parent.type || !parent.children)\n      ) {\n        throw new Error('Expected parent node')\n      }\n\n      // @ts-expect-error Looks like a node.\n      if (!node || !node.type || typeof node.type !== 'string') {\n        return false\n      }\n\n      if (\n        (parent === undefined || parent === null) !==\n        (index === undefined || index === null)\n      ) {\n        throw new Error('Expected both parent and index')\n      }\n\n      return check.call(context, node, index, parent)\n    }\n  )\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param test\n *   *  When nullish, checks if `node` is an `Element`.\n *   *  When `string`, works like passing `(element) => element.tagName === test`.\n *   *  When `function` checks if function passed the element is true.\n *   *  When `array`, checks any one of the subtests pass.\n * @returns\n *   An assertion.\n */\nconst convertElement =\n  /**\n   * @type {(\n   *   (<T extends Element>(test: T['tagName'] | TestFunctionPredicate<T>) => AssertPredicate<T>) &\n   *   ((test?: Test) => AssertAnything)\n   * )}\n   */\n  (\n    /**\n     * @param {Test | null | undefined} [test]\n     * @returns {AssertAnything}\n     */\n    function (test) {\n      if (test === undefined || test === null) {\n        return element\n      }\n\n      if (typeof test === 'string') {\n        return tagNameFactory(test)\n      }\n\n      if (typeof test === 'object') {\n        return anyFactory(test)\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      throw new Error('Expected function, string, or array as test')\n    }\n  )\n\n/**\n * Handle multiple tests.\n *\n * @param {Array<string | TestFunctionAnything>} tests\n * @returns {AssertAnything}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<AssertAnything>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convertElement(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].call(this, ...parameters)) {\n        return true\n      }\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn a string into a test for an element with a certain tag name.\n *\n * @param {string} check\n * @returns {AssertAnything}\n */\nfunction tagNameFactory(check) {\n  return tagName\n\n  /**\n   * @param {unknown} node\n   * @returns {boolean}\n   */\n  function tagName(node) {\n    return element(node) && node.tagName === check\n  }\n}\n\n/**\n * Turn a custom test into a test for an element that passes that test.\n *\n * @param {TestFunctionAnything} check\n * @returns {AssertAnything}\n */\nfunction castFactory(check) {\n  return assertion\n\n  /**\n   * @this {unknown}\n   * @param {unknown} node\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function assertion(node, ...parameters) {\n    // @ts-expect-error: fine.\n    return element(node) && Boolean(check.call(this, node, ...parameters))\n  }\n}\n\n/**\n * Make sure something is an element.\n *\n * @param {unknown} node\n * @returns {node is Element}\n */\nfunction element(node) {\n  return Boolean(\n    node &&\n      typeof node === 'object' &&\n      // @ts-expect-error Looks like a node.\n      node.type === 'element' &&\n      // @ts-expect-error Looks like an element.\n      typeof node.tagName === 'string'\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-text/node_modules/hast-util-is-element/index.js\n");

/***/ })

};
;