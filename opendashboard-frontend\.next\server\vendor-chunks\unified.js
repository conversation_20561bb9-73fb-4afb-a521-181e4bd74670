"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/unified";
exports.ids = ["vendor-chunks/unified"];
exports.modules = {

/***/ "(ssr)/./node_modules/unified/index.js":
/*!***************************************!*\
  !*** ./node_modules/unified/index.js ***!
  \***************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unified: () => (/* reexport safe */ _lib_index_js__WEBPACK_IMPORTED_MODULE_0__.unified)\n/* harmony export */ });\n/* harmony import */ var _lib_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/index.js */ \"(ssr)/./node_modules/unified/lib/index.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdW5pZmllZC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFzQyIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvdW5pZmllZC9pbmRleC5qcz8wYmE5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7dW5pZmllZH0gZnJvbSAnLi9saWIvaW5kZXguanMnXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unified/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/unified/lib/index.js":
/*!*******************************************!*\
  !*** ./node_modules/unified/lib/index.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unified: () => (/* binding */ unified)\n/* harmony export */ });\n/* harmony import */ var bail__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bail */ \"(ssr)/./node_modules/bail/index.js\");\n/* harmony import */ var is_buffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! is-buffer */ \"(ssr)/./node_modules/is-buffer/index.js\");\n/* harmony import */ var extend__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! extend */ \"(ssr)/./node_modules/extend/index.js\");\n/* harmony import */ var is_plain_obj__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! is-plain-obj */ \"(ssr)/./node_modules/is-plain-obj/index.js\");\n/* harmony import */ var trough__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! trough */ \"(ssr)/./node_modules/trough/lib/index.js\");\n/* harmony import */ var vfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! vfile */ \"(ssr)/./node_modules/vfile/lib/index.js\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('vfile').VFileCompatible} VFileCompatible\n * @typedef {import('vfile').VFileValue} VFileValue\n * @typedef {import('..').Processor} Processor\n * @typedef {import('..').Plugin} Plugin\n * @typedef {import('..').Preset} Preset\n * @typedef {import('..').Pluggable} Pluggable\n * @typedef {import('..').PluggableList} PluggableList\n * @typedef {import('..').Transformer} Transformer\n * @typedef {import('..').Parser} Parser\n * @typedef {import('..').Compiler} Compiler\n * @typedef {import('..').RunCallback} RunCallback\n * @typedef {import('..').ProcessCallback} ProcessCallback\n *\n * @typedef Context\n * @property {Node} tree\n * @property {VFile} file\n */\n\n\n\n\n\n\n\n\n// Expose a frozen processor.\nconst unified = base().freeze()\n\nconst own = {}.hasOwnProperty\n\n// Function to create the first processor.\n/**\n * @returns {Processor}\n */\nfunction base() {\n  const transformers = (0,trough__WEBPACK_IMPORTED_MODULE_3__.trough)()\n  /** @type {Processor['attachers']} */\n  const attachers = []\n  /** @type {Record<string, unknown>} */\n  let namespace = {}\n  /** @type {boolean|undefined} */\n  let frozen\n  let freezeIndex = -1\n\n  // Data management.\n  // @ts-expect-error: overloads are handled.\n  processor.data = data\n  processor.Parser = undefined\n  processor.Compiler = undefined\n\n  // Lock.\n  processor.freeze = freeze\n\n  // Plugins.\n  processor.attachers = attachers\n  // @ts-expect-error: overloads are handled.\n  processor.use = use\n\n  // API.\n  processor.parse = parse\n  processor.stringify = stringify\n  // @ts-expect-error: overloads are handled.\n  processor.run = run\n  processor.runSync = runSync\n  // @ts-expect-error: overloads are handled.\n  processor.process = process\n  processor.processSync = processSync\n\n  // Expose.\n  return processor\n\n  // Create a new processor based on the processor in the current scope.\n  /** @type {Processor} */\n  function processor() {\n    const destination = base()\n    let index = -1\n\n    while (++index < attachers.length) {\n      destination.use(...attachers[index])\n    }\n\n    destination.data(extend__WEBPACK_IMPORTED_MODULE_1__(true, {}, namespace))\n\n    return destination\n  }\n\n  /**\n   * @param {string|Record<string, unknown>} [key]\n   * @param {unknown} [value]\n   * @returns {unknown}\n   */\n  function data(key, value) {\n    if (typeof key === 'string') {\n      // Set `key`.\n      if (arguments.length === 2) {\n        assertUnfrozen('data', frozen)\n        namespace[key] = value\n        return processor\n      }\n\n      // Get `key`.\n      return (own.call(namespace, key) && namespace[key]) || null\n    }\n\n    // Set space.\n    if (key) {\n      assertUnfrozen('data', frozen)\n      namespace = key\n      return processor\n    }\n\n    // Get space.\n    return namespace\n  }\n\n  /** @type {Processor['freeze']} */\n  function freeze() {\n    if (frozen) {\n      return processor\n    }\n\n    while (++freezeIndex < attachers.length) {\n      const [attacher, ...options] = attachers[freezeIndex]\n\n      if (options[0] === false) {\n        continue\n      }\n\n      if (options[0] === true) {\n        options[0] = undefined\n      }\n\n      /** @type {Transformer|void} */\n      const transformer = attacher.call(processor, ...options)\n\n      if (typeof transformer === 'function') {\n        transformers.use(transformer)\n      }\n    }\n\n    frozen = true\n    freezeIndex = Number.POSITIVE_INFINITY\n\n    return processor\n  }\n\n  /**\n   * @param {Pluggable|null|undefined} [value]\n   * @param {...unknown} options\n   * @returns {Processor}\n   */\n  function use(value, ...options) {\n    /** @type {Record<string, unknown>|undefined} */\n    let settings\n\n    assertUnfrozen('use', frozen)\n\n    if (value === null || value === undefined) {\n      // Empty.\n    } else if (typeof value === 'function') {\n      addPlugin(value, ...options)\n    } else if (typeof value === 'object') {\n      if (Array.isArray(value)) {\n        addList(value)\n      } else {\n        addPreset(value)\n      }\n    } else {\n      throw new TypeError('Expected usable value, not `' + value + '`')\n    }\n\n    if (settings) {\n      namespace.settings = Object.assign(namespace.settings || {}, settings)\n    }\n\n    return processor\n\n    /**\n     * @param {import('..').Pluggable<unknown[]>} value\n     * @returns {void}\n     */\n    function add(value) {\n      if (typeof value === 'function') {\n        addPlugin(value)\n      } else if (typeof value === 'object') {\n        if (Array.isArray(value)) {\n          const [plugin, ...options] = value\n          addPlugin(plugin, ...options)\n        } else {\n          addPreset(value)\n        }\n      } else {\n        throw new TypeError('Expected usable value, not `' + value + '`')\n      }\n    }\n\n    /**\n     * @param {Preset} result\n     * @returns {void}\n     */\n    function addPreset(result) {\n      addList(result.plugins)\n\n      if (result.settings) {\n        settings = Object.assign(settings || {}, result.settings)\n      }\n    }\n\n    /**\n     * @param {PluggableList|null|undefined} [plugins]\n     * @returns {void}\n     */\n    function addList(plugins) {\n      let index = -1\n\n      if (plugins === null || plugins === undefined) {\n        // Empty.\n      } else if (Array.isArray(plugins)) {\n        while (++index < plugins.length) {\n          const thing = plugins[index]\n          add(thing)\n        }\n      } else {\n        throw new TypeError('Expected a list of plugins, not `' + plugins + '`')\n      }\n    }\n\n    /**\n     * @param {Plugin} plugin\n     * @param {...unknown} [value]\n     * @returns {void}\n     */\n    function addPlugin(plugin, value) {\n      let index = -1\n      /** @type {Processor['attachers'][number]|undefined} */\n      let entry\n\n      while (++index < attachers.length) {\n        if (attachers[index][0] === plugin) {\n          entry = attachers[index]\n          break\n        }\n      }\n\n      if (entry) {\n        if ((0,is_plain_obj__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(entry[1]) && (0,is_plain_obj__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(value)) {\n          value = extend__WEBPACK_IMPORTED_MODULE_1__(true, entry[1], value)\n        }\n\n        entry[1] = value\n      } else {\n        // @ts-expect-error: fine.\n        attachers.push([...arguments])\n      }\n    }\n  }\n\n  /** @type {Processor['parse']} */\n  function parse(doc) {\n    processor.freeze()\n    const file = vfile(doc)\n    const Parser = processor.Parser\n    assertParser('parse', Parser)\n\n    if (newable(Parser, 'parse')) {\n      // @ts-expect-error: `newable` checks this.\n      return new Parser(String(file), file).parse()\n    }\n\n    // @ts-expect-error: `newable` checks this.\n    return Parser(String(file), file) // eslint-disable-line new-cap\n  }\n\n  /** @type {Processor['stringify']} */\n  function stringify(node, doc) {\n    processor.freeze()\n    const file = vfile(doc)\n    const Compiler = processor.Compiler\n    assertCompiler('stringify', Compiler)\n    assertNode(node)\n\n    if (newable(Compiler, 'compile')) {\n      // @ts-expect-error: `newable` checks this.\n      return new Compiler(node, file).compile()\n    }\n\n    // @ts-expect-error: `newable` checks this.\n    return Compiler(node, file) // eslint-disable-line new-cap\n  }\n\n  /**\n   * @param {Node} node\n   * @param {VFileCompatible|RunCallback} [doc]\n   * @param {RunCallback} [callback]\n   * @returns {Promise<Node>|void}\n   */\n  function run(node, doc, callback) {\n    assertNode(node)\n    processor.freeze()\n\n    if (!callback && typeof doc === 'function') {\n      callback = doc\n      doc = undefined\n    }\n\n    if (!callback) {\n      return new Promise(executor)\n    }\n\n    executor(null, callback)\n\n    /**\n     * @param {null|((node: Node) => void)} resolve\n     * @param {(error: Error) => void} reject\n     * @returns {void}\n     */\n    function executor(resolve, reject) {\n      // @ts-expect-error: `doc` can’t be a callback anymore, we checked.\n      transformers.run(node, vfile(doc), done)\n\n      /**\n       * @param {Error|null} error\n       * @param {Node} tree\n       * @param {VFile} file\n       * @returns {void}\n       */\n      function done(error, tree, file) {\n        tree = tree || node\n        if (error) {\n          reject(error)\n        } else if (resolve) {\n          resolve(tree)\n        } else {\n          // @ts-expect-error: `callback` is defined if `resolve` is not.\n          callback(null, tree, file)\n        }\n      }\n    }\n  }\n\n  /** @type {Processor['runSync']} */\n  function runSync(node, file) {\n    /** @type {Node|undefined} */\n    let result\n    /** @type {boolean|undefined} */\n    let complete\n\n    processor.run(node, file, done)\n\n    assertDone('runSync', 'run', complete)\n\n    // @ts-expect-error: we either bailed on an error or have a tree.\n    return result\n\n    /**\n     * @param {Error|null} [error]\n     * @param {Node} [tree]\n     * @returns {void}\n     */\n    function done(error, tree) {\n      ;(0,bail__WEBPACK_IMPORTED_MODULE_4__.bail)(error)\n      result = tree\n      complete = true\n    }\n  }\n\n  /**\n   * @param {VFileCompatible} doc\n   * @param {ProcessCallback} [callback]\n   * @returns {Promise<VFile>|undefined}\n   */\n  function process(doc, callback) {\n    processor.freeze()\n    assertParser('process', processor.Parser)\n    assertCompiler('process', processor.Compiler)\n\n    if (!callback) {\n      return new Promise(executor)\n    }\n\n    executor(null, callback)\n\n    /**\n     * @param {null|((file: VFile) => void)} resolve\n     * @param {(error?: Error|null|undefined) => void} reject\n     * @returns {void}\n     */\n    function executor(resolve, reject) {\n      const file = vfile(doc)\n\n      processor.run(processor.parse(file), file, (error, tree, file) => {\n        if (error || !tree || !file) {\n          done(error)\n        } else {\n          /** @type {unknown} */\n          const result = processor.stringify(tree, file)\n\n          if (result === undefined || result === null) {\n            // Empty.\n          } else if (looksLikeAVFileValue(result)) {\n            file.value = result\n          } else {\n            file.result = result\n          }\n\n          done(error, file)\n        }\n      })\n\n      /**\n       * @param {Error|null|undefined} [error]\n       * @param {VFile|undefined} [file]\n       * @returns {void}\n       */\n      function done(error, file) {\n        if (error || !file) {\n          reject(error)\n        } else if (resolve) {\n          resolve(file)\n        } else {\n          // @ts-expect-error: `callback` is defined if `resolve` is not.\n          callback(null, file)\n        }\n      }\n    }\n  }\n\n  /** @type {Processor['processSync']} */\n  function processSync(doc) {\n    /** @type {boolean|undefined} */\n    let complete\n\n    processor.freeze()\n    assertParser('processSync', processor.Parser)\n    assertCompiler('processSync', processor.Compiler)\n\n    const file = vfile(doc)\n\n    processor.process(file, done)\n\n    assertDone('processSync', 'process', complete)\n\n    return file\n\n    /**\n     * @param {Error|null|undefined} [error]\n     * @returns {void}\n     */\n    function done(error) {\n      complete = true\n      ;(0,bail__WEBPACK_IMPORTED_MODULE_4__.bail)(error)\n    }\n  }\n}\n\n/**\n * Check if `value` is a constructor.\n *\n * @param {unknown} value\n * @param {string} name\n * @returns {boolean}\n */\nfunction newable(value, name) {\n  return (\n    typeof value === 'function' &&\n    // Prototypes do exist.\n    // type-coverage:ignore-next-line\n    value.prototype &&\n    // A function with keys in its prototype is probably a constructor.\n    // Classes’ prototype methods are not enumerable, so we check if some value\n    // exists in the prototype.\n    // type-coverage:ignore-next-line\n    (keys(value.prototype) || name in value.prototype)\n  )\n}\n\n/**\n * Check if `value` is an object with keys.\n *\n * @param {Record<string, unknown>} value\n * @returns {boolean}\n */\nfunction keys(value) {\n  /** @type {string} */\n  let key\n\n  for (key in value) {\n    if (own.call(value, key)) {\n      return true\n    }\n  }\n\n  return false\n}\n\n/**\n * Assert a parser is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Parser}\n */\nfunction assertParser(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `Parser`')\n  }\n}\n\n/**\n * Assert a compiler is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Compiler}\n */\nfunction assertCompiler(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `Compiler`')\n  }\n}\n\n/**\n * Assert the processor is not frozen.\n *\n * @param {string} name\n * @param {unknown} frozen\n * @returns {asserts frozen is false}\n */\nfunction assertUnfrozen(name, frozen) {\n  if (frozen) {\n    throw new Error(\n      'Cannot call `' +\n        name +\n        '` on a frozen processor.\\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.'\n    )\n  }\n}\n\n/**\n * Assert `node` is a unist node.\n *\n * @param {unknown} node\n * @returns {asserts node is Node}\n */\nfunction assertNode(node) {\n  // `isPlainObj` unfortunately uses `any` instead of `unknown`.\n  // type-coverage:ignore-next-line\n  if (!(0,is_plain_obj__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(node) || typeof node.type !== 'string') {\n    throw new TypeError('Expected node, got `' + node + '`')\n    // Fine.\n  }\n}\n\n/**\n * Assert that `complete` is `true`.\n *\n * @param {string} name\n * @param {string} asyncName\n * @param {unknown} complete\n * @returns {asserts complete is true}\n */\nfunction assertDone(name, asyncName, complete) {\n  if (!complete) {\n    throw new Error(\n      '`' + name + '` finished async. Use `' + asyncName + '` instead'\n    )\n  }\n}\n\n/**\n * @param {VFileCompatible} [value]\n * @returns {VFile}\n */\nfunction vfile(value) {\n  return looksLikeAVFile(value) ? value : new vfile__WEBPACK_IMPORTED_MODULE_5__.VFile(value)\n}\n\n/**\n * @param {VFileCompatible} [value]\n * @returns {value is VFile}\n */\nfunction looksLikeAVFile(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'message' in value &&\n      'messages' in value\n  )\n}\n\n/**\n * @param {unknown} [value]\n * @returns {value is VFileValue}\n */\nfunction looksLikeAVFileValue(value) {\n  return typeof value === 'string' || is_buffer__WEBPACK_IMPORTED_MODULE_0__(value)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unified/lib/index.js\n");

/***/ })

};
;