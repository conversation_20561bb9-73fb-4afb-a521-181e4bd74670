"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/w3c-keyname";
exports.ids = ["vendor-chunks/w3c-keyname"];
exports.modules = {

/***/ "(ssr)/./node_modules/w3c-keyname/index.js":
/*!*******************************************!*\
  !*** ./node_modules/w3c-keyname/index.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base: () => (/* binding */ base),\n/* harmony export */   keyName: () => (/* binding */ keyName),\n/* harmony export */   shift: () => (/* binding */ shift)\n/* harmony export */ });\nvar base = {\n  8: \"Backspace\",\n  9: \"Tab\",\n  10: \"Enter\",\n  12: \"NumLock\",\n  13: \"Enter\",\n  16: \"Shift\",\n  17: \"Control\",\n  18: \"Alt\",\n  20: \"CapsLock\",\n  27: \"Escape\",\n  32: \" \",\n  33: \"PageUp\",\n  34: \"PageDown\",\n  35: \"End\",\n  36: \"Home\",\n  37: \"ArrowLeft\",\n  38: \"ArrowUp\",\n  39: \"ArrowRight\",\n  40: \"ArrowDown\",\n  44: \"PrintScreen\",\n  45: \"Insert\",\n  46: \"Delete\",\n  59: \";\",\n  61: \"=\",\n  91: \"Meta\",\n  92: \"Meta\",\n  106: \"*\",\n  107: \"+\",\n  108: \",\",\n  109: \"-\",\n  110: \".\",\n  111: \"/\",\n  144: \"NumLock\",\n  145: \"ScrollLock\",\n  160: \"Shift\",\n  161: \"Shift\",\n  162: \"Control\",\n  163: \"Control\",\n  164: \"Alt\",\n  165: \"Alt\",\n  173: \"-\",\n  186: \";\",\n  187: \"=\",\n  188: \",\",\n  189: \"-\",\n  190: \".\",\n  191: \"/\",\n  192: \"`\",\n  219: \"[\",\n  220: \"\\\\\",\n  221: \"]\",\n  222: \"'\"\n}\n\nvar shift = {\n  48: \")\",\n  49: \"!\",\n  50: \"@\",\n  51: \"#\",\n  52: \"$\",\n  53: \"%\",\n  54: \"^\",\n  55: \"&\",\n  56: \"*\",\n  57: \"(\",\n  59: \":\",\n  61: \"+\",\n  173: \"_\",\n  186: \":\",\n  187: \"+\",\n  188: \"<\",\n  189: \"_\",\n  190: \">\",\n  191: \"?\",\n  192: \"~\",\n  219: \"{\",\n  220: \"|\",\n  221: \"}\",\n  222: \"\\\"\"\n}\n\nvar mac = typeof navigator != \"undefined\" && /Mac/.test(navigator.platform)\nvar ie = typeof navigator != \"undefined\" && /MSIE \\d|Trident\\/(?:[7-9]|\\d{2,})\\..*rv:(\\d+)/.exec(navigator.userAgent)\n\n// Fill in the digit keys\nfor (var i = 0; i < 10; i++) base[48 + i] = base[96 + i] = String(i)\n\n// The function keys\nfor (var i = 1; i <= 24; i++) base[i + 111] = \"F\" + i\n\n// And the alphabetic keys\nfor (var i = 65; i <= 90; i++) {\n  base[i] = String.fromCharCode(i + 32)\n  shift[i] = String.fromCharCode(i)\n}\n\n// For each code that doesn't have a shift-equivalent, copy the base name\nfor (var code in base) if (!shift.hasOwnProperty(code)) shift[code] = base[code]\n\nfunction keyName(event) {\n  // On macOS, keys held with Shift and Cmd don't reflect the effect of Shift in `.key`.\n  // On IE, shift effect is never included in `.key`.\n  var ignoreKey = mac && event.metaKey && event.shiftKey && !event.ctrlKey && !event.altKey ||\n      ie && event.shiftKey && event.key && event.key.length == 1 ||\n      event.key == \"Unidentified\"\n  var name = (!ignoreKey && event.key) ||\n    (event.shiftKey ? shift : base)[event.keyCode] ||\n    event.key || \"Unidentified\"\n  // Edge sometimes produces wrong names (Issue #3)\n  if (name == \"Esc\") name = \"Escape\"\n  if (name == \"Del\") name = \"Delete\"\n  // https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/8860571/\n  if (name == \"Left\") name = \"ArrowLeft\"\n  if (name == \"Up\") name = \"ArrowUp\"\n  if (name == \"Right\") name = \"ArrowRight\"\n  if (name == \"Down\") name = \"ArrowDown\"\n  return name\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/w3c-keyname/index.js\n");

/***/ })

};
;