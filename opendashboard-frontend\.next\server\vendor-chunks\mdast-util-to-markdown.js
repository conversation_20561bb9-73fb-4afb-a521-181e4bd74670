"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-to-markdown";
exports.ids = ["vendor-chunks/mdast-util-to-markdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/configure.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/configure.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   configure: () => (/* binding */ configure)\n/* harmony export */ });\n/**\n * @typedef {import('./types.js').Options} Options\n * @typedef {import('./types.js').State} State\n */\n\n/**\n * @param {State} base\n * @param {Options} extension\n * @returns {State}\n */\nfunction configure(base, extension) {\n  let index = -1\n  /** @type {keyof Options} */\n  let key\n\n  // First do subextensions.\n  if (extension.extensions) {\n    while (++index < extension.extensions.length) {\n      configure(base, extension.extensions[index])\n    }\n  }\n\n  for (key in extension) {\n    if (key === 'extensions') {\n      // Empty.\n    } else if (key === 'unsafe' || key === 'join') {\n      /* c8 ignore next 2 */\n      // @ts-expect-error: hush.\n      base[key] = [...(base[key] || []), ...(extension[key] || [])]\n    } else if (key === 'handlers') {\n      base[key] = Object.assign(base[key], extension[key] || {})\n    } else {\n      // @ts-expect-error: hush.\n      base.options[key] = extension[key]\n    }\n  }\n\n  return base\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/configure.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blockquote: () => (/* binding */ blockquote)\n/* harmony export */ });\n/**\n * @typedef {import('mdast').Blockquote} Blockquote\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n * @typedef {import('../types.js').Map} Map\n */\n\n/**\n * @param {Blockquote} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction blockquote(node, _, state, info) {\n  const exit = state.enter('blockquote')\n  const tracker = state.createTracker(info)\n  tracker.move('> ')\n  tracker.shift(2)\n  const value = state.indentLines(\n    state.containerFlow(node, tracker.current()),\n    map\n  )\n  exit()\n  return value\n}\n\n/** @type {Map} */\nfunction map(line, _, blank) {\n  return '>' + (blank ? '' : ' ') + line\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2Jsb2NrcXVvdGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSw0QkFBNEI7QUFDekMsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSw2QkFBNkI7QUFDMUMsYUFBYSw0QkFBNEI7QUFDekMsYUFBYSwyQkFBMkI7QUFDeEM7O0FBRUE7QUFDQSxXQUFXLFlBQVk7QUFDdkIsV0FBVyxvQkFBb0I7QUFDL0IsV0FBVyxPQUFPO0FBQ2xCLFdBQVcsTUFBTTtBQUNqQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsV0FBVyxLQUFLO0FBQ2hCO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2Jsb2NrcXVvdGUuanM/NzIwYyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuQmxvY2txdW90ZX0gQmxvY2txdW90ZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5QYXJlbnR9IFBhcmVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5TdGF0ZX0gU3RhdGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSW5mb30gSW5mb1xuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5NYXB9IE1hcFxuICovXG5cbi8qKlxuICogQHBhcmFtIHtCbG9ja3F1b3RlfSBub2RlXG4gKiBAcGFyYW0ge1BhcmVudCB8IHVuZGVmaW5lZH0gX1xuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEBwYXJhbSB7SW5mb30gaW5mb1xuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGJsb2NrcXVvdGUobm9kZSwgXywgc3RhdGUsIGluZm8pIHtcbiAgY29uc3QgZXhpdCA9IHN0YXRlLmVudGVyKCdibG9ja3F1b3RlJylcbiAgY29uc3QgdHJhY2tlciA9IHN0YXRlLmNyZWF0ZVRyYWNrZXIoaW5mbylcbiAgdHJhY2tlci5tb3ZlKCc+ICcpXG4gIHRyYWNrZXIuc2hpZnQoMilcbiAgY29uc3QgdmFsdWUgPSBzdGF0ZS5pbmRlbnRMaW5lcyhcbiAgICBzdGF0ZS5jb250YWluZXJGbG93KG5vZGUsIHRyYWNrZXIuY3VycmVudCgpKSxcbiAgICBtYXBcbiAgKVxuICBleGl0KClcbiAgcmV0dXJuIHZhbHVlXG59XG5cbi8qKiBAdHlwZSB7TWFwfSAqL1xuZnVuY3Rpb24gbWFwKGxpbmUsIF8sIGJsYW5rKSB7XG4gIHJldHVybiAnPicgKyAoYmxhbmsgPyAnJyA6ICcgJykgKyBsaW5lXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/break.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/break.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hardBreak: () => (/* binding */ hardBreak)\n/* harmony export */ });\n/* harmony import */ var _util_pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/pattern-in-scope.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js\");\n/**\n * @typedef {import('mdast').Break} Break\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n\n\n/**\n * @param {Break} _\n * @param {Parent | undefined} _1\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction hardBreak(_, _1, state, info) {\n  let index = -1\n\n  while (++index < state.unsafe.length) {\n    // If we can’t put eols in this construct (setext headings, tables), use a\n    // space instead.\n    if (\n      state.unsafe[index].character === '\\n' &&\n      (0,_util_pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__.patternInScope)(state.stack, state.unsafe[index])\n    ) {\n      return /[ \\t]/.test(info.before) ? '' : ' '\n    }\n  }\n\n  return '\\\\\\n'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2JyZWFrLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLHVCQUF1QjtBQUNwQyxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLDZCQUE2QjtBQUMxQyxhQUFhLDRCQUE0QjtBQUN6Qzs7QUFFMEQ7O0FBRTFEO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCLFdBQVcsb0JBQW9CO0FBQy9CLFdBQVcsT0FBTztBQUNsQixXQUFXLE1BQU07QUFDakIsYUFBYTtBQUNiO0FBQ087QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTSx5RUFBYztBQUNwQjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvYnJlYWsuanM/OTg2OSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuQnJlYWt9IEJyZWFrXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlBhcmVudH0gUGFyZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlN0YXRlfSBTdGF0ZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5JbmZvfSBJbmZvXG4gKi9cblxuaW1wb3J0IHtwYXR0ZXJuSW5TY29wZX0gZnJvbSAnLi4vdXRpbC9wYXR0ZXJuLWluLXNjb3BlLmpzJ1xuXG4vKipcbiAqIEBwYXJhbSB7QnJlYWt9IF9cbiAqIEBwYXJhbSB7UGFyZW50IHwgdW5kZWZpbmVkfSBfMVxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEBwYXJhbSB7SW5mb30gaW5mb1xuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGhhcmRCcmVhayhfLCBfMSwgc3RhdGUsIGluZm8pIHtcbiAgbGV0IGluZGV4ID0gLTFcblxuICB3aGlsZSAoKytpbmRleCA8IHN0YXRlLnVuc2FmZS5sZW5ndGgpIHtcbiAgICAvLyBJZiB3ZSBjYW7igJl0IHB1dCBlb2xzIGluIHRoaXMgY29uc3RydWN0IChzZXRleHQgaGVhZGluZ3MsIHRhYmxlcyksIHVzZSBhXG4gICAgLy8gc3BhY2UgaW5zdGVhZC5cbiAgICBpZiAoXG4gICAgICBzdGF0ZS51bnNhZmVbaW5kZXhdLmNoYXJhY3RlciA9PT0gJ1xcbicgJiZcbiAgICAgIHBhdHRlcm5JblNjb3BlKHN0YXRlLnN0YWNrLCBzdGF0ZS51bnNhZmVbaW5kZXhdKVxuICAgICkge1xuICAgICAgcmV0dXJuIC9bIFxcdF0vLnRlc3QoaW5mby5iZWZvcmUpID8gJycgOiAnICdcbiAgICB9XG4gIH1cblxuICByZXR1cm4gJ1xcXFxcXG4nXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/break.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/code.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/code.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   code: () => (/* binding */ code)\n/* harmony export */ });\n/* harmony import */ var longest_streak__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! longest-streak */ \"(ssr)/./node_modules/longest-streak/index.js\");\n/* harmony import */ var _util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/format-code-as-indented.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js\");\n/* harmony import */ var _util_check_fence_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-fence.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js\");\n/**\n * @typedef {import('mdast').Code} Code\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n * @typedef {import('../types.js').Map} Map\n */\n\n\n\n\n\n/**\n * @param {Code} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction code(node, _, state, info) {\n  const marker = (0,_util_check_fence_js__WEBPACK_IMPORTED_MODULE_0__.checkFence)(state)\n  const raw = node.value || ''\n  const suffix = marker === '`' ? 'GraveAccent' : 'Tilde'\n\n  if ((0,_util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_1__.formatCodeAsIndented)(node, state)) {\n    const exit = state.enter('codeIndented')\n    const value = state.indentLines(raw, map)\n    exit()\n    return value\n  }\n\n  const tracker = state.createTracker(info)\n  const sequence = marker.repeat(Math.max((0,longest_streak__WEBPACK_IMPORTED_MODULE_2__.longestStreak)(raw, marker) + 1, 3))\n  const exit = state.enter('codeFenced')\n  let value = tracker.move(sequence)\n\n  if (node.lang) {\n    const subexit = state.enter(`codeFencedLang${suffix}`)\n    value += tracker.move(\n      state.safe(node.lang, {\n        before: value,\n        after: ' ',\n        encode: ['`'],\n        ...tracker.current()\n      })\n    )\n    subexit()\n  }\n\n  if (node.lang && node.meta) {\n    const subexit = state.enter(`codeFencedMeta${suffix}`)\n    value += tracker.move(' ')\n    value += tracker.move(\n      state.safe(node.meta, {\n        before: value,\n        after: '\\n',\n        encode: ['`'],\n        ...tracker.current()\n      })\n    )\n    subexit()\n  }\n\n  value += tracker.move('\\n')\n\n  if (raw) {\n    value += tracker.move(raw + '\\n')\n  }\n\n  value += tracker.move(sequence)\n  exit()\n  return value\n}\n\n/** @type {Map} */\nfunction map(line, _, blank) {\n  return (blank ? '' : '    ') + line\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/definition.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   definition: () => (/* binding */ definition)\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/**\n * @typedef {import('mdast').Definition} Definition\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n\n\n/**\n * @param {Definition} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction definition(node, _, state, info) {\n  const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const exit = state.enter('definition')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('[')\n  value += tracker.move(\n    state.safe(state.associationId(node), {\n      before: value,\n      after: ']',\n      ...tracker.current()\n    })\n  )\n  value += tracker.move(']: ')\n\n  subexit()\n\n  if (\n    // If there’s no url, or…\n    !node.url ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : '\\n',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  exit()\n\n  return value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   emphasis: () => (/* binding */ emphasis)\n/* harmony export */ });\n/* harmony import */ var _util_check_emphasis_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-emphasis.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js\");\n/**\n * @typedef {import('mdast').Emphasis} Emphasis\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n\n\nemphasis.peek = emphasisPeek\n\n// To do: there are cases where emphasis cannot “form” depending on the\n// previous or next character of sequences.\n// There’s no way around that though, except for injecting zero-width stuff.\n// Do we need to safeguard against that?\n/**\n * @param {Emphasis} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction emphasis(node, _, state, info) {\n  const marker = (0,_util_check_emphasis_js__WEBPACK_IMPORTED_MODULE_0__.checkEmphasis)(state)\n  const exit = state.enter('emphasis')\n  const tracker = state.createTracker(info)\n  let value = tracker.move(marker)\n  value += tracker.move(\n    state.containerPhrasing(node, {\n      before: value,\n      after: marker,\n      ...tracker.current()\n    })\n  )\n  value += tracker.move(marker)\n  exit()\n  return value\n}\n\n/**\n * @param {Emphasis} _\n * @param {Parent | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nfunction emphasisPeek(_, _1, state) {\n  return state.options.emphasis || '*'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/heading.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   heading: () => (/* binding */ heading)\n/* harmony export */ });\n/* harmony import */ var _util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/format-heading-as-setext.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js\");\n/**\n * @typedef {import('mdast').Heading} Heading\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n\n\n/**\n * @param {Heading} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction heading(node, _, state, info) {\n  const rank = Math.max(Math.min(6, node.depth || 1), 1)\n  const tracker = state.createTracker(info)\n\n  if ((0,_util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_0__.formatHeadingAsSetext)(node, state)) {\n    const exit = state.enter('headingSetext')\n    const subexit = state.enter('phrasing')\n    const value = state.containerPhrasing(node, {\n      ...tracker.current(),\n      before: '\\n',\n      after: '\\n'\n    })\n    subexit()\n    exit()\n\n    return (\n      value +\n      '\\n' +\n      (rank === 1 ? '=' : '-').repeat(\n        // The whole size…\n        value.length -\n          // Minus the position of the character after the last EOL (or\n          // 0 if there is none)…\n          (Math.max(value.lastIndexOf('\\r'), value.lastIndexOf('\\n')) + 1)\n      )\n    )\n  }\n\n  const sequence = '#'.repeat(rank)\n  const exit = state.enter('headingAtx')\n  const subexit = state.enter('phrasing')\n\n  // Note: for proper tracking, we should reset the output positions when there\n  // is no content returned, because then the space is not output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  tracker.move(sequence + ' ')\n\n  let value = state.containerPhrasing(node, {\n    before: '# ',\n    after: '\\n',\n    ...tracker.current()\n  })\n\n  if (/^[\\t ]/.test(value)) {\n    // To do: what effect has the character reference on tracking?\n    value =\n      '&#x' +\n      value.charCodeAt(0).toString(16).toUpperCase() +\n      ';' +\n      value.slice(1)\n  }\n\n  value = value ? sequence + ' ' + value : sequence\n\n  if (state.options.closeAtx) {\n    value += ' ' + sequence\n  }\n\n  subexit()\n  exit()\n\n  return value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2hlYWRpbmcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEseUJBQXlCO0FBQ3RDLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsNkJBQTZCO0FBQzFDLGFBQWEsNEJBQTRCO0FBQ3pDOztBQUV5RTs7QUFFekU7QUFDQSxXQUFXLFNBQVM7QUFDcEIsV0FBVyxvQkFBb0I7QUFDL0IsV0FBVyxPQUFPO0FBQ2xCLFdBQVcsTUFBTTtBQUNqQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0E7O0FBRUEsTUFBTSx3RkFBcUI7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2hlYWRpbmcuanM/NjMxNCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuSGVhZGluZ30gSGVhZGluZ1xuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5QYXJlbnR9IFBhcmVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5TdGF0ZX0gU3RhdGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSW5mb30gSW5mb1xuICovXG5cbmltcG9ydCB7Zm9ybWF0SGVhZGluZ0FzU2V0ZXh0fSBmcm9tICcuLi91dGlsL2Zvcm1hdC1oZWFkaW5nLWFzLXNldGV4dC5qcydcblxuLyoqXG4gKiBAcGFyYW0ge0hlYWRpbmd9IG5vZGVcbiAqIEBwYXJhbSB7UGFyZW50IHwgdW5kZWZpbmVkfSBfXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHBhcmFtIHtJbmZvfSBpbmZvXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gaGVhZGluZyhub2RlLCBfLCBzdGF0ZSwgaW5mbykge1xuICBjb25zdCByYW5rID0gTWF0aC5tYXgoTWF0aC5taW4oNiwgbm9kZS5kZXB0aCB8fCAxKSwgMSlcbiAgY29uc3QgdHJhY2tlciA9IHN0YXRlLmNyZWF0ZVRyYWNrZXIoaW5mbylcblxuICBpZiAoZm9ybWF0SGVhZGluZ0FzU2V0ZXh0KG5vZGUsIHN0YXRlKSkge1xuICAgIGNvbnN0IGV4aXQgPSBzdGF0ZS5lbnRlcignaGVhZGluZ1NldGV4dCcpXG4gICAgY29uc3Qgc3ViZXhpdCA9IHN0YXRlLmVudGVyKCdwaHJhc2luZycpXG4gICAgY29uc3QgdmFsdWUgPSBzdGF0ZS5jb250YWluZXJQaHJhc2luZyhub2RlLCB7XG4gICAgICAuLi50cmFja2VyLmN1cnJlbnQoKSxcbiAgICAgIGJlZm9yZTogJ1xcbicsXG4gICAgICBhZnRlcjogJ1xcbidcbiAgICB9KVxuICAgIHN1YmV4aXQoKVxuICAgIGV4aXQoKVxuXG4gICAgcmV0dXJuIChcbiAgICAgIHZhbHVlICtcbiAgICAgICdcXG4nICtcbiAgICAgIChyYW5rID09PSAxID8gJz0nIDogJy0nKS5yZXBlYXQoXG4gICAgICAgIC8vIFRoZSB3aG9sZSBzaXpl4oCmXG4gICAgICAgIHZhbHVlLmxlbmd0aCAtXG4gICAgICAgICAgLy8gTWludXMgdGhlIHBvc2l0aW9uIG9mIHRoZSBjaGFyYWN0ZXIgYWZ0ZXIgdGhlIGxhc3QgRU9MIChvclxuICAgICAgICAgIC8vIDAgaWYgdGhlcmUgaXMgbm9uZSnigKZcbiAgICAgICAgICAoTWF0aC5tYXgodmFsdWUubGFzdEluZGV4T2YoJ1xccicpLCB2YWx1ZS5sYXN0SW5kZXhPZignXFxuJykpICsgMSlcbiAgICAgIClcbiAgICApXG4gIH1cblxuICBjb25zdCBzZXF1ZW5jZSA9ICcjJy5yZXBlYXQocmFuaylcbiAgY29uc3QgZXhpdCA9IHN0YXRlLmVudGVyKCdoZWFkaW5nQXR4JylcbiAgY29uc3Qgc3ViZXhpdCA9IHN0YXRlLmVudGVyKCdwaHJhc2luZycpXG5cbiAgLy8gTm90ZTogZm9yIHByb3BlciB0cmFja2luZywgd2Ugc2hvdWxkIHJlc2V0IHRoZSBvdXRwdXQgcG9zaXRpb25zIHdoZW4gdGhlcmVcbiAgLy8gaXMgbm8gY29udGVudCByZXR1cm5lZCwgYmVjYXVzZSB0aGVuIHRoZSBzcGFjZSBpcyBub3Qgb3V0cHV0LlxuICAvLyBQcmFjdGljYWxseSwgaW4gdGhhdCBjYXNlLCB0aGVyZSBpcyBubyBjb250ZW50LCBzbyBpdCBkb2VzbuKAmXQgbWF0dGVyIHRoYXRcbiAgLy8gd2XigJl2ZSB0cmFja2VkIG9uZSB0b28gbWFueSBjaGFyYWN0ZXJzLlxuICB0cmFja2VyLm1vdmUoc2VxdWVuY2UgKyAnICcpXG5cbiAgbGV0IHZhbHVlID0gc3RhdGUuY29udGFpbmVyUGhyYXNpbmcobm9kZSwge1xuICAgIGJlZm9yZTogJyMgJyxcbiAgICBhZnRlcjogJ1xcbicsXG4gICAgLi4udHJhY2tlci5jdXJyZW50KClcbiAgfSlcblxuICBpZiAoL15bXFx0IF0vLnRlc3QodmFsdWUpKSB7XG4gICAgLy8gVG8gZG86IHdoYXQgZWZmZWN0IGhhcyB0aGUgY2hhcmFjdGVyIHJlZmVyZW5jZSBvbiB0cmFja2luZz9cbiAgICB2YWx1ZSA9XG4gICAgICAnJiN4JyArXG4gICAgICB2YWx1ZS5jaGFyQ29kZUF0KDApLnRvU3RyaW5nKDE2KS50b1VwcGVyQ2FzZSgpICtcbiAgICAgICc7JyArXG4gICAgICB2YWx1ZS5zbGljZSgxKVxuICB9XG5cbiAgdmFsdWUgPSB2YWx1ZSA/IHNlcXVlbmNlICsgJyAnICsgdmFsdWUgOiBzZXF1ZW5jZVxuXG4gIGlmIChzdGF0ZS5vcHRpb25zLmNsb3NlQXR4KSB7XG4gICAgdmFsdWUgKz0gJyAnICsgc2VxdWVuY2VcbiAgfVxuXG4gIHN1YmV4aXQoKVxuICBleGl0KClcblxuICByZXR1cm4gdmFsdWVcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/html.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/html.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   html: () => (/* binding */ html)\n/* harmony export */ });\n/**\n * @typedef {import('mdast').HTML} HTML\n */\n\nhtml.peek = htmlPeek\n\n/**\n * @param {HTML} node\n * @returns {string}\n */\nfunction html(node) {\n  return node.value || ''\n}\n\n/**\n * @returns {string}\n */\nfunction htmlPeek() {\n  return '<'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2h0bWwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSxzQkFBc0I7QUFDbkM7O0FBRUE7O0FBRUE7QUFDQSxXQUFXLE1BQU07QUFDakIsYUFBYTtBQUNiO0FBQ087QUFDUDtBQUNBOztBQUVBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvaHRtbC5qcz9iOTg5Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5IVE1MfSBIVE1MXG4gKi9cblxuaHRtbC5wZWVrID0gaHRtbFBlZWtcblxuLyoqXG4gKiBAcGFyYW0ge0hUTUx9IG5vZGVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBodG1sKG5vZGUpIHtcbiAgcmV0dXJuIG5vZGUudmFsdWUgfHwgJydcbn1cblxuLyoqXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5mdW5jdGlvbiBodG1sUGVlaygpIHtcbiAgcmV0dXJuICc8J1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js":
/*!***************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   imageReference: () => (/* binding */ imageReference)\n/* harmony export */ });\n/**\n * @typedef {import('mdast').ImageReference} ImageReference\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\nimageReference.peek = imageReferencePeek\n\n/**\n * @param {ImageReference} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction imageReference(node, _, state, info) {\n  const type = node.referenceType\n  const exit = state.enter('imageReference')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('![')\n  const alt = state.safe(node.alt, {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  value += tracker.move(alt + '][')\n\n  subexit()\n  // Hide the fact that we’re in phrasing, because escapes don’t work.\n  const stack = state.stack\n  state.stack = []\n  subexit = state.enter('reference')\n  // Note: for proper tracking, we should reset the output positions when we end\n  // up making a `shortcut` reference, because then there is no brace output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  const reference = state.safe(state.associationId(node), {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  subexit()\n  state.stack = stack\n  exit()\n\n  if (type === 'full' || !alt || alt !== reference) {\n    value += tracker.move(reference + ']')\n  } else if (type === 'shortcut') {\n    // Remove the unwanted `[`.\n    value = value.slice(0, -1)\n  } else {\n    value += tracker.move(']')\n  }\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction imageReferencePeek() {\n  return '!'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/image.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   image: () => (/* binding */ image)\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/**\n * @typedef {import('mdast').Image} Image\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n\n\nimage.peek = imagePeek\n\n/**\n * @param {Image} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction image(node, _, state, info) {\n  const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const exit = state.enter('image')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('![')\n  value += tracker.move(\n    state.safe(node.alt, {before: value, after: ']', ...tracker.current()})\n  )\n  value += tracker.move('](')\n\n  subexit()\n\n  if (\n    // If there’s no url but there is a title…\n    (!node.url && node.title) ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : ')',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  value += tracker.move(')')\n  exit()\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction imagePeek() {\n  return '!'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handle: () => (/* binding */ handle)\n/* harmony export */ });\n/* harmony import */ var _blockquote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./blockquote.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js\");\n/* harmony import */ var _break_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./break.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/break.js\");\n/* harmony import */ var _code_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./code.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/code.js\");\n/* harmony import */ var _definition_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./definition.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js\");\n/* harmony import */ var _emphasis_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./emphasis.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js\");\n/* harmony import */ var _heading_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./heading.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js\");\n/* harmony import */ var _html_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./html.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/html.js\");\n/* harmony import */ var _image_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./image.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image.js\");\n/* harmony import */ var _image_reference_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./image-reference.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js\");\n/* harmony import */ var _inline_code_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./inline-code.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js\");\n/* harmony import */ var _link_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./link.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link.js\");\n/* harmony import */ var _link_reference_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./link-reference.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js\");\n/* harmony import */ var _list_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./list.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list.js\");\n/* harmony import */ var _list_item_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./list-item.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js\");\n/* harmony import */ var _paragraph_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./paragraph.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./root.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/root.js\");\n/* harmony import */ var _strong_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./strong.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./text.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/text.js\");\n/* harmony import */ var _thematic_break_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./thematic-break.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Default (CommonMark) handlers.\n */\nconst handle = {\n  blockquote: _blockquote_js__WEBPACK_IMPORTED_MODULE_0__.blockquote,\n  break: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,\n  code: _code_js__WEBPACK_IMPORTED_MODULE_2__.code,\n  definition: _definition_js__WEBPACK_IMPORTED_MODULE_3__.definition,\n  emphasis: _emphasis_js__WEBPACK_IMPORTED_MODULE_4__.emphasis,\n  hardBreak: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,\n  heading: _heading_js__WEBPACK_IMPORTED_MODULE_5__.heading,\n  html: _html_js__WEBPACK_IMPORTED_MODULE_6__.html,\n  image: _image_js__WEBPACK_IMPORTED_MODULE_7__.image,\n  imageReference: _image_reference_js__WEBPACK_IMPORTED_MODULE_8__.imageReference,\n  inlineCode: _inline_code_js__WEBPACK_IMPORTED_MODULE_9__.inlineCode,\n  link: _link_js__WEBPACK_IMPORTED_MODULE_10__.link,\n  linkReference: _link_reference_js__WEBPACK_IMPORTED_MODULE_11__.linkReference,\n  list: _list_js__WEBPACK_IMPORTED_MODULE_12__.list,\n  listItem: _list_item_js__WEBPACK_IMPORTED_MODULE_13__.listItem,\n  paragraph: _paragraph_js__WEBPACK_IMPORTED_MODULE_14__.paragraph,\n  root: _root_js__WEBPACK_IMPORTED_MODULE_15__.root,\n  strong: _strong_js__WEBPACK_IMPORTED_MODULE_16__.strong,\n  text: _text_js__WEBPACK_IMPORTED_MODULE_17__.text,\n  thematicBreak: _thematic_break_js__WEBPACK_IMPORTED_MODULE_18__.thematicBreak\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js":
/*!***********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inlineCode: () => (/* binding */ inlineCode)\n/* harmony export */ });\n/* harmony import */ var _util_pattern_compile_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/pattern-compile.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-compile.js\");\n/**\n * @typedef {import('mdast').InlineCode} InlineCode\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n */\n\n\n\ninlineCode.peek = inlineCodePeek\n\n/**\n * @param {InlineCode} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @returns {string}\n */\nfunction inlineCode(node, _, state) {\n  let value = node.value || ''\n  let sequence = '`'\n  let index = -1\n\n  // If there is a single grave accent on its own in the code, use a fence of\n  // two.\n  // If there are two in a row, use one.\n  while (new RegExp('(^|[^`])' + sequence + '([^`]|$)').test(value)) {\n    sequence += '`'\n  }\n\n  // If this is not just spaces or eols (tabs don’t count), and either the\n  // first or last character are a space, eol, or tick, then pad with spaces.\n  if (\n    /[^ \\r\\n]/.test(value) &&\n    ((/^[ \\r\\n]/.test(value) && /[ \\r\\n]$/.test(value)) || /^`|`$/.test(value))\n  ) {\n    value = ' ' + value + ' '\n  }\n\n  // We have a potential problem: certain characters after eols could result in\n  // blocks being seen.\n  // For example, if someone injected the string `'\\n# b'`, then that would\n  // result in an ATX heading.\n  // We can’t escape characters in `inlineCode`, but because eols are\n  // transformed to spaces when going from markdown to HTML anyway, we can swap\n  // them out.\n  while (++index < state.unsafe.length) {\n    const pattern = state.unsafe[index]\n    const expression = (0,_util_pattern_compile_js__WEBPACK_IMPORTED_MODULE_0__.patternCompile)(pattern)\n    /** @type {RegExpExecArray | null} */\n    let match\n\n    // Only look for `atBreak`s.\n    // Btw: note that `atBreak` patterns will always start the regex at LF or\n    // CR.\n    if (!pattern.atBreak) continue\n\n    while ((match = expression.exec(value))) {\n      let position = match.index\n\n      // Support CRLF (patterns only look for one of the characters).\n      if (\n        value.charCodeAt(position) === 10 /* `\\n` */ &&\n        value.charCodeAt(position - 1) === 13 /* `\\r` */\n      ) {\n        position--\n      }\n\n      value = value.slice(0, position) + ' ' + value.slice(match.index + 1)\n    }\n  }\n\n  return sequence + value + sequence\n}\n\n/**\n * @returns {string}\n */\nfunction inlineCodePeek() {\n  return '`'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   linkReference: () => (/* binding */ linkReference)\n/* harmony export */ });\n/**\n * @typedef {import('mdast').LinkReference} LinkReference\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\nlinkReference.peek = linkReferencePeek\n\n/**\n * @param {LinkReference} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction linkReference(node, _, state, info) {\n  const type = node.referenceType\n  const exit = state.enter('linkReference')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('[')\n  const text = state.containerPhrasing(node, {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  value += tracker.move(text + '][')\n\n  subexit()\n  // Hide the fact that we’re in phrasing, because escapes don’t work.\n  const stack = state.stack\n  state.stack = []\n  subexit = state.enter('reference')\n  // Note: for proper tracking, we should reset the output positions when we end\n  // up making a `shortcut` reference, because then there is no brace output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  const reference = state.safe(state.associationId(node), {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  subexit()\n  state.stack = stack\n  exit()\n\n  if (type === 'full' || !text || text !== reference) {\n    value += tracker.move(reference + ']')\n  } else if (type === 'shortcut') {\n    // Remove the unwanted `[`.\n    value = value.slice(0, -1)\n  } else {\n    value += tracker.move(']')\n  }\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction linkReferencePeek() {\n  return '['\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2xpbmstcmVmZXJlbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsK0JBQStCO0FBQzVDLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsNkJBQTZCO0FBQzFDLGFBQWEsNEJBQTRCO0FBQ3pDOztBQUVBOztBQUVBO0FBQ0EsV0FBVyxlQUFlO0FBQzFCLFdBQVcsb0JBQW9CO0FBQy9CLFdBQVcsT0FBTztBQUNsQixXQUFXLE1BQU07QUFDakIsYUFBYTtBQUNiO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL2hhbmRsZS9saW5rLXJlZmVyZW5jZS5qcz82OWRjIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5MaW5rUmVmZXJlbmNlfSBMaW5rUmVmZXJlbmNlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlBhcmVudH0gUGFyZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlN0YXRlfSBTdGF0ZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5JbmZvfSBJbmZvXG4gKi9cblxubGlua1JlZmVyZW5jZS5wZWVrID0gbGlua1JlZmVyZW5jZVBlZWtcblxuLyoqXG4gKiBAcGFyYW0ge0xpbmtSZWZlcmVuY2V9IG5vZGVcbiAqIEBwYXJhbSB7UGFyZW50IHwgdW5kZWZpbmVkfSBfXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHBhcmFtIHtJbmZvfSBpbmZvXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gbGlua1JlZmVyZW5jZShub2RlLCBfLCBzdGF0ZSwgaW5mbykge1xuICBjb25zdCB0eXBlID0gbm9kZS5yZWZlcmVuY2VUeXBlXG4gIGNvbnN0IGV4aXQgPSBzdGF0ZS5lbnRlcignbGlua1JlZmVyZW5jZScpXG4gIGxldCBzdWJleGl0ID0gc3RhdGUuZW50ZXIoJ2xhYmVsJylcbiAgY29uc3QgdHJhY2tlciA9IHN0YXRlLmNyZWF0ZVRyYWNrZXIoaW5mbylcbiAgbGV0IHZhbHVlID0gdHJhY2tlci5tb3ZlKCdbJylcbiAgY29uc3QgdGV4dCA9IHN0YXRlLmNvbnRhaW5lclBocmFzaW5nKG5vZGUsIHtcbiAgICBiZWZvcmU6IHZhbHVlLFxuICAgIGFmdGVyOiAnXScsXG4gICAgLi4udHJhY2tlci5jdXJyZW50KClcbiAgfSlcbiAgdmFsdWUgKz0gdHJhY2tlci5tb3ZlKHRleHQgKyAnXVsnKVxuXG4gIHN1YmV4aXQoKVxuICAvLyBIaWRlIHRoZSBmYWN0IHRoYXQgd2XigJlyZSBpbiBwaHJhc2luZywgYmVjYXVzZSBlc2NhcGVzIGRvbuKAmXQgd29yay5cbiAgY29uc3Qgc3RhY2sgPSBzdGF0ZS5zdGFja1xuICBzdGF0ZS5zdGFjayA9IFtdXG4gIHN1YmV4aXQgPSBzdGF0ZS5lbnRlcigncmVmZXJlbmNlJylcbiAgLy8gTm90ZTogZm9yIHByb3BlciB0cmFja2luZywgd2Ugc2hvdWxkIHJlc2V0IHRoZSBvdXRwdXQgcG9zaXRpb25zIHdoZW4gd2UgZW5kXG4gIC8vIHVwIG1ha2luZyBhIGBzaG9ydGN1dGAgcmVmZXJlbmNlLCBiZWNhdXNlIHRoZW4gdGhlcmUgaXMgbm8gYnJhY2Ugb3V0cHV0LlxuICAvLyBQcmFjdGljYWxseSwgaW4gdGhhdCBjYXNlLCB0aGVyZSBpcyBubyBjb250ZW50LCBzbyBpdCBkb2VzbuKAmXQgbWF0dGVyIHRoYXRcbiAgLy8gd2XigJl2ZSB0cmFja2VkIG9uZSB0b28gbWFueSBjaGFyYWN0ZXJzLlxuICBjb25zdCByZWZlcmVuY2UgPSBzdGF0ZS5zYWZlKHN0YXRlLmFzc29jaWF0aW9uSWQobm9kZSksIHtcbiAgICBiZWZvcmU6IHZhbHVlLFxuICAgIGFmdGVyOiAnXScsXG4gICAgLi4udHJhY2tlci5jdXJyZW50KClcbiAgfSlcbiAgc3ViZXhpdCgpXG4gIHN0YXRlLnN0YWNrID0gc3RhY2tcbiAgZXhpdCgpXG5cbiAgaWYgKHR5cGUgPT09ICdmdWxsJyB8fCAhdGV4dCB8fCB0ZXh0ICE9PSByZWZlcmVuY2UpIHtcbiAgICB2YWx1ZSArPSB0cmFja2VyLm1vdmUocmVmZXJlbmNlICsgJ10nKVxuICB9IGVsc2UgaWYgKHR5cGUgPT09ICdzaG9ydGN1dCcpIHtcbiAgICAvLyBSZW1vdmUgdGhlIHVud2FudGVkIGBbYC5cbiAgICB2YWx1ZSA9IHZhbHVlLnNsaWNlKDAsIC0xKVxuICB9IGVsc2Uge1xuICAgIHZhbHVlICs9IHRyYWNrZXIubW92ZSgnXScpXG4gIH1cblxuICByZXR1cm4gdmFsdWVcbn1cblxuLyoqXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5mdW5jdGlvbiBsaW5rUmVmZXJlbmNlUGVlaygpIHtcbiAgcmV0dXJuICdbJ1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/link.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   link: () => (/* binding */ link)\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/* harmony import */ var _util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/format-link-as-autolink.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js\");\n/**\n * @typedef {import('mdast').Link} Link\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n * @typedef {import('../types.js').Exit} Exit\n */\n\n\n\n\nlink.peek = linkPeek\n\n/**\n * @param {Link} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction link(node, _, state, info) {\n  const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const tracker = state.createTracker(info)\n  /** @type {Exit} */\n  let exit\n  /** @type {Exit} */\n  let subexit\n\n  if ((0,_util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__.formatLinkAsAutolink)(node, state)) {\n    // Hide the fact that we’re in phrasing, because escapes don’t work.\n    const stack = state.stack\n    state.stack = []\n    exit = state.enter('autolink')\n    let value = tracker.move('<')\n    value += tracker.move(\n      state.containerPhrasing(node, {\n        before: value,\n        after: '>',\n        ...tracker.current()\n      })\n    )\n    value += tracker.move('>')\n    exit()\n    state.stack = stack\n    return value\n  }\n\n  exit = state.enter('link')\n  subexit = state.enter('label')\n  let value = tracker.move('[')\n  value += tracker.move(\n    state.containerPhrasing(node, {\n      before: value,\n      after: '](',\n      ...tracker.current()\n    })\n  )\n  value += tracker.move('](')\n  subexit()\n\n  if (\n    // If there’s no url but there is a title…\n    (!node.url && node.title) ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : ')',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  value += tracker.move(')')\n\n  exit()\n  return value\n}\n\n/**\n * @param {Link} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @returns {string}\n */\nfunction linkPeek(node, _, state) {\n  return (0,_util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__.formatLinkAsAutolink)(node, state) ? '<' : '['\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2xpbmsuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQSxhQUFhLHNCQUFzQjtBQUNuQyxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLDZCQUE2QjtBQUMxQyxhQUFhLDRCQUE0QjtBQUN6QyxhQUFhLDRCQUE0QjtBQUN6Qzs7QUFFaUQ7QUFDc0I7O0FBRXZFOztBQUVBO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCLFdBQVcsb0JBQW9CO0FBQy9CLFdBQVcsT0FBTztBQUNsQixXQUFXLE1BQU07QUFDakIsYUFBYTtBQUNiO0FBQ087QUFDUCxnQkFBZ0IsZ0VBQVU7QUFDMUI7QUFDQTtBQUNBLGFBQWEsTUFBTTtBQUNuQjtBQUNBLGFBQWEsTUFBTTtBQUNuQjs7QUFFQSxNQUFNLHNGQUFvQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLGdEQUFnRDtBQUM1RTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQSxrQ0FBa0MsT0FBTztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCLFdBQVcsb0JBQW9CO0FBQy9CLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDQTtBQUNBLFNBQVMsc0ZBQW9CO0FBQzdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvbGluay5qcz8zMzljIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5MaW5rfSBMaW5rXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlBhcmVudH0gUGFyZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlN0YXRlfSBTdGF0ZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5JbmZvfSBJbmZvXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkV4aXR9IEV4aXRcbiAqL1xuXG5pbXBvcnQge2NoZWNrUXVvdGV9IGZyb20gJy4uL3V0aWwvY2hlY2stcXVvdGUuanMnXG5pbXBvcnQge2Zvcm1hdExpbmtBc0F1dG9saW5rfSBmcm9tICcuLi91dGlsL2Zvcm1hdC1saW5rLWFzLWF1dG9saW5rLmpzJ1xuXG5saW5rLnBlZWsgPSBsaW5rUGVla1xuXG4vKipcbiAqIEBwYXJhbSB7TGlua30gbm9kZVxuICogQHBhcmFtIHtQYXJlbnQgfCB1bmRlZmluZWR9IF9cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcGFyYW0ge0luZm99IGluZm9cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBsaW5rKG5vZGUsIF8sIHN0YXRlLCBpbmZvKSB7XG4gIGNvbnN0IHF1b3RlID0gY2hlY2tRdW90ZShzdGF0ZSlcbiAgY29uc3Qgc3VmZml4ID0gcXVvdGUgPT09ICdcIicgPyAnUXVvdGUnIDogJ0Fwb3N0cm9waGUnXG4gIGNvbnN0IHRyYWNrZXIgPSBzdGF0ZS5jcmVhdGVUcmFja2VyKGluZm8pXG4gIC8qKiBAdHlwZSB7RXhpdH0gKi9cbiAgbGV0IGV4aXRcbiAgLyoqIEB0eXBlIHtFeGl0fSAqL1xuICBsZXQgc3ViZXhpdFxuXG4gIGlmIChmb3JtYXRMaW5rQXNBdXRvbGluayhub2RlLCBzdGF0ZSkpIHtcbiAgICAvLyBIaWRlIHRoZSBmYWN0IHRoYXQgd2XigJlyZSBpbiBwaHJhc2luZywgYmVjYXVzZSBlc2NhcGVzIGRvbuKAmXQgd29yay5cbiAgICBjb25zdCBzdGFjayA9IHN0YXRlLnN0YWNrXG4gICAgc3RhdGUuc3RhY2sgPSBbXVxuICAgIGV4aXQgPSBzdGF0ZS5lbnRlcignYXV0b2xpbmsnKVxuICAgIGxldCB2YWx1ZSA9IHRyYWNrZXIubW92ZSgnPCcpXG4gICAgdmFsdWUgKz0gdHJhY2tlci5tb3ZlKFxuICAgICAgc3RhdGUuY29udGFpbmVyUGhyYXNpbmcobm9kZSwge1xuICAgICAgICBiZWZvcmU6IHZhbHVlLFxuICAgICAgICBhZnRlcjogJz4nLFxuICAgICAgICAuLi50cmFja2VyLmN1cnJlbnQoKVxuICAgICAgfSlcbiAgICApXG4gICAgdmFsdWUgKz0gdHJhY2tlci5tb3ZlKCc+JylcbiAgICBleGl0KClcbiAgICBzdGF0ZS5zdGFjayA9IHN0YWNrXG4gICAgcmV0dXJuIHZhbHVlXG4gIH1cblxuICBleGl0ID0gc3RhdGUuZW50ZXIoJ2xpbmsnKVxuICBzdWJleGl0ID0gc3RhdGUuZW50ZXIoJ2xhYmVsJylcbiAgbGV0IHZhbHVlID0gdHJhY2tlci5tb3ZlKCdbJylcbiAgdmFsdWUgKz0gdHJhY2tlci5tb3ZlKFxuICAgIHN0YXRlLmNvbnRhaW5lclBocmFzaW5nKG5vZGUsIHtcbiAgICAgIGJlZm9yZTogdmFsdWUsXG4gICAgICBhZnRlcjogJ10oJyxcbiAgICAgIC4uLnRyYWNrZXIuY3VycmVudCgpXG4gICAgfSlcbiAgKVxuICB2YWx1ZSArPSB0cmFja2VyLm1vdmUoJ10oJylcbiAgc3ViZXhpdCgpXG5cbiAgaWYgKFxuICAgIC8vIElmIHRoZXJl4oCZcyBubyB1cmwgYnV0IHRoZXJlIGlzIGEgdGl0bGXigKZcbiAgICAoIW5vZGUudXJsICYmIG5vZGUudGl0bGUpIHx8XG4gICAgLy8gSWYgdGhlcmUgYXJlIGNvbnRyb2wgY2hhcmFjdGVycyBvciB3aGl0ZXNwYWNlLlxuICAgIC9bXFwwLSBcXHUwMDdGXS8udGVzdChub2RlLnVybClcbiAgKSB7XG4gICAgc3ViZXhpdCA9IHN0YXRlLmVudGVyKCdkZXN0aW5hdGlvbkxpdGVyYWwnKVxuICAgIHZhbHVlICs9IHRyYWNrZXIubW92ZSgnPCcpXG4gICAgdmFsdWUgKz0gdHJhY2tlci5tb3ZlKFxuICAgICAgc3RhdGUuc2FmZShub2RlLnVybCwge2JlZm9yZTogdmFsdWUsIGFmdGVyOiAnPicsIC4uLnRyYWNrZXIuY3VycmVudCgpfSlcbiAgICApXG4gICAgdmFsdWUgKz0gdHJhY2tlci5tb3ZlKCc+JylcbiAgfSBlbHNlIHtcbiAgICAvLyBObyB3aGl0ZXNwYWNlLCByYXcgaXMgcHJldHRpZXIuXG4gICAgc3ViZXhpdCA9IHN0YXRlLmVudGVyKCdkZXN0aW5hdGlvblJhdycpXG4gICAgdmFsdWUgKz0gdHJhY2tlci5tb3ZlKFxuICAgICAgc3RhdGUuc2FmZShub2RlLnVybCwge1xuICAgICAgICBiZWZvcmU6IHZhbHVlLFxuICAgICAgICBhZnRlcjogbm9kZS50aXRsZSA/ICcgJyA6ICcpJyxcbiAgICAgICAgLi4udHJhY2tlci5jdXJyZW50KClcbiAgICAgIH0pXG4gICAgKVxuICB9XG5cbiAgc3ViZXhpdCgpXG5cbiAgaWYgKG5vZGUudGl0bGUpIHtcbiAgICBzdWJleGl0ID0gc3RhdGUuZW50ZXIoYHRpdGxlJHtzdWZmaXh9YClcbiAgICB2YWx1ZSArPSB0cmFja2VyLm1vdmUoJyAnICsgcXVvdGUpXG4gICAgdmFsdWUgKz0gdHJhY2tlci5tb3ZlKFxuICAgICAgc3RhdGUuc2FmZShub2RlLnRpdGxlLCB7XG4gICAgICAgIGJlZm9yZTogdmFsdWUsXG4gICAgICAgIGFmdGVyOiBxdW90ZSxcbiAgICAgICAgLi4udHJhY2tlci5jdXJyZW50KClcbiAgICAgIH0pXG4gICAgKVxuICAgIHZhbHVlICs9IHRyYWNrZXIubW92ZShxdW90ZSlcbiAgICBzdWJleGl0KClcbiAgfVxuXG4gIHZhbHVlICs9IHRyYWNrZXIubW92ZSgnKScpXG5cbiAgZXhpdCgpXG4gIHJldHVybiB2YWx1ZVxufVxuXG4vKipcbiAqIEBwYXJhbSB7TGlua30gbm9kZVxuICogQHBhcmFtIHtQYXJlbnQgfCB1bmRlZmluZWR9IF9cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5mdW5jdGlvbiBsaW5rUGVlayhub2RlLCBfLCBzdGF0ZSkge1xuICByZXR1cm4gZm9ybWF0TGlua0FzQXV0b2xpbmsobm9kZSwgc3RhdGUpID8gJzwnIDogJ1snXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/list-item.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   listItem: () => (/* binding */ listItem)\n/* harmony export */ });\n/* harmony import */ var _util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-bullet.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/* harmony import */ var _util_check_list_item_indent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-list-item-indent.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js\");\n/**\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('../types.js').Map} Map\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n\n\n\n/**\n * @param {ListItem} node\n * @param {Parent | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction listItem(node, parent, state, info) {\n  const listItemIndent = (0,_util_check_list_item_indent_js__WEBPACK_IMPORTED_MODULE_0__.checkListItemIndent)(state)\n  let bullet = state.bulletCurrent || (0,_util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__.checkBullet)(state)\n\n  // Add the marker value for ordered lists.\n  if (parent && parent.type === 'list' && parent.ordered) {\n    bullet =\n      (typeof parent.start === 'number' && parent.start > -1\n        ? parent.start\n        : 1) +\n      (state.options.incrementListMarker === false\n        ? 0\n        : parent.children.indexOf(node)) +\n      bullet\n  }\n\n  let size = bullet.length + 1\n\n  if (\n    listItemIndent === 'tab' ||\n    (listItemIndent === 'mixed' &&\n      ((parent && parent.type === 'list' && parent.spread) || node.spread))\n  ) {\n    size = Math.ceil(size / 4) * 4\n  }\n\n  const tracker = state.createTracker(info)\n  tracker.move(bullet + ' '.repeat(size - bullet.length))\n  tracker.shift(size)\n  const exit = state.enter('listItem')\n  const value = state.indentLines(\n    state.containerFlow(node, tracker.current()),\n    map\n  )\n  exit()\n\n  return value\n\n  /** @type {Map} */\n  function map(line, index, blank) {\n    if (index) {\n      return (blank ? '' : ' '.repeat(size)) + line\n    }\n\n    return (blank ? bullet : bullet + ' '.repeat(size - bullet.length)) + line\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/list.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   list: () => (/* binding */ list)\n/* harmony export */ });\n/* harmony import */ var _util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-bullet.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/* harmony import */ var _util_check_bullet_other_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/check-bullet-other.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js\");\n/* harmony import */ var _util_check_bullet_ordered_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-bullet-ordered.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js\");\n/* harmony import */ var _util_check_bullet_ordered_other_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/check-bullet-ordered-other.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered-other.js\");\n/* harmony import */ var _util_check_rule_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/check-rule.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\");\n/**\n * @typedef {import('mdast').List} List\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n\n\n\n\n\n\n/**\n * @param {List} node\n * @param {Parent | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction list(node, parent, state, info) {\n  const exit = state.enter('list')\n  const bulletCurrent = state.bulletCurrent\n  /** @type {string} */\n  let bullet = node.ordered ? (0,_util_check_bullet_ordered_js__WEBPACK_IMPORTED_MODULE_0__.checkBulletOrdered)(state) : (0,_util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__.checkBullet)(state)\n  /** @type {string} */\n  const bulletOther = node.ordered\n    ? (0,_util_check_bullet_ordered_other_js__WEBPACK_IMPORTED_MODULE_2__.checkBulletOrderedOther)(state)\n    : (0,_util_check_bullet_other_js__WEBPACK_IMPORTED_MODULE_3__.checkBulletOther)(state)\n  const bulletLastUsed = state.bulletLastUsed\n  let useDifferentMarker = false\n\n  if (\n    parent &&\n    // Explicit `other` set.\n    (node.ordered\n      ? state.options.bulletOrderedOther\n      : state.options.bulletOther) &&\n    bulletLastUsed &&\n    bullet === bulletLastUsed\n  ) {\n    useDifferentMarker = true\n  }\n\n  if (!node.ordered) {\n    const firstListItem = node.children ? node.children[0] : undefined\n\n    // If there’s an empty first list item directly in two list items,\n    // we have to use a different bullet:\n    //\n    // ```markdown\n    // * - *\n    // ```\n    //\n    // …because otherwise it would become one big thematic break.\n    if (\n      // Bullet could be used as a thematic break marker:\n      (bullet === '*' || bullet === '-') &&\n      // Empty first list item:\n      firstListItem &&\n      (!firstListItem.children || !firstListItem.children[0]) &&\n      // Directly in two other list items:\n      state.stack[state.stack.length - 1] === 'list' &&\n      state.stack[state.stack.length - 2] === 'listItem' &&\n      state.stack[state.stack.length - 3] === 'list' &&\n      state.stack[state.stack.length - 4] === 'listItem' &&\n      // That are each the first child.\n      state.indexStack[state.indexStack.length - 1] === 0 &&\n      state.indexStack[state.indexStack.length - 2] === 0 &&\n      state.indexStack[state.indexStack.length - 3] === 0\n    ) {\n      useDifferentMarker = true\n    }\n\n    // If there’s a thematic break at the start of the first list item,\n    // we have to use a different bullet:\n    //\n    // ```markdown\n    // * ---\n    // ```\n    //\n    // …because otherwise it would become one big thematic break.\n    if ((0,_util_check_rule_js__WEBPACK_IMPORTED_MODULE_4__.checkRule)(state) === bullet && firstListItem) {\n      let index = -1\n\n      while (++index < node.children.length) {\n        const item = node.children[index]\n\n        if (\n          item &&\n          item.type === 'listItem' &&\n          item.children &&\n          item.children[0] &&\n          item.children[0].type === 'thematicBreak'\n        ) {\n          useDifferentMarker = true\n          break\n        }\n      }\n    }\n  }\n\n  if (useDifferentMarker) {\n    bullet = bulletOther\n  }\n\n  state.bulletCurrent = bullet\n  const value = state.containerFlow(node, info)\n  state.bulletLastUsed = bullet\n  state.bulletCurrent = bulletCurrent\n  exit()\n  return value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   paragraph: () => (/* binding */ paragraph)\n/* harmony export */ });\n/**\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n/**\n * @param {Paragraph} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction paragraph(node, _, state, info) {\n  const exit = state.enter('paragraph')\n  const subexit = state.enter('phrasing')\n  const value = state.containerPhrasing(node, info)\n  subexit()\n  exit()\n  return value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3BhcmFncmFwaC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLDJCQUEyQjtBQUN4QyxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLDZCQUE2QjtBQUMxQyxhQUFhLDRCQUE0QjtBQUN6Qzs7QUFFQTtBQUNBLFdBQVcsV0FBVztBQUN0QixXQUFXLG9CQUFvQjtBQUMvQixXQUFXLE9BQU87QUFDbEIsV0FBVyxNQUFNO0FBQ2pCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL2hhbmRsZS9wYXJhZ3JhcGguanM/NGM3ZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuUGFyYWdyYXBofSBQYXJhZ3JhcGhcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuUGFyZW50fSBQYXJlbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuU3RhdGV9IFN0YXRlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkluZm99IEluZm9cbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7UGFyYWdyYXBofSBub2RlXG4gKiBAcGFyYW0ge1BhcmVudCB8IHVuZGVmaW5lZH0gX1xuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEBwYXJhbSB7SW5mb30gaW5mb1xuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHBhcmFncmFwaChub2RlLCBfLCBzdGF0ZSwgaW5mbykge1xuICBjb25zdCBleGl0ID0gc3RhdGUuZW50ZXIoJ3BhcmFncmFwaCcpXG4gIGNvbnN0IHN1YmV4aXQgPSBzdGF0ZS5lbnRlcigncGhyYXNpbmcnKVxuICBjb25zdCB2YWx1ZSA9IHN0YXRlLmNvbnRhaW5lclBocmFzaW5nKG5vZGUsIGluZm8pXG4gIHN1YmV4aXQoKVxuICBleGl0KClcbiAgcmV0dXJuIHZhbHVlXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/root.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/root.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: () => (/* binding */ root)\n/* harmony export */ });\n/* harmony import */ var mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-phrasing */ \"(ssr)/./node_modules/mdast-util-phrasing/lib/index.js\");\n/**\n * @typedef {import('mdast').Root} Root\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n\n\n/**\n * @param {Root} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction root(node, _, state, info) {\n  // Note: `html` nodes are ambiguous.\n  const hasPhrasing = node.children.some((d) => (0,mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_0__.phrasing)(d))\n  const fn = hasPhrasing ? state.containerPhrasing : state.containerFlow\n  // @ts-expect-error: `root`s are supposed to have one type of content\n  return fn.call(state, node, info)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3Jvb3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEsc0JBQXNCO0FBQ25DLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsNkJBQTZCO0FBQzFDLGFBQWEsNEJBQTRCO0FBQ3pDOztBQUU0Qzs7QUFFNUM7QUFDQSxXQUFXLE1BQU07QUFDakIsV0FBVyxvQkFBb0I7QUFDL0IsV0FBVyxPQUFPO0FBQ2xCLFdBQVcsTUFBTTtBQUNqQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0EsZ0RBQWdELDZEQUFRO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvcm9vdC5qcz80NGYyIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5Sb290fSBSb290XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlBhcmVudH0gUGFyZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlN0YXRlfSBTdGF0ZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5JbmZvfSBJbmZvXG4gKi9cblxuaW1wb3J0IHtwaHJhc2luZ30gZnJvbSAnbWRhc3QtdXRpbC1waHJhc2luZydcblxuLyoqXG4gKiBAcGFyYW0ge1Jvb3R9IG5vZGVcbiAqIEBwYXJhbSB7UGFyZW50IHwgdW5kZWZpbmVkfSBfXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHBhcmFtIHtJbmZvfSBpbmZvXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gcm9vdChub2RlLCBfLCBzdGF0ZSwgaW5mbykge1xuICAvLyBOb3RlOiBgaHRtbGAgbm9kZXMgYXJlIGFtYmlndW91cy5cbiAgY29uc3QgaGFzUGhyYXNpbmcgPSBub2RlLmNoaWxkcmVuLnNvbWUoKGQpID0+IHBocmFzaW5nKGQpKVxuICBjb25zdCBmbiA9IGhhc1BocmFzaW5nID8gc3RhdGUuY29udGFpbmVyUGhyYXNpbmcgOiBzdGF0ZS5jb250YWluZXJGbG93XG4gIC8vIEB0cy1leHBlY3QtZXJyb3I6IGByb290YHMgYXJlIHN1cHBvc2VkIHRvIGhhdmUgb25lIHR5cGUgb2YgY29udGVudFxuICByZXR1cm4gZm4uY2FsbChzdGF0ZSwgbm9kZSwgaW5mbylcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/root.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js":
/*!******************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/strong.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strong: () => (/* binding */ strong)\n/* harmony export */ });\n/* harmony import */ var _util_check_strong_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-strong.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js\");\n/**\n * @typedef {import('mdast').Strong} Strong\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n\n\nstrong.peek = strongPeek\n\n// To do: there are cases where emphasis cannot “form” depending on the\n// previous or next character of sequences.\n// There’s no way around that though, except for injecting zero-width stuff.\n// Do we need to safeguard against that?\n/**\n * @param {Strong} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction strong(node, _, state, info) {\n  const marker = (0,_util_check_strong_js__WEBPACK_IMPORTED_MODULE_0__.checkStrong)(state)\n  const exit = state.enter('strong')\n  const tracker = state.createTracker(info)\n  let value = tracker.move(marker + marker)\n  value += tracker.move(\n    state.containerPhrasing(node, {\n      before: value,\n      after: marker,\n      ...tracker.current()\n    })\n  )\n  value += tracker.move(marker + marker)\n  exit()\n  return value\n}\n\n/**\n * @param {Strong} _\n * @param {Parent | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nfunction strongPeek(_, _1, state) {\n  return state.options.strong || '*'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/text.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/text.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/**\n * @typedef {import('mdast').Text} Text\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n/**\n * @param {Text} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction text(node, _, state, info) {\n  return state.safe(node.value, info)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3RleHQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSxzQkFBc0I7QUFDbkMsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSw2QkFBNkI7QUFDMUMsYUFBYSw0QkFBNEI7QUFDekM7O0FBRUE7QUFDQSxXQUFXLE1BQU07QUFDakIsV0FBVyxvQkFBb0I7QUFDL0IsV0FBVyxPQUFPO0FBQ2xCLFdBQVcsTUFBTTtBQUNqQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL2hhbmRsZS90ZXh0LmpzPzE0NDMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLlRleHR9IFRleHRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuUGFyZW50fSBQYXJlbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuU3RhdGV9IFN0YXRlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkluZm99IEluZm9cbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7VGV4dH0gbm9kZVxuICogQHBhcmFtIHtQYXJlbnQgfCB1bmRlZmluZWR9IF9cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcGFyYW0ge0luZm99IGluZm9cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0ZXh0KG5vZGUsIF8sIHN0YXRlLCBpbmZvKSB7XG4gIHJldHVybiBzdGF0ZS5zYWZlKG5vZGUudmFsdWUsIGluZm8pXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   thematicBreak: () => (/* binding */ thematicBreak)\n/* harmony export */ });\n/* harmony import */ var _util_check_rule_repetition_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-rule-repetition.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js\");\n/* harmony import */ var _util_check_rule_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-rule.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\");\n/**\n * @typedef {import('mdast').ThematicBreak} ThematicBreak\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n */\n\n\n\n\n/**\n * @param {ThematicBreak} _\n * @param {Parent | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nfunction thematicBreak(_, _1, state) {\n  const value = (\n    (0,_util_check_rule_js__WEBPACK_IMPORTED_MODULE_0__.checkRule)(state) + (state.options.ruleSpaces ? ' ' : '')\n  ).repeat((0,_util_check_rule_repetition_js__WEBPACK_IMPORTED_MODULE_1__.checkRuleRepetition)(state))\n\n  return state.options.ruleSpaces ? value.slice(0, -1) : value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3RoZW1hdGljLWJyZWFrLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0EsYUFBYSwrQkFBK0I7QUFDNUMsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSw2QkFBNkI7QUFDMUM7O0FBRW9FO0FBQ3JCOztBQUUvQztBQUNBLFdBQVcsZUFBZTtBQUMxQixXQUFXLG9CQUFvQjtBQUMvQixXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ087QUFDUDtBQUNBLElBQUksOERBQVM7QUFDYixXQUFXLG1GQUFtQjs7QUFFOUI7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3RoZW1hdGljLWJyZWFrLmpzP2EwMTIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLlRoZW1hdGljQnJlYWt9IFRoZW1hdGljQnJlYWtcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuUGFyZW50fSBQYXJlbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuaW1wb3J0IHtjaGVja1J1bGVSZXBldGl0aW9ufSBmcm9tICcuLi91dGlsL2NoZWNrLXJ1bGUtcmVwZXRpdGlvbi5qcydcbmltcG9ydCB7Y2hlY2tSdWxlfSBmcm9tICcuLi91dGlsL2NoZWNrLXJ1bGUuanMnXG5cbi8qKlxuICogQHBhcmFtIHtUaGVtYXRpY0JyZWFrfSBfXG4gKiBAcGFyYW0ge1BhcmVudCB8IHVuZGVmaW5lZH0gXzFcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gdGhlbWF0aWNCcmVhayhfLCBfMSwgc3RhdGUpIHtcbiAgY29uc3QgdmFsdWUgPSAoXG4gICAgY2hlY2tSdWxlKHN0YXRlKSArIChzdGF0ZS5vcHRpb25zLnJ1bGVTcGFjZXMgPyAnICcgOiAnJylcbiAgKS5yZXBlYXQoY2hlY2tSdWxlUmVwZXRpdGlvbihzdGF0ZSkpXG5cbiAgcmV0dXJuIHN0YXRlLm9wdGlvbnMucnVsZVNwYWNlcyA/IHZhbHVlLnNsaWNlKDAsIC0xKSA6IHZhbHVlXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toMarkdown: () => (/* binding */ toMarkdown)\n/* harmony export */ });\n/* harmony import */ var zwitch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! zwitch */ \"(ssr)/./node_modules/zwitch/index.js\");\n/* harmony import */ var _configure_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./configure.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/configure.js\");\n/* harmony import */ var _handle_index_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./handle/index.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/index.js\");\n/* harmony import */ var _join_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./join.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/join.js\");\n/* harmony import */ var _unsafe_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./unsafe.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/unsafe.js\");\n/* harmony import */ var _util_association_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/association.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/association.js\");\n/* harmony import */ var _util_container_phrasing_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./util/container-phrasing.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js\");\n/* harmony import */ var _util_container_flow_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./util/container-flow.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/container-flow.js\");\n/* harmony import */ var _util_indent_lines_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/indent-lines.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/indent-lines.js\");\n/* harmony import */ var _util_safe_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./util/safe.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/safe.js\");\n/* harmony import */ var _util_track_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/track.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/track.js\");\n/**\n * @typedef {import('./types.js').Enter} Enter\n * @typedef {import('./types.js').Info} Info\n * @typedef {import('./types.js').Join} Join\n * @typedef {import('./types.js').FlowContent} FlowContent\n * @typedef {import('./types.js').Node} Node\n * @typedef {import('./types.js').Options} Options\n * @typedef {import('./types.js').Parent} Parent\n * @typedef {import('./types.js').PhrasingContent} PhrasingContent\n * @typedef {import('./types.js').SafeConfig} SafeConfig\n * @typedef {import('./types.js').State} State\n * @typedef {import('./types.js').TrackFields} TrackFields\n */\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Turn an mdast syntax tree into markdown.\n *\n * @param {Node} tree\n *   Tree to serialize.\n * @param {Options} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized markdown representing `tree`.\n */\nfunction toMarkdown(tree, options = {}) {\n  /** @type {State} */\n  const state = {\n    enter,\n    indentLines: _util_indent_lines_js__WEBPACK_IMPORTED_MODULE_0__.indentLines,\n    associationId: _util_association_js__WEBPACK_IMPORTED_MODULE_1__.association,\n    containerPhrasing: containerPhrasingBound,\n    containerFlow: containerFlowBound,\n    createTracker: _util_track_js__WEBPACK_IMPORTED_MODULE_2__.track,\n    safe: safeBound,\n    stack: [],\n    unsafe: [],\n    join: [],\n    // @ts-expect-error: we’ll fill it next.\n    handlers: {},\n    options: {},\n    indexStack: [],\n    // @ts-expect-error: we’ll add `handle` later.\n    handle: undefined\n  }\n\n  ;(0,_configure_js__WEBPACK_IMPORTED_MODULE_3__.configure)(state, {unsafe: _unsafe_js__WEBPACK_IMPORTED_MODULE_4__.unsafe, join: _join_js__WEBPACK_IMPORTED_MODULE_5__.join, handlers: _handle_index_js__WEBPACK_IMPORTED_MODULE_6__.handle})\n  ;(0,_configure_js__WEBPACK_IMPORTED_MODULE_3__.configure)(state, options)\n\n  if (state.options.tightDefinitions) {\n    (0,_configure_js__WEBPACK_IMPORTED_MODULE_3__.configure)(state, {join: [joinDefinition]})\n  }\n\n  state.handle = (0,zwitch__WEBPACK_IMPORTED_MODULE_7__.zwitch)('type', {\n    invalid,\n    unknown,\n    handlers: state.handlers\n  })\n\n  let result = state.handle(tree, undefined, state, {\n    before: '\\n',\n    after: '\\n',\n    now: {line: 1, column: 1},\n    lineShift: 0\n  })\n\n  if (\n    result &&\n    result.charCodeAt(result.length - 1) !== 10 &&\n    result.charCodeAt(result.length - 1) !== 13\n  ) {\n    result += '\\n'\n  }\n\n  return result\n\n  /** @type {Enter} */\n  function enter(name) {\n    state.stack.push(name)\n    return exit\n\n    function exit() {\n      state.stack.pop()\n    }\n  }\n}\n\n/**\n * @param {unknown} value\n * @returns {never}\n */\nfunction invalid(value) {\n  throw new Error('Cannot handle value `' + value + '`, expected node')\n}\n\n/**\n * @param {unknown} node\n * @returns {never}\n */\nfunction unknown(node) {\n  // @ts-expect-error: fine.\n  throw new Error('Cannot handle unknown node `' + node.type + '`')\n}\n\n/** @type {Join} */\nfunction joinDefinition(left, right) {\n  // No blank line between adjacent definitions.\n  if (left.type === 'definition' && left.type === right.type) {\n    return 0\n  }\n}\n\n/**\n * Serialize the children of a parent that contains phrasing children.\n *\n * These children will be joined flush together.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Parent & {children: Array<PhrasingContent>}} parent\n *   Parent of flow nodes.\n * @param {Info} info\n *   Info on where we are in the document we are generating.\n * @returns {string}\n *   Serialized children, joined together.\n */\nfunction containerPhrasingBound(parent, info) {\n  return (0,_util_container_phrasing_js__WEBPACK_IMPORTED_MODULE_8__.containerPhrasing)(parent, this, info)\n}\n\n/**\n * Serialize the children of a parent that contains flow children.\n *\n * These children will typically be joined by blank lines.\n * What they are joined by exactly is defined by `Join` functions.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Parent & {children: Array<FlowContent>}} parent\n *   Parent of flow nodes.\n * @param {TrackFields} info\n *   Info on where we are in the document we are generating.\n * @returns {string}\n *   Serialized children, joined by (blank) lines.\n */\nfunction containerFlowBound(parent, info) {\n  return (0,_util_container_flow_js__WEBPACK_IMPORTED_MODULE_9__.containerFlow)(parent, this, info)\n}\n\n/**\n * Make a string safe for embedding in markdown constructs.\n *\n * In markdown, almost all punctuation characters can, in certain cases,\n * result in something.\n * Whether they do is highly subjective to where they happen and in what\n * they happen.\n *\n * To solve this, `mdast-util-to-markdown` tracks:\n *\n * * Characters before and after something;\n * * What “constructs” we are in.\n *\n * This information is then used by this function to escape or encode\n * special characters.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {string | null | undefined} value\n *   Raw value to make safe.\n * @param {SafeConfig} config\n *   Configuration.\n * @returns {string}\n *   Serialized markdown safe for embedding.\n */\nfunction safeBound(value, config) {\n  return (0,_util_safe_js__WEBPACK_IMPORTED_MODULE_10__.safe)(this, value, config)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/join.js":
/*!*********************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/join.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   join: () => (/* binding */ join)\n/* harmony export */ });\n/* harmony import */ var _util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/format-code-as-indented.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js\");\n/* harmony import */ var _util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/format-heading-as-setext.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js\");\n/**\n * @typedef {import('./types.js').Join} Join\n */\n\n\n\n\n/** @type {Array<Join>} */\nconst join = [joinDefaults]\n\n/** @type {Join} */\nfunction joinDefaults(left, right, parent, state) {\n  // Indented code after list or another indented code.\n  if (\n    right.type === 'code' &&\n    (0,_util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_0__.formatCodeAsIndented)(right, state) &&\n    (left.type === 'list' ||\n      (left.type === right.type && (0,_util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_0__.formatCodeAsIndented)(left, state)))\n  ) {\n    return false\n  }\n\n  // Two lists with the same marker.\n  if (\n    left.type === 'list' &&\n    left.type === right.type &&\n    Boolean(left.ordered) === Boolean(right.ordered) &&\n    !(left.ordered\n      ? state.options.bulletOrderedOther\n      : state.options.bulletOther)\n  ) {\n    return false\n  }\n\n  // Join children of a list or an item.\n  // In which case, `parent` has a `spread` field.\n  if ('spread' in parent && typeof parent.spread === 'boolean') {\n    if (\n      left.type === 'paragraph' &&\n      // Two paragraphs.\n      (left.type === right.type ||\n        right.type === 'definition' ||\n        // Paragraph followed by a setext heading.\n        (right.type === 'heading' && (0,_util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_1__.formatHeadingAsSetext)(right, state)))\n    ) {\n      return\n    }\n\n    return parent.spread ? 1 : 0\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvam9pbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBLGFBQWEsMkJBQTJCO0FBQ3hDOztBQUVzRTtBQUNFOztBQUV4RSxXQUFXLGFBQWE7QUFDakI7O0FBRVAsV0FBVyxNQUFNO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSxzRkFBb0I7QUFDeEI7QUFDQSxtQ0FBbUMsc0ZBQW9CO0FBQ3ZEO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyx3RkFBcUI7QUFDMUQ7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvam9pbi5qcz82NGQ2Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi90eXBlcy5qcycpLkpvaW59IEpvaW5cbiAqL1xuXG5pbXBvcnQge2Zvcm1hdENvZGVBc0luZGVudGVkfSBmcm9tICcuL3V0aWwvZm9ybWF0LWNvZGUtYXMtaW5kZW50ZWQuanMnXG5pbXBvcnQge2Zvcm1hdEhlYWRpbmdBc1NldGV4dH0gZnJvbSAnLi91dGlsL2Zvcm1hdC1oZWFkaW5nLWFzLXNldGV4dC5qcydcblxuLyoqIEB0eXBlIHtBcnJheTxKb2luPn0gKi9cbmV4cG9ydCBjb25zdCBqb2luID0gW2pvaW5EZWZhdWx0c11cblxuLyoqIEB0eXBlIHtKb2lufSAqL1xuZnVuY3Rpb24gam9pbkRlZmF1bHRzKGxlZnQsIHJpZ2h0LCBwYXJlbnQsIHN0YXRlKSB7XG4gIC8vIEluZGVudGVkIGNvZGUgYWZ0ZXIgbGlzdCBvciBhbm90aGVyIGluZGVudGVkIGNvZGUuXG4gIGlmIChcbiAgICByaWdodC50eXBlID09PSAnY29kZScgJiZcbiAgICBmb3JtYXRDb2RlQXNJbmRlbnRlZChyaWdodCwgc3RhdGUpICYmXG4gICAgKGxlZnQudHlwZSA9PT0gJ2xpc3QnIHx8XG4gICAgICAobGVmdC50eXBlID09PSByaWdodC50eXBlICYmIGZvcm1hdENvZGVBc0luZGVudGVkKGxlZnQsIHN0YXRlKSkpXG4gICkge1xuICAgIHJldHVybiBmYWxzZVxuICB9XG5cbiAgLy8gVHdvIGxpc3RzIHdpdGggdGhlIHNhbWUgbWFya2VyLlxuICBpZiAoXG4gICAgbGVmdC50eXBlID09PSAnbGlzdCcgJiZcbiAgICBsZWZ0LnR5cGUgPT09IHJpZ2h0LnR5cGUgJiZcbiAgICBCb29sZWFuKGxlZnQub3JkZXJlZCkgPT09IEJvb2xlYW4ocmlnaHQub3JkZXJlZCkgJiZcbiAgICAhKGxlZnQub3JkZXJlZFxuICAgICAgPyBzdGF0ZS5vcHRpb25zLmJ1bGxldE9yZGVyZWRPdGhlclxuICAgICAgOiBzdGF0ZS5vcHRpb25zLmJ1bGxldE90aGVyKVxuICApIHtcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxuXG4gIC8vIEpvaW4gY2hpbGRyZW4gb2YgYSBsaXN0IG9yIGFuIGl0ZW0uXG4gIC8vIEluIHdoaWNoIGNhc2UsIGBwYXJlbnRgIGhhcyBhIGBzcHJlYWRgIGZpZWxkLlxuICBpZiAoJ3NwcmVhZCcgaW4gcGFyZW50ICYmIHR5cGVvZiBwYXJlbnQuc3ByZWFkID09PSAnYm9vbGVhbicpIHtcbiAgICBpZiAoXG4gICAgICBsZWZ0LnR5cGUgPT09ICdwYXJhZ3JhcGgnICYmXG4gICAgICAvLyBUd28gcGFyYWdyYXBocy5cbiAgICAgIChsZWZ0LnR5cGUgPT09IHJpZ2h0LnR5cGUgfHxcbiAgICAgICAgcmlnaHQudHlwZSA9PT0gJ2RlZmluaXRpb24nIHx8XG4gICAgICAgIC8vIFBhcmFncmFwaCBmb2xsb3dlZCBieSBhIHNldGV4dCBoZWFkaW5nLlxuICAgICAgICAocmlnaHQudHlwZSA9PT0gJ2hlYWRpbmcnICYmIGZvcm1hdEhlYWRpbmdBc1NldGV4dChyaWdodCwgc3RhdGUpKSlcbiAgICApIHtcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIHJldHVybiBwYXJlbnQuc3ByZWFkID8gMSA6IDBcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/join.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/unsafe.js":
/*!***********************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/unsafe.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unsafe: () => (/* binding */ unsafe)\n/* harmony export */ });\n/**\n * @typedef {import('./types.js').Unsafe} Unsafe\n * @typedef {import('./types.js').ConstructName} ConstructName\n */\n\n/**\n * List of constructs that occur in phrasing (paragraphs, headings), but cannot\n * contain things like attention (emphasis, strong), images, or links.\n * So they sort of cancel each other out.\n * Note: could use a better name.\n *\n * @type {Array<ConstructName>}\n */\nconst fullPhrasingSpans = [\n  'autolink',\n  'destinationLiteral',\n  'destinationRaw',\n  'reference',\n  'titleQuote',\n  'titleApostrophe'\n]\n\n/** @type {Array<Unsafe>} */\nconst unsafe = [\n  {character: '\\t', after: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  {character: '\\t', before: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  {\n    character: '\\t',\n    inConstruct: ['codeFencedLangGraveAccent', 'codeFencedLangTilde']\n  },\n  {\n    character: '\\r',\n    inConstruct: [\n      'codeFencedLangGraveAccent',\n      'codeFencedLangTilde',\n      'codeFencedMetaGraveAccent',\n      'codeFencedMetaTilde',\n      'destinationLiteral',\n      'headingAtx'\n    ]\n  },\n  {\n    character: '\\n',\n    inConstruct: [\n      'codeFencedLangGraveAccent',\n      'codeFencedLangTilde',\n      'codeFencedMetaGraveAccent',\n      'codeFencedMetaTilde',\n      'destinationLiteral',\n      'headingAtx'\n    ]\n  },\n  {character: ' ', after: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  {character: ' ', before: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  {\n    character: ' ',\n    inConstruct: ['codeFencedLangGraveAccent', 'codeFencedLangTilde']\n  },\n  // An exclamation mark can start an image, if it is followed by a link or\n  // a link reference.\n  {\n    character: '!',\n    after: '\\\\[',\n    inConstruct: 'phrasing',\n    notInConstruct: fullPhrasingSpans\n  },\n  // A quote can break out of a title.\n  {character: '\"', inConstruct: 'titleQuote'},\n  // A number sign could start an ATX heading if it starts a line.\n  {atBreak: true, character: '#'},\n  {character: '#', inConstruct: 'headingAtx', after: '(?:[\\r\\n]|$)'},\n  // Dollar sign and percentage are not used in markdown.\n  // An ampersand could start a character reference.\n  {character: '&', after: '[#A-Za-z]', inConstruct: 'phrasing'},\n  // An apostrophe can break out of a title.\n  {character: \"'\", inConstruct: 'titleApostrophe'},\n  // A left paren could break out of a destination raw.\n  {character: '(', inConstruct: 'destinationRaw'},\n  // A left paren followed by `]` could make something into a link or image.\n  {\n    before: '\\\\]',\n    character: '(',\n    inConstruct: 'phrasing',\n    notInConstruct: fullPhrasingSpans\n  },\n  // A right paren could start a list item or break out of a destination\n  // raw.\n  {atBreak: true, before: '\\\\d+', character: ')'},\n  {character: ')', inConstruct: 'destinationRaw'},\n  // An asterisk can start thematic breaks, list items, emphasis, strong.\n  {atBreak: true, character: '*', after: '(?:[ \\t\\r\\n*])'},\n  {character: '*', inConstruct: 'phrasing', notInConstruct: fullPhrasingSpans},\n  // A plus sign could start a list item.\n  {atBreak: true, character: '+', after: '(?:[ \\t\\r\\n])'},\n  // A dash can start thematic breaks, list items, and setext heading\n  // underlines.\n  {atBreak: true, character: '-', after: '(?:[ \\t\\r\\n-])'},\n  // A dot could start a list item.\n  {atBreak: true, before: '\\\\d+', character: '.', after: '(?:[ \\t\\r\\n]|$)'},\n  // Slash, colon, and semicolon are not used in markdown for constructs.\n  // A less than can start html (flow or text) or an autolink.\n  // HTML could start with an exclamation mark (declaration, cdata, comment),\n  // slash (closing tag), question mark (instruction), or a letter (tag).\n  // An autolink also starts with a letter.\n  // Finally, it could break out of a destination literal.\n  {atBreak: true, character: '<', after: '[!/?A-Za-z]'},\n  {\n    character: '<',\n    after: '[!/?A-Za-z]',\n    inConstruct: 'phrasing',\n    notInConstruct: fullPhrasingSpans\n  },\n  {character: '<', inConstruct: 'destinationLiteral'},\n  // An equals to can start setext heading underlines.\n  {atBreak: true, character: '='},\n  // A greater than can start block quotes and it can break out of a\n  // destination literal.\n  {atBreak: true, character: '>'},\n  {character: '>', inConstruct: 'destinationLiteral'},\n  // Question mark and at sign are not used in markdown for constructs.\n  // A left bracket can start definitions, references, labels,\n  {atBreak: true, character: '['},\n  {character: '[', inConstruct: 'phrasing', notInConstruct: fullPhrasingSpans},\n  {character: '[', inConstruct: ['label', 'reference']},\n  // A backslash can start an escape (when followed by punctuation) or a\n  // hard break (when followed by an eol).\n  // Note: typical escapes are handled in `safe`!\n  {character: '\\\\', after: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  // A right bracket can exit labels.\n  {character: ']', inConstruct: ['label', 'reference']},\n  // Caret is not used in markdown for constructs.\n  // An underscore can start emphasis, strong, or a thematic break.\n  {atBreak: true, character: '_'},\n  {character: '_', inConstruct: 'phrasing', notInConstruct: fullPhrasingSpans},\n  // A grave accent can start code (fenced or text), or it can break out of\n  // a grave accent code fence.\n  {atBreak: true, character: '`'},\n  {\n    character: '`',\n    inConstruct: ['codeFencedLangGraveAccent', 'codeFencedMetaGraveAccent']\n  },\n  {character: '`', inConstruct: 'phrasing', notInConstruct: fullPhrasingSpans},\n  // Left brace, vertical bar, right brace are not used in markdown for\n  // constructs.\n  // A tilde can start code (fenced).\n  {atBreak: true, character: '~'}\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/unsafe.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/association.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/association.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   association: () => (/* binding */ association)\n/* harmony export */ });\n/* harmony import */ var micromark_util_decode_string__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-decode-string */ \"(ssr)/./node_modules/micromark-util-decode-string/dev/index.js\");\n/**\n * @typedef {import('../types.js').AssociationId} AssociationId\n */\n\n\n\n/**\n * Get an identifier from an association to match it to others.\n *\n * Associations are nodes that match to something else through an ID:\n * <https://github.com/syntax-tree/mdast#association>.\n *\n * The `label` of an association is the string value: character escapes and\n * references work, and casing is intact.\n * The `identifier` is used to match one association to another:\n * controversially, character escapes and references don’t work in this\n * matching: `&copy;` does not match `©`, and `\\+` does not match `+`.\n *\n * But casing is ignored (and whitespace) is trimmed and collapsed: ` A\\nb`\n * matches `a b`.\n * So, we do prefer the label when figuring out how we’re going to serialize:\n * it has whitespace, casing, and we can ignore most useless character\n * escapes and all character references.\n *\n * @type {AssociationId}\n */\nfunction association(node) {\n  if (node.label || !node.identifier) {\n    return node.label || ''\n  }\n\n  return (0,micromark_util_decode_string__WEBPACK_IMPORTED_MODULE_0__.decodeString)(node.identifier)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/association.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered-other.js":
/*!************************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered-other.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBulletOrderedOther: () => (/* binding */ checkBulletOrderedOther)\n/* harmony export */ });\n/* harmony import */ var _check_bullet_ordered_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./check-bullet-ordered.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js\");\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bulletOrdered'], null | undefined>}\n */\nfunction checkBulletOrderedOther(state) {\n  const bulletOrdered = (0,_check_bullet_ordered_js__WEBPACK_IMPORTED_MODULE_0__.checkBulletOrdered)(state)\n  const bulletOrderedOther = state.options.bulletOrderedOther\n\n  if (!bulletOrderedOther) {\n    return bulletOrdered === '.' ? ')' : '.'\n  }\n\n  if (bulletOrderedOther !== '.' && bulletOrderedOther !== ')') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        bulletOrderedOther +\n        '` for `options.bulletOrderedOther`, expected `*`, `+`, or `-`'\n    )\n  }\n\n  if (bulletOrderedOther === bulletOrdered) {\n    throw new Error(\n      'Expected `bulletOrdered` (`' +\n        bulletOrdered +\n        '`) and `bulletOrderedOther` (`' +\n        bulletOrderedOther +\n        '`) to be different'\n    )\n  }\n\n  return bulletOrderedOther\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered-other.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js":
/*!******************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBulletOrdered: () => (/* binding */ checkBulletOrdered)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bulletOrdered'], null | undefined>}\n */\nfunction checkBulletOrdered(state) {\n  const marker = state.options.bulletOrdered || '.'\n\n  if (marker !== '.' && marker !== ')') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        marker +\n        '` for `options.bulletOrdered`, expected `.` or `)`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQtb3JkZXJlZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLDZCQUE2QjtBQUMxQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stYnVsbGV0LW9yZGVyZWQuanM/MTdhNCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuU3RhdGV9IFN0YXRlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLk9wdGlvbnN9IE9wdGlvbnNcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydidWxsZXRPcmRlcmVkJ10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tCdWxsZXRPcmRlcmVkKHN0YXRlKSB7XG4gIGNvbnN0IG1hcmtlciA9IHN0YXRlLm9wdGlvbnMuYnVsbGV0T3JkZXJlZCB8fCAnLidcblxuICBpZiAobWFya2VyICE9PSAnLicgJiYgbWFya2VyICE9PSAnKScpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBpdGVtcyB3aXRoIGAnICtcbiAgICAgICAgbWFya2VyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLmJ1bGxldE9yZGVyZWRgLCBleHBlY3RlZCBgLmAgb3IgYClgJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBtYXJrZXJcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js":
/*!****************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBulletOther: () => (/* binding */ checkBulletOther)\n/* harmony export */ });\n/* harmony import */ var _check_bullet_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./check-bullet.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */\nfunction checkBulletOther(state) {\n  const bullet = (0,_check_bullet_js__WEBPACK_IMPORTED_MODULE_0__.checkBullet)(state)\n  const bulletOther = state.options.bulletOther\n\n  if (!bulletOther) {\n    return bullet === '*' ? '-' : '*'\n  }\n\n  if (bulletOther !== '*' && bulletOther !== '+' && bulletOther !== '-') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        bulletOther +\n        '` for `options.bulletOther`, expected `*`, `+`, or `-`'\n    )\n  }\n\n  if (bulletOther === bullet) {\n    throw new Error(\n      'Expected `bullet` (`' +\n        bullet +\n        '`) and `bulletOther` (`' +\n        bulletOther +\n        '`) to be different'\n    )\n  }\n\n  return bulletOther\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQtb3RoZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEsNkJBQTZCO0FBQzFDLGFBQWEsK0JBQStCO0FBQzVDOztBQUU2Qzs7QUFFN0M7QUFDQSxXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ087QUFDUCxpQkFBaUIsNkRBQVc7QUFDNUI7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLWJ1bGxldC1vdGhlci5qcz8yMzNkIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5TdGF0ZX0gU3RhdGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuT3B0aW9uc30gT3B0aW9uc1xuICovXG5cbmltcG9ydCB7Y2hlY2tCdWxsZXR9IGZyb20gJy4vY2hlY2stYnVsbGV0LmpzJ1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydidWxsZXQnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja0J1bGxldE90aGVyKHN0YXRlKSB7XG4gIGNvbnN0IGJ1bGxldCA9IGNoZWNrQnVsbGV0KHN0YXRlKVxuICBjb25zdCBidWxsZXRPdGhlciA9IHN0YXRlLm9wdGlvbnMuYnVsbGV0T3RoZXJcblxuICBpZiAoIWJ1bGxldE90aGVyKSB7XG4gICAgcmV0dXJuIGJ1bGxldCA9PT0gJyonID8gJy0nIDogJyonXG4gIH1cblxuICBpZiAoYnVsbGV0T3RoZXIgIT09ICcqJyAmJiBidWxsZXRPdGhlciAhPT0gJysnICYmIGJ1bGxldE90aGVyICE9PSAnLScpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBpdGVtcyB3aXRoIGAnICtcbiAgICAgICAgYnVsbGV0T3RoZXIgK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMuYnVsbGV0T3RoZXJgLCBleHBlY3RlZCBgKmAsIGArYCwgb3IgYC1gJ1xuICAgIClcbiAgfVxuXG4gIGlmIChidWxsZXRPdGhlciA9PT0gYnVsbGV0KSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0V4cGVjdGVkIGBidWxsZXRgIChgJyArXG4gICAgICAgIGJ1bGxldCArXG4gICAgICAgICdgKSBhbmQgYGJ1bGxldE90aGVyYCAoYCcgK1xuICAgICAgICBidWxsZXRPdGhlciArXG4gICAgICAgICdgKSB0byBiZSBkaWZmZXJlbnQnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIGJ1bGxldE90aGVyXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBullet: () => (/* binding */ checkBullet)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */\nfunction checkBullet(state) {\n  const marker = state.options.bullet || '*'\n\n  if (marker !== '*' && marker !== '+' && marker !== '-') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        marker +\n        '` for `options.bullet`, expected `*`, `+`, or `-`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSw2QkFBNkI7QUFDMUMsYUFBYSwrQkFBK0I7QUFDNUM7O0FBRUE7QUFDQSxXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ087QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLWJ1bGxldC5qcz8wMGFlIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5TdGF0ZX0gU3RhdGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuT3B0aW9uc30gT3B0aW9uc1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ2J1bGxldCddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrQnVsbGV0KHN0YXRlKSB7XG4gIGNvbnN0IG1hcmtlciA9IHN0YXRlLm9wdGlvbnMuYnVsbGV0IHx8ICcqJ1xuXG4gIGlmIChtYXJrZXIgIT09ICcqJyAmJiBtYXJrZXIgIT09ICcrJyAmJiBtYXJrZXIgIT09ICctJykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIGl0ZW1zIHdpdGggYCcgK1xuICAgICAgICBtYXJrZXIgK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMuYnVsbGV0YCwgZXhwZWN0ZWQgYCpgLCBgK2AsIG9yIGAtYCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gbWFya2VyXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkEmphasis: () => (/* binding */ checkEmphasis)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['emphasis'], null | undefined>}\n */\nfunction checkEmphasis(state) {\n  const marker = state.options.emphasis || '*'\n\n  if (marker !== '*' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize emphasis with `' +\n        marker +\n        '` for `options.emphasis`, expected `*`, or `_`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1lbXBoYXNpcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLDZCQUE2QjtBQUMxQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stZW1waGFzaXMuanM/YTY4NyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuU3RhdGV9IFN0YXRlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLk9wdGlvbnN9IE9wdGlvbnNcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydlbXBoYXNpcyddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrRW1waGFzaXMoc3RhdGUpIHtcbiAgY29uc3QgbWFya2VyID0gc3RhdGUub3B0aW9ucy5lbXBoYXNpcyB8fCAnKidcblxuICBpZiAobWFya2VyICE9PSAnKicgJiYgbWFya2VyICE9PSAnXycpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBlbXBoYXNpcyB3aXRoIGAnICtcbiAgICAgICAgbWFya2VyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLmVtcGhhc2lzYCwgZXhwZWN0ZWQgYCpgLCBvciBgX2AnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIG1hcmtlclxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-fence.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkFence: () => (/* binding */ checkFence)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['fence'], null | undefined>}\n */\nfunction checkFence(state) {\n  const marker = state.options.fence || '`'\n\n  if (marker !== '`' && marker !== '~') {\n    throw new Error(\n      'Cannot serialize code with `' +\n        marker +\n        '` for `options.fence`, expected `` ` `` or `~`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1mZW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLDZCQUE2QjtBQUMxQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stZmVuY2UuanM/NTJmOSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuU3RhdGV9IFN0YXRlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLk9wdGlvbnN9IE9wdGlvbnNcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydmZW5jZSddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrRmVuY2Uoc3RhdGUpIHtcbiAgY29uc3QgbWFya2VyID0gc3RhdGUub3B0aW9ucy5mZW5jZSB8fCAnYCdcblxuICBpZiAobWFya2VyICE9PSAnYCcgJiYgbWFya2VyICE9PSAnficpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBjb2RlIHdpdGggYCcgK1xuICAgICAgICBtYXJrZXIgK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMuZmVuY2VgLCBleHBlY3RlZCBgYCBgIGBgIG9yIGB+YCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gbWFya2VyXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js":
/*!********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkListItemIndent: () => (/* binding */ checkListItemIndent)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['listItemIndent'], null | undefined>}\n */\nfunction checkListItemIndent(state) {\n  const style = state.options.listItemIndent || 'tab'\n\n  // To do: remove in a major.\n  // @ts-expect-error: deprecated.\n  if (style === 1 || style === '1') {\n    return 'one'\n  }\n\n  if (style !== 'tab' && style !== 'one' && style !== 'mixed') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        style +\n        '` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`'\n    )\n  }\n\n  return style\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1saXN0LWl0ZW0taW5kZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsNkJBQTZCO0FBQzFDLGFBQWEsK0JBQStCO0FBQzVDOztBQUVBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLWxpc3QtaXRlbS1pbmRlbnQuanM/ZTQ1OCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuU3RhdGV9IFN0YXRlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLk9wdGlvbnN9IE9wdGlvbnNcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydsaXN0SXRlbUluZGVudCddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrTGlzdEl0ZW1JbmRlbnQoc3RhdGUpIHtcbiAgY29uc3Qgc3R5bGUgPSBzdGF0ZS5vcHRpb25zLmxpc3RJdGVtSW5kZW50IHx8ICd0YWInXG5cbiAgLy8gVG8gZG86IHJlbW92ZSBpbiBhIG1ham9yLlxuICAvLyBAdHMtZXhwZWN0LWVycm9yOiBkZXByZWNhdGVkLlxuICBpZiAoc3R5bGUgPT09IDEgfHwgc3R5bGUgPT09ICcxJykge1xuICAgIHJldHVybiAnb25lJ1xuICB9XG5cbiAgaWYgKHN0eWxlICE9PSAndGFiJyAmJiBzdHlsZSAhPT0gJ29uZScgJiYgc3R5bGUgIT09ICdtaXhlZCcpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBpdGVtcyB3aXRoIGAnICtcbiAgICAgICAgc3R5bGUgK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMubGlzdEl0ZW1JbmRlbnRgLCBleHBlY3RlZCBgdGFiYCwgYG9uZWAsIG9yIGBtaXhlZGAnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIHN0eWxlXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-quote.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkQuote: () => (/* binding */ checkQuote)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['quote'], null | undefined>}\n */\nfunction checkQuote(state) {\n  const marker = state.options.quote || '\"'\n\n  if (marker !== '\"' && marker !== \"'\") {\n    throw new Error(\n      'Cannot serialize title with `' +\n        marker +\n        '` for `options.quote`, expected `\"`, or `\\'`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1xdW90ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLDZCQUE2QjtBQUMxQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stcXVvdGUuanM/ZjFiOCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuU3RhdGV9IFN0YXRlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLk9wdGlvbnN9IE9wdGlvbnNcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydxdW90ZSddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrUXVvdGUoc3RhdGUpIHtcbiAgY29uc3QgbWFya2VyID0gc3RhdGUub3B0aW9ucy5xdW90ZSB8fCAnXCInXG5cbiAgaWYgKG1hcmtlciAhPT0gJ1wiJyAmJiBtYXJrZXIgIT09IFwiJ1wiKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgdGl0bGUgd2l0aCBgJyArXG4gICAgICAgIG1hcmtlciArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5xdW90ZWAsIGV4cGVjdGVkIGBcImAsIG9yIGBcXCdgJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBtYXJrZXJcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkRuleRepetition: () => (/* binding */ checkRuleRepetition)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['ruleRepetition'], null | undefined>}\n */\nfunction checkRuleRepetition(state) {\n  const repetition = state.options.ruleRepetition || 3\n\n  if (repetition < 3) {\n    throw new Error(\n      'Cannot serialize rules with repetition `' +\n        repetition +\n        '` for `options.ruleRepetition`, expected `3` or more'\n    )\n  }\n\n  return repetition\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1ydWxlLXJlcGV0aXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSw2QkFBNkI7QUFDMUMsYUFBYSwrQkFBK0I7QUFDNUM7O0FBRUE7QUFDQSxXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ087QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLXJ1bGUtcmVwZXRpdGlvbi5qcz81ZmQxIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5TdGF0ZX0gU3RhdGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuT3B0aW9uc30gT3B0aW9uc1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ3J1bGVSZXBldGl0aW9uJ10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tSdWxlUmVwZXRpdGlvbihzdGF0ZSkge1xuICBjb25zdCByZXBldGl0aW9uID0gc3RhdGUub3B0aW9ucy5ydWxlUmVwZXRpdGlvbiB8fCAzXG5cbiAgaWYgKHJlcGV0aXRpb24gPCAzKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgcnVsZXMgd2l0aCByZXBldGl0aW9uIGAnICtcbiAgICAgICAgcmVwZXRpdGlvbiArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5ydWxlUmVwZXRpdGlvbmAsIGV4cGVjdGVkIGAzYCBvciBtb3JlJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiByZXBldGl0aW9uXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-rule.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkRule: () => (/* binding */ checkRule)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['rule'], null | undefined>}\n */\nfunction checkRule(state) {\n  const marker = state.options.rule || '*'\n\n  if (marker !== '*' && marker !== '-' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize rules with `' +\n        marker +\n        '` for `options.rule`, expected `*`, `-`, or `_`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1ydWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsNkJBQTZCO0FBQzFDLGFBQWEsK0JBQStCO0FBQzVDOztBQUVBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1ydWxlLmpzPzNiZDMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlN0YXRlfSBTdGF0ZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5PcHRpb25zfSBPcHRpb25zXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1sncnVsZSddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrUnVsZShzdGF0ZSkge1xuICBjb25zdCBtYXJrZXIgPSBzdGF0ZS5vcHRpb25zLnJ1bGUgfHwgJyonXG5cbiAgaWYgKG1hcmtlciAhPT0gJyonICYmIG1hcmtlciAhPT0gJy0nICYmIG1hcmtlciAhPT0gJ18nKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgcnVsZXMgd2l0aCBgJyArXG4gICAgICAgIG1hcmtlciArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5ydWxlYCwgZXhwZWN0ZWQgYCpgLCBgLWAsIG9yIGBfYCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gbWFya2VyXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-strong.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkStrong: () => (/* binding */ checkStrong)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['strong'], null | undefined>}\n */\nfunction checkStrong(state) {\n  const marker = state.options.strong || '*'\n\n  if (marker !== '*' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize strong with `' +\n        marker +\n        '` for `options.strong`, expected `*`, or `_`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1zdHJvbmcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSw2QkFBNkI7QUFDMUMsYUFBYSwrQkFBK0I7QUFDNUM7O0FBRUE7QUFDQSxXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ087QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLXN0cm9uZy5qcz83YWEzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5TdGF0ZX0gU3RhdGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuT3B0aW9uc30gT3B0aW9uc1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ3N0cm9uZyddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrU3Ryb25nKHN0YXRlKSB7XG4gIGNvbnN0IG1hcmtlciA9IHN0YXRlLm9wdGlvbnMuc3Ryb25nIHx8ICcqJ1xuXG4gIGlmIChtYXJrZXIgIT09ICcqJyAmJiBtYXJrZXIgIT09ICdfJykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIHN0cm9uZyB3aXRoIGAnICtcbiAgICAgICAgbWFya2VyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLnN0cm9uZ2AsIGV4cGVjdGVkIGAqYCwgb3IgYF9gJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBtYXJrZXJcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/container-flow.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/container-flow.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   containerFlow: () => (/* binding */ containerFlow)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').FlowContent} FlowContent\n * @typedef {import('../types.js').Node} Node\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').TrackFields} TrackFields\n */\n\n/**\n * @param {Parent & {children: Array<FlowContent>}} parent\n *   Parent of flow nodes.\n * @param {State} state\n *   Info passed around about the current state.\n * @param {TrackFields} info\n *   Info on where we are in the document we are generating.\n * @returns {string}\n *   Serialized children, joined by (blank) lines.\n */\nfunction containerFlow(parent, state, info) {\n  const indexStack = state.indexStack\n  const children = parent.children || []\n  const tracker = state.createTracker(info)\n  /** @type {Array<string>} */\n  const results = []\n  let index = -1\n\n  indexStack.push(-1)\n\n  while (++index < children.length) {\n    const child = children[index]\n\n    indexStack[indexStack.length - 1] = index\n\n    results.push(\n      tracker.move(\n        state.handle(child, parent, state, {\n          before: '\\n',\n          after: '\\n',\n          ...tracker.current()\n        })\n      )\n    )\n\n    if (child.type !== 'list') {\n      state.bulletLastUsed = undefined\n    }\n\n    if (index < children.length - 1) {\n      results.push(\n        tracker.move(between(child, children[index + 1], parent, state))\n      )\n    }\n  }\n\n  indexStack.pop()\n\n  return results.join('')\n}\n\n/**\n * @param {Node} left\n * @param {Node} right\n * @param {Parent} parent\n * @param {State} state\n * @returns {string}\n */\nfunction between(left, right, parent, state) {\n  let index = state.join.length\n\n  while (index--) {\n    const result = state.join[index](left, right, parent, state)\n\n    if (result === true || result === 1) {\n      break\n    }\n\n    if (typeof result === 'number') {\n      return '\\n'.repeat(1 + result)\n    }\n\n    if (result === false) {\n      return '\\n\\n<!---->\\n\\n'\n    }\n  }\n\n  return '\\n\\n'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/container-flow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js":
/*!****************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   containerPhrasing: () => (/* binding */ containerPhrasing)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Info} Info\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').PhrasingContent} PhrasingContent\n * @typedef {import('../types.js').State} State\n */\n\n/**\n * Serialize the children of a parent that contains phrasing children.\n *\n * These children will be joined flush together.\n *\n * @param {Parent & {children: Array<PhrasingContent>}} parent\n *   Parent of flow nodes.\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Info} info\n *   Info on where we are in the document we are generating.\n * @returns {string}\n *   Serialized children, joined together.\n */\nfunction containerPhrasing(parent, state, info) {\n  const indexStack = state.indexStack\n  const children = parent.children || []\n  /** @type {Array<string>} */\n  const results = []\n  let index = -1\n  let before = info.before\n\n  indexStack.push(-1)\n  let tracker = state.createTracker(info)\n\n  while (++index < children.length) {\n    const child = children[index]\n    /** @type {string} */\n    let after\n\n    indexStack[indexStack.length - 1] = index\n\n    if (index + 1 < children.length) {\n      /** @type {Handle} */\n      // @ts-expect-error: hush, it’s actually a `zwitch`.\n      let handle = state.handle.handlers[children[index + 1].type]\n      /** @type {Handle} */\n      // @ts-expect-error: hush, it’s actually a `zwitch`.\n      if (handle && handle.peek) handle = handle.peek\n      after = handle\n        ? handle(children[index + 1], parent, state, {\n            before: '',\n            after: '',\n            ...tracker.current()\n          }).charAt(0)\n        : ''\n    } else {\n      after = info.after\n    }\n\n    // In some cases, html (text) can be found in phrasing right after an eol.\n    // When we’d serialize that, in most cases that would be seen as html\n    // (flow).\n    // As we can’t escape or so to prevent it from happening, we take a somewhat\n    // reasonable approach: replace that eol with a space.\n    // See: <https://github.com/syntax-tree/mdast-util-to-markdown/issues/15>\n    if (\n      results.length > 0 &&\n      (before === '\\r' || before === '\\n') &&\n      child.type === 'html'\n    ) {\n      results[results.length - 1] = results[results.length - 1].replace(\n        /(\\r?\\n|\\r)$/,\n        ' '\n      )\n      before = ' '\n\n      // To do: does this work to reset tracker?\n      tracker = state.createTracker(info)\n      tracker.move(results.join(''))\n    }\n\n    results.push(\n      tracker.move(\n        state.handle(child, parent, state, {\n          ...tracker.current(),\n          before,\n          after\n        })\n      )\n    )\n\n    before = results[results.length - 1].slice(-1)\n  }\n\n  indexStack.pop()\n\n  return results.join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatCodeAsIndented: () => (/* binding */ formatCodeAsIndented)\n/* harmony export */ });\n/**\n * @typedef {import('mdast').Code} Code\n * @typedef {import('../types.js').State} State\n */\n\n/**\n * @param {Code} node\n * @param {State} state\n * @returns {boolean}\n */\nfunction formatCodeAsIndented(node, state) {\n  return Boolean(\n    !state.options.fences &&\n      node.value &&\n      // If there’s no info…\n      !node.lang &&\n      // And there’s a non-whitespace character…\n      /[^ \\r\\n]/.test(node.value) &&\n      // And the value doesn’t start or end in a blank…\n      !/^[\\t ]*(?:[\\r\\n]|$)|(?:^|[\\r\\n])[\\t ]*$/.test(node.value)\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9mb3JtYXQtY29kZS1hcy1pbmRlbnRlZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLHNCQUFzQjtBQUNuQyxhQUFhLDZCQUE2QjtBQUMxQzs7QUFFQTtBQUNBLFdBQVcsTUFBTTtBQUNqQixXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2Zvcm1hdC1jb2RlLWFzLWluZGVudGVkLmpzPzA4ZjciXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkNvZGV9IENvZGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge0NvZGV9IG5vZGVcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdENvZGVBc0luZGVudGVkKG5vZGUsIHN0YXRlKSB7XG4gIHJldHVybiBCb29sZWFuKFxuICAgICFzdGF0ZS5vcHRpb25zLmZlbmNlcyAmJlxuICAgICAgbm9kZS52YWx1ZSAmJlxuICAgICAgLy8gSWYgdGhlcmXigJlzIG5vIGluZm/igKZcbiAgICAgICFub2RlLmxhbmcgJiZcbiAgICAgIC8vIEFuZCB0aGVyZeKAmXMgYSBub24td2hpdGVzcGFjZSBjaGFyYWN0ZXLigKZcbiAgICAgIC9bXiBcXHJcXG5dLy50ZXN0KG5vZGUudmFsdWUpICYmXG4gICAgICAvLyBBbmQgdGhlIHZhbHVlIGRvZXNu4oCZdCBzdGFydCBvciBlbmQgaW4gYSBibGFua+KAplxuICAgICAgIS9eW1xcdCBdKig/OltcXHJcXG5dfCQpfCg/Ol58W1xcclxcbl0pW1xcdCBdKiQvLnRlc3Qobm9kZS52YWx1ZSlcbiAgKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatHeadingAsSetext: () => (/* binding */ formatHeadingAsSetext)\n/* harmony export */ });\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/node_modules/unist-util-visit-parents/lib/index.js\");\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mdast-util-to-string */ \"(ssr)/./node_modules/mdast-util-to-string/lib/index.js\");\n/**\n * @typedef {import('mdast').Heading} Heading\n * @typedef {import('../types.js').State} State\n */\n\n\n\n\n/**\n * @param {Heading} node\n * @param {State} state\n * @returns {boolean}\n */\nfunction formatHeadingAsSetext(node, state) {\n  let literalWithBreak = false\n\n  // Look for literals with a line break.\n  // Note that this also\n  ;(0,unist_util_visit__WEBPACK_IMPORTED_MODULE_0__.visit)(node, (node) => {\n    if (\n      ('value' in node && /\\r?\\n|\\r/.test(node.value)) ||\n      node.type === 'break'\n    ) {\n      literalWithBreak = true\n      return unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.EXIT\n    }\n  })\n\n  return Boolean(\n    (!node.depth || node.depth < 3) &&\n      (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_2__.toString)(node) &&\n      (state.options.setext || literalWithBreak)\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9mb3JtYXQtaGVhZGluZy1hcy1zZXRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ0EsYUFBYSx5QkFBeUI7QUFDdEMsYUFBYSw2QkFBNkI7QUFDMUM7O0FBRTRDO0FBQ0M7O0FBRTdDO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQSxFQUFFLHdEQUFLO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsa0RBQUk7QUFDakI7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQSxNQUFNLDhEQUFRO0FBQ2Q7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2Zvcm1hdC1oZWFkaW5nLWFzLXNldGV4dC5qcz9kM2UzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5IZWFkaW5nfSBIZWFkaW5nXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbmltcG9ydCB7dmlzaXQsIEVYSVR9IGZyb20gJ3VuaXN0LXV0aWwtdmlzaXQnXG5pbXBvcnQge3RvU3RyaW5nfSBmcm9tICdtZGFzdC11dGlsLXRvLXN0cmluZydcblxuLyoqXG4gKiBAcGFyYW0ge0hlYWRpbmd9IG5vZGVcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdEhlYWRpbmdBc1NldGV4dChub2RlLCBzdGF0ZSkge1xuICBsZXQgbGl0ZXJhbFdpdGhCcmVhayA9IGZhbHNlXG5cbiAgLy8gTG9vayBmb3IgbGl0ZXJhbHMgd2l0aCBhIGxpbmUgYnJlYWsuXG4gIC8vIE5vdGUgdGhhdCB0aGlzIGFsc29cbiAgdmlzaXQobm9kZSwgKG5vZGUpID0+IHtcbiAgICBpZiAoXG4gICAgICAoJ3ZhbHVlJyBpbiBub2RlICYmIC9cXHI/XFxufFxcci8udGVzdChub2RlLnZhbHVlKSkgfHxcbiAgICAgIG5vZGUudHlwZSA9PT0gJ2JyZWFrJ1xuICAgICkge1xuICAgICAgbGl0ZXJhbFdpdGhCcmVhayA9IHRydWVcbiAgICAgIHJldHVybiBFWElUXG4gICAgfVxuICB9KVxuXG4gIHJldHVybiBCb29sZWFuKFxuICAgICghbm9kZS5kZXB0aCB8fCBub2RlLmRlcHRoIDwgMykgJiZcbiAgICAgIHRvU3RyaW5nKG5vZGUpICYmXG4gICAgICAoc3RhdGUub3B0aW9ucy5zZXRleHQgfHwgbGl0ZXJhbFdpdGhCcmVhaylcbiAgKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLinkAsAutolink: () => (/* binding */ formatLinkAsAutolink)\n/* harmony export */ });\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-to-string */ \"(ssr)/./node_modules/mdast-util-to-string/lib/index.js\");\n/**\n * @typedef {import('mdast').Link} Link\n * @typedef {import('../types.js').State} State\n */\n\n\n\n/**\n * @param {Link} node\n * @param {State} state\n * @returns {boolean}\n */\nfunction formatLinkAsAutolink(node, state) {\n  const raw = (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_0__.toString)(node)\n\n  return Boolean(\n    !state.options.resourceLink &&\n      // If there’s a url…\n      node.url &&\n      // And there’s a no title…\n      !node.title &&\n      // And the content of `node` is a single text node…\n      node.children &&\n      node.children.length === 1 &&\n      node.children[0].type === 'text' &&\n      // And if the url is the same as the content…\n      (raw === node.url || 'mailto:' + raw === node.url) &&\n      // And that starts w/ a protocol…\n      /^[a-z][a-z+.-]+:/i.test(node.url) &&\n      // And that doesn’t contain ASCII control codes (character escapes and\n      // references don’t work), space, or angle brackets…\n      !/[\\0- <>\\u007F]/.test(node.url)\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/indent-lines.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/indent-lines.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   indentLines: () => (/* binding */ indentLines)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').IndentLines} IndentLines\n */\n\nconst eol = /\\r?\\n|\\r/g\n\n/**\n * @type {IndentLines}\n */\nfunction indentLines(value, map) {\n  /** @type {Array<string>} */\n  const result = []\n  let start = 0\n  let line = 0\n  /** @type {RegExpExecArray | null} */\n  let match\n\n  while ((match = eol.exec(value))) {\n    one(value.slice(start, match.index))\n    result.push(match[0])\n    start = match.index + match[0].length\n    line++\n  }\n\n  one(value.slice(start))\n\n  return result.join('')\n\n  /**\n   * @param {string} value\n   */\n  function one(value) {\n    result.push(map(value, line, !value))\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9pbmRlbnQtbGluZXMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSxtQ0FBbUM7QUFDaEQ7O0FBRUE7O0FBRUE7QUFDQSxVQUFVO0FBQ1Y7QUFDTztBQUNQLGFBQWEsZUFBZTtBQUM1QjtBQUNBO0FBQ0E7QUFDQSxhQUFhLHdCQUF3QjtBQUNyQzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7O0FBRUE7QUFDQSxhQUFhLFFBQVE7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9pbmRlbnQtbGluZXMuanM/MTA3MiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSW5kZW50TGluZXN9IEluZGVudExpbmVzXG4gKi9cblxuY29uc3QgZW9sID0gL1xccj9cXG58XFxyL2dcblxuLyoqXG4gKiBAdHlwZSB7SW5kZW50TGluZXN9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpbmRlbnRMaW5lcyh2YWx1ZSwgbWFwKSB7XG4gIC8qKiBAdHlwZSB7QXJyYXk8c3RyaW5nPn0gKi9cbiAgY29uc3QgcmVzdWx0ID0gW11cbiAgbGV0IHN0YXJ0ID0gMFxuICBsZXQgbGluZSA9IDBcbiAgLyoqIEB0eXBlIHtSZWdFeHBFeGVjQXJyYXkgfCBudWxsfSAqL1xuICBsZXQgbWF0Y2hcblxuICB3aGlsZSAoKG1hdGNoID0gZW9sLmV4ZWModmFsdWUpKSkge1xuICAgIG9uZSh2YWx1ZS5zbGljZShzdGFydCwgbWF0Y2guaW5kZXgpKVxuICAgIHJlc3VsdC5wdXNoKG1hdGNoWzBdKVxuICAgIHN0YXJ0ID0gbWF0Y2guaW5kZXggKyBtYXRjaFswXS5sZW5ndGhcbiAgICBsaW5lKytcbiAgfVxuXG4gIG9uZSh2YWx1ZS5zbGljZShzdGFydCkpXG5cbiAgcmV0dXJuIHJlc3VsdC5qb2luKCcnKVxuXG4gIC8qKlxuICAgKiBAcGFyYW0ge3N0cmluZ30gdmFsdWVcbiAgICovXG4gIGZ1bmN0aW9uIG9uZSh2YWx1ZSkge1xuICAgIHJlc3VsdC5wdXNoKG1hcCh2YWx1ZSwgbGluZSwgIXZhbHVlKSlcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/indent-lines.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-compile.js":
/*!*************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/pattern-compile.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patternCompile: () => (/* binding */ patternCompile)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Unsafe} Unsafe\n */\n\n/**\n * @param {Unsafe} pattern\n * @returns {RegExp}\n */\nfunction patternCompile(pattern) {\n  if (!pattern._compiled) {\n    const before =\n      (pattern.atBreak ? '[\\\\r\\\\n][\\\\t ]*' : '') +\n      (pattern.before ? '(?:' + pattern.before + ')' : '')\n\n    pattern._compiled = new RegExp(\n      (before ? '(' + before + ')' : '') +\n        (/[|\\\\{}()[\\]^$+*?.-]/.test(pattern.character) ? '\\\\' : '') +\n        pattern.character +\n        (pattern.after ? '(?:' + pattern.after + ')' : ''),\n      'g'\n    )\n  }\n\n  return pattern._compiled\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9wYXR0ZXJuLWNvbXBpbGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0M7O0FBRUE7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9wYXR0ZXJuLWNvbXBpbGUuanM/ZWUxNSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuVW5zYWZlfSBVbnNhZmVcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7VW5zYWZlfSBwYXR0ZXJuXG4gKiBAcmV0dXJucyB7UmVnRXhwfVxuICovXG5leHBvcnQgZnVuY3Rpb24gcGF0dGVybkNvbXBpbGUocGF0dGVybikge1xuICBpZiAoIXBhdHRlcm4uX2NvbXBpbGVkKSB7XG4gICAgY29uc3QgYmVmb3JlID1cbiAgICAgIChwYXR0ZXJuLmF0QnJlYWsgPyAnW1xcXFxyXFxcXG5dW1xcXFx0IF0qJyA6ICcnKSArXG4gICAgICAocGF0dGVybi5iZWZvcmUgPyAnKD86JyArIHBhdHRlcm4uYmVmb3JlICsgJyknIDogJycpXG5cbiAgICBwYXR0ZXJuLl9jb21waWxlZCA9IG5ldyBSZWdFeHAoXG4gICAgICAoYmVmb3JlID8gJygnICsgYmVmb3JlICsgJyknIDogJycpICtcbiAgICAgICAgKC9bfFxcXFx7fSgpW1xcXV4kKyo/Li1dLy50ZXN0KHBhdHRlcm4uY2hhcmFjdGVyKSA/ICdcXFxcJyA6ICcnKSArXG4gICAgICAgIHBhdHRlcm4uY2hhcmFjdGVyICtcbiAgICAgICAgKHBhdHRlcm4uYWZ0ZXIgPyAnKD86JyArIHBhdHRlcm4uYWZ0ZXIgKyAnKScgOiAnJyksXG4gICAgICAnZydcbiAgICApXG4gIH1cblxuICByZXR1cm4gcGF0dGVybi5fY29tcGlsZWRcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-compile.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patternInScope: () => (/* binding */ patternInScope)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Unsafe} Unsafe\n * @typedef {import('../types.js').ConstructName} ConstructName\n */\n\n/**\n * @param {Array<ConstructName>} stack\n * @param {Unsafe} pattern\n * @returns {boolean}\n */\nfunction patternInScope(stack, pattern) {\n  return (\n    listInScope(stack, pattern.inConstruct, true) &&\n    !listInScope(stack, pattern.notInConstruct, false)\n  )\n}\n\n/**\n * @param {Array<ConstructName>} stack\n * @param {Unsafe['inConstruct']} list\n * @param {boolean} none\n * @returns {boolean}\n */\nfunction listInScope(stack, list, none) {\n  if (typeof list === 'string') {\n    list = [list]\n  }\n\n  if (!list || list.length === 0) {\n    return none\n  }\n\n  let index = -1\n\n  while (++index < list.length) {\n    if (stack.includes(list[index])) {\n      return true\n    }\n  }\n\n  return false\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9wYXR0ZXJuLWluLXNjb3BlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEscUNBQXFDO0FBQ2xEOztBQUVBO0FBQ0EsV0FBVyxzQkFBc0I7QUFDakMsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFdBQVcsc0JBQXNCO0FBQ2pDLFdBQVcsdUJBQXVCO0FBQ2xDLFdBQVcsU0FBUztBQUNwQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL3BhdHRlcm4taW4tc2NvcGUuanM/MTdiMiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuVW5zYWZlfSBVbnNhZmVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuQ29uc3RydWN0TmFtZX0gQ29uc3RydWN0TmFtZVxuICovXG5cbi8qKlxuICogQHBhcmFtIHtBcnJheTxDb25zdHJ1Y3ROYW1lPn0gc3RhY2tcbiAqIEBwYXJhbSB7VW5zYWZlfSBwYXR0ZXJuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHBhdHRlcm5JblNjb3BlKHN0YWNrLCBwYXR0ZXJuKSB7XG4gIHJldHVybiAoXG4gICAgbGlzdEluU2NvcGUoc3RhY2ssIHBhdHRlcm4uaW5Db25zdHJ1Y3QsIHRydWUpICYmXG4gICAgIWxpc3RJblNjb3BlKHN0YWNrLCBwYXR0ZXJuLm5vdEluQ29uc3RydWN0LCBmYWxzZSlcbiAgKVxufVxuXG4vKipcbiAqIEBwYXJhbSB7QXJyYXk8Q29uc3RydWN0TmFtZT59IHN0YWNrXG4gKiBAcGFyYW0ge1Vuc2FmZVsnaW5Db25zdHJ1Y3QnXX0gbGlzdFxuICogQHBhcmFtIHtib29sZWFufSBub25lXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqL1xuZnVuY3Rpb24gbGlzdEluU2NvcGUoc3RhY2ssIGxpc3QsIG5vbmUpIHtcbiAgaWYgKHR5cGVvZiBsaXN0ID09PSAnc3RyaW5nJykge1xuICAgIGxpc3QgPSBbbGlzdF1cbiAgfVxuXG4gIGlmICghbGlzdCB8fCBsaXN0Lmxlbmd0aCA9PT0gMCkge1xuICAgIHJldHVybiBub25lXG4gIH1cblxuICBsZXQgaW5kZXggPSAtMVxuXG4gIHdoaWxlICgrK2luZGV4IDwgbGlzdC5sZW5ndGgpIHtcbiAgICBpZiAoc3RhY2suaW5jbHVkZXMobGlzdFtpbmRleF0pKSB7XG4gICAgICByZXR1cm4gdHJ1ZVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiBmYWxzZVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/safe.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/safe.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   safe: () => (/* binding */ safe)\n/* harmony export */ });\n/* harmony import */ var _pattern_compile_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./pattern-compile.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-compile.js\");\n/* harmony import */ var _pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./pattern-in-scope.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js\");\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').SafeConfig} SafeConfig\n */\n\n\n\n\n/**\n * Make a string safe for embedding in markdown constructs.\n *\n * In markdown, almost all punctuation characters can, in certain cases,\n * result in something.\n * Whether they do is highly subjective to where they happen and in what\n * they happen.\n *\n * To solve this, `mdast-util-to-markdown` tracks:\n *\n * * Characters before and after something;\n * * What “constructs” we are in.\n *\n * This information is then used by this function to escape or encode\n * special characters.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {string | null | undefined} input\n *   Raw value to make safe.\n * @param {SafeConfig} config\n *   Configuration.\n * @returns {string}\n *   Serialized markdown safe for embedding.\n */\nfunction safe(state, input, config) {\n  const value = (config.before || '') + (input || '') + (config.after || '')\n  /** @type {Array<number>} */\n  const positions = []\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {Record<number, {before: boolean, after: boolean}>} */\n  const infos = {}\n  let index = -1\n\n  while (++index < state.unsafe.length) {\n    const pattern = state.unsafe[index]\n\n    if (!(0,_pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__.patternInScope)(state.stack, pattern)) {\n      continue\n    }\n\n    const expression = (0,_pattern_compile_js__WEBPACK_IMPORTED_MODULE_1__.patternCompile)(pattern)\n    /** @type {RegExpExecArray | null} */\n    let match\n\n    while ((match = expression.exec(value))) {\n      const before = 'before' in pattern || Boolean(pattern.atBreak)\n      const after = 'after' in pattern\n      const position = match.index + (before ? match[1].length : 0)\n\n      if (positions.includes(position)) {\n        if (infos[position].before && !before) {\n          infos[position].before = false\n        }\n\n        if (infos[position].after && !after) {\n          infos[position].after = false\n        }\n      } else {\n        positions.push(position)\n        infos[position] = {before, after}\n      }\n    }\n  }\n\n  positions.sort(numerical)\n\n  let start = config.before ? config.before.length : 0\n  const end = value.length - (config.after ? config.after.length : 0)\n  index = -1\n\n  while (++index < positions.length) {\n    const position = positions[index]\n\n    // Character before or after matched:\n    if (position < start || position >= end) {\n      continue\n    }\n\n    // If this character is supposed to be escaped because it has a condition on\n    // the next character, and the next character is definitly being escaped,\n    // then skip this escape.\n    if (\n      (position + 1 < end &&\n        positions[index + 1] === position + 1 &&\n        infos[position].after &&\n        !infos[position + 1].before &&\n        !infos[position + 1].after) ||\n      (positions[index - 1] === position - 1 &&\n        infos[position].before &&\n        !infos[position - 1].before &&\n        !infos[position - 1].after)\n    ) {\n      continue\n    }\n\n    if (start !== position) {\n      // If we have to use a character reference, an ampersand would be more\n      // correct, but as backslashes only care about punctuation, either will\n      // do the trick\n      result.push(escapeBackslashes(value.slice(start, position), '\\\\'))\n    }\n\n    start = position\n\n    if (\n      /[!-/:-@[-`{-~]/.test(value.charAt(position)) &&\n      (!config.encode || !config.encode.includes(value.charAt(position)))\n    ) {\n      // Character escape.\n      result.push('\\\\')\n    } else {\n      // Character reference.\n      result.push(\n        '&#x' + value.charCodeAt(position).toString(16).toUpperCase() + ';'\n      )\n      start++\n    }\n  }\n\n  result.push(escapeBackslashes(value.slice(start, end), config.after))\n\n  return result.join('')\n}\n\n/**\n * @param {number} a\n * @param {number} b\n * @returns {number}\n */\nfunction numerical(a, b) {\n  return a - b\n}\n\n/**\n * @param {string} value\n * @param {string} after\n * @returns {string}\n */\nfunction escapeBackslashes(value, after) {\n  const expression = /\\\\(?=[!-/:-@[-`{-~])/g\n  /** @type {Array<number>} */\n  const positions = []\n  /** @type {Array<string>} */\n  const results = []\n  const whole = value + after\n  let index = -1\n  let start = 0\n  /** @type {RegExpExecArray | null} */\n  let match\n\n  while ((match = expression.exec(whole))) {\n    positions.push(match.index)\n  }\n\n  while (++index < positions.length) {\n    if (start !== positions[index]) {\n      results.push(value.slice(start, positions[index]))\n    }\n\n    results.push('\\\\')\n    start = positions[index]\n  }\n\n  results.push(value.slice(start))\n\n  return results.join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/safe.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/track.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/track.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   track: () => (/* binding */ track)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').CreateTracker} CreateTracker\n * @typedef {import('../types.js').TrackCurrent} TrackCurrent\n * @typedef {import('../types.js').TrackMove} TrackMove\n * @typedef {import('../types.js').TrackShift} TrackShift\n */\n\n/**\n * Track positional info in the output.\n *\n * @type {CreateTracker}\n */\nfunction track(config) {\n  // Defaults are used to prevent crashes when older utilities somehow activate\n  // this code.\n  /* c8 ignore next 5 */\n  const options = config || {}\n  const now = options.now || {}\n  let lineShift = options.lineShift || 0\n  let line = now.line || 1\n  let column = now.column || 1\n\n  return {move, current, shift}\n\n  /**\n   * Get the current tracked info.\n   *\n   * @type {TrackCurrent}\n   */\n  function current() {\n    return {now: {line, column}, lineShift}\n  }\n\n  /**\n   * Define an increased line shift (the typical indent for lines).\n   *\n   * @type {TrackShift}\n   */\n  function shift(value) {\n    lineShift += value\n  }\n\n  /**\n   * Move past some generated markdown.\n   *\n   * @type {TrackMove}\n   */\n  function move(input) {\n    // eslint-disable-next-line unicorn/prefer-default-parameters\n    const value = input || ''\n    const chunks = value.split(/\\r?\\n|\\r/g)\n    const tail = chunks[chunks.length - 1]\n    line += chunks.length - 1\n    column =\n      chunks.length === 1 ? column + tail.length : 1 + tail.length + lineShift\n    return value\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/track.js\n");

/***/ })

};
;