"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/forwarded-parse";
exports.ids = ["vendor-chunks/forwarded-parse"];
exports.modules = {

/***/ "(ssr)/./node_modules/forwarded-parse/index.js":
/*!***********************************************!*\
  !*** ./node_modules/forwarded-parse/index.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar util = __webpack_require__(/*! util */ \"util\");\n\nvar ParseError = __webpack_require__(/*! ./lib/error */ \"(ssr)/./node_modules/forwarded-parse/lib/error.js\");\nvar ascii = __webpack_require__(/*! ./lib/ascii */ \"(ssr)/./node_modules/forwarded-parse/lib/ascii.js\");\n\nvar isDelimiter = ascii.isDelimiter;\nvar isTokenChar = ascii.isTokenChar;\nvar isExtended = ascii.isExtended;\nvar isPrint = ascii.isPrint;\n\n/**\n * Unescape a string.\n *\n * @param {string} str The string to unescape.\n * @returns {string} A new unescaped string.\n * @private\n */\nfunction decode(str) {\n  return str.replace(/\\\\(.)/g, '$1');\n}\n\n/**\n * Build an error message when an unexpected character is found.\n *\n * @param {string} header The header field value.\n * @param {number} position The position of the unexpected character.\n * @returns {string} The error message.\n * @private\n */\nfunction unexpectedCharacterMessage(header, position) {\n  return util.format(\n    \"Unexpected character '%s' at index %d\",\n    header.charAt(position),\n    position\n  );\n}\n\n/**\n * Parse the `Forwarded` header field value into an array of objects.\n *\n * @param {string} header The header field value.\n * @returns {Object[]}\n * @public\n */\nfunction parse(header) {\n  var mustUnescape = false;\n  var isEscaping = false;\n  var inQuotes = false;\n  var forwarded = {};\n  var output = [];\n  var start = -1;\n  var end = -1;\n  var parameter;\n  var code;\n\n  for (var i = 0; i < header.length; i++) {\n    code = header.charCodeAt(i);\n\n    if (parameter === undefined) {\n      if (\n        i !== 0 &&\n        start === -1 &&\n        (code === 0x20/*' '*/ || code === 0x09/*'\\t'*/)\n      ) {\n        continue;\n      }\n\n      if (isTokenChar(code)) {\n        if (start === -1) start = i;\n      } else if (code === 0x3D/*'='*/ && start !== -1) {\n        parameter = header.slice(start, i).toLowerCase();\n        start = -1;\n      } else {\n        throw new ParseError(unexpectedCharacterMessage(header, i), header);\n      }\n    } else {\n      if (isEscaping && (code === 0x09 || isPrint(code) || isExtended(code))) {\n        isEscaping = false;\n      } else if (isTokenChar(code)) {\n        if (end !== -1) {\n          throw new ParseError(unexpectedCharacterMessage(header, i), header);\n        }\n\n        if (start === -1) start = i;\n      } else if (isDelimiter(code) || isExtended(code)) {\n        if (inQuotes) {\n          if (code === 0x22/*'\"'*/) {\n            inQuotes = false;\n            end = i;\n          } else if (code === 0x5C/*'\\'*/) {\n            if (start === -1) start = i;\n            isEscaping = mustUnescape = true;\n          } else if (start === -1) {\n            start = i;\n          }\n        } else if (code === 0x22 && header.charCodeAt(i - 1) === 0x3D) {\n          inQuotes = true;\n        } else if (\n          (code === 0x2C/*','*/|| code === 0x3B/*';'*/) &&\n          (start !== -1 || end !== -1)\n        ) {\n          if (start !== -1) {\n            if (end === -1) end = i;\n            forwarded[parameter] = mustUnescape\n              ? decode(header.slice(start, end))\n              : header.slice(start, end);\n          } else {\n            forwarded[parameter] = '';\n          }\n\n          if (code === 0x2C) {\n            output.push(forwarded);\n            forwarded = {};\n          }\n\n          parameter = undefined;\n          start = end = -1;\n        } else {\n          throw new ParseError(unexpectedCharacterMessage(header, i), header);\n        }\n      } else if (code === 0x20 || code === 0x09) {\n        if (end !== -1) continue;\n\n        if (inQuotes) {\n          if (start === -1) start = i;\n        } else if (start !== -1) {\n          end = i;\n        } else {\n          throw new ParseError(unexpectedCharacterMessage(header, i), header);\n        }\n      } else {\n        throw new ParseError(unexpectedCharacterMessage(header, i), header);\n      }\n    }\n  }\n\n  if (\n    parameter === undefined ||\n    inQuotes ||\n    (start === -1 && end === -1) ||\n    code === 0x20 ||\n    code === 0x09\n  ) {\n    throw new ParseError('Unexpected end of input', header);\n  }\n\n  if (start !== -1) {\n    if (end === -1) end = i;\n    forwarded[parameter] = mustUnescape\n      ? decode(header.slice(start, end))\n      : header.slice(start, end);\n  } else {\n    forwarded[parameter] = '';\n  }\n\n  output.push(forwarded);\n  return output;\n}\n\nmodule.exports = parse;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/forwarded-parse/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/forwarded-parse/lib/ascii.js":
/*!***************************************************!*\
  !*** ./node_modules/forwarded-parse/lib/ascii.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\n\n/**\n * Check if a character is a delimiter as defined in section 3.2.6 of RFC 7230.\n *\n *\n * @param {number} code The code of the character to check.\n * @returns {boolean} `true` if the character is a delimiter, else `false`.\n * @public\n */\nfunction isDelimiter(code) {\n  return code === 0x22                // '\"'\n    || code === 0x28                  // '('\n    || code === 0x29                  // ')'\n    || code === 0x2C                  // ','\n    || code === 0x2F                  // '/'\n    || code >= 0x3A && code <= 0x40   // ':', ';', '<', '=', '>', '?' '@'\n    || code >= 0x5B && code <= 0x5D   // '[', '\\', ']'\n    || code === 0x7B                  // '{'\n    || code === 0x7D;                 // '}'\n}\n\n/**\n * Check if a character is allowed in a token as defined in section 3.2.6\n * of RFC 7230.\n *\n * @param {number} code The code of the character to check.\n * @returns {boolean} `true` if the character is allowed, else `false`.\n * @public\n */\nfunction isTokenChar(code) {\n  return code === 0x21                // '!'\n    || code >= 0x23 && code <= 0x27   // '#', '$', '%', '&', '''\n    || code === 0x2A                  // '*'\n    || code === 0x2B                  // '+'\n    || code === 0x2D                  // '-'\n    || code === 0x2E                  // '.'\n    || code >= 0x30 && code <= 0x39   // 0-9\n    || code >= 0x41 && code <= 0x5A   // A-Z\n    || code >= 0x5E && code <= 0x7A   // '^', '_', '`', a-z\n    || code === 0x7C                  // '|'\n    || code === 0x7E;                 // '~'\n}\n\n/**\n * Check if a character is a printable ASCII character.\n *\n * @param {number} code The code of the character to check.\n * @returns {boolean} `true` if `code` is in the %x20-7E range, else `false`.\n * @public\n */\nfunction isPrint(code) {\n  return code >= 0x20 && code <= 0x7E;\n}\n\n/**\n * Check if a character is an extended ASCII character.\n *\n * @param {number} code The code of the character to check.\n * @returns {boolean} `true` if `code` is in the %x80-FF range, else `false`.\n * @public\n */\nfunction isExtended(code) {\n  return code >= 0x80 && code <= 0xFF;\n}\n\nmodule.exports = {\n  isDelimiter: isDelimiter,\n  isTokenChar: isTokenChar,\n  isExtended: isExtended,\n  isPrint: isPrint\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/forwarded-parse/lib/ascii.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/forwarded-parse/lib/error.js":
/*!***************************************************!*\
  !*** ./node_modules/forwarded-parse/lib/error.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar util = __webpack_require__(/*! util */ \"util\");\n\n/**\n * An error thrown by the parser on unexpected input.\n *\n * @constructor\n * @param {string} message The error message.\n * @param {string} input The unexpected input.\n * @public\n */\nfunction ParseError(message, input) {\n  Error.captureStackTrace(this, ParseError);\n\n  this.name = this.constructor.name;\n  this.message = message;\n  this.input = input;\n}\n\nutil.inherits(ParseError, Error);\n\nmodule.exports = ParseError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZm9yd2FyZGVkLXBhcnNlL2xpYi9lcnJvci5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixXQUFXLG1CQUFPLENBQUMsa0JBQU07O0FBRXpCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvZm9yd2FyZGVkLXBhcnNlL2xpYi9lcnJvci5qcz8zY2Q3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxudmFyIHV0aWwgPSByZXF1aXJlKCd1dGlsJyk7XG5cbi8qKlxuICogQW4gZXJyb3IgdGhyb3duIGJ5IHRoZSBwYXJzZXIgb24gdW5leHBlY3RlZCBpbnB1dC5cbiAqXG4gKiBAY29uc3RydWN0b3JcbiAqIEBwYXJhbSB7c3RyaW5nfSBtZXNzYWdlIFRoZSBlcnJvciBtZXNzYWdlLlxuICogQHBhcmFtIHtzdHJpbmd9IGlucHV0IFRoZSB1bmV4cGVjdGVkIGlucHV0LlxuICogQHB1YmxpY1xuICovXG5mdW5jdGlvbiBQYXJzZUVycm9yKG1lc3NhZ2UsIGlucHV0KSB7XG4gIEVycm9yLmNhcHR1cmVTdGFja1RyYWNlKHRoaXMsIFBhcnNlRXJyb3IpO1xuXG4gIHRoaXMubmFtZSA9IHRoaXMuY29uc3RydWN0b3IubmFtZTtcbiAgdGhpcy5tZXNzYWdlID0gbWVzc2FnZTtcbiAgdGhpcy5pbnB1dCA9IGlucHV0O1xufVxuXG51dGlsLmluaGVyaXRzKFBhcnNlRXJyb3IsIEVycm9yKTtcblxubW9kdWxlLmV4cG9ydHMgPSBQYXJzZUVycm9yO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/forwarded-parse/lib/error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/forwarded-parse/index.js":
/*!***********************************************!*\
  !*** ./node_modules/forwarded-parse/index.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar util = __webpack_require__(/*! util */ \"util\");\n\nvar ParseError = __webpack_require__(/*! ./lib/error */ \"(rsc)/./node_modules/forwarded-parse/lib/error.js\");\nvar ascii = __webpack_require__(/*! ./lib/ascii */ \"(rsc)/./node_modules/forwarded-parse/lib/ascii.js\");\n\nvar isDelimiter = ascii.isDelimiter;\nvar isTokenChar = ascii.isTokenChar;\nvar isExtended = ascii.isExtended;\nvar isPrint = ascii.isPrint;\n\n/**\n * Unescape a string.\n *\n * @param {string} str The string to unescape.\n * @returns {string} A new unescaped string.\n * @private\n */\nfunction decode(str) {\n  return str.replace(/\\\\(.)/g, '$1');\n}\n\n/**\n * Build an error message when an unexpected character is found.\n *\n * @param {string} header The header field value.\n * @param {number} position The position of the unexpected character.\n * @returns {string} The error message.\n * @private\n */\nfunction unexpectedCharacterMessage(header, position) {\n  return util.format(\n    \"Unexpected character '%s' at index %d\",\n    header.charAt(position),\n    position\n  );\n}\n\n/**\n * Parse the `Forwarded` header field value into an array of objects.\n *\n * @param {string} header The header field value.\n * @returns {Object[]}\n * @public\n */\nfunction parse(header) {\n  var mustUnescape = false;\n  var isEscaping = false;\n  var inQuotes = false;\n  var forwarded = {};\n  var output = [];\n  var start = -1;\n  var end = -1;\n  var parameter;\n  var code;\n\n  for (var i = 0; i < header.length; i++) {\n    code = header.charCodeAt(i);\n\n    if (parameter === undefined) {\n      if (\n        i !== 0 &&\n        start === -1 &&\n        (code === 0x20/*' '*/ || code === 0x09/*'\\t'*/)\n      ) {\n        continue;\n      }\n\n      if (isTokenChar(code)) {\n        if (start === -1) start = i;\n      } else if (code === 0x3D/*'='*/ && start !== -1) {\n        parameter = header.slice(start, i).toLowerCase();\n        start = -1;\n      } else {\n        throw new ParseError(unexpectedCharacterMessage(header, i), header);\n      }\n    } else {\n      if (isEscaping && (code === 0x09 || isPrint(code) || isExtended(code))) {\n        isEscaping = false;\n      } else if (isTokenChar(code)) {\n        if (end !== -1) {\n          throw new ParseError(unexpectedCharacterMessage(header, i), header);\n        }\n\n        if (start === -1) start = i;\n      } else if (isDelimiter(code) || isExtended(code)) {\n        if (inQuotes) {\n          if (code === 0x22/*'\"'*/) {\n            inQuotes = false;\n            end = i;\n          } else if (code === 0x5C/*'\\'*/) {\n            if (start === -1) start = i;\n            isEscaping = mustUnescape = true;\n          } else if (start === -1) {\n            start = i;\n          }\n        } else if (code === 0x22 && header.charCodeAt(i - 1) === 0x3D) {\n          inQuotes = true;\n        } else if (\n          (code === 0x2C/*','*/|| code === 0x3B/*';'*/) &&\n          (start !== -1 || end !== -1)\n        ) {\n          if (start !== -1) {\n            if (end === -1) end = i;\n            forwarded[parameter] = mustUnescape\n              ? decode(header.slice(start, end))\n              : header.slice(start, end);\n          } else {\n            forwarded[parameter] = '';\n          }\n\n          if (code === 0x2C) {\n            output.push(forwarded);\n            forwarded = {};\n          }\n\n          parameter = undefined;\n          start = end = -1;\n        } else {\n          throw new ParseError(unexpectedCharacterMessage(header, i), header);\n        }\n      } else if (code === 0x20 || code === 0x09) {\n        if (end !== -1) continue;\n\n        if (inQuotes) {\n          if (start === -1) start = i;\n        } else if (start !== -1) {\n          end = i;\n        } else {\n          throw new ParseError(unexpectedCharacterMessage(header, i), header);\n        }\n      } else {\n        throw new ParseError(unexpectedCharacterMessage(header, i), header);\n      }\n    }\n  }\n\n  if (\n    parameter === undefined ||\n    inQuotes ||\n    (start === -1 && end === -1) ||\n    code === 0x20 ||\n    code === 0x09\n  ) {\n    throw new ParseError('Unexpected end of input', header);\n  }\n\n  if (start !== -1) {\n    if (end === -1) end = i;\n    forwarded[parameter] = mustUnescape\n      ? decode(header.slice(start, end))\n      : header.slice(start, end);\n  } else {\n    forwarded[parameter] = '';\n  }\n\n  output.push(forwarded);\n  return output;\n}\n\nmodule.exports = parse;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZm9yd2FyZGVkLXBhcnNlL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLFdBQVcsbUJBQU8sQ0FBQyxrQkFBTTs7QUFFekIsaUJBQWlCLG1CQUFPLENBQUMsc0VBQWE7QUFDdEMsWUFBWSxtQkFBTyxDQUFDLHNFQUFhOztBQUVqQztBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYSxRQUFRO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQixhQUFhLFFBQVE7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGtCQUFrQixtQkFBbUI7QUFDckM7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQSxVQUFVO0FBQ1YsbURBQW1EO0FBQ25EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0EsUUFBUTtBQUNSOztBQUVBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvZm9yd2FyZGVkLXBhcnNlL2luZGV4LmpzPzkxNTciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG52YXIgdXRpbCA9IHJlcXVpcmUoJ3V0aWwnKTtcblxudmFyIFBhcnNlRXJyb3IgPSByZXF1aXJlKCcuL2xpYi9lcnJvcicpO1xudmFyIGFzY2lpID0gcmVxdWlyZSgnLi9saWIvYXNjaWknKTtcblxudmFyIGlzRGVsaW1pdGVyID0gYXNjaWkuaXNEZWxpbWl0ZXI7XG52YXIgaXNUb2tlbkNoYXIgPSBhc2NpaS5pc1Rva2VuQ2hhcjtcbnZhciBpc0V4dGVuZGVkID0gYXNjaWkuaXNFeHRlbmRlZDtcbnZhciBpc1ByaW50ID0gYXNjaWkuaXNQcmludDtcblxuLyoqXG4gKiBVbmVzY2FwZSBhIHN0cmluZy5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gc3RyIFRoZSBzdHJpbmcgdG8gdW5lc2NhcGUuXG4gKiBAcmV0dXJucyB7c3RyaW5nfSBBIG5ldyB1bmVzY2FwZWQgc3RyaW5nLlxuICogQHByaXZhdGVcbiAqL1xuZnVuY3Rpb24gZGVjb2RlKHN0cikge1xuICByZXR1cm4gc3RyLnJlcGxhY2UoL1xcXFwoLikvZywgJyQxJyk7XG59XG5cbi8qKlxuICogQnVpbGQgYW4gZXJyb3IgbWVzc2FnZSB3aGVuIGFuIHVuZXhwZWN0ZWQgY2hhcmFjdGVyIGlzIGZvdW5kLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBoZWFkZXIgVGhlIGhlYWRlciBmaWVsZCB2YWx1ZS5cbiAqIEBwYXJhbSB7bnVtYmVyfSBwb3NpdGlvbiBUaGUgcG9zaXRpb24gb2YgdGhlIHVuZXhwZWN0ZWQgY2hhcmFjdGVyLlxuICogQHJldHVybnMge3N0cmluZ30gVGhlIGVycm9yIG1lc3NhZ2UuXG4gKiBAcHJpdmF0ZVxuICovXG5mdW5jdGlvbiB1bmV4cGVjdGVkQ2hhcmFjdGVyTWVzc2FnZShoZWFkZXIsIHBvc2l0aW9uKSB7XG4gIHJldHVybiB1dGlsLmZvcm1hdChcbiAgICBcIlVuZXhwZWN0ZWQgY2hhcmFjdGVyICclcycgYXQgaW5kZXggJWRcIixcbiAgICBoZWFkZXIuY2hhckF0KHBvc2l0aW9uKSxcbiAgICBwb3NpdGlvblxuICApO1xufVxuXG4vKipcbiAqIFBhcnNlIHRoZSBgRm9yd2FyZGVkYCBoZWFkZXIgZmllbGQgdmFsdWUgaW50byBhbiBhcnJheSBvZiBvYmplY3RzLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBoZWFkZXIgVGhlIGhlYWRlciBmaWVsZCB2YWx1ZS5cbiAqIEByZXR1cm5zIHtPYmplY3RbXX1cbiAqIEBwdWJsaWNcbiAqL1xuZnVuY3Rpb24gcGFyc2UoaGVhZGVyKSB7XG4gIHZhciBtdXN0VW5lc2NhcGUgPSBmYWxzZTtcbiAgdmFyIGlzRXNjYXBpbmcgPSBmYWxzZTtcbiAgdmFyIGluUXVvdGVzID0gZmFsc2U7XG4gIHZhciBmb3J3YXJkZWQgPSB7fTtcbiAgdmFyIG91dHB1dCA9IFtdO1xuICB2YXIgc3RhcnQgPSAtMTtcbiAgdmFyIGVuZCA9IC0xO1xuICB2YXIgcGFyYW1ldGVyO1xuICB2YXIgY29kZTtcblxuICBmb3IgKHZhciBpID0gMDsgaSA8IGhlYWRlci5sZW5ndGg7IGkrKykge1xuICAgIGNvZGUgPSBoZWFkZXIuY2hhckNvZGVBdChpKTtcblxuICAgIGlmIChwYXJhbWV0ZXIgPT09IHVuZGVmaW5lZCkge1xuICAgICAgaWYgKFxuICAgICAgICBpICE9PSAwICYmXG4gICAgICAgIHN0YXJ0ID09PSAtMSAmJlxuICAgICAgICAoY29kZSA9PT0gMHgyMC8qJyAnKi8gfHwgY29kZSA9PT0gMHgwOS8qJ1xcdCcqLylcbiAgICAgICkge1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIH1cblxuICAgICAgaWYgKGlzVG9rZW5DaGFyKGNvZGUpKSB7XG4gICAgICAgIGlmIChzdGFydCA9PT0gLTEpIHN0YXJ0ID0gaTtcbiAgICAgIH0gZWxzZSBpZiAoY29kZSA9PT0gMHgzRC8qJz0nKi8gJiYgc3RhcnQgIT09IC0xKSB7XG4gICAgICAgIHBhcmFtZXRlciA9IGhlYWRlci5zbGljZShzdGFydCwgaSkudG9Mb3dlckNhc2UoKTtcbiAgICAgICAgc3RhcnQgPSAtMTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBQYXJzZUVycm9yKHVuZXhwZWN0ZWRDaGFyYWN0ZXJNZXNzYWdlKGhlYWRlciwgaSksIGhlYWRlcik7XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIGlmIChpc0VzY2FwaW5nICYmIChjb2RlID09PSAweDA5IHx8IGlzUHJpbnQoY29kZSkgfHwgaXNFeHRlbmRlZChjb2RlKSkpIHtcbiAgICAgICAgaXNFc2NhcGluZyA9IGZhbHNlO1xuICAgICAgfSBlbHNlIGlmIChpc1Rva2VuQ2hhcihjb2RlKSkge1xuICAgICAgICBpZiAoZW5kICE9PSAtMSkge1xuICAgICAgICAgIHRocm93IG5ldyBQYXJzZUVycm9yKHVuZXhwZWN0ZWRDaGFyYWN0ZXJNZXNzYWdlKGhlYWRlciwgaSksIGhlYWRlcik7XG4gICAgICAgIH1cblxuICAgICAgICBpZiAoc3RhcnQgPT09IC0xKSBzdGFydCA9IGk7XG4gICAgICB9IGVsc2UgaWYgKGlzRGVsaW1pdGVyKGNvZGUpIHx8IGlzRXh0ZW5kZWQoY29kZSkpIHtcbiAgICAgICAgaWYgKGluUXVvdGVzKSB7XG4gICAgICAgICAgaWYgKGNvZGUgPT09IDB4MjIvKidcIicqLykge1xuICAgICAgICAgICAgaW5RdW90ZXMgPSBmYWxzZTtcbiAgICAgICAgICAgIGVuZCA9IGk7XG4gICAgICAgICAgfSBlbHNlIGlmIChjb2RlID09PSAweDVDLyonXFwnKi8pIHtcbiAgICAgICAgICAgIGlmIChzdGFydCA9PT0gLTEpIHN0YXJ0ID0gaTtcbiAgICAgICAgICAgIGlzRXNjYXBpbmcgPSBtdXN0VW5lc2NhcGUgPSB0cnVlO1xuICAgICAgICAgIH0gZWxzZSBpZiAoc3RhcnQgPT09IC0xKSB7XG4gICAgICAgICAgICBzdGFydCA9IGk7XG4gICAgICAgICAgfVxuICAgICAgICB9IGVsc2UgaWYgKGNvZGUgPT09IDB4MjIgJiYgaGVhZGVyLmNoYXJDb2RlQXQoaSAtIDEpID09PSAweDNEKSB7XG4gICAgICAgICAgaW5RdW90ZXMgPSB0cnVlO1xuICAgICAgICB9IGVsc2UgaWYgKFxuICAgICAgICAgIChjb2RlID09PSAweDJDLyonLCcqL3x8IGNvZGUgPT09IDB4M0IvKic7JyovKSAmJlxuICAgICAgICAgIChzdGFydCAhPT0gLTEgfHwgZW5kICE9PSAtMSlcbiAgICAgICAgKSB7XG4gICAgICAgICAgaWYgKHN0YXJ0ICE9PSAtMSkge1xuICAgICAgICAgICAgaWYgKGVuZCA9PT0gLTEpIGVuZCA9IGk7XG4gICAgICAgICAgICBmb3J3YXJkZWRbcGFyYW1ldGVyXSA9IG11c3RVbmVzY2FwZVxuICAgICAgICAgICAgICA/IGRlY29kZShoZWFkZXIuc2xpY2Uoc3RhcnQsIGVuZCkpXG4gICAgICAgICAgICAgIDogaGVhZGVyLnNsaWNlKHN0YXJ0LCBlbmQpO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBmb3J3YXJkZWRbcGFyYW1ldGVyXSA9ICcnO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIGlmIChjb2RlID09PSAweDJDKSB7XG4gICAgICAgICAgICBvdXRwdXQucHVzaChmb3J3YXJkZWQpO1xuICAgICAgICAgICAgZm9yd2FyZGVkID0ge307XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgcGFyYW1ldGVyID0gdW5kZWZpbmVkO1xuICAgICAgICAgIHN0YXJ0ID0gZW5kID0gLTE7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgdGhyb3cgbmV3IFBhcnNlRXJyb3IodW5leHBlY3RlZENoYXJhY3Rlck1lc3NhZ2UoaGVhZGVyLCBpKSwgaGVhZGVyKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIGlmIChjb2RlID09PSAweDIwIHx8IGNvZGUgPT09IDB4MDkpIHtcbiAgICAgICAgaWYgKGVuZCAhPT0gLTEpIGNvbnRpbnVlO1xuXG4gICAgICAgIGlmIChpblF1b3Rlcykge1xuICAgICAgICAgIGlmIChzdGFydCA9PT0gLTEpIHN0YXJ0ID0gaTtcbiAgICAgICAgfSBlbHNlIGlmIChzdGFydCAhPT0gLTEpIHtcbiAgICAgICAgICBlbmQgPSBpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHRocm93IG5ldyBQYXJzZUVycm9yKHVuZXhwZWN0ZWRDaGFyYWN0ZXJNZXNzYWdlKGhlYWRlciwgaSksIGhlYWRlcik7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBQYXJzZUVycm9yKHVuZXhwZWN0ZWRDaGFyYWN0ZXJNZXNzYWdlKGhlYWRlciwgaSksIGhlYWRlcik7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgaWYgKFxuICAgIHBhcmFtZXRlciA9PT0gdW5kZWZpbmVkIHx8XG4gICAgaW5RdW90ZXMgfHxcbiAgICAoc3RhcnQgPT09IC0xICYmIGVuZCA9PT0gLTEpIHx8XG4gICAgY29kZSA9PT0gMHgyMCB8fFxuICAgIGNvZGUgPT09IDB4MDlcbiAgKSB7XG4gICAgdGhyb3cgbmV3IFBhcnNlRXJyb3IoJ1VuZXhwZWN0ZWQgZW5kIG9mIGlucHV0JywgaGVhZGVyKTtcbiAgfVxuXG4gIGlmIChzdGFydCAhPT0gLTEpIHtcbiAgICBpZiAoZW5kID09PSAtMSkgZW5kID0gaTtcbiAgICBmb3J3YXJkZWRbcGFyYW1ldGVyXSA9IG11c3RVbmVzY2FwZVxuICAgICAgPyBkZWNvZGUoaGVhZGVyLnNsaWNlKHN0YXJ0LCBlbmQpKVxuICAgICAgOiBoZWFkZXIuc2xpY2Uoc3RhcnQsIGVuZCk7XG4gIH0gZWxzZSB7XG4gICAgZm9yd2FyZGVkW3BhcmFtZXRlcl0gPSAnJztcbiAgfVxuXG4gIG91dHB1dC5wdXNoKGZvcndhcmRlZCk7XG4gIHJldHVybiBvdXRwdXQ7XG59XG5cbm1vZHVsZS5leHBvcnRzID0gcGFyc2U7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/forwarded-parse/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/forwarded-parse/lib/ascii.js":
/*!***************************************************!*\
  !*** ./node_modules/forwarded-parse/lib/ascii.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\n\n/**\n * Check if a character is a delimiter as defined in section 3.2.6 of RFC 7230.\n *\n *\n * @param {number} code The code of the character to check.\n * @returns {boolean} `true` if the character is a delimiter, else `false`.\n * @public\n */\nfunction isDelimiter(code) {\n  return code === 0x22                // '\"'\n    || code === 0x28                  // '('\n    || code === 0x29                  // ')'\n    || code === 0x2C                  // ','\n    || code === 0x2F                  // '/'\n    || code >= 0x3A && code <= 0x40   // ':', ';', '<', '=', '>', '?' '@'\n    || code >= 0x5B && code <= 0x5D   // '[', '\\', ']'\n    || code === 0x7B                  // '{'\n    || code === 0x7D;                 // '}'\n}\n\n/**\n * Check if a character is allowed in a token as defined in section 3.2.6\n * of RFC 7230.\n *\n * @param {number} code The code of the character to check.\n * @returns {boolean} `true` if the character is allowed, else `false`.\n * @public\n */\nfunction isTokenChar(code) {\n  return code === 0x21                // '!'\n    || code >= 0x23 && code <= 0x27   // '#', '$', '%', '&', '''\n    || code === 0x2A                  // '*'\n    || code === 0x2B                  // '+'\n    || code === 0x2D                  // '-'\n    || code === 0x2E                  // '.'\n    || code >= 0x30 && code <= 0x39   // 0-9\n    || code >= 0x41 && code <= 0x5A   // A-Z\n    || code >= 0x5E && code <= 0x7A   // '^', '_', '`', a-z\n    || code === 0x7C                  // '|'\n    || code === 0x7E;                 // '~'\n}\n\n/**\n * Check if a character is a printable ASCII character.\n *\n * @param {number} code The code of the character to check.\n * @returns {boolean} `true` if `code` is in the %x20-7E range, else `false`.\n * @public\n */\nfunction isPrint(code) {\n  return code >= 0x20 && code <= 0x7E;\n}\n\n/**\n * Check if a character is an extended ASCII character.\n *\n * @param {number} code The code of the character to check.\n * @returns {boolean} `true` if `code` is in the %x80-FF range, else `false`.\n * @public\n */\nfunction isExtended(code) {\n  return code >= 0x80 && code <= 0xFF;\n}\n\nmodule.exports = {\n  isDelimiter: isDelimiter,\n  isTokenChar: isTokenChar,\n  isExtended: isExtended,\n  isPrint: isPrint\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/forwarded-parse/lib/ascii.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/forwarded-parse/lib/error.js":
/*!***************************************************!*\
  !*** ./node_modules/forwarded-parse/lib/error.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar util = __webpack_require__(/*! util */ \"util\");\n\n/**\n * An error thrown by the parser on unexpected input.\n *\n * @constructor\n * @param {string} message The error message.\n * @param {string} input The unexpected input.\n * @public\n */\nfunction ParseError(message, input) {\n  Error.captureStackTrace(this, ParseError);\n\n  this.name = this.constructor.name;\n  this.message = message;\n  this.input = input;\n}\n\nutil.inherits(ParseError, Error);\n\nmodule.exports = ParseError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZm9yd2FyZGVkLXBhcnNlL2xpYi9lcnJvci5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixXQUFXLG1CQUFPLENBQUMsa0JBQU07O0FBRXpCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvZm9yd2FyZGVkLXBhcnNlL2xpYi9lcnJvci5qcz84ZTUxIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxudmFyIHV0aWwgPSByZXF1aXJlKCd1dGlsJyk7XG5cbi8qKlxuICogQW4gZXJyb3IgdGhyb3duIGJ5IHRoZSBwYXJzZXIgb24gdW5leHBlY3RlZCBpbnB1dC5cbiAqXG4gKiBAY29uc3RydWN0b3JcbiAqIEBwYXJhbSB7c3RyaW5nfSBtZXNzYWdlIFRoZSBlcnJvciBtZXNzYWdlLlxuICogQHBhcmFtIHtzdHJpbmd9IGlucHV0IFRoZSB1bmV4cGVjdGVkIGlucHV0LlxuICogQHB1YmxpY1xuICovXG5mdW5jdGlvbiBQYXJzZUVycm9yKG1lc3NhZ2UsIGlucHV0KSB7XG4gIEVycm9yLmNhcHR1cmVTdGFja1RyYWNlKHRoaXMsIFBhcnNlRXJyb3IpO1xuXG4gIHRoaXMubmFtZSA9IHRoaXMuY29uc3RydWN0b3IubmFtZTtcbiAgdGhpcy5tZXNzYWdlID0gbWVzc2FnZTtcbiAgdGhpcy5pbnB1dCA9IGlucHV0O1xufVxuXG51dGlsLmluaGVyaXRzKFBhcnNlRXJyb3IsIEVycm9yKTtcblxubW9kdWxlLmV4cG9ydHMgPSBQYXJzZUVycm9yO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/forwarded-parse/lib/error.js\n");

/***/ })

};
;