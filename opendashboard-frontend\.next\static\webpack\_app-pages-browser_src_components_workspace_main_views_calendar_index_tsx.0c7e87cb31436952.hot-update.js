"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonthView: function() { return /* binding */ MonthView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CalendarSideCard */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarSideCard.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst DayCell = (param)=>{\n    let { date, children, onClick, isCurrentMonth } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable)({\n        id: \"daycell-\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(date, \"yyyy-MM-dd\")),\n        data: {\n            date: date,\n            type: \"daycell\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        onClick: onClick,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[120px] lg:p-3 lg:min-h-[140px]\", isCurrentMonth ? \"bg-white hover:bg-neutral-50\" : \"bg-neutral-100 hover:bg-neutral-200\", isOver && \"bg-blue-50 border-blue-200\"),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DayCell, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable\n    ];\n});\n_c = DayCell;\n// New helper function to process events for the entire month\nconst useMonthEvents = (weeks, events)=>{\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const positionedEventsByWeek = new Map();\n        weeks.forEach((week, weekIndex)=>{\n            const weekStart = week[0];\n            const weekEnd = week[6];\n            const weekEvents = events.filter((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                return eventStart <= weekEnd && eventEnd >= weekStart;\n            });\n            const spanningEvents = [];\n            weekEvents.forEach((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                const startDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, eventStart));\n                const endDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, eventEnd));\n                const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || eventStart < weekStart && eventEnd > weekEnd;\n                if (eventSpansWeek) {\n                    const start = startDayIndex !== -1 ? startDayIndex : 0;\n                    const end = endDayIndex !== -1 ? endDayIndex : 6;\n                    spanningEvents.push({\n                        event,\n                        startDayIndex: start,\n                        endDayIndex: end,\n                        colSpan: end - start + 1\n                    });\n                }\n            });\n            const positioned = [];\n            const rows = [];\n            const sortedEvents = spanningEvents.sort((a, b)=>a.startDayIndex - b.startDayIndex || b.colSpan - a.colSpan);\n            sortedEvents.forEach((event)=>{\n                let assigned = false;\n                for(let i = 0; i < rows.length; i++){\n                    const row = rows[i];\n                    if (!row.some((e)=>event.startDayIndex <= e.endDayIndex && event.endDayIndex >= e.startDayIndex)) {\n                        row.push(event);\n                        positioned.push({\n                            ...event,\n                            row: i\n                        });\n                        assigned = true;\n                        break;\n                    }\n                }\n                if (!assigned) {\n                    rows.push([\n                        event\n                    ]);\n                    positioned.push({\n                        ...event,\n                        row: rows.length - 1\n                    });\n                }\n            });\n            positionedEventsByWeek.set(weekIndex, positioned);\n        });\n        return positionedEventsByWeek;\n    }, [\n        weeks,\n        events\n    ]);\n};\n_s1(useMonthEvents, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\nconst MonthView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, handleEventClick, activeDragData } = param;\n    _s2();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord)();\n    const isInRecordTab = !!maybeRecord;\n    // Memoize month calculations\n    const monthCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const monthStart = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(selectedDate);\n        const monthEnd = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(selectedDate);\n        const startDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(monthStart, {\n            weekStartsOn: 0\n        });\n        const endDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(monthEnd, {\n            weekStartsOn: 0\n        });\n        const days = [];\n        let day = startDay;\n        while(day <= endDay){\n            days.push(day);\n            day = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(day, 1);\n        }\n        const weeks = [];\n        for(let i = 0; i < days.length; i += 7){\n            weeks.push(days.slice(i, i + 7));\n        }\n        return {\n            monthStart,\n            monthEnd,\n            startDay,\n            endDay,\n            days,\n            weeks\n        };\n    }, [\n        selectedDate\n    ]);\n    // Memoize month events\n    const monthEvents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>events.filter((event)=>{\n            const eventStart = new Date(event.start);\n            return eventStart >= monthCalculations.startDay && eventStart <= monthCalculations.endDay;\n        }), [\n        events,\n        monthCalculations.startDay,\n        monthCalculations.endDay\n    ]);\n    const positionedEventsByWeek = useMonthEvents(monthCalculations.weeks, events);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 border-b border-neutral-300 bg-white\",\n                    children: [\n                        \"Sunday\",\n                        \"Monday\",\n                        \"Tuesday\",\n                        \"Wednesday\",\n                        \"Thursday\",\n                        \"Friday\",\n                        \"Saturday\"\n                    ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                            children: dayName.substring(0, 3)\n                        }, dayName, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_7__.NoEvents, {\n                    title: \"No events this month\",\n                    message: \"\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, \"MMMM yyyy\"), \" is completely free. Start planning your month!\"),\n                    showCreateButton: canEditData,\n                    onCreate: ()=>openAddEventForm(selectedDate)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n            lineNumber: 178,\n            columnNumber: 5\n        }, undefined);\n    // Render day cell content\n    const renderDayCellContent = (day, dayEvents)=>{\n        const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n        const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(day);\n        const MAX_VISIBLE_EVENTS = 4;\n        const ROW_HEIGHT = 28;\n        const sortedEvents = dayEvents.sort((a, b)=>a.row - b.row);\n        const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);\n        const hasMore = sortedEvents.length > MAX_VISIBLE_EVENTS;\n        // To fix the gap after the \"+more\" link, we must calculate the container's height\n        // instead of letting it expand. The link is placed at the start of the 5th row,\n        // and we'll give it 20px of height.\n        const maxRow = visibleEvents.reduce((max, event)=>Math.max(max, event.row), -1);\n        const containerHeight = hasMore ? MAX_VISIBLE_EVENTS * ROW_HEIGHT + 20 : (maxRow + 1) * ROW_HEIGHT;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\", isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"),\n                            children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"d\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, undefined),\n                        canEditData && isCurrentMonth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            className: \"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\",\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setTimeout(()=>openAddEventForm(day), 150);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-3 w-3 text-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    style: {\n                        height: \"\".concat(containerHeight, \"px\")\n                    },\n                    children: [\n                        visibleEvents.map((pe)=>{\n                            var _activeDragData_payload;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute\",\n                                style: {\n                                    top: \"\".concat(pe.row * ROW_HEIGHT, \"px\"),\n                                    left: \"2px\",\n                                    width: pe.colSpan > 1 ? \"calc(\".concat(pe.colSpan * 100, \"% + \").concat((pe.colSpan - 1) * 19, \"px)\") : \"calc(100% - 4px)\",\n                                    // zIndex: pe.colSpan > 1 ? 10 : 1,\n                                    zIndex: 10 + pe.row\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__.CalendarEventItem, {\n                                    event: pe.event,\n                                    view: \"month\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        setSelectedEvent(pe.event.id);\n                                        handleEventClick(pe.event);\n                                    },\n                                    isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === pe.event.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, pe.event.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, undefined);\n                        }),\n                        hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-black hover:text-black font-medium text-xs cursor-pointer\",\n                            style: {\n                                position: \"absolute\",\n                                top: \"\".concat(MAX_VISIBLE_EVENTS * ROW_HEIGHT, \"px\"),\n                                left: \"2px\"\n                            },\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setSelectedDate(day);\n                            },\n                            children: [\n                                \"+ \",\n                                sortedEvents.length - MAX_VISIBLE_EVENTS,\n                                \" more\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true);\n    };\n    // Render main view\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-background flex flex-col lg:flex-row\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\",\n                        children: [\n                            \"Sunday\",\n                            \"Monday\",\n                            \"Tuesday\",\n                            \"Wednesday\",\n                            \"Thursday\",\n                            \"Friday\",\n                            \"Saturday\"\n                        ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                                children: dayName.substring(0, 3)\n                            }, dayName, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-7 border-neutral-300 border-b\",\n                            children: monthCalculations.weeks.map((week, weekIndex)=>week.map((day, dayIndex)=>{\n                                    const weekEvents = positionedEventsByWeek.get(weekIndex) || [];\n                                    const dayEvents = weekEvents.filter((pe)=>pe.startDayIndex === dayIndex);\n                                    const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DayCell, {\n                                        date: day,\n                                        isCurrentMonth: isCurrentMonth,\n                                        onClick: ()=>setSelectedDate(day),\n                                        children: renderDayCellContent(day, dayEvents)\n                                    }, \"\".concat(weekIndex, \"-\").concat(dayIndex), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__.CalendarSideCard, {\n                selectedDate: selectedDate,\n                events: events,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 292,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(MonthView, \"LfpiC9GNlfiXFS91dl5Hp92L/ME=\", false, function() {\n    return [\n        _providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord,\n        useMonthEvents\n    ];\n});\n_c1 = MonthView;\nfunction isMultiDay(event) {\n    return !(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(new Date(event.start), new Date(event.end));\n}\nvar _c, _c1;\n$RefreshReg$(_c, \"DayCell\");\n$RefreshReg$(_c1, \"MonthView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL3ZpZXdzL2NhbGVuZGFyL2NvbXBvbmVudHMvTW9udGhWaWV3LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUF1QztBQUMwRTtBQUNoRjtBQUNlO0FBQ087QUFDRTtBQUVMO0FBQ0k7QUFDbEI7QUFDZ0I7QUFDVDtBQWU3QyxNQUFNbUIsVUFBVTtRQUFDLEVBQ2ZDLElBQUksRUFDSkMsUUFBUSxFQUNSQyxPQUFPLEVBQ1BDLGNBQWMsRUFNZjs7SUFDQyxNQUFNLEVBQUVDLFVBQVUsRUFBRUMsTUFBTSxFQUFFLEdBQUdQLDJEQUFZQSxDQUFDO1FBQzFDUSxJQUFJLFdBQXNDLE9BQTNCeEIsMkpBQU1BLENBQUNrQixNQUFNO1FBQzVCTyxNQUFNO1lBQ0pQLE1BQU1BO1lBQ05RLE1BQU07UUFDUjtJQUNGO0lBRUEscUJBQ0UsOERBQUNDO1FBQ0NDLEtBQUtOO1FBQ0xGLFNBQVNBO1FBQ1RTLFdBQVdyQiw4Q0FBRUEsQ0FDWCxnSkFDQWEsaUJBQ0ksaUNBQ0EsdUNBQ0pFLFVBQVU7a0JBR1hKOzs7Ozs7QUFHUDtHQWxDTUY7O1FBVzJCRCx1REFBWUE7OztLQVh2Q0M7QUFvQ04sNkRBQTZEO0FBQzdELE1BQU1hLGlCQUFpQixDQUFDQyxPQUFpQkM7O0lBQ3ZDLE9BQU9qQyw4Q0FBT0EsQ0FBQztRQUNiLE1BQU1rQyx5QkFBeUIsSUFBSUM7UUFFbkNILE1BQU1JLE9BQU8sQ0FBQyxDQUFDQyxNQUFNQztZQUNuQixNQUFNQyxZQUFZRixJQUFJLENBQUMsRUFBRTtZQUN6QixNQUFNRyxVQUFVSCxJQUFJLENBQUMsRUFBRTtZQUV2QixNQUFNSSxhQUFhUixPQUFPUyxNQUFNLENBQUNDLENBQUFBO2dCQUMvQixNQUFNQyxhQUFhLElBQUlDLEtBQUtGLE1BQU1HLEtBQUs7Z0JBQ3ZDLE1BQU1DLFdBQVcsSUFBSUYsS0FBS0YsTUFBTUssR0FBRztnQkFDbkMsT0FBT0osY0FBY0osV0FBV08sWUFBWVI7WUFDOUM7WUFFQSxNQUFNVSxpQkFBd0IsRUFBRTtZQUNoQ1IsV0FBV0wsT0FBTyxDQUFDTyxDQUFBQTtnQkFDakIsTUFBTUMsYUFBYSxJQUFJQyxLQUFLRixNQUFNRyxLQUFLO2dCQUN2QyxNQUFNQyxXQUFXLElBQUlGLEtBQUtGLE1BQU1LLEdBQUc7Z0JBRW5DLE1BQU1FLGdCQUFnQmIsS0FBS2MsU0FBUyxDQUFDQyxDQUFBQSxNQUFPN0MsMkpBQVNBLENBQUM2QyxLQUFLUjtnQkFDM0QsTUFBTVMsY0FBY2hCLEtBQUtjLFNBQVMsQ0FBQ0MsQ0FBQUEsTUFBTzdDLDJKQUFTQSxDQUFDNkMsS0FBS0w7Z0JBRXpELE1BQU1PLGlCQUFpQkosa0JBQWtCLENBQUMsS0FBS0csZ0JBQWdCLENBQUMsS0FBTVQsYUFBYUwsYUFBYVEsV0FBV1A7Z0JBRTNHLElBQUljLGdCQUFnQjtvQkFDbEIsTUFBTVIsUUFBUUksa0JBQWtCLENBQUMsSUFBSUEsZ0JBQWdCO29CQUNyRCxNQUFNRixNQUFNSyxnQkFBZ0IsQ0FBQyxJQUFJQSxjQUFjO29CQUMvQ0osZUFBZU0sSUFBSSxDQUFDO3dCQUNsQlo7d0JBQ0FPLGVBQWVKO3dCQUNmTyxhQUFhTDt3QkFDYlEsU0FBU1IsTUFBTUYsUUFBUTtvQkFDekI7Z0JBQ0Y7WUFDRjtZQUVBLE1BQU1XLGFBQW9CLEVBQUU7WUFDNUIsTUFBTUMsT0FBZ0IsRUFBRTtZQUN4QixNQUFNQyxlQUFlVixlQUFlVyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUQsRUFBRVgsYUFBYSxHQUFHWSxFQUFFWixhQUFhLElBQUlZLEVBQUVOLE9BQU8sR0FBR0ssRUFBRUwsT0FBTztZQUU3R0csYUFBYXZCLE9BQU8sQ0FBQ08sQ0FBQUE7Z0JBQ25CLElBQUlvQixXQUFXO2dCQUNmLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJTixLQUFLTyxNQUFNLEVBQUVELElBQUs7b0JBQ3BDLE1BQU1FLE1BQU1SLElBQUksQ0FBQ00sRUFBRTtvQkFDbkIsSUFBSSxDQUFDRSxJQUFJQyxJQUFJLENBQUNDLENBQUFBLElBQUt6QixNQUFNTyxhQUFhLElBQUlrQixFQUFFZixXQUFXLElBQUlWLE1BQU1VLFdBQVcsSUFBSWUsRUFBRWxCLGFBQWEsR0FBRzt3QkFDaEdnQixJQUFJWCxJQUFJLENBQUNaO3dCQUNUYyxXQUFXRixJQUFJLENBQUM7NEJBQUUsR0FBR1osS0FBSzs0QkFBRXVCLEtBQUtGO3dCQUFFO3dCQUNuQ0QsV0FBVzt3QkFDWDtvQkFDRjtnQkFDRjtnQkFDQSxJQUFJLENBQUNBLFVBQVU7b0JBQ2JMLEtBQUtILElBQUksQ0FBQzt3QkFBQ1o7cUJBQU07b0JBQ2pCYyxXQUFXRixJQUFJLENBQUM7d0JBQUUsR0FBR1osS0FBSzt3QkFBRXVCLEtBQUtSLEtBQUtPLE1BQU0sR0FBRztvQkFBRTtnQkFDbkQ7WUFDRjtZQUNBL0IsdUJBQXVCbUMsR0FBRyxDQUFDL0IsV0FBV21CO1FBQ3hDO1FBRUEsT0FBT3ZCO0lBQ1QsR0FBRztRQUFDRjtRQUFPQztLQUFPO0FBQ3BCO0lBN0RNRjtBQStEQyxNQUFNdUMsWUFBc0M7UUFBQyxFQUNsREMsWUFBWSxFQUNadEMsTUFBTSxFQUNOdUMsYUFBYSxFQUNiQyxnQkFBZ0IsRUFDaEJDLGVBQWUsRUFDZkMsZ0JBQWdCLEVBQ2hCQyxXQUFXLEVBQ1hDLGdCQUFnQixFQUNoQkMsY0FBYyxFQUNmOztJQUNDLE1BQU1DLGNBQWNsRSxpRUFBY0E7SUFFbEMsTUFBTW1FLGdCQUFnQixDQUFDLENBQUNEO0lBRXhCLDZCQUE2QjtJQUM3QixNQUFNRSxvQkFBb0JqRiw4Q0FBT0EsQ0FBQztRQUNoQyxNQUFNa0YsYUFBYWhGLDJKQUFZQSxDQUFDcUU7UUFDaEMsTUFBTVksV0FBV2hGLDJKQUFVQSxDQUFDb0U7UUFDNUIsTUFBTWEsV0FBV2hGLDJKQUFXQSxDQUFDOEUsWUFBWTtZQUFFRyxjQUFjO1FBQUU7UUFDM0QsTUFBTUMsU0FBU2pGLDJKQUFTQSxDQUFDOEUsVUFBVTtZQUFFRSxjQUFjO1FBQUU7UUFFckQsTUFBTUUsT0FBTyxFQUFFO1FBQ2YsSUFBSW5DLE1BQU1nQztRQUNWLE1BQU9oQyxPQUFPa0MsT0FBUTtZQUNwQkMsS0FBS2hDLElBQUksQ0FBQ0g7WUFDVkEsTUFBTTlDLDJKQUFPQSxDQUFDOEMsS0FBSztRQUNyQjtRQUVBLE1BQU1wQixRQUFRLEVBQUU7UUFDaEIsSUFBSyxJQUFJZ0MsSUFBSSxHQUFHQSxJQUFJdUIsS0FBS3RCLE1BQU0sRUFBRUQsS0FBSyxFQUFHO1lBQ3ZDaEMsTUFBTXVCLElBQUksQ0FBQ2dDLEtBQUtDLEtBQUssQ0FBQ3hCLEdBQUdBLElBQUk7UUFDL0I7UUFFQSxPQUFPO1lBQUVrQjtZQUFZQztZQUFVQztZQUFVRTtZQUFRQztZQUFNdkQ7UUFBTTtJQUMvRCxHQUFHO1FBQUN1QztLQUFhO0lBRWpCLHVCQUF1QjtJQUN2QixNQUFNa0IsY0FBY3pGLDhDQUFPQSxDQUFDLElBQzFCaUMsT0FBT1MsTUFBTSxDQUFDQyxDQUFBQTtZQUNaLE1BQU1DLGFBQWEsSUFBSUMsS0FBS0YsTUFBTUcsS0FBSztZQUN2QyxPQUFPRixjQUFjcUMsa0JBQWtCRyxRQUFRLElBQ3hDeEMsY0FBY3FDLGtCQUFrQkssTUFBTTtRQUMvQyxJQUNBO1FBQUNyRDtRQUFRZ0Qsa0JBQWtCRyxRQUFRO1FBQUVILGtCQUFrQkssTUFBTTtLQUFDO0lBR2hFLE1BQU1wRCx5QkFBeUJILGVBQWVrRCxrQkFBa0JqRCxLQUFLLEVBQUVDO0lBRXZFLG9DQUFvQztJQUNwQyxNQUFNeUQsbUJBQW1CLGtCQUN2Qiw4REFBQzlEO1lBQUlFLFdBQVU7OzhCQUNiLDhEQUFDRjtvQkFBSUUsV0FBVTs4QkFDWjt3QkFBQzt3QkFBVTt3QkFBVTt3QkFBVzt3QkFBYTt3QkFBWTt3QkFBVTtxQkFBVyxDQUFDNkQsR0FBRyxDQUFDQyxDQUFBQSx3QkFDbEYsOERBQUNoRTs0QkFBa0JFLFdBQVdyQiw4Q0FBRUEsQ0FDOUIsd0NBQ0E7c0NBRUNtRixRQUFRQyxTQUFTLENBQUMsR0FBRzsyQkFKZEQ7Ozs7Ozs7Ozs7OEJBU2QsOERBQUM3RSwrQ0FBUUE7b0JBQ1ArRSxPQUFNO29CQUNOQyxTQUFTLEdBQXFDLE9BQWxDOUYsMkpBQU1BLENBQUNzRSxjQUFjLGNBQWE7b0JBQzlDeUIsa0JBQWtCcEI7b0JBQ2xCcUIsVUFBVSxJQUFNdEIsaUJBQWlCSjs7Ozs7Ozs7Ozs7O0lBS3ZDLDBCQUEwQjtJQUMxQixNQUFNMkIsdUJBQXVCLENBQUM5QyxLQUFXK0M7UUFDdkMsTUFBTTdFLGlCQUFpQjhCLElBQUlnRCxRQUFRLE9BQU83QixhQUFhNkIsUUFBUTtRQUMvRCxNQUFNQyxlQUFlN0YsMkpBQU9BLENBQUM0QztRQUM3QixNQUFNa0QscUJBQXFCO1FBQzNCLE1BQU1DLGFBQWE7UUFFbkIsTUFBTTVDLGVBQWV3QyxVQUFVdkMsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU1ELEVBQUVLLEdBQUcsR0FBR0osRUFBRUksR0FBRztRQUMzRCxNQUFNc0MsZ0JBQWdCN0MsYUFBYTZCLEtBQUssQ0FBQyxHQUFHYztRQUM1QyxNQUFNRyxVQUFVOUMsYUFBYU0sTUFBTSxHQUFHcUM7UUFFdEMsa0ZBQWtGO1FBQ2xGLGdGQUFnRjtRQUNoRixvQ0FBb0M7UUFDcEMsTUFBTUksU0FBU0YsY0FBY0csTUFBTSxDQUFDLENBQUNDLEtBQUtqRSxRQUFVa0UsS0FBS0QsR0FBRyxDQUFDQSxLQUFLakUsTUFBTXVCLEdBQUcsR0FBRyxDQUFDO1FBQy9FLE1BQU00QyxrQkFBa0JMLFVBQ3BCLHFCQUFzQkYsYUFBYyxLQUNwQyxDQUFDRyxTQUFTLEtBQUtIO1FBRW5CLHFCQUNFOzs4QkFDRSw4REFBQzNFO29CQUFJRSxXQUFVOztzQ0FDYiw4REFBQ2lGOzRCQUFLakYsV0FBV3JCLDhDQUFFQSxDQUNqQixzRkFDQTRGLGVBQWUsd0JBQXdCL0UsaUJBQWlCLG9DQUFvQztzQ0FFM0ZyQiwySkFBTUEsQ0FBQ21ELEtBQUs7Ozs7Ozt3QkFFZHdCLGVBQWV0RCxnQ0FDZCw4REFBQ1oseURBQU1BOzRCQUNMc0csU0FBUTs0QkFDUmxGLFdBQVU7NEJBQ1ZULFNBQVMsQ0FBQytDO2dDQUNSQSxFQUFFNkMsZUFBZTtnQ0FDakJDLFdBQVcsSUFBTXZDLGlCQUFpQnZCLE1BQU07NEJBQzFDO3NDQUVBLDRFQUFDekMsbUdBQVFBO2dDQUFDbUIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBSzFCLDhEQUFDRjtvQkFBSUUsV0FBVTtvQkFBV3FGLE9BQU87d0JBQUVDLFFBQVEsR0FBbUIsT0FBaEJOLGlCQUFnQjtvQkFBSTs7d0JBQy9ETixjQUFjYixHQUFHLENBQUMwQixDQUFBQTtnQ0FzQkR2QztpREFyQmhCLDhEQUFDbEQ7Z0NBRUNFLFdBQVU7Z0NBQ1ZxRixPQUFPO29DQUNMRyxLQUFLLEdBQXVCLE9BQXBCRCxHQUFHbkQsR0FBRyxHQUFHcUMsWUFBVztvQ0FDNUJnQixNQUFNO29DQUNOQyxPQUFPSCxHQUFHN0QsT0FBTyxHQUFHLElBQ2hCLFFBQStCLE9BQXZCNkQsR0FBRzdELE9BQU8sR0FBRyxLQUFJLFFBQTRCLE9BQXRCLENBQUM2RCxHQUFHN0QsT0FBTyxHQUFHLEtBQUssSUFBRyxTQUNyRDtvQ0FDSixtQ0FBbUM7b0NBQ25DaUUsUUFBUSxLQUFLSixHQUFHbkQsR0FBRztnQ0FDckI7MENBRUEsNEVBQUNwRCxpRUFBaUJBO29DQUNoQjZCLE9BQU8wRSxHQUFHMUUsS0FBSztvQ0FDZitFLE1BQUs7b0NBQ0xyRyxTQUFTLENBQUMrQzt3Q0FDUkEsRUFBRTZDLGVBQWU7d0NBQ2pCeEMsaUJBQWlCNEMsR0FBRzFFLEtBQUssQ0FBQ2xCLEVBQUU7d0NBQzVCb0QsaUJBQWlCd0MsR0FBRzFFLEtBQUs7b0NBQzNCO29DQUNBZ0YsWUFBWTdDLENBQUFBLDJCQUFBQSxzQ0FBQUEsMEJBQUFBLGVBQWdCOEMsT0FBTyxjQUF2QjlDLDhDQUFBQSx3QkFBeUJyRCxFQUFFLE1BQUs0RixHQUFHMUUsS0FBSyxDQUFDbEIsRUFBRTs7Ozs7OytCQXBCcEQ0RixHQUFHMUUsS0FBSyxDQUFDbEIsRUFBRTs7Ozs7O3dCQXlCbkJnRix5QkFDQyw4REFBQzdFOzRCQUNDRSxXQUFVOzRCQUNWcUYsT0FBTztnQ0FDTFUsVUFBVTtnQ0FDVlAsS0FBSyxHQUFtQyxPQUFoQ2hCLHFCQUFxQkMsWUFBVztnQ0FDeENnQixNQUFNOzRCQUNSOzRCQUNBbEcsU0FBUyxDQUFDK0M7Z0NBQ1JBLEVBQUU2QyxlQUFlO2dDQUNqQnZDLGdCQUFnQnRCOzRCQUNsQjs7Z0NBQ0Q7Z0NBQ0lPLGFBQWFNLE1BQU0sR0FBR3FDO2dDQUFtQjs7Ozs7Ozs7Ozs7Ozs7O0lBTXhEO0lBRUEsbUJBQW1CO0lBQ25CLHFCQUNFLDhEQUFDMUU7UUFBSUUsV0FBVTs7MEJBQ2IsOERBQUNGO2dCQUFJRSxXQUFVOztrQ0FFYiw4REFBQ0Y7d0JBQUlFLFdBQVU7a0NBQ1o7NEJBQUM7NEJBQVU7NEJBQVU7NEJBQVc7NEJBQWE7NEJBQVk7NEJBQVU7eUJBQVcsQ0FBQzZELEdBQUcsQ0FBQ0MsQ0FBQUEsd0JBQ2xGLDhEQUFDaEU7Z0NBQWtCRSxXQUFXckIsOENBQUVBLENBQzlCLHdDQUNBOzBDQUVDbUYsUUFBUUMsU0FBUyxDQUFDLEdBQUc7K0JBSmREOzs7Ozs7Ozs7O2tDQVVkLDhEQUFDaEYsa0VBQVVBO3dCQUFDa0IsV0FBVTtrQ0FDcEIsNEVBQUNGOzRCQUFJRSxXQUFVO3NDQUNabUQsa0JBQWtCakQsS0FBSyxDQUFDMkQsR0FBRyxDQUFDLENBQUN0RCxNQUFNQyxZQUNsQ0QsS0FBS3NELEdBQUcsQ0FBQyxDQUFDdkMsS0FBSzBFO29DQUNiLE1BQU1yRixhQUFhUCx1QkFBdUI2RixHQUFHLENBQUN6RixjQUFjLEVBQUU7b0NBQzlELE1BQU02RCxZQUFZMUQsV0FBV0MsTUFBTSxDQUFDMkUsQ0FBQUEsS0FBTUEsR0FBR25FLGFBQWEsS0FBSzRFO29DQUMvRCxNQUFNeEcsaUJBQWlCOEIsSUFBSWdELFFBQVEsT0FBTzdCLGFBQWE2QixRQUFRO29DQUUvRCxxQkFDRSw4REFBQ2xGO3dDQUVDQyxNQUFNaUM7d0NBQ045QixnQkFBZ0JBO3dDQUNoQkQsU0FBUyxJQUFNcUQsZ0JBQWdCdEI7a0RBRTlCOEMscUJBQXFCOUMsS0FBSytDO3VDQUx0QixHQUFnQjJCLE9BQWJ4RixXQUFVLEtBQVksT0FBVHdGOzs7OztnQ0FRM0I7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU1SLDhEQUFDOUcsK0RBQWdCQTtnQkFDZnVELGNBQWNBO2dCQUNkdEMsUUFBUUE7Z0JBQ1J1QyxlQUFlQTtnQkFDZkMsa0JBQWtCQTtnQkFDbEJJLGtCQUFrQkE7Ozs7Ozs7Ozs7OztBQUkxQixFQUFFO0lBck5XUDs7UUFXU3pELDZEQUFjQTtRQW9DSGtCOzs7TUEvQ3BCdUM7QUF1TmIsU0FBUzBELFdBQVdyRixLQUFvQjtJQUN0QyxPQUFPLENBQUNwQywySkFBU0EsQ0FBQyxJQUFJc0MsS0FBS0YsTUFBTUcsS0FBSyxHQUFHLElBQUlELEtBQUtGLE1BQU1LLEdBQUc7QUFDN0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvd29ya3NwYWNlL21haW4vdmlld3MvY2FsZW5kYXIvY29tcG9uZW50cy9Nb250aFZpZXcudHN4P2I0NWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IHVzZU1lbW8gfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBmb3JtYXQsIHN0YXJ0T2ZNb250aCwgZW5kT2ZNb250aCwgc3RhcnRPZldlZWssIGVuZE9mV2VlaywgYWRkRGF5cywgaXNTYW1lRGF5LCBpc1RvZGF5IH0gZnJvbSAnZGF0ZS1mbnMnO1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJztcbmltcG9ydCB7IFBsdXNJY29uIH0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJztcbmltcG9ydCB7IFNjcm9sbEFyZWEgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvc2Nyb2xsLWFyZWEnO1xuaW1wb3J0IHsgQ2FsZW5kYXJFdmVudCB9IGZyb20gJ0AvdHlwaW5ncy9wYWdlJztcbmltcG9ydCB7IHVzZU1heWJlUmVjb3JkIH0gZnJvbSAnQC9wcm92aWRlcnMvcmVjb3JkJztcbmltcG9ydCB7IENhbGVuZGFyRXZlbnRJdGVtIH0gZnJvbSAnLi9DYWxlbmRhckV2ZW50SXRlbSc7XG5pbXBvcnQgeyBOb0V2ZW50cyB9IGZyb20gJy4vTm9FdmVudHMnO1xuaW1wb3J0IHsgQ2FsZW5kYXJTaWRlQ2FyZCB9IGZyb20gJy4vQ2FsZW5kYXJTaWRlQ2FyZCc7XG5pbXBvcnQgeyB1c2VEcm9wcGFibGUgfSBmcm9tICdAZG5kLWtpdC9jb3JlJztcblxuXG5pbnRlcmZhY2UgTW9udGhWaWV3UHJvcHMge1xuICBzZWxlY3RlZERhdGU6IERhdGU7XG4gIGV2ZW50czogQ2FsZW5kYXJFdmVudFtdO1xuICBzZWxlY3RlZEV2ZW50OiBzdHJpbmcgfCBudWxsO1xuICBzZXRTZWxlY3RlZEV2ZW50OiAoaWQ6IHN0cmluZykgPT4gdm9pZDtcbiAgc2V0U2VsZWN0ZWREYXRlOiAoZGF0ZTogRGF0ZSkgPT4gdm9pZDtcbiAgb3BlbkFkZEV2ZW50Rm9ybTogKGRhdGU6IERhdGUpID0+IHZvaWQ7XG4gIGNhbkVkaXREYXRhOiBib29sZWFuO1xuICBoYW5kbGVFdmVudENsaWNrOiAoZXZlbnQ6IENhbGVuZGFyRXZlbnQpID0+IHZvaWQ7XG4gIGFjdGl2ZURyYWdEYXRhOiBhbnk7XG59XG5cbmNvbnN0IERheUNlbGwgPSAoe1xuICBkYXRlLFxuICBjaGlsZHJlbixcbiAgb25DbGljayxcbiAgaXNDdXJyZW50TW9udGhcbn06IHtcbiAgZGF0ZTogRGF0ZTtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgb25DbGljazogKCkgPT4gdm9pZDtcbiAgaXNDdXJyZW50TW9udGg6IGJvb2xlYW47XG59KSA9PiB7XG4gIGNvbnN0IHsgc2V0Tm9kZVJlZiwgaXNPdmVyIH0gPSB1c2VEcm9wcGFibGUoe1xuICAgIGlkOiBgZGF5Y2VsbC0ke2Zvcm1hdChkYXRlLCAneXl5eS1NTS1kZCcpfWAsXG4gICAgZGF0YToge1xuICAgICAgZGF0ZTogZGF0ZSxcbiAgICAgIHR5cGU6ICdkYXljZWxsJ1xuICAgIH1cbiAgfSk7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2XG4gICAgICByZWY9e3NldE5vZGVSZWZ9XG4gICAgICBvbkNsaWNrPXtvbkNsaWNrfVxuICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgXCJib3JkZXItYiBib3JkZXItciBib3JkZXItbmV1dHJhbC0zMDAgcmVsYXRpdmUgY3Vyc29yLXBvaW50ZXIgdHJhbnNpdGlvbi1jb2xvcnMgZ3JvdXAgZmxleCBmbGV4LWNvbCBwLTIgbWluLWgtWzEyMHB4XSBsZzpwLTMgbGc6bWluLWgtWzE0MHB4XVwiLFxuICAgICAgICBpc0N1cnJlbnRNb250aFxuICAgICAgICAgID8gXCJiZy13aGl0ZSBob3ZlcjpiZy1uZXV0cmFsLTUwXCJcbiAgICAgICAgICA6IFwiYmctbmV1dHJhbC0xMDAgaG92ZXI6YmctbmV1dHJhbC0yMDBcIixcbiAgICAgICAgaXNPdmVyICYmIFwiYmctYmx1ZS01MCBib3JkZXItYmx1ZS0yMDBcIlxuICAgICAgKX1cbiAgICA+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG4vLyBOZXcgaGVscGVyIGZ1bmN0aW9uIHRvIHByb2Nlc3MgZXZlbnRzIGZvciB0aGUgZW50aXJlIG1vbnRoXG5jb25zdCB1c2VNb250aEV2ZW50cyA9ICh3ZWVrczogRGF0ZVtdW10sIGV2ZW50czogQ2FsZW5kYXJFdmVudFtdKSA9PiB7XG4gIHJldHVybiB1c2VNZW1vKCgpID0+IHtcbiAgICBjb25zdCBwb3NpdGlvbmVkRXZlbnRzQnlXZWVrID0gbmV3IE1hcDxudW1iZXIsIGFueVtdPigpO1xuXG4gICAgd2Vla3MuZm9yRWFjaCgod2Vlaywgd2Vla0luZGV4KSA9PiB7XG4gICAgICBjb25zdCB3ZWVrU3RhcnQgPSB3ZWVrWzBdO1xuICAgICAgY29uc3Qgd2Vla0VuZCA9IHdlZWtbNl07XG5cbiAgICAgIGNvbnN0IHdlZWtFdmVudHMgPSBldmVudHMuZmlsdGVyKGV2ZW50ID0+IHtcbiAgICAgICAgY29uc3QgZXZlbnRTdGFydCA9IG5ldyBEYXRlKGV2ZW50LnN0YXJ0KTtcbiAgICAgICAgY29uc3QgZXZlbnRFbmQgPSBuZXcgRGF0ZShldmVudC5lbmQpO1xuICAgICAgICByZXR1cm4gZXZlbnRTdGFydCA8PSB3ZWVrRW5kICYmIGV2ZW50RW5kID49IHdlZWtTdGFydDtcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCBzcGFubmluZ0V2ZW50czogYW55W10gPSBbXTtcbiAgICAgIHdlZWtFdmVudHMuZm9yRWFjaChldmVudCA9PiB7XG4gICAgICAgIGNvbnN0IGV2ZW50U3RhcnQgPSBuZXcgRGF0ZShldmVudC5zdGFydCk7XG4gICAgICAgIGNvbnN0IGV2ZW50RW5kID0gbmV3IERhdGUoZXZlbnQuZW5kKTtcbiAgICAgICAgXG4gICAgICAgIGNvbnN0IHN0YXJ0RGF5SW5kZXggPSB3ZWVrLmZpbmRJbmRleChkYXkgPT4gaXNTYW1lRGF5KGRheSwgZXZlbnRTdGFydCkpO1xuICAgICAgICBjb25zdCBlbmREYXlJbmRleCA9IHdlZWsuZmluZEluZGV4KGRheSA9PiBpc1NhbWVEYXkoZGF5LCBldmVudEVuZCkpO1xuXG4gICAgICAgIGNvbnN0IGV2ZW50U3BhbnNXZWVrID0gc3RhcnREYXlJbmRleCAhPT0gLTEgfHwgZW5kRGF5SW5kZXggIT09IC0xIHx8IChldmVudFN0YXJ0IDwgd2Vla1N0YXJ0ICYmIGV2ZW50RW5kID4gd2Vla0VuZCk7XG5cbiAgICAgICAgaWYgKGV2ZW50U3BhbnNXZWVrKSB7XG4gICAgICAgICAgY29uc3Qgc3RhcnQgPSBzdGFydERheUluZGV4ICE9PSAtMSA/IHN0YXJ0RGF5SW5kZXggOiAwO1xuICAgICAgICAgIGNvbnN0IGVuZCA9IGVuZERheUluZGV4ICE9PSAtMSA/IGVuZERheUluZGV4IDogNjtcbiAgICAgICAgICBzcGFubmluZ0V2ZW50cy5wdXNoKHtcbiAgICAgICAgICAgIGV2ZW50LFxuICAgICAgICAgICAgc3RhcnREYXlJbmRleDogc3RhcnQsXG4gICAgICAgICAgICBlbmREYXlJbmRleDogZW5kLFxuICAgICAgICAgICAgY29sU3BhbjogZW5kIC0gc3RhcnQgKyAxLFxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgY29uc3QgcG9zaXRpb25lZDogYW55W10gPSBbXTtcbiAgICAgIGNvbnN0IHJvd3M6IGFueVtdW10gPSBbXTtcbiAgICAgIGNvbnN0IHNvcnRlZEV2ZW50cyA9IHNwYW5uaW5nRXZlbnRzLnNvcnQoKGEsIGIpID0+IGEuc3RhcnREYXlJbmRleCAtIGIuc3RhcnREYXlJbmRleCB8fCBiLmNvbFNwYW4gLSBhLmNvbFNwYW4pO1xuXG4gICAgICBzb3J0ZWRFdmVudHMuZm9yRWFjaChldmVudCA9PiB7XG4gICAgICAgIGxldCBhc3NpZ25lZCA9IGZhbHNlO1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHJvd3MubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICBjb25zdCByb3cgPSByb3dzW2ldO1xuICAgICAgICAgIGlmICghcm93LnNvbWUoZSA9PiBldmVudC5zdGFydERheUluZGV4IDw9IGUuZW5kRGF5SW5kZXggJiYgZXZlbnQuZW5kRGF5SW5kZXggPj0gZS5zdGFydERheUluZGV4KSkge1xuICAgICAgICAgICAgcm93LnB1c2goZXZlbnQpO1xuICAgICAgICAgICAgcG9zaXRpb25lZC5wdXNoKHsgLi4uZXZlbnQsIHJvdzogaSB9KTtcbiAgICAgICAgICAgIGFzc2lnbmVkID0gdHJ1ZTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAoIWFzc2lnbmVkKSB7XG4gICAgICAgICAgcm93cy5wdXNoKFtldmVudF0pO1xuICAgICAgICAgIHBvc2l0aW9uZWQucHVzaCh7IC4uLmV2ZW50LCByb3c6IHJvd3MubGVuZ3RoIC0gMSB9KTtcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgICBwb3NpdGlvbmVkRXZlbnRzQnlXZWVrLnNldCh3ZWVrSW5kZXgsIHBvc2l0aW9uZWQpO1xuICAgIH0pO1xuXG4gICAgcmV0dXJuIHBvc2l0aW9uZWRFdmVudHNCeVdlZWs7XG4gIH0sIFt3ZWVrcywgZXZlbnRzXSk7XG59O1xuXG5leHBvcnQgY29uc3QgTW9udGhWaWV3OiBSZWFjdC5GQzxNb250aFZpZXdQcm9wcz4gPSAoe1xuICBzZWxlY3RlZERhdGUsXG4gIGV2ZW50cyxcbiAgc2VsZWN0ZWRFdmVudCxcbiAgc2V0U2VsZWN0ZWRFdmVudCxcbiAgc2V0U2VsZWN0ZWREYXRlLFxuICBvcGVuQWRkRXZlbnRGb3JtLFxuICBjYW5FZGl0RGF0YSxcbiAgaGFuZGxlRXZlbnRDbGljayxcbiAgYWN0aXZlRHJhZ0RhdGEsXG59KSA9PiB7XG4gIGNvbnN0IG1heWJlUmVjb3JkID0gdXNlTWF5YmVSZWNvcmQoKTtcblxuICBjb25zdCBpc0luUmVjb3JkVGFiID0gISFtYXliZVJlY29yZDtcblxuICAvLyBNZW1vaXplIG1vbnRoIGNhbGN1bGF0aW9uc1xuICBjb25zdCBtb250aENhbGN1bGF0aW9ucyA9IHVzZU1lbW8oKCkgPT4ge1xuICAgIGNvbnN0IG1vbnRoU3RhcnQgPSBzdGFydE9mTW9udGgoc2VsZWN0ZWREYXRlKTtcbiAgICBjb25zdCBtb250aEVuZCA9IGVuZE9mTW9udGgoc2VsZWN0ZWREYXRlKTtcbiAgICBjb25zdCBzdGFydERheSA9IHN0YXJ0T2ZXZWVrKG1vbnRoU3RhcnQsIHsgd2Vla1N0YXJ0c09uOiAwIH0pO1xuICAgIGNvbnN0IGVuZERheSA9IGVuZE9mV2Vlayhtb250aEVuZCwgeyB3ZWVrU3RhcnRzT246IDAgfSk7XG5cbiAgICBjb25zdCBkYXlzID0gW107XG4gICAgbGV0IGRheSA9IHN0YXJ0RGF5O1xuICAgIHdoaWxlIChkYXkgPD0gZW5kRGF5KSB7XG4gICAgICBkYXlzLnB1c2goZGF5KTtcbiAgICAgIGRheSA9IGFkZERheXMoZGF5LCAxKTtcbiAgICB9XG5cbiAgICBjb25zdCB3ZWVrcyA9IFtdO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZGF5cy5sZW5ndGg7IGkgKz0gNykge1xuICAgICAgd2Vla3MucHVzaChkYXlzLnNsaWNlKGksIGkgKyA3KSk7XG4gICAgfVxuXG4gICAgcmV0dXJuIHsgbW9udGhTdGFydCwgbW9udGhFbmQsIHN0YXJ0RGF5LCBlbmREYXksIGRheXMsIHdlZWtzIH07XG4gIH0sIFtzZWxlY3RlZERhdGVdKTtcblxuICAvLyBNZW1vaXplIG1vbnRoIGV2ZW50c1xuICBjb25zdCBtb250aEV2ZW50cyA9IHVzZU1lbW8oKCkgPT4gXG4gICAgZXZlbnRzLmZpbHRlcihldmVudCA9PiB7XG4gICAgICBjb25zdCBldmVudFN0YXJ0ID0gbmV3IERhdGUoZXZlbnQuc3RhcnQpO1xuICAgICAgcmV0dXJuIGV2ZW50U3RhcnQgPj0gbW9udGhDYWxjdWxhdGlvbnMuc3RhcnREYXkgJiYgXG4gICAgICAgICAgICAgZXZlbnRTdGFydCA8PSBtb250aENhbGN1bGF0aW9ucy5lbmREYXk7XG4gICAgfSksIFxuICAgIFtldmVudHMsIG1vbnRoQ2FsY3VsYXRpb25zLnN0YXJ0RGF5LCBtb250aENhbGN1bGF0aW9ucy5lbmREYXldXG4gICk7XG5cbiAgY29uc3QgcG9zaXRpb25lZEV2ZW50c0J5V2VlayA9IHVzZU1vbnRoRXZlbnRzKG1vbnRoQ2FsY3VsYXRpb25zLndlZWtzLCBldmVudHMpO1xuXG4gIC8vIFJlbmRlciBlbXB0eSBzdGF0ZSB3aGVuIG5vIGV2ZW50c1xuICBjb25zdCByZW5kZXJFbXB0eVN0YXRlID0gKCkgPT4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBoLWZ1bGwgYmctYmFja2dyb3VuZFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy03IGJvcmRlci1iIGJvcmRlci1uZXV0cmFsLTMwMCBiZy13aGl0ZVwiPlxuICAgICAgICB7WydTdW5kYXknLCAnTW9uZGF5JywgJ1R1ZXNkYXknLCAnV2VkbmVzZGF5JywgJ1RodXJzZGF5JywgJ0ZyaWRheScsICdTYXR1cmRheSddLm1hcChkYXlOYW1lID0+IChcbiAgICAgICAgICA8ZGl2IGtleT17ZGF5TmFtZX0gY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgIFwidGV4dC1jZW50ZXIgZm9udC1zZW1pYm9sZCB0ZXh0LWJsYWNrXCIsXG4gICAgICAgICAgICBcInB5LTIgdGV4dC14c1wiXG4gICAgICAgICAgKX0+XG4gICAgICAgICAgICB7ZGF5TmFtZS5zdWJzdHJpbmcoMCwgMyl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkpfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxOb0V2ZW50c1xuICAgICAgICB0aXRsZT1cIk5vIGV2ZW50cyB0aGlzIG1vbnRoXCJcbiAgICAgICAgbWVzc2FnZT17YCR7Zm9ybWF0KHNlbGVjdGVkRGF0ZSwgJ01NTU0geXl5eScpfSBpcyBjb21wbGV0ZWx5IGZyZWUuIFN0YXJ0IHBsYW5uaW5nIHlvdXIgbW9udGghYH1cbiAgICAgICAgc2hvd0NyZWF0ZUJ1dHRvbj17Y2FuRWRpdERhdGF9XG4gICAgICAgIG9uQ3JlYXRlPXsoKSA9PiBvcGVuQWRkRXZlbnRGb3JtKHNlbGVjdGVkRGF0ZSl9XG4gICAgICAvPlxuICAgIDwvZGl2PlxuICApO1xuXG4gIC8vIFJlbmRlciBkYXkgY2VsbCBjb250ZW50XG4gIGNvbnN0IHJlbmRlckRheUNlbGxDb250ZW50ID0gKGRheTogRGF0ZSwgZGF5RXZlbnRzOiBhbnlbXSkgPT4ge1xuICAgIGNvbnN0IGlzQ3VycmVudE1vbnRoID0gZGF5LmdldE1vbnRoKCkgPT09IHNlbGVjdGVkRGF0ZS5nZXRNb250aCgpO1xuICAgIGNvbnN0IGlzQ3VycmVudERheSA9IGlzVG9kYXkoZGF5KTtcbiAgICBjb25zdCBNQVhfVklTSUJMRV9FVkVOVFMgPSA0O1xuICAgIGNvbnN0IFJPV19IRUlHSFQgPSAyODtcblxuICAgIGNvbnN0IHNvcnRlZEV2ZW50cyA9IGRheUV2ZW50cy5zb3J0KChhLCBiKSA9PiBhLnJvdyAtIGIucm93KTtcbiAgICBjb25zdCB2aXNpYmxlRXZlbnRzID0gc29ydGVkRXZlbnRzLnNsaWNlKDAsIE1BWF9WSVNJQkxFX0VWRU5UUyk7XG4gICAgY29uc3QgaGFzTW9yZSA9IHNvcnRlZEV2ZW50cy5sZW5ndGggPiBNQVhfVklTSUJMRV9FVkVOVFM7XG5cbiAgICAvLyBUbyBmaXggdGhlIGdhcCBhZnRlciB0aGUgXCIrbW9yZVwiIGxpbmssIHdlIG11c3QgY2FsY3VsYXRlIHRoZSBjb250YWluZXIncyBoZWlnaHRcbiAgICAvLyBpbnN0ZWFkIG9mIGxldHRpbmcgaXQgZXhwYW5kLiBUaGUgbGluayBpcyBwbGFjZWQgYXQgdGhlIHN0YXJ0IG9mIHRoZSA1dGggcm93LFxuICAgIC8vIGFuZCB3ZSdsbCBnaXZlIGl0IDIwcHggb2YgaGVpZ2h0LlxuICAgIGNvbnN0IG1heFJvdyA9IHZpc2libGVFdmVudHMucmVkdWNlKChtYXgsIGV2ZW50KSA9PiBNYXRoLm1heChtYXgsIGV2ZW50LnJvdyksIC0xKTtcbiAgICBjb25zdCBjb250YWluZXJIZWlnaHQgPSBoYXNNb3JlXG4gICAgICA/IChNQVhfVklTSUJMRV9FVkVOVFMgKiBST1dfSEVJR0hUKSArIDIwIFxuICAgICAgOiAobWF4Um93ICsgMSkgKiBST1dfSEVJR0hUO1xuXG4gICAgcmV0dXJuIChcbiAgICAgIDw+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTJcIj5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgXCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcm91bmRlZC1mdWxsIGZvbnQtc2VtaWJvbGQgdGV4dC14cyB3LTYgaC02XCIsXG4gICAgICAgICAgICBpc0N1cnJlbnREYXkgPyBcImJnLWJsYWNrIHRleHQtd2hpdGVcIiA6IGlzQ3VycmVudE1vbnRoID8gXCJ0ZXh0LWJsYWNrIGhvdmVyOmJnLW5ldXRyYWwtMTAwXCIgOiBcInRleHQtbmV1dHJhbC00MDBcIlxuICAgICAgICAgICl9PlxuICAgICAgICAgICAge2Zvcm1hdChkYXksICdkJyl9XG4gICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgIHtjYW5FZGl0RGF0YSAmJiBpc0N1cnJlbnRNb250aCAmJiAoXG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWQtZnVsbCBoLTYgdy02IHAtMSBvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1vcGFjaXR5IGhvdmVyOmJnLW5ldXRyYWwtMTAwIGhpZGRlbiBsZzppbmxpbmUtZmxleFwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IG9wZW5BZGRFdmVudEZvcm0oZGF5KSwgMTUwKTtcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFBsdXNJY29uIGNsYXNzTmFtZT1cImgtMyB3LTMgdGV4dC1ibGFja1wiIC8+XG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCIgc3R5bGU9e3sgaGVpZ2h0OiBgJHtjb250YWluZXJIZWlnaHR9cHhgIH19PlxuICAgICAgICAgIHt2aXNpYmxlRXZlbnRzLm1hcChwZSA9PiAoXG4gICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgIGtleT17cGUuZXZlbnQuaWR9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlXCJcbiAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICB0b3A6IGAke3BlLnJvdyAqIFJPV19IRUlHSFR9cHhgLFxuICAgICAgICAgICAgICAgIGxlZnQ6ICcycHgnLFxuICAgICAgICAgICAgICAgIHdpZHRoOiBwZS5jb2xTcGFuID4gMSBcbiAgICAgICAgICAgICAgICAgID8gYGNhbGMoJHtwZS5jb2xTcGFuICogMTAwfSUgKyAkeyhwZS5jb2xTcGFuIC0gMSkgKiAxOX1weClgXG4gICAgICAgICAgICAgICAgICA6ICdjYWxjKDEwMCUgLSA0cHgpJyxcbiAgICAgICAgICAgICAgICAvLyB6SW5kZXg6IHBlLmNvbFNwYW4gPiAxID8gMTAgOiAxLFxuICAgICAgICAgICAgICAgIHpJbmRleDogMTAgKyBwZS5yb3csXG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxDYWxlbmRhckV2ZW50SXRlbVxuICAgICAgICAgICAgICAgIGV2ZW50PXtwZS5ldmVudH1cbiAgICAgICAgICAgICAgICB2aWV3PVwibW9udGhcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRFdmVudChwZS5ldmVudC5pZCk7XG4gICAgICAgICAgICAgICAgICBoYW5kbGVFdmVudENsaWNrKHBlLmV2ZW50KTtcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIGlzRHJhZ2dpbmc9e2FjdGl2ZURyYWdEYXRhPy5wYXlsb2FkPy5pZCA9PT0gcGUuZXZlbnQuaWR9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApKX1cblxuICAgICAgICAgIHtoYXNNb3JlICYmIChcbiAgICAgICAgICAgIDxkaXYgXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmxhY2sgaG92ZXI6dGV4dC1ibGFjayBmb250LW1lZGl1bSB0ZXh0LXhzIGN1cnNvci1wb2ludGVyXCJcbiAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgICAgICAgICAgICAgICB0b3A6IGAke01BWF9WSVNJQkxFX0VWRU5UUyAqIFJPV19IRUlHSFR9cHhgLFxuICAgICAgICAgICAgICAgIGxlZnQ6ICcycHgnLFxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWREYXRlKGRheSk7XG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgICsge3NvcnRlZEV2ZW50cy5sZW5ndGggLSBNQVhfVklTSUJMRV9FVkVOVFN9IG1vcmVcbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC8+XG4gICAgKTtcbiAgfTtcblxuICAvLyBSZW5kZXIgbWFpbiB2aWV3XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJoLWZ1bGwgYmctYmFja2dyb3VuZCBmbGV4IGZsZXgtY29sIGxnOmZsZXgtcm93XCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBmbGV4IGZsZXgtY29sIG1pbi1oLTBcIj5cbiAgICAgICAgey8qIERheSBIZWFkZXJzICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTcgYm9yZGVyLWIgYm9yZGVyLW5ldXRyYWwtMzAwIHN0aWNreSB0b3AtMCB6LTEwIGJnLXdoaXRlXCI+XG4gICAgICAgICAge1snU3VuZGF5JywgJ01vbmRheScsICdUdWVzZGF5JywgJ1dlZG5lc2RheScsICdUaHVyc2RheScsICdGcmlkYXknLCAnU2F0dXJkYXknXS5tYXAoZGF5TmFtZSA9PiAoXG4gICAgICAgICAgICA8ZGl2IGtleT17ZGF5TmFtZX0gY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgXCJ0ZXh0LWNlbnRlciBmb250LXNlbWlib2xkIHRleHQtYmxhY2tcIixcbiAgICAgICAgICAgICAgXCJweS0yIHRleHQteHNcIlxuICAgICAgICAgICAgKX0+XG4gICAgICAgICAgICAgIHtkYXlOYW1lLnN1YnN0cmluZygwLCAzKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogTW9udGggR3JpZCAqL31cbiAgICAgICAgPFNjcm9sbEFyZWEgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy03IGJvcmRlci1uZXV0cmFsLTMwMCBib3JkZXItYlwiPlxuICAgICAgICAgICAge21vbnRoQ2FsY3VsYXRpb25zLndlZWtzLm1hcCgod2Vlaywgd2Vla0luZGV4KSA9PlxuICAgICAgICAgICAgICB3ZWVrLm1hcCgoZGF5LCBkYXlJbmRleCkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IHdlZWtFdmVudHMgPSBwb3NpdGlvbmVkRXZlbnRzQnlXZWVrLmdldCh3ZWVrSW5kZXgpIHx8IFtdO1xuICAgICAgICAgICAgICAgIGNvbnN0IGRheUV2ZW50cyA9IHdlZWtFdmVudHMuZmlsdGVyKHBlID0+IHBlLnN0YXJ0RGF5SW5kZXggPT09IGRheUluZGV4KTtcbiAgICAgICAgICAgICAgICBjb25zdCBpc0N1cnJlbnRNb250aCA9IGRheS5nZXRNb250aCgpID09PSBzZWxlY3RlZERhdGUuZ2V0TW9udGgoKTtcblxuICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICA8RGF5Q2VsbFxuICAgICAgICAgICAgICAgICAgICBrZXk9e2Ake3dlZWtJbmRleH0tJHtkYXlJbmRleH1gfVxuICAgICAgICAgICAgICAgICAgICBkYXRlPXtkYXl9XG4gICAgICAgICAgICAgICAgICAgIGlzQ3VycmVudE1vbnRoPXtpc0N1cnJlbnRNb250aH1cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2VsZWN0ZWREYXRlKGRheSl9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHtyZW5kZXJEYXlDZWxsQ29udGVudChkYXksIGRheUV2ZW50cyl9XG4gICAgICAgICAgICAgICAgICA8L0RheUNlbGw+XG4gICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgfSksXG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L1Njcm9sbEFyZWE+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPENhbGVuZGFyU2lkZUNhcmRcbiAgICAgICAgc2VsZWN0ZWREYXRlPXtzZWxlY3RlZERhdGV9XG4gICAgICAgIGV2ZW50cz17ZXZlbnRzfVxuICAgICAgICBzZWxlY3RlZEV2ZW50PXtzZWxlY3RlZEV2ZW50fVxuICAgICAgICBzZXRTZWxlY3RlZEV2ZW50PXtzZXRTZWxlY3RlZEV2ZW50fVxuICAgICAgICBoYW5kbGVFdmVudENsaWNrPXtoYW5kbGVFdmVudENsaWNrfVxuICAgICAgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmZ1bmN0aW9uIGlzTXVsdGlEYXkoZXZlbnQ6IENhbGVuZGFyRXZlbnQpOiBib29sZWFuIHtcbiAgcmV0dXJuICFpc1NhbWVEYXkobmV3IERhdGUoZXZlbnQuc3RhcnQpLCBuZXcgRGF0ZShldmVudC5lbmQpKTtcbn0iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VNZW1vIiwiZm9ybWF0Iiwic3RhcnRPZk1vbnRoIiwiZW5kT2ZNb250aCIsInN0YXJ0T2ZXZWVrIiwiZW5kT2ZXZWVrIiwiYWRkRGF5cyIsImlzU2FtZURheSIsImlzVG9kYXkiLCJjbiIsIkJ1dHRvbiIsIlBsdXNJY29uIiwiU2Nyb2xsQXJlYSIsInVzZU1heWJlUmVjb3JkIiwiQ2FsZW5kYXJFdmVudEl0ZW0iLCJOb0V2ZW50cyIsIkNhbGVuZGFyU2lkZUNhcmQiLCJ1c2VEcm9wcGFibGUiLCJEYXlDZWxsIiwiZGF0ZSIsImNoaWxkcmVuIiwib25DbGljayIsImlzQ3VycmVudE1vbnRoIiwic2V0Tm9kZVJlZiIsImlzT3ZlciIsImlkIiwiZGF0YSIsInR5cGUiLCJkaXYiLCJyZWYiLCJjbGFzc05hbWUiLCJ1c2VNb250aEV2ZW50cyIsIndlZWtzIiwiZXZlbnRzIiwicG9zaXRpb25lZEV2ZW50c0J5V2VlayIsIk1hcCIsImZvckVhY2giLCJ3ZWVrIiwid2Vla0luZGV4Iiwid2Vla1N0YXJ0Iiwid2Vla0VuZCIsIndlZWtFdmVudHMiLCJmaWx0ZXIiLCJldmVudCIsImV2ZW50U3RhcnQiLCJEYXRlIiwic3RhcnQiLCJldmVudEVuZCIsImVuZCIsInNwYW5uaW5nRXZlbnRzIiwic3RhcnREYXlJbmRleCIsImZpbmRJbmRleCIsImRheSIsImVuZERheUluZGV4IiwiZXZlbnRTcGFuc1dlZWsiLCJwdXNoIiwiY29sU3BhbiIsInBvc2l0aW9uZWQiLCJyb3dzIiwic29ydGVkRXZlbnRzIiwic29ydCIsImEiLCJiIiwiYXNzaWduZWQiLCJpIiwibGVuZ3RoIiwicm93Iiwic29tZSIsImUiLCJzZXQiLCJNb250aFZpZXciLCJzZWxlY3RlZERhdGUiLCJzZWxlY3RlZEV2ZW50Iiwic2V0U2VsZWN0ZWRFdmVudCIsInNldFNlbGVjdGVkRGF0ZSIsIm9wZW5BZGRFdmVudEZvcm0iLCJjYW5FZGl0RGF0YSIsImhhbmRsZUV2ZW50Q2xpY2siLCJhY3RpdmVEcmFnRGF0YSIsIm1heWJlUmVjb3JkIiwiaXNJblJlY29yZFRhYiIsIm1vbnRoQ2FsY3VsYXRpb25zIiwibW9udGhTdGFydCIsIm1vbnRoRW5kIiwic3RhcnREYXkiLCJ3ZWVrU3RhcnRzT24iLCJlbmREYXkiLCJkYXlzIiwic2xpY2UiLCJtb250aEV2ZW50cyIsInJlbmRlckVtcHR5U3RhdGUiLCJtYXAiLCJkYXlOYW1lIiwic3Vic3RyaW5nIiwidGl0bGUiLCJtZXNzYWdlIiwic2hvd0NyZWF0ZUJ1dHRvbiIsIm9uQ3JlYXRlIiwicmVuZGVyRGF5Q2VsbENvbnRlbnQiLCJkYXlFdmVudHMiLCJnZXRNb250aCIsImlzQ3VycmVudERheSIsIk1BWF9WSVNJQkxFX0VWRU5UUyIsIlJPV19IRUlHSFQiLCJ2aXNpYmxlRXZlbnRzIiwiaGFzTW9yZSIsIm1heFJvdyIsInJlZHVjZSIsIm1heCIsIk1hdGgiLCJjb250YWluZXJIZWlnaHQiLCJzcGFuIiwidmFyaWFudCIsInN0b3BQcm9wYWdhdGlvbiIsInNldFRpbWVvdXQiLCJzdHlsZSIsImhlaWdodCIsInBlIiwidG9wIiwibGVmdCIsIndpZHRoIiwiekluZGV4IiwidmlldyIsImlzRHJhZ2dpbmciLCJwYXlsb2FkIiwicG9zaXRpb24iLCJkYXlJbmRleCIsImdldCIsImlzTXVsdGlEYXkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ })

});