"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx":
/*!******************************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx ***!
  \******************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarEventSegment: function() { return /* binding */ CalendarEventSegment; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _utils_color__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/color */ \"(app-pages-browser)/./src/utils/color.ts\");\n/* harmony import */ var _utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/dateUtils */ \"(app-pages-browser)/./src/utils/dateUtils.ts\");\n/* harmony import */ var _utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/multiDayEventUtils */ \"(app-pages-browser)/./src/utils/multiDayEventUtils.ts\");\n/* harmony import */ var _MultiDayEventBadge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./MultiDayEventBadge */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MultiDayEventBadge.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CalendarEventSegment = (param)=>{\n    let { segment, style, onClick, onContextMenu, view = \"month\", isEndOfEvent, isDragging } = param;\n    _s();\n    const dragRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { attributes, listeners, setNodeRef, isDragging: dndIsDragging } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_7__.useDraggable)({\n        id: \"segment-\".concat(segment.id),\n        data: {\n            type: \"segment\",\n            payload: segment\n        }\n    });\n    const combinedIsDragging = isDragging || dndIsDragging;\n    // Memoize event calculations\n    const eventDetails = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const eventHeight = (style === null || style === void 0 ? void 0 : style.height) ? parseInt(style.height.toString().replace(\"px\", \"\")) : null;\n        const showTime = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.shouldShowTimeInSegment)(segment, view);\n        const continuationText = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentContinuationText)(segment);\n        // let formattedTime = null;\n        // if (segment.isAllDay) {\n        //   formattedTime = 'All day';\n        // } else if (showTime) {\n        //   formattedTime = formatEventTime(segment.startTime, view, { shortFormat: true });\n        // }\n        return {\n            eventSize: (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.getEventSize)(eventHeight),\n            showTime,\n            continuationText,\n            formattedTime: showTime ? (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.formatEventTime)(segment.startTime, view, {\n                shortFormat: true\n            }) : null\n        };\n    }, [\n        segment,\n        style,\n        view\n    ]);\n    // Memoize styling\n    const eventStyles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const denimColorInfo = (0,_utils_color__WEBPACK_IMPORTED_MODULE_3__.ColorInfo)(\"Denim\");\n        const stylingClasses = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentStylingClasses)(segment);\n        // Extract RGB values from the rgba string and make it fully opaque\n        const rgbMatch = denimColorInfo.bg.match(/rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)/);\n        const opaqueBackground = rgbMatch ? \"rgb(\".concat(rgbMatch[1], \", \").concat(rgbMatch[2], \", \").concat(rgbMatch[3], \")\") : denimColorInfo.bg;\n        return {\n            style: {\n                ...style,\n                backgroundColor: opaqueBackground,\n                minHeight: \"24px\",\n                // Add subtle shadow for better visual depth\n                boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)\",\n                opacity: combinedIsDragging ? 0.5 : 1\n            },\n            classes: stylingClasses\n        };\n    }, [\n        style,\n        segment,\n        combinedIsDragging\n    ]);\n    // Memoize classes\n    const eventClasses = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const baseClasses = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"select-none text-black text-xs overflow-hidden relative\", !combinedIsDragging && \"cursor-pointer\", eventStyles.classes.roundedCorners, eventStyles.classes.continuationIndicator, eventStyles.classes.opacity, \"p-1\");\n        // Month view or small events\n        if (view === \"month\" || eventDetails.eventSize === \"small\") {\n            return {\n                baseClasses,\n                containerClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-1 flex-nowrap\"),\n                titleClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-medium truncate leading-tight text-xs overflow-hidden\", segment.isMultiDay ? \"max-w-[60%]\" : \"max-w-[70%]\"),\n                timeClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"opacity-75 text-xs flex-shrink-0 text-[0.65rem]\"),\n                continuationClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-xs opacity-60 flex-shrink-0 text-[0.6rem]\")\n            };\n        }\n        // Day and Week views for medium/large events\n        return {\n            baseClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, \"p-2\"),\n            containerClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col\", \"space-y-0.5\"),\n            titleClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-medium truncate leading-tight text-xs overflow-hidden\"),\n            timeClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"opacity-75 text-xs\"),\n            continuationClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-xs opacity-60\")\n        };\n    }, [\n        eventDetails,\n        view,\n        segment.isMultiDay,\n        eventStyles.classes,\n        combinedIsDragging\n    ]);\n    // Render event content based on view and size\n    const renderEventContent = ()=>{\n        const event = segment.originalEvent;\n        // Month view or small events - horizontal layout\n        if (view === \"month\" || eventDetails.eventSize === \"small\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: eventClasses.containerClasses,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: eventClasses.titleClasses,\n                        children: event.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, undefined),\n                    eventDetails.showTime && eventDetails.formattedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: eventClasses.timeClasses,\n                        children: eventDetails.formattedTime\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 13\n                    }, undefined),\n                    segment.isMultiDay && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultiDayEventBadge__WEBPACK_IMPORTED_MODULE_6__.MultiDayEventBadge, {\n                        segment: segment,\n                        view: view,\n                        size: \"small\",\n                        className: eventClasses.continuationClasses,\n                        isEndOfEvent: isEndOfEvent\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, undefined);\n        }\n        // Day and Week views - vertical layout\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: eventClasses.containerClasses,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: eventClasses.titleClasses,\n                    children: [\n                        event.title,\n                        segment.isMultiDay && !eventDetails.showTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultiDayEventBadge__WEBPACK_IMPORTED_MODULE_6__.ContinuationArrow, {\n                            direction: segment.isFirstSegment ? \"right\" : segment.isLastSegment ? \"left\" : \"both\",\n                            className: \"ml-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, undefined),\n                (eventDetails.showTime || segment.isAllDay) && eventDetails.formattedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: eventClasses.timeClasses,\n                    children: [\n                        eventDetails.formattedTime,\n                        segment.isMultiDay && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultiDayEventBadge__WEBPACK_IMPORTED_MODULE_6__.ContinuationArrow, {\n                            direction: segment.isFirstSegment ? \"right\" : segment.isLastSegment ? \"left\" : \"both\",\n                            className: \"ml-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 11\n                }, undefined),\n                segment.isMultiDay && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: eventClasses.continuationClasses,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultiDayEventBadge__WEBPACK_IMPORTED_MODULE_6__.MultiDayEventBadge, {\n                        segment: segment,\n                        view: view,\n                        size: eventDetails.eventSize === \"large\" ? \"medium\" : \"small\",\n                        isEndOfEvent: isEndOfEvent\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n            lineNumber: 170,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"segment-\".concat(segment.id),\n        ref: (node)=>{\n            setNodeRef(node);\n            dragRef.current = node;\n        },\n        style: {\n            ...eventStyles.style,\n            zIndex: combinedIsDragging ? 1000 : \"auto\" // Ensure dragged item is on top\n        },\n        className: eventClasses.baseClasses,\n        onClick: onClick,\n        onContextMenu: onContextMenu,\n        ...listeners,\n        ...attributes,\n        children: renderEventContent()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n        lineNumber: 206,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CalendarEventSegment, \"kiz8nz2pAp5RwnBQHmiQwwayVv8=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_7__.useDraggable\n    ];\n});\n_c = CalendarEventSegment;\nvar _c;\n$RefreshReg$(_c, \"CalendarEventSegment\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL3ZpZXdzL2NhbGVuZGFyL2NvbXBvbmVudHMvQ2FsZW5kYXJFdmVudFNlZ21lbnQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUErQztBQUNkO0FBRVM7QUFJZjtBQU1TO0FBQ3lDO0FBQ2hDO0FBRXRDLE1BQU1hLHVCQUF1QjtRQUFDLEVBQ25DQyxPQUFPLEVBQ1BDLEtBQUssRUFDTEMsT0FBTyxFQUNQQyxhQUFhLEVBQ2JDLE9BQU8sT0FBTyxFQUNkQyxZQUFZLEVBQ1pDLFVBQVUsRUFTWDs7SUFDQyxNQUFNQyxVQUFVbkIsNkNBQU1BLENBQWlCO0lBQ3ZDLE1BQU0sRUFBRW9CLFVBQVUsRUFBRUMsU0FBUyxFQUFFQyxVQUFVLEVBQUVKLFlBQVlLLGFBQWEsRUFBRSxHQUFHYiwyREFBWUEsQ0FBQztRQUNwRmMsSUFBSSxXQUFzQixPQUFYWixRQUFRWSxFQUFFO1FBQ3pCQyxNQUFNO1lBQ0pDLE1BQU07WUFDTkMsU0FBU2Y7UUFDWDtJQUNGO0lBRUEsTUFBTWdCLHFCQUFxQlYsY0FBY0s7SUFFekMsNkJBQTZCO0lBQzdCLE1BQU1NLGVBQWU5Qiw4Q0FBT0EsQ0FBQztRQUMzQixNQUFNK0IsY0FBY2pCLENBQUFBLGtCQUFBQSw0QkFBQUEsTUFBT2tCLE1BQU0sSUFBR0MsU0FBU25CLE1BQU1rQixNQUFNLENBQUNFLFFBQVEsR0FBR0MsT0FBTyxDQUFDLE1BQU0sT0FBTztRQUMxRixNQUFNQyxXQUFXN0Isa0ZBQXVCQSxDQUFDTSxTQUFTSTtRQUNsRCxNQUFNb0IsbUJBQW1CN0IscUZBQTBCQSxDQUFDSztRQUVwRCw0QkFBNEI7UUFDNUIsMEJBQTBCO1FBQzFCLCtCQUErQjtRQUMvQix5QkFBeUI7UUFDekIscUZBQXFGO1FBQ3JGLElBQUk7UUFFSixPQUFPO1lBQ0x5QixXQUFXakMsOERBQVlBLENBQUMwQjtZQUN4Qks7WUFDQUM7WUFDQUUsZUFBZUgsV0FBV2hDLGlFQUFlQSxDQUFDUyxRQUFRMkIsU0FBUyxFQUFFdkIsTUFBTTtnQkFBRXdCLGFBQWE7WUFBSyxLQUFLO1FBQzlGO0lBQ0YsR0FBRztRQUFDNUI7UUFBU0M7UUFBT0c7S0FBSztJQUV6QixrQkFBa0I7SUFDbEIsTUFBTXlCLGNBQWMxQyw4Q0FBT0EsQ0FBQztRQUMxQixNQUFNMkMsaUJBQWlCeEMsdURBQVNBLENBQUM7UUFDakMsTUFBTXlDLGlCQUFpQnRDLG1GQUF3QkEsQ0FBQ087UUFFaEQsbUVBQW1FO1FBQ25FLE1BQU1nQyxXQUFXRixlQUFlRyxFQUFFLENBQUNDLEtBQUssQ0FBQztRQUN6QyxNQUFNQyxtQkFBbUJILFdBQ3JCLE9BQXVCQSxPQUFoQkEsUUFBUSxDQUFDLEVBQUUsRUFBQyxNQUFvQkEsT0FBaEJBLFFBQVEsQ0FBQyxFQUFFLEVBQUMsTUFBZ0IsT0FBWkEsUUFBUSxDQUFDLEVBQUUsRUFBQyxPQUNuREYsZUFBZUcsRUFBRTtRQUVyQixPQUFPO1lBQ0xoQyxPQUFPO2dCQUNMLEdBQUdBLEtBQUs7Z0JBQ1JtQyxpQkFBaUJEO2dCQUNqQkUsV0FBVztnQkFDWCw0Q0FBNEM7Z0JBQzVDQyxXQUFXO2dCQUNYQyxTQUFTdkIscUJBQXFCLE1BQU07WUFDdEM7WUFDQXdCLFNBQVNUO1FBQ1g7SUFDRixHQUFHO1FBQUM5QjtRQUFPRDtRQUFTZ0I7S0FBbUI7SUFFdkMsa0JBQWtCO0lBQ2xCLE1BQU15QixlQUFldEQsOENBQU9BLENBQUM7UUFDM0IsTUFBTXVELGNBQWNyRCw4Q0FBRUEsQ0FDcEIsMkRBQ0EsQ0FBQzJCLHNCQUFzQixrQkFDdkJhLFlBQVlXLE9BQU8sQ0FBQ0csY0FBYyxFQUNsQ2QsWUFBWVcsT0FBTyxDQUFDSSxxQkFBcUIsRUFDekNmLFlBQVlXLE9BQU8sQ0FBQ0QsT0FBTyxFQUMzQjtRQUdGLDZCQUE2QjtRQUM3QixJQUFJbkMsU0FBUyxXQUFXYSxhQUFhUSxTQUFTLEtBQUssU0FBUztZQUMxRCxPQUFPO2dCQUNMaUI7Z0JBQ0FHLGtCQUFrQnhELDhDQUFFQSxDQUNsQjtnQkFFRnlELGNBQWN6RCw4Q0FBRUEsQ0FDZCw4REFDQVcsUUFBUStDLFVBQVUsR0FBRyxnQkFBZ0I7Z0JBRXZDQyxhQUFhM0QsOENBQUVBLENBQ2I7Z0JBRUY0RCxxQkFBcUI1RCw4Q0FBRUEsQ0FDckI7WUFFSjtRQUNGO1FBRUEsNkNBQTZDO1FBQzdDLE9BQU87WUFDTHFELGFBQWFyRCw4Q0FBRUEsQ0FBQ3FELGFBQWE7WUFDN0JHLGtCQUFrQnhELDhDQUFFQSxDQUNsQixpQkFDQTtZQUVGeUQsY0FBY3pELDhDQUFFQSxDQUNkO1lBRUYyRCxhQUFhM0QsOENBQUVBLENBQ2I7WUFFRjRELHFCQUFxQjVELDhDQUFFQSxDQUNyQjtRQUVKO0lBQ0YsR0FBRztRQUFDNEI7UUFBY2I7UUFBTUosUUFBUStDLFVBQVU7UUFBRWxCLFlBQVlXLE9BQU87UUFBRXhCO0tBQW1CO0lBRXBGLDhDQUE4QztJQUM5QyxNQUFNa0MscUJBQXFCO1FBQ3pCLE1BQU1DLFFBQVFuRCxRQUFRb0QsYUFBYTtRQUVuQyxpREFBaUQ7UUFDakQsSUFBSWhELFNBQVMsV0FBV2EsYUFBYVEsU0FBUyxLQUFLLFNBQVM7WUFDMUQscUJBQ0UsOERBQUM0QjtnQkFBSUMsV0FBV2IsYUFBYUksZ0JBQWdCOztrQ0FDM0MsOERBQUNVO3dCQUFLRCxXQUFXYixhQUFhSyxZQUFZO2tDQUFHSyxNQUFNSyxLQUFLOzs7Ozs7b0JBQ3ZEdkMsYUFBYU0sUUFBUSxJQUFJTixhQUFhUyxhQUFhLGtCQUNsRCw4REFBQzZCO3dCQUFLRCxXQUFXYixhQUFhTyxXQUFXO2tDQUN0Qy9CLGFBQWFTLGFBQWE7Ozs7OztvQkFHOUIxQixRQUFRK0MsVUFBVSxrQkFDakIsOERBQUNuRCxtRUFBa0JBO3dCQUNqQkksU0FBU0E7d0JBQ1RJLE1BQU1BO3dCQUNOcUQsTUFBSzt3QkFDTEgsV0FBV2IsYUFBYVEsbUJBQW1CO3dCQUMzQzVDLGNBQWNBOzs7Ozs7Ozs7Ozs7UUFLeEI7UUFFQSx1Q0FBdUM7UUFDdkMscUJBQ0UsOERBQUNnRDtZQUFJQyxXQUFXYixhQUFhSSxnQkFBZ0I7OzhCQUMzQyw4REFBQ1E7b0JBQUlDLFdBQVdiLGFBQWFLLFlBQVk7O3dCQUN0Q0ssTUFBTUssS0FBSzt3QkFDWHhELFFBQVErQyxVQUFVLElBQUksQ0FBQzlCLGFBQWFNLFFBQVEsa0JBQzNDLDhEQUFDMUIsa0VBQWlCQTs0QkFDaEI2RCxXQUFXMUQsUUFBUTJELGNBQWMsR0FBRyxVQUFVM0QsUUFBUTRELGFBQWEsR0FBRyxTQUFTOzRCQUMvRU4sV0FBVTs7Ozs7Ozs7Ozs7O2dCQUlkckMsQ0FBQUEsYUFBYU0sUUFBUSxJQUFJdkIsUUFBUTZELFFBQVEsS0FBSzVDLGFBQWFTLGFBQWEsa0JBQ3hFLDhEQUFDMkI7b0JBQUlDLFdBQVdiLGFBQWFPLFdBQVc7O3dCQUNyQy9CLGFBQWFTLGFBQWE7d0JBQzFCMUIsUUFBUStDLFVBQVUsa0JBQ2pCLDhEQUFDbEQsa0VBQWlCQTs0QkFDaEI2RCxXQUFXMUQsUUFBUTJELGNBQWMsR0FBRyxVQUFVM0QsUUFBUTRELGFBQWEsR0FBRyxTQUFTOzRCQUMvRU4sV0FBVTs7Ozs7Ozs7Ozs7O2dCQUtqQnRELFFBQVErQyxVQUFVLGtCQUNqQiw4REFBQ007b0JBQUlDLFdBQVdiLGFBQWFRLG1CQUFtQjs4QkFDOUMsNEVBQUNyRCxtRUFBa0JBO3dCQUNqQkksU0FBU0E7d0JBQ1RJLE1BQU1BO3dCQUNOcUQsTUFBTXhDLGFBQWFRLFNBQVMsS0FBSyxVQUFVLFdBQVc7d0JBQ3REcEIsY0FBY0E7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTTFCO0lBRUEscUJBQ0UsOERBQUNnRDtRQUNDekMsSUFBSSxXQUFzQixPQUFYWixRQUFRWSxFQUFFO1FBQ3pCa0QsS0FBSyxDQUFDQztZQUNKckQsV0FBV3FEO1lBQ1Z4RCxRQUEwRHlELE9BQU8sR0FBR0Q7UUFDdkU7UUFDQTlELE9BQU87WUFDTCxHQUFHNEIsWUFBWTVCLEtBQUs7WUFDcEJnRSxRQUFRakQscUJBQXFCLE9BQU8sT0FBTyxnQ0FBZ0M7UUFDN0U7UUFDQXNDLFdBQVdiLGFBQWFDLFdBQVc7UUFDbkN4QyxTQUFTQTtRQUNUQyxlQUFlQTtRQUNkLEdBQUdNLFNBQVM7UUFDWixHQUFHRCxVQUFVO2tCQUViMEM7Ozs7OztBQUdQLEVBQUU7R0EvTVduRDs7UUFrQjhERCx1REFBWUE7OztLQWxCMUVDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL3ZpZXdzL2NhbGVuZGFyL2NvbXBvbmVudHMvQ2FsZW5kYXJFdmVudFNlZ21lbnQudHN4P2M4MTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IHVzZU1lbW8sIHVzZVJlZiB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XHJcbmltcG9ydCB7IENhbGVuZGFyRXZlbnQgfSBmcm9tICdAL3R5cGluZ3MvcGFnZSc7XHJcbmltcG9ydCB7IENvbG9ySW5mbyB9IGZyb20gJ0AvdXRpbHMvY29sb3InO1xyXG5pbXBvcnQgeyBcclxuICBmb3JtYXRFdmVudFRpbWUsIFxyXG4gIGdldEV2ZW50U2l6ZSBcclxufSBmcm9tICdAL3V0aWxzL2RhdGVVdGlscyc7XHJcbmltcG9ydCB7IFxyXG4gIEV2ZW50U2VnbWVudCwgXHJcbiAgZ2V0U2VnbWVudFN0eWxpbmdDbGFzc2VzLCBcclxuICBzaG91bGRTaG93VGltZUluU2VnbWVudCwgXHJcbiAgZ2V0U2VnbWVudENvbnRpbnVhdGlvblRleHQgXHJcbn0gZnJvbSAnQC91dGlscy9tdWx0aURheUV2ZW50VXRpbHMnO1xyXG5pbXBvcnQgeyBNdWx0aURheUV2ZW50QmFkZ2UsIENvbnRpbnVhdGlvbkFycm93IH0gZnJvbSAnLi9NdWx0aURheUV2ZW50QmFkZ2UnO1xyXG5pbXBvcnQgeyB1c2VEcmFnZ2FibGUgfSBmcm9tICdAZG5kLWtpdC9jb3JlJztcclxuXHJcbmV4cG9ydCBjb25zdCBDYWxlbmRhckV2ZW50U2VnbWVudCA9ICh7XHJcbiAgc2VnbWVudCxcclxuICBzdHlsZSxcclxuICBvbkNsaWNrLFxyXG4gIG9uQ29udGV4dE1lbnUsXHJcbiAgdmlldyA9ICdtb250aCcsXHJcbiAgaXNFbmRPZkV2ZW50LFxyXG4gIGlzRHJhZ2dpbmdcclxufToge1xyXG4gIHNlZ21lbnQ6IEV2ZW50U2VnbWVudDtcclxuICBzdHlsZT86IFJlYWN0LkNTU1Byb3BlcnRpZXM7XHJcbiAgb25DbGljazogKGU6IFJlYWN0Lk1vdXNlRXZlbnQpID0+IHZvaWQ7XHJcbiAgb25Db250ZXh0TWVudT86IChlOiBSZWFjdC5Nb3VzZUV2ZW50KSA9PiB2b2lkO1xyXG4gIHZpZXc/OiAnZGF5JyB8ICd3ZWVrJyB8ICdtb250aCc7XHJcbiAgaXNFbmRPZkV2ZW50PzogYm9vbGVhbjtcclxuICBpc0RyYWdnaW5nPzogYm9vbGVhbjtcclxufSkgPT4ge1xyXG4gIGNvbnN0IGRyYWdSZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpO1xyXG4gIGNvbnN0IHsgYXR0cmlidXRlcywgbGlzdGVuZXJzLCBzZXROb2RlUmVmLCBpc0RyYWdnaW5nOiBkbmRJc0RyYWdnaW5nIH0gPSB1c2VEcmFnZ2FibGUoe1xyXG4gICAgaWQ6IGBzZWdtZW50LSR7c2VnbWVudC5pZH1gLFxyXG4gICAgZGF0YToge1xyXG4gICAgICB0eXBlOiAnc2VnbWVudCcsXHJcbiAgICAgIHBheWxvYWQ6IHNlZ21lbnQsXHJcbiAgICB9LFxyXG4gIH0pO1xyXG5cclxuICBjb25zdCBjb21iaW5lZElzRHJhZ2dpbmcgPSBpc0RyYWdnaW5nIHx8IGRuZElzRHJhZ2dpbmc7XHJcblxyXG4gIC8vIE1lbW9pemUgZXZlbnQgY2FsY3VsYXRpb25zXHJcbiAgY29uc3QgZXZlbnREZXRhaWxzID0gdXNlTWVtbygoKSA9PiB7XHJcbiAgICBjb25zdCBldmVudEhlaWdodCA9IHN0eWxlPy5oZWlnaHQgPyBwYXJzZUludChzdHlsZS5oZWlnaHQudG9TdHJpbmcoKS5yZXBsYWNlKCdweCcsICcnKSkgOiBudWxsO1xyXG4gICAgY29uc3Qgc2hvd1RpbWUgPSBzaG91bGRTaG93VGltZUluU2VnbWVudChzZWdtZW50LCB2aWV3KTtcclxuICAgIGNvbnN0IGNvbnRpbnVhdGlvblRleHQgPSBnZXRTZWdtZW50Q29udGludWF0aW9uVGV4dChzZWdtZW50KTtcclxuICAgIFxyXG4gICAgLy8gbGV0IGZvcm1hdHRlZFRpbWUgPSBudWxsO1xyXG4gICAgLy8gaWYgKHNlZ21lbnQuaXNBbGxEYXkpIHtcclxuICAgIC8vICAgZm9ybWF0dGVkVGltZSA9ICdBbGwgZGF5JztcclxuICAgIC8vIH0gZWxzZSBpZiAoc2hvd1RpbWUpIHtcclxuICAgIC8vICAgZm9ybWF0dGVkVGltZSA9IGZvcm1hdEV2ZW50VGltZShzZWdtZW50LnN0YXJ0VGltZSwgdmlldywgeyBzaG9ydEZvcm1hdDogdHJ1ZSB9KTtcclxuICAgIC8vIH1cclxuXHJcbiAgICByZXR1cm4geyBcclxuICAgICAgZXZlbnRTaXplOiBnZXRFdmVudFNpemUoZXZlbnRIZWlnaHQpLFxyXG4gICAgICBzaG93VGltZSxcclxuICAgICAgY29udGludWF0aW9uVGV4dCxcclxuICAgICAgZm9ybWF0dGVkVGltZTogc2hvd1RpbWUgPyBmb3JtYXRFdmVudFRpbWUoc2VnbWVudC5zdGFydFRpbWUsIHZpZXcsIHsgc2hvcnRGb3JtYXQ6IHRydWUgfSkgOiBudWxsXHJcbiAgICB9O1xyXG4gIH0sIFtzZWdtZW50LCBzdHlsZSwgdmlld10pO1xyXG5cclxuICAvLyBNZW1vaXplIHN0eWxpbmdcclxuICBjb25zdCBldmVudFN0eWxlcyA9IHVzZU1lbW8oKCkgPT4ge1xyXG4gICAgY29uc3QgZGVuaW1Db2xvckluZm8gPSBDb2xvckluZm8oJ0RlbmltJyk7XHJcbiAgICBjb25zdCBzdHlsaW5nQ2xhc3NlcyA9IGdldFNlZ21lbnRTdHlsaW5nQ2xhc3NlcyhzZWdtZW50KTtcclxuICAgIFxyXG4gICAgLy8gRXh0cmFjdCBSR0IgdmFsdWVzIGZyb20gdGhlIHJnYmEgc3RyaW5nIGFuZCBtYWtlIGl0IGZ1bGx5IG9wYXF1ZVxyXG4gICAgY29uc3QgcmdiTWF0Y2ggPSBkZW5pbUNvbG9ySW5mby5iZy5tYXRjaCgvcmdiYT9cXCgoXFxkKyksXFxzKihcXGQrKSxcXHMqKFxcZCspLyk7XHJcbiAgICBjb25zdCBvcGFxdWVCYWNrZ3JvdW5kID0gcmdiTWF0Y2ggXHJcbiAgICAgID8gYHJnYigke3JnYk1hdGNoWzFdfSwgJHtyZ2JNYXRjaFsyXX0sICR7cmdiTWF0Y2hbM119KWBcclxuICAgICAgOiBkZW5pbUNvbG9ySW5mby5iZztcclxuICAgIFxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgc3R5bGU6IHtcclxuICAgICAgICAuLi5zdHlsZSxcclxuICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IG9wYXF1ZUJhY2tncm91bmQsXHJcbiAgICAgICAgbWluSGVpZ2h0OiAnMjRweCcsIC8vIEFkanVzdGVkIGZvciBjb25zaXN0ZW5jeVxyXG4gICAgICAgIC8vIEFkZCBzdWJ0bGUgc2hhZG93IGZvciBiZXR0ZXIgdmlzdWFsIGRlcHRoXHJcbiAgICAgICAgYm94U2hhZG93OiAnMCAxcHggM3B4IHJnYmEoMCwgMCwgMCwgMC4xMiksIDAgMXB4IDJweCByZ2JhKDAsIDAsIDAsIDAuMjQpJyxcclxuICAgICAgICBvcGFjaXR5OiBjb21iaW5lZElzRHJhZ2dpbmcgPyAwLjUgOiAxLFxyXG4gICAgICB9LFxyXG4gICAgICBjbGFzc2VzOiBzdHlsaW5nQ2xhc3Nlc1xyXG4gICAgfTtcclxuICB9LCBbc3R5bGUsIHNlZ21lbnQsIGNvbWJpbmVkSXNEcmFnZ2luZ10pO1xyXG5cclxuICAvLyBNZW1vaXplIGNsYXNzZXNcclxuICBjb25zdCBldmVudENsYXNzZXMgPSB1c2VNZW1vKCgpID0+IHtcclxuICAgIGNvbnN0IGJhc2VDbGFzc2VzID0gY24oXHJcbiAgICAgIFwic2VsZWN0LW5vbmUgdGV4dC1ibGFjayB0ZXh0LXhzIG92ZXJmbG93LWhpZGRlbiByZWxhdGl2ZVwiLFxyXG4gICAgICAhY29tYmluZWRJc0RyYWdnaW5nICYmIFwiY3Vyc29yLXBvaW50ZXJcIixcclxuICAgICAgZXZlbnRTdHlsZXMuY2xhc3Nlcy5yb3VuZGVkQ29ybmVycyxcclxuICAgICAgZXZlbnRTdHlsZXMuY2xhc3Nlcy5jb250aW51YXRpb25JbmRpY2F0b3IsXHJcbiAgICAgIGV2ZW50U3R5bGVzLmNsYXNzZXMub3BhY2l0eSxcclxuICAgICAgXCJwLTFcIixcclxuICAgICk7XHJcblxyXG4gICAgLy8gTW9udGggdmlldyBvciBzbWFsbCBldmVudHNcclxuICAgIGlmICh2aWV3ID09PSAnbW9udGgnIHx8IGV2ZW50RGV0YWlscy5ldmVudFNpemUgPT09ICdzbWFsbCcpIHtcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBiYXNlQ2xhc3NlcyxcclxuICAgICAgICBjb250YWluZXJDbGFzc2VzOiBjbihcclxuICAgICAgICAgIFwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xIGZsZXgtbm93cmFwXCJcclxuICAgICAgICApLFxyXG4gICAgICAgIHRpdGxlQ2xhc3NlczogY24oXHJcbiAgICAgICAgICBcImZvbnQtbWVkaXVtIHRydW5jYXRlIGxlYWRpbmctdGlnaHQgdGV4dC14cyBvdmVyZmxvdy1oaWRkZW5cIixcclxuICAgICAgICAgIHNlZ21lbnQuaXNNdWx0aURheSA/IFwibWF4LXctWzYwJV1cIiA6IFwibWF4LXctWzcwJV1cIlxyXG4gICAgICAgICksXHJcbiAgICAgICAgdGltZUNsYXNzZXM6IGNuKFxyXG4gICAgICAgICAgXCJvcGFjaXR5LTc1IHRleHQteHMgZmxleC1zaHJpbmstMCB0ZXh0LVswLjY1cmVtXVwiXHJcbiAgICAgICAgKSxcclxuICAgICAgICBjb250aW51YXRpb25DbGFzc2VzOiBjbihcclxuICAgICAgICAgIFwidGV4dC14cyBvcGFjaXR5LTYwIGZsZXgtc2hyaW5rLTAgdGV4dC1bMC42cmVtXVwiXHJcbiAgICAgICAgKVxyXG4gICAgICB9O1xyXG4gICAgfVxyXG5cclxuICAgIC8vIERheSBhbmQgV2VlayB2aWV3cyBmb3IgbWVkaXVtL2xhcmdlIGV2ZW50c1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgYmFzZUNsYXNzZXM6IGNuKGJhc2VDbGFzc2VzLCBcInAtMlwiKSxcclxuICAgICAgY29udGFpbmVyQ2xhc3NlczogY24oXHJcbiAgICAgICAgXCJmbGV4IGZsZXgtY29sXCIsXHJcbiAgICAgICAgXCJzcGFjZS15LTAuNVwiXHJcbiAgICAgICksXHJcbiAgICAgIHRpdGxlQ2xhc3NlczogY24oXHJcbiAgICAgICAgXCJmb250LW1lZGl1bSB0cnVuY2F0ZSBsZWFkaW5nLXRpZ2h0IHRleHQteHMgb3ZlcmZsb3ctaGlkZGVuXCJcclxuICAgICAgKSxcclxuICAgICAgdGltZUNsYXNzZXM6IGNuKFxyXG4gICAgICAgIFwib3BhY2l0eS03NSB0ZXh0LXhzXCJcclxuICAgICAgKSxcclxuICAgICAgY29udGludWF0aW9uQ2xhc3NlczogY24oXHJcbiAgICAgICAgXCJ0ZXh0LXhzIG9wYWNpdHktNjBcIlxyXG4gICAgICApXHJcbiAgICB9O1xyXG4gIH0sIFtldmVudERldGFpbHMsIHZpZXcsIHNlZ21lbnQuaXNNdWx0aURheSwgZXZlbnRTdHlsZXMuY2xhc3NlcywgY29tYmluZWRJc0RyYWdnaW5nXSk7XHJcblxyXG4gIC8vIFJlbmRlciBldmVudCBjb250ZW50IGJhc2VkIG9uIHZpZXcgYW5kIHNpemVcclxuICBjb25zdCByZW5kZXJFdmVudENvbnRlbnQgPSAoKSA9PiB7XHJcbiAgICBjb25zdCBldmVudCA9IHNlZ21lbnQub3JpZ2luYWxFdmVudDtcclxuICAgIFxyXG4gICAgLy8gTW9udGggdmlldyBvciBzbWFsbCBldmVudHMgLSBob3Jpem9udGFsIGxheW91dFxyXG4gICAgaWYgKHZpZXcgPT09ICdtb250aCcgfHwgZXZlbnREZXRhaWxzLmV2ZW50U2l6ZSA9PT0gJ3NtYWxsJykge1xyXG4gICAgICByZXR1cm4gKFxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPXtldmVudENsYXNzZXMuY29udGFpbmVyQ2xhc3Nlc30+XHJcbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2V2ZW50Q2xhc3Nlcy50aXRsZUNsYXNzZXN9PntldmVudC50aXRsZX08L3NwYW4+XHJcbiAgICAgICAgICB7ZXZlbnREZXRhaWxzLnNob3dUaW1lICYmIGV2ZW50RGV0YWlscy5mb3JtYXR0ZWRUaW1lICYmIChcclxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtldmVudENsYXNzZXMudGltZUNsYXNzZXN9PlxyXG4gICAgICAgICAgICAgIHtldmVudERldGFpbHMuZm9ybWF0dGVkVGltZX1cclxuICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgKX1cclxuICAgICAgICAgIHtzZWdtZW50LmlzTXVsdGlEYXkgJiYgKFxyXG4gICAgICAgICAgICA8TXVsdGlEYXlFdmVudEJhZGdlXHJcbiAgICAgICAgICAgICAgc2VnbWVudD17c2VnbWVudH1cclxuICAgICAgICAgICAgICB2aWV3PXt2aWV3fVxyXG4gICAgICAgICAgICAgIHNpemU9XCJzbWFsbFwiXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtldmVudENsYXNzZXMuY29udGludWF0aW9uQ2xhc3Nlc31cclxuICAgICAgICAgICAgICBpc0VuZE9mRXZlbnQ9e2lzRW5kT2ZFdmVudH1cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gRGF5IGFuZCBXZWVrIHZpZXdzIC0gdmVydGljYWwgbGF5b3V0XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17ZXZlbnRDbGFzc2VzLmNvbnRhaW5lckNsYXNzZXN9PlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPXtldmVudENsYXNzZXMudGl0bGVDbGFzc2VzfT5cclxuICAgICAgICAgIHtldmVudC50aXRsZX1cclxuICAgICAgICAgIHtzZWdtZW50LmlzTXVsdGlEYXkgJiYgIWV2ZW50RGV0YWlscy5zaG93VGltZSAmJiAoXHJcbiAgICAgICAgICAgIDxDb250aW51YXRpb25BcnJvd1xyXG4gICAgICAgICAgICAgIGRpcmVjdGlvbj17c2VnbWVudC5pc0ZpcnN0U2VnbWVudCA/ICdyaWdodCcgOiBzZWdtZW50LmlzTGFzdFNlZ21lbnQgPyAnbGVmdCcgOiAnYm90aCd9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWwtMVwiXHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICApfVxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICAgIHsoZXZlbnREZXRhaWxzLnNob3dUaW1lIHx8IHNlZ21lbnQuaXNBbGxEYXkpICYmIGV2ZW50RGV0YWlscy5mb3JtYXR0ZWRUaW1lICYmIChcclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtldmVudENsYXNzZXMudGltZUNsYXNzZXN9PlxyXG4gICAgICAgICAgICB7ZXZlbnREZXRhaWxzLmZvcm1hdHRlZFRpbWV9XHJcbiAgICAgICAgICAgIHtzZWdtZW50LmlzTXVsdGlEYXkgJiYgKFxyXG4gICAgICAgICAgICAgIDxDb250aW51YXRpb25BcnJvd1xyXG4gICAgICAgICAgICAgICAgZGlyZWN0aW9uPXtzZWdtZW50LmlzRmlyc3RTZWdtZW50ID8gJ3JpZ2h0JyA6IHNlZ21lbnQuaXNMYXN0U2VnbWVudCA/ICdsZWZ0JyA6ICdib3RoJ31cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1sLTFcIlxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApfVxyXG4gICAgICAgIHtzZWdtZW50LmlzTXVsdGlEYXkgJiYgKFxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2V2ZW50Q2xhc3Nlcy5jb250aW51YXRpb25DbGFzc2VzfT5cclxuICAgICAgICAgICAgPE11bHRpRGF5RXZlbnRCYWRnZVxyXG4gICAgICAgICAgICAgIHNlZ21lbnQ9e3NlZ21lbnR9XHJcbiAgICAgICAgICAgICAgdmlldz17dmlld31cclxuICAgICAgICAgICAgICBzaXplPXtldmVudERldGFpbHMuZXZlbnRTaXplID09PSAnbGFyZ2UnID8gJ21lZGl1bScgOiAnc21hbGwnfVxyXG4gICAgICAgICAgICAgIGlzRW5kT2ZFdmVudD17aXNFbmRPZkV2ZW50fVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgKX1cclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2XHJcbiAgICAgIGlkPXtgc2VnbWVudC0ke3NlZ21lbnQuaWR9YH1cclxuICAgICAgcmVmPXsobm9kZSkgPT4ge1xyXG4gICAgICAgIHNldE5vZGVSZWYobm9kZSk7XHJcbiAgICAgICAgKGRyYWdSZWYgYXMgUmVhY3QuTXV0YWJsZVJlZk9iamVjdDxIVE1MRGl2RWxlbWVudCB8IG51bGw+KS5jdXJyZW50ID0gbm9kZTtcclxuICAgICAgfX1cclxuICAgICAgc3R5bGU9e3tcclxuICAgICAgICAuLi5ldmVudFN0eWxlcy5zdHlsZSxcclxuICAgICAgICB6SW5kZXg6IGNvbWJpbmVkSXNEcmFnZ2luZyA/IDEwMDAgOiAnYXV0bycgLy8gRW5zdXJlIGRyYWdnZWQgaXRlbSBpcyBvbiB0b3BcclxuICAgICAgfX1cclxuICAgICAgY2xhc3NOYW1lPXtldmVudENsYXNzZXMuYmFzZUNsYXNzZXN9XHJcbiAgICAgIG9uQ2xpY2s9e29uQ2xpY2t9XHJcbiAgICAgIG9uQ29udGV4dE1lbnU9e29uQ29udGV4dE1lbnV9XHJcbiAgICAgIHsuLi5saXN0ZW5lcnN9XHJcbiAgICAgIHsuLi5hdHRyaWJ1dGVzfVxyXG4gICAgPlxyXG4gICAgICB7cmVuZGVyRXZlbnRDb250ZW50KCl9XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59OyAiXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VNZW1vIiwidXNlUmVmIiwiY24iLCJDb2xvckluZm8iLCJmb3JtYXRFdmVudFRpbWUiLCJnZXRFdmVudFNpemUiLCJnZXRTZWdtZW50U3R5bGluZ0NsYXNzZXMiLCJzaG91bGRTaG93VGltZUluU2VnbWVudCIsImdldFNlZ21lbnRDb250aW51YXRpb25UZXh0IiwiTXVsdGlEYXlFdmVudEJhZGdlIiwiQ29udGludWF0aW9uQXJyb3ciLCJ1c2VEcmFnZ2FibGUiLCJDYWxlbmRhckV2ZW50U2VnbWVudCIsInNlZ21lbnQiLCJzdHlsZSIsIm9uQ2xpY2siLCJvbkNvbnRleHRNZW51IiwidmlldyIsImlzRW5kT2ZFdmVudCIsImlzRHJhZ2dpbmciLCJkcmFnUmVmIiwiYXR0cmlidXRlcyIsImxpc3RlbmVycyIsInNldE5vZGVSZWYiLCJkbmRJc0RyYWdnaW5nIiwiaWQiLCJkYXRhIiwidHlwZSIsInBheWxvYWQiLCJjb21iaW5lZElzRHJhZ2dpbmciLCJldmVudERldGFpbHMiLCJldmVudEhlaWdodCIsImhlaWdodCIsInBhcnNlSW50IiwidG9TdHJpbmciLCJyZXBsYWNlIiwic2hvd1RpbWUiLCJjb250aW51YXRpb25UZXh0IiwiZXZlbnRTaXplIiwiZm9ybWF0dGVkVGltZSIsInN0YXJ0VGltZSIsInNob3J0Rm9ybWF0IiwiZXZlbnRTdHlsZXMiLCJkZW5pbUNvbG9ySW5mbyIsInN0eWxpbmdDbGFzc2VzIiwicmdiTWF0Y2giLCJiZyIsIm1hdGNoIiwib3BhcXVlQmFja2dyb3VuZCIsImJhY2tncm91bmRDb2xvciIsIm1pbkhlaWdodCIsImJveFNoYWRvdyIsIm9wYWNpdHkiLCJjbGFzc2VzIiwiZXZlbnRDbGFzc2VzIiwiYmFzZUNsYXNzZXMiLCJyb3VuZGVkQ29ybmVycyIsImNvbnRpbnVhdGlvbkluZGljYXRvciIsImNvbnRhaW5lckNsYXNzZXMiLCJ0aXRsZUNsYXNzZXMiLCJpc011bHRpRGF5IiwidGltZUNsYXNzZXMiLCJjb250aW51YXRpb25DbGFzc2VzIiwicmVuZGVyRXZlbnRDb250ZW50IiwiZXZlbnQiLCJvcmlnaW5hbEV2ZW50IiwiZGl2IiwiY2xhc3NOYW1lIiwic3BhbiIsInRpdGxlIiwic2l6ZSIsImRpcmVjdGlvbiIsImlzRmlyc3RTZWdtZW50IiwiaXNMYXN0U2VnbWVudCIsImlzQWxsRGF5IiwicmVmIiwibm9kZSIsImN1cnJlbnQiLCJ6SW5kZXgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\n"));

/***/ })

});