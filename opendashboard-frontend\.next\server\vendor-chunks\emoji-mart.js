"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/emoji-mart";
exports.ids = ["vendor-chunks/emoji-mart"];
exports.modules = {

/***/ "(ssr)/./node_modules/emoji-mart/dist/module.js":
/*!************************************************!*\
  !*** ./node_modules/emoji-mart/dist/module.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Data: () => (/* binding */ $7adb23b0109cc36a$export$2d0294657ab35f1b),\n/* harmony export */   Emoji: () => (/* binding */ $331b4160623139bf$export$2e2bcd8739ae039),\n/* harmony export */   FrequentlyUsed: () => (/* binding */ $b22cfd0a55410b4f$export$2e2bcd8739ae039),\n/* harmony export */   I18n: () => (/* binding */ $7adb23b0109cc36a$export$dbe3113d60765c1a),\n/* harmony export */   Picker: () => (/* binding */ $efa000751917694d$export$2e2bcd8739ae039),\n/* harmony export */   SafeFlags: () => (/* binding */ $e6eae5155b87f591$export$bcb25aa587e9cb13),\n/* harmony export */   SearchIndex: () => (/* binding */ $c4d155af13ad4d4b$export$2e2bcd8739ae039),\n/* harmony export */   Store: () => (/* binding */ $f72b75cf796873c7$export$2e2bcd8739ae039),\n/* harmony export */   getEmojiDataFromNative: () => (/* binding */ $693b183b0a78708f$export$5ef5574deca44bc0),\n/* harmony export */   init: () => (/* binding */ $7adb23b0109cc36a$export$2cd8252107eb640b)\n/* harmony export */ });\nfunction $parcel$interopDefault(a) {\n  return a && a.__esModule ? a.default : a;\n}\nfunction $c770c458706daa72$export$2e2bcd8739ae039(obj, key, value) {\n    if (key in obj) Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n    });\n    else obj[key] = value;\n    return obj;\n}\n\n\nvar $fb96b826c0c5f37a$var$n, $fb96b826c0c5f37a$export$41c562ebe57d11e2, $fb96b826c0c5f37a$var$u, $fb96b826c0c5f37a$export$a8257692ac88316c, $fb96b826c0c5f37a$var$t, $fb96b826c0c5f37a$var$r, $fb96b826c0c5f37a$var$o, $fb96b826c0c5f37a$var$f, $fb96b826c0c5f37a$var$e = {}, $fb96b826c0c5f37a$var$c = [], $fb96b826c0c5f37a$var$s = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\nfunction $fb96b826c0c5f37a$var$a(n1, l1) {\n    for(var u1 in l1)n1[u1] = l1[u1];\n    return n1;\n}\nfunction $fb96b826c0c5f37a$var$h(n2) {\n    var l2 = n2.parentNode;\n    l2 && l2.removeChild(n2);\n}\nfunction $fb96b826c0c5f37a$export$c8a8987d4410bf2d(l3, u2, i1) {\n    var t1, r1, o1, f1 = {};\n    for(o1 in u2)\"key\" == o1 ? t1 = u2[o1] : \"ref\" == o1 ? r1 = u2[o1] : f1[o1] = u2[o1];\n    if (arguments.length > 2 && (f1.children = arguments.length > 3 ? $fb96b826c0c5f37a$var$n.call(arguments, 2) : i1), \"function\" == typeof l3 && null != l3.defaultProps) for(o1 in l3.defaultProps)void 0 === f1[o1] && (f1[o1] = l3.defaultProps[o1]);\n    return $fb96b826c0c5f37a$var$y(l3, f1, t1, r1, null);\n}\nfunction $fb96b826c0c5f37a$var$y(n3, i2, t2, r2, o2) {\n    var f2 = {\n        type: n3,\n        props: i2,\n        key: t2,\n        ref: r2,\n        __k: null,\n        __: null,\n        __b: 0,\n        __e: null,\n        __d: void 0,\n        __c: null,\n        __h: null,\n        constructor: void 0,\n        __v: null == o2 ? ++$fb96b826c0c5f37a$var$u : o2\n    };\n    return null == o2 && null != $fb96b826c0c5f37a$export$41c562ebe57d11e2.vnode && $fb96b826c0c5f37a$export$41c562ebe57d11e2.vnode(f2), f2;\n}\nfunction $fb96b826c0c5f37a$export$7d1e3a5e95ceca43() {\n    return {\n        current: null\n    };\n}\nfunction $fb96b826c0c5f37a$export$ffb0004e005737fa(n4) {\n    return n4.children;\n}\nfunction $fb96b826c0c5f37a$export$16fa2f45be04daa8(n5, l4) {\n    this.props = n5, this.context = l4;\n}\nfunction $fb96b826c0c5f37a$var$k(n6, l5) {\n    if (null == l5) return n6.__ ? $fb96b826c0c5f37a$var$k(n6.__, n6.__.__k.indexOf(n6) + 1) : null;\n    for(var u3; l5 < n6.__k.length; l5++)if (null != (u3 = n6.__k[l5]) && null != u3.__e) return u3.__e;\n    return \"function\" == typeof n6.type ? $fb96b826c0c5f37a$var$k(n6) : null;\n}\nfunction $fb96b826c0c5f37a$var$b(n7) {\n    var l6, u4;\n    if (null != (n7 = n7.__) && null != n7.__c) {\n        for(n7.__e = n7.__c.base = null, l6 = 0; l6 < n7.__k.length; l6++)if (null != (u4 = n7.__k[l6]) && null != u4.__e) {\n            n7.__e = n7.__c.base = u4.__e;\n            break;\n        }\n        return $fb96b826c0c5f37a$var$b(n7);\n    }\n}\nfunction $fb96b826c0c5f37a$var$m(n8) {\n    (!n8.__d && (n8.__d = !0) && $fb96b826c0c5f37a$var$t.push(n8) && !$fb96b826c0c5f37a$var$g.__r++ || $fb96b826c0c5f37a$var$o !== $fb96b826c0c5f37a$export$41c562ebe57d11e2.debounceRendering) && (($fb96b826c0c5f37a$var$o = $fb96b826c0c5f37a$export$41c562ebe57d11e2.debounceRendering) || $fb96b826c0c5f37a$var$r)($fb96b826c0c5f37a$var$g);\n}\nfunction $fb96b826c0c5f37a$var$g() {\n    for(var n9; $fb96b826c0c5f37a$var$g.__r = $fb96b826c0c5f37a$var$t.length;)n9 = $fb96b826c0c5f37a$var$t.sort(function(n10, l7) {\n        return n10.__v.__b - l7.__v.__b;\n    }), $fb96b826c0c5f37a$var$t = [], n9.some(function(n11) {\n        var l8, u5, i3, t3, r3, o3;\n        n11.__d && (r3 = (t3 = (l8 = n11).__v).__e, (o3 = l8.__P) && (u5 = [], (i3 = $fb96b826c0c5f37a$var$a({}, t3)).__v = t3.__v + 1, $fb96b826c0c5f37a$var$j(o3, t3, i3, l8.__n, void 0 !== o3.ownerSVGElement, null != t3.__h ? [\n            r3\n        ] : null, u5, null == r3 ? $fb96b826c0c5f37a$var$k(t3) : r3, t3.__h), $fb96b826c0c5f37a$var$z(u5, t3), t3.__e != r3 && $fb96b826c0c5f37a$var$b(t3)));\n    });\n}\nfunction $fb96b826c0c5f37a$var$w(n12, l9, u6, i4, t4, r4, o4, f3, s1, a1) {\n    var h1, v1, p1, _1, b1, m1, g1, w1 = i4 && i4.__k || $fb96b826c0c5f37a$var$c, A1 = w1.length;\n    for(u6.__k = [], h1 = 0; h1 < l9.length; h1++)if (null != (_1 = u6.__k[h1] = null == (_1 = l9[h1]) || \"boolean\" == typeof _1 ? null : \"string\" == typeof _1 || \"number\" == typeof _1 || \"bigint\" == typeof _1 ? $fb96b826c0c5f37a$var$y(null, _1, null, null, _1) : Array.isArray(_1) ? $fb96b826c0c5f37a$var$y($fb96b826c0c5f37a$export$ffb0004e005737fa, {\n        children: _1\n    }, null, null, null) : _1.__b > 0 ? $fb96b826c0c5f37a$var$y(_1.type, _1.props, _1.key, null, _1.__v) : _1)) {\n        if (_1.__ = u6, _1.__b = u6.__b + 1, null === (p1 = w1[h1]) || p1 && _1.key == p1.key && _1.type === p1.type) w1[h1] = void 0;\n        else for(v1 = 0; v1 < A1; v1++){\n            if ((p1 = w1[v1]) && _1.key == p1.key && _1.type === p1.type) {\n                w1[v1] = void 0;\n                break;\n            }\n            p1 = null;\n        }\n        $fb96b826c0c5f37a$var$j(n12, _1, p1 = p1 || $fb96b826c0c5f37a$var$e, t4, r4, o4, f3, s1, a1), b1 = _1.__e, (v1 = _1.ref) && p1.ref != v1 && (g1 || (g1 = []), p1.ref && g1.push(p1.ref, null, _1), g1.push(v1, _1.__c || b1, _1)), null != b1 ? (null == m1 && (m1 = b1), \"function\" == typeof _1.type && _1.__k === p1.__k ? _1.__d = s1 = $fb96b826c0c5f37a$var$x(_1, s1, n12) : s1 = $fb96b826c0c5f37a$var$P(n12, _1, p1, w1, b1, s1), \"function\" == typeof u6.type && (u6.__d = s1)) : s1 && p1.__e == s1 && s1.parentNode != n12 && (s1 = $fb96b826c0c5f37a$var$k(p1));\n    }\n    for(u6.__e = m1, h1 = A1; h1--;)null != w1[h1] && (\"function\" == typeof u6.type && null != w1[h1].__e && w1[h1].__e == u6.__d && (u6.__d = $fb96b826c0c5f37a$var$k(i4, h1 + 1)), $fb96b826c0c5f37a$var$N(w1[h1], w1[h1]));\n    if (g1) for(h1 = 0; h1 < g1.length; h1++)$fb96b826c0c5f37a$var$M(g1[h1], g1[++h1], g1[++h1]);\n}\nfunction $fb96b826c0c5f37a$var$x(n13, l10, u7) {\n    for(var i5, t5 = n13.__k, r5 = 0; t5 && r5 < t5.length; r5++)(i5 = t5[r5]) && (i5.__ = n13, l10 = \"function\" == typeof i5.type ? $fb96b826c0c5f37a$var$x(i5, l10, u7) : $fb96b826c0c5f37a$var$P(u7, i5, i5, t5, i5.__e, l10));\n    return l10;\n}\nfunction $fb96b826c0c5f37a$export$47e4c5b300681277(n14, l11) {\n    return l11 = l11 || [], null == n14 || \"boolean\" == typeof n14 || (Array.isArray(n14) ? n14.some(function(n15) {\n        $fb96b826c0c5f37a$export$47e4c5b300681277(n15, l11);\n    }) : l11.push(n14)), l11;\n}\nfunction $fb96b826c0c5f37a$var$P(n16, l12, u8, i6, t6, r6) {\n    var o5, f4, e1;\n    if (void 0 !== l12.__d) o5 = l12.__d, l12.__d = void 0;\n    else if (null == u8 || t6 != r6 || null == t6.parentNode) n: if (null == r6 || r6.parentNode !== n16) n16.appendChild(t6), o5 = null;\n    else {\n        for(f4 = r6, e1 = 0; (f4 = f4.nextSibling) && e1 < i6.length; e1 += 2)if (f4 == t6) break n;\n        n16.insertBefore(t6, r6), o5 = r6;\n    }\n    return void 0 !== o5 ? o5 : t6.nextSibling;\n}\nfunction $fb96b826c0c5f37a$var$C(n17, l13, u9, i7, t7) {\n    var r7;\n    for(r7 in u9)\"children\" === r7 || \"key\" === r7 || r7 in l13 || $fb96b826c0c5f37a$var$H(n17, r7, null, u9[r7], i7);\n    for(r7 in l13)t7 && \"function\" != typeof l13[r7] || \"children\" === r7 || \"key\" === r7 || \"value\" === r7 || \"checked\" === r7 || u9[r7] === l13[r7] || $fb96b826c0c5f37a$var$H(n17, r7, l13[r7], u9[r7], i7);\n}\nfunction $fb96b826c0c5f37a$var$$(n18, l14, u10) {\n    \"-\" === l14[0] ? n18.setProperty(l14, u10) : n18[l14] = null == u10 ? \"\" : \"number\" != typeof u10 || $fb96b826c0c5f37a$var$s.test(l14) ? u10 : u10 + \"px\";\n}\nfunction $fb96b826c0c5f37a$var$H(n19, l15, u11, i8, t8) {\n    var r8;\n    n: if (\"style\" === l15) {\n        if (\"string\" == typeof u11) n19.style.cssText = u11;\n        else {\n            if (\"string\" == typeof i8 && (n19.style.cssText = i8 = \"\"), i8) for(l15 in i8)u11 && l15 in u11 || $fb96b826c0c5f37a$var$$(n19.style, l15, \"\");\n            if (u11) for(l15 in u11)i8 && u11[l15] === i8[l15] || $fb96b826c0c5f37a$var$$(n19.style, l15, u11[l15]);\n        }\n    } else if (\"o\" === l15[0] && \"n\" === l15[1]) r8 = l15 !== (l15 = l15.replace(/Capture$/, \"\")), l15 = l15.toLowerCase() in n19 ? l15.toLowerCase().slice(2) : l15.slice(2), n19.l || (n19.l = {}), n19.l[l15 + r8] = u11, u11 ? i8 || n19.addEventListener(l15, r8 ? $fb96b826c0c5f37a$var$T : $fb96b826c0c5f37a$var$I, r8) : n19.removeEventListener(l15, r8 ? $fb96b826c0c5f37a$var$T : $fb96b826c0c5f37a$var$I, r8);\n    else if (\"dangerouslySetInnerHTML\" !== l15) {\n        if (t8) l15 = l15.replace(/xlink[H:h]/, \"h\").replace(/sName$/, \"s\");\n        else if (\"href\" !== l15 && \"list\" !== l15 && \"form\" !== l15 && \"tabIndex\" !== l15 && \"download\" !== l15 && l15 in n19) try {\n            n19[l15] = null == u11 ? \"\" : u11;\n            break n;\n        } catch (n) {}\n        \"function\" == typeof u11 || (null != u11 && (!1 !== u11 || \"a\" === l15[0] && \"r\" === l15[1]) ? n19.setAttribute(l15, u11) : n19.removeAttribute(l15));\n    }\n}\nfunction $fb96b826c0c5f37a$var$I(n20) {\n    this.l[n20.type + !1]($fb96b826c0c5f37a$export$41c562ebe57d11e2.event ? $fb96b826c0c5f37a$export$41c562ebe57d11e2.event(n20) : n20);\n}\nfunction $fb96b826c0c5f37a$var$T(n21) {\n    this.l[n21.type + !0]($fb96b826c0c5f37a$export$41c562ebe57d11e2.event ? $fb96b826c0c5f37a$export$41c562ebe57d11e2.event(n21) : n21);\n}\nfunction $fb96b826c0c5f37a$var$j(n22, u12, i9, t9, r9, o6, f5, e2, c1) {\n    var s2, h2, v2, y1, p2, k1, b2, m2, g2, x1, A2, P1 = u12.type;\n    if (void 0 !== u12.constructor) return null;\n    null != i9.__h && (c1 = i9.__h, e2 = u12.__e = i9.__e, u12.__h = null, o6 = [\n        e2\n    ]), (s2 = $fb96b826c0c5f37a$export$41c562ebe57d11e2.__b) && s2(u12);\n    try {\n        n: if (\"function\" == typeof P1) {\n            if (m2 = u12.props, g2 = (s2 = P1.contextType) && t9[s2.__c], x1 = s2 ? g2 ? g2.props.value : s2.__ : t9, i9.__c ? b2 = (h2 = u12.__c = i9.__c).__ = h2.__E : (\"prototype\" in P1 && P1.prototype.render ? u12.__c = h2 = new P1(m2, x1) : (u12.__c = h2 = new $fb96b826c0c5f37a$export$16fa2f45be04daa8(m2, x1), h2.constructor = P1, h2.render = $fb96b826c0c5f37a$var$O), g2 && g2.sub(h2), h2.props = m2, h2.state || (h2.state = {}), h2.context = x1, h2.__n = t9, v2 = h2.__d = !0, h2.__h = []), null == h2.__s && (h2.__s = h2.state), null != P1.getDerivedStateFromProps && (h2.__s == h2.state && (h2.__s = $fb96b826c0c5f37a$var$a({}, h2.__s)), $fb96b826c0c5f37a$var$a(h2.__s, P1.getDerivedStateFromProps(m2, h2.__s))), y1 = h2.props, p2 = h2.state, v2) null == P1.getDerivedStateFromProps && null != h2.componentWillMount && h2.componentWillMount(), null != h2.componentDidMount && h2.__h.push(h2.componentDidMount);\n            else {\n                if (null == P1.getDerivedStateFromProps && m2 !== y1 && null != h2.componentWillReceiveProps && h2.componentWillReceiveProps(m2, x1), !h2.__e && null != h2.shouldComponentUpdate && !1 === h2.shouldComponentUpdate(m2, h2.__s, x1) || u12.__v === i9.__v) {\n                    h2.props = m2, h2.state = h2.__s, u12.__v !== i9.__v && (h2.__d = !1), h2.__v = u12, u12.__e = i9.__e, u12.__k = i9.__k, u12.__k.forEach(function(n23) {\n                        n23 && (n23.__ = u12);\n                    }), h2.__h.length && f5.push(h2);\n                    break n;\n                }\n                null != h2.componentWillUpdate && h2.componentWillUpdate(m2, h2.__s, x1), null != h2.componentDidUpdate && h2.__h.push(function() {\n                    h2.componentDidUpdate(y1, p2, k1);\n                });\n            }\n            h2.context = x1, h2.props = m2, h2.state = h2.__s, (s2 = $fb96b826c0c5f37a$export$41c562ebe57d11e2.__r) && s2(u12), h2.__d = !1, h2.__v = u12, h2.__P = n22, s2 = h2.render(h2.props, h2.state, h2.context), h2.state = h2.__s, null != h2.getChildContext && (t9 = $fb96b826c0c5f37a$var$a($fb96b826c0c5f37a$var$a({}, t9), h2.getChildContext())), v2 || null == h2.getSnapshotBeforeUpdate || (k1 = h2.getSnapshotBeforeUpdate(y1, p2)), A2 = null != s2 && s2.type === $fb96b826c0c5f37a$export$ffb0004e005737fa && null == s2.key ? s2.props.children : s2, $fb96b826c0c5f37a$var$w(n22, Array.isArray(A2) ? A2 : [\n                A2\n            ], u12, i9, t9, r9, o6, f5, e2, c1), h2.base = u12.__e, u12.__h = null, h2.__h.length && f5.push(h2), b2 && (h2.__E = h2.__ = null), h2.__e = !1;\n        } else null == o6 && u12.__v === i9.__v ? (u12.__k = i9.__k, u12.__e = i9.__e) : u12.__e = $fb96b826c0c5f37a$var$L(i9.__e, u12, i9, t9, r9, o6, f5, c1);\n        (s2 = $fb96b826c0c5f37a$export$41c562ebe57d11e2.diffed) && s2(u12);\n    } catch (n24) {\n        u12.__v = null, (c1 || null != o6) && (u12.__e = e2, u12.__h = !!c1, o6[o6.indexOf(e2)] = null), $fb96b826c0c5f37a$export$41c562ebe57d11e2.__e(n24, u12, i9);\n    }\n}\nfunction $fb96b826c0c5f37a$var$z(n25, u13) {\n    $fb96b826c0c5f37a$export$41c562ebe57d11e2.__c && $fb96b826c0c5f37a$export$41c562ebe57d11e2.__c(u13, n25), n25.some(function(u14) {\n        try {\n            n25 = u14.__h, u14.__h = [], n25.some(function(n26) {\n                n26.call(u14);\n            });\n        } catch (n27) {\n            $fb96b826c0c5f37a$export$41c562ebe57d11e2.__e(n27, u14.__v);\n        }\n    });\n}\nfunction $fb96b826c0c5f37a$var$L(l16, u15, i10, t10, r10, o7, f6, c2) {\n    var s3, a2, v3, y2 = i10.props, p3 = u15.props, d1 = u15.type, _2 = 0;\n    if (\"svg\" === d1 && (r10 = !0), null != o7) {\n        for(; _2 < o7.length; _2++)if ((s3 = o7[_2]) && \"setAttribute\" in s3 == !!d1 && (d1 ? s3.localName === d1 : 3 === s3.nodeType)) {\n            l16 = s3, o7[_2] = null;\n            break;\n        }\n    }\n    if (null == l16) {\n        if (null === d1) return document.createTextNode(p3);\n        l16 = r10 ? document.createElementNS(\"http://www.w3.org/2000/svg\", d1) : document.createElement(d1, p3.is && p3), o7 = null, c2 = !1;\n    }\n    if (null === d1) y2 === p3 || c2 && l16.data === p3 || (l16.data = p3);\n    else {\n        if (o7 = o7 && $fb96b826c0c5f37a$var$n.call(l16.childNodes), a2 = (y2 = i10.props || $fb96b826c0c5f37a$var$e).dangerouslySetInnerHTML, v3 = p3.dangerouslySetInnerHTML, !c2) {\n            if (null != o7) for(y2 = {}, _2 = 0; _2 < l16.attributes.length; _2++)y2[l16.attributes[_2].name] = l16.attributes[_2].value;\n            (v3 || a2) && (v3 && (a2 && v3.__html == a2.__html || v3.__html === l16.innerHTML) || (l16.innerHTML = v3 && v3.__html || \"\"));\n        }\n        if ($fb96b826c0c5f37a$var$C(l16, p3, y2, r10, c2), v3) u15.__k = [];\n        else if (_2 = u15.props.children, $fb96b826c0c5f37a$var$w(l16, Array.isArray(_2) ? _2 : [\n            _2\n        ], u15, i10, t10, r10 && \"foreignObject\" !== d1, o7, f6, o7 ? o7[0] : i10.__k && $fb96b826c0c5f37a$var$k(i10, 0), c2), null != o7) for(_2 = o7.length; _2--;)null != o7[_2] && $fb96b826c0c5f37a$var$h(o7[_2]);\n        c2 || (\"value\" in p3 && void 0 !== (_2 = p3.value) && (_2 !== y2.value || _2 !== l16.value || \"progress\" === d1 && !_2) && $fb96b826c0c5f37a$var$H(l16, \"value\", _2, y2.value, !1), \"checked\" in p3 && void 0 !== (_2 = p3.checked) && _2 !== l16.checked && $fb96b826c0c5f37a$var$H(l16, \"checked\", _2, y2.checked, !1));\n    }\n    return l16;\n}\nfunction $fb96b826c0c5f37a$var$M(n28, u16, i11) {\n    try {\n        \"function\" == typeof n28 ? n28(u16) : n28.current = u16;\n    } catch (n29) {\n        $fb96b826c0c5f37a$export$41c562ebe57d11e2.__e(n29, i11);\n    }\n}\nfunction $fb96b826c0c5f37a$var$N(n30, u17, i12) {\n    var t11, r11;\n    if ($fb96b826c0c5f37a$export$41c562ebe57d11e2.unmount && $fb96b826c0c5f37a$export$41c562ebe57d11e2.unmount(n30), (t11 = n30.ref) && (t11.current && t11.current !== n30.__e || $fb96b826c0c5f37a$var$M(t11, null, u17)), null != (t11 = n30.__c)) {\n        if (t11.componentWillUnmount) try {\n            t11.componentWillUnmount();\n        } catch (n31) {\n            $fb96b826c0c5f37a$export$41c562ebe57d11e2.__e(n31, u17);\n        }\n        t11.base = t11.__P = null;\n    }\n    if (t11 = n30.__k) for(r11 = 0; r11 < t11.length; r11++)t11[r11] && $fb96b826c0c5f37a$var$N(t11[r11], u17, \"function\" != typeof n30.type);\n    i12 || null == n30.__e || $fb96b826c0c5f37a$var$h(n30.__e), n30.__e = n30.__d = void 0;\n}\nfunction $fb96b826c0c5f37a$var$O(n32, l, u18) {\n    return this.constructor(n32, u18);\n}\nfunction $fb96b826c0c5f37a$export$b3890eb0ae9dca99(u19, i13, t12) {\n    var r12, o8, f7;\n    $fb96b826c0c5f37a$export$41c562ebe57d11e2.__ && $fb96b826c0c5f37a$export$41c562ebe57d11e2.__(u19, i13), o8 = (r12 = \"function\" == typeof t12) ? null : t12 && t12.__k || i13.__k, f7 = [], $fb96b826c0c5f37a$var$j(i13, u19 = (!r12 && t12 || i13).__k = $fb96b826c0c5f37a$export$c8a8987d4410bf2d($fb96b826c0c5f37a$export$ffb0004e005737fa, null, [\n        u19\n    ]), o8 || $fb96b826c0c5f37a$var$e, $fb96b826c0c5f37a$var$e, void 0 !== i13.ownerSVGElement, !r12 && t12 ? [\n        t12\n    ] : o8 ? null : i13.firstChild ? $fb96b826c0c5f37a$var$n.call(i13.childNodes) : null, f7, !r12 && t12 ? t12 : o8 ? o8.__e : i13.firstChild, r12), $fb96b826c0c5f37a$var$z(f7, u19);\n}\nfunction $fb96b826c0c5f37a$export$fa8d919ba61d84db(n33, l17) {\n    $fb96b826c0c5f37a$export$b3890eb0ae9dca99(n33, l17, $fb96b826c0c5f37a$export$fa8d919ba61d84db);\n}\nfunction $fb96b826c0c5f37a$export$e530037191fcd5d7(l18, u20, i14) {\n    var t13, r13, o9, f8 = $fb96b826c0c5f37a$var$a({}, l18.props);\n    for(o9 in u20)\"key\" == o9 ? t13 = u20[o9] : \"ref\" == o9 ? r13 = u20[o9] : f8[o9] = u20[o9];\n    return arguments.length > 2 && (f8.children = arguments.length > 3 ? $fb96b826c0c5f37a$var$n.call(arguments, 2) : i14), $fb96b826c0c5f37a$var$y(l18.type, f8, t13 || l18.key, r13 || l18.ref, null);\n}\nfunction $fb96b826c0c5f37a$export$fd42f52fd3ae1109(n34, l19) {\n    var u21 = {\n        __c: l19 = \"__cC\" + $fb96b826c0c5f37a$var$f++,\n        __: n34,\n        Consumer: function(n35, l20) {\n            return n35.children(l20);\n        },\n        Provider: function(n36) {\n            var u22, i15;\n            return this.getChildContext || (u22 = [], (i15 = {})[l19] = this, this.getChildContext = function() {\n                return i15;\n            }, this.shouldComponentUpdate = function(n37) {\n                this.props.value !== n37.value && u22.some($fb96b826c0c5f37a$var$m);\n            }, this.sub = function(n38) {\n                u22.push(n38);\n                var l21 = n38.componentWillUnmount;\n                n38.componentWillUnmount = function() {\n                    u22.splice(u22.indexOf(n38), 1), l21 && l21.call(n38);\n                };\n            }), n36.children;\n        }\n    };\n    return u21.Provider.__ = u21.Consumer.contextType = u21;\n}\n$fb96b826c0c5f37a$var$n = $fb96b826c0c5f37a$var$c.slice, $fb96b826c0c5f37a$export$41c562ebe57d11e2 = {\n    __e: function(n39, l22) {\n        for(var u23, i16, t14; l22 = l22.__;)if ((u23 = l22.__c) && !u23.__) try {\n            if ((i16 = u23.constructor) && null != i16.getDerivedStateFromError && (u23.setState(i16.getDerivedStateFromError(n39)), t14 = u23.__d), null != u23.componentDidCatch && (u23.componentDidCatch(n39), t14 = u23.__d), t14) return u23.__E = u23;\n        } catch (l23) {\n            n39 = l23;\n        }\n        throw n39;\n    }\n}, $fb96b826c0c5f37a$var$u = 0, $fb96b826c0c5f37a$export$a8257692ac88316c = function(n40) {\n    return null != n40 && void 0 === n40.constructor;\n}, $fb96b826c0c5f37a$export$16fa2f45be04daa8.prototype.setState = function(n41, l24) {\n    var u24;\n    u24 = null != this.__s && this.__s !== this.state ? this.__s : this.__s = $fb96b826c0c5f37a$var$a({}, this.state), \"function\" == typeof n41 && (n41 = n41($fb96b826c0c5f37a$var$a({}, u24), this.props)), n41 && $fb96b826c0c5f37a$var$a(u24, n41), null != n41 && this.__v && (l24 && this.__h.push(l24), $fb96b826c0c5f37a$var$m(this));\n}, $fb96b826c0c5f37a$export$16fa2f45be04daa8.prototype.forceUpdate = function(n42) {\n    this.__v && (this.__e = !0, n42 && this.__h.push(n42), $fb96b826c0c5f37a$var$m(this));\n}, $fb96b826c0c5f37a$export$16fa2f45be04daa8.prototype.render = $fb96b826c0c5f37a$export$ffb0004e005737fa, $fb96b826c0c5f37a$var$t = [], $fb96b826c0c5f37a$var$r = \"function\" == typeof Promise ? Promise.prototype.then.bind(Promise.resolve()) : setTimeout, $fb96b826c0c5f37a$var$g.__r = 0, $fb96b826c0c5f37a$var$f = 0;\n\n\n\nvar $bd9dd35321b03dd4$var$o = 0;\nfunction $bd9dd35321b03dd4$export$34b9dba7ce09269b(_1, e1, n, t, f) {\n    var l, s, u = {};\n    for(s in e1)\"ref\" == s ? l = e1[s] : u[s] = e1[s];\n    var a = {\n        type: _1,\n        props: u,\n        key: n,\n        ref: l,\n        __k: null,\n        __: null,\n        __b: 0,\n        __e: null,\n        __d: void 0,\n        __c: null,\n        __h: null,\n        constructor: void 0,\n        __v: --$bd9dd35321b03dd4$var$o,\n        __source: t,\n        __self: f\n    };\n    if (\"function\" == typeof _1 && (l = _1.defaultProps)) for(s in l)void 0 === u[s] && (u[s] = l[s]);\n    return (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).vnode && (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).vnode(a), a;\n}\n\n\n\nfunction $f72b75cf796873c7$var$set(key, value) {\n    try {\n        window.localStorage[`emoji-mart.${key}`] = JSON.stringify(value);\n    } catch (error) {}\n}\nfunction $f72b75cf796873c7$var$get(key) {\n    try {\n        const value = window.localStorage[`emoji-mart.${key}`];\n        if (value) return JSON.parse(value);\n    } catch (error) {}\n}\nvar $f72b75cf796873c7$export$2e2bcd8739ae039 = {\n    set: $f72b75cf796873c7$var$set,\n    get: $f72b75cf796873c7$var$get\n};\n\n\nconst $c84d045dcc34faf5$var$CACHE = new Map();\nconst $c84d045dcc34faf5$var$VERSIONS = [\n    {\n        v: 15,\n        emoji: \"\\uD83E\\uDEE8\"\n    },\n    {\n        v: 14,\n        emoji: \"\\uD83E\\uDEE0\"\n    },\n    {\n        v: 13.1,\n        emoji: \"\\uD83D\\uDE36\\u200D\\uD83C\\uDF2B\\uFE0F\"\n    },\n    {\n        v: 13,\n        emoji: \"\\uD83E\\uDD78\"\n    },\n    {\n        v: 12.1,\n        emoji: \"\\uD83E\\uDDD1\\u200D\\uD83E\\uDDB0\"\n    },\n    {\n        v: 12,\n        emoji: \"\\uD83E\\uDD71\"\n    },\n    {\n        v: 11,\n        emoji: \"\\uD83E\\uDD70\"\n    },\n    {\n        v: 5,\n        emoji: \"\\uD83E\\uDD29\"\n    },\n    {\n        v: 4,\n        emoji: \"\\uD83D\\uDC71\\u200D\\u2640\\uFE0F\"\n    },\n    {\n        v: 3,\n        emoji: \"\\uD83E\\uDD23\"\n    },\n    {\n        v: 2,\n        emoji: \"\\uD83D\\uDC4B\\uD83C\\uDFFB\"\n    },\n    {\n        v: 1,\n        emoji: \"\\uD83D\\uDE43\"\n    }, \n];\nfunction $c84d045dcc34faf5$var$latestVersion() {\n    for (const { v: v , emoji: emoji  } of $c84d045dcc34faf5$var$VERSIONS){\n        if ($c84d045dcc34faf5$var$isSupported(emoji)) return v;\n    }\n}\nfunction $c84d045dcc34faf5$var$noCountryFlags() {\n    if ($c84d045dcc34faf5$var$isSupported(\"\\uD83C\\uDDE8\\uD83C\\uDDE6\")) return false;\n    return true;\n}\nfunction $c84d045dcc34faf5$var$isSupported(emoji) {\n    if ($c84d045dcc34faf5$var$CACHE.has(emoji)) return $c84d045dcc34faf5$var$CACHE.get(emoji);\n    const supported = $c84d045dcc34faf5$var$isEmojiSupported(emoji);\n    $c84d045dcc34faf5$var$CACHE.set(emoji, supported);\n    return supported;\n}\n// https://github.com/koala-interactive/is-emoji-supported\nconst $c84d045dcc34faf5$var$isEmojiSupported = (()=>{\n    let ctx = null;\n    try {\n        if (!navigator.userAgent.includes(\"jsdom\")) ctx = document.createElement(\"canvas\").getContext(\"2d\", {\n            willReadFrequently: true\n        });\n    } catch  {}\n    // Not in browser env\n    if (!ctx) return ()=>false;\n    const CANVAS_HEIGHT = 25;\n    const CANVAS_WIDTH = 20;\n    const textSize = Math.floor(CANVAS_HEIGHT / 2);\n    // Initialize convas context\n    ctx.font = textSize + \"px Arial, Sans-Serif\";\n    ctx.textBaseline = \"top\";\n    ctx.canvas.width = CANVAS_WIDTH * 2;\n    ctx.canvas.height = CANVAS_HEIGHT;\n    return (unicode)=>{\n        ctx.clearRect(0, 0, CANVAS_WIDTH * 2, CANVAS_HEIGHT);\n        // Draw in red on the left\n        ctx.fillStyle = \"#FF0000\";\n        ctx.fillText(unicode, 0, 22);\n        // Draw in blue on right\n        ctx.fillStyle = \"#0000FF\";\n        ctx.fillText(unicode, CANVAS_WIDTH, 22);\n        const a = ctx.getImageData(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT).data;\n        const count = a.length;\n        let i = 0;\n        // Search the first visible pixel\n        for(; i < count && !a[i + 3]; i += 4);\n        // No visible pixel\n        if (i >= count) return false;\n        // Emoji has immutable color, so we check the color of the emoji in two different colors\n        // the result show be the same.\n        const x = CANVAS_WIDTH + i / 4 % CANVAS_WIDTH;\n        const y = Math.floor(i / 4 / CANVAS_WIDTH);\n        const b = ctx.getImageData(x, y, 1, 1).data;\n        if (a[i] !== b[0] || a[i + 2] !== b[2]) return false;\n        // Some emojis are a contraction of different ones, so if it's not\n        // supported, it will show multiple characters\n        if (ctx.measureText(unicode).width >= CANVAS_WIDTH) return false;\n        // Supported\n        return true;\n    };\n})();\nvar $c84d045dcc34faf5$export$2e2bcd8739ae039 = {\n    latestVersion: $c84d045dcc34faf5$var$latestVersion,\n    noCountryFlags: $c84d045dcc34faf5$var$noCountryFlags\n};\n\n\n\nconst $b22cfd0a55410b4f$var$DEFAULTS = [\n    \"+1\",\n    \"grinning\",\n    \"kissing_heart\",\n    \"heart_eyes\",\n    \"laughing\",\n    \"stuck_out_tongue_winking_eye\",\n    \"sweat_smile\",\n    \"joy\",\n    \"scream\",\n    \"disappointed\",\n    \"unamused\",\n    \"weary\",\n    \"sob\",\n    \"sunglasses\",\n    \"heart\", \n];\nlet $b22cfd0a55410b4f$var$Index = null;\nfunction $b22cfd0a55410b4f$var$add(emoji) {\n    $b22cfd0a55410b4f$var$Index || ($b22cfd0a55410b4f$var$Index = (0, $f72b75cf796873c7$export$2e2bcd8739ae039).get(\"frequently\") || {});\n    const emojiId = emoji.id || emoji;\n    if (!emojiId) return;\n    $b22cfd0a55410b4f$var$Index[emojiId] || ($b22cfd0a55410b4f$var$Index[emojiId] = 0);\n    $b22cfd0a55410b4f$var$Index[emojiId] += 1;\n    (0, $f72b75cf796873c7$export$2e2bcd8739ae039).set(\"last\", emojiId);\n    (0, $f72b75cf796873c7$export$2e2bcd8739ae039).set(\"frequently\", $b22cfd0a55410b4f$var$Index);\n}\nfunction $b22cfd0a55410b4f$var$get({ maxFrequentRows: maxFrequentRows , perLine: perLine  }) {\n    if (!maxFrequentRows) return [];\n    $b22cfd0a55410b4f$var$Index || ($b22cfd0a55410b4f$var$Index = (0, $f72b75cf796873c7$export$2e2bcd8739ae039).get(\"frequently\"));\n    let emojiIds = [];\n    if (!$b22cfd0a55410b4f$var$Index) {\n        $b22cfd0a55410b4f$var$Index = {};\n        for(let i in $b22cfd0a55410b4f$var$DEFAULTS.slice(0, perLine)){\n            const emojiId = $b22cfd0a55410b4f$var$DEFAULTS[i];\n            $b22cfd0a55410b4f$var$Index[emojiId] = perLine - i;\n            emojiIds.push(emojiId);\n        }\n        return emojiIds;\n    }\n    const max = maxFrequentRows * perLine;\n    const last = (0, $f72b75cf796873c7$export$2e2bcd8739ae039).get(\"last\");\n    for(let emojiId in $b22cfd0a55410b4f$var$Index)emojiIds.push(emojiId);\n    emojiIds.sort((a, b)=>{\n        const aScore = $b22cfd0a55410b4f$var$Index[b];\n        const bScore = $b22cfd0a55410b4f$var$Index[a];\n        if (aScore == bScore) return a.localeCompare(b);\n        return aScore - bScore;\n    });\n    if (emojiIds.length > max) {\n        const removedIds = emojiIds.slice(max);\n        emojiIds = emojiIds.slice(0, max);\n        for (let removedId of removedIds){\n            if (removedId == last) continue;\n            delete $b22cfd0a55410b4f$var$Index[removedId];\n        }\n        if (last && emojiIds.indexOf(last) == -1) {\n            delete $b22cfd0a55410b4f$var$Index[emojiIds[emojiIds.length - 1]];\n            emojiIds.splice(-1, 1, last);\n        }\n        (0, $f72b75cf796873c7$export$2e2bcd8739ae039).set(\"frequently\", $b22cfd0a55410b4f$var$Index);\n    }\n    return emojiIds;\n}\nvar $b22cfd0a55410b4f$export$2e2bcd8739ae039 = {\n    add: $b22cfd0a55410b4f$var$add,\n    get: $b22cfd0a55410b4f$var$get,\n    DEFAULTS: $b22cfd0a55410b4f$var$DEFAULTS\n};\n\n\nvar $8d50d93417ef682a$exports = {};\n$8d50d93417ef682a$exports = JSON.parse('{\"search\":\"Search\",\"search_no_results_1\":\"Oh no!\",\"search_no_results_2\":\"That emoji couldn\\u2019t be found\",\"pick\":\"Pick an emoji\\u2026\",\"add_custom\":\"Add custom emoji\",\"categories\":{\"activity\":\"Activity\",\"custom\":\"Custom\",\"flags\":\"Flags\",\"foods\":\"Food & Drink\",\"frequent\":\"Frequently used\",\"nature\":\"Animals & Nature\",\"objects\":\"Objects\",\"people\":\"Smileys & People\",\"places\":\"Travel & Places\",\"search\":\"Search Results\",\"symbols\":\"Symbols\"},\"skins\":{\"1\":\"Default\",\"2\":\"Light\",\"3\":\"Medium-Light\",\"4\":\"Medium\",\"5\":\"Medium-Dark\",\"6\":\"Dark\",\"choose\":\"Choose default skin tone\"}}');\n\n\nvar $b247ea80b67298d5$export$2e2bcd8739ae039 = {\n    autoFocus: {\n        value: false\n    },\n    dynamicWidth: {\n        value: false\n    },\n    emojiButtonColors: {\n        value: null\n    },\n    emojiButtonRadius: {\n        value: \"100%\"\n    },\n    emojiButtonSize: {\n        value: 36\n    },\n    emojiSize: {\n        value: 24\n    },\n    emojiVersion: {\n        value: 15,\n        choices: [\n            1,\n            2,\n            3,\n            4,\n            5,\n            11,\n            12,\n            12.1,\n            13,\n            13.1,\n            14,\n            15\n        ]\n    },\n    exceptEmojis: {\n        value: []\n    },\n    icons: {\n        value: \"auto\",\n        choices: [\n            \"auto\",\n            \"outline\",\n            \"solid\"\n        ]\n    },\n    locale: {\n        value: \"en\",\n        choices: [\n            \"en\",\n            \"ar\",\n            \"be\",\n            \"cs\",\n            \"de\",\n            \"es\",\n            \"fa\",\n            \"fi\",\n            \"fr\",\n            \"hi\",\n            \"it\",\n            \"ja\",\n            \"ko\",\n            \"nl\",\n            \"pl\",\n            \"pt\",\n            \"ru\",\n            \"sa\",\n            \"tr\",\n            \"uk\",\n            \"vi\",\n            \"zh\", \n        ]\n    },\n    maxFrequentRows: {\n        value: 4\n    },\n    navPosition: {\n        value: \"top\",\n        choices: [\n            \"top\",\n            \"bottom\",\n            \"none\"\n        ]\n    },\n    noCountryFlags: {\n        value: false\n    },\n    noResultsEmoji: {\n        value: null\n    },\n    perLine: {\n        value: 9\n    },\n    previewEmoji: {\n        value: null\n    },\n    previewPosition: {\n        value: \"bottom\",\n        choices: [\n            \"top\",\n            \"bottom\",\n            \"none\"\n        ]\n    },\n    searchPosition: {\n        value: \"sticky\",\n        choices: [\n            \"sticky\",\n            \"static\",\n            \"none\"\n        ]\n    },\n    set: {\n        value: \"native\",\n        choices: [\n            \"native\",\n            \"apple\",\n            \"facebook\",\n            \"google\",\n            \"twitter\"\n        ]\n    },\n    skin: {\n        value: 1,\n        choices: [\n            1,\n            2,\n            3,\n            4,\n            5,\n            6\n        ]\n    },\n    skinTonePosition: {\n        value: \"preview\",\n        choices: [\n            \"preview\",\n            \"search\",\n            \"none\"\n        ]\n    },\n    theme: {\n        value: \"auto\",\n        choices: [\n            \"auto\",\n            \"light\",\n            \"dark\"\n        ]\n    },\n    // Data\n    categories: null,\n    categoryIcons: null,\n    custom: null,\n    data: null,\n    i18n: null,\n    // Callbacks\n    getImageURL: null,\n    getSpritesheetURL: null,\n    onAddCustomEmoji: null,\n    onClickOutside: null,\n    onEmojiSelect: null,\n    // Deprecated\n    stickySearch: {\n        deprecated: true,\n        value: true\n    }\n};\n\n\n\nlet $7adb23b0109cc36a$export$dbe3113d60765c1a = null;\nlet $7adb23b0109cc36a$export$2d0294657ab35f1b = null;\nconst $7adb23b0109cc36a$var$fetchCache = {};\nasync function $7adb23b0109cc36a$var$fetchJSON(src) {\n    if ($7adb23b0109cc36a$var$fetchCache[src]) return $7adb23b0109cc36a$var$fetchCache[src];\n    const response = await fetch(src);\n    const json = await response.json();\n    $7adb23b0109cc36a$var$fetchCache[src] = json;\n    return json;\n}\nlet $7adb23b0109cc36a$var$promise = null;\nlet $7adb23b0109cc36a$var$initiated = false;\nlet $7adb23b0109cc36a$var$initCallback = null;\nlet $7adb23b0109cc36a$var$initialized = false;\nfunction $7adb23b0109cc36a$export$2cd8252107eb640b(options, { caller: caller  } = {}) {\n    $7adb23b0109cc36a$var$promise || ($7adb23b0109cc36a$var$promise = new Promise((resolve)=>{\n        $7adb23b0109cc36a$var$initCallback = resolve;\n    }));\n    if (options) $7adb23b0109cc36a$var$_init(options);\n    else if (caller && !$7adb23b0109cc36a$var$initialized) console.warn(`\\`${caller}\\` requires data to be initialized first. Promise will be pending until \\`init\\` is called.`);\n    return $7adb23b0109cc36a$var$promise;\n}\nasync function $7adb23b0109cc36a$var$_init(props) {\n    $7adb23b0109cc36a$var$initialized = true;\n    let { emojiVersion: emojiVersion , set: set , locale: locale  } = props;\n    emojiVersion || (emojiVersion = (0, $b247ea80b67298d5$export$2e2bcd8739ae039).emojiVersion.value);\n    set || (set = (0, $b247ea80b67298d5$export$2e2bcd8739ae039).set.value);\n    locale || (locale = (0, $b247ea80b67298d5$export$2e2bcd8739ae039).locale.value);\n    if (!$7adb23b0109cc36a$export$2d0294657ab35f1b) {\n        $7adb23b0109cc36a$export$2d0294657ab35f1b = (typeof props.data === \"function\" ? await props.data() : props.data) || await $7adb23b0109cc36a$var$fetchJSON(`https://cdn.jsdelivr.net/npm/@emoji-mart/data@latest/sets/${emojiVersion}/${set}.json`);\n        $7adb23b0109cc36a$export$2d0294657ab35f1b.emoticons = {};\n        $7adb23b0109cc36a$export$2d0294657ab35f1b.natives = {};\n        $7adb23b0109cc36a$export$2d0294657ab35f1b.categories.unshift({\n            id: \"frequent\",\n            emojis: []\n        });\n        for(const alias in $7adb23b0109cc36a$export$2d0294657ab35f1b.aliases){\n            const emojiId = $7adb23b0109cc36a$export$2d0294657ab35f1b.aliases[alias];\n            const emoji = $7adb23b0109cc36a$export$2d0294657ab35f1b.emojis[emojiId];\n            if (!emoji) continue;\n            emoji.aliases || (emoji.aliases = []);\n            emoji.aliases.push(alias);\n        }\n        $7adb23b0109cc36a$export$2d0294657ab35f1b.originalCategories = $7adb23b0109cc36a$export$2d0294657ab35f1b.categories;\n    } else $7adb23b0109cc36a$export$2d0294657ab35f1b.categories = $7adb23b0109cc36a$export$2d0294657ab35f1b.categories.filter((c)=>{\n        const isCustom = !!c.name;\n        if (!isCustom) return true;\n        return false;\n    });\n    $7adb23b0109cc36a$export$dbe3113d60765c1a = (typeof props.i18n === \"function\" ? await props.i18n() : props.i18n) || (locale == \"en\" ? (0, (/*@__PURE__*/$parcel$interopDefault($8d50d93417ef682a$exports))) : await $7adb23b0109cc36a$var$fetchJSON(`https://cdn.jsdelivr.net/npm/@emoji-mart/data@latest/i18n/${locale}.json`));\n    if (props.custom) for(let i in props.custom){\n        i = parseInt(i);\n        const category = props.custom[i];\n        const prevCategory = props.custom[i - 1];\n        if (!category.emojis || !category.emojis.length) continue;\n        category.id || (category.id = `custom_${i + 1}`);\n        category.name || (category.name = $7adb23b0109cc36a$export$dbe3113d60765c1a.categories.custom);\n        if (prevCategory && !category.icon) category.target = prevCategory.target || prevCategory;\n        $7adb23b0109cc36a$export$2d0294657ab35f1b.categories.push(category);\n        for (const emoji of category.emojis)$7adb23b0109cc36a$export$2d0294657ab35f1b.emojis[emoji.id] = emoji;\n    }\n    if (props.categories) $7adb23b0109cc36a$export$2d0294657ab35f1b.categories = $7adb23b0109cc36a$export$2d0294657ab35f1b.originalCategories.filter((c)=>{\n        return props.categories.indexOf(c.id) != -1;\n    }).sort((c1, c2)=>{\n        const i1 = props.categories.indexOf(c1.id);\n        const i2 = props.categories.indexOf(c2.id);\n        return i1 - i2;\n    });\n    let latestVersionSupport = null;\n    let noCountryFlags = null;\n    if (set == \"native\") {\n        latestVersionSupport = (0, $c84d045dcc34faf5$export$2e2bcd8739ae039).latestVersion();\n        noCountryFlags = props.noCountryFlags || (0, $c84d045dcc34faf5$export$2e2bcd8739ae039).noCountryFlags();\n    }\n    let categoryIndex = $7adb23b0109cc36a$export$2d0294657ab35f1b.categories.length;\n    let resetSearchIndex = false;\n    while(categoryIndex--){\n        const category = $7adb23b0109cc36a$export$2d0294657ab35f1b.categories[categoryIndex];\n        if (category.id == \"frequent\") {\n            let { maxFrequentRows: maxFrequentRows , perLine: perLine  } = props;\n            maxFrequentRows = maxFrequentRows >= 0 ? maxFrequentRows : (0, $b247ea80b67298d5$export$2e2bcd8739ae039).maxFrequentRows.value;\n            perLine || (perLine = (0, $b247ea80b67298d5$export$2e2bcd8739ae039).perLine.value);\n            category.emojis = (0, $b22cfd0a55410b4f$export$2e2bcd8739ae039).get({\n                maxFrequentRows: maxFrequentRows,\n                perLine: perLine\n            });\n        }\n        if (!category.emojis || !category.emojis.length) {\n            $7adb23b0109cc36a$export$2d0294657ab35f1b.categories.splice(categoryIndex, 1);\n            continue;\n        }\n        const { categoryIcons: categoryIcons  } = props;\n        if (categoryIcons) {\n            const icon = categoryIcons[category.id];\n            if (icon && !category.icon) category.icon = icon;\n        }\n        let emojiIndex = category.emojis.length;\n        while(emojiIndex--){\n            const emojiId = category.emojis[emojiIndex];\n            const emoji = emojiId.id ? emojiId : $7adb23b0109cc36a$export$2d0294657ab35f1b.emojis[emojiId];\n            const ignore = ()=>{\n                category.emojis.splice(emojiIndex, 1);\n            };\n            if (!emoji || props.exceptEmojis && props.exceptEmojis.includes(emoji.id)) {\n                ignore();\n                continue;\n            }\n            if (latestVersionSupport && emoji.version > latestVersionSupport) {\n                ignore();\n                continue;\n            }\n            if (noCountryFlags && category.id == \"flags\") {\n                if (!(0, $e6eae5155b87f591$export$bcb25aa587e9cb13).includes(emoji.id)) {\n                    ignore();\n                    continue;\n                }\n            }\n            if (!emoji.search) {\n                resetSearchIndex = true;\n                emoji.search = \",\" + [\n                    [\n                        emoji.id,\n                        false\n                    ],\n                    [\n                        emoji.name,\n                        true\n                    ],\n                    [\n                        emoji.keywords,\n                        false\n                    ],\n                    [\n                        emoji.emoticons,\n                        false\n                    ], \n                ].map(([strings, split])=>{\n                    if (!strings) return;\n                    return (Array.isArray(strings) ? strings : [\n                        strings\n                    ]).map((string)=>{\n                        return (split ? string.split(/[-|_|\\s]+/) : [\n                            string\n                        ]).map((s)=>s.toLowerCase());\n                    }).flat();\n                }).flat().filter((a)=>a && a.trim()).join(\",\");\n                if (emoji.emoticons) for (const emoticon of emoji.emoticons){\n                    if ($7adb23b0109cc36a$export$2d0294657ab35f1b.emoticons[emoticon]) continue;\n                    $7adb23b0109cc36a$export$2d0294657ab35f1b.emoticons[emoticon] = emoji.id;\n                }\n                let skinIndex = 0;\n                for (const skin of emoji.skins){\n                    if (!skin) continue;\n                    skinIndex++;\n                    const { native: native  } = skin;\n                    if (native) {\n                        $7adb23b0109cc36a$export$2d0294657ab35f1b.natives[native] = emoji.id;\n                        emoji.search += `,${native}`;\n                    }\n                    const skinShortcodes = skinIndex == 1 ? \"\" : `:skin-tone-${skinIndex}:`;\n                    skin.shortcodes = `:${emoji.id}:${skinShortcodes}`;\n                }\n            }\n        }\n    }\n    if (resetSearchIndex) (0, $c4d155af13ad4d4b$export$2e2bcd8739ae039).reset();\n    $7adb23b0109cc36a$var$initCallback();\n}\nfunction $7adb23b0109cc36a$export$75fe5f91d452f94b(props, defaultProps, element) {\n    props || (props = {});\n    const _props = {};\n    for(let k in defaultProps)_props[k] = $7adb23b0109cc36a$export$88c9ddb45cea7241(k, props, defaultProps, element);\n    return _props;\n}\nfunction $7adb23b0109cc36a$export$88c9ddb45cea7241(propName, props, defaultProps, element) {\n    const defaults = defaultProps[propName];\n    let value = element && element.getAttribute(propName) || (props[propName] != null && props[propName] != undefined ? props[propName] : null);\n    if (!defaults) return value;\n    if (value != null && defaults.value && typeof defaults.value != typeof value) {\n        if (typeof defaults.value == \"boolean\") value = value == \"false\" ? false : true;\n        else value = defaults.value.constructor(value);\n    }\n    if (defaults.transform && value) value = defaults.transform(value);\n    if (value == null || defaults.choices && defaults.choices.indexOf(value) == -1) value = defaults.value;\n    return value;\n}\n\n\nconst $c4d155af13ad4d4b$var$SHORTCODES_REGEX = /^(?:\\:([^\\:]+)\\:)(?:\\:skin-tone-(\\d)\\:)?$/;\nlet $c4d155af13ad4d4b$var$Pool = null;\nfunction $c4d155af13ad4d4b$var$get(emojiId) {\n    if (emojiId.id) return emojiId;\n    return (0, $7adb23b0109cc36a$export$2d0294657ab35f1b).emojis[emojiId] || (0, $7adb23b0109cc36a$export$2d0294657ab35f1b).emojis[(0, $7adb23b0109cc36a$export$2d0294657ab35f1b).aliases[emojiId]] || (0, $7adb23b0109cc36a$export$2d0294657ab35f1b).emojis[(0, $7adb23b0109cc36a$export$2d0294657ab35f1b).natives[emojiId]];\n}\nfunction $c4d155af13ad4d4b$var$reset() {\n    $c4d155af13ad4d4b$var$Pool = null;\n}\nasync function $c4d155af13ad4d4b$var$search(value, { maxResults: maxResults , caller: caller  } = {}) {\n    if (!value || !value.trim().length) return null;\n    maxResults || (maxResults = 90);\n    await (0, $7adb23b0109cc36a$export$2cd8252107eb640b)(null, {\n        caller: caller || \"SearchIndex.search\"\n    });\n    const values = value.toLowerCase().replace(/(\\w)-/, \"$1 \").split(/[\\s|,]+/).filter((word, i, words)=>{\n        return word.trim() && words.indexOf(word) == i;\n    });\n    if (!values.length) return;\n    let pool = $c4d155af13ad4d4b$var$Pool || ($c4d155af13ad4d4b$var$Pool = Object.values((0, $7adb23b0109cc36a$export$2d0294657ab35f1b).emojis));\n    let results, scores;\n    for (const value1 of values){\n        if (!pool.length) break;\n        results = [];\n        scores = {};\n        for (const emoji of pool){\n            if (!emoji.search) continue;\n            const score = emoji.search.indexOf(`,${value1}`);\n            if (score == -1) continue;\n            results.push(emoji);\n            scores[emoji.id] || (scores[emoji.id] = 0);\n            scores[emoji.id] += emoji.id == value1 ? 0 : score + 1;\n        }\n        pool = results;\n    }\n    if (results.length < 2) return results;\n    results.sort((a, b)=>{\n        const aScore = scores[a.id];\n        const bScore = scores[b.id];\n        if (aScore == bScore) return a.id.localeCompare(b.id);\n        return aScore - bScore;\n    });\n    if (results.length > maxResults) results = results.slice(0, maxResults);\n    return results;\n}\nvar $c4d155af13ad4d4b$export$2e2bcd8739ae039 = {\n    search: $c4d155af13ad4d4b$var$search,\n    get: $c4d155af13ad4d4b$var$get,\n    reset: $c4d155af13ad4d4b$var$reset,\n    SHORTCODES_REGEX: $c4d155af13ad4d4b$var$SHORTCODES_REGEX\n};\n\n\nconst $e6eae5155b87f591$export$bcb25aa587e9cb13 = [\n    \"checkered_flag\",\n    \"crossed_flags\",\n    \"pirate_flag\",\n    \"rainbow-flag\",\n    \"transgender_flag\",\n    \"triangular_flag_on_post\",\n    \"waving_black_flag\",\n    \"waving_white_flag\", \n];\n\n\nfunction $693b183b0a78708f$export$9cb4719e2e525b7a(a, b) {\n    return Array.isArray(a) && Array.isArray(b) && a.length === b.length && a.every((val, index)=>val == b[index]);\n}\nasync function $693b183b0a78708f$export$e772c8ff12451969(frames = 1) {\n    for(let _ in [\n        ...Array(frames).keys()\n    ])await new Promise(requestAnimationFrame);\n}\nfunction $693b183b0a78708f$export$d10ac59fbe52a745(emoji, { skinIndex: skinIndex = 0  } = {}) {\n    const skin = emoji.skins[skinIndex] || (()=>{\n        skinIndex = 0;\n        return emoji.skins[skinIndex];\n    })();\n    const emojiData = {\n        id: emoji.id,\n        name: emoji.name,\n        native: skin.native,\n        unified: skin.unified,\n        keywords: emoji.keywords,\n        shortcodes: skin.shortcodes || emoji.shortcodes\n    };\n    if (emoji.skins.length > 1) emojiData.skin = skinIndex + 1;\n    if (skin.src) emojiData.src = skin.src;\n    if (emoji.aliases && emoji.aliases.length) emojiData.aliases = emoji.aliases;\n    if (emoji.emoticons && emoji.emoticons.length) emojiData.emoticons = emoji.emoticons;\n    return emojiData;\n}\nasync function $693b183b0a78708f$export$5ef5574deca44bc0(nativeString) {\n    const results = await (0, $c4d155af13ad4d4b$export$2e2bcd8739ae039).search(nativeString, {\n        maxResults: 1,\n        caller: \"getEmojiDataFromNative\"\n    });\n    if (!results || !results.length) return null;\n    const emoji = results[0];\n    let skinIndex = 0;\n    for (let skin of emoji.skins){\n        if (skin.native == nativeString) break;\n        skinIndex++;\n    }\n    return $693b183b0a78708f$export$d10ac59fbe52a745(emoji, {\n        skinIndex: skinIndex\n    });\n}\n\n\n\n\n\nconst $fcccfb36ed0cde68$var$categories = {\n    activity: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M12 0C5.373 0 0 5.372 0 12c0 6.627 5.373 12 12 12 6.628 0 12-5.373 12-12 0-6.628-5.372-12-12-12m9.949 11H17.05c.224-2.527 1.232-4.773 1.968-6.113A9.966 9.966 0 0 1 21.949 11M13 11V2.051a9.945 9.945 0 0 1 4.432 1.564c-.858 1.491-2.156 4.22-2.392 7.385H13zm-2 0H8.961c-.238-3.165-1.536-5.894-2.393-7.385A9.95 9.95 0 0 1 11 2.051V11zm0 2v8.949a9.937 9.937 0 0 1-4.432-1.564c.857-1.492 2.155-4.221 2.393-7.385H11zm4.04 0c.236 3.164 1.534 5.893 2.392 7.385A9.92 9.92 0 0 1 13 21.949V13h2.04zM4.982 4.887C5.718 6.227 6.726 8.473 6.951 11h-4.9a9.977 9.977 0 0 1 2.931-6.113M2.051 13h4.9c-.226 2.527-1.233 4.771-1.969 6.113A9.972 9.972 0 0 1 2.051 13m16.967 6.113c-.735-1.342-1.744-3.586-1.968-6.113h4.899a9.961 9.961 0 0 1-2.931 6.113\"\n            })\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 512 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M16.17 337.5c0 44.98 7.565 83.54 13.98 107.9C35.22 464.3 50.46 496 174.9 496c9.566 0 19.59-.4707 29.84-1.271L17.33 307.3C16.53 317.6 16.17 327.7 16.17 337.5zM495.8 174.5c0-44.98-7.565-83.53-13.98-107.9c-4.688-17.54-18.34-31.23-36.04-35.95C435.5 27.91 392.9 16 337 16c-9.564 0-19.59 .4707-29.84 1.271l187.5 187.5C495.5 194.4 495.8 184.3 495.8 174.5zM26.77 248.8l236.3 236.3c142-36.1 203.9-150.4 222.2-221.1L248.9 26.87C106.9 62.96 45.07 177.2 26.77 248.8zM256 335.1c0 9.141-7.474 16-16 16c-4.094 0-8.188-1.564-11.31-4.689L164.7 283.3C161.6 280.2 160 276.1 160 271.1c0-8.529 6.865-16 16-16c4.095 0 8.189 1.562 11.31 4.688l64.01 64C254.4 327.8 256 331.9 256 335.1zM304 287.1c0 9.141-7.474 16-16 16c-4.094 0-8.188-1.564-11.31-4.689L212.7 235.3C209.6 232.2 208 228.1 208 223.1c0-9.141 7.473-16 16-16c4.094 0 8.188 1.562 11.31 4.688l64.01 64.01C302.5 279.8 304 283.9 304 287.1zM256 175.1c0-9.141 7.473-16 16-16c4.094 0 8.188 1.562 11.31 4.688l64.01 64.01c3.125 3.125 4.688 7.219 4.688 11.31c0 9.133-7.468 16-16 16c-4.094 0-8.189-1.562-11.31-4.688l-64.01-64.01C257.6 184.2 256 180.1 256 175.1z\"\n            })\n        })\n    },\n    custom: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 448 512\",\n        children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n            d: \"M417.1 368c-5.937 10.27-16.69 16-27.75 16c-5.422 0-10.92-1.375-15.97-4.281L256 311.4V448c0 17.67-14.33 32-31.1 32S192 465.7 192 448V311.4l-118.3 68.29C68.67 382.6 63.17 384 57.75 384c-11.06 0-21.81-5.734-27.75-16c-8.828-15.31-3.594-34.88 11.72-43.72L159.1 256L41.72 187.7C26.41 178.9 21.17 159.3 29.1 144C36.63 132.5 49.26 126.7 61.65 128.2C65.78 128.7 69.88 130.1 73.72 132.3L192 200.6V64c0-17.67 14.33-32 32-32S256 46.33 256 64v136.6l118.3-68.29c3.838-2.213 7.939-3.539 12.07-4.051C398.7 126.7 411.4 132.5 417.1 144c8.828 15.31 3.594 34.88-11.72 43.72L288 256l118.3 68.28C421.6 333.1 426.8 352.7 417.1 368z\"\n        })\n    }),\n    flags: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M0 0l6.084 24H8L1.916 0zM21 5h-4l-1-4H4l3 12h3l1 4h13L21 5zM6.563 3h7.875l2 8H8.563l-2-8zm8.832 10l-2.856 1.904L12.063 13h3.332zM19 13l-1.5-6h1.938l2 8H16l3-2z\"\n            })\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 512 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M64 496C64 504.8 56.75 512 48 512h-32C7.25 512 0 504.8 0 496V32c0-17.75 14.25-32 32-32s32 14.25 32 32V496zM476.3 0c-6.365 0-13.01 1.35-19.34 4.233c-45.69 20.86-79.56 27.94-107.8 27.94c-59.96 0-94.81-31.86-163.9-31.87C160.9 .3055 131.6 4.867 96 15.75v350.5c32-9.984 59.87-14.1 84.85-14.1c73.63 0 124.9 31.78 198.6 31.78c31.91 0 68.02-5.971 111.1-23.09C504.1 355.9 512 344.4 512 332.1V30.73C512 11.1 495.3 0 476.3 0z\"\n            })\n        })\n    },\n    foods: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M17 4.978c-1.838 0-2.876.396-3.68.934.513-1.172 1.768-2.934 4.68-2.934a1 1 0 0 0 0-2c-2.921 0-4.629 1.365-5.547 2.512-.064.078-.119.162-.18.244C11.73 1.838 10.798.023 9.207.023 8.579.022 7.85.306 7 .978 5.027 2.54 5.329 3.902 6.492 4.999 3.609 5.222 0 7.352 0 12.969c0 4.582 4.961 11.009 9 11.009 1.975 0 2.371-.486 3-1 .629.514 1.025 1 3 1 4.039 0 9-6.418 9-11 0-5.953-4.055-8-7-8M8.242 2.546c.641-.508.943-.523.965-.523.426.169.975 1.405 1.357 3.055-1.527-.629-2.741-1.352-2.98-1.846.059-.112.241-.356.658-.686M15 21.978c-1.08 0-1.21-.109-1.559-.402l-.176-.146c-.367-.302-.816-.452-1.266-.452s-.898.15-1.266.452l-.176.146c-.347.292-.477.402-1.557.402-2.813 0-7-5.389-7-9.009 0-5.823 4.488-5.991 5-5.991 1.939 0 2.484.471 3.387 1.251l.323.276a1.995 1.995 0 0 0 2.58 0l.323-.276c.902-.78 1.447-1.251 3.387-1.251.512 0 5 .168 5 6 0 3.617-4.187 9-7 9\"\n            })\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 512 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M481.9 270.1C490.9 279.1 496 291.3 496 304C496 316.7 490.9 328.9 481.9 337.9C472.9 346.9 460.7 352 448 352H64C51.27 352 39.06 346.9 30.06 337.9C21.06 328.9 16 316.7 16 304C16 291.3 21.06 279.1 30.06 270.1C39.06 261.1 51.27 256 64 256H448C460.7 256 472.9 261.1 481.9 270.1zM475.3 388.7C478.3 391.7 480 395.8 480 400V416C480 432.1 473.3 449.3 461.3 461.3C449.3 473.3 432.1 480 416 480H96C79.03 480 62.75 473.3 50.75 461.3C38.74 449.3 32 432.1 32 416V400C32 395.8 33.69 391.7 36.69 388.7C39.69 385.7 43.76 384 48 384H464C468.2 384 472.3 385.7 475.3 388.7zM50.39 220.8C45.93 218.6 42.03 215.5 38.97 211.6C35.91 207.7 33.79 203.2 32.75 198.4C31.71 193.5 31.8 188.5 32.99 183.7C54.98 97.02 146.5 32 256 32C365.5 32 457 97.02 479 183.7C480.2 188.5 480.3 193.5 479.2 198.4C478.2 203.2 476.1 207.7 473 211.6C469.1 215.5 466.1 218.6 461.6 220.8C457.2 222.9 452.3 224 447.3 224H64.67C59.73 224 54.84 222.9 50.39 220.8zM372.7 116.7C369.7 119.7 368 123.8 368 128C368 131.2 368.9 134.3 370.7 136.9C372.5 139.5 374.1 141.6 377.9 142.8C380.8 143.1 384 144.3 387.1 143.7C390.2 143.1 393.1 141.6 395.3 139.3C397.6 137.1 399.1 134.2 399.7 131.1C400.3 128 399.1 124.8 398.8 121.9C397.6 118.1 395.5 116.5 392.9 114.7C390.3 112.9 387.2 111.1 384 111.1C379.8 111.1 375.7 113.7 372.7 116.7V116.7zM244.7 84.69C241.7 87.69 240 91.76 240 96C240 99.16 240.9 102.3 242.7 104.9C244.5 107.5 246.1 109.6 249.9 110.8C252.8 111.1 256 112.3 259.1 111.7C262.2 111.1 265.1 109.6 267.3 107.3C269.6 105.1 271.1 102.2 271.7 99.12C272.3 96.02 271.1 92.8 270.8 89.88C269.6 86.95 267.5 84.45 264.9 82.7C262.3 80.94 259.2 79.1 256 79.1C251.8 79.1 247.7 81.69 244.7 84.69V84.69zM116.7 116.7C113.7 119.7 112 123.8 112 128C112 131.2 112.9 134.3 114.7 136.9C116.5 139.5 118.1 141.6 121.9 142.8C124.8 143.1 128 144.3 131.1 143.7C134.2 143.1 137.1 141.6 139.3 139.3C141.6 137.1 143.1 134.2 143.7 131.1C144.3 128 143.1 124.8 142.8 121.9C141.6 118.1 139.5 116.5 136.9 114.7C134.3 112.9 131.2 111.1 128 111.1C123.8 111.1 119.7 113.7 116.7 116.7L116.7 116.7z\"\n            })\n        })\n    },\n    frequent: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M13 4h-2l-.001 7H9v2h2v2h2v-2h4v-2h-4z\"\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10\"\n                })\n            ]\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 512 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512zM232 256C232 264 236 271.5 242.7 275.1L338.7 339.1C349.7 347.3 364.6 344.3 371.1 333.3C379.3 322.3 376.3 307.4 365.3 300L280 243.2V120C280 106.7 269.3 96 255.1 96C242.7 96 231.1 106.7 231.1 120L232 256z\"\n            })\n        })\n    },\n    nature: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M15.5 8a1.5 1.5 0 1 0 .001 3.001A1.5 1.5 0 0 0 15.5 8M8.5 8a1.5 1.5 0 1 0 .001 3.001A1.5 1.5 0 0 0 8.5 8\"\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M18.933 0h-.027c-.97 0-2.138.787-3.018 1.497-1.274-.374-2.612-.51-3.887-.51-1.285 0-2.616.133-3.874.517C7.245.79 6.069 0 5.093 0h-.027C3.352 0 .07 2.67.002 7.026c-.039 2.479.276 4.238 1.04 5.013.254.258.882.677 1.295.882.191 3.177.922 5.238 2.536 6.38.897.637 2.187.949 3.2 1.102C8.04 20.6 8 20.795 8 21c0 1.773 2.35 3 4 3 1.648 0 4-1.227 4-3 0-.201-.038-.393-.072-.586 2.573-.385 5.435-1.877 5.925-7.587.396-.22.887-.568 1.104-.788.763-.774 1.079-2.534 1.04-5.013C23.929 2.67 20.646 0 18.933 0M3.223 9.135c-.237.281-.837 1.155-.884 1.238-.15-.41-.368-1.349-.337-3.291.051-3.281 2.478-4.972 3.091-5.031.256.015.731.27 1.265.646-1.11 1.171-2.275 2.915-2.352 5.125-.133.546-.398.858-.783 1.313M12 22c-.901 0-1.954-.693-2-1 0-.654.475-1.236 1-1.602V20a1 1 0 1 0 2 0v-.602c.524.365 1 .947 1 1.602-.046.307-1.099 1-2 1m3-3.48v.02a4.752 4.752 0 0 0-1.262-1.02c1.092-.516 2.239-1.334 2.239-2.217 0-1.842-1.781-2.195-3.977-2.195-2.196 0-3.978.354-3.978 2.195 0 .883 1.148 1.701 2.238 2.217A4.8 4.8 0 0 0 9 18.539v-.025c-1-.076-2.182-.281-2.973-.842-1.301-.92-1.838-3.045-1.853-6.478l.023-.041c.496-.826 1.49-1.45 1.804-3.102 0-2.047 1.357-3.631 2.362-4.522C9.37 3.178 10.555 3 11.948 3c1.447 0 2.685.192 3.733.57 1 .9 2.316 2.465 2.316 4.48.313 1.651 1.307 2.275 1.803 3.102.035.058.068.117.102.178-.059 5.967-1.949 7.01-4.902 7.19m6.628-8.202c-.037-.065-.074-.13-.113-.195a7.587 7.587 0 0 0-.739-.987c-.385-.455-.648-.768-.782-1.313-.076-2.209-1.241-3.954-2.353-5.124.531-.376 1.004-.63 1.261-.647.636.071 3.044 1.764 3.096 5.031.027 1.81-.347 3.218-.37 3.235\"\n                })\n            ]\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 576 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M332.7 19.85C334.6 8.395 344.5 0 356.1 0C363.6 0 370.6 3.52 375.1 9.502L392 32H444.1C456.8 32 469.1 37.06 478.1 46.06L496 64H552C565.3 64 576 74.75 576 88V112C576 156.2 540.2 192 496 192H426.7L421.6 222.5L309.6 158.5L332.7 19.85zM448 64C439.2 64 432 71.16 432 80C432 88.84 439.2 96 448 96C456.8 96 464 88.84 464 80C464 71.16 456.8 64 448 64zM416 256.1V480C416 497.7 401.7 512 384 512H352C334.3 512 320 497.7 320 480V364.8C295.1 377.1 268.8 384 240 384C211.2 384 184 377.1 160 364.8V480C160 497.7 145.7 512 128 512H96C78.33 512 64 497.7 64 480V249.8C35.23 238.9 12.64 214.5 4.836 183.3L.9558 167.8C-3.331 150.6 7.094 133.2 24.24 128.1C41.38 124.7 58.76 135.1 63.05 152.2L66.93 167.8C70.49 182 83.29 191.1 97.97 191.1H303.8L416 256.1z\"\n            })\n        })\n    },\n    objects: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M12 0a9 9 0 0 0-5 16.482V21s2.035 3 5 3 5-3 5-3v-4.518A9 9 0 0 0 12 0zm0 2c3.86 0 7 3.141 7 7s-3.14 7-7 7-7-3.141-7-7 3.14-7 7-7zM9 17.477c.94.332 1.946.523 3 .523s2.06-.19 3-.523v.834c-.91.436-1.925.689-3 .689a6.924 6.924 0 0 1-3-.69v-.833zm.236 3.07A8.854 8.854 0 0 0 12 21c.965 0 1.888-.167 2.758-.451C14.155 21.173 13.153 22 12 22c-1.102 0-2.117-.789-2.764-1.453z\"\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M14.745 12.449h-.004c-.852-.024-1.188-.858-1.577-1.824-.421-1.061-.703-1.561-1.182-1.566h-.009c-.481 0-.783.497-1.235 1.537-.436.982-.801 1.811-1.636 1.791l-.276-.043c-.565-.171-.853-.691-1.284-1.794-.125-.313-.202-.632-.27-.913-.051-.213-.127-.53-.195-.634C7.067 9.004 7.039 9 6.99 9A1 1 0 0 1 7 7h.01c1.662.017 2.015 1.373 2.198 2.134.486-.981 1.304-2.058 2.797-2.075 1.531.018 2.28 1.153 2.731 2.141l.002-.008C14.944 8.424 15.327 7 16.979 7h.032A1 1 0 1 1 17 9h-.011c-.149.076-.256.474-.319.709a6.484 6.484 0 0 1-.311.951c-.429.973-.79 1.789-1.614 1.789\"\n                })\n            ]\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 384 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M112.1 454.3c0 6.297 1.816 12.44 5.284 17.69l17.14 25.69c5.25 7.875 17.17 14.28 26.64 14.28h61.67c9.438 0 21.36-6.401 26.61-14.28l17.08-25.68c2.938-4.438 5.348-12.37 5.348-17.7L272 415.1h-160L112.1 454.3zM191.4 .0132C89.44 .3257 16 82.97 16 175.1c0 44.38 16.44 84.84 43.56 115.8c16.53 18.84 42.34 58.23 52.22 91.45c.0313 .25 .0938 .5166 .125 .7823h160.2c.0313-.2656 .0938-.5166 .125-.7823c9.875-33.22 35.69-72.61 52.22-91.45C351.6 260.8 368 220.4 368 175.1C368 78.61 288.9-.2837 191.4 .0132zM192 96.01c-44.13 0-80 35.89-80 79.1C112 184.8 104.8 192 96 192S80 184.8 80 176c0-61.76 50.25-111.1 112-111.1c8.844 0 16 7.159 16 16S200.8 96.01 192 96.01z\"\n            })\n        })\n    },\n    people: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10\"\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M8 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 8 7M16 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 16 7M15.232 15c-.693 1.195-1.87 2-3.349 2-1.477 0-2.655-.805-3.347-2H15m3-2H6a6 6 0 1 0 12 0\"\n                })\n            ]\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 512 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256zM256 432C332.1 432 396.2 382 415.2 314.1C419.1 300.4 407.8 288 393.6 288H118.4C104.2 288 92.92 300.4 96.76 314.1C115.8 382 179.9 432 256 432V432zM176.4 160C158.7 160 144.4 174.3 144.4 192C144.4 209.7 158.7 224 176.4 224C194 224 208.4 209.7 208.4 192C208.4 174.3 194 160 176.4 160zM336.4 224C354 224 368.4 209.7 368.4 192C368.4 174.3 354 160 336.4 160C318.7 160 304.4 174.3 304.4 192C304.4 209.7 318.7 224 336.4 224z\"\n            })\n        })\n    },\n    places: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M6.5 12C5.122 12 4 13.121 4 14.5S5.122 17 6.5 17 9 15.879 9 14.5 7.878 12 6.5 12m0 3c-.275 0-.5-.225-.5-.5s.225-.5.5-.5.5.225.5.5-.225.5-.5.5M17.5 12c-1.378 0-2.5 1.121-2.5 2.5s1.122 2.5 2.5 2.5 2.5-1.121 2.5-2.5-1.122-2.5-2.5-2.5m0 3c-.275 0-.5-.225-.5-.5s.225-.5.5-.5.5.225.5.5-.225.5-.5.5\"\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M22.482 9.494l-1.039-.346L21.4 9h.6c.552 0 1-.439 1-.992 0-.006-.003-.008-.003-.008H23c0-1-.889-2-1.984-2h-.642l-.731-1.717C19.262 3.012 18.091 2 16.764 2H7.236C5.909 2 4.738 3.012 4.357 4.283L3.626 6h-.642C1.889 6 1 7 1 8h.003S1 8.002 1 8.008C1 8.561 1.448 9 2 9h.6l-.043.148-1.039.346a2.001 2.001 0 0 0-1.359 2.097l.751 7.508a1 1 0 0 0 .994.901H3v1c0 1.103.896 2 2 2h2c1.104 0 2-.897 2-2v-1h6v1c0 1.103.896 2 2 2h2c1.104 0 2-.897 2-2v-1h1.096a.999.999 0 0 0 .994-.901l.751-7.508a2.001 2.001 0 0 0-1.359-2.097M6.273 4.857C6.402 4.43 6.788 4 7.236 4h9.527c.448 0 .834.43.963.857L19.313 9H4.688l1.585-4.143zM7 21H5v-1h2v1zm12 0h-2v-1h2v1zm2.189-3H2.811l-.662-6.607L3 11h18l.852.393L21.189 18z\"\n                })\n            ]\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 512 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M39.61 196.8L74.8 96.29C88.27 57.78 124.6 32 165.4 32H346.6C387.4 32 423.7 57.78 437.2 96.29L472.4 196.8C495.6 206.4 512 229.3 512 256V448C512 465.7 497.7 480 480 480H448C430.3 480 416 465.7 416 448V400H96V448C96 465.7 81.67 480 64 480H32C14.33 480 0 465.7 0 448V256C0 229.3 16.36 206.4 39.61 196.8V196.8zM109.1 192H402.9L376.8 117.4C372.3 104.6 360.2 96 346.6 96H165.4C151.8 96 139.7 104.6 135.2 117.4L109.1 192zM96 256C78.33 256 64 270.3 64 288C64 305.7 78.33 320 96 320C113.7 320 128 305.7 128 288C128 270.3 113.7 256 96 256zM416 320C433.7 320 448 305.7 448 288C448 270.3 433.7 256 416 256C398.3 256 384 270.3 384 288C384 305.7 398.3 320 416 320z\"\n            })\n        })\n    },\n    symbols: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M0 0h11v2H0zM4 11h3V6h4V4H0v2h4zM15.5 17c1.381 0 2.5-1.116 2.5-2.493s-1.119-2.493-2.5-2.493S13 13.13 13 14.507 14.119 17 15.5 17m0-2.986c.276 0 .5.222.5.493 0 .272-.224.493-.5.493s-.5-.221-.5-.493.224-.493.5-.493M21.5 19.014c-1.381 0-2.5 1.116-2.5 2.493S20.119 24 21.5 24s2.5-1.116 2.5-2.493-1.119-2.493-2.5-2.493m0 2.986a.497.497 0 0 1-.5-.493c0-.271.224-.493.5-.493s.5.222.5.493a.497.497 0 0 1-.5.493M22 13l-9 9 1.513 1.5 8.99-9.009zM17 11c2.209 0 4-1.119 4-2.5V2s.985-.161 1.498.949C23.01 4.055 23 6 23 6s1-1.119 1-3.135C24-.02 21 0 21 0h-2v6.347A5.853 5.853 0 0 0 17 6c-2.209 0-4 1.119-4 2.5s1.791 2.5 4 2.5M10.297 20.482l-1.475-1.585a47.54 47.54 0 0 1-1.442 1.129c-.307-.288-.989-1.016-2.045-2.183.902-.836 1.479-1.466 1.729-1.892s.376-.871.376-1.336c0-.592-.273-1.178-.818-1.759-.546-.581-1.329-.871-2.349-.871-1.008 0-1.79.293-2.344.879-.556.587-.832 1.181-.832 1.784 0 .813.419 1.748 1.256 2.805-.847.614-1.444 1.208-1.794 1.784a3.465 3.465 0 0 0-.523 1.833c0 .857.308 1.56.924 2.107.616.549 1.423.823 2.42.823 1.173 0 2.444-.379 3.813-1.137L8.235 24h2.819l-2.09-2.383 1.333-1.135zm-6.736-6.389a1.02 1.02 0 0 1 .73-.286c.31 0 .559.085.747.254a.849.849 0 0 1 .283.659c0 .518-.419 1.112-1.257 1.784-.536-.651-.805-1.231-.805-1.742a.901.901 0 0 1 .302-.669M3.74 22c-.427 0-.778-.116-1.057-.349-.279-.232-.418-.487-.418-.766 0-.594.509-1.288 1.527-2.083.968 1.134 1.717 1.946 2.248 2.438-.921.507-1.686.76-2.3.76\"\n            })\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 512 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M500.3 7.251C507.7 13.33 512 22.41 512 31.1V175.1C512 202.5 483.3 223.1 447.1 223.1C412.7 223.1 383.1 202.5 383.1 175.1C383.1 149.5 412.7 127.1 447.1 127.1V71.03L351.1 90.23V207.1C351.1 234.5 323.3 255.1 287.1 255.1C252.7 255.1 223.1 234.5 223.1 207.1C223.1 181.5 252.7 159.1 287.1 159.1V63.1C287.1 48.74 298.8 35.61 313.7 32.62L473.7 .6198C483.1-1.261 492.9 1.173 500.3 7.251H500.3zM74.66 303.1L86.5 286.2C92.43 277.3 102.4 271.1 113.1 271.1H174.9C185.6 271.1 195.6 277.3 201.5 286.2L213.3 303.1H239.1C266.5 303.1 287.1 325.5 287.1 351.1V463.1C287.1 490.5 266.5 511.1 239.1 511.1H47.1C21.49 511.1-.0019 490.5-.0019 463.1V351.1C-.0019 325.5 21.49 303.1 47.1 303.1H74.66zM143.1 359.1C117.5 359.1 95.1 381.5 95.1 407.1C95.1 434.5 117.5 455.1 143.1 455.1C170.5 455.1 191.1 434.5 191.1 407.1C191.1 381.5 170.5 359.1 143.1 359.1zM440.3 367.1H496C502.7 367.1 508.6 372.1 510.1 378.4C513.3 384.6 511.6 391.7 506.5 396L378.5 508C372.9 512.1 364.6 513.3 358.6 508.9C352.6 504.6 350.3 496.6 353.3 489.7L391.7 399.1H336C329.3 399.1 323.4 395.9 321 389.6C318.7 383.4 320.4 376.3 325.5 371.1L453.5 259.1C459.1 255 467.4 254.7 473.4 259.1C479.4 263.4 481.6 271.4 478.7 278.3L440.3 367.1zM116.7 219.1L19.85 119.2C-8.112 90.26-6.614 42.31 24.85 15.34C51.82-8.137 93.26-3.642 118.2 21.83L128.2 32.32L137.7 21.83C162.7-3.642 203.6-8.137 231.6 15.34C262.6 42.31 264.1 90.26 236.1 119.2L139.7 219.1C133.2 225.6 122.7 225.6 116.7 219.1H116.7z\"\n            })\n        })\n    }\n};\nconst $fcccfb36ed0cde68$var$search = {\n    loupe: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 20 20\",\n        children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n            d: \"M12.9 14.32a8 8 0 1 1 1.41-1.41l5.35 5.33-1.42 1.42-5.33-5.34zM8 14A6 6 0 1 0 8 2a6 6 0 0 0 0 12z\"\n        })\n    }),\n    delete: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 20 20\",\n        children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n            d: \"M10 8.586L2.929 1.515 1.515 2.929 8.586 10l-7.071 7.071 1.414 1.414L10 11.414l7.071 7.071 1.414-1.414L11.414 10l7.071-7.071-1.414-1.414L10 8.586z\"\n        })\n    })\n};\nvar $fcccfb36ed0cde68$export$2e2bcd8739ae039 = {\n    categories: $fcccfb36ed0cde68$var$categories,\n    search: $fcccfb36ed0cde68$var$search\n};\n\n\n\n\n\nfunction $254755d3f438722f$export$2e2bcd8739ae039(props) {\n    let { id: id , skin: skin , emoji: emoji  } = props;\n    if (props.shortcodes) {\n        const matches = props.shortcodes.match((0, $c4d155af13ad4d4b$export$2e2bcd8739ae039).SHORTCODES_REGEX);\n        if (matches) {\n            id = matches[1];\n            if (matches[2]) skin = matches[2];\n        }\n    }\n    emoji || (emoji = (0, $c4d155af13ad4d4b$export$2e2bcd8739ae039).get(id || props.native));\n    if (!emoji) return props.fallback;\n    const emojiSkin = emoji.skins[skin - 1] || emoji.skins[0];\n    const imageSrc = emojiSkin.src || (props.set != \"native\" && !props.spritesheet ? typeof props.getImageURL === \"function\" ? props.getImageURL(props.set, emojiSkin.unified) : `https://cdn.jsdelivr.net/npm/emoji-datasource-${props.set}@15.0.1/img/${props.set}/64/${emojiSkin.unified}.png` : undefined);\n    const spritesheetSrc = typeof props.getSpritesheetURL === \"function\" ? props.getSpritesheetURL(props.set) : `https://cdn.jsdelivr.net/npm/emoji-datasource-${props.set}@15.0.1/img/${props.set}/sheets-256/64.png`;\n    return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n        class: \"emoji-mart-emoji\",\n        \"data-emoji-set\": props.set,\n        children: imageSrc ? /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"img\", {\n            style: {\n                maxWidth: props.size || \"1em\",\n                maxHeight: props.size || \"1em\",\n                display: \"inline-block\"\n            },\n            alt: emojiSkin.native || emojiSkin.shortcodes,\n            src: imageSrc\n        }) : props.set == \"native\" ? /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n            style: {\n                fontSize: props.size,\n                fontFamily: '\"EmojiMart\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Segoe UI\", \"Apple Color Emoji\", \"Twemoji Mozilla\", \"Noto Color Emoji\", \"Android Emoji\"'\n            },\n            children: emojiSkin.native\n        }) : /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n            style: {\n                display: \"block\",\n                width: props.size,\n                height: props.size,\n                backgroundImage: `url(${spritesheetSrc})`,\n                backgroundSize: `${100 * (0, $7adb23b0109cc36a$export$2d0294657ab35f1b).sheet.cols}% ${100 * (0, $7adb23b0109cc36a$export$2d0294657ab35f1b).sheet.rows}%`,\n                backgroundPosition: `${100 / ((0, $7adb23b0109cc36a$export$2d0294657ab35f1b).sheet.cols - 1) * emojiSkin.x}% ${100 / ((0, $7adb23b0109cc36a$export$2d0294657ab35f1b).sheet.rows - 1) * emojiSkin.y}%`\n            }\n        })\n    });\n}\n\n\n\n\n\n\n\nconst $6f57cc9cd54c5aaa$var$WindowHTMLElement = typeof window !== \"undefined\" && window.HTMLElement ? window.HTMLElement : Object;\nclass $6f57cc9cd54c5aaa$export$2e2bcd8739ae039 extends $6f57cc9cd54c5aaa$var$WindowHTMLElement {\n    static get observedAttributes() {\n        return Object.keys(this.Props);\n    }\n    update(props = {}) {\n        for(let k in props)this.attributeChangedCallback(k, null, props[k]);\n    }\n    attributeChangedCallback(attr, _, newValue) {\n        if (!this.component) return;\n        const value = (0, $7adb23b0109cc36a$export$88c9ddb45cea7241)(attr, {\n            [attr]: newValue\n        }, this.constructor.Props, this);\n        if (this.component.componentWillReceiveProps) this.component.componentWillReceiveProps({\n            [attr]: value\n        });\n        else {\n            this.component.props[attr] = value;\n            this.component.forceUpdate();\n        }\n    }\n    disconnectedCallback() {\n        this.disconnected = true;\n        if (this.component && this.component.unregister) this.component.unregister();\n    }\n    constructor(props = {}){\n        super();\n        this.props = props;\n        if (props.parent || props.ref) {\n            let ref = null;\n            const parent = props.parent || (ref = props.ref && props.ref.current);\n            if (ref) ref.innerHTML = \"\";\n            if (parent) parent.appendChild(this);\n        }\n    }\n}\n\n\n\nclass $26f27c338a96b1a6$export$2e2bcd8739ae039 extends (0, $6f57cc9cd54c5aaa$export$2e2bcd8739ae039) {\n    setShadow() {\n        this.attachShadow({\n            mode: \"open\"\n        });\n    }\n    injectStyles(styles) {\n        if (!styles) return;\n        const style = document.createElement(\"style\");\n        style.textContent = styles;\n        this.shadowRoot.insertBefore(style, this.shadowRoot.firstChild);\n    }\n    constructor(props, { styles: styles  } = {}){\n        super(props);\n        this.setShadow();\n        this.injectStyles(styles);\n    }\n}\n\n\n\n\n\n\nvar $3d90f6e46fb2dd47$export$2e2bcd8739ae039 = {\n    fallback: \"\",\n    id: \"\",\n    native: \"\",\n    shortcodes: \"\",\n    size: {\n        value: \"\",\n        transform: (value)=>{\n            // If the value is a number, then we assume it’s a pixel value.\n            if (!/\\D/.test(value)) return `${value}px`;\n            return value;\n        }\n    },\n    // Shared\n    set: (0, $b247ea80b67298d5$export$2e2bcd8739ae039).set,\n    skin: (0, $b247ea80b67298d5$export$2e2bcd8739ae039).skin\n};\n\n\nclass $331b4160623139bf$export$2e2bcd8739ae039 extends (0, $6f57cc9cd54c5aaa$export$2e2bcd8739ae039) {\n    async connectedCallback() {\n        const props = (0, $7adb23b0109cc36a$export$75fe5f91d452f94b)(this.props, (0, $3d90f6e46fb2dd47$export$2e2bcd8739ae039), this);\n        props.element = this;\n        props.ref = (component)=>{\n            this.component = component;\n        };\n        await (0, $7adb23b0109cc36a$export$2cd8252107eb640b)();\n        if (this.disconnected) return;\n        (0, $fb96b826c0c5f37a$export$b3890eb0ae9dca99)(/*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)((0, $254755d3f438722f$export$2e2bcd8739ae039), {\n            ...props\n        }), this);\n    }\n    constructor(props){\n        super(props);\n    }\n}\n(0, $c770c458706daa72$export$2e2bcd8739ae039)($331b4160623139bf$export$2e2bcd8739ae039, \"Props\", (0, $3d90f6e46fb2dd47$export$2e2bcd8739ae039));\nif (typeof customElements !== \"undefined\" && !customElements.get(\"em-emoji\")) customElements.define(\"em-emoji\", $331b4160623139bf$export$2e2bcd8739ae039);\n\n\n\n\n\n\nvar $1a9a8ef576b7773d$var$t, $1a9a8ef576b7773d$var$u, $1a9a8ef576b7773d$var$r, $1a9a8ef576b7773d$var$o = 0, $1a9a8ef576b7773d$var$i = [], $1a9a8ef576b7773d$var$c = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__b, $1a9a8ef576b7773d$var$f = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__r, $1a9a8ef576b7773d$var$e = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).diffed, $1a9a8ef576b7773d$var$a = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__c, $1a9a8ef576b7773d$var$v = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).unmount;\nfunction $1a9a8ef576b7773d$var$m(t1, r1) {\n    (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__h && (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__h($1a9a8ef576b7773d$var$u, t1, $1a9a8ef576b7773d$var$o || r1), $1a9a8ef576b7773d$var$o = 0;\n    var i1 = $1a9a8ef576b7773d$var$u.__H || ($1a9a8ef576b7773d$var$u.__H = {\n        __: [],\n        __h: []\n    });\n    return t1 >= i1.__.length && i1.__.push({}), i1.__[t1];\n}\nfunction $1a9a8ef576b7773d$export$60241385465d0a34(n1) {\n    return $1a9a8ef576b7773d$var$o = 1, $1a9a8ef576b7773d$export$13e3392192263954($1a9a8ef576b7773d$var$w, n1);\n}\nfunction $1a9a8ef576b7773d$export$13e3392192263954(n2, r2, o1) {\n    var i2 = $1a9a8ef576b7773d$var$m($1a9a8ef576b7773d$var$t++, 2);\n    return i2.t = n2, i2.__c || (i2.__ = [\n        o1 ? o1(r2) : $1a9a8ef576b7773d$var$w(void 0, r2),\n        function(n3) {\n            var t2 = i2.t(i2.__[0], n3);\n            i2.__[0] !== t2 && (i2.__ = [\n                t2,\n                i2.__[1]\n            ], i2.__c.setState({}));\n        }\n    ], i2.__c = $1a9a8ef576b7773d$var$u), i2.__;\n}\nfunction $1a9a8ef576b7773d$export$6d9c69b0de29b591(r3, o2) {\n    var i3 = $1a9a8ef576b7773d$var$m($1a9a8ef576b7773d$var$t++, 3);\n    !(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__s && $1a9a8ef576b7773d$var$k(i3.__H, o2) && (i3.__ = r3, i3.__H = o2, $1a9a8ef576b7773d$var$u.__H.__h.push(i3));\n}\nfunction $1a9a8ef576b7773d$export$e5c5a5f917a5871c(r4, o3) {\n    var i4 = $1a9a8ef576b7773d$var$m($1a9a8ef576b7773d$var$t++, 4);\n    !(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__s && $1a9a8ef576b7773d$var$k(i4.__H, o3) && (i4.__ = r4, i4.__H = o3, $1a9a8ef576b7773d$var$u.__h.push(i4));\n}\nfunction $1a9a8ef576b7773d$export$b8f5890fc79d6aca(n4) {\n    return $1a9a8ef576b7773d$var$o = 5, $1a9a8ef576b7773d$export$1538c33de8887b59(function() {\n        return {\n            current: n4\n        };\n    }, []);\n}\nfunction $1a9a8ef576b7773d$export$d5a552a76deda3c2(n5, t3, u1) {\n    $1a9a8ef576b7773d$var$o = 6, $1a9a8ef576b7773d$export$e5c5a5f917a5871c(function() {\n        \"function\" == typeof n5 ? n5(t3()) : n5 && (n5.current = t3());\n    }, null == u1 ? u1 : u1.concat(n5));\n}\nfunction $1a9a8ef576b7773d$export$1538c33de8887b59(n6, u2) {\n    var r5 = $1a9a8ef576b7773d$var$m($1a9a8ef576b7773d$var$t++, 7);\n    return $1a9a8ef576b7773d$var$k(r5.__H, u2) && (r5.__ = n6(), r5.__H = u2, r5.__h = n6), r5.__;\n}\nfunction $1a9a8ef576b7773d$export$35808ee640e87ca7(n7, t4) {\n    return $1a9a8ef576b7773d$var$o = 8, $1a9a8ef576b7773d$export$1538c33de8887b59(function() {\n        return n7;\n    }, t4);\n}\nfunction $1a9a8ef576b7773d$export$fae74005e78b1a27(n8) {\n    var r6 = $1a9a8ef576b7773d$var$u.context[n8.__c], o4 = $1a9a8ef576b7773d$var$m($1a9a8ef576b7773d$var$t++, 9);\n    return o4.c = n8, r6 ? (null == o4.__ && (o4.__ = !0, r6.sub($1a9a8ef576b7773d$var$u)), r6.props.value) : n8.__;\n}\nfunction $1a9a8ef576b7773d$export$dc8fbce3eb94dc1e(t5, u3) {\n    (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).useDebugValue && (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).useDebugValue(u3 ? u3(t5) : t5);\n}\nfunction $1a9a8ef576b7773d$export$c052f6604b7d51fe(n9) {\n    var r7 = $1a9a8ef576b7773d$var$m($1a9a8ef576b7773d$var$t++, 10), o5 = $1a9a8ef576b7773d$export$60241385465d0a34();\n    return r7.__ = n9, $1a9a8ef576b7773d$var$u.componentDidCatch || ($1a9a8ef576b7773d$var$u.componentDidCatch = function(n10) {\n        r7.__ && r7.__(n10), o5[1](n10);\n    }), [\n        o5[0],\n        function() {\n            o5[1](void 0);\n        }\n    ];\n}\nfunction $1a9a8ef576b7773d$var$x() {\n    var t6;\n    for($1a9a8ef576b7773d$var$i.sort(function(n11, t7) {\n        return n11.__v.__b - t7.__v.__b;\n    }); t6 = $1a9a8ef576b7773d$var$i.pop();)if (t6.__P) try {\n        t6.__H.__h.forEach($1a9a8ef576b7773d$var$g), t6.__H.__h.forEach($1a9a8ef576b7773d$var$j), t6.__H.__h = [];\n    } catch (u4) {\n        t6.__H.__h = [], (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__e(u4, t6.__v);\n    }\n}\n(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__b = function(n12) {\n    $1a9a8ef576b7773d$var$u = null, $1a9a8ef576b7773d$var$c && $1a9a8ef576b7773d$var$c(n12);\n}, (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__r = function(n13) {\n    $1a9a8ef576b7773d$var$f && $1a9a8ef576b7773d$var$f(n13), $1a9a8ef576b7773d$var$t = 0;\n    var r8 = ($1a9a8ef576b7773d$var$u = n13.__c).__H;\n    r8 && (r8.__h.forEach($1a9a8ef576b7773d$var$g), r8.__h.forEach($1a9a8ef576b7773d$var$j), r8.__h = []);\n}, (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).diffed = function(t8) {\n    $1a9a8ef576b7773d$var$e && $1a9a8ef576b7773d$var$e(t8);\n    var o6 = t8.__c;\n    o6 && o6.__H && o6.__H.__h.length && (1 !== $1a9a8ef576b7773d$var$i.push(o6) && $1a9a8ef576b7773d$var$r === (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).requestAnimationFrame || (($1a9a8ef576b7773d$var$r = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).requestAnimationFrame) || function(n14) {\n        var t9, u5 = function() {\n            clearTimeout(r9), $1a9a8ef576b7773d$var$b && cancelAnimationFrame(t9), setTimeout(n14);\n        }, r9 = setTimeout(u5, 100);\n        $1a9a8ef576b7773d$var$b && (t9 = requestAnimationFrame(u5));\n    })($1a9a8ef576b7773d$var$x)), $1a9a8ef576b7773d$var$u = null;\n}, (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__c = function(t10, u6) {\n    u6.some(function(t11) {\n        try {\n            t11.__h.forEach($1a9a8ef576b7773d$var$g), t11.__h = t11.__h.filter(function(n15) {\n                return !n15.__ || $1a9a8ef576b7773d$var$j(n15);\n            });\n        } catch (r10) {\n            u6.some(function(n16) {\n                n16.__h && (n16.__h = []);\n            }), u6 = [], (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__e(r10, t11.__v);\n        }\n    }), $1a9a8ef576b7773d$var$a && $1a9a8ef576b7773d$var$a(t10, u6);\n}, (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).unmount = function(t12) {\n    $1a9a8ef576b7773d$var$v && $1a9a8ef576b7773d$var$v(t12);\n    var u7, r11 = t12.__c;\n    r11 && r11.__H && (r11.__H.__.forEach(function(n17) {\n        try {\n            $1a9a8ef576b7773d$var$g(n17);\n        } catch (n18) {\n            u7 = n18;\n        }\n    }), u7 && (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__e(u7, r11.__v));\n};\nvar $1a9a8ef576b7773d$var$b = \"function\" == typeof requestAnimationFrame;\nfunction $1a9a8ef576b7773d$var$g(n19) {\n    var t13 = $1a9a8ef576b7773d$var$u, r12 = n19.__c;\n    \"function\" == typeof r12 && (n19.__c = void 0, r12()), $1a9a8ef576b7773d$var$u = t13;\n}\nfunction $1a9a8ef576b7773d$var$j(n20) {\n    var t14 = $1a9a8ef576b7773d$var$u;\n    n20.__c = n20.__(), $1a9a8ef576b7773d$var$u = t14;\n}\nfunction $1a9a8ef576b7773d$var$k(n21, t15) {\n    return !n21 || n21.length !== t15.length || t15.some(function(t16, u8) {\n        return t16 !== n21[u8];\n    });\n}\nfunction $1a9a8ef576b7773d$var$w(n22, t17) {\n    return \"function\" == typeof t17 ? t17(n22) : t17;\n}\n\n\n\n\n\nfunction $dc040a17866866fa$var$S(n1, t1) {\n    for(var e1 in t1)n1[e1] = t1[e1];\n    return n1;\n}\nfunction $dc040a17866866fa$var$C(n2, t2) {\n    for(var e2 in n2)if (\"__source\" !== e2 && !(e2 in t2)) return !0;\n    for(var r1 in t2)if (\"__source\" !== r1 && n2[r1] !== t2[r1]) return !0;\n    return !1;\n}\nfunction $dc040a17866866fa$export$221d75b3f55bb0bd(n3) {\n    this.props = n3;\n}\nfunction $dc040a17866866fa$export$7c73462e0d25e514(n4, t3) {\n    function e3(n5) {\n        var e4 = this.props.ref, r3 = e4 == n5.ref;\n        return !r3 && e4 && (e4.call ? e4(null) : e4.current = null), t3 ? !t3(this.props, n5) || !r3 : $dc040a17866866fa$var$C(this.props, n5);\n    }\n    function r2(t4) {\n        return this.shouldComponentUpdate = e3, (0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d)(n4, t4);\n    }\n    return r2.displayName = \"Memo(\" + (n4.displayName || n4.name) + \")\", r2.prototype.isReactComponent = !0, r2.__f = !0, r2;\n}\n($dc040a17866866fa$export$221d75b3f55bb0bd.prototype = new (0, $fb96b826c0c5f37a$export$16fa2f45be04daa8)).isPureReactComponent = !0, $dc040a17866866fa$export$221d75b3f55bb0bd.prototype.shouldComponentUpdate = function(n6, t5) {\n    return $dc040a17866866fa$var$C(this.props, n6) || $dc040a17866866fa$var$C(this.state, t5);\n};\nvar $dc040a17866866fa$var$w = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__b;\n(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__b = function(n7) {\n    n7.type && n7.type.__f && n7.ref && (n7.props.ref = n7.ref, n7.ref = null), $dc040a17866866fa$var$w && $dc040a17866866fa$var$w(n7);\n};\nvar $dc040a17866866fa$var$R = \"undefined\" != typeof Symbol && Symbol.for && Symbol.for(\"react.forward_ref\") || 3911;\nfunction $dc040a17866866fa$export$257a8862b851cb5b(n8) {\n    function t6(t7, e5) {\n        var r4 = $dc040a17866866fa$var$S({}, t7);\n        return delete r4.ref, n8(r4, (e5 = t7.ref || e5) && (\"object\" != typeof e5 || \"current\" in e5) ? e5 : null);\n    }\n    return t6.$$typeof = $dc040a17866866fa$var$R, t6.render = t6, t6.prototype.isReactComponent = t6.__f = !0, t6.displayName = \"ForwardRef(\" + (n8.displayName || n8.name) + \")\", t6;\n}\nvar $dc040a17866866fa$var$N = function(n9, t8) {\n    return null == n9 ? null : (0, $fb96b826c0c5f37a$export$47e4c5b300681277)((0, $fb96b826c0c5f37a$export$47e4c5b300681277)(n9).map(t8));\n}, $dc040a17866866fa$export$dca3b0875bd9a954 = {\n    map: $dc040a17866866fa$var$N,\n    forEach: $dc040a17866866fa$var$N,\n    count: function(n10) {\n        return n10 ? (0, $fb96b826c0c5f37a$export$47e4c5b300681277)(n10).length : 0;\n    },\n    only: function(n11) {\n        var t9 = (0, $fb96b826c0c5f37a$export$47e4c5b300681277)(n11);\n        if (1 !== t9.length) throw \"Children.only\";\n        return t9[0];\n    },\n    toArray: (0, $fb96b826c0c5f37a$export$47e4c5b300681277)\n}, $dc040a17866866fa$var$A = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__e;\n(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__e = function(n12, t10, e6) {\n    if (n12.then) {\n        for(var r5, u1 = t10; u1 = u1.__;)if ((r5 = u1.__c) && r5.__c) return null == t10.__e && (t10.__e = e6.__e, t10.__k = e6.__k), r5.__c(n12, t10);\n    }\n    $dc040a17866866fa$var$A(n12, t10, e6);\n};\nvar $dc040a17866866fa$var$O = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).unmount;\nfunction $dc040a17866866fa$export$74bf444e3cd11ea5() {\n    this.__u = 0, this.t = null, this.__b = null;\n}\nfunction $dc040a17866866fa$var$U(n13) {\n    var t11 = n13.__.__c;\n    return t11 && t11.__e && t11.__e(n13);\n}\nfunction $dc040a17866866fa$export$488013bae63b21da(n14) {\n    var t12, e7, r6;\n    function u2(u3) {\n        if (t12 || (t12 = n14()).then(function(n15) {\n            e7 = n15.default || n15;\n        }, function(n16) {\n            r6 = n16;\n        }), r6) throw r6;\n        if (!e7) throw t12;\n        return (0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d)(e7, u3);\n    }\n    return u2.displayName = \"Lazy\", u2.__f = !0, u2;\n}\nfunction $dc040a17866866fa$export$998bcd577473dd93() {\n    this.u = null, this.o = null;\n}\n(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).unmount = function(n17) {\n    var t13 = n17.__c;\n    t13 && t13.__R && t13.__R(), t13 && !0 === n17.__h && (n17.type = null), $dc040a17866866fa$var$O && $dc040a17866866fa$var$O(n17);\n}, ($dc040a17866866fa$export$74bf444e3cd11ea5.prototype = new (0, $fb96b826c0c5f37a$export$16fa2f45be04daa8)).__c = function(n18, t14) {\n    var e8 = t14.__c, r7 = this;\n    null == r7.t && (r7.t = []), r7.t.push(e8);\n    var u4 = $dc040a17866866fa$var$U(r7.__v), o1 = !1, i1 = function() {\n        o1 || (o1 = !0, e8.__R = null, u4 ? u4(l1) : l1());\n    };\n    e8.__R = i1;\n    var l1 = function() {\n        if (!--r7.__u) {\n            if (r7.state.__e) {\n                var n19 = r7.state.__e;\n                r7.__v.__k[0] = function n22(t17, e9, r8) {\n                    return t17 && (t17.__v = null, t17.__k = t17.__k && t17.__k.map(function(t18) {\n                        return n22(t18, e9, r8);\n                    }), t17.__c && t17.__c.__P === e9 && (t17.__e && r8.insertBefore(t17.__e, t17.__d), t17.__c.__e = !0, t17.__c.__P = r8)), t17;\n                }(n19, n19.__c.__P, n19.__c.__O);\n            }\n            var t15;\n            for(r7.setState({\n                __e: r7.__b = null\n            }); t15 = r7.t.pop();)t15.forceUpdate();\n        }\n    }, c1 = !0 === t14.__h;\n    (r7.__u++) || c1 || r7.setState({\n        __e: r7.__b = r7.__v.__k[0]\n    }), n18.then(i1, i1);\n}, $dc040a17866866fa$export$74bf444e3cd11ea5.prototype.componentWillUnmount = function() {\n    this.t = [];\n}, $dc040a17866866fa$export$74bf444e3cd11ea5.prototype.render = function(n23, t19) {\n    if (this.__b) {\n        if (this.__v.__k) {\n            var e10 = document.createElement(\"div\"), r9 = this.__v.__k[0].__c;\n            this.__v.__k[0] = function n24(t20, e13, r12) {\n                return t20 && (t20.__c && t20.__c.__H && (t20.__c.__H.__.forEach(function(n25) {\n                    \"function\" == typeof n25.__c && n25.__c();\n                }), t20.__c.__H = null), null != (t20 = $dc040a17866866fa$var$S({}, t20)).__c && (t20.__c.__P === r12 && (t20.__c.__P = e13), t20.__c = null), t20.__k = t20.__k && t20.__k.map(function(t21) {\n                    return n24(t21, e13, r12);\n                })), t20;\n            }(this.__b, e10, r9.__O = r9.__P);\n        }\n        this.__b = null;\n    }\n    var u5 = t19.__e && (0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d)((0, $fb96b826c0c5f37a$export$ffb0004e005737fa), null, n23.fallback);\n    return u5 && (u5.__h = null), [\n        (0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d)((0, $fb96b826c0c5f37a$export$ffb0004e005737fa), null, t19.__e ? null : n23.children),\n        u5\n    ];\n};\nvar $dc040a17866866fa$var$T = function(n26, t22, e14) {\n    if (++e14[1] === e14[0] && n26.o.delete(t22), n26.props.revealOrder && (\"t\" !== n26.props.revealOrder[0] || !n26.o.size)) for(e14 = n26.u; e14;){\n        for(; e14.length > 3;)e14.pop()();\n        if (e14[1] < e14[0]) break;\n        n26.u = e14 = e14[2];\n    }\n};\nfunction $dc040a17866866fa$var$D(n27) {\n    return this.getChildContext = function() {\n        return n27.context;\n    }, n27.children;\n}\nfunction $dc040a17866866fa$var$I(n28) {\n    var t23 = this, e15 = n28.i;\n    t23.componentWillUnmount = function() {\n        (0, $fb96b826c0c5f37a$export$b3890eb0ae9dca99)(null, t23.l), t23.l = null, t23.i = null;\n    }, t23.i && t23.i !== e15 && t23.componentWillUnmount(), n28.__v ? (t23.l || (t23.i = e15, t23.l = {\n        nodeType: 1,\n        parentNode: e15,\n        childNodes: [],\n        appendChild: function(n29) {\n            this.childNodes.push(n29), t23.i.appendChild(n29);\n        },\n        insertBefore: function(n30, e) {\n            this.childNodes.push(n30), t23.i.appendChild(n30);\n        },\n        removeChild: function(n31) {\n            this.childNodes.splice(this.childNodes.indexOf(n31) >>> 1, 1), t23.i.removeChild(n31);\n        }\n    }), (0, $fb96b826c0c5f37a$export$b3890eb0ae9dca99)((0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d)($dc040a17866866fa$var$D, {\n        context: t23.context\n    }, n28.__v), t23.l)) : t23.l && t23.componentWillUnmount();\n}\nfunction $dc040a17866866fa$export$d39a5bbd09211389(n32, t24) {\n    return (0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d)($dc040a17866866fa$var$I, {\n        __v: n32,\n        i: t24\n    });\n}\n($dc040a17866866fa$export$998bcd577473dd93.prototype = new (0, $fb96b826c0c5f37a$export$16fa2f45be04daa8)).__e = function(n33) {\n    var t25 = this, e16 = $dc040a17866866fa$var$U(t25.__v), r13 = t25.o.get(n33);\n    return r13[0]++, function(u6) {\n        var o2 = function() {\n            t25.props.revealOrder ? (r13.push(u6), $dc040a17866866fa$var$T(t25, n33, r13)) : u6();\n        };\n        e16 ? e16(o2) : o2();\n    };\n}, $dc040a17866866fa$export$998bcd577473dd93.prototype.render = function(n34) {\n    this.u = null, this.o = new Map;\n    var t26 = (0, $fb96b826c0c5f37a$export$47e4c5b300681277)(n34.children);\n    n34.revealOrder && \"b\" === n34.revealOrder[0] && t26.reverse();\n    for(var e17 = t26.length; e17--;)this.o.set(t26[e17], this.u = [\n        1,\n        0,\n        this.u\n    ]);\n    return n34.children;\n}, $dc040a17866866fa$export$998bcd577473dd93.prototype.componentDidUpdate = $dc040a17866866fa$export$998bcd577473dd93.prototype.componentDidMount = function() {\n    var n35 = this;\n    this.o.forEach(function(t27, e18) {\n        $dc040a17866866fa$var$T(n35, e18, t27);\n    });\n};\nvar $dc040a17866866fa$var$j = \"undefined\" != typeof Symbol && Symbol.for && Symbol.for(\"react.element\") || 60103, $dc040a17866866fa$var$P = /^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|marker(?!H|W|U)|overline|paint|stop|strikethrough|stroke|text(?!L)|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/, $dc040a17866866fa$var$V = \"undefined\" != typeof document, $dc040a17866866fa$var$z = function(n36) {\n    return (\"undefined\" != typeof Symbol && \"symbol\" == typeof Symbol() ? /fil|che|rad/i : /fil|che|ra/i).test(n36);\n};\nfunction $dc040a17866866fa$export$b3890eb0ae9dca99(n37, t28, e19) {\n    return null == t28.__k && (t28.textContent = \"\"), (0, $fb96b826c0c5f37a$export$b3890eb0ae9dca99)(n37, t28), \"function\" == typeof e19 && e19(), n37 ? n37.__c : null;\n}\nfunction $dc040a17866866fa$export$fa8d919ba61d84db(n38, t29, e20) {\n    return (0, $fb96b826c0c5f37a$export$fa8d919ba61d84db)(n38, t29), \"function\" == typeof e20 && e20(), n38 ? n38.__c : null;\n}\n(0, $fb96b826c0c5f37a$export$16fa2f45be04daa8).prototype.isReactComponent = {}, [\n    \"componentWillMount\",\n    \"componentWillReceiveProps\",\n    \"componentWillUpdate\"\n].forEach(function(n39) {\n    Object.defineProperty((0, $fb96b826c0c5f37a$export$16fa2f45be04daa8).prototype, n39, {\n        configurable: !0,\n        get: function() {\n            return this[\"UNSAFE_\" + n39];\n        },\n        set: function(t30) {\n            Object.defineProperty(this, n39, {\n                configurable: !0,\n                writable: !0,\n                value: t30\n            });\n        }\n    });\n});\nvar $dc040a17866866fa$var$H = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).event;\nfunction $dc040a17866866fa$var$Z() {}\nfunction $dc040a17866866fa$var$Y() {\n    return this.cancelBubble;\n}\nfunction $dc040a17866866fa$var$q() {\n    return this.defaultPrevented;\n}\n(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).event = function(n40) {\n    return $dc040a17866866fa$var$H && (n40 = $dc040a17866866fa$var$H(n40)), n40.persist = $dc040a17866866fa$var$Z, n40.isPropagationStopped = $dc040a17866866fa$var$Y, n40.isDefaultPrevented = $dc040a17866866fa$var$q, n40.nativeEvent = n40;\n};\nvar $dc040a17866866fa$var$G, $dc040a17866866fa$var$J = {\n    configurable: !0,\n    get: function() {\n        return this.class;\n    }\n}, $dc040a17866866fa$var$K = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).vnode;\n(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).vnode = function(n41) {\n    var t31 = n41.type, e21 = n41.props, r14 = e21;\n    if (\"string\" == typeof t31) {\n        var u7 = -1 === t31.indexOf(\"-\");\n        for(var o3 in r14 = {}, e21){\n            var i2 = e21[o3];\n            $dc040a17866866fa$var$V && \"children\" === o3 && \"noscript\" === t31 || \"value\" === o3 && \"defaultValue\" in e21 && null == i2 || (\"defaultValue\" === o3 && \"value\" in e21 && null == e21.value ? o3 = \"value\" : \"download\" === o3 && !0 === i2 ? i2 = \"\" : /ondoubleclick/i.test(o3) ? o3 = \"ondblclick\" : /^onchange(textarea|input)/i.test(o3 + t31) && !$dc040a17866866fa$var$z(e21.type) ? o3 = \"oninput\" : /^onfocus$/i.test(o3) ? o3 = \"onfocusin\" : /^onblur$/i.test(o3) ? o3 = \"onfocusout\" : /^on(Ani|Tra|Tou|BeforeInp)/.test(o3) ? o3 = o3.toLowerCase() : u7 && $dc040a17866866fa$var$P.test(o3) ? o3 = o3.replace(/[A-Z0-9]/, \"-$&\").toLowerCase() : null === i2 && (i2 = void 0), r14[o3] = i2);\n        }\n        \"select\" == t31 && r14.multiple && Array.isArray(r14.value) && (r14.value = (0, $fb96b826c0c5f37a$export$47e4c5b300681277)(e21.children).forEach(function(n42) {\n            n42.props.selected = -1 != r14.value.indexOf(n42.props.value);\n        })), \"select\" == t31 && null != r14.defaultValue && (r14.value = (0, $fb96b826c0c5f37a$export$47e4c5b300681277)(e21.children).forEach(function(n43) {\n            n43.props.selected = r14.multiple ? -1 != r14.defaultValue.indexOf(n43.props.value) : r14.defaultValue == n43.props.value;\n        })), n41.props = r14, e21.class != e21.className && ($dc040a17866866fa$var$J.enumerable = \"className\" in e21, null != e21.className && (r14.class = e21.className), Object.defineProperty(r14, \"className\", $dc040a17866866fa$var$J));\n    }\n    n41.$$typeof = $dc040a17866866fa$var$j, $dc040a17866866fa$var$K && $dc040a17866866fa$var$K(n41);\n};\nvar $dc040a17866866fa$var$Q = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__r;\n(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__r = function(n44) {\n    $dc040a17866866fa$var$Q && $dc040a17866866fa$var$Q(n44), $dc040a17866866fa$var$G = n44.__c;\n};\nvar $dc040a17866866fa$export$ae55be85d98224ed = {\n    ReactCurrentDispatcher: {\n        current: {\n            readContext: function(n45) {\n                return $dc040a17866866fa$var$G.__n[n45.__c].props.value;\n            }\n        }\n    }\n}, $dc040a17866866fa$export$83d89fbfd8236492 = \"17.0.2\";\nfunction $dc040a17866866fa$export$d38cd72104c1f0e9(n46) {\n    return (0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d).bind(null, n46);\n}\nfunction $dc040a17866866fa$export$a8257692ac88316c(n47) {\n    return !!n47 && n47.$$typeof === $dc040a17866866fa$var$j;\n}\nfunction $dc040a17866866fa$export$e530037191fcd5d7(n48) {\n    return $dc040a17866866fa$export$a8257692ac88316c(n48) ? (0, $fb96b826c0c5f37a$export$e530037191fcd5d7).apply(null, arguments) : n48;\n}\nfunction $dc040a17866866fa$export$502457920280e6be(n49) {\n    return !!n49.__k && ((0, $fb96b826c0c5f37a$export$b3890eb0ae9dca99)(null, n49), !0);\n}\nfunction $dc040a17866866fa$export$466bfc07425424d5(n50) {\n    return n50 && (n50.base || 1 === n50.nodeType && n50) || null;\n}\nvar $dc040a17866866fa$export$c78a37762a8d58e1 = function(n51, t32) {\n    return n51(t32);\n}, $dc040a17866866fa$export$cd75ccfd720a3cd4 = function(n52, t33) {\n    return n52(t33);\n}, $dc040a17866866fa$export$5f8d39834fd61797 = (0, $fb96b826c0c5f37a$export$ffb0004e005737fa);\nvar $dc040a17866866fa$export$2e2bcd8739ae039 = {\n    useState: (0, $1a9a8ef576b7773d$export$60241385465d0a34),\n    useReducer: (0, $1a9a8ef576b7773d$export$13e3392192263954),\n    useEffect: (0, $1a9a8ef576b7773d$export$6d9c69b0de29b591),\n    useLayoutEffect: (0, $1a9a8ef576b7773d$export$e5c5a5f917a5871c),\n    useRef: (0, $1a9a8ef576b7773d$export$b8f5890fc79d6aca),\n    useImperativeHandle: (0, $1a9a8ef576b7773d$export$d5a552a76deda3c2),\n    useMemo: (0, $1a9a8ef576b7773d$export$1538c33de8887b59),\n    useCallback: (0, $1a9a8ef576b7773d$export$35808ee640e87ca7),\n    useContext: (0, $1a9a8ef576b7773d$export$fae74005e78b1a27),\n    useDebugValue: (0, $1a9a8ef576b7773d$export$dc8fbce3eb94dc1e),\n    version: \"17.0.2\",\n    Children: $dc040a17866866fa$export$dca3b0875bd9a954,\n    render: $dc040a17866866fa$export$b3890eb0ae9dca99,\n    hydrate: $dc040a17866866fa$export$fa8d919ba61d84db,\n    unmountComponentAtNode: $dc040a17866866fa$export$502457920280e6be,\n    createPortal: $dc040a17866866fa$export$d39a5bbd09211389,\n    createElement: (0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d),\n    createContext: (0, $fb96b826c0c5f37a$export$fd42f52fd3ae1109),\n    createFactory: $dc040a17866866fa$export$d38cd72104c1f0e9,\n    cloneElement: $dc040a17866866fa$export$e530037191fcd5d7,\n    createRef: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43),\n    Fragment: (0, $fb96b826c0c5f37a$export$ffb0004e005737fa),\n    isValidElement: $dc040a17866866fa$export$a8257692ac88316c,\n    findDOMNode: $dc040a17866866fa$export$466bfc07425424d5,\n    Component: (0, $fb96b826c0c5f37a$export$16fa2f45be04daa8),\n    PureComponent: $dc040a17866866fa$export$221d75b3f55bb0bd,\n    memo: $dc040a17866866fa$export$7c73462e0d25e514,\n    forwardRef: $dc040a17866866fa$export$257a8862b851cb5b,\n    flushSync: $dc040a17866866fa$export$cd75ccfd720a3cd4,\n    unstable_batchedUpdates: $dc040a17866866fa$export$c78a37762a8d58e1,\n    StrictMode: (0, $fb96b826c0c5f37a$export$ffb0004e005737fa),\n    Suspense: $dc040a17866866fa$export$74bf444e3cd11ea5,\n    SuspenseList: $dc040a17866866fa$export$998bcd577473dd93,\n    lazy: $dc040a17866866fa$export$488013bae63b21da,\n    __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED: $dc040a17866866fa$export$ae55be85d98224ed\n};\n\n\n\n\nconst $ec8c39fdad15601a$var$THEME_ICONS = {\n    light: \"outline\",\n    dark: \"solid\"\n};\nclass $ec8c39fdad15601a$export$2e2bcd8739ae039 extends (0, $dc040a17866866fa$export$221d75b3f55bb0bd) {\n    renderIcon(category) {\n        const { icon: icon  } = category;\n        if (icon) {\n            if (icon.svg) return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n                class: \"flex\",\n                dangerouslySetInnerHTML: {\n                    __html: icon.svg\n                }\n            });\n            if (icon.src) return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"img\", {\n                src: icon.src\n            });\n        }\n        const categoryIcons = (0, $fcccfb36ed0cde68$export$2e2bcd8739ae039).categories[category.id] || (0, $fcccfb36ed0cde68$export$2e2bcd8739ae039).categories.custom;\n        const style = this.props.icons == \"auto\" ? $ec8c39fdad15601a$var$THEME_ICONS[this.props.theme] : this.props.icons;\n        return categoryIcons[style] || categoryIcons;\n    }\n    render() {\n        let selectedCategoryIndex = null;\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"nav\", {\n            id: \"nav\",\n            class: \"padding\",\n            \"data-position\": this.props.position,\n            dir: this.props.dir,\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                class: \"flex relative\",\n                children: [\n                    this.categories.map((category, i)=>{\n                        const title = category.name || (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).categories[category.id];\n                        const selected = !this.props.unfocused && category.id == this.state.categoryId;\n                        if (selected) selectedCategoryIndex = i;\n                        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"button\", {\n                            \"aria-label\": title,\n                            \"aria-selected\": selected || undefined,\n                            title: title,\n                            type: \"button\",\n                            class: \"flex flex-grow flex-center\",\n                            onMouseDown: (e)=>e.preventDefault(),\n                            onClick: ()=>{\n                                this.props.onClick({\n                                    category: category,\n                                    i: i\n                                });\n                            },\n                            children: this.renderIcon(category)\n                        });\n                    }),\n                    /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                        class: \"bar\",\n                        style: {\n                            width: `${100 / this.categories.length}%`,\n                            opacity: selectedCategoryIndex == null ? 0 : 1,\n                            transform: this.props.dir === \"rtl\" ? `scaleX(-1) translateX(${selectedCategoryIndex * 100}%)` : `translateX(${selectedCategoryIndex * 100}%)`\n                        }\n                    })\n                ]\n            })\n        });\n    }\n    constructor(){\n        super();\n        this.categories = (0, $7adb23b0109cc36a$export$2d0294657ab35f1b).categories.filter((category)=>{\n            return !category.target;\n        });\n        this.state = {\n            categoryId: this.categories[0].id\n        };\n    }\n}\n\n\n\n\n\nclass $e0d4dda61265ff1e$export$2e2bcd8739ae039 extends (0, $dc040a17866866fa$export$221d75b3f55bb0bd) {\n    shouldComponentUpdate(nextProps) {\n        for(let k in nextProps){\n            if (k == \"children\") continue;\n            if (nextProps[k] != this.props[k]) return true;\n        }\n        return false;\n    }\n    render() {\n        return this.props.children;\n    }\n}\n\n\n\n\nconst $89bd6bb200cc8fef$var$Performance = {\n    rowsPerRender: 10\n};\nclass $89bd6bb200cc8fef$export$2e2bcd8739ae039 extends (0, $fb96b826c0c5f37a$export$16fa2f45be04daa8) {\n    getInitialState(props = this.props) {\n        return {\n            skin: (0, $f72b75cf796873c7$export$2e2bcd8739ae039).get(\"skin\") || props.skin,\n            theme: this.initTheme(props.theme)\n        };\n    }\n    componentWillMount() {\n        this.dir = (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).rtl ? \"rtl\" : \"ltr\";\n        this.refs = {\n            menu: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)(),\n            navigation: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)(),\n            scroll: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)(),\n            search: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)(),\n            searchInput: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)(),\n            skinToneButton: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)(),\n            skinToneRadio: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)()\n        };\n        this.initGrid();\n        if (this.props.stickySearch == false && this.props.searchPosition == \"sticky\") {\n            console.warn(\"[EmojiMart] Deprecation warning: `stickySearch` has been renamed `searchPosition`.\");\n            this.props.searchPosition = \"static\";\n        }\n    }\n    componentDidMount() {\n        this.register();\n        this.shadowRoot = this.base.parentNode;\n        if (this.props.autoFocus) {\n            const { searchInput: searchInput  } = this.refs;\n            if (searchInput.current) searchInput.current.focus();\n        }\n    }\n    componentWillReceiveProps(nextProps) {\n        this.nextState || (this.nextState = {});\n        for(const k1 in nextProps)this.nextState[k1] = nextProps[k1];\n        clearTimeout(this.nextStateTimer);\n        this.nextStateTimer = setTimeout(()=>{\n            let requiresGridReset = false;\n            for(const k in this.nextState){\n                this.props[k] = this.nextState[k];\n                if (k === \"custom\" || k === \"categories\") requiresGridReset = true;\n            }\n            delete this.nextState;\n            const nextState = this.getInitialState();\n            if (requiresGridReset) return this.reset(nextState);\n            this.setState(nextState);\n        });\n    }\n    componentWillUnmount() {\n        this.unregister();\n    }\n    async reset(nextState = {}) {\n        await (0, $7adb23b0109cc36a$export$2cd8252107eb640b)(this.props);\n        this.initGrid();\n        this.unobserve();\n        this.setState(nextState, ()=>{\n            this.observeCategories();\n            this.observeRows();\n        });\n    }\n    register() {\n        document.addEventListener(\"click\", this.handleClickOutside);\n        this.observe();\n    }\n    unregister() {\n        document.removeEventListener(\"click\", this.handleClickOutside);\n        this.darkMedia?.removeEventListener(\"change\", this.darkMediaCallback);\n        this.unobserve();\n    }\n    observe() {\n        this.observeCategories();\n        this.observeRows();\n    }\n    unobserve({ except: except = []  } = {}) {\n        if (!Array.isArray(except)) except = [\n            except\n        ];\n        for (const observer of this.observers){\n            if (except.includes(observer)) continue;\n            observer.disconnect();\n        }\n        this.observers = [].concat(except);\n    }\n    initGrid() {\n        const { categories: categories  } = (0, $7adb23b0109cc36a$export$2d0294657ab35f1b);\n        this.refs.categories = new Map();\n        const navKey = (0, $7adb23b0109cc36a$export$2d0294657ab35f1b).categories.map((category)=>category.id).join(\",\");\n        if (this.navKey && this.navKey != navKey) this.refs.scroll.current && (this.refs.scroll.current.scrollTop = 0);\n        this.navKey = navKey;\n        this.grid = [];\n        this.grid.setsize = 0;\n        const addRow = (rows, category)=>{\n            const row = [];\n            row.__categoryId = category.id;\n            row.__index = rows.length;\n            this.grid.push(row);\n            const rowIndex = this.grid.length - 1;\n            const rowRef = rowIndex % $89bd6bb200cc8fef$var$Performance.rowsPerRender ? {} : (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)();\n            rowRef.index = rowIndex;\n            rowRef.posinset = this.grid.setsize + 1;\n            rows.push(rowRef);\n            return row;\n        };\n        for (let category1 of categories){\n            const rows = [];\n            let row = addRow(rows, category1);\n            for (let emoji of category1.emojis){\n                if (row.length == this.getPerLine()) row = addRow(rows, category1);\n                this.grid.setsize += 1;\n                row.push(emoji);\n            }\n            this.refs.categories.set(category1.id, {\n                root: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)(),\n                rows: rows\n            });\n        }\n    }\n    initTheme(theme) {\n        if (theme != \"auto\") return theme;\n        if (!this.darkMedia) {\n            this.darkMedia = matchMedia(\"(prefers-color-scheme: dark)\");\n            if (this.darkMedia.media.match(/^not/)) return \"light\";\n            this.darkMedia.addEventListener(\"change\", this.darkMediaCallback);\n        }\n        return this.darkMedia.matches ? \"dark\" : \"light\";\n    }\n    initDynamicPerLine(props = this.props) {\n        if (!props.dynamicWidth) return;\n        const { element: element , emojiButtonSize: emojiButtonSize  } = props;\n        const calculatePerLine = ()=>{\n            const { width: width  } = element.getBoundingClientRect();\n            return Math.floor(width / emojiButtonSize);\n        };\n        const observer = new ResizeObserver(()=>{\n            this.unobserve({\n                except: observer\n            });\n            this.setState({\n                perLine: calculatePerLine()\n            }, ()=>{\n                this.initGrid();\n                this.forceUpdate(()=>{\n                    this.observeCategories();\n                    this.observeRows();\n                });\n            });\n        });\n        observer.observe(element);\n        this.observers.push(observer);\n        return calculatePerLine();\n    }\n    getPerLine() {\n        return this.state.perLine || this.props.perLine;\n    }\n    getEmojiByPos([p1, p2]) {\n        const grid = this.state.searchResults || this.grid;\n        const emoji = grid[p1] && grid[p1][p2];\n        if (!emoji) return;\n        return (0, $c4d155af13ad4d4b$export$2e2bcd8739ae039).get(emoji);\n    }\n    observeCategories() {\n        const navigation = this.refs.navigation.current;\n        if (!navigation) return;\n        const visibleCategories = new Map();\n        const setFocusedCategory = (categoryId)=>{\n            if (categoryId != navigation.state.categoryId) navigation.setState({\n                categoryId: categoryId\n            });\n        };\n        const observerOptions = {\n            root: this.refs.scroll.current,\n            threshold: [\n                0.0,\n                1.0\n            ]\n        };\n        const observer = new IntersectionObserver((entries)=>{\n            for (const entry of entries){\n                const id = entry.target.dataset.id;\n                visibleCategories.set(id, entry.intersectionRatio);\n            }\n            const ratios = [\n                ...visibleCategories\n            ];\n            for (const [id, ratio] of ratios)if (ratio) {\n                setFocusedCategory(id);\n                break;\n            }\n        }, observerOptions);\n        for (const { root: root  } of this.refs.categories.values())observer.observe(root.current);\n        this.observers.push(observer);\n    }\n    observeRows() {\n        const visibleRows = {\n            ...this.state.visibleRows\n        };\n        const observer = new IntersectionObserver((entries)=>{\n            for (const entry of entries){\n                const index = parseInt(entry.target.dataset.index);\n                if (entry.isIntersecting) visibleRows[index] = true;\n                else delete visibleRows[index];\n            }\n            this.setState({\n                visibleRows: visibleRows\n            });\n        }, {\n            root: this.refs.scroll.current,\n            rootMargin: `${this.props.emojiButtonSize * ($89bd6bb200cc8fef$var$Performance.rowsPerRender + 5)}px 0px ${this.props.emojiButtonSize * $89bd6bb200cc8fef$var$Performance.rowsPerRender}px`\n        });\n        for (const { rows: rows  } of this.refs.categories.values()){\n            for (const row of rows)if (row.current) observer.observe(row.current);\n        }\n        this.observers.push(observer);\n    }\n    preventDefault(e) {\n        e.preventDefault();\n    }\n    unfocusSearch() {\n        const input = this.refs.searchInput.current;\n        if (!input) return;\n        input.blur();\n    }\n    navigate({ e: e , input: input , left: left , right: right , up: up , down: down  }) {\n        const grid = this.state.searchResults || this.grid;\n        if (!grid.length) return;\n        let [p1, p2] = this.state.pos;\n        const pos = (()=>{\n            if (p1 == 0) {\n                if (p2 == 0 && !e.repeat && (left || up)) return null;\n            }\n            if (p1 == -1) {\n                if (!e.repeat && (right || down) && input.selectionStart == input.value.length) return [\n                    0,\n                    0\n                ];\n                return null;\n            }\n            if (left || right) {\n                let row = grid[p1];\n                const increment = left ? -1 : 1;\n                p2 += increment;\n                if (!row[p2]) {\n                    p1 += increment;\n                    row = grid[p1];\n                    if (!row) {\n                        p1 = left ? 0 : grid.length - 1;\n                        p2 = left ? 0 : grid[p1].length - 1;\n                        return [\n                            p1,\n                            p2\n                        ];\n                    }\n                    p2 = left ? row.length - 1 : 0;\n                }\n                return [\n                    p1,\n                    p2\n                ];\n            }\n            if (up || down) {\n                p1 += up ? -1 : 1;\n                const row = grid[p1];\n                if (!row) {\n                    p1 = up ? 0 : grid.length - 1;\n                    p2 = up ? 0 : grid[p1].length - 1;\n                    return [\n                        p1,\n                        p2\n                    ];\n                }\n                if (!row[p2]) p2 = row.length - 1;\n                return [\n                    p1,\n                    p2\n                ];\n            }\n        })();\n        if (pos) e.preventDefault();\n        else {\n            if (this.state.pos[0] > -1) this.setState({\n                pos: [\n                    -1,\n                    -1\n                ]\n            });\n            return;\n        }\n        this.setState({\n            pos: pos,\n            keyboard: true\n        }, ()=>{\n            this.scrollTo({\n                row: pos[0]\n            });\n        });\n    }\n    scrollTo({ categoryId: categoryId , row: row  }) {\n        const grid = this.state.searchResults || this.grid;\n        if (!grid.length) return;\n        const scroll = this.refs.scroll.current;\n        const scrollRect = scroll.getBoundingClientRect();\n        let scrollTop = 0;\n        if (row >= 0) categoryId = grid[row].__categoryId;\n        if (categoryId) {\n            const ref = this.refs[categoryId] || this.refs.categories.get(categoryId).root;\n            const categoryRect = ref.current.getBoundingClientRect();\n            scrollTop = categoryRect.top - (scrollRect.top - scroll.scrollTop) + 1;\n        }\n        if (row >= 0) {\n            if (!row) scrollTop = 0;\n            else {\n                const rowIndex = grid[row].__index;\n                const rowTop = scrollTop + rowIndex * this.props.emojiButtonSize;\n                const rowBot = rowTop + this.props.emojiButtonSize + this.props.emojiButtonSize * 0.88;\n                if (rowTop < scroll.scrollTop) scrollTop = rowTop;\n                else if (rowBot > scroll.scrollTop + scrollRect.height) scrollTop = rowBot - scrollRect.height;\n                else return;\n            }\n        }\n        this.ignoreMouse();\n        scroll.scrollTop = scrollTop;\n    }\n    ignoreMouse() {\n        this.mouseIsIgnored = true;\n        clearTimeout(this.ignoreMouseTimer);\n        this.ignoreMouseTimer = setTimeout(()=>{\n            delete this.mouseIsIgnored;\n        }, 100);\n    }\n    handleEmojiOver(pos) {\n        if (this.mouseIsIgnored || this.state.showSkins) return;\n        this.setState({\n            pos: pos || [\n                -1,\n                -1\n            ],\n            keyboard: false\n        });\n    }\n    handleEmojiClick({ e: e , emoji: emoji , pos: pos  }) {\n        if (!this.props.onEmojiSelect) return;\n        if (!emoji && pos) emoji = this.getEmojiByPos(pos);\n        if (emoji) {\n            const emojiData = (0, $693b183b0a78708f$export$d10ac59fbe52a745)(emoji, {\n                skinIndex: this.state.skin - 1\n            });\n            if (this.props.maxFrequentRows) (0, $b22cfd0a55410b4f$export$2e2bcd8739ae039).add(emojiData, this.props);\n            this.props.onEmojiSelect(emojiData, e);\n        }\n    }\n    closeSkins() {\n        if (!this.state.showSkins) return;\n        this.setState({\n            showSkins: null,\n            tempSkin: null\n        });\n        this.base.removeEventListener(\"click\", this.handleBaseClick);\n        this.base.removeEventListener(\"keydown\", this.handleBaseKeydown);\n    }\n    handleSkinMouseOver(tempSkin) {\n        this.setState({\n            tempSkin: tempSkin\n        });\n    }\n    handleSkinClick(skin) {\n        this.ignoreMouse();\n        this.closeSkins();\n        this.setState({\n            skin: skin,\n            tempSkin: null\n        });\n        (0, $f72b75cf796873c7$export$2e2bcd8739ae039).set(\"skin\", skin);\n    }\n    renderNav() {\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)((0, $ec8c39fdad15601a$export$2e2bcd8739ae039), {\n            ref: this.refs.navigation,\n            icons: this.props.icons,\n            theme: this.state.theme,\n            dir: this.dir,\n            unfocused: !!this.state.searchResults,\n            position: this.props.navPosition,\n            onClick: this.handleCategoryClick\n        }, this.navKey);\n    }\n    renderPreview() {\n        const emoji = this.getEmojiByPos(this.state.pos);\n        const noSearchResults = this.state.searchResults && !this.state.searchResults.length;\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n            id: \"preview\",\n            class: \"flex flex-middle\",\n            dir: this.dir,\n            \"data-position\": this.props.previewPosition,\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    class: \"flex flex-middle flex-grow\",\n                    children: [\n                        /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                            class: \"flex flex-auto flex-middle flex-center\",\n                            style: {\n                                height: this.props.emojiButtonSize,\n                                fontSize: this.props.emojiButtonSize\n                            },\n                            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)((0, $254755d3f438722f$export$2e2bcd8739ae039), {\n                                emoji: emoji,\n                                id: noSearchResults ? this.props.noResultsEmoji || \"cry\" : this.props.previewEmoji || (this.props.previewPosition == \"top\" ? \"point_down\" : \"point_up\"),\n                                set: this.props.set,\n                                size: this.props.emojiButtonSize,\n                                skin: this.state.tempSkin || this.state.skin,\n                                spritesheet: true,\n                                getSpritesheetURL: this.props.getSpritesheetURL\n                            })\n                        }),\n                        /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                            class: `margin-${this.dir[0]}`,\n                            children: emoji || noSearchResults ? /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                                class: `padding-${this.dir[2]} align-${this.dir[0]}`,\n                                children: [\n                                    /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                                        class: \"preview-title ellipsis\",\n                                        children: emoji ? emoji.name : (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).search_no_results_1\n                                    }),\n                                    /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                                        class: \"preview-subtitle ellipsis color-c\",\n                                        children: emoji ? emoji.skins[0].shortcodes : (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).search_no_results_2\n                                    })\n                                ]\n                            }) : /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                                class: \"preview-placeholder color-c\",\n                                children: (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).pick\n                            })\n                        })\n                    ]\n                }),\n                !emoji && this.props.skinTonePosition == \"preview\" && this.renderSkinToneButton()\n            ]\n        });\n    }\n    renderEmojiButton(emoji, { pos: pos , posinset: posinset , grid: grid  }) {\n        const size = this.props.emojiButtonSize;\n        const skin = this.state.tempSkin || this.state.skin;\n        const emojiSkin = emoji.skins[skin - 1] || emoji.skins[0];\n        const native = emojiSkin.native;\n        const selected = (0, $693b183b0a78708f$export$9cb4719e2e525b7a)(this.state.pos, pos);\n        const key = pos.concat(emoji.id).join(\"\");\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)((0, $e0d4dda61265ff1e$export$2e2bcd8739ae039), {\n            selected: selected,\n            skin: skin,\n            size: size,\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"button\", {\n                \"aria-label\": native,\n                \"aria-selected\": selected || undefined,\n                \"aria-posinset\": posinset,\n                \"aria-setsize\": grid.setsize,\n                \"data-keyboard\": this.state.keyboard,\n                title: this.props.previewPosition == \"none\" ? emoji.name : undefined,\n                type: \"button\",\n                class: \"flex flex-center flex-middle\",\n                tabindex: \"-1\",\n                onClick: (e)=>this.handleEmojiClick({\n                        e: e,\n                        emoji: emoji\n                    }),\n                onMouseEnter: ()=>this.handleEmojiOver(pos),\n                onMouseLeave: ()=>this.handleEmojiOver(),\n                style: {\n                    width: this.props.emojiButtonSize,\n                    height: this.props.emojiButtonSize,\n                    fontSize: this.props.emojiSize,\n                    lineHeight: 0\n                },\n                children: [\n                    /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                        \"aria-hidden\": \"true\",\n                        class: \"background\",\n                        style: {\n                            borderRadius: this.props.emojiButtonRadius,\n                            backgroundColor: this.props.emojiButtonColors ? this.props.emojiButtonColors[(posinset - 1) % this.props.emojiButtonColors.length] : undefined\n                        }\n                    }),\n                    /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)((0, $254755d3f438722f$export$2e2bcd8739ae039), {\n                        emoji: emoji,\n                        set: this.props.set,\n                        size: this.props.emojiSize,\n                        skin: skin,\n                        spritesheet: true,\n                        getSpritesheetURL: this.props.getSpritesheetURL\n                    })\n                ]\n            })\n        }, key);\n    }\n    renderSearch() {\n        const renderSkinTone = this.props.previewPosition == \"none\" || this.props.skinTonePosition == \"search\";\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    class: \"spacer\"\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    class: \"flex flex-middle\",\n                    children: [\n                        /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                            class: \"search relative flex-grow\",\n                            children: [\n                                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"input\", {\n                                    type: \"search\",\n                                    ref: this.refs.searchInput,\n                                    placeholder: (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).search,\n                                    onClick: this.handleSearchClick,\n                                    onInput: this.handleSearchInput,\n                                    onKeyDown: this.handleSearchKeyDown,\n                                    autoComplete: \"off\"\n                                }),\n                                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n                                    class: \"icon loupe flex\",\n                                    children: (0, $fcccfb36ed0cde68$export$2e2bcd8739ae039).search.loupe\n                                }),\n                                this.state.searchResults && /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"button\", {\n                                    title: \"Clear\",\n                                    \"aria-label\": \"Clear\",\n                                    type: \"button\",\n                                    class: \"icon delete flex\",\n                                    onClick: this.clearSearch,\n                                    onMouseDown: this.preventDefault,\n                                    children: (0, $fcccfb36ed0cde68$export$2e2bcd8739ae039).search.delete\n                                })\n                            ]\n                        }),\n                        renderSkinTone && this.renderSkinToneButton()\n                    ]\n                })\n            ]\n        });\n    }\n    renderSearchResults() {\n        const { searchResults: searchResults  } = this.state;\n        if (!searchResults) return null;\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n            class: \"category\",\n            ref: this.refs.search,\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    class: `sticky padding-small align-${this.dir[0]}`,\n                    children: (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).categories.search\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    children: !searchResults.length ? /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                        class: `padding-small align-${this.dir[0]}`,\n                        children: this.props.onAddCustomEmoji && /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"a\", {\n                            onClick: this.props.onAddCustomEmoji,\n                            children: (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).add_custom\n                        })\n                    }) : searchResults.map((row, i)=>{\n                        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                            class: \"flex\",\n                            children: row.map((emoji, ii)=>{\n                                return this.renderEmojiButton(emoji, {\n                                    pos: [\n                                        i,\n                                        ii\n                                    ],\n                                    posinset: i * this.props.perLine + ii + 1,\n                                    grid: searchResults\n                                });\n                            })\n                        });\n                    })\n                })\n            ]\n        });\n    }\n    renderCategories() {\n        const { categories: categories  } = (0, $7adb23b0109cc36a$export$2d0294657ab35f1b);\n        const hidden = !!this.state.searchResults;\n        const perLine = this.getPerLine();\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n            style: {\n                visibility: hidden ? \"hidden\" : undefined,\n                display: hidden ? \"none\" : undefined,\n                height: \"100%\"\n            },\n            children: categories.map((category)=>{\n                const { root: root , rows: rows  } = this.refs.categories.get(category.id);\n                return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    \"data-id\": category.target ? category.target.id : category.id,\n                    class: \"category\",\n                    ref: root,\n                    children: [\n                        /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                            class: `sticky padding-small align-${this.dir[0]}`,\n                            children: category.name || (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).categories[category.id]\n                        }),\n                        /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                            class: \"relative\",\n                            style: {\n                                height: rows.length * this.props.emojiButtonSize\n                            },\n                            children: rows.map((row, i)=>{\n                                const targetRow = row.index - row.index % $89bd6bb200cc8fef$var$Performance.rowsPerRender;\n                                const visible = this.state.visibleRows[targetRow];\n                                const ref = \"current\" in row ? row : undefined;\n                                if (!visible && !ref) return null;\n                                const start = i * perLine;\n                                const end = start + perLine;\n                                const emojiIds = category.emojis.slice(start, end);\n                                if (emojiIds.length < perLine) emojiIds.push(...new Array(perLine - emojiIds.length));\n                                return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                                    \"data-index\": row.index,\n                                    ref: ref,\n                                    class: \"flex row\",\n                                    style: {\n                                        top: i * this.props.emojiButtonSize\n                                    },\n                                    children: visible && emojiIds.map((emojiId, ii)=>{\n                                        if (!emojiId) return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                                            style: {\n                                                width: this.props.emojiButtonSize,\n                                                height: this.props.emojiButtonSize\n                                            }\n                                        });\n                                        const emoji = (0, $c4d155af13ad4d4b$export$2e2bcd8739ae039).get(emojiId);\n                                        return this.renderEmojiButton(emoji, {\n                                            pos: [\n                                                row.index,\n                                                ii\n                                            ],\n                                            posinset: row.posinset + ii,\n                                            grid: this.grid\n                                        });\n                                    })\n                                }, row.index);\n                            })\n                        })\n                    ]\n                });\n            })\n        });\n    }\n    renderSkinToneButton() {\n        if (this.props.skinTonePosition == \"none\") return null;\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n            class: \"flex flex-auto flex-center flex-middle\",\n            style: {\n                position: \"relative\",\n                width: this.props.emojiButtonSize,\n                height: this.props.emojiButtonSize\n            },\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"button\", {\n                type: \"button\",\n                ref: this.refs.skinToneButton,\n                class: \"skin-tone-button flex flex-auto flex-center flex-middle\",\n                \"aria-selected\": this.state.showSkins ? \"\" : undefined,\n                \"aria-label\": (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).skins.choose,\n                title: (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).skins.choose,\n                onClick: this.openSkins,\n                style: {\n                    width: this.props.emojiSize,\n                    height: this.props.emojiSize\n                },\n                children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n                    class: `skin-tone skin-tone-${this.state.skin}`\n                })\n            })\n        });\n    }\n    renderLiveRegion() {\n        const emoji = this.getEmojiByPos(this.state.pos);\n        const contents = emoji ? emoji.name : \"\";\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n            \"aria-live\": \"polite\",\n            class: \"sr-only\",\n            children: contents\n        });\n    }\n    renderSkins() {\n        const skinToneButton = this.refs.skinToneButton.current;\n        const skinToneButtonRect = skinToneButton.getBoundingClientRect();\n        const baseRect = this.base.getBoundingClientRect();\n        const position = {};\n        if (this.dir == \"ltr\") position.right = baseRect.right - skinToneButtonRect.right - 3;\n        else position.left = skinToneButtonRect.left - baseRect.left - 3;\n        if (this.props.previewPosition == \"bottom\" && this.props.skinTonePosition == \"preview\") position.bottom = baseRect.bottom - skinToneButtonRect.top + 6;\n        else {\n            position.top = skinToneButtonRect.bottom - baseRect.top + 3;\n            position.bottom = \"auto\";\n        }\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n            ref: this.refs.menu,\n            role: \"radiogroup\",\n            dir: this.dir,\n            \"aria-label\": (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).skins.choose,\n            class: \"menu hidden\",\n            \"data-position\": position.top ? \"top\" : \"bottom\",\n            style: position,\n            children: [\n                ...Array(6).keys()\n            ].map((i)=>{\n                const skin = i + 1;\n                const checked = this.state.skin == skin;\n                return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"input\", {\n                            type: \"radio\",\n                            name: \"skin-tone\",\n                            value: skin,\n                            \"aria-label\": (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).skins[skin],\n                            ref: checked ? this.refs.skinToneRadio : null,\n                            defaultChecked: checked,\n                            onChange: ()=>this.handleSkinMouseOver(skin),\n                            onKeyDown: (e)=>{\n                                if (e.code == \"Enter\" || e.code == \"Space\" || e.code == \"Tab\") {\n                                    e.preventDefault();\n                                    this.handleSkinClick(skin);\n                                }\n                            }\n                        }),\n                        /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"button\", {\n                            \"aria-hidden\": \"true\",\n                            tabindex: \"-1\",\n                            onClick: ()=>this.handleSkinClick(skin),\n                            onMouseEnter: ()=>this.handleSkinMouseOver(skin),\n                            onMouseLeave: ()=>this.handleSkinMouseOver(),\n                            class: \"option flex flex-grow flex-middle\",\n                            children: [\n                                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n                                    class: `skin-tone skin-tone-${skin}`\n                                }),\n                                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n                                    class: \"margin-small-lr\",\n                                    children: (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).skins[skin]\n                                })\n                            ]\n                        })\n                    ]\n                });\n            })\n        });\n    }\n    render() {\n        const lineWidth = this.props.perLine * this.props.emojiButtonSize;\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"section\", {\n            id: \"root\",\n            class: \"flex flex-column\",\n            dir: this.dir,\n            style: {\n                width: this.props.dynamicWidth ? \"100%\" : `calc(${lineWidth}px + (var(--padding) + var(--sidebar-width)))`\n            },\n            \"data-emoji-set\": this.props.set,\n            \"data-theme\": this.state.theme,\n            \"data-menu\": this.state.showSkins ? \"\" : undefined,\n            children: [\n                this.props.previewPosition == \"top\" && this.renderPreview(),\n                this.props.navPosition == \"top\" && this.renderNav(),\n                this.props.searchPosition == \"sticky\" && /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    class: \"padding-lr\",\n                    children: this.renderSearch()\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    ref: this.refs.scroll,\n                    class: \"scroll flex-grow padding-lr\",\n                    children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                        style: {\n                            width: this.props.dynamicWidth ? \"100%\" : lineWidth,\n                            height: \"100%\"\n                        },\n                        children: [\n                            this.props.searchPosition == \"static\" && this.renderSearch(),\n                            this.renderSearchResults(),\n                            this.renderCategories()\n                        ]\n                    })\n                }),\n                this.props.navPosition == \"bottom\" && this.renderNav(),\n                this.props.previewPosition == \"bottom\" && this.renderPreview(),\n                this.state.showSkins && this.renderSkins(),\n                this.renderLiveRegion()\n            ]\n        });\n    }\n    constructor(props){\n        super();\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"darkMediaCallback\", ()=>{\n            if (this.props.theme != \"auto\") return;\n            this.setState({\n                theme: this.darkMedia.matches ? \"dark\" : \"light\"\n            });\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"handleClickOutside\", (e)=>{\n            const { element: element  } = this.props;\n            if (e.target != element) {\n                if (this.state.showSkins) this.closeSkins();\n                if (this.props.onClickOutside) this.props.onClickOutside(e);\n            }\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"handleBaseClick\", (e)=>{\n            if (!this.state.showSkins) return;\n            if (!e.target.closest(\".menu\")) {\n                e.preventDefault();\n                e.stopImmediatePropagation();\n                this.closeSkins();\n            }\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"handleBaseKeydown\", (e)=>{\n            if (!this.state.showSkins) return;\n            if (e.key == \"Escape\") {\n                e.preventDefault();\n                e.stopImmediatePropagation();\n                this.closeSkins();\n            }\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"handleSearchClick\", ()=>{\n            const emoji = this.getEmojiByPos(this.state.pos);\n            if (!emoji) return;\n            this.setState({\n                pos: [\n                    -1,\n                    -1\n                ]\n            });\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"handleSearchInput\", async ()=>{\n            const input = this.refs.searchInput.current;\n            if (!input) return;\n            const { value: value  } = input;\n            const searchResults = await (0, $c4d155af13ad4d4b$export$2e2bcd8739ae039).search(value);\n            const afterRender = ()=>{\n                if (!this.refs.scroll.current) return;\n                this.refs.scroll.current.scrollTop = 0;\n            };\n            if (!searchResults) return this.setState({\n                searchResults: searchResults,\n                pos: [\n                    -1,\n                    -1\n                ]\n            }, afterRender);\n            const pos = input.selectionStart == input.value.length ? [\n                0,\n                0\n            ] : [\n                -1,\n                -1\n            ];\n            const grid = [];\n            grid.setsize = searchResults.length;\n            let row = null;\n            for (let emoji of searchResults){\n                if (!grid.length || row.length == this.getPerLine()) {\n                    row = [];\n                    row.__categoryId = \"search\";\n                    row.__index = grid.length;\n                    grid.push(row);\n                }\n                row.push(emoji);\n            }\n            this.ignoreMouse();\n            this.setState({\n                searchResults: grid,\n                pos: pos\n            }, afterRender);\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"handleSearchKeyDown\", (e)=>{\n            // const specialKey = e.altKey || e.ctrlKey || e.metaKey\n            const input = e.currentTarget;\n            e.stopImmediatePropagation();\n            switch(e.key){\n                case \"ArrowLeft\":\n                    // if (specialKey) return\n                    // e.preventDefault()\n                    this.navigate({\n                        e: e,\n                        input: input,\n                        left: true\n                    });\n                    break;\n                case \"ArrowRight\":\n                    // if (specialKey) return\n                    // e.preventDefault()\n                    this.navigate({\n                        e: e,\n                        input: input,\n                        right: true\n                    });\n                    break;\n                case \"ArrowUp\":\n                    // if (specialKey) return\n                    // e.preventDefault()\n                    this.navigate({\n                        e: e,\n                        input: input,\n                        up: true\n                    });\n                    break;\n                case \"ArrowDown\":\n                    // if (specialKey) return\n                    // e.preventDefault()\n                    this.navigate({\n                        e: e,\n                        input: input,\n                        down: true\n                    });\n                    break;\n                case \"Enter\":\n                    e.preventDefault();\n                    this.handleEmojiClick({\n                        e: e,\n                        pos: this.state.pos\n                    });\n                    break;\n                case \"Escape\":\n                    e.preventDefault();\n                    if (this.state.searchResults) this.clearSearch();\n                    else this.unfocusSearch();\n                    break;\n                default:\n                    break;\n            }\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"clearSearch\", ()=>{\n            const input = this.refs.searchInput.current;\n            if (!input) return;\n            input.value = \"\";\n            input.focus();\n            this.handleSearchInput();\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"handleCategoryClick\", ({ category: category , i: i  })=>{\n            this.scrollTo(i == 0 ? {\n                row: -1\n            } : {\n                categoryId: category.id\n            });\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"openSkins\", (e)=>{\n            const { currentTarget: currentTarget  } = e;\n            const rect = currentTarget.getBoundingClientRect();\n            this.setState({\n                showSkins: rect\n            }, async ()=>{\n                // Firefox requires 2 frames for the transition to consistenly work\n                await (0, $693b183b0a78708f$export$e772c8ff12451969)(2);\n                const menu = this.refs.menu.current;\n                if (!menu) return;\n                menu.classList.remove(\"hidden\");\n                this.refs.skinToneRadio.current.focus();\n                this.base.addEventListener(\"click\", this.handleBaseClick, true);\n                this.base.addEventListener(\"keydown\", this.handleBaseKeydown, true);\n            });\n        });\n        this.observers = [];\n        this.state = {\n            pos: [\n                -1,\n                -1\n            ],\n            perLine: this.initDynamicPerLine(props),\n            visibleRows: {\n                0: true\n            },\n            ...this.getInitialState(props)\n        };\n    }\n}\n\n\n\n\n\n\n\n\n\nclass $efa000751917694d$export$2e2bcd8739ae039 extends (0, $26f27c338a96b1a6$export$2e2bcd8739ae039) {\n    async connectedCallback() {\n        const props = (0, $7adb23b0109cc36a$export$75fe5f91d452f94b)(this.props, (0, $b247ea80b67298d5$export$2e2bcd8739ae039), this);\n        props.element = this;\n        props.ref = (component)=>{\n            this.component = component;\n        };\n        await (0, $7adb23b0109cc36a$export$2cd8252107eb640b)(props);\n        if (this.disconnected) return;\n        (0, $fb96b826c0c5f37a$export$b3890eb0ae9dca99)(/*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)((0, $89bd6bb200cc8fef$export$2e2bcd8739ae039), {\n            ...props\n        }), this.shadowRoot);\n    }\n    constructor(props){\n        super(props, {\n            styles: (0, (/*@__PURE__*/$parcel$interopDefault($329d53ba9fd7125f$exports)))\n        });\n    }\n}\n(0, $c770c458706daa72$export$2e2bcd8739ae039)($efa000751917694d$export$2e2bcd8739ae039, \"Props\", (0, $b247ea80b67298d5$export$2e2bcd8739ae039));\nif (typeof customElements !== \"undefined\" && !customElements.get(\"em-emoji-picker\")) customElements.define(\"em-emoji-picker\", $efa000751917694d$export$2e2bcd8739ae039);\n\n\nvar $329d53ba9fd7125f$exports = {};\n$329d53ba9fd7125f$exports = \":host {\\n  width: min-content;\\n  height: 435px;\\n  min-height: 230px;\\n  border-radius: var(--border-radius);\\n  box-shadow: var(--shadow);\\n  --border-radius: 10px;\\n  --category-icon-size: 18px;\\n  --font-family: -apple-system, BlinkMacSystemFont, \\\"Helvetica Neue\\\", sans-serif;\\n  --font-size: 15px;\\n  --preview-placeholder-size: 21px;\\n  --preview-title-size: 1.1em;\\n  --preview-subtitle-size: .9em;\\n  --shadow-color: 0deg 0% 0%;\\n  --shadow: .3px .5px 2.7px hsl(var(--shadow-color) / .14), .4px .8px 1px -3.2px hsl(var(--shadow-color) / .14), 1px 2px 2.5px -4.5px hsl(var(--shadow-color) / .14);\\n  display: flex;\\n}\\n\\n[data-theme=\\\"light\\\"] {\\n  --em-rgb-color: var(--rgb-color, 34, 36, 39);\\n  --em-rgb-accent: var(--rgb-accent, 34, 102, 237);\\n  --em-rgb-background: var(--rgb-background, 255, 255, 255);\\n  --em-rgb-input: var(--rgb-input, 255, 255, 255);\\n  --em-color-border: var(--color-border, rgba(0, 0, 0, .05));\\n  --em-color-border-over: var(--color-border-over, rgba(0, 0, 0, .1));\\n}\\n\\n[data-theme=\\\"dark\\\"] {\\n  --em-rgb-color: var(--rgb-color, 222, 222, 221);\\n  --em-rgb-accent: var(--rgb-accent, 58, 130, 247);\\n  --em-rgb-background: var(--rgb-background, 21, 22, 23);\\n  --em-rgb-input: var(--rgb-input, 0, 0, 0);\\n  --em-color-border: var(--color-border, rgba(255, 255, 255, .1));\\n  --em-color-border-over: var(--color-border-over, rgba(255, 255, 255, .2));\\n}\\n\\n#root {\\n  --color-a: rgb(var(--em-rgb-color));\\n  --color-b: rgba(var(--em-rgb-color), .65);\\n  --color-c: rgba(var(--em-rgb-color), .45);\\n  --padding: 12px;\\n  --padding-small: calc(var(--padding) / 2);\\n  --sidebar-width: 16px;\\n  --duration: 225ms;\\n  --duration-fast: 125ms;\\n  --duration-instant: 50ms;\\n  --easing: cubic-bezier(.4, 0, .2, 1);\\n  width: 100%;\\n  text-align: left;\\n  border-radius: var(--border-radius);\\n  background-color: rgb(var(--em-rgb-background));\\n  position: relative;\\n}\\n\\n@media (prefers-reduced-motion) {\\n  #root {\\n    --duration: 0;\\n    --duration-fast: 0;\\n    --duration-instant: 0;\\n  }\\n}\\n\\n#root[data-menu] button {\\n  cursor: auto;\\n}\\n\\n#root[data-menu] .menu button {\\n  cursor: pointer;\\n}\\n\\n:host, #root, input, button {\\n  color: rgb(var(--em-rgb-color));\\n  font-family: var(--font-family);\\n  font-size: var(--font-size);\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n  line-height: normal;\\n}\\n\\n*, :before, :after {\\n  box-sizing: border-box;\\n  min-width: 0;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n.relative {\\n  position: relative;\\n}\\n\\n.flex {\\n  display: flex;\\n}\\n\\n.flex-auto {\\n  flex: none;\\n}\\n\\n.flex-center {\\n  justify-content: center;\\n}\\n\\n.flex-column {\\n  flex-direction: column;\\n}\\n\\n.flex-grow {\\n  flex: auto;\\n}\\n\\n.flex-middle {\\n  align-items: center;\\n}\\n\\n.flex-wrap {\\n  flex-wrap: wrap;\\n}\\n\\n.padding {\\n  padding: var(--padding);\\n}\\n\\n.padding-t {\\n  padding-top: var(--padding);\\n}\\n\\n.padding-lr {\\n  padding-left: var(--padding);\\n  padding-right: var(--padding);\\n}\\n\\n.padding-r {\\n  padding-right: var(--padding);\\n}\\n\\n.padding-small {\\n  padding: var(--padding-small);\\n}\\n\\n.padding-small-b {\\n  padding-bottom: var(--padding-small);\\n}\\n\\n.padding-small-lr {\\n  padding-left: var(--padding-small);\\n  padding-right: var(--padding-small);\\n}\\n\\n.margin {\\n  margin: var(--padding);\\n}\\n\\n.margin-r {\\n  margin-right: var(--padding);\\n}\\n\\n.margin-l {\\n  margin-left: var(--padding);\\n}\\n\\n.margin-small-l {\\n  margin-left: var(--padding-small);\\n}\\n\\n.margin-small-lr {\\n  margin-left: var(--padding-small);\\n  margin-right: var(--padding-small);\\n}\\n\\n.align-l {\\n  text-align: left;\\n}\\n\\n.align-r {\\n  text-align: right;\\n}\\n\\n.color-a {\\n  color: var(--color-a);\\n}\\n\\n.color-b {\\n  color: var(--color-b);\\n}\\n\\n.color-c {\\n  color: var(--color-c);\\n}\\n\\n.ellipsis {\\n  white-space: nowrap;\\n  max-width: 100%;\\n  width: auto;\\n  text-overflow: ellipsis;\\n  overflow: hidden;\\n}\\n\\n.sr-only {\\n  width: 1px;\\n  height: 1px;\\n  position: absolute;\\n  top: auto;\\n  left: -10000px;\\n  overflow: hidden;\\n}\\n\\na {\\n  cursor: pointer;\\n  color: rgb(var(--em-rgb-accent));\\n}\\n\\na:hover {\\n  text-decoration: underline;\\n}\\n\\n.spacer {\\n  height: 10px;\\n}\\n\\n[dir=\\\"rtl\\\"] .scroll {\\n  padding-left: 0;\\n  padding-right: var(--padding);\\n}\\n\\n.scroll {\\n  padding-right: 0;\\n  overflow-x: hidden;\\n  overflow-y: auto;\\n}\\n\\n.scroll::-webkit-scrollbar {\\n  width: var(--sidebar-width);\\n  height: var(--sidebar-width);\\n}\\n\\n.scroll::-webkit-scrollbar-track {\\n  border: 0;\\n}\\n\\n.scroll::-webkit-scrollbar-button {\\n  width: 0;\\n  height: 0;\\n  display: none;\\n}\\n\\n.scroll::-webkit-scrollbar-corner {\\n  background-color: rgba(0, 0, 0, 0);\\n}\\n\\n.scroll::-webkit-scrollbar-thumb {\\n  min-height: 20%;\\n  min-height: 65px;\\n  border: 4px solid rgb(var(--em-rgb-background));\\n  border-radius: 8px;\\n}\\n\\n.scroll::-webkit-scrollbar-thumb:hover {\\n  background-color: var(--em-color-border-over) !important;\\n}\\n\\n.scroll:hover::-webkit-scrollbar-thumb {\\n  background-color: var(--em-color-border);\\n}\\n\\n.sticky {\\n  z-index: 1;\\n  background-color: rgba(var(--em-rgb-background), .9);\\n  -webkit-backdrop-filter: blur(4px);\\n  backdrop-filter: blur(4px);\\n  font-weight: 500;\\n  position: sticky;\\n  top: -1px;\\n}\\n\\n[dir=\\\"rtl\\\"] .search input[type=\\\"search\\\"] {\\n  padding: 10px 2.2em 10px 2em;\\n}\\n\\n[dir=\\\"rtl\\\"] .search .loupe {\\n  left: auto;\\n  right: .7em;\\n}\\n\\n[dir=\\\"rtl\\\"] .search .delete {\\n  left: .7em;\\n  right: auto;\\n}\\n\\n.search {\\n  z-index: 2;\\n  position: relative;\\n}\\n\\n.search input, .search button {\\n  font-size: calc(var(--font-size)  - 1px);\\n}\\n\\n.search input[type=\\\"search\\\"] {\\n  width: 100%;\\n  background-color: var(--em-color-border);\\n  transition-duration: var(--duration);\\n  transition-property: background-color, box-shadow;\\n  transition-timing-function: var(--easing);\\n  border: 0;\\n  border-radius: 10px;\\n  outline: 0;\\n  padding: 10px 2em 10px 2.2em;\\n  display: block;\\n}\\n\\n.search input[type=\\\"search\\\"]::-ms-input-placeholder {\\n  color: inherit;\\n  opacity: .6;\\n}\\n\\n.search input[type=\\\"search\\\"]::placeholder {\\n  color: inherit;\\n  opacity: .6;\\n}\\n\\n.search input[type=\\\"search\\\"], .search input[type=\\\"search\\\"]::-webkit-search-decoration, .search input[type=\\\"search\\\"]::-webkit-search-cancel-button, .search input[type=\\\"search\\\"]::-webkit-search-results-button, .search input[type=\\\"search\\\"]::-webkit-search-results-decoration {\\n  -webkit-appearance: none;\\n  -ms-appearance: none;\\n  appearance: none;\\n}\\n\\n.search input[type=\\\"search\\\"]:focus {\\n  background-color: rgb(var(--em-rgb-input));\\n  box-shadow: inset 0 0 0 1px rgb(var(--em-rgb-accent)), 0 1px 3px rgba(65, 69, 73, .2);\\n}\\n\\n.search .icon {\\n  z-index: 1;\\n  color: rgba(var(--em-rgb-color), .7);\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n}\\n\\n.search .loupe {\\n  pointer-events: none;\\n  left: .7em;\\n}\\n\\n.search .delete {\\n  right: .7em;\\n}\\n\\nsvg {\\n  fill: currentColor;\\n  width: 1em;\\n  height: 1em;\\n}\\n\\nbutton {\\n  -webkit-appearance: none;\\n  -ms-appearance: none;\\n  appearance: none;\\n  cursor: pointer;\\n  color: currentColor;\\n  background-color: rgba(0, 0, 0, 0);\\n  border: 0;\\n}\\n\\n#nav {\\n  z-index: 2;\\n  padding-top: 12px;\\n  padding-bottom: 12px;\\n  padding-right: var(--sidebar-width);\\n  position: relative;\\n}\\n\\n#nav button {\\n  color: var(--color-b);\\n  transition: color var(--duration) var(--easing);\\n}\\n\\n#nav button:hover {\\n  color: var(--color-a);\\n}\\n\\n#nav svg, #nav img {\\n  width: var(--category-icon-size);\\n  height: var(--category-icon-size);\\n}\\n\\n#nav[dir=\\\"rtl\\\"] .bar {\\n  left: auto;\\n  right: 0;\\n}\\n\\n#nav .bar {\\n  width: 100%;\\n  height: 3px;\\n  background-color: rgb(var(--em-rgb-accent));\\n  transition: transform var(--duration) var(--easing);\\n  border-radius: 3px 3px 0 0;\\n  position: absolute;\\n  bottom: -12px;\\n  left: 0;\\n}\\n\\n#nav button[aria-selected] {\\n  color: rgb(var(--em-rgb-accent));\\n}\\n\\n#preview {\\n  z-index: 2;\\n  padding: calc(var(--padding)  + 4px) var(--padding);\\n  padding-right: var(--sidebar-width);\\n  position: relative;\\n}\\n\\n#preview .preview-placeholder {\\n  font-size: var(--preview-placeholder-size);\\n}\\n\\n#preview .preview-title {\\n  font-size: var(--preview-title-size);\\n}\\n\\n#preview .preview-subtitle {\\n  font-size: var(--preview-subtitle-size);\\n}\\n\\n#nav:before, #preview:before {\\n  content: \\\"\\\";\\n  height: 2px;\\n  position: absolute;\\n  left: 0;\\n  right: 0;\\n}\\n\\n#nav[data-position=\\\"top\\\"]:before, #preview[data-position=\\\"top\\\"]:before {\\n  background: linear-gradient(to bottom, var(--em-color-border), transparent);\\n  top: 100%;\\n}\\n\\n#nav[data-position=\\\"bottom\\\"]:before, #preview[data-position=\\\"bottom\\\"]:before {\\n  background: linear-gradient(to top, var(--em-color-border), transparent);\\n  bottom: 100%;\\n}\\n\\n.category:last-child {\\n  min-height: calc(100% + 1px);\\n}\\n\\n.category button {\\n  font-family: -apple-system, BlinkMacSystemFont, Helvetica Neue, sans-serif;\\n  position: relative;\\n}\\n\\n.category button > * {\\n  position: relative;\\n}\\n\\n.category button .background {\\n  opacity: 0;\\n  background-color: var(--em-color-border);\\n  transition: opacity var(--duration-fast) var(--easing) var(--duration-instant);\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n}\\n\\n.category button:hover .background {\\n  transition-duration: var(--duration-instant);\\n  transition-delay: 0s;\\n}\\n\\n.category button[aria-selected] .background {\\n  opacity: 1;\\n}\\n\\n.category button[data-keyboard] .background {\\n  transition: none;\\n}\\n\\n.row {\\n  width: 100%;\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n}\\n\\n.skin-tone-button {\\n  border: 1px solid rgba(0, 0, 0, 0);\\n  border-radius: 100%;\\n}\\n\\n.skin-tone-button:hover {\\n  border-color: var(--em-color-border);\\n}\\n\\n.skin-tone-button:active .skin-tone {\\n  transform: scale(.85) !important;\\n}\\n\\n.skin-tone-button .skin-tone {\\n  transition: transform var(--duration) var(--easing);\\n}\\n\\n.skin-tone-button[aria-selected] {\\n  background-color: var(--em-color-border);\\n  border-top-color: rgba(0, 0, 0, .05);\\n  border-bottom-color: rgba(0, 0, 0, 0);\\n  border-left-width: 0;\\n  border-right-width: 0;\\n}\\n\\n.skin-tone-button[aria-selected] .skin-tone {\\n  transform: scale(.9);\\n}\\n\\n.menu {\\n  z-index: 2;\\n  white-space: nowrap;\\n  border: 1px solid var(--em-color-border);\\n  background-color: rgba(var(--em-rgb-background), .9);\\n  -webkit-backdrop-filter: blur(4px);\\n  backdrop-filter: blur(4px);\\n  transition-property: opacity, transform;\\n  transition-duration: var(--duration);\\n  transition-timing-function: var(--easing);\\n  border-radius: 10px;\\n  padding: 4px;\\n  position: absolute;\\n  box-shadow: 1px 1px 5px rgba(0, 0, 0, .05);\\n}\\n\\n.menu.hidden {\\n  opacity: 0;\\n}\\n\\n.menu[data-position=\\\"bottom\\\"] {\\n  transform-origin: 100% 100%;\\n}\\n\\n.menu[data-position=\\\"bottom\\\"].hidden {\\n  transform: scale(.9)rotate(-3deg)translateY(5%);\\n}\\n\\n.menu[data-position=\\\"top\\\"] {\\n  transform-origin: 100% 0;\\n}\\n\\n.menu[data-position=\\\"top\\\"].hidden {\\n  transform: scale(.9)rotate(3deg)translateY(-5%);\\n}\\n\\n.menu input[type=\\\"radio\\\"] {\\n  clip: rect(0 0 0 0);\\n  width: 1px;\\n  height: 1px;\\n  border: 0;\\n  margin: 0;\\n  padding: 0;\\n  position: absolute;\\n  overflow: hidden;\\n}\\n\\n.menu input[type=\\\"radio\\\"]:checked + .option {\\n  box-shadow: 0 0 0 2px rgb(var(--em-rgb-accent));\\n}\\n\\n.option {\\n  width: 100%;\\n  border-radius: 6px;\\n  padding: 4px 6px;\\n}\\n\\n.option:hover {\\n  color: #fff;\\n  background-color: rgb(var(--em-rgb-accent));\\n}\\n\\n.skin-tone {\\n  width: 16px;\\n  height: 16px;\\n  border-radius: 100%;\\n  display: inline-block;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.skin-tone:after {\\n  content: \\\"\\\";\\n  mix-blend-mode: overlay;\\n  background: linear-gradient(rgba(255, 255, 255, .2), rgba(0, 0, 0, 0));\\n  border: 1px solid rgba(0, 0, 0, .8);\\n  border-radius: 100%;\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  box-shadow: inset 0 -2px 3px #000, inset 0 1px 2px #fff;\\n}\\n\\n.skin-tone-1 {\\n  background-color: #ffc93a;\\n}\\n\\n.skin-tone-2 {\\n  background-color: #ffdab7;\\n}\\n\\n.skin-tone-3 {\\n  background-color: #e7b98f;\\n}\\n\\n.skin-tone-4 {\\n  background-color: #c88c61;\\n}\\n\\n.skin-tone-5 {\\n  background-color: #a46134;\\n}\\n\\n.skin-tone-6 {\\n  background-color: #5d4437;\\n}\\n\\n[data-index] {\\n  justify-content: space-between;\\n}\\n\\n[data-emoji-set=\\\"twitter\\\"] .skin-tone:after {\\n  box-shadow: none;\\n  border-color: rgba(0, 0, 0, .5);\\n}\\n\\n[data-emoji-set=\\\"twitter\\\"] .skin-tone-1 {\\n  background-color: #fade72;\\n}\\n\\n[data-emoji-set=\\\"twitter\\\"] .skin-tone-2 {\\n  background-color: #f3dfd0;\\n}\\n\\n[data-emoji-set=\\\"twitter\\\"] .skin-tone-3 {\\n  background-color: #eed3a8;\\n}\\n\\n[data-emoji-set=\\\"twitter\\\"] .skin-tone-4 {\\n  background-color: #cfad8d;\\n}\\n\\n[data-emoji-set=\\\"twitter\\\"] .skin-tone-5 {\\n  background-color: #a8805d;\\n}\\n\\n[data-emoji-set=\\\"twitter\\\"] .skin-tone-6 {\\n  background-color: #765542;\\n}\\n\\n[data-emoji-set=\\\"google\\\"] .skin-tone:after {\\n  box-shadow: inset 0 0 2px 2px rgba(0, 0, 0, .4);\\n}\\n\\n[data-emoji-set=\\\"google\\\"] .skin-tone-1 {\\n  background-color: #f5c748;\\n}\\n\\n[data-emoji-set=\\\"google\\\"] .skin-tone-2 {\\n  background-color: #f1d5aa;\\n}\\n\\n[data-emoji-set=\\\"google\\\"] .skin-tone-3 {\\n  background-color: #d4b48d;\\n}\\n\\n[data-emoji-set=\\\"google\\\"] .skin-tone-4 {\\n  background-color: #aa876b;\\n}\\n\\n[data-emoji-set=\\\"google\\\"] .skin-tone-5 {\\n  background-color: #916544;\\n}\\n\\n[data-emoji-set=\\\"google\\\"] .skin-tone-6 {\\n  background-color: #61493f;\\n}\\n\\n[data-emoji-set=\\\"facebook\\\"] .skin-tone:after {\\n  border-color: rgba(0, 0, 0, .4);\\n  box-shadow: inset 0 -2px 3px #000, inset 0 1px 4px #fff;\\n}\\n\\n[data-emoji-set=\\\"facebook\\\"] .skin-tone-1 {\\n  background-color: #f5c748;\\n}\\n\\n[data-emoji-set=\\\"facebook\\\"] .skin-tone-2 {\\n  background-color: #f1d5aa;\\n}\\n\\n[data-emoji-set=\\\"facebook\\\"] .skin-tone-3 {\\n  background-color: #d4b48d;\\n}\\n\\n[data-emoji-set=\\\"facebook\\\"] .skin-tone-4 {\\n  background-color: #aa876b;\\n}\\n\\n[data-emoji-set=\\\"facebook\\\"] .skin-tone-5 {\\n  background-color: #916544;\\n}\\n\\n[data-emoji-set=\\\"facebook\\\"] .skin-tone-6 {\\n  background-color: #61493f;\\n}\\n\\n\";\n\n\n\n\n\n\n\n\n\n\n\n//# sourceMappingURL=module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/emoji-mart/dist/module.js\n");

/***/ })

};
;