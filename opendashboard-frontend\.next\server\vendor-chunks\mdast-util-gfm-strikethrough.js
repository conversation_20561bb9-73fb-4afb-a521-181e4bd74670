"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-gfm-strikethrough";
exports.ids = ["vendor-chunks/mdast-util-gfm-strikethrough"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-gfm-strikethrough/lib/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-gfm-strikethrough/lib/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmStrikethroughFromMarkdown: () => (/* binding */ gfmStrikethroughFromMarkdown),\n/* harmony export */   gfmStrikethroughToMarkdown: () => (/* binding */ gfmStrikethroughToMarkdown)\n/* harmony export */ });\n/* harmony import */ var mdast_util_to_markdown_lib_util_container_phrasing_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mdast-util-to-markdown/lib/util/container-phrasing.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js\");\n/* harmony import */ var mdast_util_to_markdown_lib_util_track_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-to-markdown/lib/util/track.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/track.js\");\n/**\n * @typedef {import('mdast').Delete} Delete\n *\n * @typedef {import('mdast-util-from-markdown').CompileContext} CompileContext\n * @typedef {import('mdast-util-from-markdown').Extension} FromMarkdownExtension\n * @typedef {import('mdast-util-from-markdown').Handle} FromMarkdownHandle\n *\n * @typedef {import('mdast-util-to-markdown').ConstructName} ConstructName\n * @typedef {import('mdast-util-to-markdown').Options} ToMarkdownExtension\n * @typedef {import('mdast-util-to-markdown').Handle} ToMarkdownHandle\n */\n\n\n\n\n// To do: next major: expose functions.\n// To do: next major: use `state`, state utilities.\n\n/**\n * List of constructs that occur in phrasing (paragraphs, headings), but cannot\n * contain strikethrough.\n * So they sort of cancel each other out.\n * Note: could use a better name.\n *\n * Note: keep in sync with: <https://github.com/syntax-tree/mdast-util-to-markdown/blob/8ce8dbf/lib/unsafe.js#L14>\n *\n * @type {Array<ConstructName>}\n */\nconst constructsWithoutStrikethrough = [\n  'autolink',\n  'destinationLiteral',\n  'destinationRaw',\n  'reference',\n  'titleQuote',\n  'titleApostrophe'\n]\n\nhandleDelete.peek = peekDelete\n\n/**\n * Extension for `mdast-util-from-markdown` to enable GFM strikethrough.\n *\n * @type {FromMarkdownExtension}\n */\nconst gfmStrikethroughFromMarkdown = {\n  canContainEols: ['delete'],\n  enter: {strikethrough: enterStrikethrough},\n  exit: {strikethrough: exitStrikethrough}\n}\n\n/**\n * Extension for `mdast-util-to-markdown` to enable GFM strikethrough.\n *\n * @type {ToMarkdownExtension}\n */\nconst gfmStrikethroughToMarkdown = {\n  unsafe: [\n    {\n      character: '~',\n      inConstruct: 'phrasing',\n      notInConstruct: constructsWithoutStrikethrough\n    }\n  ],\n  handlers: {delete: handleDelete}\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterStrikethrough(token) {\n  this.enter({type: 'delete', children: []}, token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitStrikethrough(token) {\n  this.exit(token)\n}\n\n/**\n * @type {ToMarkdownHandle}\n * @param {Delete} node\n */\nfunction handleDelete(node, _, context, safeOptions) {\n  const tracker = (0,mdast_util_to_markdown_lib_util_track_js__WEBPACK_IMPORTED_MODULE_0__.track)(safeOptions)\n  const exit = context.enter('strikethrough')\n  let value = tracker.move('~~')\n  value += (0,mdast_util_to_markdown_lib_util_container_phrasing_js__WEBPACK_IMPORTED_MODULE_1__.containerPhrasing)(node, context, {\n    ...tracker.current(),\n    before: value,\n    after: '~'\n  })\n  value += tracker.move('~~')\n  exit()\n  return value\n}\n\n/** @type {ToMarkdownHandle} */\nfunction peekDelete() {\n  return '~'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm-strikethrough/lib/index.js\n");

/***/ })

};
;