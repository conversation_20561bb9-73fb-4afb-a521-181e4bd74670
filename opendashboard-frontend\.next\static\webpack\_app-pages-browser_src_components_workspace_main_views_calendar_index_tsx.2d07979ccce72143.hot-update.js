"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonthView: function() { return /* binding */ MonthView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CalendarSideCard */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarSideCard.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst DayCell = (param)=>{\n    let { date, children, onClick, isCurrentMonth } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable)({\n        id: \"daycell-\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(date, \"yyyy-MM-dd\")),\n        data: {\n            date: date,\n            type: \"daycell\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        onClick: onClick,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[120px] lg:p-3 lg:min-h-[140px]\", isCurrentMonth ? \"bg-white hover:bg-neutral-50\" : \"bg-neutral-100 hover:bg-neutral-200\", isOver && \"bg-blue-50 border-blue-200\"),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DayCell, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable\n    ];\n});\n_c = DayCell;\n// My own calculation function for processing events\nconst useMonthEvents = (weeks, events)=>{\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const eventsByDay = new Map();\n        const eventsByWeek = new Map();\n        // Process each week\n        weeks.forEach((week, weekIndex)=>{\n            const weekStart = week[0];\n            const weekEnd = week[6];\n            // Find all events that overlap with this week\n            const weekEvents = events.filter((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                return eventStart <= weekEnd && eventEnd >= weekStart;\n            });\n            // Process each day in the week\n            week.forEach((day, dayIndex)=>{\n                const dayKey = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"yyyy-MM-dd\");\n                const dayEvents = [];\n                weekEvents.forEach((event)=>{\n                    const eventStart = new Date(event.start);\n                    const eventEnd = new Date(event.end);\n                    // Check if this event appears on this day\n                    const isEventOnThisDay = eventStart <= day && eventEnd >= day;\n                    if (isEventOnThisDay) {\n                        // Calculate event properties for this day\n                        const isStartDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(eventStart, day);\n                        const isEndDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(eventEnd, day);\n                        const isMultiDay = !(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(eventStart, eventEnd);\n                        // Calculate span within this week\n                        const weekStartIndex = week.findIndex((d)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(d, eventStart));\n                        const weekEndIndex = week.findIndex((d)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(eventEnd, d));\n                        const startInWeek = weekStartIndex !== -1 ? weekStartIndex : 0;\n                        const endInWeek = weekEndIndex !== -1 ? weekEndIndex : 6;\n                        const spanInWeek = endInWeek - startInWeek + 1;\n                        dayEvents.push({\n                            event,\n                            dayIndex,\n                            isStartDay,\n                            isEndDay,\n                            isMultiDay,\n                            spanInWeek,\n                            showTitle: isStartDay || dayIndex === 0 && !isStartDay,\n                            isDraggable: isStartDay,\n                            row: 0,\n                            colSpan: spanInWeek\n                        });\n                    }\n                });\n                // Sort events by start time and assign rows\n                const sortedDayEvents = dayEvents.sort((a, b)=>new Date(a.event.start).getTime() - new Date(b.event.start).getTime());\n                // Assign row positions (simple stacking for now)\n                sortedDayEvents.forEach((event, index)=>{\n                    event.row = index;\n                });\n                eventsByDay.set(dayKey, sortedDayEvents);\n            });\n            // Store week events for spanning calculations\n            eventsByWeek.set(weekIndex, weekEvents);\n        });\n        return {\n            eventsByDay,\n            eventsByWeek\n        };\n    }, [\n        weeks,\n        events\n    ]);\n};\n_s1(useMonthEvents, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\nconst MonthView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, handleEventClick, activeDragData } = param;\n    _s2();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord)();\n    const isInRecordTab = !!maybeRecord;\n    // Memoize month calculations\n    const monthCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const monthStart = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(selectedDate);\n        const monthEnd = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(selectedDate);\n        const startDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(monthStart, {\n            weekStartsOn: 0\n        });\n        const endDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(monthEnd, {\n            weekStartsOn: 0\n        });\n        const days = [];\n        let day = startDay;\n        while(day <= endDay){\n            days.push(day);\n            day = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(day, 1);\n        }\n        const weeks = [];\n        for(let i = 0; i < days.length; i += 7){\n            weeks.push(days.slice(i, i + 7));\n        }\n        return {\n            monthStart,\n            monthEnd,\n            startDay,\n            endDay,\n            days,\n            weeks\n        };\n    }, [\n        selectedDate\n    ]);\n    // Memoize month events\n    const monthEvents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>events.filter((event)=>{\n            const eventStart = new Date(event.start);\n            return eventStart >= monthCalculations.startDay && eventStart <= monthCalculations.endDay;\n        }), [\n        events,\n        monthCalculations.startDay,\n        monthCalculations.endDay\n    ]);\n    const { eventsByDay, eventsByWeek } = useMonthEvents(monthCalculations.weeks, events);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 border-b border-neutral-300 bg-white\",\n                    children: [\n                        \"Sunday\",\n                        \"Monday\",\n                        \"Tuesday\",\n                        \"Wednesday\",\n                        \"Thursday\",\n                        \"Friday\",\n                        \"Saturday\"\n                    ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                            children: dayName.substring(0, 3)\n                        }, dayName, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_7__.NoEvents, {\n                    title: \"No events this month\",\n                    message: \"\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, \"MMMM yyyy\"), \" is completely free. Start planning your month!\"),\n                    showCreateButton: canEditData,\n                    onCreate: ()=>openAddEventForm(selectedDate)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n            lineNumber: 194,\n            columnNumber: 5\n        }, undefined);\n    // Render day cell content\n    const renderDayCellContent = (day)=>{\n        const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n        const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(day);\n        const MAX_VISIBLE_EVENTS = 4;\n        const ROW_HEIGHT = 28;\n        // Get events for this specific day\n        const dayKey = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"yyyy-MM-dd\");\n        const dayEvents = eventsByDay.get(dayKey) || [];\n        const visibleEvents = dayEvents.slice(0, MAX_VISIBLE_EVENTS);\n        const hasMore = dayEvents.length > MAX_VISIBLE_EVENTS;\n        // Fixed height calculation\n        const containerHeight = (MAX_VISIBLE_EVENTS + 1) * ROW_HEIGHT; // 5 * 28 = 140px\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\", isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"),\n                            children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"d\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, undefined),\n                        canEditData && isCurrentMonth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            className: \"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\",\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setTimeout(()=>openAddEventForm(day), 150);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-3 w-3 text-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    style: {\n                        height: \"\".concat(containerHeight, \"px\")\n                    },\n                    children: [\n                        visibleEvents.map((pe)=>{\n                            var _activeDragData_payload;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute\",\n                                style: {\n                                    top: \"\".concat(pe.row * ROW_HEIGHT, \"px\"),\n                                    left: \"2px\",\n                                    width: pe.colSpan > 1 ? \"calc(\".concat(pe.colSpan * 100, \"% + \").concat((pe.colSpan - 1) * 19, \"px)\") : \"calc(100% - 4px)\",\n                                    zIndex: 10 + pe.row\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__.CalendarEventItem, {\n                                    event: pe.event,\n                                    view: \"month\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        setSelectedEvent(pe.event.id);\n                                        handleEventClick(pe.event);\n                                    },\n                                    isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === pe.event.id,\n                                    showTitle: pe.showTitle,\n                                    isDraggable: pe.isDraggable\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, pe.event.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, undefined);\n                        }),\n                        hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-black hover:text-black font-medium text-xs cursor-pointer\",\n                            style: {\n                                position: \"absolute\",\n                                top: \"\".concat(MAX_VISIBLE_EVENTS * ROW_HEIGHT, \"px\"),\n                                left: \"2px\"\n                            },\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setSelectedDate(day);\n                            },\n                            children: [\n                                \"+ \",\n                                dayEvents.length - MAX_VISIBLE_EVENTS,\n                                \" more\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true);\n    };\n    // Render main view\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-background flex flex-col lg:flex-row\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\",\n                        children: [\n                            \"Sunday\",\n                            \"Monday\",\n                            \"Tuesday\",\n                            \"Wednesday\",\n                            \"Thursday\",\n                            \"Friday\",\n                            \"Saturday\"\n                        ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                                children: dayName.substring(0, 3)\n                            }, dayName, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-7 border-neutral-300 border-b\",\n                            children: monthCalculations.weeks.map((week, weekIndex)=>week.map((day, dayIndex)=>{\n                                    const weekEvents = positionedEventsByWeek.get(weekIndex) || [];\n                                    const dayEvents = weekEvents.filter((pe)=>pe.startDayIndex === dayIndex);\n                                    const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DayCell, {\n                                        date: day,\n                                        isCurrentMonth: isCurrentMonth,\n                                        onClick: ()=>setSelectedDate(day),\n                                        children: renderDayCellContent(day, dayEvents)\n                                    }, \"\".concat(weekIndex, \"-\").concat(dayIndex), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__.CalendarSideCard, {\n                selectedDate: selectedDate,\n                events: events,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 346,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 307,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(MonthView, \"xuj2ymautd1Bv9M7yORRYsVT0FU=\", false, function() {\n    return [\n        _providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord,\n        useMonthEvents\n    ];\n});\n_c1 = MonthView;\nfunction isMultiDay(event) {\n    return !(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(new Date(event.start), new Date(event.end));\n}\nvar _c, _c1;\n$RefreshReg$(_c, \"DayCell\");\n$RefreshReg$(_c1, \"MonthView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ })

});