"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/AllDayRow.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/AllDayRow.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AllDayRow: function() { return /* binding */ AllDayRow; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isSameDay,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isSameDay,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isSameDay,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isSameDay,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/eventCollisionUtils */ \"(app-pages-browser)/./src/utils/eventCollisionUtils.ts\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst AllDayRow = (param)=>{\n    let { selectedDate, segments, selectedEvent, setSelectedEvent, handleEventClick, canEditData, openAddEventForm, view, activeDragData } = param;\n    _s();\n    // Create individual droppable hooks for each possible day\n    // IMPORTANT: All hooks must be called before any conditional returns\n    const dayViewHook = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"yyyy-MM-dd\")),\n        data: {\n            date: selectedDate,\n            type: \"allday-day\"\n        }\n    });\n    // Create hooks for week view days\n    const weekDay1 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 0), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 0),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay2 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 1), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 1),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay3 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 2), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 2),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay4 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 3), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 3),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay5 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 4), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 4),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay6 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 5), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 5),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay7 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 6), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 6),\n            type: \"allday-week\"\n        }\n    });\n    const weekViewHooks = [\n        weekDay1,\n        weekDay2,\n        weekDay3,\n        weekDay4,\n        weekDay5,\n        weekDay6,\n        weekDay7\n    ];\n    if (segments.length === 0 && !activeDragData) {\n        return null;\n    }\n    const renderDayView = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"border-b border-neutral-300 bg-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky left-0 bg-white z-10 w-14 lg:w-20 border-r border-neutral-300 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-neutral-500\",\n                        children: \"All-day\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: dayViewHook.setNodeRef,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 relative p-2 space-y-1\", dayViewHook.isOver && \"bg-blue-50\"),\n                        children: [\n                            segments.slice(0, 3).map((segment)=>{\n                                var _activeDragData_payload;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                    segment: segment,\n                                    style: {\n                                        height: \"24px\",\n                                        width: \"100%\"\n                                    },\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        setSelectedEvent(segment.originalEventId);\n                                        handleEventClick(segment.originalEvent);\n                                    },\n                                    view: \"day\",\n                                    isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === segment.id\n                                }, segment.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, undefined);\n                            }),\n                            segments.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-neutral-600 font-medium cursor-pointer hover:text-neutral-800\",\n                                children: [\n                                    \"+ \",\n                                    segments.length - 3,\n                                    \" more\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n            lineNumber: 117,\n            columnNumber: 5\n        }, undefined);\n    const renderWeekView = ()=>{\n        // Week view specific logic starts here\n        const weekDays = Array.from({\n            length: 7\n        }, (_, i)=>(0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), i));\n        const spanningEvents = (()=>{\n            const eventGroups = new Map();\n            segments.forEach((segment)=>{\n                if (segment.isMultiDay || segment.isAllDay) {\n                    const eventId = segment.originalEventId;\n                    if (!eventGroups.has(eventId)) eventGroups.set(eventId, []);\n                    eventGroups.get(eventId).push(segment);\n                }\n            });\n            const spanning = [];\n            eventGroups.forEach((eventSegments)=>{\n                eventSegments.sort((a, b)=>a.date.getTime() - b.date.getTime());\n                const firstSegmentInWeek = eventSegments[0];\n                const lastSegmentInWeek = eventSegments[eventSegments.length - 1];\n                const startDayIndex = weekDays.findIndex((day)=>(0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, firstSegmentInWeek.date));\n                const endDayIndex = weekDays.findIndex((day)=>(0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, lastSegmentInWeek.date));\n                if (startDayIndex >= 0 && endDayIndex >= 0) {\n                    spanning.push({\n                        segment: firstSegmentInWeek,\n                        startDayIndex,\n                        endDayIndex,\n                        colSpan: endDayIndex - startDayIndex + 1,\n                        isEndOfEvent: lastSegmentInWeek.isLastSegment\n                    });\n                }\n            });\n            return spanning;\n        })();\n        const positionedEvents = (()=>{\n            const positioned = [];\n            const rows = [];\n            const sortedEvents = [\n                ...spanningEvents\n            ].sort((a, b)=>a.startDayIndex - b.startDayIndex || b.colSpan - a.colSpan);\n            sortedEvents.forEach((event)=>{\n                let assigned = false;\n                for(let i = 0; i < rows.length; i++){\n                    const row = rows[i];\n                    if (row.every((rowEvent)=>event.startDayIndex > rowEvent.endDayIndex || event.endDayIndex < rowEvent.startDayIndex)) {\n                        row.push(event);\n                        positioned.push({\n                            ...event,\n                            row: i\n                        });\n                        assigned = true;\n                        break;\n                    }\n                }\n                if (!assigned) {\n                    rows.push([\n                        event\n                    ]);\n                    positioned.push({\n                        ...event,\n                        row: rows.length - 1\n                    });\n                }\n            });\n            return positioned;\n        })();\n        // Use the new layout calculator\n        const { visibleSegments, moreCount } = (0,_utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_4__.calculateAllDayLayout)(positionedEvents.map((e)=>e.segment), 3);\n        const visibleEvents = positionedEvents.filter((p)=>visibleSegments.some((s)=>s.id === p.segment.id));\n        const hasMore = moreCount > 0;\n        const firstEventDayIndex = positionedEvents.length > 0 ? Math.min(...positionedEvents.map((e)=>e.startDayIndex)) : 0;\n        if (positionedEvents.length === 0) return null;\n        const maxRows = positionedEvents.length > 0 ? Math.max(...positionedEvents.map((e)=>e.row)) + 1 : 0;\n        const rowHeight = 28;\n        const displayRows = hasMore ? 3.5 : Math.max(1, maxRows);\n        const totalHeight = displayRows * rowHeight + 16;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            \"data-all-day-row\": \"true\",\n            className: \"border-b border-neutral-300 bg-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky left-0 bg-white z-10 w-14 lg:w-20 border-r border-neutral-300 flex items-center justify-end pr-4 pt-2 text-xs font-medium text-neutral-500\",\n                        children: \"All-day\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative p-2\",\n                        style: {\n                            height: \"\".concat(totalHeight, \"px\")\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-7 gap-1 h-full\",\n                            children: weekDays.map((day, dayIndex)=>{\n                                const hook = weekViewHooks[dayIndex];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: hook.setNodeRef,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative cursor-pointer hover:bg-neutral-50 rounded-sm transition-colors\", hook.isOver && \"bg-blue-50\"),\n                                    onDoubleClick: ()=>{\n                                        if (canEditData) {\n                                            const newDate = new Date(day);\n                                            newDate.setHours(9, 0, 0, 0);\n                                            openAddEventForm(newDate);\n                                        }\n                                    },\n                                    children: [\n                                        visibleEvents.filter((spanningEvent)=>spanningEvent.startDayIndex === dayIndex).map((spanningEvent)=>{\n                                            var _activeDragData_payload;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute z-10\",\n                                                style: {\n                                                    top: \"\".concat(spanningEvent.row * rowHeight + 2, \"px\"),\n                                                    left: \"0px\",\n                                                    width: \"calc(\".concat(spanningEvent.colSpan * 100, \"% + \").concat((spanningEvent.colSpan - 1) * 4, \"px)\"),\n                                                    height: \"24px\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                                    segment: spanningEvent.segment,\n                                                    isEndOfEvent: spanningEvent.isEndOfEvent,\n                                                    style: {\n                                                        height: \"24px\",\n                                                        width: \"100%\"\n                                                    },\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        setSelectedEvent(spanningEvent.segment.originalEventId);\n                                                        handleEventClick(spanningEvent.segment.originalEvent);\n                                                    },\n                                                    view: view,\n                                                    isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === spanningEvent.segment.id\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, spanningEvent.segment.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 23\n                                            }, undefined);\n                                        }),\n                                        hasMore && dayIndex === firstEventDayIndex && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute z-10 cursor-pointer text-xs text-gray-600 hover:underline\",\n                                            style: {\n                                                top: \"\".concat(3 * rowHeight + 2, \"px\"),\n                                                left: \"4px\",\n                                                right: \"4px\"\n                                            },\n                                            onClick: ()=>console.log(\"More clicked\"),\n                                            children: [\n                                                \"+\",\n                                                moreCount,\n                                                \" more\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, dayIndex, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n                lineNumber: 234,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\AllDayRow.tsx\",\n            lineNumber: 230,\n            columnNumber: 5\n        }, undefined);\n    };\n    return view === \"day\" ? renderDayView() : renderWeekView();\n};\n_s(AllDayRow, \"7+zBFPGq0SU8Pt7cRj6jYFJX7rY=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable\n    ];\n});\n_c = AllDayRow;\nvar _c;\n$RefreshReg$(_c, \"AllDayRow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/AllDayRow.tsx\n"));

/***/ })

});