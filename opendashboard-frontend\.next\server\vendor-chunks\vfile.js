"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/vfile";
exports.ids = ["vendor-chunks/vfile"];
exports.modules = {

/***/ "(ssr)/./node_modules/vfile/lib/index.js":
/*!*****************************************!*\
  !*** ./node_modules/vfile/lib/index.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VFile: () => (/* binding */ VFile)\n/* harmony export */ });\n/* harmony import */ var is_buffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! is-buffer */ \"(ssr)/./node_modules/is-buffer/index.js\");\n/* harmony import */ var vfile_message__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! vfile-message */ \"(ssr)/./node_modules/vfile-message/lib/index.js\");\n/* harmony import */ var _minpath_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./minpath.js */ \"path\");\n/* harmony import */ var _minproc_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./minproc.js */ \"process\");\n/* harmony import */ var _minurl_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./minurl.js */ \"(ssr)/./node_modules/vfile/lib/minurl.shared.js\");\n/* harmony import */ var _minurl_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./minurl.js */ \"url\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Position} Position\n * @typedef {import('unist').Point} Point\n * @typedef {import('./minurl.shared.js').URL} URL\n * @typedef {import('../index.js').Data} Data\n * @typedef {import('../index.js').Value} Value\n */\n\n/**\n * @typedef {Record<string, unknown> & {type: string, position?: Position | undefined}} NodeLike\n *\n * @typedef {'ascii' | 'utf8' | 'utf-8' | 'utf16le' | 'ucs2' | 'ucs-2' | 'base64' | 'base64url' | 'latin1' | 'binary' | 'hex'} BufferEncoding\n *   Encodings supported by the buffer class.\n *\n *   This is a copy of the types from Node, copied to prevent Node globals from\n *   being needed.\n *   Copied from: <https://github.com/DefinitelyTyped/DefinitelyTyped/blob/90a4ec8/types/node/buffer.d.ts#L170>\n *\n * @typedef {Options | URL | Value | VFile} Compatible\n *   Things that can be passed to the constructor.\n *\n * @typedef VFileCoreOptions\n *   Set multiple values.\n * @property {Value | null | undefined} [value]\n *   Set `value`.\n * @property {string | null | undefined} [cwd]\n *   Set `cwd`.\n * @property {Array<string> | null | undefined} [history]\n *   Set `history`.\n * @property {URL | string | null | undefined} [path]\n *   Set `path`.\n * @property {string | null | undefined} [basename]\n *   Set `basename`.\n * @property {string | null | undefined} [stem]\n *   Set `stem`.\n * @property {string | null | undefined} [extname]\n *   Set `extname`.\n * @property {string | null | undefined} [dirname]\n *   Set `dirname`.\n * @property {Data | null | undefined} [data]\n *   Set `data`.\n *\n * @typedef Map\n *   Raw source map.\n *\n *   See:\n *   <https://github.com/mozilla/source-map/blob/58819f0/source-map.d.ts#L15-L23>.\n * @property {number} version\n *   Which version of the source map spec this map is following.\n * @property {Array<string>} sources\n *   An array of URLs to the original source files.\n * @property {Array<string>} names\n *   An array of identifiers which can be referenced by individual mappings.\n * @property {string | undefined} [sourceRoot]\n *   The URL root from which all sources are relative.\n * @property {Array<string> | undefined} [sourcesContent]\n *   An array of contents of the original source files.\n * @property {string} mappings\n *   A string of base64 VLQs which contain the actual mappings.\n * @property {string} file\n *   The generated file this source map is associated with.\n *\n * @typedef {{[key: string]: unknown} & VFileCoreOptions} Options\n *   Configuration.\n *\n *   A bunch of keys that will be shallow copied over to the new file.\n *\n * @typedef {Record<string, unknown>} ReporterSettings\n *   Configuration for reporters.\n */\n\n/**\n * @template {ReporterSettings} Settings\n *   Options type.\n * @callback Reporter\n *   Type for a reporter.\n * @param {Array<VFile>} files\n *   Files to report.\n * @param {Settings} options\n *   Configuration.\n * @returns {string}\n *   Report.\n */\n\n\n\n\n\n\n\n/**\n * Order of setting (least specific to most), we need this because otherwise\n * `{stem: 'a', path: '~/b.js'}` would throw, as a path is needed before a\n * stem can be set.\n *\n * @type {Array<'basename' | 'dirname' | 'extname' | 'history' | 'path' | 'stem'>}\n */\nconst order = ['history', 'path', 'basename', 'stem', 'extname', 'dirname']\n\nclass VFile {\n  /**\n   * Create a new virtual file.\n   *\n   * `options` is treated as:\n   *\n   * *   `string` or `Buffer` — `{value: options}`\n   * *   `URL` — `{path: options}`\n   * *   `VFile` — shallow copies its data over to the new file\n   * *   `object` — all fields are shallow copied over to the new file\n   *\n   * Path related fields are set in the following order (least specific to\n   * most specific): `history`, `path`, `basename`, `stem`, `extname`,\n   * `dirname`.\n   *\n   * You cannot set `dirname` or `extname` without setting either `history`,\n   * `path`, `basename`, or `stem` too.\n   *\n   * @param {Compatible | null | undefined} [value]\n   *   File value.\n   * @returns\n   *   New instance.\n   */\n  constructor(value) {\n    /** @type {Options | VFile} */\n    let options\n\n    if (!value) {\n      options = {}\n    } else if (typeof value === 'string' || buffer(value)) {\n      options = {value}\n    } else if ((0,_minurl_js__WEBPACK_IMPORTED_MODULE_1__.isUrl)(value)) {\n      options = {path: value}\n    } else {\n      options = value\n    }\n\n    /**\n     * Place to store custom information (default: `{}`).\n     *\n     * It’s OK to store custom data directly on the file but moving it to\n     * `data` is recommended.\n     *\n     * @type {Data}\n     */\n    this.data = {}\n\n    /**\n     * List of messages associated with the file.\n     *\n     * @type {Array<VFileMessage>}\n     */\n    this.messages = []\n\n    /**\n     * List of filepaths the file moved between.\n     *\n     * The first is the original path and the last is the current path.\n     *\n     * @type {Array<string>}\n     */\n    this.history = []\n\n    /**\n     * Base of `path` (default: `process.cwd()` or `'/'` in browsers).\n     *\n     * @type {string}\n     */\n    this.cwd = _minproc_js__WEBPACK_IMPORTED_MODULE_2__.cwd()\n\n    /* eslint-disable no-unused-expressions */\n    /**\n     * Raw value.\n     *\n     * @type {Value}\n     */\n    this.value\n\n    // The below are non-standard, they are “well-known”.\n    // As in, used in several tools.\n\n    /**\n     * Whether a file was saved to disk.\n     *\n     * This is used by vfile reporters.\n     *\n     * @type {boolean}\n     */\n    this.stored\n\n    /**\n     * Custom, non-string, compiled, representation.\n     *\n     * This is used by unified to store non-string results.\n     * One example is when turning markdown into React nodes.\n     *\n     * @type {unknown}\n     */\n    this.result\n\n    /**\n     * Source map.\n     *\n     * This type is equivalent to the `RawSourceMap` type from the `source-map`\n     * module.\n     *\n     * @type {Map | null | undefined}\n     */\n    this.map\n    /* eslint-enable no-unused-expressions */\n\n    // Set path related properties in the correct order.\n    let index = -1\n\n    while (++index < order.length) {\n      const prop = order[index]\n\n      // Note: we specifically use `in` instead of `hasOwnProperty` to accept\n      // `vfile`s too.\n      if (\n        prop in options &&\n        options[prop] !== undefined &&\n        options[prop] !== null\n      ) {\n        // @ts-expect-error: TS doesn’t understand basic reality.\n        this[prop] = prop === 'history' ? [...options[prop]] : options[prop]\n      }\n    }\n\n    /** @type {string} */\n    let prop\n\n    // Set non-path related properties.\n    for (prop in options) {\n      // @ts-expect-error: fine to set other things.\n      if (!order.includes(prop)) {\n        // @ts-expect-error: fine to set other things.\n        this[prop] = options[prop]\n      }\n    }\n  }\n\n  /**\n   * Get the full path (example: `'~/index.min.js'`).\n   *\n   * @returns {string}\n   */\n  get path() {\n    return this.history[this.history.length - 1]\n  }\n\n  /**\n   * Set the full path (example: `'~/index.min.js'`).\n   *\n   * Cannot be nullified.\n   * You can set a file URL (a `URL` object with a `file:` protocol) which will\n   * be turned into a path with `url.fileURLToPath`.\n   *\n   * @param {string | URL} path\n   */\n  set path(path) {\n    if ((0,_minurl_js__WEBPACK_IMPORTED_MODULE_1__.isUrl)(path)) {\n      path = (0,_minurl_js__WEBPACK_IMPORTED_MODULE_3__.fileURLToPath)(path)\n    }\n\n    assertNonEmpty(path, 'path')\n\n    if (this.path !== path) {\n      this.history.push(path)\n    }\n  }\n\n  /**\n   * Get the parent path (example: `'~'`).\n   */\n  get dirname() {\n    return typeof this.path === 'string' ? _minpath_js__WEBPACK_IMPORTED_MODULE_4__.dirname(this.path) : undefined\n  }\n\n  /**\n   * Set the parent path (example: `'~'`).\n   *\n   * Cannot be set if there’s no `path` yet.\n   */\n  set dirname(dirname) {\n    assertPath(this.basename, 'dirname')\n    this.path = _minpath_js__WEBPACK_IMPORTED_MODULE_4__.join(dirname || '', this.basename)\n  }\n\n  /**\n   * Get the basename (including extname) (example: `'index.min.js'`).\n   */\n  get basename() {\n    return typeof this.path === 'string' ? _minpath_js__WEBPACK_IMPORTED_MODULE_4__.basename(this.path) : undefined\n  }\n\n  /**\n   * Set basename (including extname) (`'index.min.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   */\n  set basename(basename) {\n    assertNonEmpty(basename, 'basename')\n    assertPart(basename, 'basename')\n    this.path = _minpath_js__WEBPACK_IMPORTED_MODULE_4__.join(this.dirname || '', basename)\n  }\n\n  /**\n   * Get the extname (including dot) (example: `'.js'`).\n   */\n  get extname() {\n    return typeof this.path === 'string' ? _minpath_js__WEBPACK_IMPORTED_MODULE_4__.extname(this.path) : undefined\n  }\n\n  /**\n   * Set the extname (including dot) (example: `'.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be set if there’s no `path` yet.\n   */\n  set extname(extname) {\n    assertPart(extname, 'extname')\n    assertPath(this.dirname, 'extname')\n\n    if (extname) {\n      if (extname.charCodeAt(0) !== 46 /* `.` */) {\n        throw new Error('`extname` must start with `.`')\n      }\n\n      if (extname.includes('.', 1)) {\n        throw new Error('`extname` cannot contain multiple dots')\n      }\n    }\n\n    this.path = _minpath_js__WEBPACK_IMPORTED_MODULE_4__.join(this.dirname, this.stem + (extname || ''))\n  }\n\n  /**\n   * Get the stem (basename w/o extname) (example: `'index.min'`).\n   */\n  get stem() {\n    return typeof this.path === 'string'\n      ? _minpath_js__WEBPACK_IMPORTED_MODULE_4__.basename(this.path, this.extname)\n      : undefined\n  }\n\n  /**\n   * Set the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   */\n  set stem(stem) {\n    assertNonEmpty(stem, 'stem')\n    assertPart(stem, 'stem')\n    this.path = _minpath_js__WEBPACK_IMPORTED_MODULE_4__.join(this.dirname || '', stem + (this.extname || ''))\n  }\n\n  /**\n   * Serialize the file.\n   *\n   * @param {BufferEncoding | null | undefined} [encoding='utf8']\n   *   Character encoding to understand `value` as when it’s a `Buffer`\n   *   (default: `'utf8'`).\n   * @returns {string}\n   *   Serialized file.\n   */\n  toString(encoding) {\n    return (this.value || '').toString(encoding || undefined)\n  }\n\n  /**\n   * Create a warning message associated with the file.\n   *\n   * Its `fatal` is set to `false` and `file` is set to the current file path.\n   * Its added to `file.messages`.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  message(reason, place, origin) {\n    const message = new vfile_message__WEBPACK_IMPORTED_MODULE_5__.VFileMessage(reason, place, origin)\n\n    if (this.path) {\n      message.name = this.path + ':' + message.name\n      message.file = this.path\n    }\n\n    message.fatal = false\n\n    this.messages.push(message)\n\n    return message\n  }\n\n  /**\n   * Create an info message associated with the file.\n   *\n   * Its `fatal` is set to `null` and `file` is set to the current file path.\n   * Its added to `file.messages`.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  info(reason, place, origin) {\n    const message = this.message(reason, place, origin)\n\n    message.fatal = null\n\n    return message\n  }\n\n  /**\n   * Create a fatal error associated with the file.\n   *\n   * Its `fatal` is set to `true` and `file` is set to the current file path.\n   * Its added to `file.messages`.\n   *\n   * > 👉 **Note**: a fatal error means that a file is no longer processable.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {never}\n   *   Message.\n   * @throws {VFileMessage}\n   *   Message.\n   */\n  fail(reason, place, origin) {\n    const message = this.message(reason, place, origin)\n\n    message.fatal = true\n\n    throw message\n  }\n}\n\n/**\n * Assert that `part` is not a path (as in, does not contain `path.sep`).\n *\n * @param {string | null | undefined} part\n *   File path part.\n * @param {string} name\n *   Part name.\n * @returns {void}\n *   Nothing.\n */\nfunction assertPart(part, name) {\n  if (part && part.includes(_minpath_js__WEBPACK_IMPORTED_MODULE_4__.sep)) {\n    throw new Error(\n      '`' + name + '` cannot be a path: did not expect `' + _minpath_js__WEBPACK_IMPORTED_MODULE_4__.sep + '`'\n    )\n  }\n}\n\n/**\n * Assert that `part` is not empty.\n *\n * @param {string | undefined} part\n *   Thing.\n * @param {string} name\n *   Part name.\n * @returns {asserts part is string}\n *   Nothing.\n */\nfunction assertNonEmpty(part, name) {\n  if (!part) {\n    throw new Error('`' + name + '` cannot be empty')\n  }\n}\n\n/**\n * Assert `path` exists.\n *\n * @param {string | undefined} path\n *   Path.\n * @param {string} name\n *   Dependency name.\n * @returns {asserts path is string}\n *   Nothing.\n */\nfunction assertPath(path, name) {\n  if (!path) {\n    throw new Error('Setting `' + name + '` requires `path` to be set too')\n  }\n}\n\n/**\n * Assert `value` is a buffer.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Buffer}\n *   Whether `value` is a Node.js buffer.\n */\nfunction buffer(value) {\n  return is_buffer__WEBPACK_IMPORTED_MODULE_0__(value)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vfile/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/vfile/lib/minurl.shared.js":
/*!*************************************************!*\
  !*** ./node_modules/vfile/lib/minurl.shared.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isUrl: () => (/* binding */ isUrl)\n/* harmony export */ });\n/**\n * @typedef URL\n * @property {string} hash\n * @property {string} host\n * @property {string} hostname\n * @property {string} href\n * @property {string} origin\n * @property {string} password\n * @property {string} pathname\n * @property {string} port\n * @property {string} protocol\n * @property {string} search\n * @property {any} searchParams\n * @property {string} username\n * @property {() => string} toString\n * @property {() => string} toJSON\n */\n\n/**\n * Check if `fileUrlOrPath` looks like a URL.\n *\n * @param {unknown} fileUrlOrPath\n *   File path or URL.\n * @returns {fileUrlOrPath is URL}\n *   Whether it’s a URL.\n */\n// From: <https://github.com/nodejs/node/blob/fcf8ba4/lib/internal/url.js#L1501>\nfunction isUrl(fileUrlOrPath) {\n  return (\n    fileUrlOrPath !== null &&\n    typeof fileUrlOrPath === 'object' &&\n    // @ts-expect-error: indexable.\n    fileUrlOrPath.href &&\n    // @ts-expect-error: indexable.\n    fileUrlOrPath.origin\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vfile/lib/minurl.shared.js\n");

/***/ })

};
;