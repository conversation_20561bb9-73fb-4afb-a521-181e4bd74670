"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-tables";
exports.ids = ["vendor-chunks/prosemirror-tables"];
exports.modules = {

/***/ "(ssr)/./node_modules/prosemirror-tables/dist/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/prosemirror-tables/dist/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CellBookmark: () => (/* binding */ CellBookmark),\n/* harmony export */   CellSelection: () => (/* binding */ CellSelection),\n/* harmony export */   ResizeState: () => (/* binding */ ResizeState),\n/* harmony export */   TableMap: () => (/* binding */ TableMap),\n/* harmony export */   TableView: () => (/* binding */ TableView),\n/* harmony export */   __clipCells: () => (/* binding */ clipCells),\n/* harmony export */   __insertCells: () => (/* binding */ insertCells),\n/* harmony export */   __pastedCells: () => (/* binding */ pastedCells),\n/* harmony export */   addColSpan: () => (/* binding */ addColSpan),\n/* harmony export */   addColumn: () => (/* binding */ addColumn),\n/* harmony export */   addColumnAfter: () => (/* binding */ addColumnAfter),\n/* harmony export */   addColumnBefore: () => (/* binding */ addColumnBefore),\n/* harmony export */   addRow: () => (/* binding */ addRow),\n/* harmony export */   addRowAfter: () => (/* binding */ addRowAfter),\n/* harmony export */   addRowBefore: () => (/* binding */ addRowBefore),\n/* harmony export */   cellAround: () => (/* binding */ cellAround),\n/* harmony export */   cellNear: () => (/* binding */ cellNear),\n/* harmony export */   colCount: () => (/* binding */ colCount),\n/* harmony export */   columnIsHeader: () => (/* binding */ columnIsHeader),\n/* harmony export */   columnResizing: () => (/* binding */ columnResizing),\n/* harmony export */   columnResizingPluginKey: () => (/* binding */ columnResizingPluginKey),\n/* harmony export */   deleteCellSelection: () => (/* binding */ deleteCellSelection),\n/* harmony export */   deleteColumn: () => (/* binding */ deleteColumn),\n/* harmony export */   deleteRow: () => (/* binding */ deleteRow),\n/* harmony export */   deleteTable: () => (/* binding */ deleteTable),\n/* harmony export */   findCell: () => (/* binding */ findCell),\n/* harmony export */   fixTables: () => (/* binding */ fixTables),\n/* harmony export */   fixTablesKey: () => (/* binding */ fixTablesKey),\n/* harmony export */   goToNextCell: () => (/* binding */ goToNextCell),\n/* harmony export */   handlePaste: () => (/* binding */ handlePaste),\n/* harmony export */   inSameTable: () => (/* binding */ inSameTable),\n/* harmony export */   isInTable: () => (/* binding */ isInTable),\n/* harmony export */   mergeCells: () => (/* binding */ mergeCells),\n/* harmony export */   moveCellForward: () => (/* binding */ moveCellForward),\n/* harmony export */   nextCell: () => (/* binding */ nextCell),\n/* harmony export */   pointsAtCell: () => (/* binding */ pointsAtCell),\n/* harmony export */   removeColSpan: () => (/* binding */ removeColSpan),\n/* harmony export */   removeColumn: () => (/* binding */ removeColumn),\n/* harmony export */   removeRow: () => (/* binding */ removeRow),\n/* harmony export */   rowIsHeader: () => (/* binding */ rowIsHeader),\n/* harmony export */   selectedRect: () => (/* binding */ selectedRect),\n/* harmony export */   selectionCell: () => (/* binding */ selectionCell),\n/* harmony export */   setCellAttr: () => (/* binding */ setCellAttr),\n/* harmony export */   splitCell: () => (/* binding */ splitCell),\n/* harmony export */   splitCellWithType: () => (/* binding */ splitCellWithType),\n/* harmony export */   tableEditing: () => (/* binding */ tableEditing),\n/* harmony export */   tableEditingKey: () => (/* binding */ tableEditingKey),\n/* harmony export */   tableNodeTypes: () => (/* binding */ tableNodeTypes),\n/* harmony export */   tableNodes: () => (/* binding */ tableNodes),\n/* harmony export */   toggleHeader: () => (/* binding */ toggleHeader),\n/* harmony export */   toggleHeaderCell: () => (/* binding */ toggleHeaderCell),\n/* harmony export */   toggleHeaderColumn: () => (/* binding */ toggleHeaderColumn),\n/* harmony export */   toggleHeaderRow: () => (/* binding */ toggleHeaderRow),\n/* harmony export */   updateColumnsOnResize: () => (/* binding */ updateColumnsOnResize)\n/* harmony export */ });\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/./node_modules/prosemirror-state/dist/index.js\");\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/./node_modules/prosemirror-model/dist/index.js\");\n/* harmony import */ var prosemirror_view__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prosemirror-view */ \"(ssr)/./node_modules/prosemirror-view/dist/index.js\");\n/* harmony import */ var prosemirror_keymap__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! prosemirror-keymap */ \"(ssr)/./node_modules/prosemirror-keymap/dist/index.js\");\n/* harmony import */ var prosemirror_transform__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prosemirror-transform */ \"(ssr)/./node_modules/prosemirror-transform/dist/index.js\");\n// src/index.ts\n\n\n// src/cellselection.ts\n\n\n\n\n// src/tablemap.ts\nvar readFromCache;\nvar addToCache;\nif (typeof WeakMap != \"undefined\") {\n  let cache = /* @__PURE__ */ new WeakMap();\n  readFromCache = (key) => cache.get(key);\n  addToCache = (key, value) => {\n    cache.set(key, value);\n    return value;\n  };\n} else {\n  const cache = [];\n  const cacheSize = 10;\n  let cachePos = 0;\n  readFromCache = (key) => {\n    for (let i = 0; i < cache.length; i += 2)\n      if (cache[i] == key) return cache[i + 1];\n  };\n  addToCache = (key, value) => {\n    if (cachePos == cacheSize) cachePos = 0;\n    cache[cachePos++] = key;\n    return cache[cachePos++] = value;\n  };\n}\nvar TableMap = class {\n  constructor(width, height, map, problems) {\n    this.width = width;\n    this.height = height;\n    this.map = map;\n    this.problems = problems;\n  }\n  // Find the dimensions of the cell at the given position.\n  findCell(pos) {\n    for (let i = 0; i < this.map.length; i++) {\n      const curPos = this.map[i];\n      if (curPos != pos) continue;\n      const left = i % this.width;\n      const top = i / this.width | 0;\n      let right = left + 1;\n      let bottom = top + 1;\n      for (let j = 1; right < this.width && this.map[i + j] == curPos; j++) {\n        right++;\n      }\n      for (let j = 1; bottom < this.height && this.map[i + this.width * j] == curPos; j++) {\n        bottom++;\n      }\n      return { left, top, right, bottom };\n    }\n    throw new RangeError(`No cell with offset ${pos} found`);\n  }\n  // Find the left side of the cell at the given position.\n  colCount(pos) {\n    for (let i = 0; i < this.map.length; i++) {\n      if (this.map[i] == pos) {\n        return i % this.width;\n      }\n    }\n    throw new RangeError(`No cell with offset ${pos} found`);\n  }\n  // Find the next cell in the given direction, starting from the cell\n  // at `pos`, if any.\n  nextCell(pos, axis, dir) {\n    const { left, right, top, bottom } = this.findCell(pos);\n    if (axis == \"horiz\") {\n      if (dir < 0 ? left == 0 : right == this.width) return null;\n      return this.map[top * this.width + (dir < 0 ? left - 1 : right)];\n    } else {\n      if (dir < 0 ? top == 0 : bottom == this.height) return null;\n      return this.map[left + this.width * (dir < 0 ? top - 1 : bottom)];\n    }\n  }\n  // Get the rectangle spanning the two given cells.\n  rectBetween(a, b) {\n    const {\n      left: leftA,\n      right: rightA,\n      top: topA,\n      bottom: bottomA\n    } = this.findCell(a);\n    const {\n      left: leftB,\n      right: rightB,\n      top: topB,\n      bottom: bottomB\n    } = this.findCell(b);\n    return {\n      left: Math.min(leftA, leftB),\n      top: Math.min(topA, topB),\n      right: Math.max(rightA, rightB),\n      bottom: Math.max(bottomA, bottomB)\n    };\n  }\n  // Return the position of all cells that have the top left corner in\n  // the given rectangle.\n  cellsInRect(rect) {\n    const result = [];\n    const seen = {};\n    for (let row = rect.top; row < rect.bottom; row++) {\n      for (let col = rect.left; col < rect.right; col++) {\n        const index = row * this.width + col;\n        const pos = this.map[index];\n        if (seen[pos]) continue;\n        seen[pos] = true;\n        if (col == rect.left && col && this.map[index - 1] == pos || row == rect.top && row && this.map[index - this.width] == pos) {\n          continue;\n        }\n        result.push(pos);\n      }\n    }\n    return result;\n  }\n  // Return the position at which the cell at the given row and column\n  // starts, or would start, if a cell started there.\n  positionAt(row, col, table) {\n    for (let i = 0, rowStart = 0; ; i++) {\n      const rowEnd = rowStart + table.child(i).nodeSize;\n      if (i == row) {\n        let index = col + row * this.width;\n        const rowEndIndex = (row + 1) * this.width;\n        while (index < rowEndIndex && this.map[index] < rowStart) index++;\n        return index == rowEndIndex ? rowEnd - 1 : this.map[index];\n      }\n      rowStart = rowEnd;\n    }\n  }\n  // Find the table map for the given table node.\n  static get(table) {\n    return readFromCache(table) || addToCache(table, computeMap(table));\n  }\n};\nfunction computeMap(table) {\n  if (table.type.spec.tableRole != \"table\")\n    throw new RangeError(\"Not a table node: \" + table.type.name);\n  const width = findWidth(table), height = table.childCount;\n  const map = [];\n  let mapPos = 0;\n  let problems = null;\n  const colWidths = [];\n  for (let i = 0, e = width * height; i < e; i++) map[i] = 0;\n  for (let row = 0, pos = 0; row < height; row++) {\n    const rowNode = table.child(row);\n    pos++;\n    for (let i = 0; ; i++) {\n      while (mapPos < map.length && map[mapPos] != 0) mapPos++;\n      if (i == rowNode.childCount) break;\n      const cellNode = rowNode.child(i);\n      const { colspan, rowspan, colwidth } = cellNode.attrs;\n      for (let h = 0; h < rowspan; h++) {\n        if (h + row >= height) {\n          (problems || (problems = [])).push({\n            type: \"overlong_rowspan\",\n            pos,\n            n: rowspan - h\n          });\n          break;\n        }\n        const start = mapPos + h * width;\n        for (let w = 0; w < colspan; w++) {\n          if (map[start + w] == 0) map[start + w] = pos;\n          else\n            (problems || (problems = [])).push({\n              type: \"collision\",\n              row,\n              pos,\n              n: colspan - w\n            });\n          const colW = colwidth && colwidth[w];\n          if (colW) {\n            const widthIndex = (start + w) % width * 2, prev = colWidths[widthIndex];\n            if (prev == null || prev != colW && colWidths[widthIndex + 1] == 1) {\n              colWidths[widthIndex] = colW;\n              colWidths[widthIndex + 1] = 1;\n            } else if (prev == colW) {\n              colWidths[widthIndex + 1]++;\n            }\n          }\n        }\n      }\n      mapPos += colspan;\n      pos += cellNode.nodeSize;\n    }\n    const expectedPos = (row + 1) * width;\n    let missing = 0;\n    while (mapPos < expectedPos) if (map[mapPos++] == 0) missing++;\n    if (missing)\n      (problems || (problems = [])).push({ type: \"missing\", row, n: missing });\n    pos++;\n  }\n  if (width === 0 || height === 0)\n    (problems || (problems = [])).push({ type: \"zero_sized\" });\n  const tableMap = new TableMap(width, height, map, problems);\n  let badWidths = false;\n  for (let i = 0; !badWidths && i < colWidths.length; i += 2)\n    if (colWidths[i] != null && colWidths[i + 1] < height) badWidths = true;\n  if (badWidths) findBadColWidths(tableMap, colWidths, table);\n  return tableMap;\n}\nfunction findWidth(table) {\n  let width = -1;\n  let hasRowSpan = false;\n  for (let row = 0; row < table.childCount; row++) {\n    const rowNode = table.child(row);\n    let rowWidth = 0;\n    if (hasRowSpan)\n      for (let j = 0; j < row; j++) {\n        const prevRow = table.child(j);\n        for (let i = 0; i < prevRow.childCount; i++) {\n          const cell = prevRow.child(i);\n          if (j + cell.attrs.rowspan > row) rowWidth += cell.attrs.colspan;\n        }\n      }\n    for (let i = 0; i < rowNode.childCount; i++) {\n      const cell = rowNode.child(i);\n      rowWidth += cell.attrs.colspan;\n      if (cell.attrs.rowspan > 1) hasRowSpan = true;\n    }\n    if (width == -1) width = rowWidth;\n    else if (width != rowWidth) width = Math.max(width, rowWidth);\n  }\n  return width;\n}\nfunction findBadColWidths(map, colWidths, table) {\n  if (!map.problems) map.problems = [];\n  const seen = {};\n  for (let i = 0; i < map.map.length; i++) {\n    const pos = map.map[i];\n    if (seen[pos]) continue;\n    seen[pos] = true;\n    const node = table.nodeAt(pos);\n    if (!node) {\n      throw new RangeError(`No cell with offset ${pos} found`);\n    }\n    let updated = null;\n    const attrs = node.attrs;\n    for (let j = 0; j < attrs.colspan; j++) {\n      const col = (i + j) % map.width;\n      const colWidth = colWidths[col * 2];\n      if (colWidth != null && (!attrs.colwidth || attrs.colwidth[j] != colWidth))\n        (updated || (updated = freshColWidth(attrs)))[j] = colWidth;\n    }\n    if (updated)\n      map.problems.unshift({\n        type: \"colwidth mismatch\",\n        pos,\n        colwidth: updated\n      });\n  }\n}\nfunction freshColWidth(attrs) {\n  if (attrs.colwidth) return attrs.colwidth.slice();\n  const result = [];\n  for (let i = 0; i < attrs.colspan; i++) result.push(0);\n  return result;\n}\n\n// src/util.ts\n\n\n// src/schema.ts\nfunction getCellAttrs(dom, extraAttrs) {\n  if (typeof dom === \"string\") {\n    return {};\n  }\n  const widthAttr = dom.getAttribute(\"data-colwidth\");\n  const widths = widthAttr && /^\\d+(,\\d+)*$/.test(widthAttr) ? widthAttr.split(\",\").map((s) => Number(s)) : null;\n  const colspan = Number(dom.getAttribute(\"colspan\") || 1);\n  const result = {\n    colspan,\n    rowspan: Number(dom.getAttribute(\"rowspan\") || 1),\n    colwidth: widths && widths.length == colspan ? widths : null\n  };\n  for (const prop in extraAttrs) {\n    const getter = extraAttrs[prop].getFromDOM;\n    const value = getter && getter(dom);\n    if (value != null) {\n      result[prop] = value;\n    }\n  }\n  return result;\n}\nfunction setCellAttrs(node, extraAttrs) {\n  const attrs = {};\n  if (node.attrs.colspan != 1) attrs.colspan = node.attrs.colspan;\n  if (node.attrs.rowspan != 1) attrs.rowspan = node.attrs.rowspan;\n  if (node.attrs.colwidth)\n    attrs[\"data-colwidth\"] = node.attrs.colwidth.join(\",\");\n  for (const prop in extraAttrs) {\n    const setter = extraAttrs[prop].setDOMAttr;\n    if (setter) setter(node.attrs[prop], attrs);\n  }\n  return attrs;\n}\nfunction validateColwidth(value) {\n  if (value === null) {\n    return;\n  }\n  if (!Array.isArray(value)) {\n    throw new TypeError(\"colwidth must be null or an array\");\n  }\n  for (const item of value) {\n    if (typeof item !== \"number\") {\n      throw new TypeError(\"colwidth must be null or an array of numbers\");\n    }\n  }\n}\nfunction tableNodes(options) {\n  const extraAttrs = options.cellAttributes || {};\n  const cellAttrs = {\n    colspan: { default: 1, validate: \"number\" },\n    rowspan: { default: 1, validate: \"number\" },\n    colwidth: { default: null, validate: validateColwidth }\n  };\n  for (const prop in extraAttrs)\n    cellAttrs[prop] = {\n      default: extraAttrs[prop].default,\n      validate: extraAttrs[prop].validate\n    };\n  return {\n    table: {\n      content: \"table_row+\",\n      tableRole: \"table\",\n      isolating: true,\n      group: options.tableGroup,\n      parseDOM: [{ tag: \"table\" }],\n      toDOM() {\n        return [\"table\", [\"tbody\", 0]];\n      }\n    },\n    table_row: {\n      content: \"(table_cell | table_header)*\",\n      tableRole: \"row\",\n      parseDOM: [{ tag: \"tr\" }],\n      toDOM() {\n        return [\"tr\", 0];\n      }\n    },\n    table_cell: {\n      content: options.cellContent,\n      attrs: cellAttrs,\n      tableRole: \"cell\",\n      isolating: true,\n      parseDOM: [\n        { tag: \"td\", getAttrs: (dom) => getCellAttrs(dom, extraAttrs) }\n      ],\n      toDOM(node) {\n        return [\"td\", setCellAttrs(node, extraAttrs), 0];\n      }\n    },\n    table_header: {\n      content: options.cellContent,\n      attrs: cellAttrs,\n      tableRole: \"header_cell\",\n      isolating: true,\n      parseDOM: [\n        { tag: \"th\", getAttrs: (dom) => getCellAttrs(dom, extraAttrs) }\n      ],\n      toDOM(node) {\n        return [\"th\", setCellAttrs(node, extraAttrs), 0];\n      }\n    }\n  };\n}\nfunction tableNodeTypes(schema) {\n  let result = schema.cached.tableNodeTypes;\n  if (!result) {\n    result = schema.cached.tableNodeTypes = {};\n    for (const name in schema.nodes) {\n      const type = schema.nodes[name], role = type.spec.tableRole;\n      if (role) result[role] = type;\n    }\n  }\n  return result;\n}\n\n// src/util.ts\nvar tableEditingKey = new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey(\"selectingCells\");\nfunction cellAround($pos) {\n  for (let d = $pos.depth - 1; d > 0; d--)\n    if ($pos.node(d).type.spec.tableRole == \"row\")\n      return $pos.node(0).resolve($pos.before(d + 1));\n  return null;\n}\nfunction cellWrapping($pos) {\n  for (let d = $pos.depth; d > 0; d--) {\n    const role = $pos.node(d).type.spec.tableRole;\n    if (role === \"cell\" || role === \"header_cell\") return $pos.node(d);\n  }\n  return null;\n}\nfunction isInTable(state) {\n  const $head = state.selection.$head;\n  for (let d = $head.depth; d > 0; d--)\n    if ($head.node(d).type.spec.tableRole == \"row\") return true;\n  return false;\n}\nfunction selectionCell(state) {\n  const sel = state.selection;\n  if (\"$anchorCell\" in sel && sel.$anchorCell) {\n    return sel.$anchorCell.pos > sel.$headCell.pos ? sel.$anchorCell : sel.$headCell;\n  } else if (\"node\" in sel && sel.node && sel.node.type.spec.tableRole == \"cell\") {\n    return sel.$anchor;\n  }\n  const $cell = cellAround(sel.$head) || cellNear(sel.$head);\n  if ($cell) {\n    return $cell;\n  }\n  throw new RangeError(`No cell found around position ${sel.head}`);\n}\nfunction cellNear($pos) {\n  for (let after = $pos.nodeAfter, pos = $pos.pos; after; after = after.firstChild, pos++) {\n    const role = after.type.spec.tableRole;\n    if (role == \"cell\" || role == \"header_cell\") return $pos.doc.resolve(pos);\n  }\n  for (let before = $pos.nodeBefore, pos = $pos.pos; before; before = before.lastChild, pos--) {\n    const role = before.type.spec.tableRole;\n    if (role == \"cell\" || role == \"header_cell\")\n      return $pos.doc.resolve(pos - before.nodeSize);\n  }\n}\nfunction pointsAtCell($pos) {\n  return $pos.parent.type.spec.tableRole == \"row\" && !!$pos.nodeAfter;\n}\nfunction moveCellForward($pos) {\n  return $pos.node(0).resolve($pos.pos + $pos.nodeAfter.nodeSize);\n}\nfunction inSameTable($cellA, $cellB) {\n  return $cellA.depth == $cellB.depth && $cellA.pos >= $cellB.start(-1) && $cellA.pos <= $cellB.end(-1);\n}\nfunction findCell($pos) {\n  return TableMap.get($pos.node(-1)).findCell($pos.pos - $pos.start(-1));\n}\nfunction colCount($pos) {\n  return TableMap.get($pos.node(-1)).colCount($pos.pos - $pos.start(-1));\n}\nfunction nextCell($pos, axis, dir) {\n  const table = $pos.node(-1);\n  const map = TableMap.get(table);\n  const tableStart = $pos.start(-1);\n  const moved = map.nextCell($pos.pos - tableStart, axis, dir);\n  return moved == null ? null : $pos.node(0).resolve(tableStart + moved);\n}\nfunction removeColSpan(attrs, pos, n = 1) {\n  const result = { ...attrs, colspan: attrs.colspan - n };\n  if (result.colwidth) {\n    result.colwidth = result.colwidth.slice();\n    result.colwidth.splice(pos, n);\n    if (!result.colwidth.some((w) => w > 0)) result.colwidth = null;\n  }\n  return result;\n}\nfunction addColSpan(attrs, pos, n = 1) {\n  const result = { ...attrs, colspan: attrs.colspan + n };\n  if (result.colwidth) {\n    result.colwidth = result.colwidth.slice();\n    for (let i = 0; i < n; i++) result.colwidth.splice(pos, 0, 0);\n  }\n  return result;\n}\nfunction columnIsHeader(map, table, col) {\n  const headerCell = tableNodeTypes(table.type.schema).header_cell;\n  for (let row = 0; row < map.height; row++)\n    if (table.nodeAt(map.map[col + row * map.width]).type != headerCell)\n      return false;\n  return true;\n}\n\n// src/cellselection.ts\nvar CellSelection = class _CellSelection extends prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection {\n  // A table selection is identified by its anchor and head cells. The\n  // positions given to this constructor should point _before_ two\n  // cells in the same table. They may be the same, to select a single\n  // cell.\n  constructor($anchorCell, $headCell = $anchorCell) {\n    const table = $anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = $anchorCell.start(-1);\n    const rect = map.rectBetween(\n      $anchorCell.pos - tableStart,\n      $headCell.pos - tableStart\n    );\n    const doc = $anchorCell.node(0);\n    const cells = map.cellsInRect(rect).filter((p) => p != $headCell.pos - tableStart);\n    cells.unshift($headCell.pos - tableStart);\n    const ranges = cells.map((pos) => {\n      const cell = table.nodeAt(pos);\n      if (!cell) {\n        throw RangeError(`No cell with offset ${pos} found`);\n      }\n      const from = tableStart + pos + 1;\n      return new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.SelectionRange(\n        doc.resolve(from),\n        doc.resolve(from + cell.content.size)\n      );\n    });\n    super(ranges[0].$from, ranges[0].$to, ranges);\n    this.$anchorCell = $anchorCell;\n    this.$headCell = $headCell;\n  }\n  map(doc, mapping) {\n    const $anchorCell = doc.resolve(mapping.map(this.$anchorCell.pos));\n    const $headCell = doc.resolve(mapping.map(this.$headCell.pos));\n    if (pointsAtCell($anchorCell) && pointsAtCell($headCell) && inSameTable($anchorCell, $headCell)) {\n      const tableChanged = this.$anchorCell.node(-1) != $anchorCell.node(-1);\n      if (tableChanged && this.isRowSelection())\n        return _CellSelection.rowSelection($anchorCell, $headCell);\n      else if (tableChanged && this.isColSelection())\n        return _CellSelection.colSelection($anchorCell, $headCell);\n      else return new _CellSelection($anchorCell, $headCell);\n    }\n    return prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.between($anchorCell, $headCell);\n  }\n  // Returns a rectangular slice of table rows containing the selected\n  // cells.\n  content() {\n    const table = this.$anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = this.$anchorCell.start(-1);\n    const rect = map.rectBetween(\n      this.$anchorCell.pos - tableStart,\n      this.$headCell.pos - tableStart\n    );\n    const seen = {};\n    const rows = [];\n    for (let row = rect.top; row < rect.bottom; row++) {\n      const rowContent = [];\n      for (let index = row * map.width + rect.left, col = rect.left; col < rect.right; col++, index++) {\n        const pos = map.map[index];\n        if (seen[pos]) continue;\n        seen[pos] = true;\n        const cellRect = map.findCell(pos);\n        let cell = table.nodeAt(pos);\n        if (!cell) {\n          throw RangeError(`No cell with offset ${pos} found`);\n        }\n        const extraLeft = rect.left - cellRect.left;\n        const extraRight = cellRect.right - rect.right;\n        if (extraLeft > 0 || extraRight > 0) {\n          let attrs = cell.attrs;\n          if (extraLeft > 0) {\n            attrs = removeColSpan(attrs, 0, extraLeft);\n          }\n          if (extraRight > 0) {\n            attrs = removeColSpan(\n              attrs,\n              attrs.colspan - extraRight,\n              extraRight\n            );\n          }\n          if (cellRect.left < rect.left) {\n            cell = cell.type.createAndFill(attrs);\n            if (!cell) {\n              throw RangeError(\n                `Could not create cell with attrs ${JSON.stringify(attrs)}`\n              );\n            }\n          } else {\n            cell = cell.type.create(attrs, cell.content);\n          }\n        }\n        if (cellRect.top < rect.top || cellRect.bottom > rect.bottom) {\n          const attrs = {\n            ...cell.attrs,\n            rowspan: Math.min(cellRect.bottom, rect.bottom) - Math.max(cellRect.top, rect.top)\n          };\n          if (cellRect.top < rect.top) {\n            cell = cell.type.createAndFill(attrs);\n          } else {\n            cell = cell.type.create(attrs, cell.content);\n          }\n        }\n        rowContent.push(cell);\n      }\n      rows.push(table.child(row).copy(prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(rowContent)));\n    }\n    const fragment = this.isColSelection() && this.isRowSelection() ? table : rows;\n    return new prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(fragment), 1, 1);\n  }\n  replace(tr, content = prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice.empty) {\n    const mapFrom = tr.steps.length, ranges = this.ranges;\n    for (let i = 0; i < ranges.length; i++) {\n      const { $from, $to } = ranges[i], mapping = tr.mapping.slice(mapFrom);\n      tr.replace(\n        mapping.map($from.pos),\n        mapping.map($to.pos),\n        i ? prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice.empty : content\n      );\n    }\n    const sel = prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.findFrom(\n      tr.doc.resolve(tr.mapping.slice(mapFrom).map(this.to)),\n      -1\n    );\n    if (sel) tr.setSelection(sel);\n  }\n  replaceWith(tr, node) {\n    this.replace(tr, new prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(node), 0, 0));\n  }\n  forEachCell(f) {\n    const table = this.$anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = this.$anchorCell.start(-1);\n    const cells = map.cellsInRect(\n      map.rectBetween(\n        this.$anchorCell.pos - tableStart,\n        this.$headCell.pos - tableStart\n      )\n    );\n    for (let i = 0; i < cells.length; i++) {\n      f(table.nodeAt(cells[i]), tableStart + cells[i]);\n    }\n  }\n  // True if this selection goes all the way from the top to the\n  // bottom of the table.\n  isColSelection() {\n    const anchorTop = this.$anchorCell.index(-1);\n    const headTop = this.$headCell.index(-1);\n    if (Math.min(anchorTop, headTop) > 0) return false;\n    const anchorBottom = anchorTop + this.$anchorCell.nodeAfter.attrs.rowspan;\n    const headBottom = headTop + this.$headCell.nodeAfter.attrs.rowspan;\n    return Math.max(anchorBottom, headBottom) == this.$headCell.node(-1).childCount;\n  }\n  // Returns the smallest column selection that covers the given anchor\n  // and head cell.\n  static colSelection($anchorCell, $headCell = $anchorCell) {\n    const table = $anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = $anchorCell.start(-1);\n    const anchorRect = map.findCell($anchorCell.pos - tableStart);\n    const headRect = map.findCell($headCell.pos - tableStart);\n    const doc = $anchorCell.node(0);\n    if (anchorRect.top <= headRect.top) {\n      if (anchorRect.top > 0)\n        $anchorCell = doc.resolve(tableStart + map.map[anchorRect.left]);\n      if (headRect.bottom < map.height)\n        $headCell = doc.resolve(\n          tableStart + map.map[map.width * (map.height - 1) + headRect.right - 1]\n        );\n    } else {\n      if (headRect.top > 0)\n        $headCell = doc.resolve(tableStart + map.map[headRect.left]);\n      if (anchorRect.bottom < map.height)\n        $anchorCell = doc.resolve(\n          tableStart + map.map[map.width * (map.height - 1) + anchorRect.right - 1]\n        );\n    }\n    return new _CellSelection($anchorCell, $headCell);\n  }\n  // True if this selection goes all the way from the left to the\n  // right of the table.\n  isRowSelection() {\n    const table = this.$anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = this.$anchorCell.start(-1);\n    const anchorLeft = map.colCount(this.$anchorCell.pos - tableStart);\n    const headLeft = map.colCount(this.$headCell.pos - tableStart);\n    if (Math.min(anchorLeft, headLeft) > 0) return false;\n    const anchorRight = anchorLeft + this.$anchorCell.nodeAfter.attrs.colspan;\n    const headRight = headLeft + this.$headCell.nodeAfter.attrs.colspan;\n    return Math.max(anchorRight, headRight) == map.width;\n  }\n  eq(other) {\n    return other instanceof _CellSelection && other.$anchorCell.pos == this.$anchorCell.pos && other.$headCell.pos == this.$headCell.pos;\n  }\n  // Returns the smallest row selection that covers the given anchor\n  // and head cell.\n  static rowSelection($anchorCell, $headCell = $anchorCell) {\n    const table = $anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = $anchorCell.start(-1);\n    const anchorRect = map.findCell($anchorCell.pos - tableStart);\n    const headRect = map.findCell($headCell.pos - tableStart);\n    const doc = $anchorCell.node(0);\n    if (anchorRect.left <= headRect.left) {\n      if (anchorRect.left > 0)\n        $anchorCell = doc.resolve(\n          tableStart + map.map[anchorRect.top * map.width]\n        );\n      if (headRect.right < map.width)\n        $headCell = doc.resolve(\n          tableStart + map.map[map.width * (headRect.top + 1) - 1]\n        );\n    } else {\n      if (headRect.left > 0)\n        $headCell = doc.resolve(tableStart + map.map[headRect.top * map.width]);\n      if (anchorRect.right < map.width)\n        $anchorCell = doc.resolve(\n          tableStart + map.map[map.width * (anchorRect.top + 1) - 1]\n        );\n    }\n    return new _CellSelection($anchorCell, $headCell);\n  }\n  toJSON() {\n    return {\n      type: \"cell\",\n      anchor: this.$anchorCell.pos,\n      head: this.$headCell.pos\n    };\n  }\n  static fromJSON(doc, json) {\n    return new _CellSelection(doc.resolve(json.anchor), doc.resolve(json.head));\n  }\n  static create(doc, anchorCell, headCell = anchorCell) {\n    return new _CellSelection(doc.resolve(anchorCell), doc.resolve(headCell));\n  }\n  getBookmark() {\n    return new CellBookmark(this.$anchorCell.pos, this.$headCell.pos);\n  }\n};\nCellSelection.prototype.visible = false;\nprosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.jsonID(\"cell\", CellSelection);\nvar CellBookmark = class _CellBookmark {\n  constructor(anchor, head) {\n    this.anchor = anchor;\n    this.head = head;\n  }\n  map(mapping) {\n    return new _CellBookmark(mapping.map(this.anchor), mapping.map(this.head));\n  }\n  resolve(doc) {\n    const $anchorCell = doc.resolve(this.anchor), $headCell = doc.resolve(this.head);\n    if ($anchorCell.parent.type.spec.tableRole == \"row\" && $headCell.parent.type.spec.tableRole == \"row\" && $anchorCell.index() < $anchorCell.parent.childCount && $headCell.index() < $headCell.parent.childCount && inSameTable($anchorCell, $headCell))\n      return new CellSelection($anchorCell, $headCell);\n    else return prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.near($headCell, 1);\n  }\n};\nfunction drawCellSelection(state) {\n  if (!(state.selection instanceof CellSelection)) return null;\n  const cells = [];\n  state.selection.forEachCell((node, pos) => {\n    cells.push(\n      prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.node(pos, pos + node.nodeSize, { class: \"selectedCell\" })\n    );\n  });\n  return prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.DecorationSet.create(state.doc, cells);\n}\nfunction isCellBoundarySelection({ $from, $to }) {\n  if ($from.pos == $to.pos || $from.pos < $to.pos - 6) return false;\n  let afterFrom = $from.pos;\n  let beforeTo = $to.pos;\n  let depth = $from.depth;\n  for (; depth >= 0; depth--, afterFrom++)\n    if ($from.after(depth + 1) < $from.end(depth)) break;\n  for (let d = $to.depth; d >= 0; d--, beforeTo--)\n    if ($to.before(d + 1) > $to.start(d)) break;\n  return afterFrom == beforeTo && /row|table/.test($from.node(depth).type.spec.tableRole);\n}\nfunction isTextSelectionAcrossCells({ $from, $to }) {\n  let fromCellBoundaryNode;\n  let toCellBoundaryNode;\n  for (let i = $from.depth; i > 0; i--) {\n    const node = $from.node(i);\n    if (node.type.spec.tableRole === \"cell\" || node.type.spec.tableRole === \"header_cell\") {\n      fromCellBoundaryNode = node;\n      break;\n    }\n  }\n  for (let i = $to.depth; i > 0; i--) {\n    const node = $to.node(i);\n    if (node.type.spec.tableRole === \"cell\" || node.type.spec.tableRole === \"header_cell\") {\n      toCellBoundaryNode = node;\n      break;\n    }\n  }\n  return fromCellBoundaryNode !== toCellBoundaryNode && $to.parentOffset === 0;\n}\nfunction normalizeSelection(state, tr, allowTableNodeSelection) {\n  const sel = (tr || state).selection;\n  const doc = (tr || state).doc;\n  let normalize;\n  let role;\n  if (sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection && (role = sel.node.type.spec.tableRole)) {\n    if (role == \"cell\" || role == \"header_cell\") {\n      normalize = CellSelection.create(doc, sel.from);\n    } else if (role == \"row\") {\n      const $cell = doc.resolve(sel.from + 1);\n      normalize = CellSelection.rowSelection($cell, $cell);\n    } else if (!allowTableNodeSelection) {\n      const map = TableMap.get(sel.node);\n      const start = sel.from + 1;\n      const lastCell = start + map.map[map.width * map.height - 1];\n      normalize = CellSelection.create(doc, start + 1, lastCell);\n    }\n  } else if (sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection && isCellBoundarySelection(sel)) {\n    normalize = prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.create(doc, sel.from);\n  } else if (sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection && isTextSelectionAcrossCells(sel)) {\n    normalize = prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.create(doc, sel.$from.start(), sel.$from.end());\n  }\n  if (normalize) (tr || (tr = state.tr)).setSelection(normalize);\n  return tr;\n}\n\n// src/fixtables.ts\n\nvar fixTablesKey = new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey(\"fix-tables\");\nfunction changedDescendants(old, cur, offset, f) {\n  const oldSize = old.childCount, curSize = cur.childCount;\n  outer: for (let i = 0, j = 0; i < curSize; i++) {\n    const child = cur.child(i);\n    for (let scan = j, e = Math.min(oldSize, i + 3); scan < e; scan++) {\n      if (old.child(scan) == child) {\n        j = scan + 1;\n        offset += child.nodeSize;\n        continue outer;\n      }\n    }\n    f(child, offset);\n    if (j < oldSize && old.child(j).sameMarkup(child))\n      changedDescendants(old.child(j), child, offset + 1, f);\n    else child.nodesBetween(0, child.content.size, f, offset + 1);\n    offset += child.nodeSize;\n  }\n}\nfunction fixTables(state, oldState) {\n  let tr;\n  const check = (node, pos) => {\n    if (node.type.spec.tableRole == \"table\")\n      tr = fixTable(state, node, pos, tr);\n  };\n  if (!oldState) state.doc.descendants(check);\n  else if (oldState.doc != state.doc)\n    changedDescendants(oldState.doc, state.doc, 0, check);\n  return tr;\n}\nfunction fixTable(state, table, tablePos, tr) {\n  const map = TableMap.get(table);\n  if (!map.problems) return tr;\n  if (!tr) tr = state.tr;\n  const mustAdd = [];\n  for (let i = 0; i < map.height; i++) mustAdd.push(0);\n  for (let i = 0; i < map.problems.length; i++) {\n    const prob = map.problems[i];\n    if (prob.type == \"collision\") {\n      const cell = table.nodeAt(prob.pos);\n      if (!cell) continue;\n      const attrs = cell.attrs;\n      for (let j = 0; j < attrs.rowspan; j++) mustAdd[prob.row + j] += prob.n;\n      tr.setNodeMarkup(\n        tr.mapping.map(tablePos + 1 + prob.pos),\n        null,\n        removeColSpan(attrs, attrs.colspan - prob.n, prob.n)\n      );\n    } else if (prob.type == \"missing\") {\n      mustAdd[prob.row] += prob.n;\n    } else if (prob.type == \"overlong_rowspan\") {\n      const cell = table.nodeAt(prob.pos);\n      if (!cell) continue;\n      tr.setNodeMarkup(tr.mapping.map(tablePos + 1 + prob.pos), null, {\n        ...cell.attrs,\n        rowspan: cell.attrs.rowspan - prob.n\n      });\n    } else if (prob.type == \"colwidth mismatch\") {\n      const cell = table.nodeAt(prob.pos);\n      if (!cell) continue;\n      tr.setNodeMarkup(tr.mapping.map(tablePos + 1 + prob.pos), null, {\n        ...cell.attrs,\n        colwidth: prob.colwidth\n      });\n    } else if (prob.type == \"zero_sized\") {\n      const pos = tr.mapping.map(tablePos);\n      tr.delete(pos, pos + table.nodeSize);\n    }\n  }\n  let first, last;\n  for (let i = 0; i < mustAdd.length; i++)\n    if (mustAdd[i]) {\n      if (first == null) first = i;\n      last = i;\n    }\n  for (let i = 0, pos = tablePos + 1; i < map.height; i++) {\n    const row = table.child(i);\n    const end = pos + row.nodeSize;\n    const add = mustAdd[i];\n    if (add > 0) {\n      let role = \"cell\";\n      if (row.firstChild) {\n        role = row.firstChild.type.spec.tableRole;\n      }\n      const nodes = [];\n      for (let j = 0; j < add; j++) {\n        const node = tableNodeTypes(state.schema)[role].createAndFill();\n        if (node) nodes.push(node);\n      }\n      const side = (i == 0 || first == i - 1) && last == i ? pos + 1 : end - 1;\n      tr.insert(tr.mapping.map(side), nodes);\n    }\n    pos = end;\n  }\n  return tr.setMeta(fixTablesKey, { fixTables: true });\n}\n\n// src/input.ts\n\n\n\n\n// src/commands.ts\n\n\nfunction selectedRect(state) {\n  const sel = state.selection;\n  const $pos = selectionCell(state);\n  const table = $pos.node(-1);\n  const tableStart = $pos.start(-1);\n  const map = TableMap.get(table);\n  const rect = sel instanceof CellSelection ? map.rectBetween(\n    sel.$anchorCell.pos - tableStart,\n    sel.$headCell.pos - tableStart\n  ) : map.findCell($pos.pos - tableStart);\n  return { ...rect, tableStart, map, table };\n}\nfunction addColumn(tr, { map, tableStart, table }, col) {\n  let refColumn = col > 0 ? -1 : 0;\n  if (columnIsHeader(map, table, col + refColumn)) {\n    refColumn = col == 0 || col == map.width ? null : 0;\n  }\n  for (let row = 0; row < map.height; row++) {\n    const index = row * map.width + col;\n    if (col > 0 && col < map.width && map.map[index - 1] == map.map[index]) {\n      const pos = map.map[index];\n      const cell = table.nodeAt(pos);\n      tr.setNodeMarkup(\n        tr.mapping.map(tableStart + pos),\n        null,\n        addColSpan(cell.attrs, col - map.colCount(pos))\n      );\n      row += cell.attrs.rowspan - 1;\n    } else {\n      const type = refColumn == null ? tableNodeTypes(table.type.schema).cell : table.nodeAt(map.map[index + refColumn]).type;\n      const pos = map.positionAt(row, col, table);\n      tr.insert(tr.mapping.map(tableStart + pos), type.createAndFill());\n    }\n  }\n  return tr;\n}\nfunction addColumnBefore(state, dispatch) {\n  if (!isInTable(state)) return false;\n  if (dispatch) {\n    const rect = selectedRect(state);\n    dispatch(addColumn(state.tr, rect, rect.left));\n  }\n  return true;\n}\nfunction addColumnAfter(state, dispatch) {\n  if (!isInTable(state)) return false;\n  if (dispatch) {\n    const rect = selectedRect(state);\n    dispatch(addColumn(state.tr, rect, rect.right));\n  }\n  return true;\n}\nfunction removeColumn(tr, { map, table, tableStart }, col) {\n  const mapStart = tr.mapping.maps.length;\n  for (let row = 0; row < map.height; ) {\n    const index = row * map.width + col;\n    const pos = map.map[index];\n    const cell = table.nodeAt(pos);\n    const attrs = cell.attrs;\n    if (col > 0 && map.map[index - 1] == pos || col < map.width - 1 && map.map[index + 1] == pos) {\n      tr.setNodeMarkup(\n        tr.mapping.slice(mapStart).map(tableStart + pos),\n        null,\n        removeColSpan(attrs, col - map.colCount(pos))\n      );\n    } else {\n      const start = tr.mapping.slice(mapStart).map(tableStart + pos);\n      tr.delete(start, start + cell.nodeSize);\n    }\n    row += attrs.rowspan;\n  }\n}\nfunction deleteColumn(state, dispatch) {\n  if (!isInTable(state)) return false;\n  if (dispatch) {\n    const rect = selectedRect(state);\n    const tr = state.tr;\n    if (rect.left == 0 && rect.right == rect.map.width) return false;\n    for (let i = rect.right - 1; ; i--) {\n      removeColumn(tr, rect, i);\n      if (i == rect.left) break;\n      const table = rect.tableStart ? tr.doc.nodeAt(rect.tableStart - 1) : tr.doc;\n      if (!table) {\n        throw RangeError(\"No table found\");\n      }\n      rect.table = table;\n      rect.map = TableMap.get(table);\n    }\n    dispatch(tr);\n  }\n  return true;\n}\nfunction rowIsHeader(map, table, row) {\n  var _a;\n  const headerCell = tableNodeTypes(table.type.schema).header_cell;\n  for (let col = 0; col < map.width; col++)\n    if (((_a = table.nodeAt(map.map[col + row * map.width])) == null ? void 0 : _a.type) != headerCell)\n      return false;\n  return true;\n}\nfunction addRow(tr, { map, tableStart, table }, row) {\n  var _a;\n  let rowPos = tableStart;\n  for (let i = 0; i < row; i++) rowPos += table.child(i).nodeSize;\n  const cells = [];\n  let refRow = row > 0 ? -1 : 0;\n  if (rowIsHeader(map, table, row + refRow))\n    refRow = row == 0 || row == map.height ? null : 0;\n  for (let col = 0, index = map.width * row; col < map.width; col++, index++) {\n    if (row > 0 && row < map.height && map.map[index] == map.map[index - map.width]) {\n      const pos = map.map[index];\n      const attrs = table.nodeAt(pos).attrs;\n      tr.setNodeMarkup(tableStart + pos, null, {\n        ...attrs,\n        rowspan: attrs.rowspan + 1\n      });\n      col += attrs.colspan - 1;\n    } else {\n      const type = refRow == null ? tableNodeTypes(table.type.schema).cell : (_a = table.nodeAt(map.map[index + refRow * map.width])) == null ? void 0 : _a.type;\n      const node = type == null ? void 0 : type.createAndFill();\n      if (node) cells.push(node);\n    }\n  }\n  tr.insert(rowPos, tableNodeTypes(table.type.schema).row.create(null, cells));\n  return tr;\n}\nfunction addRowBefore(state, dispatch) {\n  if (!isInTable(state)) return false;\n  if (dispatch) {\n    const rect = selectedRect(state);\n    dispatch(addRow(state.tr, rect, rect.top));\n  }\n  return true;\n}\nfunction addRowAfter(state, dispatch) {\n  if (!isInTable(state)) return false;\n  if (dispatch) {\n    const rect = selectedRect(state);\n    dispatch(addRow(state.tr, rect, rect.bottom));\n  }\n  return true;\n}\nfunction removeRow(tr, { map, table, tableStart }, row) {\n  let rowPos = 0;\n  for (let i = 0; i < row; i++) rowPos += table.child(i).nodeSize;\n  const nextRow = rowPos + table.child(row).nodeSize;\n  const mapFrom = tr.mapping.maps.length;\n  tr.delete(rowPos + tableStart, nextRow + tableStart);\n  const seen = /* @__PURE__ */ new Set();\n  for (let col = 0, index = row * map.width; col < map.width; col++, index++) {\n    const pos = map.map[index];\n    if (seen.has(pos)) continue;\n    seen.add(pos);\n    if (row > 0 && pos == map.map[index - map.width]) {\n      const attrs = table.nodeAt(pos).attrs;\n      tr.setNodeMarkup(tr.mapping.slice(mapFrom).map(pos + tableStart), null, {\n        ...attrs,\n        rowspan: attrs.rowspan - 1\n      });\n      col += attrs.colspan - 1;\n    } else if (row < map.height && pos == map.map[index + map.width]) {\n      const cell = table.nodeAt(pos);\n      const attrs = cell.attrs;\n      const copy = cell.type.create(\n        { ...attrs, rowspan: cell.attrs.rowspan - 1 },\n        cell.content\n      );\n      const newPos = map.positionAt(row + 1, col, table);\n      tr.insert(tr.mapping.slice(mapFrom).map(tableStart + newPos), copy);\n      col += attrs.colspan - 1;\n    }\n  }\n}\nfunction deleteRow(state, dispatch) {\n  if (!isInTable(state)) return false;\n  if (dispatch) {\n    const rect = selectedRect(state), tr = state.tr;\n    if (rect.top == 0 && rect.bottom == rect.map.height) return false;\n    for (let i = rect.bottom - 1; ; i--) {\n      removeRow(tr, rect, i);\n      if (i == rect.top) break;\n      const table = rect.tableStart ? tr.doc.nodeAt(rect.tableStart - 1) : tr.doc;\n      if (!table) {\n        throw RangeError(\"No table found\");\n      }\n      rect.table = table;\n      rect.map = TableMap.get(rect.table);\n    }\n    dispatch(tr);\n  }\n  return true;\n}\nfunction isEmpty(cell) {\n  const c = cell.content;\n  return c.childCount == 1 && c.child(0).isTextblock && c.child(0).childCount == 0;\n}\nfunction cellsOverlapRectangle({ width, height, map }, rect) {\n  let indexTop = rect.top * width + rect.left, indexLeft = indexTop;\n  let indexBottom = (rect.bottom - 1) * width + rect.left, indexRight = indexTop + (rect.right - rect.left - 1);\n  for (let i = rect.top; i < rect.bottom; i++) {\n    if (rect.left > 0 && map[indexLeft] == map[indexLeft - 1] || rect.right < width && map[indexRight] == map[indexRight + 1])\n      return true;\n    indexLeft += width;\n    indexRight += width;\n  }\n  for (let i = rect.left; i < rect.right; i++) {\n    if (rect.top > 0 && map[indexTop] == map[indexTop - width] || rect.bottom < height && map[indexBottom] == map[indexBottom + width])\n      return true;\n    indexTop++;\n    indexBottom++;\n  }\n  return false;\n}\nfunction mergeCells(state, dispatch) {\n  const sel = state.selection;\n  if (!(sel instanceof CellSelection) || sel.$anchorCell.pos == sel.$headCell.pos)\n    return false;\n  const rect = selectedRect(state), { map } = rect;\n  if (cellsOverlapRectangle(map, rect)) return false;\n  if (dispatch) {\n    const tr = state.tr;\n    const seen = {};\n    let content = prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.empty;\n    let mergedPos;\n    let mergedCell;\n    for (let row = rect.top; row < rect.bottom; row++) {\n      for (let col = rect.left; col < rect.right; col++) {\n        const cellPos = map.map[row * map.width + col];\n        const cell = rect.table.nodeAt(cellPos);\n        if (seen[cellPos] || !cell) continue;\n        seen[cellPos] = true;\n        if (mergedPos == null) {\n          mergedPos = cellPos;\n          mergedCell = cell;\n        } else {\n          if (!isEmpty(cell)) content = content.append(cell.content);\n          const mapped = tr.mapping.map(cellPos + rect.tableStart);\n          tr.delete(mapped, mapped + cell.nodeSize);\n        }\n      }\n    }\n    if (mergedPos == null || mergedCell == null) {\n      return true;\n    }\n    tr.setNodeMarkup(mergedPos + rect.tableStart, null, {\n      ...addColSpan(\n        mergedCell.attrs,\n        mergedCell.attrs.colspan,\n        rect.right - rect.left - mergedCell.attrs.colspan\n      ),\n      rowspan: rect.bottom - rect.top\n    });\n    if (content.size) {\n      const end = mergedPos + 1 + mergedCell.content.size;\n      const start = isEmpty(mergedCell) ? mergedPos + 1 : end;\n      tr.replaceWith(start + rect.tableStart, end + rect.tableStart, content);\n    }\n    tr.setSelection(\n      new CellSelection(tr.doc.resolve(mergedPos + rect.tableStart))\n    );\n    dispatch(tr);\n  }\n  return true;\n}\nfunction splitCell(state, dispatch) {\n  const nodeTypes = tableNodeTypes(state.schema);\n  return splitCellWithType(({ node }) => {\n    return nodeTypes[node.type.spec.tableRole];\n  })(state, dispatch);\n}\nfunction splitCellWithType(getCellType) {\n  return (state, dispatch) => {\n    var _a;\n    const sel = state.selection;\n    let cellNode;\n    let cellPos;\n    if (!(sel instanceof CellSelection)) {\n      cellNode = cellWrapping(sel.$from);\n      if (!cellNode) return false;\n      cellPos = (_a = cellAround(sel.$from)) == null ? void 0 : _a.pos;\n    } else {\n      if (sel.$anchorCell.pos != sel.$headCell.pos) return false;\n      cellNode = sel.$anchorCell.nodeAfter;\n      cellPos = sel.$anchorCell.pos;\n    }\n    if (cellNode == null || cellPos == null) {\n      return false;\n    }\n    if (cellNode.attrs.colspan == 1 && cellNode.attrs.rowspan == 1) {\n      return false;\n    }\n    if (dispatch) {\n      let baseAttrs = cellNode.attrs;\n      const attrs = [];\n      const colwidth = baseAttrs.colwidth;\n      if (baseAttrs.rowspan > 1) baseAttrs = { ...baseAttrs, rowspan: 1 };\n      if (baseAttrs.colspan > 1) baseAttrs = { ...baseAttrs, colspan: 1 };\n      const rect = selectedRect(state), tr = state.tr;\n      for (let i = 0; i < rect.right - rect.left; i++)\n        attrs.push(\n          colwidth ? {\n            ...baseAttrs,\n            colwidth: colwidth && colwidth[i] ? [colwidth[i]] : null\n          } : baseAttrs\n        );\n      let lastCell;\n      for (let row = rect.top; row < rect.bottom; row++) {\n        let pos = rect.map.positionAt(row, rect.left, rect.table);\n        if (row == rect.top) pos += cellNode.nodeSize;\n        for (let col = rect.left, i = 0; col < rect.right; col++, i++) {\n          if (col == rect.left && row == rect.top) continue;\n          tr.insert(\n            lastCell = tr.mapping.map(pos + rect.tableStart, 1),\n            getCellType({ node: cellNode, row, col }).createAndFill(attrs[i])\n          );\n        }\n      }\n      tr.setNodeMarkup(\n        cellPos,\n        getCellType({ node: cellNode, row: rect.top, col: rect.left }),\n        attrs[0]\n      );\n      if (sel instanceof CellSelection)\n        tr.setSelection(\n          new CellSelection(\n            tr.doc.resolve(sel.$anchorCell.pos),\n            lastCell ? tr.doc.resolve(lastCell) : void 0\n          )\n        );\n      dispatch(tr);\n    }\n    return true;\n  };\n}\nfunction setCellAttr(name, value) {\n  return function(state, dispatch) {\n    if (!isInTable(state)) return false;\n    const $cell = selectionCell(state);\n    if ($cell.nodeAfter.attrs[name] === value) return false;\n    if (dispatch) {\n      const tr = state.tr;\n      if (state.selection instanceof CellSelection)\n        state.selection.forEachCell((node, pos) => {\n          if (node.attrs[name] !== value)\n            tr.setNodeMarkup(pos, null, {\n              ...node.attrs,\n              [name]: value\n            });\n        });\n      else\n        tr.setNodeMarkup($cell.pos, null, {\n          ...$cell.nodeAfter.attrs,\n          [name]: value\n        });\n      dispatch(tr);\n    }\n    return true;\n  };\n}\nfunction deprecated_toggleHeader(type) {\n  return function(state, dispatch) {\n    if (!isInTable(state)) return false;\n    if (dispatch) {\n      const types = tableNodeTypes(state.schema);\n      const rect = selectedRect(state), tr = state.tr;\n      const cells = rect.map.cellsInRect(\n        type == \"column\" ? {\n          left: rect.left,\n          top: 0,\n          right: rect.right,\n          bottom: rect.map.height\n        } : type == \"row\" ? {\n          left: 0,\n          top: rect.top,\n          right: rect.map.width,\n          bottom: rect.bottom\n        } : rect\n      );\n      const nodes = cells.map((pos) => rect.table.nodeAt(pos));\n      for (let i = 0; i < cells.length; i++)\n        if (nodes[i].type == types.header_cell)\n          tr.setNodeMarkup(\n            rect.tableStart + cells[i],\n            types.cell,\n            nodes[i].attrs\n          );\n      if (tr.steps.length == 0)\n        for (let i = 0; i < cells.length; i++)\n          tr.setNodeMarkup(\n            rect.tableStart + cells[i],\n            types.header_cell,\n            nodes[i].attrs\n          );\n      dispatch(tr);\n    }\n    return true;\n  };\n}\nfunction isHeaderEnabledByType(type, rect, types) {\n  const cellPositions = rect.map.cellsInRect({\n    left: 0,\n    top: 0,\n    right: type == \"row\" ? rect.map.width : 1,\n    bottom: type == \"column\" ? rect.map.height : 1\n  });\n  for (let i = 0; i < cellPositions.length; i++) {\n    const cell = rect.table.nodeAt(cellPositions[i]);\n    if (cell && cell.type !== types.header_cell) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction toggleHeader(type, options) {\n  options = options || { useDeprecatedLogic: false };\n  if (options.useDeprecatedLogic) return deprecated_toggleHeader(type);\n  return function(state, dispatch) {\n    if (!isInTable(state)) return false;\n    if (dispatch) {\n      const types = tableNodeTypes(state.schema);\n      const rect = selectedRect(state), tr = state.tr;\n      const isHeaderRowEnabled = isHeaderEnabledByType(\"row\", rect, types);\n      const isHeaderColumnEnabled = isHeaderEnabledByType(\n        \"column\",\n        rect,\n        types\n      );\n      const isHeaderEnabled = type === \"column\" ? isHeaderRowEnabled : type === \"row\" ? isHeaderColumnEnabled : false;\n      const selectionStartsAt = isHeaderEnabled ? 1 : 0;\n      const cellsRect = type == \"column\" ? {\n        left: 0,\n        top: selectionStartsAt,\n        right: 1,\n        bottom: rect.map.height\n      } : type == \"row\" ? {\n        left: selectionStartsAt,\n        top: 0,\n        right: rect.map.width,\n        bottom: 1\n      } : rect;\n      const newType = type == \"column\" ? isHeaderColumnEnabled ? types.cell : types.header_cell : type == \"row\" ? isHeaderRowEnabled ? types.cell : types.header_cell : types.cell;\n      rect.map.cellsInRect(cellsRect).forEach((relativeCellPos) => {\n        const cellPos = relativeCellPos + rect.tableStart;\n        const cell = tr.doc.nodeAt(cellPos);\n        if (cell) {\n          tr.setNodeMarkup(cellPos, newType, cell.attrs);\n        }\n      });\n      dispatch(tr);\n    }\n    return true;\n  };\n}\nvar toggleHeaderRow = toggleHeader(\"row\", {\n  useDeprecatedLogic: true\n});\nvar toggleHeaderColumn = toggleHeader(\"column\", {\n  useDeprecatedLogic: true\n});\nvar toggleHeaderCell = toggleHeader(\"cell\", {\n  useDeprecatedLogic: true\n});\nfunction findNextCell($cell, dir) {\n  if (dir < 0) {\n    const before = $cell.nodeBefore;\n    if (before) return $cell.pos - before.nodeSize;\n    for (let row = $cell.index(-1) - 1, rowEnd = $cell.before(); row >= 0; row--) {\n      const rowNode = $cell.node(-1).child(row);\n      const lastChild = rowNode.lastChild;\n      if (lastChild) {\n        return rowEnd - 1 - lastChild.nodeSize;\n      }\n      rowEnd -= rowNode.nodeSize;\n    }\n  } else {\n    if ($cell.index() < $cell.parent.childCount - 1) {\n      return $cell.pos + $cell.nodeAfter.nodeSize;\n    }\n    const table = $cell.node(-1);\n    for (let row = $cell.indexAfter(-1), rowStart = $cell.after(); row < table.childCount; row++) {\n      const rowNode = table.child(row);\n      if (rowNode.childCount) return rowStart + 1;\n      rowStart += rowNode.nodeSize;\n    }\n  }\n  return null;\n}\nfunction goToNextCell(direction) {\n  return function(state, dispatch) {\n    if (!isInTable(state)) return false;\n    const cell = findNextCell(selectionCell(state), direction);\n    if (cell == null) return false;\n    if (dispatch) {\n      const $cell = state.doc.resolve(cell);\n      dispatch(\n        state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.between($cell, moveCellForward($cell))).scrollIntoView()\n      );\n    }\n    return true;\n  };\n}\nfunction deleteTable(state, dispatch) {\n  const $pos = state.selection.$anchor;\n  for (let d = $pos.depth; d > 0; d--) {\n    const node = $pos.node(d);\n    if (node.type.spec.tableRole == \"table\") {\n      if (dispatch)\n        dispatch(\n          state.tr.delete($pos.before(d), $pos.after(d)).scrollIntoView()\n        );\n      return true;\n    }\n  }\n  return false;\n}\nfunction deleteCellSelection(state, dispatch) {\n  const sel = state.selection;\n  if (!(sel instanceof CellSelection)) return false;\n  if (dispatch) {\n    const tr = state.tr;\n    const baseContent = tableNodeTypes(state.schema).cell.createAndFill().content;\n    sel.forEachCell((cell, pos) => {\n      if (!cell.content.eq(baseContent))\n        tr.replace(\n          tr.mapping.map(pos + 1),\n          tr.mapping.map(pos + cell.nodeSize - 1),\n          new prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice(baseContent, 0, 0)\n        );\n    });\n    if (tr.docChanged) dispatch(tr);\n  }\n  return true;\n}\n\n// src/copypaste.ts\n\n\nfunction pastedCells(slice) {\n  if (!slice.size) return null;\n  let { content, openStart, openEnd } = slice;\n  while (content.childCount == 1 && (openStart > 0 && openEnd > 0 || content.child(0).type.spec.tableRole == \"table\")) {\n    openStart--;\n    openEnd--;\n    content = content.child(0).content;\n  }\n  const first = content.child(0);\n  const role = first.type.spec.tableRole;\n  const schema = first.type.schema, rows = [];\n  if (role == \"row\") {\n    for (let i = 0; i < content.childCount; i++) {\n      let cells = content.child(i).content;\n      const left = i ? 0 : Math.max(0, openStart - 1);\n      const right = i < content.childCount - 1 ? 0 : Math.max(0, openEnd - 1);\n      if (left || right)\n        cells = fitSlice(\n          tableNodeTypes(schema).row,\n          new prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice(cells, left, right)\n        ).content;\n      rows.push(cells);\n    }\n  } else if (role == \"cell\" || role == \"header_cell\") {\n    rows.push(\n      openStart || openEnd ? fitSlice(\n        tableNodeTypes(schema).row,\n        new prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice(content, openStart, openEnd)\n      ).content : content\n    );\n  } else {\n    return null;\n  }\n  return ensureRectangular(schema, rows);\n}\nfunction ensureRectangular(schema, rows) {\n  const widths = [];\n  for (let i = 0; i < rows.length; i++) {\n    const row = rows[i];\n    for (let j = row.childCount - 1; j >= 0; j--) {\n      const { rowspan, colspan } = row.child(j).attrs;\n      for (let r = i; r < i + rowspan; r++)\n        widths[r] = (widths[r] || 0) + colspan;\n    }\n  }\n  let width = 0;\n  for (let r = 0; r < widths.length; r++) width = Math.max(width, widths[r]);\n  for (let r = 0; r < widths.length; r++) {\n    if (r >= rows.length) rows.push(prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.empty);\n    if (widths[r] < width) {\n      const empty = tableNodeTypes(schema).cell.createAndFill();\n      const cells = [];\n      for (let i = widths[r]; i < width; i++) {\n        cells.push(empty);\n      }\n      rows[r] = rows[r].append(prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(cells));\n    }\n  }\n  return { height: rows.length, width, rows };\n}\nfunction fitSlice(nodeType, slice) {\n  const node = nodeType.createAndFill();\n  const tr = new prosemirror_transform__WEBPACK_IMPORTED_MODULE_3__.Transform(node).replace(0, node.content.size, slice);\n  return tr.doc;\n}\nfunction clipCells({ width, height, rows }, newWidth, newHeight) {\n  if (width != newWidth) {\n    const added = [];\n    const newRows = [];\n    for (let row = 0; row < rows.length; row++) {\n      const frag = rows[row], cells = [];\n      for (let col = added[row] || 0, i = 0; col < newWidth; i++) {\n        let cell = frag.child(i % frag.childCount);\n        if (col + cell.attrs.colspan > newWidth)\n          cell = cell.type.createChecked(\n            removeColSpan(\n              cell.attrs,\n              cell.attrs.colspan,\n              col + cell.attrs.colspan - newWidth\n            ),\n            cell.content\n          );\n        cells.push(cell);\n        col += cell.attrs.colspan;\n        for (let j = 1; j < cell.attrs.rowspan; j++)\n          added[row + j] = (added[row + j] || 0) + cell.attrs.colspan;\n      }\n      newRows.push(prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(cells));\n    }\n    rows = newRows;\n    width = newWidth;\n  }\n  if (height != newHeight) {\n    const newRows = [];\n    for (let row = 0, i = 0; row < newHeight; row++, i++) {\n      const cells = [], source = rows[i % height];\n      for (let j = 0; j < source.childCount; j++) {\n        let cell = source.child(j);\n        if (row + cell.attrs.rowspan > newHeight)\n          cell = cell.type.create(\n            {\n              ...cell.attrs,\n              rowspan: Math.max(1, newHeight - cell.attrs.rowspan)\n            },\n            cell.content\n          );\n        cells.push(cell);\n      }\n      newRows.push(prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(cells));\n    }\n    rows = newRows;\n    height = newHeight;\n  }\n  return { width, height, rows };\n}\nfunction growTable(tr, map, table, start, width, height, mapFrom) {\n  const schema = tr.doc.type.schema;\n  const types = tableNodeTypes(schema);\n  let empty;\n  let emptyHead;\n  if (width > map.width) {\n    for (let row = 0, rowEnd = 0; row < map.height; row++) {\n      const rowNode = table.child(row);\n      rowEnd += rowNode.nodeSize;\n      const cells = [];\n      let add;\n      if (rowNode.lastChild == null || rowNode.lastChild.type == types.cell)\n        add = empty || (empty = types.cell.createAndFill());\n      else add = emptyHead || (emptyHead = types.header_cell.createAndFill());\n      for (let i = map.width; i < width; i++) cells.push(add);\n      tr.insert(tr.mapping.slice(mapFrom).map(rowEnd - 1 + start), cells);\n    }\n  }\n  if (height > map.height) {\n    const cells = [];\n    for (let i = 0, start2 = (map.height - 1) * map.width; i < Math.max(map.width, width); i++) {\n      const header = i >= map.width ? false : table.nodeAt(map.map[start2 + i]).type == types.header_cell;\n      cells.push(\n        header ? emptyHead || (emptyHead = types.header_cell.createAndFill()) : empty || (empty = types.cell.createAndFill())\n      );\n    }\n    const emptyRow = types.row.create(null, prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(cells)), rows = [];\n    for (let i = map.height; i < height; i++) rows.push(emptyRow);\n    tr.insert(tr.mapping.slice(mapFrom).map(start + table.nodeSize - 2), rows);\n  }\n  return !!(empty || emptyHead);\n}\nfunction isolateHorizontal(tr, map, table, start, left, right, top, mapFrom) {\n  if (top == 0 || top == map.height) return false;\n  let found = false;\n  for (let col = left; col < right; col++) {\n    const index = top * map.width + col, pos = map.map[index];\n    if (map.map[index - map.width] == pos) {\n      found = true;\n      const cell = table.nodeAt(pos);\n      const { top: cellTop, left: cellLeft } = map.findCell(pos);\n      tr.setNodeMarkup(tr.mapping.slice(mapFrom).map(pos + start), null, {\n        ...cell.attrs,\n        rowspan: top - cellTop\n      });\n      tr.insert(\n        tr.mapping.slice(mapFrom).map(map.positionAt(top, cellLeft, table)),\n        cell.type.createAndFill({\n          ...cell.attrs,\n          rowspan: cellTop + cell.attrs.rowspan - top\n        })\n      );\n      col += cell.attrs.colspan - 1;\n    }\n  }\n  return found;\n}\nfunction isolateVertical(tr, map, table, start, top, bottom, left, mapFrom) {\n  if (left == 0 || left == map.width) return false;\n  let found = false;\n  for (let row = top; row < bottom; row++) {\n    const index = row * map.width + left, pos = map.map[index];\n    if (map.map[index - 1] == pos) {\n      found = true;\n      const cell = table.nodeAt(pos);\n      const cellLeft = map.colCount(pos);\n      const updatePos = tr.mapping.slice(mapFrom).map(pos + start);\n      tr.setNodeMarkup(\n        updatePos,\n        null,\n        removeColSpan(\n          cell.attrs,\n          left - cellLeft,\n          cell.attrs.colspan - (left - cellLeft)\n        )\n      );\n      tr.insert(\n        updatePos + cell.nodeSize,\n        cell.type.createAndFill(\n          removeColSpan(cell.attrs, 0, left - cellLeft)\n        )\n      );\n      row += cell.attrs.rowspan - 1;\n    }\n  }\n  return found;\n}\nfunction insertCells(state, dispatch, tableStart, rect, cells) {\n  let table = tableStart ? state.doc.nodeAt(tableStart - 1) : state.doc;\n  if (!table) {\n    throw new Error(\"No table found\");\n  }\n  let map = TableMap.get(table);\n  const { top, left } = rect;\n  const right = left + cells.width, bottom = top + cells.height;\n  const tr = state.tr;\n  let mapFrom = 0;\n  function recomp() {\n    table = tableStart ? tr.doc.nodeAt(tableStart - 1) : tr.doc;\n    if (!table) {\n      throw new Error(\"No table found\");\n    }\n    map = TableMap.get(table);\n    mapFrom = tr.mapping.maps.length;\n  }\n  if (growTable(tr, map, table, tableStart, right, bottom, mapFrom)) recomp();\n  if (isolateHorizontal(tr, map, table, tableStart, left, right, top, mapFrom))\n    recomp();\n  if (isolateHorizontal(tr, map, table, tableStart, left, right, bottom, mapFrom))\n    recomp();\n  if (isolateVertical(tr, map, table, tableStart, top, bottom, left, mapFrom))\n    recomp();\n  if (isolateVertical(tr, map, table, tableStart, top, bottom, right, mapFrom))\n    recomp();\n  for (let row = top; row < bottom; row++) {\n    const from = map.positionAt(row, left, table), to = map.positionAt(row, right, table);\n    tr.replace(\n      tr.mapping.slice(mapFrom).map(from + tableStart),\n      tr.mapping.slice(mapFrom).map(to + tableStart),\n      new prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice(cells.rows[row - top], 0, 0)\n    );\n  }\n  recomp();\n  tr.setSelection(\n    new CellSelection(\n      tr.doc.resolve(tableStart + map.positionAt(top, left, table)),\n      tr.doc.resolve(tableStart + map.positionAt(bottom - 1, right - 1, table))\n    )\n  );\n  dispatch(tr);\n}\n\n// src/input.ts\nvar handleKeyDown = (0,prosemirror_keymap__WEBPACK_IMPORTED_MODULE_4__.keydownHandler)({\n  ArrowLeft: arrow(\"horiz\", -1),\n  ArrowRight: arrow(\"horiz\", 1),\n  ArrowUp: arrow(\"vert\", -1),\n  ArrowDown: arrow(\"vert\", 1),\n  \"Shift-ArrowLeft\": shiftArrow(\"horiz\", -1),\n  \"Shift-ArrowRight\": shiftArrow(\"horiz\", 1),\n  \"Shift-ArrowUp\": shiftArrow(\"vert\", -1),\n  \"Shift-ArrowDown\": shiftArrow(\"vert\", 1),\n  Backspace: deleteCellSelection,\n  \"Mod-Backspace\": deleteCellSelection,\n  Delete: deleteCellSelection,\n  \"Mod-Delete\": deleteCellSelection\n});\nfunction maybeSetSelection(state, dispatch, selection) {\n  if (selection.eq(state.selection)) return false;\n  if (dispatch) dispatch(state.tr.setSelection(selection).scrollIntoView());\n  return true;\n}\nfunction arrow(axis, dir) {\n  return (state, dispatch, view) => {\n    if (!view) return false;\n    const sel = state.selection;\n    if (sel instanceof CellSelection) {\n      return maybeSetSelection(\n        state,\n        dispatch,\n        prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.near(sel.$headCell, dir)\n      );\n    }\n    if (axis != \"horiz\" && !sel.empty) return false;\n    const end = atEndOfCell(view, axis, dir);\n    if (end == null) return false;\n    if (axis == \"horiz\") {\n      return maybeSetSelection(\n        state,\n        dispatch,\n        prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.near(state.doc.resolve(sel.head + dir), dir)\n      );\n    } else {\n      const $cell = state.doc.resolve(end);\n      const $next = nextCell($cell, axis, dir);\n      let newSel;\n      if ($next) newSel = prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.near($next, 1);\n      else if (dir < 0)\n        newSel = prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.near(state.doc.resolve($cell.before(-1)), -1);\n      else newSel = prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.near(state.doc.resolve($cell.after(-1)), 1);\n      return maybeSetSelection(state, dispatch, newSel);\n    }\n  };\n}\nfunction shiftArrow(axis, dir) {\n  return (state, dispatch, view) => {\n    if (!view) return false;\n    const sel = state.selection;\n    let cellSel;\n    if (sel instanceof CellSelection) {\n      cellSel = sel;\n    } else {\n      const end = atEndOfCell(view, axis, dir);\n      if (end == null) return false;\n      cellSel = new CellSelection(state.doc.resolve(end));\n    }\n    const $head = nextCell(cellSel.$headCell, axis, dir);\n    if (!$head) return false;\n    return maybeSetSelection(\n      state,\n      dispatch,\n      new CellSelection(cellSel.$anchorCell, $head)\n    );\n  };\n}\nfunction handleTripleClick(view, pos) {\n  const doc = view.state.doc, $cell = cellAround(doc.resolve(pos));\n  if (!$cell) return false;\n  view.dispatch(view.state.tr.setSelection(new CellSelection($cell)));\n  return true;\n}\nfunction handlePaste(view, _, slice) {\n  if (!isInTable(view.state)) return false;\n  let cells = pastedCells(slice);\n  const sel = view.state.selection;\n  if (sel instanceof CellSelection) {\n    if (!cells)\n      cells = {\n        width: 1,\n        height: 1,\n        rows: [\n          prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(\n            fitSlice(tableNodeTypes(view.state.schema).cell, slice)\n          )\n        ]\n      };\n    const table = sel.$anchorCell.node(-1);\n    const start = sel.$anchorCell.start(-1);\n    const rect = TableMap.get(table).rectBetween(\n      sel.$anchorCell.pos - start,\n      sel.$headCell.pos - start\n    );\n    cells = clipCells(cells, rect.right - rect.left, rect.bottom - rect.top);\n    insertCells(view.state, view.dispatch, start, rect, cells);\n    return true;\n  } else if (cells) {\n    const $cell = selectionCell(view.state);\n    const start = $cell.start(-1);\n    insertCells(\n      view.state,\n      view.dispatch,\n      start,\n      TableMap.get($cell.node(-1)).findCell($cell.pos - start),\n      cells\n    );\n    return true;\n  } else {\n    return false;\n  }\n}\nfunction handleMouseDown(view, startEvent) {\n  var _a;\n  if (startEvent.ctrlKey || startEvent.metaKey) return;\n  const startDOMCell = domInCell(view, startEvent.target);\n  let $anchor;\n  if (startEvent.shiftKey && view.state.selection instanceof CellSelection) {\n    setCellSelection(view.state.selection.$anchorCell, startEvent);\n    startEvent.preventDefault();\n  } else if (startEvent.shiftKey && startDOMCell && ($anchor = cellAround(view.state.selection.$anchor)) != null && ((_a = cellUnderMouse(view, startEvent)) == null ? void 0 : _a.pos) != $anchor.pos) {\n    setCellSelection($anchor, startEvent);\n    startEvent.preventDefault();\n  } else if (!startDOMCell) {\n    return;\n  }\n  function setCellSelection($anchor2, event) {\n    let $head = cellUnderMouse(view, event);\n    const starting = tableEditingKey.getState(view.state) == null;\n    if (!$head || !inSameTable($anchor2, $head)) {\n      if (starting) $head = $anchor2;\n      else return;\n    }\n    const selection = new CellSelection($anchor2, $head);\n    if (starting || !view.state.selection.eq(selection)) {\n      const tr = view.state.tr.setSelection(selection);\n      if (starting) tr.setMeta(tableEditingKey, $anchor2.pos);\n      view.dispatch(tr);\n    }\n  }\n  function stop() {\n    view.root.removeEventListener(\"mouseup\", stop);\n    view.root.removeEventListener(\"dragstart\", stop);\n    view.root.removeEventListener(\"mousemove\", move);\n    if (tableEditingKey.getState(view.state) != null)\n      view.dispatch(view.state.tr.setMeta(tableEditingKey, -1));\n  }\n  function move(_event) {\n    const event = _event;\n    const anchor = tableEditingKey.getState(view.state);\n    let $anchor2;\n    if (anchor != null) {\n      $anchor2 = view.state.doc.resolve(anchor);\n    } else if (domInCell(view, event.target) != startDOMCell) {\n      $anchor2 = cellUnderMouse(view, startEvent);\n      if (!$anchor2) return stop();\n    }\n    if ($anchor2) setCellSelection($anchor2, event);\n  }\n  view.root.addEventListener(\"mouseup\", stop);\n  view.root.addEventListener(\"dragstart\", stop);\n  view.root.addEventListener(\"mousemove\", move);\n}\nfunction atEndOfCell(view, axis, dir) {\n  if (!(view.state.selection instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection)) return null;\n  const { $head } = view.state.selection;\n  for (let d = $head.depth - 1; d >= 0; d--) {\n    const parent = $head.node(d), index = dir < 0 ? $head.index(d) : $head.indexAfter(d);\n    if (index != (dir < 0 ? 0 : parent.childCount)) return null;\n    if (parent.type.spec.tableRole == \"cell\" || parent.type.spec.tableRole == \"header_cell\") {\n      const cellPos = $head.before(d);\n      const dirStr = axis == \"vert\" ? dir > 0 ? \"down\" : \"up\" : dir > 0 ? \"right\" : \"left\";\n      return view.endOfTextblock(dirStr) ? cellPos : null;\n    }\n  }\n  return null;\n}\nfunction domInCell(view, dom) {\n  for (; dom && dom != view.dom; dom = dom.parentNode) {\n    if (dom.nodeName == \"TD\" || dom.nodeName == \"TH\") {\n      return dom;\n    }\n  }\n  return null;\n}\nfunction cellUnderMouse(view, event) {\n  const mousePos = view.posAtCoords({\n    left: event.clientX,\n    top: event.clientY\n  });\n  if (!mousePos) return null;\n  return mousePos ? cellAround(view.state.doc.resolve(mousePos.pos)) : null;\n}\n\n// src/columnresizing.ts\n\n\n\n// src/tableview.ts\nvar TableView = class {\n  constructor(node, defaultCellMinWidth) {\n    this.node = node;\n    this.defaultCellMinWidth = defaultCellMinWidth;\n    this.dom = document.createElement(\"div\");\n    this.dom.className = \"tableWrapper\";\n    this.table = this.dom.appendChild(document.createElement(\"table\"));\n    this.table.style.setProperty(\n      \"--default-cell-min-width\",\n      `${defaultCellMinWidth}px`\n    );\n    this.colgroup = this.table.appendChild(document.createElement(\"colgroup\"));\n    updateColumnsOnResize(node, this.colgroup, this.table, defaultCellMinWidth);\n    this.contentDOM = this.table.appendChild(document.createElement(\"tbody\"));\n  }\n  update(node) {\n    if (node.type != this.node.type) return false;\n    this.node = node;\n    updateColumnsOnResize(\n      node,\n      this.colgroup,\n      this.table,\n      this.defaultCellMinWidth\n    );\n    return true;\n  }\n  ignoreMutation(record) {\n    return record.type == \"attributes\" && (record.target == this.table || this.colgroup.contains(record.target));\n  }\n};\nfunction updateColumnsOnResize(node, colgroup, table, defaultCellMinWidth, overrideCol, overrideValue) {\n  var _a;\n  let totalWidth = 0;\n  let fixedWidth = true;\n  let nextDOM = colgroup.firstChild;\n  const row = node.firstChild;\n  if (!row) return;\n  for (let i = 0, col = 0; i < row.childCount; i++) {\n    const { colspan, colwidth } = row.child(i).attrs;\n    for (let j = 0; j < colspan; j++, col++) {\n      const hasWidth = overrideCol == col ? overrideValue : colwidth && colwidth[j];\n      const cssWidth = hasWidth ? hasWidth + \"px\" : \"\";\n      totalWidth += hasWidth || defaultCellMinWidth;\n      if (!hasWidth) fixedWidth = false;\n      if (!nextDOM) {\n        const col2 = document.createElement(\"col\");\n        col2.style.width = cssWidth;\n        colgroup.appendChild(col2);\n      } else {\n        if (nextDOM.style.width != cssWidth) {\n          nextDOM.style.width = cssWidth;\n        }\n        nextDOM = nextDOM.nextSibling;\n      }\n    }\n  }\n  while (nextDOM) {\n    const after = nextDOM.nextSibling;\n    (_a = nextDOM.parentNode) == null ? void 0 : _a.removeChild(nextDOM);\n    nextDOM = after;\n  }\n  if (fixedWidth) {\n    table.style.width = totalWidth + \"px\";\n    table.style.minWidth = \"\";\n  } else {\n    table.style.width = \"\";\n    table.style.minWidth = totalWidth + \"px\";\n  }\n}\n\n// src/columnresizing.ts\nvar columnResizingPluginKey = new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey(\n  \"tableColumnResizing\"\n);\nfunction columnResizing({\n  handleWidth = 5,\n  cellMinWidth = 25,\n  defaultCellMinWidth = 100,\n  View = TableView,\n  lastColumnResizable = true\n} = {}) {\n  const plugin = new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n    key: columnResizingPluginKey,\n    state: {\n      init(_, state) {\n        var _a, _b;\n        const nodeViews = (_b = (_a = plugin.spec) == null ? void 0 : _a.props) == null ? void 0 : _b.nodeViews;\n        const tableName = tableNodeTypes(state.schema).table.name;\n        if (View && nodeViews) {\n          nodeViews[tableName] = (node, view) => {\n            return new View(node, defaultCellMinWidth, view);\n          };\n        }\n        return new ResizeState(-1, false);\n      },\n      apply(tr, prev) {\n        return prev.apply(tr);\n      }\n    },\n    props: {\n      attributes: (state) => {\n        const pluginState = columnResizingPluginKey.getState(state);\n        return pluginState && pluginState.activeHandle > -1 ? { class: \"resize-cursor\" } : {};\n      },\n      handleDOMEvents: {\n        mousemove: (view, event) => {\n          handleMouseMove(view, event, handleWidth, lastColumnResizable);\n        },\n        mouseleave: (view) => {\n          handleMouseLeave(view);\n        },\n        mousedown: (view, event) => {\n          handleMouseDown2(view, event, cellMinWidth, defaultCellMinWidth);\n        }\n      },\n      decorations: (state) => {\n        const pluginState = columnResizingPluginKey.getState(state);\n        if (pluginState && pluginState.activeHandle > -1) {\n          return handleDecorations(state, pluginState.activeHandle);\n        }\n      },\n      nodeViews: {}\n    }\n  });\n  return plugin;\n}\nvar ResizeState = class _ResizeState {\n  constructor(activeHandle, dragging) {\n    this.activeHandle = activeHandle;\n    this.dragging = dragging;\n  }\n  apply(tr) {\n    const state = this;\n    const action = tr.getMeta(columnResizingPluginKey);\n    if (action && action.setHandle != null)\n      return new _ResizeState(action.setHandle, false);\n    if (action && action.setDragging !== void 0)\n      return new _ResizeState(state.activeHandle, action.setDragging);\n    if (state.activeHandle > -1 && tr.docChanged) {\n      let handle = tr.mapping.map(state.activeHandle, -1);\n      if (!pointsAtCell(tr.doc.resolve(handle))) {\n        handle = -1;\n      }\n      return new _ResizeState(handle, state.dragging);\n    }\n    return state;\n  }\n};\nfunction handleMouseMove(view, event, handleWidth, lastColumnResizable) {\n  if (!view.editable) return;\n  const pluginState = columnResizingPluginKey.getState(view.state);\n  if (!pluginState) return;\n  if (!pluginState.dragging) {\n    const target = domCellAround(event.target);\n    let cell = -1;\n    if (target) {\n      const { left, right } = target.getBoundingClientRect();\n      if (event.clientX - left <= handleWidth)\n        cell = edgeCell(view, event, \"left\", handleWidth);\n      else if (right - event.clientX <= handleWidth)\n        cell = edgeCell(view, event, \"right\", handleWidth);\n    }\n    if (cell != pluginState.activeHandle) {\n      if (!lastColumnResizable && cell !== -1) {\n        const $cell = view.state.doc.resolve(cell);\n        const table = $cell.node(-1);\n        const map = TableMap.get(table);\n        const tableStart = $cell.start(-1);\n        const col = map.colCount($cell.pos - tableStart) + $cell.nodeAfter.attrs.colspan - 1;\n        if (col == map.width - 1) {\n          return;\n        }\n      }\n      updateHandle(view, cell);\n    }\n  }\n}\nfunction handleMouseLeave(view) {\n  if (!view.editable) return;\n  const pluginState = columnResizingPluginKey.getState(view.state);\n  if (pluginState && pluginState.activeHandle > -1 && !pluginState.dragging)\n    updateHandle(view, -1);\n}\nfunction handleMouseDown2(view, event, cellMinWidth, defaultCellMinWidth) {\n  var _a;\n  if (!view.editable) return false;\n  const win = (_a = view.dom.ownerDocument.defaultView) != null ? _a : window;\n  const pluginState = columnResizingPluginKey.getState(view.state);\n  if (!pluginState || pluginState.activeHandle == -1 || pluginState.dragging)\n    return false;\n  const cell = view.state.doc.nodeAt(pluginState.activeHandle);\n  const width = currentColWidth(view, pluginState.activeHandle, cell.attrs);\n  view.dispatch(\n    view.state.tr.setMeta(columnResizingPluginKey, {\n      setDragging: { startX: event.clientX, startWidth: width }\n    })\n  );\n  function finish(event2) {\n    win.removeEventListener(\"mouseup\", finish);\n    win.removeEventListener(\"mousemove\", move);\n    const pluginState2 = columnResizingPluginKey.getState(view.state);\n    if (pluginState2 == null ? void 0 : pluginState2.dragging) {\n      updateColumnWidth(\n        view,\n        pluginState2.activeHandle,\n        draggedWidth(pluginState2.dragging, event2, cellMinWidth)\n      );\n      view.dispatch(\n        view.state.tr.setMeta(columnResizingPluginKey, { setDragging: null })\n      );\n    }\n  }\n  function move(event2) {\n    if (!event2.which) return finish(event2);\n    const pluginState2 = columnResizingPluginKey.getState(view.state);\n    if (!pluginState2) return;\n    if (pluginState2.dragging) {\n      const dragged = draggedWidth(pluginState2.dragging, event2, cellMinWidth);\n      displayColumnWidth(\n        view,\n        pluginState2.activeHandle,\n        dragged,\n        defaultCellMinWidth\n      );\n    }\n  }\n  displayColumnWidth(\n    view,\n    pluginState.activeHandle,\n    width,\n    defaultCellMinWidth\n  );\n  win.addEventListener(\"mouseup\", finish);\n  win.addEventListener(\"mousemove\", move);\n  event.preventDefault();\n  return true;\n}\nfunction currentColWidth(view, cellPos, { colspan, colwidth }) {\n  const width = colwidth && colwidth[colwidth.length - 1];\n  if (width) return width;\n  const dom = view.domAtPos(cellPos);\n  const node = dom.node.childNodes[dom.offset];\n  let domWidth = node.offsetWidth, parts = colspan;\n  if (colwidth) {\n    for (let i = 0; i < colspan; i++)\n      if (colwidth[i]) {\n        domWidth -= colwidth[i];\n        parts--;\n      }\n  }\n  return domWidth / parts;\n}\nfunction domCellAround(target) {\n  while (target && target.nodeName != \"TD\" && target.nodeName != \"TH\")\n    target = target.classList && target.classList.contains(\"ProseMirror\") ? null : target.parentNode;\n  return target;\n}\nfunction edgeCell(view, event, side, handleWidth) {\n  const offset = side == \"right\" ? -handleWidth : handleWidth;\n  const found = view.posAtCoords({\n    left: event.clientX + offset,\n    top: event.clientY\n  });\n  if (!found) return -1;\n  const { pos } = found;\n  const $cell = cellAround(view.state.doc.resolve(pos));\n  if (!$cell) return -1;\n  if (side == \"right\") return $cell.pos;\n  const map = TableMap.get($cell.node(-1)), start = $cell.start(-1);\n  const index = map.map.indexOf($cell.pos - start);\n  return index % map.width == 0 ? -1 : start + map.map[index - 1];\n}\nfunction draggedWidth(dragging, event, resizeMinWidth) {\n  const offset = event.clientX - dragging.startX;\n  return Math.max(resizeMinWidth, dragging.startWidth + offset);\n}\nfunction updateHandle(view, value) {\n  view.dispatch(\n    view.state.tr.setMeta(columnResizingPluginKey, { setHandle: value })\n  );\n}\nfunction updateColumnWidth(view, cell, width) {\n  const $cell = view.state.doc.resolve(cell);\n  const table = $cell.node(-1), map = TableMap.get(table), start = $cell.start(-1);\n  const col = map.colCount($cell.pos - start) + $cell.nodeAfter.attrs.colspan - 1;\n  const tr = view.state.tr;\n  for (let row = 0; row < map.height; row++) {\n    const mapIndex = row * map.width + col;\n    if (row && map.map[mapIndex] == map.map[mapIndex - map.width]) continue;\n    const pos = map.map[mapIndex];\n    const attrs = table.nodeAt(pos).attrs;\n    const index = attrs.colspan == 1 ? 0 : col - map.colCount(pos);\n    if (attrs.colwidth && attrs.colwidth[index] == width) continue;\n    const colwidth = attrs.colwidth ? attrs.colwidth.slice() : zeroes(attrs.colspan);\n    colwidth[index] = width;\n    tr.setNodeMarkup(start + pos, null, { ...attrs, colwidth });\n  }\n  if (tr.docChanged) view.dispatch(tr);\n}\nfunction displayColumnWidth(view, cell, width, defaultCellMinWidth) {\n  const $cell = view.state.doc.resolve(cell);\n  const table = $cell.node(-1), start = $cell.start(-1);\n  const col = TableMap.get(table).colCount($cell.pos - start) + $cell.nodeAfter.attrs.colspan - 1;\n  let dom = view.domAtPos($cell.start(-1)).node;\n  while (dom && dom.nodeName != \"TABLE\") {\n    dom = dom.parentNode;\n  }\n  if (!dom) return;\n  updateColumnsOnResize(\n    table,\n    dom.firstChild,\n    dom,\n    defaultCellMinWidth,\n    col,\n    width\n  );\n}\nfunction zeroes(n) {\n  return Array(n).fill(0);\n}\nfunction handleDecorations(state, cell) {\n  var _a;\n  const decorations = [];\n  const $cell = state.doc.resolve(cell);\n  const table = $cell.node(-1);\n  if (!table) {\n    return prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.DecorationSet.empty;\n  }\n  const map = TableMap.get(table);\n  const start = $cell.start(-1);\n  const col = map.colCount($cell.pos - start) + $cell.nodeAfter.attrs.colspan - 1;\n  for (let row = 0; row < map.height; row++) {\n    const index = col + row * map.width;\n    if ((col == map.width - 1 || map.map[index] != map.map[index + 1]) && (row == 0 || map.map[index] != map.map[index - map.width])) {\n      const cellPos = map.map[index];\n      const pos = start + cellPos + table.nodeAt(cellPos).nodeSize - 1;\n      const dom = document.createElement(\"div\");\n      dom.className = \"column-resize-handle\";\n      if ((_a = columnResizingPluginKey.getState(state)) == null ? void 0 : _a.dragging) {\n        decorations.push(\n          prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.node(\n            start + cellPos,\n            start + cellPos + table.nodeAt(cellPos).nodeSize,\n            {\n              class: \"column-resize-dragging\"\n            }\n          )\n        );\n      }\n      decorations.push(prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.widget(pos, dom));\n    }\n  }\n  return prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.DecorationSet.create(state.doc, decorations);\n}\n\n// src/index.ts\nfunction tableEditing({\n  allowTableNodeSelection = false\n} = {}) {\n  return new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n    key: tableEditingKey,\n    // This piece of state is used to remember when a mouse-drag\n    // cell-selection is happening, so that it can continue even as\n    // transactions (which might move its anchor cell) come in.\n    state: {\n      init() {\n        return null;\n      },\n      apply(tr, cur) {\n        const set = tr.getMeta(tableEditingKey);\n        if (set != null) return set == -1 ? null : set;\n        if (cur == null || !tr.docChanged) return cur;\n        const { deleted, pos } = tr.mapping.mapResult(cur);\n        return deleted ? null : pos;\n      }\n    },\n    props: {\n      decorations: drawCellSelection,\n      handleDOMEvents: {\n        mousedown: handleMouseDown\n      },\n      createSelectionBetween(view) {\n        return tableEditingKey.getState(view.state) != null ? view.state.selection : null;\n      },\n      handleTripleClick,\n      handleKeyDown,\n      handlePaste\n    },\n    appendTransaction(_, oldState, state) {\n      return normalizeSelection(\n        state,\n        fixTables(state, oldState),\n        allowTableNodeSelection\n      );\n    }\n  });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-tables/dist/index.js\n");

/***/ })

};
;