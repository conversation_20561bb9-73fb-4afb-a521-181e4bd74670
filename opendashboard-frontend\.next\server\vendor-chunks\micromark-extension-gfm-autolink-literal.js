"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-autolink-literal";
exports.ids = ["vendor-chunks/micromark-extension-gfm-autolink-literal"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-gfm-autolink-literal/dev/lib/html.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-autolink-literal/dev/lib/html.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmAutolinkLiteralHtml: () => (/* binding */ gfmAutolinkLiteralHtml)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @typedef {import('micromark-util-types').CompileContext} CompileContext\n * @typedef {import('micromark-util-types').Handle} Handle\n * @typedef {import('micromark-util-types').HtmlExtension} HtmlExtension\n * @typedef {import('micromark-util-types').Token} Token\n */\n\n\n\n// To do: next major: expose functions that yields extension.\n\n/**\n * Extension for `micromark` that can be passed in `htmlExtensions` to support\n * GFM autolink literals when serializing to HTML.\n *\n * @type {HtmlExtension}\n */\nconst gfmAutolinkLiteralHtml = {\n  exit: {literalAutolinkEmail, literalAutolinkHttp, literalAutolinkWww}\n}\n\n/**\n * @this {CompileContext}\n * @type {Handle}\n */\nfunction literalAutolinkWww(token) {\n  anchorFromToken.call(this, token, 'http://')\n}\n\n/**\n * @this {CompileContext}\n * @type {Handle}\n */\nfunction literalAutolinkEmail(token) {\n  anchorFromToken.call(this, token, 'mailto:')\n}\n\n/**\n * @this {CompileContext}\n * @type {Handle}\n */\nfunction literalAutolinkHttp(token) {\n  anchorFromToken.call(this, token)\n}\n\n/**\n * @this CompileContext\n * @param {Token} token\n * @param {string | null | undefined} [protocol]\n * @returns {void}\n */\nfunction anchorFromToken(token, protocol) {\n  const url = this.sliceSerialize(token)\n  this.tag('<a href=\"' + (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.sanitizeUri)((protocol || '') + url) + '\">')\n  this.raw(this.encode(url))\n  this.tag('</a>')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-autolink-literal/dev/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmAutolinkLiteral: () => (/* binding */ gfmAutolinkLiteral)\n/* harmony export */ });\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/micromark-util-symbol/codes.js\");\n/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').ConstructRecord} ConstructRecord\n * @typedef {import('micromark-util-types').Event} Event\n * @typedef {import('micromark-util-types').Extension} Extension\n * @typedef {import('micromark-util-types').Previous} Previous\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\nconst wwwPrefix = {tokenize: tokenizeWwwPrefix, partial: true}\nconst domain = {tokenize: tokenizeDomain, partial: true}\nconst path = {tokenize: tokenizePath, partial: true}\nconst trail = {tokenize: tokenizeTrail, partial: true}\nconst emailDomainDotTrail = {\n  tokenize: tokenizeEmailDomainDotTrail,\n  partial: true\n}\n\nconst wwwAutolink = {tokenize: tokenizeWwwAutolink, previous: previousWww}\nconst protocolAutolink = {\n  tokenize: tokenizeProtocolAutolink,\n  previous: previousProtocol\n}\nconst emailAutolink = {tokenize: tokenizeEmailAutolink, previous: previousEmail}\n\n/** @type {ConstructRecord} */\nconst text = {}\n\n// To do: next major: expose functions that yields extension.\n\n/**\n * Extension for `micromark` that can be passed in `extensions` to enable GFM\n * autolink literal syntax.\n *\n * @type {Extension}\n */\nconst gfmAutolinkLiteral = {text}\n\n/** @type {Code} */\nlet code = micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.digit0\n\n// Add alphanumerics.\nwhile (code < micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.leftCurlyBrace) {\n  text[code] = emailAutolink\n  code++\n  if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.colon) code = micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseA\n  else if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket) code = micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseA\n}\n\ntext[micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.plusSign] = emailAutolink\ntext[micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.dash] = emailAutolink\ntext[micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.dot] = emailAutolink\ntext[micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.underscore] = emailAutolink\ntext[micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseH] = [emailAutolink, protocolAutolink]\ntext[micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseH] = [emailAutolink, protocolAutolink]\ntext[micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseW] = [emailAutolink, wwwAutolink]\ntext[micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseW] = [emailAutolink, wwwAutolink]\n\n// To do: perform email autolink literals on events, afterwards.\n// That’s where `markdown-rs` and `cmark-gfm` perform it.\n// It should look for `@`, then for atext backwards, and then for a label\n// forwards.\n// To do: `mailto:`, `xmpp:` protocol as prefix.\n\n/**\n * Email autolink literal.\n *\n * ```markdown\n * > | a <EMAIL> b\n *       ^^^^^^^^^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeEmailAutolink(effects, ok, nok) {\n  const self = this\n  /** @type {boolean | undefined} */\n  let dot\n  /** @type {boolean} */\n  let data\n\n  return start\n\n  /**\n   * Start of email autolink literal.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (\n      !gfmAtext(code) ||\n      !previousEmail.call(self, self.previous) ||\n      previousUnbalanced(self.events)\n    ) {\n      return nok(code)\n    }\n\n    effects.enter('literalAutolink')\n    effects.enter('literalAutolinkEmail')\n    return atext(code)\n  }\n\n  /**\n   * In email atext.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atext(code) {\n    if (gfmAtext(code)) {\n      effects.consume(code)\n      return atext\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.atSign) {\n      effects.consume(code)\n      return emailDomain\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In email domain.\n   *\n   * The reference code is a bit overly complex as it handles the `@`, of which\n   * there may be just one.\n   * Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L318>\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailDomain(code) {\n    // Dot followed by alphanumerical (not `-` or `_`).\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.dot) {\n      return effects.check(\n        emailDomainDotTrail,\n        emailDomainAfter,\n        emailDomainDot\n      )(code)\n    }\n\n    // Alphanumerical, `-`, and `_`.\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.dash ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.underscore ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlphanumeric)(code)\n    ) {\n      data = true\n      effects.consume(code)\n      return emailDomain\n    }\n\n    // To do: `/` if xmpp.\n\n    // Note: normally we’d truncate trailing punctuation from the link.\n    // However, email autolink literals cannot contain any of those markers,\n    // except for `.`, but that can only occur if it isn’t trailing.\n    // So we can ignore truncating!\n    return emailDomainAfter(code)\n  }\n\n  /**\n   * In email domain, on dot that is not a trail.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *                      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailDomainDot(code) {\n    effects.consume(code)\n    dot = true\n    return emailDomain\n  }\n\n  /**\n   * After email domain.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *                          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailDomainAfter(code) {\n    // Domain must not be empty, must include a dot, and must end in alphabetical.\n    // Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L332>.\n    if (data && dot && (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(self.previous)) {\n      effects.exit('literalAutolinkEmail')\n      effects.exit('literalAutolink')\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n\n/**\n * `www` autolink literal.\n *\n * ```markdown\n * > | a www.example.org b\n *       ^^^^^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeWwwAutolink(effects, ok, nok) {\n  const self = this\n\n  return wwwStart\n\n  /**\n   * Start of www autolink literal.\n   *\n   * ```markdown\n   * > | www.example.com/a?b#c\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function wwwStart(code) {\n    if (\n      (code !== micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseW && code !== micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseW) ||\n      !previousWww.call(self, self.previous) ||\n      previousUnbalanced(self.events)\n    ) {\n      return nok(code)\n    }\n\n    effects.enter('literalAutolink')\n    effects.enter('literalAutolinkWww')\n    // Note: we *check*, so we can discard the `www.` we parsed.\n    // If it worked, we consider it as a part of the domain.\n    return effects.check(\n      wwwPrefix,\n      effects.attempt(domain, effects.attempt(path, wwwAfter), nok),\n      nok\n    )(code)\n  }\n\n  /**\n   * After a www autolink literal.\n   *\n   * ```markdown\n   * > | www.example.com/a?b#c\n   *                          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function wwwAfter(code) {\n    effects.exit('literalAutolinkWww')\n    effects.exit('literalAutolink')\n    return ok(code)\n  }\n}\n\n/**\n * Protocol autolink literal.\n *\n * ```markdown\n * > | a https://example.org b\n *       ^^^^^^^^^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeProtocolAutolink(effects, ok, nok) {\n  const self = this\n  let buffer = ''\n  let seen = false\n\n  return protocolStart\n\n  /**\n   * Start of protocol autolink literal.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function protocolStart(code) {\n    if (\n      (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseH || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseH) &&\n      previousProtocol.call(self, self.previous) &&\n      !previousUnbalanced(self.events)\n    ) {\n      effects.enter('literalAutolink')\n      effects.enter('literalAutolinkHttp')\n      buffer += String.fromCodePoint(code)\n      effects.consume(code)\n      return protocolPrefixInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In protocol.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *     ^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function protocolPrefixInside(code) {\n    // `5` is size of `https`\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(code) && buffer.length < 5) {\n      // @ts-expect-error: definitely number.\n      buffer += String.fromCodePoint(code)\n      effects.consume(code)\n      return protocolPrefixInside\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.colon) {\n      const protocol = buffer.toLowerCase()\n\n      if (protocol === 'http' || protocol === 'https') {\n        effects.consume(code)\n        return protocolSlashesInside\n      }\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In slashes.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *           ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function protocolSlashesInside(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.slash) {\n      effects.consume(code)\n\n      if (seen) {\n        return afterProtocol\n      }\n\n      seen = true\n      return protocolSlashesInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After protocol, before domain.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterProtocol(code) {\n    // To do: this is different from `markdown-rs`:\n    // https://github.com/wooorm/markdown-rs/blob/b3a921c761309ae00a51fe348d8a43adbc54b518/src/construct/gfm_autolink_literal.rs#L172-L182\n    return code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiControl)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodePunctuation)(code)\n      ? nok(code)\n      : effects.attempt(domain, effects.attempt(path, protocolAfter), nok)(code)\n  }\n\n  /**\n   * After a protocol autolink literal.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *                              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function protocolAfter(code) {\n    effects.exit('literalAutolinkHttp')\n    effects.exit('literalAutolink')\n    return ok(code)\n  }\n}\n\n/**\n * `www` prefix.\n *\n * ```markdown\n * > | a www.example.org b\n *       ^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeWwwPrefix(effects, ok, nok) {\n  let size = 0\n\n  return wwwPrefixInside\n\n  /**\n   * In www prefix.\n   *\n   * ```markdown\n   * > | www.example.com\n   *     ^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function wwwPrefixInside(code) {\n    if ((code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseW || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseW) && size < 3) {\n      size++\n      effects.consume(code)\n      return wwwPrefixInside\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.dot && size === 3) {\n      effects.consume(code)\n      return wwwPrefixAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After www prefix.\n   *\n   * ```markdown\n   * > | www.example.com\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function wwwPrefixAfter(code) {\n    // If there is *anything*, we can link.\n    return code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.eof ? nok(code) : ok(code)\n  }\n}\n\n/**\n * Domain.\n *\n * ```markdown\n * > | a https://example.org b\n *               ^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeDomain(effects, ok, nok) {\n  /** @type {boolean | undefined} */\n  let underscoreInLastSegment\n  /** @type {boolean | undefined} */\n  let underscoreInLastLastSegment\n  /** @type {boolean | undefined} */\n  let seen\n\n  return domainInside\n\n  /**\n   * In domain.\n   *\n   * ```markdown\n   * > | https://example.com/a\n   *             ^^^^^^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function domainInside(code) {\n    // Check whether this marker, which is a trailing punctuation\n    // marker, optionally followed by more trailing markers, and then\n    // followed by an end.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.dot || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.underscore) {\n      return effects.check(trail, domainAfter, domainAtPunctuation)(code)\n    }\n\n    // GH documents that only alphanumerics (other than `-`, `.`, and `_`) can\n    // occur, which sounds like ASCII only, but they also support `www.點看.com`,\n    // so that’s Unicode.\n    // Instead of some new production for Unicode alphanumerics, markdown\n    // already has that for Unicode punctuation and whitespace, so use those.\n    // Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L12>.\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code) ||\n      (code !== micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.dash && (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodePunctuation)(code))\n    ) {\n      return domainAfter(code)\n    }\n\n    seen = true\n    effects.consume(code)\n    return domainInside\n  }\n\n  /**\n   * In domain, at potential trailing punctuation, that was not trailing.\n   *\n   * ```markdown\n   * > | https://example.com\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */\n  function domainAtPunctuation(code) {\n    // There is an underscore in the last segment of the domain\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.underscore) {\n      underscoreInLastSegment = true\n    }\n    // Otherwise, it’s a `.`: save the last segment underscore in the\n    // penultimate segment slot.\n    else {\n      underscoreInLastLastSegment = underscoreInLastSegment\n      underscoreInLastSegment = undefined\n    }\n\n    effects.consume(code)\n    return domainInside\n  }\n\n  /**\n   * After domain.\n   *\n   * ```markdown\n   * > | https://example.com/a\n   *                        ^\n   * ```\n   *\n   * @type {State} */\n  function domainAfter(code) {\n    // Note: that’s GH says a dot is needed, but it’s not true:\n    // <https://github.com/github/cmark-gfm/issues/279>\n    if (underscoreInLastLastSegment || underscoreInLastSegment || !seen) {\n      return nok(code)\n    }\n\n    return ok(code)\n  }\n}\n\n/**\n * Path.\n *\n * ```markdown\n * > | a https://example.org/stuff b\n *                          ^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizePath(effects, ok) {\n  let sizeOpen = 0\n  let sizeClose = 0\n\n  return pathInside\n\n  /**\n   * In path.\n   *\n   * ```markdown\n   * > | https://example.com/a\n   *                        ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function pathInside(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.leftParenthesis) {\n      sizeOpen++\n      effects.consume(code)\n      return pathInside\n    }\n\n    // To do: `markdown-rs` also needs this.\n    // If this is a paren, and there are less closings than openings,\n    // we don’t check for a trail.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.rightParenthesis && sizeClose < sizeOpen) {\n      return pathAtPunctuation(code)\n    }\n\n    // Check whether this trailing punctuation marker is optionally\n    // followed by more trailing markers, and then followed\n    // by an end.\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.exclamationMark ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.quotationMark ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.apostrophe ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.rightParenthesis ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.comma ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.dot ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.colon ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.semicolon ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.questionMark ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.underscore ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.tilde\n    ) {\n      return effects.check(trail, ok, pathAtPunctuation)(code)\n    }\n\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code)\n    ) {\n      return ok(code)\n    }\n\n    effects.consume(code)\n    return pathInside\n  }\n\n  /**\n   * In path, at potential trailing punctuation, that was not trailing.\n   *\n   * ```markdown\n   * > | https://example.com/a\"b\n   *                          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function pathAtPunctuation(code) {\n    // Count closing parens.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.rightParenthesis) {\n      sizeClose++\n    }\n\n    effects.consume(code)\n    return pathInside\n  }\n}\n\n/**\n * Trail.\n *\n * This calls `ok` if this *is* the trail, followed by an end, which means\n * the entire trail is not part of the link.\n * It calls `nok` if this *is* part of the link.\n *\n * ```markdown\n * > | https://example.com\").\n *                        ^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeTrail(effects, ok, nok) {\n  return trail\n\n  /**\n   * In trail of domain or path.\n   *\n   * ```markdown\n   * > | https://example.com\").\n   *                        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function trail(code) {\n    // Regular trailing punctuation.\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.exclamationMark ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.quotationMark ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.apostrophe ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.rightParenthesis ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.comma ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.dot ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.colon ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.semicolon ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.questionMark ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.underscore ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.tilde\n    ) {\n      effects.consume(code)\n      return trail\n    }\n\n    // `&` followed by one or more alphabeticals and then a `;`, is\n    // as a whole considered as trailing punctuation.\n    // In all other cases, it is considered as continuation of the URL.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand) {\n      effects.consume(code)\n      return trailCharRefStart\n    }\n\n    // Needed because we allow literals after `[`, as we fix:\n    // <https://github.com/github/cmark-gfm/issues/278>.\n    // Check that it is not followed by `(` or `[`.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket) {\n      effects.consume(code)\n      return trailBracketAfter\n    }\n\n    if (\n      // `<` is an end.\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan ||\n      // So is whitespace.\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code)\n    ) {\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In trail, after `]`.\n   *\n   * > 👉 **Note**: this deviates from `cmark-gfm` to fix a bug.\n   * > See end of <https://github.com/github/cmark-gfm/issues/278> for more.\n   *\n   * ```markdown\n   * > | https://example.com](\n   *                         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function trailBracketAfter(code) {\n    // Whitespace or something that could start a resource or reference is the end.\n    // Switch back to trail otherwise.\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.leftParenthesis ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code)\n    ) {\n      return ok(code)\n    }\n\n    return trail(code)\n  }\n\n  /**\n   * In character-reference like trail, after `&`.\n   *\n   * ```markdown\n   * > | https://example.com&amp;).\n   *                         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function trailCharRefStart(code) {\n    // When non-alpha, it’s not a trail.\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(code) ? trailCharRefInside(code) : nok(code)\n  }\n\n  /**\n   * In character-reference like trail.\n   *\n   * ```markdown\n   * > | https://example.com&amp;).\n   *                         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function trailCharRefInside(code) {\n    // Switch back to trail if this is well-formed.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.semicolon) {\n      effects.consume(code)\n      return trail\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(code)) {\n      effects.consume(code)\n      return trailCharRefInside\n    }\n\n    // It’s not a trail.\n    return nok(code)\n  }\n}\n\n/**\n * Dot in email domain trail.\n *\n * This calls `ok` if this *is* the trail, followed by an end, which means\n * the trail is not part of the link.\n * It calls `nok` if this *is* part of the link.\n *\n * ```markdown\n * > | <EMAIL>.\n *                        ^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeEmailDomainDotTrail(effects, ok, nok) {\n  return start\n\n  /**\n   * Dot.\n   *\n   * ```markdown\n   * > | <EMAIL>.\n   *                    ^   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // Must be dot.\n    effects.consume(code)\n    return after\n  }\n\n  /**\n   * After dot.\n   *\n   * ```markdown\n   * > | <EMAIL>.\n   *                     ^   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // Not a trail if alphanumeric.\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlphanumeric)(code) ? nok(code) : ok(code)\n  }\n}\n\n/**\n * See:\n * <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L156>.\n *\n * @type {Previous}\n */\nfunction previousWww(code) {\n  return (\n    code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n    code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.leftParenthesis ||\n    code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk ||\n    code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.underscore ||\n    code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket ||\n    code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket ||\n    code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.tilde ||\n    (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code)\n  )\n}\n\n/**\n * See:\n * <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L214>.\n *\n * @type {Previous}\n */\nfunction previousProtocol(code) {\n  return !(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(code)\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Previous}\n */\nfunction previousEmail(code) {\n  // Do not allow a slash “inside” atext.\n  // The reference code is a bit weird, but that’s what it results in.\n  // Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L307>.\n  // Other than slash, every preceding character is allowed.\n  return !(code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.slash || gfmAtext(code))\n}\n\n/**\n * @param {Code} code\n * @returns {boolean}\n */\nfunction gfmAtext(code) {\n  return (\n    code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.plusSign ||\n    code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.dash ||\n    code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.dot ||\n    code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.underscore ||\n    (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlphanumeric)(code)\n  )\n}\n\n/**\n * @param {Array<Event>} events\n * @returns {boolean}\n */\nfunction previousUnbalanced(events) {\n  let index = events.length\n  let result = false\n\n  while (index--) {\n    const token = events[index][1]\n\n    if (\n      (token.type === 'labelLink' || token.type === 'labelImage') &&\n      !token._balanced\n    ) {\n      result = true\n      break\n    }\n\n    // If we’ve seen this token, and it was marked as not having any unbalanced\n    // bracket before it, we can exit.\n    if (token._gfmAutolinkLiteralWalkedInto) {\n      result = false\n      break\n    }\n  }\n\n  if (events.length > 0 && !result) {\n    // Mark the last token as “walked into” w/o finding\n    // anything.\n    events[events.length - 1][1]._gfmAutolinkLiteralWalkedInto = true\n  }\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWV4dGVuc2lvbi1nZm0tYXV0b2xpbmstbGl0ZXJhbC9kZXYvbGliL3N5bnRheC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBLGFBQWEscUNBQXFDO0FBQ2xELGFBQWEsZ0RBQWdEO0FBQzdELGFBQWEsc0NBQXNDO0FBQ25ELGFBQWEsMENBQTBDO0FBQ3ZELGFBQWEseUNBQXlDO0FBQ3RELGFBQWEsc0NBQXNDO0FBQ25ELGFBQWEsZ0RBQWdEO0FBQzdELGFBQWEsMENBQTBDO0FBQ3ZEOztBQVNpQztBQUNtQjs7QUFFcEQsbUJBQW1CO0FBQ25CLGdCQUFnQjtBQUNoQixjQUFjO0FBQ2QsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1Qjs7QUFFdkIsV0FBVyxpQkFBaUI7QUFDNUI7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDTyw0QkFBNEI7O0FBRW5DLFdBQVcsTUFBTTtBQUNqQixXQUFXLGlFQUFLOztBQUVoQjtBQUNBLGNBQWMsaUVBQUs7QUFDbkI7QUFDQTtBQUNBLGVBQWUsaUVBQUssZUFBZSxpRUFBSztBQUN4QyxvQkFBb0IsaUVBQUssMkJBQTJCLGlFQUFLO0FBQ3pEOztBQUVBLEtBQUssaUVBQUs7QUFDVixLQUFLLGlFQUFLO0FBQ1YsS0FBSyxpRUFBSztBQUNWLEtBQUssaUVBQUs7QUFDVixLQUFLLGlFQUFLO0FBQ1YsS0FBSyxpRUFBSztBQUNWLEtBQUssaUVBQUs7QUFDVixLQUFLLGlFQUFLOztBQUVWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVixVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0EsYUFBYSxxQkFBcUI7QUFDbEM7QUFDQSxhQUFhLFNBQVM7QUFDdEI7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsaUJBQWlCLGlFQUFLO0FBQ3RCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsaUVBQUs7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxlQUFlLGlFQUFLO0FBQ3BCLGVBQWUsaUVBQUs7QUFDcEIsTUFBTSwyRUFBaUI7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixvRUFBVTtBQUNqQztBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVixVQUFVO0FBQ1Y7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLGlFQUFLLHdCQUF3QixpRUFBSztBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVixVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixpRUFBSyx3QkFBd0IsaUVBQUs7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQSxRQUFRLG9FQUFVO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsaUJBQWlCLGlFQUFLO0FBQ3RCOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQSxpQkFBaUIsaUVBQUs7QUFDdEI7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsaUVBQUs7QUFDekIsTUFBTSxzRUFBWTtBQUNsQixNQUFNLG1GQUF5QjtBQUMvQixNQUFNLDJFQUFpQjtBQUN2QixNQUFNLDRFQUFrQjtBQUN4QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWLFVBQVU7QUFDVjtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0Esa0JBQWtCLGlFQUFLLHdCQUF3QixpRUFBSztBQUNwRDtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxpQkFBaUIsaUVBQUs7QUFDdEI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsaUVBQUs7QUFDekI7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWLFVBQVU7QUFDVjtBQUNBO0FBQ0EsYUFBYSxxQkFBcUI7QUFDbEM7QUFDQSxhQUFhLHFCQUFxQjtBQUNsQztBQUNBLGFBQWEscUJBQXFCO0FBQ2xDOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQixpRUFBSyxpQkFBaUIsaUVBQUs7QUFDNUM7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsaUVBQUs7QUFDcEIsTUFBTSxtRkFBeUI7QUFDL0IsTUFBTSwyRUFBaUI7QUFDdkIsZ0JBQWdCLGlFQUFLLFNBQVMsNEVBQWtCO0FBQ2hEO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQixpRUFBSztBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxPQUFPO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVixVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0EsaUJBQWlCLGlFQUFLO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQixpRUFBSztBQUN0QjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxpRUFBSztBQUNwQixlQUFlLGlFQUFLO0FBQ3BCLGVBQWUsaUVBQUs7QUFDcEIsZUFBZSxpRUFBSztBQUNwQixlQUFlLGlFQUFLO0FBQ3BCLGVBQWUsaUVBQUs7QUFDcEIsZUFBZSxpRUFBSztBQUNwQixlQUFlLGlFQUFLO0FBQ3BCLGVBQWUsaUVBQUs7QUFDcEIsZUFBZSxpRUFBSztBQUNwQixlQUFlLGlFQUFLO0FBQ3BCLGVBQWUsaUVBQUs7QUFDcEIsZUFBZSxpRUFBSztBQUNwQixlQUFlLGlFQUFLO0FBQ3BCLGVBQWUsaUVBQUs7QUFDcEI7QUFDQTtBQUNBOztBQUVBO0FBQ0EsZUFBZSxpRUFBSztBQUNwQixNQUFNLG1GQUF5QjtBQUMvQixNQUFNLDJFQUFpQjtBQUN2QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLGlFQUFLO0FBQ3RCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWLFVBQVU7QUFDVjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsaUVBQUs7QUFDcEIsZUFBZSxpRUFBSztBQUNwQixlQUFlLGlFQUFLO0FBQ3BCLGVBQWUsaUVBQUs7QUFDcEIsZUFBZSxpRUFBSztBQUNwQixlQUFlLGlFQUFLO0FBQ3BCLGVBQWUsaUVBQUs7QUFDcEIsZUFBZSxpRUFBSztBQUNwQixlQUFlLGlFQUFLO0FBQ3BCLGVBQWUsaUVBQUs7QUFDcEIsZUFBZSxpRUFBSztBQUNwQixlQUFlLGlFQUFLO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBOztBQUVBLDhEQUE4RDtBQUM5RDtBQUNBO0FBQ0EsaUJBQWlCLGlFQUFLO0FBQ3RCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsaUVBQUs7QUFDdEI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxlQUFlLGlFQUFLO0FBQ3BCO0FBQ0EsZUFBZSxpRUFBSztBQUNwQixNQUFNLG1GQUF5QjtBQUMvQixNQUFNLDJFQUFpQjtBQUN2QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLGlFQUFLO0FBQ3BCLGVBQWUsaUVBQUs7QUFDcEIsZUFBZSxpRUFBSztBQUNwQixNQUFNLG1GQUF5QjtBQUMvQixNQUFNLDJFQUFpQjtBQUN2QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsV0FBVyxvRUFBVTtBQUNyQjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLGlFQUFLO0FBQ3RCO0FBQ0E7QUFDQTs7QUFFQSxRQUFRLG9FQUFVO0FBQ2xCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1YsVUFBVTtBQUNWO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBLFdBQVcsMkVBQWlCO0FBQzVCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0EsYUFBYSxpRUFBSztBQUNsQixhQUFhLGlFQUFLO0FBQ2xCLGFBQWEsaUVBQUs7QUFDbEIsYUFBYSxpRUFBSztBQUNsQixhQUFhLGlFQUFLO0FBQ2xCLGFBQWEsaUVBQUs7QUFDbEIsYUFBYSxpRUFBSztBQUNsQixJQUFJLG1GQUF5QjtBQUM3QjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQSxVQUFVLG9FQUFVO0FBQ3BCOztBQUVBO0FBQ0EsVUFBVTtBQUNWLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsaUVBQUs7QUFDekI7O0FBRUE7QUFDQSxXQUFXLE1BQU07QUFDakIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLGFBQWEsaUVBQUs7QUFDbEIsYUFBYSxpRUFBSztBQUNsQixhQUFhLGlFQUFLO0FBQ2xCLGFBQWEsaUVBQUs7QUFDbEIsSUFBSSwyRUFBaUI7QUFDckI7QUFDQTs7QUFFQTtBQUNBLFdBQVcsY0FBYztBQUN6QixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay1leHRlbnNpb24tZ2ZtLWF1dG9saW5rLWxpdGVyYWwvZGV2L2xpYi9zeW50YXguanM/MzA5MSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21pY3JvbWFyay11dGlsLXR5cGVzJykuQ29kZX0gQ29kZVxuICogQHR5cGVkZWYge2ltcG9ydCgnbWljcm9tYXJrLXV0aWwtdHlwZXMnKS5Db25zdHJ1Y3RSZWNvcmR9IENvbnN0cnVjdFJlY29yZFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWljcm9tYXJrLXV0aWwtdHlwZXMnKS5FdmVudH0gRXZlbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21pY3JvbWFyay11dGlsLXR5cGVzJykuRXh0ZW5zaW9ufSBFeHRlbnNpb25cbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21pY3JvbWFyay11dGlsLXR5cGVzJykuUHJldmlvdXN9IFByZXZpb3VzXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtaWNyb21hcmstdXRpbC10eXBlcycpLlN0YXRlfSBTdGF0ZVxuICogQHR5cGVkZWYge2ltcG9ydCgnbWljcm9tYXJrLXV0aWwtdHlwZXMnKS5Ub2tlbml6ZUNvbnRleHR9IFRva2VuaXplQ29udGV4dFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWljcm9tYXJrLXV0aWwtdHlwZXMnKS5Ub2tlbml6ZXJ9IFRva2VuaXplclxuICovXG5cbmltcG9ydCB7XG4gIGFzY2lpQWxwaGEsXG4gIGFzY2lpQWxwaGFudW1lcmljLFxuICBhc2NpaUNvbnRyb2wsXG4gIG1hcmtkb3duTGluZUVuZGluZ09yU3BhY2UsXG4gIHVuaWNvZGVQdW5jdHVhdGlvbixcbiAgdW5pY29kZVdoaXRlc3BhY2Vcbn0gZnJvbSAnbWljcm9tYXJrLXV0aWwtY2hhcmFjdGVyJ1xuaW1wb3J0IHtjb2Rlc30gZnJvbSAnbWljcm9tYXJrLXV0aWwtc3ltYm9sL2NvZGVzLmpzJ1xuXG5jb25zdCB3d3dQcmVmaXggPSB7dG9rZW5pemU6IHRva2VuaXplV3d3UHJlZml4LCBwYXJ0aWFsOiB0cnVlfVxuY29uc3QgZG9tYWluID0ge3Rva2VuaXplOiB0b2tlbml6ZURvbWFpbiwgcGFydGlhbDogdHJ1ZX1cbmNvbnN0IHBhdGggPSB7dG9rZW5pemU6IHRva2VuaXplUGF0aCwgcGFydGlhbDogdHJ1ZX1cbmNvbnN0IHRyYWlsID0ge3Rva2VuaXplOiB0b2tlbml6ZVRyYWlsLCBwYXJ0aWFsOiB0cnVlfVxuY29uc3QgZW1haWxEb21haW5Eb3RUcmFpbCA9IHtcbiAgdG9rZW5pemU6IHRva2VuaXplRW1haWxEb21haW5Eb3RUcmFpbCxcbiAgcGFydGlhbDogdHJ1ZVxufVxuXG5jb25zdCB3d3dBdXRvbGluayA9IHt0b2tlbml6ZTogdG9rZW5pemVXd3dBdXRvbGluaywgcHJldmlvdXM6IHByZXZpb3VzV3d3fVxuY29uc3QgcHJvdG9jb2xBdXRvbGluayA9IHtcbiAgdG9rZW5pemU6IHRva2VuaXplUHJvdG9jb2xBdXRvbGluayxcbiAgcHJldmlvdXM6IHByZXZpb3VzUHJvdG9jb2xcbn1cbmNvbnN0IGVtYWlsQXV0b2xpbmsgPSB7dG9rZW5pemU6IHRva2VuaXplRW1haWxBdXRvbGluaywgcHJldmlvdXM6IHByZXZpb3VzRW1haWx9XG5cbi8qKiBAdHlwZSB7Q29uc3RydWN0UmVjb3JkfSAqL1xuY29uc3QgdGV4dCA9IHt9XG5cbi8vIFRvIGRvOiBuZXh0IG1ham9yOiBleHBvc2UgZnVuY3Rpb25zIHRoYXQgeWllbGRzIGV4dGVuc2lvbi5cblxuLyoqXG4gKiBFeHRlbnNpb24gZm9yIGBtaWNyb21hcmtgIHRoYXQgY2FuIGJlIHBhc3NlZCBpbiBgZXh0ZW5zaW9uc2AgdG8gZW5hYmxlIEdGTVxuICogYXV0b2xpbmsgbGl0ZXJhbCBzeW50YXguXG4gKlxuICogQHR5cGUge0V4dGVuc2lvbn1cbiAqL1xuZXhwb3J0IGNvbnN0IGdmbUF1dG9saW5rTGl0ZXJhbCA9IHt0ZXh0fVxuXG4vKiogQHR5cGUge0NvZGV9ICovXG5sZXQgY29kZSA9IGNvZGVzLmRpZ2l0MFxuXG4vLyBBZGQgYWxwaGFudW1lcmljcy5cbndoaWxlIChjb2RlIDwgY29kZXMubGVmdEN1cmx5QnJhY2UpIHtcbiAgdGV4dFtjb2RlXSA9IGVtYWlsQXV0b2xpbmtcbiAgY29kZSsrXG4gIGlmIChjb2RlID09PSBjb2Rlcy5jb2xvbikgY29kZSA9IGNvZGVzLnVwcGVyY2FzZUFcbiAgZWxzZSBpZiAoY29kZSA9PT0gY29kZXMubGVmdFNxdWFyZUJyYWNrZXQpIGNvZGUgPSBjb2Rlcy5sb3dlcmNhc2VBXG59XG5cbnRleHRbY29kZXMucGx1c1NpZ25dID0gZW1haWxBdXRvbGlua1xudGV4dFtjb2Rlcy5kYXNoXSA9IGVtYWlsQXV0b2xpbmtcbnRleHRbY29kZXMuZG90XSA9IGVtYWlsQXV0b2xpbmtcbnRleHRbY29kZXMudW5kZXJzY29yZV0gPSBlbWFpbEF1dG9saW5rXG50ZXh0W2NvZGVzLnVwcGVyY2FzZUhdID0gW2VtYWlsQXV0b2xpbmssIHByb3RvY29sQXV0b2xpbmtdXG50ZXh0W2NvZGVzLmxvd2VyY2FzZUhdID0gW2VtYWlsQXV0b2xpbmssIHByb3RvY29sQXV0b2xpbmtdXG50ZXh0W2NvZGVzLnVwcGVyY2FzZVddID0gW2VtYWlsQXV0b2xpbmssIHd3d0F1dG9saW5rXVxudGV4dFtjb2Rlcy5sb3dlcmNhc2VXXSA9IFtlbWFpbEF1dG9saW5rLCB3d3dBdXRvbGlua11cblxuLy8gVG8gZG86IHBlcmZvcm0gZW1haWwgYXV0b2xpbmsgbGl0ZXJhbHMgb24gZXZlbnRzLCBhZnRlcndhcmRzLlxuLy8gVGhhdOKAmXMgd2hlcmUgYG1hcmtkb3duLXJzYCBhbmQgYGNtYXJrLWdmbWAgcGVyZm9ybSBpdC5cbi8vIEl0IHNob3VsZCBsb29rIGZvciBgQGAsIHRoZW4gZm9yIGF0ZXh0IGJhY2t3YXJkcywgYW5kIHRoZW4gZm9yIGEgbGFiZWxcbi8vIGZvcndhcmRzLlxuLy8gVG8gZG86IGBtYWlsdG86YCwgYHhtcHA6YCBwcm90b2NvbCBhcyBwcmVmaXguXG5cbi8qKlxuICogRW1haWwgYXV0b2xpbmsgbGl0ZXJhbC5cbiAqXG4gKiBgYGBtYXJrZG93blxuICogPiB8IGEgY29udGFjdEBleGFtcGxlLm9yZyBiXG4gKiAgICAgICBeXl5eXl5eXl5eXl5eXl5eXl5eXG4gKiBgYGBcbiAqXG4gKiBAdGhpcyB7VG9rZW5pemVDb250ZXh0fVxuICogQHR5cGUge1Rva2VuaXplcn1cbiAqL1xuZnVuY3Rpb24gdG9rZW5pemVFbWFpbEF1dG9saW5rKGVmZmVjdHMsIG9rLCBub2spIHtcbiAgY29uc3Qgc2VsZiA9IHRoaXNcbiAgLyoqIEB0eXBlIHtib29sZWFuIHwgdW5kZWZpbmVkfSAqL1xuICBsZXQgZG90XG4gIC8qKiBAdHlwZSB7Ym9vbGVhbn0gKi9cbiAgbGV0IGRhdGFcblxuICByZXR1cm4gc3RhcnRcblxuICAvKipcbiAgICogU3RhcnQgb2YgZW1haWwgYXV0b2xpbmsgbGl0ZXJhbC5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IGEgY29udGFjdEBleGFtcGxlLm9yZyBiXG4gICAqICAgICAgIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIHN0YXJ0KGNvZGUpIHtcbiAgICBpZiAoXG4gICAgICAhZ2ZtQXRleHQoY29kZSkgfHxcbiAgICAgICFwcmV2aW91c0VtYWlsLmNhbGwoc2VsZiwgc2VsZi5wcmV2aW91cykgfHxcbiAgICAgIHByZXZpb3VzVW5iYWxhbmNlZChzZWxmLmV2ZW50cylcbiAgICApIHtcbiAgICAgIHJldHVybiBub2soY29kZSlcbiAgICB9XG5cbiAgICBlZmZlY3RzLmVudGVyKCdsaXRlcmFsQXV0b2xpbmsnKVxuICAgIGVmZmVjdHMuZW50ZXIoJ2xpdGVyYWxBdXRvbGlua0VtYWlsJylcbiAgICByZXR1cm4gYXRleHQoY29kZSlcbiAgfVxuXG4gIC8qKlxuICAgKiBJbiBlbWFpbCBhdGV4dC5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IGEgY29udGFjdEBleGFtcGxlLm9yZyBiXG4gICAqICAgICAgIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIGF0ZXh0KGNvZGUpIHtcbiAgICBpZiAoZ2ZtQXRleHQoY29kZSkpIHtcbiAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgcmV0dXJuIGF0ZXh0XG4gICAgfVxuXG4gICAgaWYgKGNvZGUgPT09IGNvZGVzLmF0U2lnbikge1xuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICByZXR1cm4gZW1haWxEb21haW5cbiAgICB9XG5cbiAgICByZXR1cm4gbm9rKGNvZGUpXG4gIH1cblxuICAvKipcbiAgICogSW4gZW1haWwgZG9tYWluLlxuICAgKlxuICAgKiBUaGUgcmVmZXJlbmNlIGNvZGUgaXMgYSBiaXQgb3Zlcmx5IGNvbXBsZXggYXMgaXQgaGFuZGxlcyB0aGUgYEBgLCBvZiB3aGljaFxuICAgKiB0aGVyZSBtYXkgYmUganVzdCBvbmUuXG4gICAqIFNvdXJjZTogPGh0dHBzOi8vZ2l0aHViLmNvbS9naXRodWIvY21hcmstZ2ZtL2Jsb2IvZWYxY2ZjYi9leHRlbnNpb25zL2F1dG9saW5rLmMjTDMxOD5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IGEgY29udGFjdEBleGFtcGxlLm9yZyBiXG4gICAqICAgICAgICAgICAgICAgXlxuICAgKiBgYGBcbiAgICpcbiAgICogQHR5cGUge1N0YXRlfVxuICAgKi9cbiAgZnVuY3Rpb24gZW1haWxEb21haW4oY29kZSkge1xuICAgIC8vIERvdCBmb2xsb3dlZCBieSBhbHBoYW51bWVyaWNhbCAobm90IGAtYCBvciBgX2ApLlxuICAgIGlmIChjb2RlID09PSBjb2Rlcy5kb3QpIHtcbiAgICAgIHJldHVybiBlZmZlY3RzLmNoZWNrKFxuICAgICAgICBlbWFpbERvbWFpbkRvdFRyYWlsLFxuICAgICAgICBlbWFpbERvbWFpbkFmdGVyLFxuICAgICAgICBlbWFpbERvbWFpbkRvdFxuICAgICAgKShjb2RlKVxuICAgIH1cblxuICAgIC8vIEFscGhhbnVtZXJpY2FsLCBgLWAsIGFuZCBgX2AuXG4gICAgaWYgKFxuICAgICAgY29kZSA9PT0gY29kZXMuZGFzaCB8fFxuICAgICAgY29kZSA9PT0gY29kZXMudW5kZXJzY29yZSB8fFxuICAgICAgYXNjaWlBbHBoYW51bWVyaWMoY29kZSlcbiAgICApIHtcbiAgICAgIGRhdGEgPSB0cnVlXG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIHJldHVybiBlbWFpbERvbWFpblxuICAgIH1cblxuICAgIC8vIFRvIGRvOiBgL2AgaWYgeG1wcC5cblxuICAgIC8vIE5vdGU6IG5vcm1hbGx5IHdl4oCZZCB0cnVuY2F0ZSB0cmFpbGluZyBwdW5jdHVhdGlvbiBmcm9tIHRoZSBsaW5rLlxuICAgIC8vIEhvd2V2ZXIsIGVtYWlsIGF1dG9saW5rIGxpdGVyYWxzIGNhbm5vdCBjb250YWluIGFueSBvZiB0aG9zZSBtYXJrZXJzLFxuICAgIC8vIGV4Y2VwdCBmb3IgYC5gLCBidXQgdGhhdCBjYW4gb25seSBvY2N1ciBpZiBpdCBpc27igJl0IHRyYWlsaW5nLlxuICAgIC8vIFNvIHdlIGNhbiBpZ25vcmUgdHJ1bmNhdGluZyFcbiAgICByZXR1cm4gZW1haWxEb21haW5BZnRlcihjb2RlKVxuICB9XG5cbiAgLyoqXG4gICAqIEluIGVtYWlsIGRvbWFpbiwgb24gZG90IHRoYXQgaXMgbm90IGEgdHJhaWwuXG4gICAqXG4gICAqIGBgYG1hcmtkb3duXG4gICAqID4gfCBhIGNvbnRhY3RAZXhhbXBsZS5vcmcgYlxuICAgKiAgICAgICAgICAgICAgICAgICAgICBeXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBlbWFpbERvbWFpbkRvdChjb2RlKSB7XG4gICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgZG90ID0gdHJ1ZVxuICAgIHJldHVybiBlbWFpbERvbWFpblxuICB9XG5cbiAgLyoqXG4gICAqIEFmdGVyIGVtYWlsIGRvbWFpbi5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IGEgY29udGFjdEBleGFtcGxlLm9yZyBiXG4gICAqICAgICAgICAgICAgICAgICAgICAgICAgICBeXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBlbWFpbERvbWFpbkFmdGVyKGNvZGUpIHtcbiAgICAvLyBEb21haW4gbXVzdCBub3QgYmUgZW1wdHksIG11c3QgaW5jbHVkZSBhIGRvdCwgYW5kIG11c3QgZW5kIGluIGFscGhhYmV0aWNhbC5cbiAgICAvLyBTb3VyY2U6IDxodHRwczovL2dpdGh1Yi5jb20vZ2l0aHViL2NtYXJrLWdmbS9ibG9iL2VmMWNmY2IvZXh0ZW5zaW9ucy9hdXRvbGluay5jI0wzMzI+LlxuICAgIGlmIChkYXRhICYmIGRvdCAmJiBhc2NpaUFscGhhKHNlbGYucHJldmlvdXMpKSB7XG4gICAgICBlZmZlY3RzLmV4aXQoJ2xpdGVyYWxBdXRvbGlua0VtYWlsJylcbiAgICAgIGVmZmVjdHMuZXhpdCgnbGl0ZXJhbEF1dG9saW5rJylcbiAgICAgIHJldHVybiBvayhjb2RlKVxuICAgIH1cblxuICAgIHJldHVybiBub2soY29kZSlcbiAgfVxufVxuXG4vKipcbiAqIGB3d3dgIGF1dG9saW5rIGxpdGVyYWwuXG4gKlxuICogYGBgbWFya2Rvd25cbiAqID4gfCBhIHd3dy5leGFtcGxlLm9yZyBiXG4gKiAgICAgICBeXl5eXl5eXl5eXl5eXl5cbiAqIGBgYFxuICpcbiAqIEB0aGlzIHtUb2tlbml6ZUNvbnRleHR9XG4gKiBAdHlwZSB7VG9rZW5pemVyfVxuICovXG5mdW5jdGlvbiB0b2tlbml6ZVd3d0F1dG9saW5rKGVmZmVjdHMsIG9rLCBub2spIHtcbiAgY29uc3Qgc2VsZiA9IHRoaXNcblxuICByZXR1cm4gd3d3U3RhcnRcblxuICAvKipcbiAgICogU3RhcnQgb2Ygd3d3IGF1dG9saW5rIGxpdGVyYWwuXG4gICAqXG4gICAqIGBgYG1hcmtkb3duXG4gICAqID4gfCB3d3cuZXhhbXBsZS5jb20vYT9iI2NcbiAgICogICAgIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIHd3d1N0YXJ0KGNvZGUpIHtcbiAgICBpZiAoXG4gICAgICAoY29kZSAhPT0gY29kZXMudXBwZXJjYXNlVyAmJiBjb2RlICE9PSBjb2Rlcy5sb3dlcmNhc2VXKSB8fFxuICAgICAgIXByZXZpb3VzV3d3LmNhbGwoc2VsZiwgc2VsZi5wcmV2aW91cykgfHxcbiAgICAgIHByZXZpb3VzVW5iYWxhbmNlZChzZWxmLmV2ZW50cylcbiAgICApIHtcbiAgICAgIHJldHVybiBub2soY29kZSlcbiAgICB9XG5cbiAgICBlZmZlY3RzLmVudGVyKCdsaXRlcmFsQXV0b2xpbmsnKVxuICAgIGVmZmVjdHMuZW50ZXIoJ2xpdGVyYWxBdXRvbGlua1d3dycpXG4gICAgLy8gTm90ZTogd2UgKmNoZWNrKiwgc28gd2UgY2FuIGRpc2NhcmQgdGhlIGB3d3cuYCB3ZSBwYXJzZWQuXG4gICAgLy8gSWYgaXQgd29ya2VkLCB3ZSBjb25zaWRlciBpdCBhcyBhIHBhcnQgb2YgdGhlIGRvbWFpbi5cbiAgICByZXR1cm4gZWZmZWN0cy5jaGVjayhcbiAgICAgIHd3d1ByZWZpeCxcbiAgICAgIGVmZmVjdHMuYXR0ZW1wdChkb21haW4sIGVmZmVjdHMuYXR0ZW1wdChwYXRoLCB3d3dBZnRlciksIG5vayksXG4gICAgICBub2tcbiAgICApKGNvZGUpXG4gIH1cblxuICAvKipcbiAgICogQWZ0ZXIgYSB3d3cgYXV0b2xpbmsgbGl0ZXJhbC5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IHd3dy5leGFtcGxlLmNvbS9hP2IjY1xuICAgKiAgICAgICAgICAgICAgICAgICAgICAgICAgXlxuICAgKiBgYGBcbiAgICpcbiAgICogQHR5cGUge1N0YXRlfVxuICAgKi9cbiAgZnVuY3Rpb24gd3d3QWZ0ZXIoY29kZSkge1xuICAgIGVmZmVjdHMuZXhpdCgnbGl0ZXJhbEF1dG9saW5rV3d3JylcbiAgICBlZmZlY3RzLmV4aXQoJ2xpdGVyYWxBdXRvbGluaycpXG4gICAgcmV0dXJuIG9rKGNvZGUpXG4gIH1cbn1cblxuLyoqXG4gKiBQcm90b2NvbCBhdXRvbGluayBsaXRlcmFsLlxuICpcbiAqIGBgYG1hcmtkb3duXG4gKiA+IHwgYSBodHRwczovL2V4YW1wbGUub3JnIGJcbiAqICAgICAgIF5eXl5eXl5eXl5eXl5eXl5eXl5cbiAqIGBgYFxuICpcbiAqIEB0aGlzIHtUb2tlbml6ZUNvbnRleHR9XG4gKiBAdHlwZSB7VG9rZW5pemVyfVxuICovXG5mdW5jdGlvbiB0b2tlbml6ZVByb3RvY29sQXV0b2xpbmsoZWZmZWN0cywgb2ssIG5vaykge1xuICBjb25zdCBzZWxmID0gdGhpc1xuICBsZXQgYnVmZmVyID0gJydcbiAgbGV0IHNlZW4gPSBmYWxzZVxuXG4gIHJldHVybiBwcm90b2NvbFN0YXJ0XG5cbiAgLyoqXG4gICAqIFN0YXJ0IG9mIHByb3RvY29sIGF1dG9saW5rIGxpdGVyYWwuXG4gICAqXG4gICAqIGBgYG1hcmtkb3duXG4gICAqID4gfCBodHRwczovL2V4YW1wbGUuY29tL2E/YiNjXG4gICAqICAgICBeXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBwcm90b2NvbFN0YXJ0KGNvZGUpIHtcbiAgICBpZiAoXG4gICAgICAoY29kZSA9PT0gY29kZXMudXBwZXJjYXNlSCB8fCBjb2RlID09PSBjb2Rlcy5sb3dlcmNhc2VIKSAmJlxuICAgICAgcHJldmlvdXNQcm90b2NvbC5jYWxsKHNlbGYsIHNlbGYucHJldmlvdXMpICYmXG4gICAgICAhcHJldmlvdXNVbmJhbGFuY2VkKHNlbGYuZXZlbnRzKVxuICAgICkge1xuICAgICAgZWZmZWN0cy5lbnRlcignbGl0ZXJhbEF1dG9saW5rJylcbiAgICAgIGVmZmVjdHMuZW50ZXIoJ2xpdGVyYWxBdXRvbGlua0h0dHAnKVxuICAgICAgYnVmZmVyICs9IFN0cmluZy5mcm9tQ29kZVBvaW50KGNvZGUpXG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIHJldHVybiBwcm90b2NvbFByZWZpeEluc2lkZVxuICAgIH1cblxuICAgIHJldHVybiBub2soY29kZSlcbiAgfVxuXG4gIC8qKlxuICAgKiBJbiBwcm90b2NvbC5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IGh0dHBzOi8vZXhhbXBsZS5jb20vYT9iI2NcbiAgICogICAgIF5eXl5eXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBwcm90b2NvbFByZWZpeEluc2lkZShjb2RlKSB7XG4gICAgLy8gYDVgIGlzIHNpemUgb2YgYGh0dHBzYFxuICAgIGlmIChhc2NpaUFscGhhKGNvZGUpICYmIGJ1ZmZlci5sZW5ndGggPCA1KSB7XG4gICAgICAvLyBAdHMtZXhwZWN0LWVycm9yOiBkZWZpbml0ZWx5IG51bWJlci5cbiAgICAgIGJ1ZmZlciArPSBTdHJpbmcuZnJvbUNvZGVQb2ludChjb2RlKVxuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICByZXR1cm4gcHJvdG9jb2xQcmVmaXhJbnNpZGVcbiAgICB9XG5cbiAgICBpZiAoY29kZSA9PT0gY29kZXMuY29sb24pIHtcbiAgICAgIGNvbnN0IHByb3RvY29sID0gYnVmZmVyLnRvTG93ZXJDYXNlKClcblxuICAgICAgaWYgKHByb3RvY29sID09PSAnaHR0cCcgfHwgcHJvdG9jb2wgPT09ICdodHRwcycpIHtcbiAgICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICAgIHJldHVybiBwcm90b2NvbFNsYXNoZXNJbnNpZGVcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4gbm9rKGNvZGUpXG4gIH1cblxuICAvKipcbiAgICogSW4gc2xhc2hlcy5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IGh0dHBzOi8vZXhhbXBsZS5jb20vYT9iI2NcbiAgICogICAgICAgICAgIF5eXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBwcm90b2NvbFNsYXNoZXNJbnNpZGUoY29kZSkge1xuICAgIGlmIChjb2RlID09PSBjb2Rlcy5zbGFzaCkge1xuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG5cbiAgICAgIGlmIChzZWVuKSB7XG4gICAgICAgIHJldHVybiBhZnRlclByb3RvY29sXG4gICAgICB9XG5cbiAgICAgIHNlZW4gPSB0cnVlXG4gICAgICByZXR1cm4gcHJvdG9jb2xTbGFzaGVzSW5zaWRlXG4gICAgfVxuXG4gICAgcmV0dXJuIG5vayhjb2RlKVxuICB9XG5cbiAgLyoqXG4gICAqIEFmdGVyIHByb3RvY29sLCBiZWZvcmUgZG9tYWluLlxuICAgKlxuICAgKiBgYGBtYXJrZG93blxuICAgKiA+IHwgaHR0cHM6Ly9leGFtcGxlLmNvbS9hP2IjY1xuICAgKiAgICAgICAgICAgICBeXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBhZnRlclByb3RvY29sKGNvZGUpIHtcbiAgICAvLyBUbyBkbzogdGhpcyBpcyBkaWZmZXJlbnQgZnJvbSBgbWFya2Rvd24tcnNgOlxuICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS93b29vcm0vbWFya2Rvd24tcnMvYmxvYi9iM2E5MjFjNzYxMzA5YWUwMGE1MWZlMzQ4ZDhhNDNhZGJjNTRiNTE4L3NyYy9jb25zdHJ1Y3QvZ2ZtX2F1dG9saW5rX2xpdGVyYWwucnMjTDE3Mi1MMTgyXG4gICAgcmV0dXJuIGNvZGUgPT09IGNvZGVzLmVvZiB8fFxuICAgICAgYXNjaWlDb250cm9sKGNvZGUpIHx8XG4gICAgICBtYXJrZG93bkxpbmVFbmRpbmdPclNwYWNlKGNvZGUpIHx8XG4gICAgICB1bmljb2RlV2hpdGVzcGFjZShjb2RlKSB8fFxuICAgICAgdW5pY29kZVB1bmN0dWF0aW9uKGNvZGUpXG4gICAgICA/IG5vayhjb2RlKVxuICAgICAgOiBlZmZlY3RzLmF0dGVtcHQoZG9tYWluLCBlZmZlY3RzLmF0dGVtcHQocGF0aCwgcHJvdG9jb2xBZnRlciksIG5vaykoY29kZSlcbiAgfVxuXG4gIC8qKlxuICAgKiBBZnRlciBhIHByb3RvY29sIGF1dG9saW5rIGxpdGVyYWwuXG4gICAqXG4gICAqIGBgYG1hcmtkb3duXG4gICAqID4gfCBodHRwczovL2V4YW1wbGUuY29tL2E/YiNjXG4gICAqICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXlxuICAgKiBgYGBcbiAgICpcbiAgICogQHR5cGUge1N0YXRlfVxuICAgKi9cbiAgZnVuY3Rpb24gcHJvdG9jb2xBZnRlcihjb2RlKSB7XG4gICAgZWZmZWN0cy5leGl0KCdsaXRlcmFsQXV0b2xpbmtIdHRwJylcbiAgICBlZmZlY3RzLmV4aXQoJ2xpdGVyYWxBdXRvbGluaycpXG4gICAgcmV0dXJuIG9rKGNvZGUpXG4gIH1cbn1cblxuLyoqXG4gKiBgd3d3YCBwcmVmaXguXG4gKlxuICogYGBgbWFya2Rvd25cbiAqID4gfCBhIHd3dy5leGFtcGxlLm9yZyBiXG4gKiAgICAgICBeXl5eXG4gKiBgYGBcbiAqXG4gKiBAdGhpcyB7VG9rZW5pemVDb250ZXh0fVxuICogQHR5cGUge1Rva2VuaXplcn1cbiAqL1xuZnVuY3Rpb24gdG9rZW5pemVXd3dQcmVmaXgoZWZmZWN0cywgb2ssIG5vaykge1xuICBsZXQgc2l6ZSA9IDBcblxuICByZXR1cm4gd3d3UHJlZml4SW5zaWRlXG5cbiAgLyoqXG4gICAqIEluIHd3dyBwcmVmaXguXG4gICAqXG4gICAqIGBgYG1hcmtkb3duXG4gICAqID4gfCB3d3cuZXhhbXBsZS5jb21cbiAgICogICAgIF5eXl5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIHd3d1ByZWZpeEluc2lkZShjb2RlKSB7XG4gICAgaWYgKChjb2RlID09PSBjb2Rlcy51cHBlcmNhc2VXIHx8IGNvZGUgPT09IGNvZGVzLmxvd2VyY2FzZVcpICYmIHNpemUgPCAzKSB7XG4gICAgICBzaXplKytcbiAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgcmV0dXJuIHd3d1ByZWZpeEluc2lkZVxuICAgIH1cblxuICAgIGlmIChjb2RlID09PSBjb2Rlcy5kb3QgJiYgc2l6ZSA9PT0gMykge1xuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICByZXR1cm4gd3d3UHJlZml4QWZ0ZXJcbiAgICB9XG5cbiAgICByZXR1cm4gbm9rKGNvZGUpXG4gIH1cblxuICAvKipcbiAgICogQWZ0ZXIgd3d3IHByZWZpeC5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IHd3dy5leGFtcGxlLmNvbVxuICAgKiAgICAgICAgIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIHd3d1ByZWZpeEFmdGVyKGNvZGUpIHtcbiAgICAvLyBJZiB0aGVyZSBpcyAqYW55dGhpbmcqLCB3ZSBjYW4gbGluay5cbiAgICByZXR1cm4gY29kZSA9PT0gY29kZXMuZW9mID8gbm9rKGNvZGUpIDogb2soY29kZSlcbiAgfVxufVxuXG4vKipcbiAqIERvbWFpbi5cbiAqXG4gKiBgYGBtYXJrZG93blxuICogPiB8IGEgaHR0cHM6Ly9leGFtcGxlLm9yZyBiXG4gKiAgICAgICAgICAgICAgIF5eXl5eXl5eXl5eXG4gKiBgYGBcbiAqXG4gKiBAdGhpcyB7VG9rZW5pemVDb250ZXh0fVxuICogQHR5cGUge1Rva2VuaXplcn1cbiAqL1xuZnVuY3Rpb24gdG9rZW5pemVEb21haW4oZWZmZWN0cywgb2ssIG5vaykge1xuICAvKiogQHR5cGUge2Jvb2xlYW4gfCB1bmRlZmluZWR9ICovXG4gIGxldCB1bmRlcnNjb3JlSW5MYXN0U2VnbWVudFxuICAvKiogQHR5cGUge2Jvb2xlYW4gfCB1bmRlZmluZWR9ICovXG4gIGxldCB1bmRlcnNjb3JlSW5MYXN0TGFzdFNlZ21lbnRcbiAgLyoqIEB0eXBlIHtib29sZWFuIHwgdW5kZWZpbmVkfSAqL1xuICBsZXQgc2VlblxuXG4gIHJldHVybiBkb21haW5JbnNpZGVcblxuICAvKipcbiAgICogSW4gZG9tYWluLlxuICAgKlxuICAgKiBgYGBtYXJrZG93blxuICAgKiA+IHwgaHR0cHM6Ly9leGFtcGxlLmNvbS9hXG4gICAqICAgICAgICAgICAgIF5eXl5eXl5eXl5eXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBkb21haW5JbnNpZGUoY29kZSkge1xuICAgIC8vIENoZWNrIHdoZXRoZXIgdGhpcyBtYXJrZXIsIHdoaWNoIGlzIGEgdHJhaWxpbmcgcHVuY3R1YXRpb25cbiAgICAvLyBtYXJrZXIsIG9wdGlvbmFsbHkgZm9sbG93ZWQgYnkgbW9yZSB0cmFpbGluZyBtYXJrZXJzLCBhbmQgdGhlblxuICAgIC8vIGZvbGxvd2VkIGJ5IGFuIGVuZC5cbiAgICBpZiAoY29kZSA9PT0gY29kZXMuZG90IHx8IGNvZGUgPT09IGNvZGVzLnVuZGVyc2NvcmUpIHtcbiAgICAgIHJldHVybiBlZmZlY3RzLmNoZWNrKHRyYWlsLCBkb21haW5BZnRlciwgZG9tYWluQXRQdW5jdHVhdGlvbikoY29kZSlcbiAgICB9XG5cbiAgICAvLyBHSCBkb2N1bWVudHMgdGhhdCBvbmx5IGFscGhhbnVtZXJpY3MgKG90aGVyIHRoYW4gYC1gLCBgLmAsIGFuZCBgX2ApIGNhblxuICAgIC8vIG9jY3VyLCB3aGljaCBzb3VuZHMgbGlrZSBBU0NJSSBvbmx5LCBidXQgdGhleSBhbHNvIHN1cHBvcnQgYHd3dy7pu57nnIsuY29tYCxcbiAgICAvLyBzbyB0aGF04oCZcyBVbmljb2RlLlxuICAgIC8vIEluc3RlYWQgb2Ygc29tZSBuZXcgcHJvZHVjdGlvbiBmb3IgVW5pY29kZSBhbHBoYW51bWVyaWNzLCBtYXJrZG93blxuICAgIC8vIGFscmVhZHkgaGFzIHRoYXQgZm9yIFVuaWNvZGUgcHVuY3R1YXRpb24gYW5kIHdoaXRlc3BhY2UsIHNvIHVzZSB0aG9zZS5cbiAgICAvLyBTb3VyY2U6IDxodHRwczovL2dpdGh1Yi5jb20vZ2l0aHViL2NtYXJrLWdmbS9ibG9iL2VmMWNmY2IvZXh0ZW5zaW9ucy9hdXRvbGluay5jI0wxMj4uXG4gICAgaWYgKFxuICAgICAgY29kZSA9PT0gY29kZXMuZW9mIHx8XG4gICAgICBtYXJrZG93bkxpbmVFbmRpbmdPclNwYWNlKGNvZGUpIHx8XG4gICAgICB1bmljb2RlV2hpdGVzcGFjZShjb2RlKSB8fFxuICAgICAgKGNvZGUgIT09IGNvZGVzLmRhc2ggJiYgdW5pY29kZVB1bmN0dWF0aW9uKGNvZGUpKVxuICAgICkge1xuICAgICAgcmV0dXJuIGRvbWFpbkFmdGVyKGNvZGUpXG4gICAgfVxuXG4gICAgc2VlbiA9IHRydWVcbiAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICByZXR1cm4gZG9tYWluSW5zaWRlXG4gIH1cblxuICAvKipcbiAgICogSW4gZG9tYWluLCBhdCBwb3RlbnRpYWwgdHJhaWxpbmcgcHVuY3R1YXRpb24sIHRoYXQgd2FzIG5vdCB0cmFpbGluZy5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IGh0dHBzOi8vZXhhbXBsZS5jb21cbiAgICogICAgICAgICAgICAgICAgICAgIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIGRvbWFpbkF0UHVuY3R1YXRpb24oY29kZSkge1xuICAgIC8vIFRoZXJlIGlzIGFuIHVuZGVyc2NvcmUgaW4gdGhlIGxhc3Qgc2VnbWVudCBvZiB0aGUgZG9tYWluXG4gICAgaWYgKGNvZGUgPT09IGNvZGVzLnVuZGVyc2NvcmUpIHtcbiAgICAgIHVuZGVyc2NvcmVJbkxhc3RTZWdtZW50ID0gdHJ1ZVxuICAgIH1cbiAgICAvLyBPdGhlcndpc2UsIGl04oCZcyBhIGAuYDogc2F2ZSB0aGUgbGFzdCBzZWdtZW50IHVuZGVyc2NvcmUgaW4gdGhlXG4gICAgLy8gcGVudWx0aW1hdGUgc2VnbWVudCBzbG90LlxuICAgIGVsc2Uge1xuICAgICAgdW5kZXJzY29yZUluTGFzdExhc3RTZWdtZW50ID0gdW5kZXJzY29yZUluTGFzdFNlZ21lbnRcbiAgICAgIHVuZGVyc2NvcmVJbkxhc3RTZWdtZW50ID0gdW5kZWZpbmVkXG4gICAgfVxuXG4gICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgcmV0dXJuIGRvbWFpbkluc2lkZVxuICB9XG5cbiAgLyoqXG4gICAqIEFmdGVyIGRvbWFpbi5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IGh0dHBzOi8vZXhhbXBsZS5jb20vYVxuICAgKiAgICAgICAgICAgICAgICAgICAgICAgIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX0gKi9cbiAgZnVuY3Rpb24gZG9tYWluQWZ0ZXIoY29kZSkge1xuICAgIC8vIE5vdGU6IHRoYXTigJlzIEdIIHNheXMgYSBkb3QgaXMgbmVlZGVkLCBidXQgaXTigJlzIG5vdCB0cnVlOlxuICAgIC8vIDxodHRwczovL2dpdGh1Yi5jb20vZ2l0aHViL2NtYXJrLWdmbS9pc3N1ZXMvMjc5PlxuICAgIGlmICh1bmRlcnNjb3JlSW5MYXN0TGFzdFNlZ21lbnQgfHwgdW5kZXJzY29yZUluTGFzdFNlZ21lbnQgfHwgIXNlZW4pIHtcbiAgICAgIHJldHVybiBub2soY29kZSlcbiAgICB9XG5cbiAgICByZXR1cm4gb2soY29kZSlcbiAgfVxufVxuXG4vKipcbiAqIFBhdGguXG4gKlxuICogYGBgbWFya2Rvd25cbiAqID4gfCBhIGh0dHBzOi8vZXhhbXBsZS5vcmcvc3R1ZmYgYlxuICogICAgICAgICAgICAgICAgICAgICAgICAgIF5eXl5eXlxuICogYGBgXG4gKlxuICogQHRoaXMge1Rva2VuaXplQ29udGV4dH1cbiAqIEB0eXBlIHtUb2tlbml6ZXJ9XG4gKi9cbmZ1bmN0aW9uIHRva2VuaXplUGF0aChlZmZlY3RzLCBvaykge1xuICBsZXQgc2l6ZU9wZW4gPSAwXG4gIGxldCBzaXplQ2xvc2UgPSAwXG5cbiAgcmV0dXJuIHBhdGhJbnNpZGVcblxuICAvKipcbiAgICogSW4gcGF0aC5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IGh0dHBzOi8vZXhhbXBsZS5jb20vYVxuICAgKiAgICAgICAgICAgICAgICAgICAgICAgIF5eXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBwYXRoSW5zaWRlKGNvZGUpIHtcbiAgICBpZiAoY29kZSA9PT0gY29kZXMubGVmdFBhcmVudGhlc2lzKSB7XG4gICAgICBzaXplT3BlbisrXG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIHJldHVybiBwYXRoSW5zaWRlXG4gICAgfVxuXG4gICAgLy8gVG8gZG86IGBtYXJrZG93bi1yc2AgYWxzbyBuZWVkcyB0aGlzLlxuICAgIC8vIElmIHRoaXMgaXMgYSBwYXJlbiwgYW5kIHRoZXJlIGFyZSBsZXNzIGNsb3NpbmdzIHRoYW4gb3BlbmluZ3MsXG4gICAgLy8gd2UgZG9u4oCZdCBjaGVjayBmb3IgYSB0cmFpbC5cbiAgICBpZiAoY29kZSA9PT0gY29kZXMucmlnaHRQYXJlbnRoZXNpcyAmJiBzaXplQ2xvc2UgPCBzaXplT3Blbikge1xuICAgICAgcmV0dXJuIHBhdGhBdFB1bmN0dWF0aW9uKGNvZGUpXG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgd2hldGhlciB0aGlzIHRyYWlsaW5nIHB1bmN0dWF0aW9uIG1hcmtlciBpcyBvcHRpb25hbGx5XG4gICAgLy8gZm9sbG93ZWQgYnkgbW9yZSB0cmFpbGluZyBtYXJrZXJzLCBhbmQgdGhlbiBmb2xsb3dlZFxuICAgIC8vIGJ5IGFuIGVuZC5cbiAgICBpZiAoXG4gICAgICBjb2RlID09PSBjb2Rlcy5leGNsYW1hdGlvbk1hcmsgfHxcbiAgICAgIGNvZGUgPT09IGNvZGVzLnF1b3RhdGlvbk1hcmsgfHxcbiAgICAgIGNvZGUgPT09IGNvZGVzLmFtcGVyc2FuZCB8fFxuICAgICAgY29kZSA9PT0gY29kZXMuYXBvc3Ryb3BoZSB8fFxuICAgICAgY29kZSA9PT0gY29kZXMucmlnaHRQYXJlbnRoZXNpcyB8fFxuICAgICAgY29kZSA9PT0gY29kZXMuYXN0ZXJpc2sgfHxcbiAgICAgIGNvZGUgPT09IGNvZGVzLmNvbW1hIHx8XG4gICAgICBjb2RlID09PSBjb2Rlcy5kb3QgfHxcbiAgICAgIGNvZGUgPT09IGNvZGVzLmNvbG9uIHx8XG4gICAgICBjb2RlID09PSBjb2Rlcy5zZW1pY29sb24gfHxcbiAgICAgIGNvZGUgPT09IGNvZGVzLmxlc3NUaGFuIHx8XG4gICAgICBjb2RlID09PSBjb2Rlcy5xdWVzdGlvbk1hcmsgfHxcbiAgICAgIGNvZGUgPT09IGNvZGVzLnJpZ2h0U3F1YXJlQnJhY2tldCB8fFxuICAgICAgY29kZSA9PT0gY29kZXMudW5kZXJzY29yZSB8fFxuICAgICAgY29kZSA9PT0gY29kZXMudGlsZGVcbiAgICApIHtcbiAgICAgIHJldHVybiBlZmZlY3RzLmNoZWNrKHRyYWlsLCBvaywgcGF0aEF0UHVuY3R1YXRpb24pKGNvZGUpXG4gICAgfVxuXG4gICAgaWYgKFxuICAgICAgY29kZSA9PT0gY29kZXMuZW9mIHx8XG4gICAgICBtYXJrZG93bkxpbmVFbmRpbmdPclNwYWNlKGNvZGUpIHx8XG4gICAgICB1bmljb2RlV2hpdGVzcGFjZShjb2RlKVxuICAgICkge1xuICAgICAgcmV0dXJuIG9rKGNvZGUpXG4gICAgfVxuXG4gICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgcmV0dXJuIHBhdGhJbnNpZGVcbiAgfVxuXG4gIC8qKlxuICAgKiBJbiBwYXRoLCBhdCBwb3RlbnRpYWwgdHJhaWxpbmcgcHVuY3R1YXRpb24sIHRoYXQgd2FzIG5vdCB0cmFpbGluZy5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IGh0dHBzOi8vZXhhbXBsZS5jb20vYVwiYlxuICAgKiAgICAgICAgICAgICAgICAgICAgICAgICAgXlxuICAgKiBgYGBcbiAgICpcbiAgICogQHR5cGUge1N0YXRlfVxuICAgKi9cbiAgZnVuY3Rpb24gcGF0aEF0UHVuY3R1YXRpb24oY29kZSkge1xuICAgIC8vIENvdW50IGNsb3NpbmcgcGFyZW5zLlxuICAgIGlmIChjb2RlID09PSBjb2Rlcy5yaWdodFBhcmVudGhlc2lzKSB7XG4gICAgICBzaXplQ2xvc2UrK1xuICAgIH1cblxuICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgIHJldHVybiBwYXRoSW5zaWRlXG4gIH1cbn1cblxuLyoqXG4gKiBUcmFpbC5cbiAqXG4gKiBUaGlzIGNhbGxzIGBva2AgaWYgdGhpcyAqaXMqIHRoZSB0cmFpbCwgZm9sbG93ZWQgYnkgYW4gZW5kLCB3aGljaCBtZWFuc1xuICogdGhlIGVudGlyZSB0cmFpbCBpcyBub3QgcGFydCBvZiB0aGUgbGluay5cbiAqIEl0IGNhbGxzIGBub2tgIGlmIHRoaXMgKmlzKiBwYXJ0IG9mIHRoZSBsaW5rLlxuICpcbiAqIGBgYG1hcmtkb3duXG4gKiA+IHwgaHR0cHM6Ly9leGFtcGxlLmNvbVwiKS5cbiAqICAgICAgICAgICAgICAgICAgICAgICAgXl5eXG4gKiBgYGBcbiAqXG4gKiBAdGhpcyB7VG9rZW5pemVDb250ZXh0fVxuICogQHR5cGUge1Rva2VuaXplcn1cbiAqL1xuZnVuY3Rpb24gdG9rZW5pemVUcmFpbChlZmZlY3RzLCBvaywgbm9rKSB7XG4gIHJldHVybiB0cmFpbFxuXG4gIC8qKlxuICAgKiBJbiB0cmFpbCBvZiBkb21haW4gb3IgcGF0aC5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IGh0dHBzOi8vZXhhbXBsZS5jb21cIikuXG4gICAqICAgICAgICAgICAgICAgICAgICAgICAgXlxuICAgKiBgYGBcbiAgICpcbiAgICogQHR5cGUge1N0YXRlfVxuICAgKi9cbiAgZnVuY3Rpb24gdHJhaWwoY29kZSkge1xuICAgIC8vIFJlZ3VsYXIgdHJhaWxpbmcgcHVuY3R1YXRpb24uXG4gICAgaWYgKFxuICAgICAgY29kZSA9PT0gY29kZXMuZXhjbGFtYXRpb25NYXJrIHx8XG4gICAgICBjb2RlID09PSBjb2Rlcy5xdW90YXRpb25NYXJrIHx8XG4gICAgICBjb2RlID09PSBjb2Rlcy5hcG9zdHJvcGhlIHx8XG4gICAgICBjb2RlID09PSBjb2Rlcy5yaWdodFBhcmVudGhlc2lzIHx8XG4gICAgICBjb2RlID09PSBjb2Rlcy5hc3RlcmlzayB8fFxuICAgICAgY29kZSA9PT0gY29kZXMuY29tbWEgfHxcbiAgICAgIGNvZGUgPT09IGNvZGVzLmRvdCB8fFxuICAgICAgY29kZSA9PT0gY29kZXMuY29sb24gfHxcbiAgICAgIGNvZGUgPT09IGNvZGVzLnNlbWljb2xvbiB8fFxuICAgICAgY29kZSA9PT0gY29kZXMucXVlc3Rpb25NYXJrIHx8XG4gICAgICBjb2RlID09PSBjb2Rlcy51bmRlcnNjb3JlIHx8XG4gICAgICBjb2RlID09PSBjb2Rlcy50aWxkZVxuICAgICkge1xuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICByZXR1cm4gdHJhaWxcbiAgICB9XG5cbiAgICAvLyBgJmAgZm9sbG93ZWQgYnkgb25lIG9yIG1vcmUgYWxwaGFiZXRpY2FscyBhbmQgdGhlbiBhIGA7YCwgaXNcbiAgICAvLyBhcyBhIHdob2xlIGNvbnNpZGVyZWQgYXMgdHJhaWxpbmcgcHVuY3R1YXRpb24uXG4gICAgLy8gSW4gYWxsIG90aGVyIGNhc2VzLCBpdCBpcyBjb25zaWRlcmVkIGFzIGNvbnRpbnVhdGlvbiBvZiB0aGUgVVJMLlxuICAgIGlmIChjb2RlID09PSBjb2Rlcy5hbXBlcnNhbmQpIHtcbiAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgcmV0dXJuIHRyYWlsQ2hhclJlZlN0YXJ0XG4gICAgfVxuXG4gICAgLy8gTmVlZGVkIGJlY2F1c2Ugd2UgYWxsb3cgbGl0ZXJhbHMgYWZ0ZXIgYFtgLCBhcyB3ZSBmaXg6XG4gICAgLy8gPGh0dHBzOi8vZ2l0aHViLmNvbS9naXRodWIvY21hcmstZ2ZtL2lzc3Vlcy8yNzg+LlxuICAgIC8vIENoZWNrIHRoYXQgaXQgaXMgbm90IGZvbGxvd2VkIGJ5IGAoYCBvciBgW2AuXG4gICAgaWYgKGNvZGUgPT09IGNvZGVzLnJpZ2h0U3F1YXJlQnJhY2tldCkge1xuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICByZXR1cm4gdHJhaWxCcmFja2V0QWZ0ZXJcbiAgICB9XG5cbiAgICBpZiAoXG4gICAgICAvLyBgPGAgaXMgYW4gZW5kLlxuICAgICAgY29kZSA9PT0gY29kZXMubGVzc1RoYW4gfHxcbiAgICAgIC8vIFNvIGlzIHdoaXRlc3BhY2UuXG4gICAgICBjb2RlID09PSBjb2Rlcy5lb2YgfHxcbiAgICAgIG1hcmtkb3duTGluZUVuZGluZ09yU3BhY2UoY29kZSkgfHxcbiAgICAgIHVuaWNvZGVXaGl0ZXNwYWNlKGNvZGUpXG4gICAgKSB7XG4gICAgICByZXR1cm4gb2soY29kZSlcbiAgICB9XG5cbiAgICByZXR1cm4gbm9rKGNvZGUpXG4gIH1cblxuICAvKipcbiAgICogSW4gdHJhaWwsIGFmdGVyIGBdYC5cbiAgICpcbiAgICogPiDwn5GJICoqTm90ZSoqOiB0aGlzIGRldmlhdGVzIGZyb20gYGNtYXJrLWdmbWAgdG8gZml4IGEgYnVnLlxuICAgKiA+IFNlZSBlbmQgb2YgPGh0dHBzOi8vZ2l0aHViLmNvbS9naXRodWIvY21hcmstZ2ZtL2lzc3Vlcy8yNzg+IGZvciBtb3JlLlxuICAgKlxuICAgKiBgYGBtYXJrZG93blxuICAgKiA+IHwgaHR0cHM6Ly9leGFtcGxlLmNvbV0oXG4gICAqICAgICAgICAgICAgICAgICAgICAgICAgIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIHRyYWlsQnJhY2tldEFmdGVyKGNvZGUpIHtcbiAgICAvLyBXaGl0ZXNwYWNlIG9yIHNvbWV0aGluZyB0aGF0IGNvdWxkIHN0YXJ0IGEgcmVzb3VyY2Ugb3IgcmVmZXJlbmNlIGlzIHRoZSBlbmQuXG4gICAgLy8gU3dpdGNoIGJhY2sgdG8gdHJhaWwgb3RoZXJ3aXNlLlxuICAgIGlmIChcbiAgICAgIGNvZGUgPT09IGNvZGVzLmVvZiB8fFxuICAgICAgY29kZSA9PT0gY29kZXMubGVmdFBhcmVudGhlc2lzIHx8XG4gICAgICBjb2RlID09PSBjb2Rlcy5sZWZ0U3F1YXJlQnJhY2tldCB8fFxuICAgICAgbWFya2Rvd25MaW5lRW5kaW5nT3JTcGFjZShjb2RlKSB8fFxuICAgICAgdW5pY29kZVdoaXRlc3BhY2UoY29kZSlcbiAgICApIHtcbiAgICAgIHJldHVybiBvayhjb2RlKVxuICAgIH1cblxuICAgIHJldHVybiB0cmFpbChjb2RlKVxuICB9XG5cbiAgLyoqXG4gICAqIEluIGNoYXJhY3Rlci1yZWZlcmVuY2UgbGlrZSB0cmFpbCwgYWZ0ZXIgYCZgLlxuICAgKlxuICAgKiBgYGBtYXJrZG93blxuICAgKiA+IHwgaHR0cHM6Ly9leGFtcGxlLmNvbSZhbXA7KS5cbiAgICogICAgICAgICAgICAgICAgICAgICAgICAgXlxuICAgKiBgYGBcbiAgICpcbiAgICogQHR5cGUge1N0YXRlfVxuICAgKi9cbiAgZnVuY3Rpb24gdHJhaWxDaGFyUmVmU3RhcnQoY29kZSkge1xuICAgIC8vIFdoZW4gbm9uLWFscGhhLCBpdOKAmXMgbm90IGEgdHJhaWwuXG4gICAgcmV0dXJuIGFzY2lpQWxwaGEoY29kZSkgPyB0cmFpbENoYXJSZWZJbnNpZGUoY29kZSkgOiBub2soY29kZSlcbiAgfVxuXG4gIC8qKlxuICAgKiBJbiBjaGFyYWN0ZXItcmVmZXJlbmNlIGxpa2UgdHJhaWwuXG4gICAqXG4gICAqIGBgYG1hcmtkb3duXG4gICAqID4gfCBodHRwczovL2V4YW1wbGUuY29tJmFtcDspLlxuICAgKiAgICAgICAgICAgICAgICAgICAgICAgICBeXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiB0cmFpbENoYXJSZWZJbnNpZGUoY29kZSkge1xuICAgIC8vIFN3aXRjaCBiYWNrIHRvIHRyYWlsIGlmIHRoaXMgaXMgd2VsbC1mb3JtZWQuXG4gICAgaWYgKGNvZGUgPT09IGNvZGVzLnNlbWljb2xvbikge1xuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICByZXR1cm4gdHJhaWxcbiAgICB9XG5cbiAgICBpZiAoYXNjaWlBbHBoYShjb2RlKSkge1xuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICByZXR1cm4gdHJhaWxDaGFyUmVmSW5zaWRlXG4gICAgfVxuXG4gICAgLy8gSXTigJlzIG5vdCBhIHRyYWlsLlxuICAgIHJldHVybiBub2soY29kZSlcbiAgfVxufVxuXG4vKipcbiAqIERvdCBpbiBlbWFpbCBkb21haW4gdHJhaWwuXG4gKlxuICogVGhpcyBjYWxscyBgb2tgIGlmIHRoaXMgKmlzKiB0aGUgdHJhaWwsIGZvbGxvd2VkIGJ5IGFuIGVuZCwgd2hpY2ggbWVhbnNcbiAqIHRoZSB0cmFpbCBpcyBub3QgcGFydCBvZiB0aGUgbGluay5cbiAqIEl0IGNhbGxzIGBub2tgIGlmIHRoaXMgKmlzKiBwYXJ0IG9mIHRoZSBsaW5rLlxuICpcbiAqIGBgYG1hcmtkb3duXG4gKiA+IHwgY29udGFjdEBleGFtcGxlLm9yZy5cbiAqICAgICAgICAgICAgICAgICAgICAgICAgXlxuICogYGBgXG4gKlxuICogQHRoaXMge1Rva2VuaXplQ29udGV4dH1cbiAqIEB0eXBlIHtUb2tlbml6ZXJ9XG4gKi9cbmZ1bmN0aW9uIHRva2VuaXplRW1haWxEb21haW5Eb3RUcmFpbChlZmZlY3RzLCBvaywgbm9rKSB7XG4gIHJldHVybiBzdGFydFxuXG4gIC8qKlxuICAgKiBEb3QuXG4gICAqXG4gICAqIGBgYG1hcmtkb3duXG4gICAqID4gfCBjb250YWN0QGV4YW1wbGUub3JnLlxuICAgKiAgICAgICAgICAgICAgICAgICAgXiAgIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIHN0YXJ0KGNvZGUpIHtcbiAgICAvLyBNdXN0IGJlIGRvdC5cbiAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICByZXR1cm4gYWZ0ZXJcbiAgfVxuXG4gIC8qKlxuICAgKiBBZnRlciBkb3QuXG4gICAqXG4gICAqIGBgYG1hcmtkb3duXG4gICAqID4gfCBjb250YWN0QGV4YW1wbGUub3JnLlxuICAgKiAgICAgICAgICAgICAgICAgICAgIF4gICBeXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBhZnRlcihjb2RlKSB7XG4gICAgLy8gTm90IGEgdHJhaWwgaWYgYWxwaGFudW1lcmljLlxuICAgIHJldHVybiBhc2NpaUFscGhhbnVtZXJpYyhjb2RlKSA/IG5vayhjb2RlKSA6IG9rKGNvZGUpXG4gIH1cbn1cblxuLyoqXG4gKiBTZWU6XG4gKiA8aHR0cHM6Ly9naXRodWIuY29tL2dpdGh1Yi9jbWFyay1nZm0vYmxvYi9lZjFjZmNiL2V4dGVuc2lvbnMvYXV0b2xpbmsuYyNMMTU2Pi5cbiAqXG4gKiBAdHlwZSB7UHJldmlvdXN9XG4gKi9cbmZ1bmN0aW9uIHByZXZpb3VzV3d3KGNvZGUpIHtcbiAgcmV0dXJuIChcbiAgICBjb2RlID09PSBjb2Rlcy5lb2YgfHxcbiAgICBjb2RlID09PSBjb2Rlcy5sZWZ0UGFyZW50aGVzaXMgfHxcbiAgICBjb2RlID09PSBjb2Rlcy5hc3RlcmlzayB8fFxuICAgIGNvZGUgPT09IGNvZGVzLnVuZGVyc2NvcmUgfHxcbiAgICBjb2RlID09PSBjb2Rlcy5sZWZ0U3F1YXJlQnJhY2tldCB8fFxuICAgIGNvZGUgPT09IGNvZGVzLnJpZ2h0U3F1YXJlQnJhY2tldCB8fFxuICAgIGNvZGUgPT09IGNvZGVzLnRpbGRlIHx8XG4gICAgbWFya2Rvd25MaW5lRW5kaW5nT3JTcGFjZShjb2RlKVxuICApXG59XG5cbi8qKlxuICogU2VlOlxuICogPGh0dHBzOi8vZ2l0aHViLmNvbS9naXRodWIvY21hcmstZ2ZtL2Jsb2IvZWYxY2ZjYi9leHRlbnNpb25zL2F1dG9saW5rLmMjTDIxND4uXG4gKlxuICogQHR5cGUge1ByZXZpb3VzfVxuICovXG5mdW5jdGlvbiBwcmV2aW91c1Byb3RvY29sKGNvZGUpIHtcbiAgcmV0dXJuICFhc2NpaUFscGhhKGNvZGUpXG59XG5cbi8qKlxuICogQHRoaXMge1Rva2VuaXplQ29udGV4dH1cbiAqIEB0eXBlIHtQcmV2aW91c31cbiAqL1xuZnVuY3Rpb24gcHJldmlvdXNFbWFpbChjb2RlKSB7XG4gIC8vIERvIG5vdCBhbGxvdyBhIHNsYXNoIOKAnGluc2lkZeKAnSBhdGV4dC5cbiAgLy8gVGhlIHJlZmVyZW5jZSBjb2RlIGlzIGEgYml0IHdlaXJkLCBidXQgdGhhdOKAmXMgd2hhdCBpdCByZXN1bHRzIGluLlxuICAvLyBTb3VyY2U6IDxodHRwczovL2dpdGh1Yi5jb20vZ2l0aHViL2NtYXJrLWdmbS9ibG9iL2VmMWNmY2IvZXh0ZW5zaW9ucy9hdXRvbGluay5jI0wzMDc+LlxuICAvLyBPdGhlciB0aGFuIHNsYXNoLCBldmVyeSBwcmVjZWRpbmcgY2hhcmFjdGVyIGlzIGFsbG93ZWQuXG4gIHJldHVybiAhKGNvZGUgPT09IGNvZGVzLnNsYXNoIHx8IGdmbUF0ZXh0KGNvZGUpKVxufVxuXG4vKipcbiAqIEBwYXJhbSB7Q29kZX0gY29kZVxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKi9cbmZ1bmN0aW9uIGdmbUF0ZXh0KGNvZGUpIHtcbiAgcmV0dXJuIChcbiAgICBjb2RlID09PSBjb2Rlcy5wbHVzU2lnbiB8fFxuICAgIGNvZGUgPT09IGNvZGVzLmRhc2ggfHxcbiAgICBjb2RlID09PSBjb2Rlcy5kb3QgfHxcbiAgICBjb2RlID09PSBjb2Rlcy51bmRlcnNjb3JlIHx8XG4gICAgYXNjaWlBbHBoYW51bWVyaWMoY29kZSlcbiAgKVxufVxuXG4vKipcbiAqIEBwYXJhbSB7QXJyYXk8RXZlbnQ+fSBldmVudHNcbiAqIEByZXR1cm5zIHtib29sZWFufVxuICovXG5mdW5jdGlvbiBwcmV2aW91c1VuYmFsYW5jZWQoZXZlbnRzKSB7XG4gIGxldCBpbmRleCA9IGV2ZW50cy5sZW5ndGhcbiAgbGV0IHJlc3VsdCA9IGZhbHNlXG5cbiAgd2hpbGUgKGluZGV4LS0pIHtcbiAgICBjb25zdCB0b2tlbiA9IGV2ZW50c1tpbmRleF1bMV1cblxuICAgIGlmIChcbiAgICAgICh0b2tlbi50eXBlID09PSAnbGFiZWxMaW5rJyB8fCB0b2tlbi50eXBlID09PSAnbGFiZWxJbWFnZScpICYmXG4gICAgICAhdG9rZW4uX2JhbGFuY2VkXG4gICAgKSB7XG4gICAgICByZXN1bHQgPSB0cnVlXG4gICAgICBicmVha1xuICAgIH1cblxuICAgIC8vIElmIHdl4oCZdmUgc2VlbiB0aGlzIHRva2VuLCBhbmQgaXQgd2FzIG1hcmtlZCBhcyBub3QgaGF2aW5nIGFueSB1bmJhbGFuY2VkXG4gICAgLy8gYnJhY2tldCBiZWZvcmUgaXQsIHdlIGNhbiBleGl0LlxuICAgIGlmICh0b2tlbi5fZ2ZtQXV0b2xpbmtMaXRlcmFsV2Fsa2VkSW50bykge1xuICAgICAgcmVzdWx0ID0gZmFsc2VcbiAgICAgIGJyZWFrXG4gICAgfVxuICB9XG5cbiAgaWYgKGV2ZW50cy5sZW5ndGggPiAwICYmICFyZXN1bHQpIHtcbiAgICAvLyBNYXJrIHRoZSBsYXN0IHRva2VuIGFzIOKAnHdhbGtlZCBpbnRv4oCdIHcvbyBmaW5kaW5nXG4gICAgLy8gYW55dGhpbmcuXG4gICAgZXZlbnRzW2V2ZW50cy5sZW5ndGggLSAxXVsxXS5fZ2ZtQXV0b2xpbmtMaXRlcmFsV2Fsa2VkSW50byA9IHRydWVcbiAgfVxuXG4gIHJldHVybiByZXN1bHRcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js\n");

/***/ })

};
;