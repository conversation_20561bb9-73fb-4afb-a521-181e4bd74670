/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/papaparse";
exports.ids = ["vendor-chunks/papaparse"];
exports.modules = {

/***/ "(ssr)/./node_modules/papaparse/papaparse.js":
/*!*********************************************!*\
  !*** ./node_modules/papaparse/papaparse.js ***!
  \*********************************************/
/***/ (function(module, exports, __webpack_require__) {

eval("var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;/* @license\nPapa Parse\nv5.5.3\nhttps://github.com/mholt/PapaParse\nLicense: MIT\n*/\n\n(function(root, factory)\n{\n\t/* globals define */\n\tif (true)\n\t{\n\t\t// AMD. Register as an anonymous module.\n\t\t!(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\t}\n\telse {}\n\t// in strict mode we cannot access arguments.callee, so we need a named reference to\n\t// stringify the factory method for the blob worker\n\t// eslint-disable-next-line func-name\n}(this, function moduleFactory()\n{\n\t'use strict';\n\n\tvar global = (function() {\n\t\t// alternative method, similar to `Function('return this')()`\n\t\t// but without using `eval` (which is disabled when\n\t\t// using Content Security Policy).\n\n\t\tif (typeof self !== 'undefined') { return self; }\n\t\tif (typeof window !== 'undefined') { return window; }\n\t\tif (typeof global !== 'undefined') { return global; }\n\n\t\t// When running tests none of the above have been defined\n\t\treturn {};\n\t})();\n\n\n\tfunction getWorkerBlob() {\n\t\tvar URL = global.URL || global.webkitURL || null;\n\t\tvar code = moduleFactory.toString();\n\t\treturn Papa.BLOB_URL || (Papa.BLOB_URL = URL.createObjectURL(new Blob([\"var global = (function() { if (typeof self !== 'undefined') { return self; } if (typeof window !== 'undefined') { return window; } if (typeof global !== 'undefined') { return global; } return {}; })(); global.IS_PAPA_WORKER=true; \", '(', code, ')();'], {type: 'text/javascript'})));\n\t}\n\n\tvar IS_WORKER = !global.document && !!global.postMessage,\n\t\tIS_PAPA_WORKER = global.IS_PAPA_WORKER || false;\n\n\tvar workers = {}, workerIdCounter = 0;\n\n\tvar Papa = {};\n\n\tPapa.parse = CsvToJson;\n\tPapa.unparse = JsonToCsv;\n\n\tPapa.RECORD_SEP = String.fromCharCode(30);\n\tPapa.UNIT_SEP = String.fromCharCode(31);\n\tPapa.BYTE_ORDER_MARK = '\\ufeff';\n\tPapa.BAD_DELIMITERS = ['\\r', '\\n', '\"', Papa.BYTE_ORDER_MARK];\n\tPapa.WORKERS_SUPPORTED = !IS_WORKER && !!global.Worker;\n\tPapa.NODE_STREAM_INPUT = 1;\n\n\t// Configurable chunk sizes for local and remote files, respectively\n\tPapa.LocalChunkSize = 1024 * 1024 * 10;\t// 10 MB\n\tPapa.RemoteChunkSize = 1024 * 1024 * 5;\t// 5 MB\n\tPapa.DefaultDelimiter = ',';\t\t\t// Used if not specified and detection fails\n\n\t// Exposed for testing and development only\n\tPapa.Parser = Parser;\n\tPapa.ParserHandle = ParserHandle;\n\tPapa.NetworkStreamer = NetworkStreamer;\n\tPapa.FileStreamer = FileStreamer;\n\tPapa.StringStreamer = StringStreamer;\n\tPapa.ReadableStreamStreamer = ReadableStreamStreamer;\n\tif (typeof PAPA_BROWSER_CONTEXT === 'undefined') {\n\t\tPapa.DuplexStreamStreamer = DuplexStreamStreamer;\n\t}\n\n\tif (global.jQuery)\n\t{\n\t\tvar $ = global.jQuery;\n\t\t$.fn.parse = function(options)\n\t\t{\n\t\t\tvar config = options.config || {};\n\t\t\tvar queue = [];\n\n\t\t\tthis.each(function(idx)\n\t\t\t{\n\t\t\t\tvar supported = $(this).prop('tagName').toUpperCase() === 'INPUT'\n\t\t\t\t\t\t\t\t&& $(this).attr('type').toLowerCase() === 'file'\n\t\t\t\t\t\t\t\t&& global.FileReader;\n\n\t\t\t\tif (!supported || !this.files || this.files.length === 0)\n\t\t\t\t\treturn true;\t// continue to next input element\n\n\t\t\t\tfor (var i = 0; i < this.files.length; i++)\n\t\t\t\t{\n\t\t\t\t\tqueue.push({\n\t\t\t\t\t\tfile: this.files[i],\n\t\t\t\t\t\tinputElem: this,\n\t\t\t\t\t\tinstanceConfig: $.extend({}, config)\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tparseNextFile();\t// begin parsing\n\t\t\treturn this;\t\t// maintains chainability\n\n\n\t\t\tfunction parseNextFile()\n\t\t\t{\n\t\t\t\tif (queue.length === 0)\n\t\t\t\t{\n\t\t\t\t\tif (isFunction(options.complete))\n\t\t\t\t\t\toptions.complete();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tvar f = queue[0];\n\n\t\t\t\tif (isFunction(options.before))\n\t\t\t\t{\n\t\t\t\t\tvar returned = options.before(f.file, f.inputElem);\n\n\t\t\t\t\tif (typeof returned === 'object')\n\t\t\t\t\t{\n\t\t\t\t\t\tif (returned.action === 'abort')\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\terror('AbortError', f.file, f.inputElem, returned.reason);\n\t\t\t\t\t\t\treturn;\t// Aborts all queued files immediately\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if (returned.action === 'skip')\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tfileComplete();\t// parse the next file in the queue, if any\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if (typeof returned.config === 'object')\n\t\t\t\t\t\t\tf.instanceConfig = $.extend(f.instanceConfig, returned.config);\n\t\t\t\t\t}\n\t\t\t\t\telse if (returned === 'skip')\n\t\t\t\t\t{\n\t\t\t\t\t\tfileComplete();\t// parse the next file in the queue, if any\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Wrap up the user's complete callback, if any, so that ours also gets executed\n\t\t\t\tvar userCompleteFunc = f.instanceConfig.complete;\n\t\t\t\tf.instanceConfig.complete = function(results)\n\t\t\t\t{\n\t\t\t\t\tif (isFunction(userCompleteFunc))\n\t\t\t\t\t\tuserCompleteFunc(results, f.file, f.inputElem);\n\t\t\t\t\tfileComplete();\n\t\t\t\t};\n\n\t\t\t\tPapa.parse(f.file, f.instanceConfig);\n\t\t\t}\n\n\t\t\tfunction error(name, file, elem, reason)\n\t\t\t{\n\t\t\t\tif (isFunction(options.error))\n\t\t\t\t\toptions.error({name: name}, file, elem, reason);\n\t\t\t}\n\n\t\t\tfunction fileComplete()\n\t\t\t{\n\t\t\t\tqueue.splice(0, 1);\n\t\t\t\tparseNextFile();\n\t\t\t}\n\t\t};\n\t}\n\n\n\tif (IS_PAPA_WORKER)\n\t{\n\t\tglobal.onmessage = workerThreadReceivedMessage;\n\t}\n\n\n\n\n\tfunction CsvToJson(_input, _config)\n\t{\n\t\t_config = _config || {};\n\t\tvar dynamicTyping = _config.dynamicTyping || false;\n\t\tif (isFunction(dynamicTyping)) {\n\t\t\t_config.dynamicTypingFunction = dynamicTyping;\n\t\t\t// Will be filled on first row call\n\t\t\tdynamicTyping = {};\n\t\t}\n\t\t_config.dynamicTyping = dynamicTyping;\n\n\t\t_config.transform = isFunction(_config.transform) ? _config.transform : false;\n\n\t\tif (_config.worker && Papa.WORKERS_SUPPORTED)\n\t\t{\n\t\t\tvar w = newWorker();\n\n\t\t\tw.userStep = _config.step;\n\t\t\tw.userChunk = _config.chunk;\n\t\t\tw.userComplete = _config.complete;\n\t\t\tw.userError = _config.error;\n\n\t\t\t_config.step = isFunction(_config.step);\n\t\t\t_config.chunk = isFunction(_config.chunk);\n\t\t\t_config.complete = isFunction(_config.complete);\n\t\t\t_config.error = isFunction(_config.error);\n\t\t\tdelete _config.worker;\t// prevent infinite loop\n\n\t\t\tw.postMessage({\n\t\t\t\tinput: _input,\n\t\t\t\tconfig: _config,\n\t\t\t\tworkerId: w.id\n\t\t\t});\n\n\t\t\treturn;\n\t\t}\n\n\t\tvar streamer = null;\n\t\tif (_input === Papa.NODE_STREAM_INPUT && typeof PAPA_BROWSER_CONTEXT === 'undefined')\n\t\t{\n\t\t\t// create a node Duplex stream for use\n\t\t\t// with .pipe\n\t\t\tstreamer = new DuplexStreamStreamer(_config);\n\t\t\treturn streamer.getStream();\n\t\t}\n\t\telse if (typeof _input === 'string')\n\t\t{\n\t\t\t_input = stripBom(_input);\n\t\t\tif (_config.download)\n\t\t\t\tstreamer = new NetworkStreamer(_config);\n\t\t\telse\n\t\t\t\tstreamer = new StringStreamer(_config);\n\t\t}\n\t\telse if (_input.readable === true && isFunction(_input.read) && isFunction(_input.on))\n\t\t{\n\t\t\tstreamer = new ReadableStreamStreamer(_config);\n\t\t}\n\t\telse if ((global.File && _input instanceof File) || _input instanceof Object)\t// ...Safari. (see issue #106)\n\t\t\tstreamer = new FileStreamer(_config);\n\n\t\treturn streamer.stream(_input);\n\n\t\t// Strip character from UTF-8 BOM encoded files that cause issue parsing the file\n\t\tfunction stripBom(string) {\n\t\t\tif (string.charCodeAt(0) === 0xfeff) {\n\t\t\t\treturn string.slice(1);\n\t\t\t}\n\t\t\treturn string;\n\t\t}\n\t}\n\n\n\n\n\n\n\tfunction JsonToCsv(_input, _config)\n\t{\n\t\t// Default configuration\n\n\t\t/** whether to surround every datum with quotes */\n\t\tvar _quotes = false;\n\n\t\t/** whether to write headers */\n\t\tvar _writeHeader = true;\n\n\t\t/** delimiting character(s) */\n\t\tvar _delimiter = ',';\n\n\t\t/** newline character(s) */\n\t\tvar _newline = '\\r\\n';\n\n\t\t/** quote character */\n\t\tvar _quoteChar = '\"';\n\n\t\t/** escaped quote character, either \"\" or <config.escapeChar>\" */\n\t\tvar _escapedQuote = _quoteChar + _quoteChar;\n\n\t\t/** whether to skip empty lines */\n\t\tvar _skipEmptyLines = false;\n\n\t\t/** the columns (keys) we expect when we unparse objects */\n\t\tvar _columns = null;\n\n\t\t/** whether to prevent outputting cells that can be parsed as formulae by spreadsheet software (Excel and LibreOffice) */\n\t\tvar _escapeFormulae = false;\n\n\t\tunpackConfig();\n\n\t\tvar quoteCharRegex = new RegExp(escapeRegExp(_quoteChar), 'g');\n\n\t\tif (typeof _input === 'string')\n\t\t\t_input = JSON.parse(_input);\n\n\t\tif (Array.isArray(_input))\n\t\t{\n\t\t\tif (!_input.length || Array.isArray(_input[0]))\n\t\t\t\treturn serialize(null, _input, _skipEmptyLines);\n\t\t\telse if (typeof _input[0] === 'object')\n\t\t\t\treturn serialize(_columns || Object.keys(_input[0]), _input, _skipEmptyLines);\n\t\t}\n\t\telse if (typeof _input === 'object')\n\t\t{\n\t\t\tif (typeof _input.data === 'string')\n\t\t\t\t_input.data = JSON.parse(_input.data);\n\n\t\t\tif (Array.isArray(_input.data))\n\t\t\t{\n\t\t\t\tif (!_input.fields)\n\t\t\t\t\t_input.fields = _input.meta && _input.meta.fields || _columns;\n\n\t\t\t\tif (!_input.fields)\n\t\t\t\t\t_input.fields =  Array.isArray(_input.data[0])\n\t\t\t\t\t\t? _input.fields\n\t\t\t\t\t\t: typeof _input.data[0] === 'object'\n\t\t\t\t\t\t\t? Object.keys(_input.data[0])\n\t\t\t\t\t\t\t: [];\n\n\t\t\t\tif (!(Array.isArray(_input.data[0])) && typeof _input.data[0] !== 'object')\n\t\t\t\t\t_input.data = [_input.data];\t// handles input like [1,2,3] or ['asdf']\n\t\t\t}\n\n\t\t\treturn serialize(_input.fields || [], _input.data || [], _skipEmptyLines);\n\t\t}\n\n\t\t// Default (any valid paths should return before this)\n\t\tthrow new Error('Unable to serialize unrecognized input');\n\n\n\t\tfunction unpackConfig()\n\t\t{\n\t\t\tif (typeof _config !== 'object')\n\t\t\t\treturn;\n\n\t\t\tif (typeof _config.delimiter === 'string'\n                && !Papa.BAD_DELIMITERS.filter(function(value) { return _config.delimiter.indexOf(value) !== -1; }).length)\n\t\t\t{\n\t\t\t\t_delimiter = _config.delimiter;\n\t\t\t}\n\n\t\t\tif (typeof _config.quotes === 'boolean'\n\t\t\t\t|| typeof _config.quotes === 'function'\n\t\t\t\t|| Array.isArray(_config.quotes))\n\t\t\t\t_quotes = _config.quotes;\n\n\t\t\tif (typeof _config.skipEmptyLines === 'boolean'\n\t\t\t\t|| typeof _config.skipEmptyLines === 'string')\n\t\t\t\t_skipEmptyLines = _config.skipEmptyLines;\n\n\t\t\tif (typeof _config.newline === 'string')\n\t\t\t\t_newline = _config.newline;\n\n\t\t\tif (typeof _config.quoteChar === 'string')\n\t\t\t\t_quoteChar = _config.quoteChar;\n\n\t\t\tif (typeof _config.header === 'boolean')\n\t\t\t\t_writeHeader = _config.header;\n\n\t\t\tif (Array.isArray(_config.columns)) {\n\n\t\t\t\tif (_config.columns.length === 0) throw new Error('Option columns is empty');\n\n\t\t\t\t_columns = _config.columns;\n\t\t\t}\n\n\t\t\tif (_config.escapeChar !== undefined) {\n\t\t\t\t_escapedQuote = _config.escapeChar + _quoteChar;\n\t\t\t}\n\n\t\t\tif (_config.escapeFormulae instanceof RegExp) {\n\t\t\t\t_escapeFormulae = _config.escapeFormulae;\n\t\t\t} else if (typeof _config.escapeFormulae === 'boolean' && _config.escapeFormulae) {\n\t\t\t\t_escapeFormulae =  /^[=+\\-@\\t\\r].*$/;\n\t\t\t}\n\t\t}\n\n\t\t/** The double for loop that iterates the data and writes out a CSV string including header row */\n\t\tfunction serialize(fields, data, skipEmptyLines)\n\t\t{\n\t\t\tvar csv = '';\n\n\t\t\tif (typeof fields === 'string')\n\t\t\t\tfields = JSON.parse(fields);\n\t\t\tif (typeof data === 'string')\n\t\t\t\tdata = JSON.parse(data);\n\n\t\t\tvar hasHeader = Array.isArray(fields) && fields.length > 0;\n\t\t\tvar dataKeyedByField = !(Array.isArray(data[0]));\n\n\t\t\t// If there a header row, write it first\n\t\t\tif (hasHeader && _writeHeader)\n\t\t\t{\n\t\t\t\tfor (var i = 0; i < fields.length; i++)\n\t\t\t\t{\n\t\t\t\t\tif (i > 0)\n\t\t\t\t\t\tcsv += _delimiter;\n\t\t\t\t\tcsv += safe(fields[i], i);\n\t\t\t\t}\n\t\t\t\tif (data.length > 0)\n\t\t\t\t\tcsv += _newline;\n\t\t\t}\n\n\t\t\t// Then write out the data\n\t\t\tfor (var row = 0; row < data.length; row++)\n\t\t\t{\n\t\t\t\tvar maxCol = hasHeader ? fields.length : data[row].length;\n\n\t\t\t\tvar emptyLine = false;\n\t\t\t\tvar nullLine = hasHeader ? Object.keys(data[row]).length === 0 : data[row].length === 0;\n\t\t\t\tif (skipEmptyLines && !hasHeader)\n\t\t\t\t{\n\t\t\t\t\temptyLine = skipEmptyLines === 'greedy' ? data[row].join('').trim() === '' : data[row].length === 1 && data[row][0].length === 0;\n\t\t\t\t}\n\t\t\t\tif (skipEmptyLines === 'greedy' && hasHeader) {\n\t\t\t\t\tvar line = [];\n\t\t\t\t\tfor (var c = 0; c < maxCol; c++) {\n\t\t\t\t\t\tvar cx = dataKeyedByField ? fields[c] : c;\n\t\t\t\t\t\tline.push(data[row][cx]);\n\t\t\t\t\t}\n\t\t\t\t\temptyLine = line.join('').trim() === '';\n\t\t\t\t}\n\t\t\t\tif (!emptyLine)\n\t\t\t\t{\n\t\t\t\t\tfor (var col = 0; col < maxCol; col++)\n\t\t\t\t\t{\n\t\t\t\t\t\tif (col > 0 && !nullLine)\n\t\t\t\t\t\t\tcsv += _delimiter;\n\t\t\t\t\t\tvar colIdx = hasHeader && dataKeyedByField ? fields[col] : col;\n\t\t\t\t\t\tcsv += safe(data[row][colIdx], col);\n\t\t\t\t\t}\n\t\t\t\t\tif (row < data.length - 1 && (!skipEmptyLines || (maxCol > 0 && !nullLine)))\n\t\t\t\t\t{\n\t\t\t\t\t\tcsv += _newline;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn csv;\n\t\t}\n\n\t\t/** Encloses a value around quotes if needed (makes a value safe for CSV insertion) */\n\t\tfunction safe(str, col)\n\t\t{\n\t\t\tif (typeof str === 'undefined' || str === null)\n\t\t\t\treturn '';\n\n\t\t\tif (str.constructor === Date)\n\t\t\t\treturn JSON.stringify(str).slice(1, 25);\n\n\t\t\tvar needsQuotes = false;\n\n\t\t\tif (_escapeFormulae && typeof str === \"string\" && _escapeFormulae.test(str)) {\n\t\t\t\tstr = \"'\" + str;\n\t\t\t\tneedsQuotes = true;\n\t\t\t}\n\n\t\t\tvar escapedQuoteStr = str.toString().replace(quoteCharRegex, _escapedQuote);\n\n\t\t\tneedsQuotes = needsQuotes\n\t\t\t\t\t\t\t|| _quotes === true\n\t\t\t\t\t\t\t|| (typeof _quotes === 'function' && _quotes(str, col))\n\t\t\t\t\t\t\t|| (Array.isArray(_quotes) && _quotes[col])\n\t\t\t\t\t\t\t|| hasAny(escapedQuoteStr, Papa.BAD_DELIMITERS)\n\t\t\t\t\t\t\t|| escapedQuoteStr.indexOf(_delimiter) > -1\n\t\t\t\t\t\t\t|| escapedQuoteStr.charAt(0) === ' '\n\t\t\t\t\t\t\t|| escapedQuoteStr.charAt(escapedQuoteStr.length - 1) === ' ';\n\n\t\t\treturn needsQuotes ? _quoteChar + escapedQuoteStr + _quoteChar : escapedQuoteStr;\n\t\t}\n\n\t\tfunction hasAny(str, substrings)\n\t\t{\n\t\t\tfor (var i = 0; i < substrings.length; i++)\n\t\t\t\tif (str.indexOf(substrings[i]) > -1)\n\t\t\t\t\treturn true;\n\t\t\treturn false;\n\t\t}\n\t}\n\n\n\t/** ChunkStreamer is the base prototype for various streamer implementations. */\n\tfunction ChunkStreamer(config)\n\t{\n\t\tthis._handle = null;\n\t\tthis._finished = false;\n\t\tthis._completed = false;\n\t\tthis._halted = false;\n\t\tthis._input = null;\n\t\tthis._baseIndex = 0;\n\t\tthis._partialLine = '';\n\t\tthis._rowCount = 0;\n\t\tthis._start = 0;\n\t\tthis._nextChunk = null;\n\t\tthis.isFirstChunk = true;\n\t\tthis._completeResults = {\n\t\t\tdata: [],\n\t\t\terrors: [],\n\t\t\tmeta: {}\n\t\t};\n\t\treplaceConfig.call(this, config);\n\n\t\tthis.parseChunk = function(chunk, isFakeChunk)\n\t\t{\n\t\t\t// First chunk pre-processing\n\t\t\tconst skipFirstNLines = parseInt(this._config.skipFirstNLines) || 0;\n\t\t\tif (this.isFirstChunk && skipFirstNLines > 0) {\n\t\t\t\tlet _newline = this._config.newline;\n\t\t\t\tif (!_newline) {\n\t\t\t\t\tconst quoteChar = this._config.quoteChar || '\"';\n\t\t\t\t\t_newline = this._handle.guessLineEndings(chunk, quoteChar);\n\t\t\t\t}\n\t\t\t\tconst splitChunk = chunk.split(_newline);\n\t\t\t\tchunk = [...splitChunk.slice(skipFirstNLines)].join(_newline);\n\t\t\t}\n\t\t\tif (this.isFirstChunk && isFunction(this._config.beforeFirstChunk))\n\t\t\t{\n\t\t\t\tvar modifiedChunk = this._config.beforeFirstChunk(chunk);\n\t\t\t\tif (modifiedChunk !== undefined)\n\t\t\t\t\tchunk = modifiedChunk;\n\t\t\t}\n\t\t\tthis.isFirstChunk = false;\n\t\t\tthis._halted = false;\n\n\t\t\t// Rejoin the line we likely just split in two by chunking the file\n\t\t\tvar aggregate = this._partialLine + chunk;\n\t\t\tthis._partialLine = '';\n\t\t\tvar results = this._handle.parse(aggregate, this._baseIndex, !this._finished);\n\n\t\t\tif (this._handle.paused() || this._handle.aborted()) {\n\t\t\t\tthis._halted = true;\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tvar lastIndex = results.meta.cursor;\n\n\t\t\tif (!this._finished)\n\t\t\t{\n\t\t\t\tthis._partialLine = aggregate.substring(lastIndex - this._baseIndex);\n\t\t\t\tthis._baseIndex = lastIndex;\n\t\t\t}\n\n\t\t\tif (results && results.data)\n\t\t\t\tthis._rowCount += results.data.length;\n\n\t\t\tvar finishedIncludingPreview = this._finished || (this._config.preview && this._rowCount >= this._config.preview);\n\n\t\t\tif (IS_PAPA_WORKER)\n\t\t\t{\n\t\t\t\tglobal.postMessage({\n\t\t\t\t\tresults: results,\n\t\t\t\t\tworkerId: Papa.WORKER_ID,\n\t\t\t\t\tfinished: finishedIncludingPreview\n\t\t\t\t});\n\t\t\t}\n\t\t\telse if (isFunction(this._config.chunk) && !isFakeChunk)\n\t\t\t{\n\t\t\t\tthis._config.chunk(results, this._handle);\n\t\t\t\tif (this._handle.paused() || this._handle.aborted()) {\n\t\t\t\t\tthis._halted = true;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tresults = undefined;\n\t\t\t\tthis._completeResults = undefined;\n\t\t\t}\n\n\t\t\tif (!this._config.step && !this._config.chunk) {\n\t\t\t\tthis._completeResults.data = this._completeResults.data.concat(results.data);\n\t\t\t\tthis._completeResults.errors = this._completeResults.errors.concat(results.errors);\n\t\t\t\tthis._completeResults.meta = results.meta;\n\t\t\t}\n\n\t\t\tif (!this._completed && finishedIncludingPreview && isFunction(this._config.complete) && (!results || !results.meta.aborted)) {\n\t\t\t\tthis._config.complete(this._completeResults, this._input);\n\t\t\t\tthis._completed = true;\n\t\t\t}\n\n\t\t\tif (!finishedIncludingPreview && (!results || !results.meta.paused))\n\t\t\t\tthis._nextChunk();\n\n\t\t\treturn results;\n\t\t};\n\n\t\tthis._sendError = function(error)\n\t\t{\n\t\t\tif (isFunction(this._config.error))\n\t\t\t\tthis._config.error(error);\n\t\t\telse if (IS_PAPA_WORKER && this._config.error)\n\t\t\t{\n\t\t\t\tglobal.postMessage({\n\t\t\t\t\tworkerId: Papa.WORKER_ID,\n\t\t\t\t\terror: error,\n\t\t\t\t\tfinished: false\n\t\t\t\t});\n\t\t\t}\n\t\t};\n\n\t\tfunction replaceConfig(config)\n\t\t{\n\t\t\t// Deep-copy the config so we can edit it\n\t\t\tvar configCopy = copy(config);\n\t\t\tconfigCopy.chunkSize = parseInt(configCopy.chunkSize);\t// parseInt VERY important so we don't concatenate strings!\n\t\t\tif (!config.step && !config.chunk)\n\t\t\t\tconfigCopy.chunkSize = null;  // disable Range header if not streaming; bad values break IIS - see issue #196\n\t\t\tthis._handle = new ParserHandle(configCopy);\n\t\t\tthis._handle.streamer = this;\n\t\t\tthis._config = configCopy;\t// persist the copy to the caller\n\t\t}\n\t}\n\n\n\tfunction NetworkStreamer(config)\n\t{\n\t\tconfig = config || {};\n\t\tif (!config.chunkSize)\n\t\t\tconfig.chunkSize = Papa.RemoteChunkSize;\n\t\tChunkStreamer.call(this, config);\n\n\t\tvar xhr;\n\n\t\tif (IS_WORKER)\n\t\t{\n\t\t\tthis._nextChunk = function()\n\t\t\t{\n\t\t\t\tthis._readChunk();\n\t\t\t\tthis._chunkLoaded();\n\t\t\t};\n\t\t}\n\t\telse\n\t\t{\n\t\t\tthis._nextChunk = function()\n\t\t\t{\n\t\t\t\tthis._readChunk();\n\t\t\t};\n\t\t}\n\n\t\tthis.stream = function(url)\n\t\t{\n\t\t\tthis._input = url;\n\t\t\tthis._nextChunk();\t// Starts streaming\n\t\t};\n\n\t\tthis._readChunk = function()\n\t\t{\n\t\t\tif (this._finished)\n\t\t\t{\n\t\t\t\tthis._chunkLoaded();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\txhr = new XMLHttpRequest();\n\n\t\t\tif (this._config.withCredentials)\n\t\t\t{\n\t\t\t\txhr.withCredentials = this._config.withCredentials;\n\t\t\t}\n\n\t\t\tif (!IS_WORKER)\n\t\t\t{\n\t\t\t\txhr.onload = bindFunction(this._chunkLoaded, this);\n\t\t\t\txhr.onerror = bindFunction(this._chunkError, this);\n\t\t\t}\n\n\t\t\txhr.open(this._config.downloadRequestBody ? 'POST' : 'GET', this._input, !IS_WORKER);\n\t\t\t// Headers can only be set when once the request state is OPENED\n\t\t\tif (this._config.downloadRequestHeaders)\n\t\t\t{\n\t\t\t\tvar headers = this._config.downloadRequestHeaders;\n\n\t\t\t\tfor (var headerName in headers)\n\t\t\t\t{\n\t\t\t\t\txhr.setRequestHeader(headerName, headers[headerName]);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (this._config.chunkSize)\n\t\t\t{\n\t\t\t\tvar end = this._start + this._config.chunkSize - 1;\t// minus one because byte range is inclusive\n\t\t\t\txhr.setRequestHeader('Range', 'bytes=' + this._start + '-' + end);\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\txhr.send(this._config.downloadRequestBody);\n\t\t\t}\n\t\t\tcatch (err) {\n\t\t\t\tthis._chunkError(err.message);\n\t\t\t}\n\n\t\t\tif (IS_WORKER && xhr.status === 0)\n\t\t\t\tthis._chunkError();\n\t\t};\n\n\t\tthis._chunkLoaded = function()\n\t\t{\n\t\t\tif (xhr.readyState !== 4)\n\t\t\t\treturn;\n\n\t\t\tif (xhr.status < 200 || xhr.status >= 400)\n\t\t\t{\n\t\t\t\tthis._chunkError();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Use chunckSize as it may be a diference on reponse lentgh due to characters with more than 1 byte\n\t\t\tthis._start += this._config.chunkSize ? this._config.chunkSize : xhr.responseText.length;\n\t\t\tthis._finished = !this._config.chunkSize || this._start >= getFileSize(xhr);\n\t\t\tthis.parseChunk(xhr.responseText);\n\t\t};\n\n\t\tthis._chunkError = function(errorMessage)\n\t\t{\n\t\t\tvar errorText = xhr.statusText || errorMessage;\n\t\t\tthis._sendError(new Error(errorText));\n\t\t};\n\n\t\tfunction getFileSize(xhr)\n\t\t{\n\t\t\tvar contentRange = xhr.getResponseHeader('Content-Range');\n\t\t\tif (contentRange === null) { // no content range, then finish!\n\t\t\t\treturn -1;\n\t\t\t}\n\t\t\treturn parseInt(contentRange.substring(contentRange.lastIndexOf('/') + 1));\n\t\t}\n\t}\n\tNetworkStreamer.prototype = Object.create(ChunkStreamer.prototype);\n\tNetworkStreamer.prototype.constructor = NetworkStreamer;\n\n\n\tfunction FileStreamer(config)\n\t{\n\t\tconfig = config || {};\n\t\tif (!config.chunkSize)\n\t\t\tconfig.chunkSize = Papa.LocalChunkSize;\n\t\tChunkStreamer.call(this, config);\n\n\t\tvar reader, slice;\n\n\t\t// FileReader is better than FileReaderSync (even in worker) - see http://stackoverflow.com/q/24708649/1048862\n\t\t// But Firefox is a pill, too - see issue #76: https://github.com/mholt/PapaParse/issues/76\n\t\tvar usingAsyncReader = typeof FileReader !== 'undefined';\t// Safari doesn't consider it a function - see issue #105\n\n\t\tthis.stream = function(file)\n\t\t{\n\t\t\tthis._input = file;\n\t\t\tslice = file.slice || file.webkitSlice || file.mozSlice;\n\n\t\t\tif (usingAsyncReader)\n\t\t\t{\n\t\t\t\treader = new FileReader();\t\t// Preferred method of reading files, even in workers\n\t\t\t\treader.onload = bindFunction(this._chunkLoaded, this);\n\t\t\t\treader.onerror = bindFunction(this._chunkError, this);\n\t\t\t}\n\t\t\telse\n\t\t\t\treader = new FileReaderSync();\t// Hack for running in a web worker in Firefox\n\n\t\t\tthis._nextChunk();\t// Starts streaming\n\t\t};\n\n\t\tthis._nextChunk = function()\n\t\t{\n\t\t\tif (!this._finished && (!this._config.preview || this._rowCount < this._config.preview))\n\t\t\t\tthis._readChunk();\n\t\t};\n\n\t\tthis._readChunk = function()\n\t\t{\n\t\t\tvar input = this._input;\n\t\t\tif (this._config.chunkSize)\n\t\t\t{\n\t\t\t\tvar end = Math.min(this._start + this._config.chunkSize, this._input.size);\n\t\t\t\tinput = slice.call(input, this._start, end);\n\t\t\t}\n\t\t\tvar txt = reader.readAsText(input, this._config.encoding);\n\t\t\tif (!usingAsyncReader)\n\t\t\t\tthis._chunkLoaded({ target: { result: txt } });\t// mimic the async signature\n\t\t};\n\n\t\tthis._chunkLoaded = function(event)\n\t\t{\n\t\t\t// Very important to increment start each time before handling results\n\t\t\tthis._start += this._config.chunkSize;\n\t\t\tthis._finished = !this._config.chunkSize || this._start >= this._input.size;\n\t\t\tthis.parseChunk(event.target.result);\n\t\t};\n\n\t\tthis._chunkError = function()\n\t\t{\n\t\t\tthis._sendError(reader.error);\n\t\t};\n\n\t}\n\tFileStreamer.prototype = Object.create(ChunkStreamer.prototype);\n\tFileStreamer.prototype.constructor = FileStreamer;\n\n\n\tfunction StringStreamer(config)\n\t{\n\t\tconfig = config || {};\n\t\tChunkStreamer.call(this, config);\n\n\t\tvar remaining;\n\t\tthis.stream = function(s)\n\t\t{\n\t\t\tremaining = s;\n\t\t\treturn this._nextChunk();\n\t\t};\n\t\tthis._nextChunk = function()\n\t\t{\n\t\t\tif (this._finished) return;\n\t\t\tvar size = this._config.chunkSize;\n\t\t\tvar chunk;\n\t\t\tif(size) {\n\t\t\t\tchunk = remaining.substring(0, size);\n\t\t\t\tremaining = remaining.substring(size);\n\t\t\t} else {\n\t\t\t\tchunk = remaining;\n\t\t\t\tremaining = '';\n\t\t\t}\n\t\t\tthis._finished = !remaining;\n\t\t\treturn this.parseChunk(chunk);\n\t\t};\n\t}\n\tStringStreamer.prototype = Object.create(StringStreamer.prototype);\n\tStringStreamer.prototype.constructor = StringStreamer;\n\n\n\tfunction ReadableStreamStreamer(config)\n\t{\n\t\tconfig = config || {};\n\n\t\tChunkStreamer.call(this, config);\n\n\t\tvar queue = [];\n\t\tvar parseOnData = true;\n\t\tvar streamHasEnded = false;\n\n\t\tthis.pause = function()\n\t\t{\n\t\t\tChunkStreamer.prototype.pause.apply(this, arguments);\n\t\t\tthis._input.pause();\n\t\t};\n\n\t\tthis.resume = function()\n\t\t{\n\t\t\tChunkStreamer.prototype.resume.apply(this, arguments);\n\t\t\tthis._input.resume();\n\t\t};\n\n\t\tthis.stream = function(stream)\n\t\t{\n\t\t\tthis._input = stream;\n\n\t\t\tthis._input.on('data', this._streamData);\n\t\t\tthis._input.on('end', this._streamEnd);\n\t\t\tthis._input.on('error', this._streamError);\n\t\t};\n\n\t\tthis._checkIsFinished = function()\n\t\t{\n\t\t\tif (streamHasEnded && queue.length === 1) {\n\t\t\t\tthis._finished = true;\n\t\t\t}\n\t\t};\n\n\t\tthis._nextChunk = function()\n\t\t{\n\t\t\tthis._checkIsFinished();\n\t\t\tif (queue.length)\n\t\t\t{\n\t\t\t\tthis.parseChunk(queue.shift());\n\t\t\t}\n\t\t\telse\n\t\t\t{\n\t\t\t\tparseOnData = true;\n\t\t\t}\n\t\t};\n\n\t\tthis._streamData = bindFunction(function(chunk)\n\t\t{\n\t\t\ttry\n\t\t\t{\n\t\t\t\tqueue.push(typeof chunk === 'string' ? chunk : chunk.toString(this._config.encoding));\n\n\t\t\t\tif (parseOnData)\n\t\t\t\t{\n\t\t\t\t\tparseOnData = false;\n\t\t\t\t\tthis._checkIsFinished();\n\t\t\t\t\tthis.parseChunk(queue.shift());\n\t\t\t\t}\n\t\t\t}\n\t\t\tcatch (error)\n\t\t\t{\n\t\t\t\tthis._streamError(error);\n\t\t\t}\n\t\t}, this);\n\n\t\tthis._streamError = bindFunction(function(error)\n\t\t{\n\t\t\tthis._streamCleanUp();\n\t\t\tthis._sendError(error);\n\t\t}, this);\n\n\t\tthis._streamEnd = bindFunction(function()\n\t\t{\n\t\t\tthis._streamCleanUp();\n\t\t\tstreamHasEnded = true;\n\t\t\tthis._streamData('');\n\t\t}, this);\n\n\t\tthis._streamCleanUp = bindFunction(function()\n\t\t{\n\t\t\tthis._input.removeListener('data', this._streamData);\n\t\t\tthis._input.removeListener('end', this._streamEnd);\n\t\t\tthis._input.removeListener('error', this._streamError);\n\t\t}, this);\n\t}\n\tReadableStreamStreamer.prototype = Object.create(ChunkStreamer.prototype);\n\tReadableStreamStreamer.prototype.constructor = ReadableStreamStreamer;\n\n\n\tfunction DuplexStreamStreamer(_config) {\n\t\tvar Duplex = (__webpack_require__(/*! stream */ \"stream\").Duplex);\n\t\tvar config = copy(_config);\n\t\tvar parseOnWrite = true;\n\t\tvar writeStreamHasFinished = false;\n\t\tvar parseCallbackQueue = [];\n\t\tvar stream = null;\n\n\t\tthis._onCsvData = function(results)\n\t\t{\n\t\t\tvar data = results.data;\n\t\t\tif (!stream.push(data) && !this._handle.paused()) {\n\t\t\t\t// the writeable consumer buffer has filled up\n\t\t\t\t// so we need to pause until more items\n\t\t\t\t// can be processed\n\t\t\t\tthis._handle.pause();\n\t\t\t}\n\t\t};\n\n\t\tthis._onCsvComplete = function()\n\t\t{\n\t\t\t// node will finish the read stream when\n\t\t\t// null is pushed\n\t\t\tstream.push(null);\n\t\t};\n\n\t\tconfig.step = bindFunction(this._onCsvData, this);\n\t\tconfig.complete = bindFunction(this._onCsvComplete, this);\n\t\tChunkStreamer.call(this, config);\n\n\t\tthis._nextChunk = function()\n\t\t{\n\t\t\tif (writeStreamHasFinished && parseCallbackQueue.length === 1) {\n\t\t\t\tthis._finished = true;\n\t\t\t}\n\t\t\tif (parseCallbackQueue.length) {\n\t\t\t\tparseCallbackQueue.shift()();\n\t\t\t} else {\n\t\t\t\tparseOnWrite = true;\n\t\t\t}\n\t\t};\n\n\t\tthis._addToParseQueue = function(chunk, callback)\n\t\t{\n\t\t\t// add to queue so that we can indicate\n\t\t\t// completion via callback\n\t\t\t// node will automatically pause the incoming stream\n\t\t\t// when too many items have been added without their\n\t\t\t// callback being invoked\n\t\t\tparseCallbackQueue.push(bindFunction(function() {\n\t\t\t\tthis.parseChunk(typeof chunk === 'string' ? chunk : chunk.toString(config.encoding));\n\t\t\t\tif (isFunction(callback)) {\n\t\t\t\t\treturn callback();\n\t\t\t\t}\n\t\t\t}, this));\n\t\t\tif (parseOnWrite) {\n\t\t\t\tparseOnWrite = false;\n\t\t\t\tthis._nextChunk();\n\t\t\t}\n\t\t};\n\n\t\tthis._onRead = function()\n\t\t{\n\t\t\tif (this._handle.paused()) {\n\t\t\t\t// the writeable consumer can handle more data\n\t\t\t\t// so resume the chunk parsing\n\t\t\t\tthis._handle.resume();\n\t\t\t}\n\t\t};\n\n\t\tthis._onWrite = function(chunk, encoding, callback)\n\t\t{\n\t\t\tthis._addToParseQueue(chunk, callback);\n\t\t};\n\n\t\tthis._onWriteComplete = function()\n\t\t{\n\t\t\twriteStreamHasFinished = true;\n\t\t\t// have to write empty string\n\t\t\t// so parser knows its done\n\t\t\tthis._addToParseQueue('');\n\t\t};\n\n\t\tthis.getStream = function()\n\t\t{\n\t\t\treturn stream;\n\t\t};\n\t\tstream = new Duplex({\n\t\t\treadableObjectMode: true,\n\t\t\tdecodeStrings: false,\n\t\t\tread: bindFunction(this._onRead, this),\n\t\t\twrite: bindFunction(this._onWrite, this)\n\t\t});\n\t\tstream.once('finish', bindFunction(this._onWriteComplete, this));\n\t}\n\tif (typeof PAPA_BROWSER_CONTEXT === 'undefined') {\n\t\tDuplexStreamStreamer.prototype = Object.create(ChunkStreamer.prototype);\n\t\tDuplexStreamStreamer.prototype.constructor = DuplexStreamStreamer;\n\t}\n\n\n\t// Use one ParserHandle per entire CSV file or string\n\tfunction ParserHandle(_config)\n\t{\n\t\t// One goal is to minimize the use of regular expressions...\n\t\tvar MAX_FLOAT = Math.pow(2, 53);\n\t\tvar MIN_FLOAT = -MAX_FLOAT;\n\t\tvar FLOAT = /^\\s*-?(\\d+\\.?|\\.\\d+|\\d+\\.\\d+)([eE][-+]?\\d+)?\\s*$/;\n\t\tvar ISO_DATE = /^((\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d\\.\\d+([+-][0-2]\\d:[0-5]\\d|Z))|(\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z))|(\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)))$/;\n\t\tvar self = this;\n\t\tvar _stepCounter = 0;\t// Number of times step was called (number of rows parsed)\n\t\tvar _rowCounter = 0;\t// Number of rows that have been parsed so far\n\t\tvar _input;\t\t\t\t// The input being parsed\n\t\tvar _parser;\t\t\t// The core parser being used\n\t\tvar _paused = false;\t// Whether we are paused or not\n\t\tvar _aborted = false;\t// Whether the parser has aborted or not\n\t\tvar _delimiterError;\t// Temporary state between delimiter detection and processing results\n\t\tvar _fields = [];\t\t// Fields are from the header row of the input, if there is one\n\t\tvar _results = {\t\t// The last results returned from the parser\n\t\t\tdata: [],\n\t\t\terrors: [],\n\t\t\tmeta: {}\n\t\t};\n\n\t\tif (isFunction(_config.step))\n\t\t{\n\t\t\tvar userStep = _config.step;\n\t\t\t_config.step = function(results)\n\t\t\t{\n\t\t\t\t_results = results;\n\n\t\t\t\tif (needsHeaderRow())\n\t\t\t\t\tprocessResults();\n\t\t\t\telse\t// only call user's step function after header row\n\t\t\t\t{\n\t\t\t\t\tprocessResults();\n\n\t\t\t\t\t// It's possbile that this line was empty and there's no row here after all\n\t\t\t\t\tif (_results.data.length === 0)\n\t\t\t\t\t\treturn;\n\n\t\t\t\t\t_stepCounter += results.data.length;\n\t\t\t\t\tif (_config.preview && _stepCounter > _config.preview)\n\t\t\t\t\t\t_parser.abort();\n\t\t\t\t\telse {\n\t\t\t\t\t\t_results.data = _results.data[0];\n\t\t\t\t\t\tuserStep(_results, self);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t};\n\t\t}\n\n\t\t/**\n\t\t * Parses input. Most users won't need, and shouldn't mess with, the baseIndex\n\t\t * and ignoreLastRow parameters. They are used by streamers (wrapper functions)\n\t\t * when an input comes in multiple chunks, like from a file.\n\t\t */\n\t\tthis.parse = function(input, baseIndex, ignoreLastRow)\n\t\t{\n\t\t\tvar quoteChar = _config.quoteChar || '\"';\n\t\t\tif (!_config.newline)\n\t\t\t\t_config.newline = this.guessLineEndings(input, quoteChar);\n\n\t\t\t_delimiterError = false;\n\t\t\tif (!_config.delimiter)\n\t\t\t{\n\t\t\t\tvar delimGuess = guessDelimiter(input, _config.newline, _config.skipEmptyLines, _config.comments, _config.delimitersToGuess);\n\t\t\t\tif (delimGuess.successful)\n\t\t\t\t\t_config.delimiter = delimGuess.bestDelimiter;\n\t\t\t\telse\n\t\t\t\t{\n\t\t\t\t\t_delimiterError = true;\t// add error after parsing (otherwise it would be overwritten)\n\t\t\t\t\t_config.delimiter = Papa.DefaultDelimiter;\n\t\t\t\t}\n\t\t\t\t_results.meta.delimiter = _config.delimiter;\n\t\t\t}\n\t\t\telse if(isFunction(_config.delimiter))\n\t\t\t{\n\t\t\t\t_config.delimiter = _config.delimiter(input);\n\t\t\t\t_results.meta.delimiter = _config.delimiter;\n\t\t\t}\n\n\t\t\tvar parserConfig = copy(_config);\n\t\t\tif (_config.preview && _config.header)\n\t\t\t\tparserConfig.preview++;\t// to compensate for header row\n\n\t\t\t_input = input;\n\t\t\t_parser = new Parser(parserConfig);\n\t\t\t_results = _parser.parse(_input, baseIndex, ignoreLastRow);\n\t\t\tprocessResults();\n\t\t\treturn _paused ? { meta: { paused: true } } : (_results || { meta: { paused: false } });\n\t\t};\n\n\t\tthis.paused = function()\n\t\t{\n\t\t\treturn _paused;\n\t\t};\n\n\t\tthis.pause = function()\n\t\t{\n\t\t\t_paused = true;\n\t\t\t_parser.abort();\n\n\t\t\t// If it is streaming via \"chunking\", the reader will start appending correctly already so no need to substring,\n\t\t\t// otherwise we can get duplicate content within a row\n\t\t\t_input = isFunction(_config.chunk) ? \"\" : _input.substring(_parser.getCharIndex());\n\t\t};\n\n\t\tthis.resume = function()\n\t\t{\n\t\t\tif(self.streamer._halted) {\n\t\t\t\t_paused = false;\n\t\t\t\tself.streamer.parseChunk(_input, true);\n\t\t\t} else {\n\t\t\t\t// Bugfix: #636 In case the processing hasn't halted yet\n\t\t\t\t// wait for it to halt in order to resume\n\t\t\t\tsetTimeout(self.resume, 3);\n\t\t\t}\n\t\t};\n\n\t\tthis.aborted = function()\n\t\t{\n\t\t\treturn _aborted;\n\t\t};\n\n\t\tthis.abort = function()\n\t\t{\n\t\t\t_aborted = true;\n\t\t\t_parser.abort();\n\t\t\t_results.meta.aborted = true;\n\t\t\tif (isFunction(_config.complete))\n\t\t\t\t_config.complete(_results);\n\t\t\t_input = '';\n\t\t};\n\n\t\tthis.guessLineEndings = function(input, quoteChar)\n\t\t{\n\t\t\tinput = input.substring(0, 1024 * 1024);\t// max length 1 MB\n\t\t\t// Replace all the text inside quotes\n\t\t\tvar re = new RegExp(escapeRegExp(quoteChar) + '([^]*?)' + escapeRegExp(quoteChar), 'gm');\n\t\t\tinput = input.replace(re, '');\n\n\t\t\tvar r = input.split('\\r');\n\n\t\t\tvar n = input.split('\\n');\n\n\t\t\tvar nAppearsFirst = (n.length > 1 && n[0].length < r[0].length);\n\n\t\t\tif (r.length === 1 || nAppearsFirst)\n\t\t\t\treturn '\\n';\n\n\t\t\tvar numWithN = 0;\n\t\t\tfor (var i = 0; i < r.length; i++)\n\t\t\t{\n\t\t\t\tif (r[i][0] === '\\n')\n\t\t\t\t\tnumWithN++;\n\t\t\t}\n\n\t\t\treturn numWithN >= r.length / 2 ? '\\r\\n' : '\\r';\n\t\t};\n\n\t\tfunction testEmptyLine(s) {\n\t\t\treturn _config.skipEmptyLines === 'greedy' ? s.join('').trim() === '' : s.length === 1 && s[0].length === 0;\n\t\t}\n\n\t\tfunction testFloat(s) {\n\t\t\tif (FLOAT.test(s)) {\n\t\t\t\tvar floatValue = parseFloat(s);\n\t\t\t\tif (floatValue > MIN_FLOAT && floatValue < MAX_FLOAT) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\n\t\tfunction processResults()\n\t\t{\n\t\t\tif (_results && _delimiterError)\n\t\t\t{\n\t\t\t\taddError('Delimiter', 'UndetectableDelimiter', 'Unable to auto-detect delimiting character; defaulted to \\'' + Papa.DefaultDelimiter + '\\'');\n\t\t\t\t_delimiterError = false;\n\t\t\t}\n\n\t\t\tif (_config.skipEmptyLines)\n\t\t\t{\n\t\t\t\t_results.data = _results.data.filter(function(d) {\n\t\t\t\t\treturn !testEmptyLine(d);\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tif (needsHeaderRow())\n\t\t\t\tfillHeaderFields();\n\n\t\t\treturn applyHeaderAndDynamicTypingAndTransformation();\n\t\t}\n\n\t\tfunction needsHeaderRow()\n\t\t{\n\t\t\treturn _config.header && _fields.length === 0;\n\t\t}\n\n\t\tfunction fillHeaderFields()\n\t\t{\n\t\t\tif (!_results)\n\t\t\t\treturn;\n\n\t\t\tfunction addHeader(header, i)\n\t\t\t{\n\t\t\t\tif (isFunction(_config.transformHeader))\n\t\t\t\t\theader = _config.transformHeader(header, i);\n\n\t\t\t\t_fields.push(header);\n\t\t\t}\n\n\t\t\tif (Array.isArray(_results.data[0]))\n\t\t\t{\n\t\t\t\tfor (var i = 0; needsHeaderRow() && i < _results.data.length; i++)\n\t\t\t\t\t_results.data[i].forEach(addHeader);\n\n\t\t\t\t_results.data.splice(0, 1);\n\t\t\t}\n\t\t\t// if _results.data[0] is not an array, we are in a step where _results.data is the row.\n\t\t\telse\n\t\t\t\t_results.data.forEach(addHeader);\n\t\t}\n\n\t\tfunction shouldApplyDynamicTyping(field) {\n\t\t\t// Cache function values to avoid calling it for each row\n\t\t\tif (_config.dynamicTypingFunction && _config.dynamicTyping[field] === undefined) {\n\t\t\t\t_config.dynamicTyping[field] = _config.dynamicTypingFunction(field);\n\t\t\t}\n\t\t\treturn (_config.dynamicTyping[field] || _config.dynamicTyping) === true;\n\t\t}\n\n\t\tfunction parseDynamic(field, value)\n\t\t{\n\t\t\tif (shouldApplyDynamicTyping(field))\n\t\t\t{\n\t\t\t\tif (value === 'true' || value === 'TRUE')\n\t\t\t\t\treturn true;\n\t\t\t\telse if (value === 'false' || value === 'FALSE')\n\t\t\t\t\treturn false;\n\t\t\t\telse if (testFloat(value))\n\t\t\t\t\treturn parseFloat(value);\n\t\t\t\telse if (ISO_DATE.test(value))\n\t\t\t\t\treturn new Date(value);\n\t\t\t\telse\n\t\t\t\t\treturn (value === '' ? null : value);\n\t\t\t}\n\t\t\treturn value;\n\t\t}\n\n\t\tfunction applyHeaderAndDynamicTypingAndTransformation()\n\t\t{\n\t\t\tif (!_results || (!_config.header && !_config.dynamicTyping && !_config.transform))\n\t\t\t\treturn _results;\n\n\t\t\tfunction processRow(rowSource, i)\n\t\t\t{\n\t\t\t\tvar row = _config.header ? {} : [];\n\n\t\t\t\tvar j;\n\t\t\t\tfor (j = 0; j < rowSource.length; j++)\n\t\t\t\t{\n\t\t\t\t\tvar field = j;\n\t\t\t\t\tvar value = rowSource[j];\n\n\t\t\t\t\tif (_config.header)\n\t\t\t\t\t\tfield = j >= _fields.length ? '__parsed_extra' : _fields[j];\n\n\t\t\t\t\tif (_config.transform)\n\t\t\t\t\t\tvalue = _config.transform(value,field);\n\n\t\t\t\t\tvalue = parseDynamic(field, value);\n\n\t\t\t\t\tif (field === '__parsed_extra')\n\t\t\t\t\t{\n\t\t\t\t\t\trow[field] = row[field] || [];\n\t\t\t\t\t\trow[field].push(value);\n\t\t\t\t\t}\n\t\t\t\t\telse\n\t\t\t\t\t\trow[field] = value;\n\t\t\t\t}\n\n\n\t\t\t\tif (_config.header)\n\t\t\t\t{\n\t\t\t\t\tif (j > _fields.length)\n\t\t\t\t\t\taddError('FieldMismatch', 'TooManyFields', 'Too many fields: expected ' + _fields.length + ' fields but parsed ' + j, _rowCounter + i);\n\t\t\t\t\telse if (j < _fields.length)\n\t\t\t\t\t\taddError('FieldMismatch', 'TooFewFields', 'Too few fields: expected ' + _fields.length + ' fields but parsed ' + j, _rowCounter + i);\n\t\t\t\t}\n\n\t\t\t\treturn row;\n\t\t\t}\n\n\t\t\tvar incrementBy = 1;\n\t\t\tif (!_results.data.length || Array.isArray(_results.data[0]))\n\t\t\t{\n\t\t\t\t_results.data = _results.data.map(processRow);\n\t\t\t\tincrementBy = _results.data.length;\n\t\t\t}\n\t\t\telse\n\t\t\t\t_results.data = processRow(_results.data, 0);\n\n\n\t\t\tif (_config.header && _results.meta)\n\t\t\t\t_results.meta.fields = _fields;\n\n\t\t\t_rowCounter += incrementBy;\n\t\t\treturn _results;\n\t\t}\n\n\t\tfunction guessDelimiter(input, newline, skipEmptyLines, comments, delimitersToGuess) {\n\t\t\tvar bestDelim, bestDelta, fieldCountPrevRow, maxFieldCount;\n\n\t\t\tdelimitersToGuess = delimitersToGuess || [',', '\\t', '|', ';', Papa.RECORD_SEP, Papa.UNIT_SEP];\n\n\t\t\tfor (var i = 0; i < delimitersToGuess.length; i++) {\n\t\t\t\tvar delim = delimitersToGuess[i];\n\t\t\t\tvar delta = 0, avgFieldCount = 0, emptyLinesCount = 0;\n\t\t\t\tfieldCountPrevRow = undefined;\n\n\t\t\t\tvar preview = new Parser({\n\t\t\t\t\tcomments: comments,\n\t\t\t\t\tdelimiter: delim,\n\t\t\t\t\tnewline: newline,\n\t\t\t\t\tpreview: 10\n\t\t\t\t}).parse(input);\n\n\t\t\t\tfor (var j = 0; j < preview.data.length; j++) {\n\t\t\t\t\tif (skipEmptyLines && testEmptyLine(preview.data[j])) {\n\t\t\t\t\t\temptyLinesCount++;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tvar fieldCount = preview.data[j].length;\n\t\t\t\t\tavgFieldCount += fieldCount;\n\n\t\t\t\t\tif (typeof fieldCountPrevRow === 'undefined') {\n\t\t\t\t\t\tfieldCountPrevRow = fieldCount;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\telse if (fieldCount > 0) {\n\t\t\t\t\t\tdelta += Math.abs(fieldCount - fieldCountPrevRow);\n\t\t\t\t\t\tfieldCountPrevRow = fieldCount;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (preview.data.length > 0)\n\t\t\t\t\tavgFieldCount /= (preview.data.length - emptyLinesCount);\n\n\t\t\t\tif ((typeof bestDelta === 'undefined' || delta <= bestDelta)\n\t\t\t\t\t&& (typeof maxFieldCount === 'undefined' || avgFieldCount > maxFieldCount) && avgFieldCount > 1.99) {\n\t\t\t\t\tbestDelta = delta;\n\t\t\t\t\tbestDelim = delim;\n\t\t\t\t\tmaxFieldCount = avgFieldCount;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t_config.delimiter = bestDelim;\n\n\t\t\treturn {\n\t\t\t\tsuccessful: !!bestDelim,\n\t\t\t\tbestDelimiter: bestDelim\n\t\t\t};\n\t\t}\n\n\t\tfunction addError(type, code, msg, row)\n\t\t{\n\t\t\tvar error = {\n\t\t\t\ttype: type,\n\t\t\t\tcode: code,\n\t\t\t\tmessage: msg\n\t\t\t};\n\t\t\tif(row !== undefined) {\n\t\t\t\terror.row = row;\n\t\t\t}\n\t\t\t_results.errors.push(error);\n\t\t}\n\t}\n\n\t/** https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions */\n\tfunction escapeRegExp(string)\n\t{\n\t\treturn string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'); // $& means the whole matched string\n\t}\n\n\t/** The core parser implements speedy and correct CSV parsing */\n\tfunction Parser(config)\n\t{\n\t\t// Unpack the config object\n\t\tconfig = config || {};\n\t\tvar delim = config.delimiter;\n\t\tvar newline = config.newline;\n\t\tvar comments = config.comments;\n\t\tvar step = config.step;\n\t\tvar preview = config.preview;\n\t\tvar fastMode = config.fastMode;\n\t\tvar quoteChar;\n\t\tvar renamedHeaders = null;\n\t\tvar headerParsed = false;\n\n\t\tif (config.quoteChar === undefined || config.quoteChar === null) {\n\t\t\tquoteChar = '\"';\n\t\t} else {\n\t\t\tquoteChar = config.quoteChar;\n\t\t}\n\t\tvar escapeChar = quoteChar;\n\t\tif (config.escapeChar !== undefined) {\n\t\t\tescapeChar = config.escapeChar;\n\t\t}\n\n\t\t// Delimiter must be valid\n\t\tif (typeof delim !== 'string'\n\t\t\t|| Papa.BAD_DELIMITERS.indexOf(delim) > -1)\n\t\t\tdelim = ',';\n\n\t\t// Comment character must be valid\n\t\tif (comments === delim)\n\t\t\tthrow new Error('Comment character same as delimiter');\n\t\telse if (comments === true)\n\t\t\tcomments = '#';\n\t\telse if (typeof comments !== 'string'\n\t\t\t|| Papa.BAD_DELIMITERS.indexOf(comments) > -1)\n\t\t\tcomments = false;\n\n\t\t// Newline must be valid: \\r, \\n, or \\r\\n\n\t\tif (newline !== '\\n' && newline !== '\\r' && newline !== '\\r\\n')\n\t\t\tnewline = '\\n';\n\n\t\t// We're gonna need these at the Parser scope\n\t\tvar cursor = 0;\n\t\tvar aborted = false;\n\n\t\tthis.parse = function(input, baseIndex, ignoreLastRow)\n\t\t{\n\t\t\t// For some reason, in Chrome, this speeds things up (!?)\n\t\t\tif (typeof input !== 'string')\n\t\t\t\tthrow new Error('Input must be a string');\n\n\t\t\t// We don't need to compute some of these every time parse() is called,\n\t\t\t// but having them in a more local scope seems to perform better\n\t\t\tvar inputLen = input.length,\n\t\t\t\tdelimLen = delim.length,\n\t\t\t\tnewlineLen = newline.length,\n\t\t\t\tcommentsLen = comments.length;\n\t\t\tvar stepIsFunction = isFunction(step);\n\n\t\t\t// Establish starting state\n\t\t\tcursor = 0;\n\t\t\tvar data = [], errors = [], row = [], lastCursor = 0;\n\n\t\t\tif (!input)\n\t\t\t\treturn returnable();\n\n\t\t\tif (fastMode || (fastMode !== false && input.indexOf(quoteChar) === -1))\n\t\t\t{\n\t\t\t\tvar rows = input.split(newline);\n\t\t\t\tfor (var i = 0; i < rows.length; i++)\n\t\t\t\t{\n\t\t\t\t\trow = rows[i];\n\t\t\t\t\tcursor += row.length;\n\n\t\t\t\t\tif (i !== rows.length - 1)\n\t\t\t\t\t\tcursor += newline.length;\n\t\t\t\t\telse if (ignoreLastRow)\n\t\t\t\t\t\treturn returnable();\n\t\t\t\t\tif (comments && row.substring(0, commentsLen) === comments)\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\tif (stepIsFunction)\n\t\t\t\t\t{\n\t\t\t\t\t\tdata = [];\n\t\t\t\t\t\tpushRow(row.split(delim));\n\t\t\t\t\t\tdoStep();\n\t\t\t\t\t\tif (aborted)\n\t\t\t\t\t\t\treturn returnable();\n\t\t\t\t\t}\n\t\t\t\t\telse\n\t\t\t\t\t\tpushRow(row.split(delim));\n\t\t\t\t\tif (preview && i >= preview)\n\t\t\t\t\t{\n\t\t\t\t\t\tdata = data.slice(0, preview);\n\t\t\t\t\t\treturn returnable(true);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn returnable();\n\t\t\t}\n\n\t\t\tvar nextDelim = input.indexOf(delim, cursor);\n\t\t\tvar nextNewline = input.indexOf(newline, cursor);\n\t\t\tvar quoteCharRegex = new RegExp(escapeRegExp(escapeChar) + escapeRegExp(quoteChar), 'g');\n\t\t\tvar quoteSearch = input.indexOf(quoteChar, cursor);\n\n\t\t\t// Parser loop\n\t\t\tfor (;;)\n\t\t\t{\n\t\t\t\t// Field has opening quote\n\t\t\t\tif (input[cursor] === quoteChar)\n\t\t\t\t{\n\t\t\t\t\t// Start our search for the closing quote where the cursor is\n\t\t\t\t\tquoteSearch = cursor;\n\n\t\t\t\t\t// Skip the opening quote\n\t\t\t\t\tcursor++;\n\n\t\t\t\t\tfor (;;)\n\t\t\t\t\t{\n\t\t\t\t\t\t// Find closing quote\n\t\t\t\t\t\tquoteSearch = input.indexOf(quoteChar, quoteSearch + 1);\n\n\t\t\t\t\t\t//No other quotes are found - no other delimiters\n\t\t\t\t\t\tif (quoteSearch === -1)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tif (!ignoreLastRow) {\n\t\t\t\t\t\t\t\t// No closing quote... what a pity\n\t\t\t\t\t\t\t\terrors.push({\n\t\t\t\t\t\t\t\t\ttype: 'Quotes',\n\t\t\t\t\t\t\t\t\tcode: 'MissingQuotes',\n\t\t\t\t\t\t\t\t\tmessage: 'Quoted field unterminated',\n\t\t\t\t\t\t\t\t\trow: data.length,\t// row has yet to be inserted\n\t\t\t\t\t\t\t\t\tindex: cursor\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn finish();\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Closing quote at EOF\n\t\t\t\t\t\tif (quoteSearch === inputLen - 1)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tvar value = input.substring(cursor, quoteSearch).replace(quoteCharRegex, quoteChar);\n\t\t\t\t\t\t\treturn finish(value);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// If this quote is escaped, it's part of the data; skip it\n\t\t\t\t\t\t// If the quote character is the escape character, then check if the next character is the escape character\n\t\t\t\t\t\tif (quoteChar === escapeChar &&  input[quoteSearch + 1] === escapeChar)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tquoteSearch++;\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// If the quote character is not the escape character, then check if the previous character was the escape character\n\t\t\t\t\t\tif (quoteChar !== escapeChar && quoteSearch !== 0 && input[quoteSearch - 1] === escapeChar)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif(nextDelim !== -1 && nextDelim < (quoteSearch + 1)) {\n\t\t\t\t\t\t\tnextDelim = input.indexOf(delim, (quoteSearch + 1));\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(nextNewline !== -1 && nextNewline < (quoteSearch + 1)) {\n\t\t\t\t\t\t\tnextNewline = input.indexOf(newline, (quoteSearch + 1));\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// Check up to nextDelim or nextNewline, whichever is closest\n\t\t\t\t\t\tvar checkUpTo = nextNewline === -1 ? nextDelim : Math.min(nextDelim, nextNewline);\n\t\t\t\t\t\tvar spacesBetweenQuoteAndDelimiter = extraSpaces(checkUpTo);\n\n\t\t\t\t\t\t// Closing quote followed by delimiter or 'unnecessary spaces + delimiter'\n\t\t\t\t\t\tif (input.substr(quoteSearch + 1 + spacesBetweenQuoteAndDelimiter, delimLen) === delim)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\trow.push(input.substring(cursor, quoteSearch).replace(quoteCharRegex, quoteChar));\n\t\t\t\t\t\t\tcursor = quoteSearch + 1 + spacesBetweenQuoteAndDelimiter + delimLen;\n\n\t\t\t\t\t\t\t// If char after following delimiter is not quoteChar, we find next quote char position\n\t\t\t\t\t\t\tif (input[quoteSearch + 1 + spacesBetweenQuoteAndDelimiter + delimLen] !== quoteChar)\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tquoteSearch = input.indexOf(quoteChar, cursor);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tnextDelim = input.indexOf(delim, cursor);\n\t\t\t\t\t\t\tnextNewline = input.indexOf(newline, cursor);\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tvar spacesBetweenQuoteAndNewLine = extraSpaces(nextNewline);\n\n\t\t\t\t\t\t// Closing quote followed by newline or 'unnecessary spaces + newLine'\n\t\t\t\t\t\tif (input.substring(quoteSearch + 1 + spacesBetweenQuoteAndNewLine, quoteSearch + 1 + spacesBetweenQuoteAndNewLine + newlineLen) === newline)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\trow.push(input.substring(cursor, quoteSearch).replace(quoteCharRegex, quoteChar));\n\t\t\t\t\t\t\tsaveRow(quoteSearch + 1 + spacesBetweenQuoteAndNewLine + newlineLen);\n\t\t\t\t\t\t\tnextDelim = input.indexOf(delim, cursor);\t// because we may have skipped the nextDelim in the quoted field\n\t\t\t\t\t\t\tquoteSearch = input.indexOf(quoteChar, cursor);\t// we search for first quote in next line\n\n\t\t\t\t\t\t\tif (stepIsFunction)\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdoStep();\n\t\t\t\t\t\t\t\tif (aborted)\n\t\t\t\t\t\t\t\t\treturn returnable();\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (preview && data.length >= preview)\n\t\t\t\t\t\t\t\treturn returnable(true);\n\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\n\n\t\t\t\t\t\t// Checks for valid closing quotes are complete (escaped quotes or quote followed by EOF/delimiter/newline) -- assume these quotes are part of an invalid text string\n\t\t\t\t\t\terrors.push({\n\t\t\t\t\t\t\ttype: 'Quotes',\n\t\t\t\t\t\t\tcode: 'InvalidQuotes',\n\t\t\t\t\t\t\tmessage: 'Trailing quote on quoted field is malformed',\n\t\t\t\t\t\t\trow: data.length,\t// row has yet to be inserted\n\t\t\t\t\t\t\tindex: cursor\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tquoteSearch++;\n\t\t\t\t\t\tcontinue;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t// Comment found at start of new line\n\t\t\t\tif (comments && row.length === 0 && input.substring(cursor, cursor + commentsLen) === comments)\n\t\t\t\t{\n\t\t\t\t\tif (nextNewline === -1)\t// Comment ends at EOF\n\t\t\t\t\t\treturn returnable();\n\t\t\t\t\tcursor = nextNewline + newlineLen;\n\t\t\t\t\tnextNewline = input.indexOf(newline, cursor);\n\t\t\t\t\tnextDelim = input.indexOf(delim, cursor);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t// Next delimiter comes before next newline, so we've reached end of field\n\t\t\t\tif (nextDelim !== -1 && (nextDelim < nextNewline || nextNewline === -1))\n\t\t\t\t{\n\t\t\t\t\trow.push(input.substring(cursor, nextDelim));\n\t\t\t\t\tcursor = nextDelim + delimLen;\n\t\t\t\t\t// we look for next delimiter char\n\t\t\t\t\tnextDelim = input.indexOf(delim, cursor);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t// End of row\n\t\t\t\tif (nextNewline !== -1)\n\t\t\t\t{\n\t\t\t\t\trow.push(input.substring(cursor, nextNewline));\n\t\t\t\t\tsaveRow(nextNewline + newlineLen);\n\n\t\t\t\t\tif (stepIsFunction)\n\t\t\t\t\t{\n\t\t\t\t\t\tdoStep();\n\t\t\t\t\t\tif (aborted)\n\t\t\t\t\t\t\treturn returnable();\n\t\t\t\t\t}\n\n\t\t\t\t\tif (preview && data.length >= preview)\n\t\t\t\t\t\treturn returnable(true);\n\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\treturn finish();\n\n\n\t\t\tfunction pushRow(row)\n\t\t\t{\n\t\t\t\tdata.push(row);\n\t\t\t\tlastCursor = cursor;\n\t\t\t}\n\n\t\t\t/**\n             * checks if there are extra spaces after closing quote and given index without any text\n             * if Yes, returns the number of spaces\n             */\n\t\t\tfunction extraSpaces(index) {\n\t\t\t\tvar spaceLength = 0;\n\t\t\t\tif (index !== -1) {\n\t\t\t\t\tvar textBetweenClosingQuoteAndIndex = input.substring(quoteSearch + 1, index);\n\t\t\t\t\tif (textBetweenClosingQuoteAndIndex && textBetweenClosingQuoteAndIndex.trim() === '') {\n\t\t\t\t\t\tspaceLength = textBetweenClosingQuoteAndIndex.length;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn spaceLength;\n\t\t\t}\n\n\t\t\t/**\n\t\t\t * Appends the remaining input from cursor to the end into\n\t\t\t * row, saves the row, calls step, and returns the results.\n\t\t\t */\n\t\t\tfunction finish(value)\n\t\t\t{\n\t\t\t\tif (ignoreLastRow)\n\t\t\t\t\treturn returnable();\n\t\t\t\tif (typeof value === 'undefined')\n\t\t\t\t\tvalue = input.substring(cursor);\n\t\t\t\trow.push(value);\n\t\t\t\tcursor = inputLen;\t// important in case parsing is paused\n\t\t\t\tpushRow(row);\n\t\t\t\tif (stepIsFunction)\n\t\t\t\t\tdoStep();\n\t\t\t\treturn returnable();\n\t\t\t}\n\n\t\t\t/**\n\t\t\t * Appends the current row to the results. It sets the cursor\n\t\t\t * to newCursor and finds the nextNewline. The caller should\n\t\t\t * take care to execute user's step function and check for\n\t\t\t * preview and end parsing if necessary.\n\t\t\t */\n\t\t\tfunction saveRow(newCursor)\n\t\t\t{\n\t\t\t\tcursor = newCursor;\n\t\t\t\tpushRow(row);\n\t\t\t\trow = [];\n\t\t\t\tnextNewline = input.indexOf(newline, cursor);\n\t\t\t}\n\n\t\t\t/** Returns an object with the results, errors, and meta. */\n\t\t\tfunction returnable(stopped)\n\t\t\t{\n\t\t\t\tif (config.header && !baseIndex && data.length && !headerParsed)\n\t\t\t\t{\n\t\t\t\t\tconst result = data[0];\n\t\t\t\t\tconst headerCount = Object.create(null); // To track the count of each base header\n\t\t\t\t\tconst usedHeaders = new Set(result); // To track used headers and avoid duplicates\n\t\t\t\t\tlet duplicateHeaders = false;\n\n\t\t\t\t\tfor (let i = 0; i < result.length; i++) {\n\t\t\t\t\t\tlet header = result[i];\n\t\t\t\t\t\tif (isFunction(config.transformHeader))\n\t\t\t\t\t\t\theader = config.transformHeader(header, i);\n\n\t\t\t\t\t\tif (!headerCount[header]) {\n\t\t\t\t\t\t\theaderCount[header] = 1;\n\t\t\t\t\t\t\tresult[i] = header;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tlet newHeader;\n\t\t\t\t\t\t\tlet suffixCount = headerCount[header];\n\n\t\t\t\t\t\t\t// Find a unique new header\n\t\t\t\t\t\t\tdo {\n\t\t\t\t\t\t\t\tnewHeader = `${header}_${suffixCount}`;\n\t\t\t\t\t\t\t\tsuffixCount++;\n\t\t\t\t\t\t\t} while (usedHeaders.has(newHeader));\n\n\t\t\t\t\t\t\tusedHeaders.add(newHeader); // Mark this new Header as used\n\t\t\t\t\t\t\tresult[i] = newHeader;\n\t\t\t\t\t\t\theaderCount[header]++;\n\t\t\t\t\t\t\tduplicateHeaders = true;\n\t\t\t\t\t\t\tif (renamedHeaders === null) {\n\t\t\t\t\t\t\t\trenamedHeaders = {};\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\trenamedHeaders[newHeader] = header;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tusedHeaders.add(header); // Ensure the original header is marked as used\n\t\t\t\t\t}\n\t\t\t\t\tif (duplicateHeaders) {\n\t\t\t\t\t\tconsole.warn('Duplicate headers found and renamed.');\n\t\t\t\t\t}\n\t\t\t\t\theaderParsed = true;\n\t\t\t\t}\n\t\t\t\treturn {\n\t\t\t\t\tdata: data,\n\t\t\t\t\terrors: errors,\n\t\t\t\t\tmeta: {\n\t\t\t\t\t\tdelimiter: delim,\n\t\t\t\t\t\tlinebreak: newline,\n\t\t\t\t\t\taborted: aborted,\n\t\t\t\t\t\ttruncated: !!stopped,\n\t\t\t\t\t\tcursor: lastCursor + (baseIndex || 0),\n\t\t\t\t\t\trenamedHeaders: renamedHeaders\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t/** Executes the user's step function and resets data & errors. */\n\t\t\tfunction doStep()\n\t\t\t{\n\t\t\t\tstep(returnable());\n\t\t\t\tdata = [];\n\t\t\t\terrors = [];\n\t\t\t}\n\t\t};\n\n\t\t/** Sets the abort flag */\n\t\tthis.abort = function()\n\t\t{\n\t\t\taborted = true;\n\t\t};\n\n\t\t/** Gets the cursor position */\n\t\tthis.getCharIndex = function()\n\t\t{\n\t\t\treturn cursor;\n\t\t};\n\t}\n\n\n\tfunction newWorker()\n\t{\n\t\tif (!Papa.WORKERS_SUPPORTED)\n\t\t\treturn false;\n\n\t\tvar workerUrl = getWorkerBlob();\n\t\tvar w = new global.Worker(workerUrl);\n\t\tw.onmessage = mainThreadReceivedMessage;\n\t\tw.id = workerIdCounter++;\n\t\tworkers[w.id] = w;\n\t\treturn w;\n\t}\n\n\t/** Callback when main thread receives a message */\n\tfunction mainThreadReceivedMessage(e)\n\t{\n\t\tvar msg = e.data;\n\t\tvar worker = workers[msg.workerId];\n\t\tvar aborted = false;\n\n\t\tif (msg.error)\n\t\t\tworker.userError(msg.error, msg.file);\n\t\telse if (msg.results && msg.results.data)\n\t\t{\n\t\t\tvar abort = function() {\n\t\t\t\taborted = true;\n\t\t\t\tcompleteWorker(msg.workerId, { data: [], errors: [], meta: { aborted: true } });\n\t\t\t};\n\n\t\t\tvar handle = {\n\t\t\t\tabort: abort,\n\t\t\t\tpause: notImplemented,\n\t\t\t\tresume: notImplemented\n\t\t\t};\n\n\t\t\tif (isFunction(worker.userStep))\n\t\t\t{\n\t\t\t\tfor (var i = 0; i < msg.results.data.length; i++)\n\t\t\t\t{\n\t\t\t\t\tworker.userStep({\n\t\t\t\t\t\tdata: msg.results.data[i],\n\t\t\t\t\t\terrors: msg.results.errors,\n\t\t\t\t\t\tmeta: msg.results.meta\n\t\t\t\t\t}, handle);\n\t\t\t\t\tif (aborted)\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\tdelete msg.results;\t// free memory ASAP\n\t\t\t}\n\t\t\telse if (isFunction(worker.userChunk))\n\t\t\t{\n\t\t\t\tworker.userChunk(msg.results, handle, msg.file);\n\t\t\t\tdelete msg.results;\n\t\t\t}\n\t\t}\n\n\t\tif (msg.finished && !aborted)\n\t\t\tcompleteWorker(msg.workerId, msg.results);\n\t}\n\n\tfunction completeWorker(workerId, results) {\n\t\tvar worker = workers[workerId];\n\t\tif (isFunction(worker.userComplete))\n\t\t\tworker.userComplete(results);\n\t\tworker.terminate();\n\t\tdelete workers[workerId];\n\t}\n\n\tfunction notImplemented() {\n\t\tthrow new Error('Not implemented.');\n\t}\n\n\t/** Callback when worker thread receives a message */\n\tfunction workerThreadReceivedMessage(e)\n\t{\n\t\tvar msg = e.data;\n\n\t\tif (typeof Papa.WORKER_ID === 'undefined' && msg)\n\t\t\tPapa.WORKER_ID = msg.workerId;\n\n\t\tif (typeof msg.input === 'string')\n\t\t{\n\t\t\tglobal.postMessage({\n\t\t\t\tworkerId: Papa.WORKER_ID,\n\t\t\t\tresults: Papa.parse(msg.input, msg.config),\n\t\t\t\tfinished: true\n\t\t\t});\n\t\t}\n\t\telse if ((global.File && msg.input instanceof File) || msg.input instanceof Object)\t// thank you, Safari (see issue #106)\n\t\t{\n\t\t\tvar results = Papa.parse(msg.input, msg.config);\n\t\t\tif (results)\n\t\t\t\tglobal.postMessage({\n\t\t\t\t\tworkerId: Papa.WORKER_ID,\n\t\t\t\t\tresults: results,\n\t\t\t\t\tfinished: true\n\t\t\t\t});\n\t\t}\n\t}\n\n\t/** Makes a deep copy of an array or object (mostly) */\n\tfunction copy(obj)\n\t{\n\t\tif (typeof obj !== 'object' || obj === null)\n\t\t\treturn obj;\n\t\tvar cpy = Array.isArray(obj) ? [] : {};\n\t\tfor (var key in obj)\n\t\t\tcpy[key] = copy(obj[key]);\n\t\treturn cpy;\n\t}\n\n\tfunction bindFunction(f, self)\n\t{\n\t\treturn function() { f.apply(self, arguments); };\n\t}\n\tfunction isFunction(func)\n\t{\n\t\treturn typeof func === 'function';\n\t}\n\n\treturn Papa;\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/papaparse/papaparse.js\n");

/***/ })

};
;