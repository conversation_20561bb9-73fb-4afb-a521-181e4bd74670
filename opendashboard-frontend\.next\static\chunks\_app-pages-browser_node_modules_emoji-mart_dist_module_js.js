"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_emoji-mart_dist_module_js"],{

/***/ "(app-pages-browser)/./node_modules/emoji-mart/dist/module.js":
/*!************************************************!*\
  !*** ./node_modules/emoji-mart/dist/module.js ***!
  \************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Data: function() { return /* binding */ $7adb23b0109cc36a$export$2d0294657ab35f1b; },\n/* harmony export */   Emoji: function() { return /* binding */ $331b4160623139bf$export$2e2bcd8739ae039; },\n/* harmony export */   FrequentlyUsed: function() { return /* binding */ $b22cfd0a55410b4f$export$2e2bcd8739ae039; },\n/* harmony export */   I18n: function() { return /* binding */ $7adb23b0109cc36a$export$dbe3113d60765c1a; },\n/* harmony export */   Picker: function() { return /* binding */ $efa000751917694d$export$2e2bcd8739ae039; },\n/* harmony export */   SafeFlags: function() { return /* binding */ $e6eae5155b87f591$export$bcb25aa587e9cb13; },\n/* harmony export */   SearchIndex: function() { return /* binding */ $c4d155af13ad4d4b$export$2e2bcd8739ae039; },\n/* harmony export */   Store: function() { return /* binding */ $f72b75cf796873c7$export$2e2bcd8739ae039; },\n/* harmony export */   getEmojiDataFromNative: function() { return /* binding */ $693b183b0a78708f$export$5ef5574deca44bc0; },\n/* harmony export */   init: function() { return /* binding */ $7adb23b0109cc36a$export$2cd8252107eb640b; }\n/* harmony export */ });\nfunction $parcel$interopDefault(a) {\n  return a && a.__esModule ? a.default : a;\n}\nfunction $c770c458706daa72$export$2e2bcd8739ae039(obj, key, value) {\n    if (key in obj) Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n    });\n    else obj[key] = value;\n    return obj;\n}\n\n\nvar $fb96b826c0c5f37a$var$n, $fb96b826c0c5f37a$export$41c562ebe57d11e2, $fb96b826c0c5f37a$var$u, $fb96b826c0c5f37a$export$a8257692ac88316c, $fb96b826c0c5f37a$var$t, $fb96b826c0c5f37a$var$r, $fb96b826c0c5f37a$var$o, $fb96b826c0c5f37a$var$f, $fb96b826c0c5f37a$var$e = {}, $fb96b826c0c5f37a$var$c = [], $fb96b826c0c5f37a$var$s = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\nfunction $fb96b826c0c5f37a$var$a(n1, l1) {\n    for(var u1 in l1)n1[u1] = l1[u1];\n    return n1;\n}\nfunction $fb96b826c0c5f37a$var$h(n2) {\n    var l2 = n2.parentNode;\n    l2 && l2.removeChild(n2);\n}\nfunction $fb96b826c0c5f37a$export$c8a8987d4410bf2d(l3, u2, i1) {\n    var t1, r1, o1, f1 = {};\n    for(o1 in u2)\"key\" == o1 ? t1 = u2[o1] : \"ref\" == o1 ? r1 = u2[o1] : f1[o1] = u2[o1];\n    if (arguments.length > 2 && (f1.children = arguments.length > 3 ? $fb96b826c0c5f37a$var$n.call(arguments, 2) : i1), \"function\" == typeof l3 && null != l3.defaultProps) for(o1 in l3.defaultProps)void 0 === f1[o1] && (f1[o1] = l3.defaultProps[o1]);\n    return $fb96b826c0c5f37a$var$y(l3, f1, t1, r1, null);\n}\nfunction $fb96b826c0c5f37a$var$y(n3, i2, t2, r2, o2) {\n    var f2 = {\n        type: n3,\n        props: i2,\n        key: t2,\n        ref: r2,\n        __k: null,\n        __: null,\n        __b: 0,\n        __e: null,\n        __d: void 0,\n        __c: null,\n        __h: null,\n        constructor: void 0,\n        __v: null == o2 ? ++$fb96b826c0c5f37a$var$u : o2\n    };\n    return null == o2 && null != $fb96b826c0c5f37a$export$41c562ebe57d11e2.vnode && $fb96b826c0c5f37a$export$41c562ebe57d11e2.vnode(f2), f2;\n}\nfunction $fb96b826c0c5f37a$export$7d1e3a5e95ceca43() {\n    return {\n        current: null\n    };\n}\nfunction $fb96b826c0c5f37a$export$ffb0004e005737fa(n4) {\n    return n4.children;\n}\nfunction $fb96b826c0c5f37a$export$16fa2f45be04daa8(n5, l4) {\n    this.props = n5, this.context = l4;\n}\nfunction $fb96b826c0c5f37a$var$k(n6, l5) {\n    if (null == l5) return n6.__ ? $fb96b826c0c5f37a$var$k(n6.__, n6.__.__k.indexOf(n6) + 1) : null;\n    for(var u3; l5 < n6.__k.length; l5++)if (null != (u3 = n6.__k[l5]) && null != u3.__e) return u3.__e;\n    return \"function\" == typeof n6.type ? $fb96b826c0c5f37a$var$k(n6) : null;\n}\nfunction $fb96b826c0c5f37a$var$b(n7) {\n    var l6, u4;\n    if (null != (n7 = n7.__) && null != n7.__c) {\n        for(n7.__e = n7.__c.base = null, l6 = 0; l6 < n7.__k.length; l6++)if (null != (u4 = n7.__k[l6]) && null != u4.__e) {\n            n7.__e = n7.__c.base = u4.__e;\n            break;\n        }\n        return $fb96b826c0c5f37a$var$b(n7);\n    }\n}\nfunction $fb96b826c0c5f37a$var$m(n8) {\n    (!n8.__d && (n8.__d = !0) && $fb96b826c0c5f37a$var$t.push(n8) && !$fb96b826c0c5f37a$var$g.__r++ || $fb96b826c0c5f37a$var$o !== $fb96b826c0c5f37a$export$41c562ebe57d11e2.debounceRendering) && (($fb96b826c0c5f37a$var$o = $fb96b826c0c5f37a$export$41c562ebe57d11e2.debounceRendering) || $fb96b826c0c5f37a$var$r)($fb96b826c0c5f37a$var$g);\n}\nfunction $fb96b826c0c5f37a$var$g() {\n    for(var n9; $fb96b826c0c5f37a$var$g.__r = $fb96b826c0c5f37a$var$t.length;)n9 = $fb96b826c0c5f37a$var$t.sort(function(n10, l7) {\n        return n10.__v.__b - l7.__v.__b;\n    }), $fb96b826c0c5f37a$var$t = [], n9.some(function(n11) {\n        var l8, u5, i3, t3, r3, o3;\n        n11.__d && (r3 = (t3 = (l8 = n11).__v).__e, (o3 = l8.__P) && (u5 = [], (i3 = $fb96b826c0c5f37a$var$a({}, t3)).__v = t3.__v + 1, $fb96b826c0c5f37a$var$j(o3, t3, i3, l8.__n, void 0 !== o3.ownerSVGElement, null != t3.__h ? [\n            r3\n        ] : null, u5, null == r3 ? $fb96b826c0c5f37a$var$k(t3) : r3, t3.__h), $fb96b826c0c5f37a$var$z(u5, t3), t3.__e != r3 && $fb96b826c0c5f37a$var$b(t3)));\n    });\n}\nfunction $fb96b826c0c5f37a$var$w(n12, l9, u6, i4, t4, r4, o4, f3, s1, a1) {\n    var h1, v1, p1, _1, b1, m1, g1, w1 = i4 && i4.__k || $fb96b826c0c5f37a$var$c, A1 = w1.length;\n    for(u6.__k = [], h1 = 0; h1 < l9.length; h1++)if (null != (_1 = u6.__k[h1] = null == (_1 = l9[h1]) || \"boolean\" == typeof _1 ? null : \"string\" == typeof _1 || \"number\" == typeof _1 || \"bigint\" == typeof _1 ? $fb96b826c0c5f37a$var$y(null, _1, null, null, _1) : Array.isArray(_1) ? $fb96b826c0c5f37a$var$y($fb96b826c0c5f37a$export$ffb0004e005737fa, {\n        children: _1\n    }, null, null, null) : _1.__b > 0 ? $fb96b826c0c5f37a$var$y(_1.type, _1.props, _1.key, null, _1.__v) : _1)) {\n        if (_1.__ = u6, _1.__b = u6.__b + 1, null === (p1 = w1[h1]) || p1 && _1.key == p1.key && _1.type === p1.type) w1[h1] = void 0;\n        else for(v1 = 0; v1 < A1; v1++){\n            if ((p1 = w1[v1]) && _1.key == p1.key && _1.type === p1.type) {\n                w1[v1] = void 0;\n                break;\n            }\n            p1 = null;\n        }\n        $fb96b826c0c5f37a$var$j(n12, _1, p1 = p1 || $fb96b826c0c5f37a$var$e, t4, r4, o4, f3, s1, a1), b1 = _1.__e, (v1 = _1.ref) && p1.ref != v1 && (g1 || (g1 = []), p1.ref && g1.push(p1.ref, null, _1), g1.push(v1, _1.__c || b1, _1)), null != b1 ? (null == m1 && (m1 = b1), \"function\" == typeof _1.type && _1.__k === p1.__k ? _1.__d = s1 = $fb96b826c0c5f37a$var$x(_1, s1, n12) : s1 = $fb96b826c0c5f37a$var$P(n12, _1, p1, w1, b1, s1), \"function\" == typeof u6.type && (u6.__d = s1)) : s1 && p1.__e == s1 && s1.parentNode != n12 && (s1 = $fb96b826c0c5f37a$var$k(p1));\n    }\n    for(u6.__e = m1, h1 = A1; h1--;)null != w1[h1] && (\"function\" == typeof u6.type && null != w1[h1].__e && w1[h1].__e == u6.__d && (u6.__d = $fb96b826c0c5f37a$var$k(i4, h1 + 1)), $fb96b826c0c5f37a$var$N(w1[h1], w1[h1]));\n    if (g1) for(h1 = 0; h1 < g1.length; h1++)$fb96b826c0c5f37a$var$M(g1[h1], g1[++h1], g1[++h1]);\n}\nfunction $fb96b826c0c5f37a$var$x(n13, l10, u7) {\n    for(var i5, t5 = n13.__k, r5 = 0; t5 && r5 < t5.length; r5++)(i5 = t5[r5]) && (i5.__ = n13, l10 = \"function\" == typeof i5.type ? $fb96b826c0c5f37a$var$x(i5, l10, u7) : $fb96b826c0c5f37a$var$P(u7, i5, i5, t5, i5.__e, l10));\n    return l10;\n}\nfunction $fb96b826c0c5f37a$export$47e4c5b300681277(n14, l11) {\n    return l11 = l11 || [], null == n14 || \"boolean\" == typeof n14 || (Array.isArray(n14) ? n14.some(function(n15) {\n        $fb96b826c0c5f37a$export$47e4c5b300681277(n15, l11);\n    }) : l11.push(n14)), l11;\n}\nfunction $fb96b826c0c5f37a$var$P(n16, l12, u8, i6, t6, r6) {\n    var o5, f4, e1;\n    if (void 0 !== l12.__d) o5 = l12.__d, l12.__d = void 0;\n    else if (null == u8 || t6 != r6 || null == t6.parentNode) n: if (null == r6 || r6.parentNode !== n16) n16.appendChild(t6), o5 = null;\n    else {\n        for(f4 = r6, e1 = 0; (f4 = f4.nextSibling) && e1 < i6.length; e1 += 2)if (f4 == t6) break n;\n        n16.insertBefore(t6, r6), o5 = r6;\n    }\n    return void 0 !== o5 ? o5 : t6.nextSibling;\n}\nfunction $fb96b826c0c5f37a$var$C(n17, l13, u9, i7, t7) {\n    var r7;\n    for(r7 in u9)\"children\" === r7 || \"key\" === r7 || r7 in l13 || $fb96b826c0c5f37a$var$H(n17, r7, null, u9[r7], i7);\n    for(r7 in l13)t7 && \"function\" != typeof l13[r7] || \"children\" === r7 || \"key\" === r7 || \"value\" === r7 || \"checked\" === r7 || u9[r7] === l13[r7] || $fb96b826c0c5f37a$var$H(n17, r7, l13[r7], u9[r7], i7);\n}\nfunction $fb96b826c0c5f37a$var$$(n18, l14, u10) {\n    \"-\" === l14[0] ? n18.setProperty(l14, u10) : n18[l14] = null == u10 ? \"\" : \"number\" != typeof u10 || $fb96b826c0c5f37a$var$s.test(l14) ? u10 : u10 + \"px\";\n}\nfunction $fb96b826c0c5f37a$var$H(n19, l15, u11, i8, t8) {\n    var r8;\n    n: if (\"style\" === l15) {\n        if (\"string\" == typeof u11) n19.style.cssText = u11;\n        else {\n            if (\"string\" == typeof i8 && (n19.style.cssText = i8 = \"\"), i8) for(l15 in i8)u11 && l15 in u11 || $fb96b826c0c5f37a$var$$(n19.style, l15, \"\");\n            if (u11) for(l15 in u11)i8 && u11[l15] === i8[l15] || $fb96b826c0c5f37a$var$$(n19.style, l15, u11[l15]);\n        }\n    } else if (\"o\" === l15[0] && \"n\" === l15[1]) r8 = l15 !== (l15 = l15.replace(/Capture$/, \"\")), l15 = l15.toLowerCase() in n19 ? l15.toLowerCase().slice(2) : l15.slice(2), n19.l || (n19.l = {}), n19.l[l15 + r8] = u11, u11 ? i8 || n19.addEventListener(l15, r8 ? $fb96b826c0c5f37a$var$T : $fb96b826c0c5f37a$var$I, r8) : n19.removeEventListener(l15, r8 ? $fb96b826c0c5f37a$var$T : $fb96b826c0c5f37a$var$I, r8);\n    else if (\"dangerouslySetInnerHTML\" !== l15) {\n        if (t8) l15 = l15.replace(/xlink[H:h]/, \"h\").replace(/sName$/, \"s\");\n        else if (\"href\" !== l15 && \"list\" !== l15 && \"form\" !== l15 && \"tabIndex\" !== l15 && \"download\" !== l15 && l15 in n19) try {\n            n19[l15] = null == u11 ? \"\" : u11;\n            break n;\n        } catch (n) {}\n        \"function\" == typeof u11 || (null != u11 && (!1 !== u11 || \"a\" === l15[0] && \"r\" === l15[1]) ? n19.setAttribute(l15, u11) : n19.removeAttribute(l15));\n    }\n}\nfunction $fb96b826c0c5f37a$var$I(n20) {\n    this.l[n20.type + !1]($fb96b826c0c5f37a$export$41c562ebe57d11e2.event ? $fb96b826c0c5f37a$export$41c562ebe57d11e2.event(n20) : n20);\n}\nfunction $fb96b826c0c5f37a$var$T(n21) {\n    this.l[n21.type + !0]($fb96b826c0c5f37a$export$41c562ebe57d11e2.event ? $fb96b826c0c5f37a$export$41c562ebe57d11e2.event(n21) : n21);\n}\nfunction $fb96b826c0c5f37a$var$j(n22, u12, i9, t9, r9, o6, f5, e2, c1) {\n    var s2, h2, v2, y1, p2, k1, b2, m2, g2, x1, A2, P1 = u12.type;\n    if (void 0 !== u12.constructor) return null;\n    null != i9.__h && (c1 = i9.__h, e2 = u12.__e = i9.__e, u12.__h = null, o6 = [\n        e2\n    ]), (s2 = $fb96b826c0c5f37a$export$41c562ebe57d11e2.__b) && s2(u12);\n    try {\n        n: if (\"function\" == typeof P1) {\n            if (m2 = u12.props, g2 = (s2 = P1.contextType) && t9[s2.__c], x1 = s2 ? g2 ? g2.props.value : s2.__ : t9, i9.__c ? b2 = (h2 = u12.__c = i9.__c).__ = h2.__E : (\"prototype\" in P1 && P1.prototype.render ? u12.__c = h2 = new P1(m2, x1) : (u12.__c = h2 = new $fb96b826c0c5f37a$export$16fa2f45be04daa8(m2, x1), h2.constructor = P1, h2.render = $fb96b826c0c5f37a$var$O), g2 && g2.sub(h2), h2.props = m2, h2.state || (h2.state = {}), h2.context = x1, h2.__n = t9, v2 = h2.__d = !0, h2.__h = []), null == h2.__s && (h2.__s = h2.state), null != P1.getDerivedStateFromProps && (h2.__s == h2.state && (h2.__s = $fb96b826c0c5f37a$var$a({}, h2.__s)), $fb96b826c0c5f37a$var$a(h2.__s, P1.getDerivedStateFromProps(m2, h2.__s))), y1 = h2.props, p2 = h2.state, v2) null == P1.getDerivedStateFromProps && null != h2.componentWillMount && h2.componentWillMount(), null != h2.componentDidMount && h2.__h.push(h2.componentDidMount);\n            else {\n                if (null == P1.getDerivedStateFromProps && m2 !== y1 && null != h2.componentWillReceiveProps && h2.componentWillReceiveProps(m2, x1), !h2.__e && null != h2.shouldComponentUpdate && !1 === h2.shouldComponentUpdate(m2, h2.__s, x1) || u12.__v === i9.__v) {\n                    h2.props = m2, h2.state = h2.__s, u12.__v !== i9.__v && (h2.__d = !1), h2.__v = u12, u12.__e = i9.__e, u12.__k = i9.__k, u12.__k.forEach(function(n23) {\n                        n23 && (n23.__ = u12);\n                    }), h2.__h.length && f5.push(h2);\n                    break n;\n                }\n                null != h2.componentWillUpdate && h2.componentWillUpdate(m2, h2.__s, x1), null != h2.componentDidUpdate && h2.__h.push(function() {\n                    h2.componentDidUpdate(y1, p2, k1);\n                });\n            }\n            h2.context = x1, h2.props = m2, h2.state = h2.__s, (s2 = $fb96b826c0c5f37a$export$41c562ebe57d11e2.__r) && s2(u12), h2.__d = !1, h2.__v = u12, h2.__P = n22, s2 = h2.render(h2.props, h2.state, h2.context), h2.state = h2.__s, null != h2.getChildContext && (t9 = $fb96b826c0c5f37a$var$a($fb96b826c0c5f37a$var$a({}, t9), h2.getChildContext())), v2 || null == h2.getSnapshotBeforeUpdate || (k1 = h2.getSnapshotBeforeUpdate(y1, p2)), A2 = null != s2 && s2.type === $fb96b826c0c5f37a$export$ffb0004e005737fa && null == s2.key ? s2.props.children : s2, $fb96b826c0c5f37a$var$w(n22, Array.isArray(A2) ? A2 : [\n                A2\n            ], u12, i9, t9, r9, o6, f5, e2, c1), h2.base = u12.__e, u12.__h = null, h2.__h.length && f5.push(h2), b2 && (h2.__E = h2.__ = null), h2.__e = !1;\n        } else null == o6 && u12.__v === i9.__v ? (u12.__k = i9.__k, u12.__e = i9.__e) : u12.__e = $fb96b826c0c5f37a$var$L(i9.__e, u12, i9, t9, r9, o6, f5, c1);\n        (s2 = $fb96b826c0c5f37a$export$41c562ebe57d11e2.diffed) && s2(u12);\n    } catch (n24) {\n        u12.__v = null, (c1 || null != o6) && (u12.__e = e2, u12.__h = !!c1, o6[o6.indexOf(e2)] = null), $fb96b826c0c5f37a$export$41c562ebe57d11e2.__e(n24, u12, i9);\n    }\n}\nfunction $fb96b826c0c5f37a$var$z(n25, u13) {\n    $fb96b826c0c5f37a$export$41c562ebe57d11e2.__c && $fb96b826c0c5f37a$export$41c562ebe57d11e2.__c(u13, n25), n25.some(function(u14) {\n        try {\n            n25 = u14.__h, u14.__h = [], n25.some(function(n26) {\n                n26.call(u14);\n            });\n        } catch (n27) {\n            $fb96b826c0c5f37a$export$41c562ebe57d11e2.__e(n27, u14.__v);\n        }\n    });\n}\nfunction $fb96b826c0c5f37a$var$L(l16, u15, i10, t10, r10, o7, f6, c2) {\n    var s3, a2, v3, y2 = i10.props, p3 = u15.props, d1 = u15.type, _2 = 0;\n    if (\"svg\" === d1 && (r10 = !0), null != o7) {\n        for(; _2 < o7.length; _2++)if ((s3 = o7[_2]) && \"setAttribute\" in s3 == !!d1 && (d1 ? s3.localName === d1 : 3 === s3.nodeType)) {\n            l16 = s3, o7[_2] = null;\n            break;\n        }\n    }\n    if (null == l16) {\n        if (null === d1) return document.createTextNode(p3);\n        l16 = r10 ? document.createElementNS(\"http://www.w3.org/2000/svg\", d1) : document.createElement(d1, p3.is && p3), o7 = null, c2 = !1;\n    }\n    if (null === d1) y2 === p3 || c2 && l16.data === p3 || (l16.data = p3);\n    else {\n        if (o7 = o7 && $fb96b826c0c5f37a$var$n.call(l16.childNodes), a2 = (y2 = i10.props || $fb96b826c0c5f37a$var$e).dangerouslySetInnerHTML, v3 = p3.dangerouslySetInnerHTML, !c2) {\n            if (null != o7) for(y2 = {}, _2 = 0; _2 < l16.attributes.length; _2++)y2[l16.attributes[_2].name] = l16.attributes[_2].value;\n            (v3 || a2) && (v3 && (a2 && v3.__html == a2.__html || v3.__html === l16.innerHTML) || (l16.innerHTML = v3 && v3.__html || \"\"));\n        }\n        if ($fb96b826c0c5f37a$var$C(l16, p3, y2, r10, c2), v3) u15.__k = [];\n        else if (_2 = u15.props.children, $fb96b826c0c5f37a$var$w(l16, Array.isArray(_2) ? _2 : [\n            _2\n        ], u15, i10, t10, r10 && \"foreignObject\" !== d1, o7, f6, o7 ? o7[0] : i10.__k && $fb96b826c0c5f37a$var$k(i10, 0), c2), null != o7) for(_2 = o7.length; _2--;)null != o7[_2] && $fb96b826c0c5f37a$var$h(o7[_2]);\n        c2 || (\"value\" in p3 && void 0 !== (_2 = p3.value) && (_2 !== y2.value || _2 !== l16.value || \"progress\" === d1 && !_2) && $fb96b826c0c5f37a$var$H(l16, \"value\", _2, y2.value, !1), \"checked\" in p3 && void 0 !== (_2 = p3.checked) && _2 !== l16.checked && $fb96b826c0c5f37a$var$H(l16, \"checked\", _2, y2.checked, !1));\n    }\n    return l16;\n}\nfunction $fb96b826c0c5f37a$var$M(n28, u16, i11) {\n    try {\n        \"function\" == typeof n28 ? n28(u16) : n28.current = u16;\n    } catch (n29) {\n        $fb96b826c0c5f37a$export$41c562ebe57d11e2.__e(n29, i11);\n    }\n}\nfunction $fb96b826c0c5f37a$var$N(n30, u17, i12) {\n    var t11, r11;\n    if ($fb96b826c0c5f37a$export$41c562ebe57d11e2.unmount && $fb96b826c0c5f37a$export$41c562ebe57d11e2.unmount(n30), (t11 = n30.ref) && (t11.current && t11.current !== n30.__e || $fb96b826c0c5f37a$var$M(t11, null, u17)), null != (t11 = n30.__c)) {\n        if (t11.componentWillUnmount) try {\n            t11.componentWillUnmount();\n        } catch (n31) {\n            $fb96b826c0c5f37a$export$41c562ebe57d11e2.__e(n31, u17);\n        }\n        t11.base = t11.__P = null;\n    }\n    if (t11 = n30.__k) for(r11 = 0; r11 < t11.length; r11++)t11[r11] && $fb96b826c0c5f37a$var$N(t11[r11], u17, \"function\" != typeof n30.type);\n    i12 || null == n30.__e || $fb96b826c0c5f37a$var$h(n30.__e), n30.__e = n30.__d = void 0;\n}\nfunction $fb96b826c0c5f37a$var$O(n32, l, u18) {\n    return this.constructor(n32, u18);\n}\nfunction $fb96b826c0c5f37a$export$b3890eb0ae9dca99(u19, i13, t12) {\n    var r12, o8, f7;\n    $fb96b826c0c5f37a$export$41c562ebe57d11e2.__ && $fb96b826c0c5f37a$export$41c562ebe57d11e2.__(u19, i13), o8 = (r12 = \"function\" == typeof t12) ? null : t12 && t12.__k || i13.__k, f7 = [], $fb96b826c0c5f37a$var$j(i13, u19 = (!r12 && t12 || i13).__k = $fb96b826c0c5f37a$export$c8a8987d4410bf2d($fb96b826c0c5f37a$export$ffb0004e005737fa, null, [\n        u19\n    ]), o8 || $fb96b826c0c5f37a$var$e, $fb96b826c0c5f37a$var$e, void 0 !== i13.ownerSVGElement, !r12 && t12 ? [\n        t12\n    ] : o8 ? null : i13.firstChild ? $fb96b826c0c5f37a$var$n.call(i13.childNodes) : null, f7, !r12 && t12 ? t12 : o8 ? o8.__e : i13.firstChild, r12), $fb96b826c0c5f37a$var$z(f7, u19);\n}\nfunction $fb96b826c0c5f37a$export$fa8d919ba61d84db(n33, l17) {\n    $fb96b826c0c5f37a$export$b3890eb0ae9dca99(n33, l17, $fb96b826c0c5f37a$export$fa8d919ba61d84db);\n}\nfunction $fb96b826c0c5f37a$export$e530037191fcd5d7(l18, u20, i14) {\n    var t13, r13, o9, f8 = $fb96b826c0c5f37a$var$a({}, l18.props);\n    for(o9 in u20)\"key\" == o9 ? t13 = u20[o9] : \"ref\" == o9 ? r13 = u20[o9] : f8[o9] = u20[o9];\n    return arguments.length > 2 && (f8.children = arguments.length > 3 ? $fb96b826c0c5f37a$var$n.call(arguments, 2) : i14), $fb96b826c0c5f37a$var$y(l18.type, f8, t13 || l18.key, r13 || l18.ref, null);\n}\nfunction $fb96b826c0c5f37a$export$fd42f52fd3ae1109(n34, l19) {\n    var u21 = {\n        __c: l19 = \"__cC\" + $fb96b826c0c5f37a$var$f++,\n        __: n34,\n        Consumer: function(n35, l20) {\n            return n35.children(l20);\n        },\n        Provider: function(n36) {\n            var u22, i15;\n            return this.getChildContext || (u22 = [], (i15 = {})[l19] = this, this.getChildContext = function() {\n                return i15;\n            }, this.shouldComponentUpdate = function(n37) {\n                this.props.value !== n37.value && u22.some($fb96b826c0c5f37a$var$m);\n            }, this.sub = function(n38) {\n                u22.push(n38);\n                var l21 = n38.componentWillUnmount;\n                n38.componentWillUnmount = function() {\n                    u22.splice(u22.indexOf(n38), 1), l21 && l21.call(n38);\n                };\n            }), n36.children;\n        }\n    };\n    return u21.Provider.__ = u21.Consumer.contextType = u21;\n}\n$fb96b826c0c5f37a$var$n = $fb96b826c0c5f37a$var$c.slice, $fb96b826c0c5f37a$export$41c562ebe57d11e2 = {\n    __e: function(n39, l22) {\n        for(var u23, i16, t14; l22 = l22.__;)if ((u23 = l22.__c) && !u23.__) try {\n            if ((i16 = u23.constructor) && null != i16.getDerivedStateFromError && (u23.setState(i16.getDerivedStateFromError(n39)), t14 = u23.__d), null != u23.componentDidCatch && (u23.componentDidCatch(n39), t14 = u23.__d), t14) return u23.__E = u23;\n        } catch (l23) {\n            n39 = l23;\n        }\n        throw n39;\n    }\n}, $fb96b826c0c5f37a$var$u = 0, $fb96b826c0c5f37a$export$a8257692ac88316c = function(n40) {\n    return null != n40 && void 0 === n40.constructor;\n}, $fb96b826c0c5f37a$export$16fa2f45be04daa8.prototype.setState = function(n41, l24) {\n    var u24;\n    u24 = null != this.__s && this.__s !== this.state ? this.__s : this.__s = $fb96b826c0c5f37a$var$a({}, this.state), \"function\" == typeof n41 && (n41 = n41($fb96b826c0c5f37a$var$a({}, u24), this.props)), n41 && $fb96b826c0c5f37a$var$a(u24, n41), null != n41 && this.__v && (l24 && this.__h.push(l24), $fb96b826c0c5f37a$var$m(this));\n}, $fb96b826c0c5f37a$export$16fa2f45be04daa8.prototype.forceUpdate = function(n42) {\n    this.__v && (this.__e = !0, n42 && this.__h.push(n42), $fb96b826c0c5f37a$var$m(this));\n}, $fb96b826c0c5f37a$export$16fa2f45be04daa8.prototype.render = $fb96b826c0c5f37a$export$ffb0004e005737fa, $fb96b826c0c5f37a$var$t = [], $fb96b826c0c5f37a$var$r = \"function\" == typeof Promise ? Promise.prototype.then.bind(Promise.resolve()) : setTimeout, $fb96b826c0c5f37a$var$g.__r = 0, $fb96b826c0c5f37a$var$f = 0;\n\n\n\nvar $bd9dd35321b03dd4$var$o = 0;\nfunction $bd9dd35321b03dd4$export$34b9dba7ce09269b(_1, e1, n, t, f) {\n    var l, s, u = {};\n    for(s in e1)\"ref\" == s ? l = e1[s] : u[s] = e1[s];\n    var a = {\n        type: _1,\n        props: u,\n        key: n,\n        ref: l,\n        __k: null,\n        __: null,\n        __b: 0,\n        __e: null,\n        __d: void 0,\n        __c: null,\n        __h: null,\n        constructor: void 0,\n        __v: --$bd9dd35321b03dd4$var$o,\n        __source: t,\n        __self: f\n    };\n    if (\"function\" == typeof _1 && (l = _1.defaultProps)) for(s in l)void 0 === u[s] && (u[s] = l[s]);\n    return (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).vnode && (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).vnode(a), a;\n}\n\n\n\nfunction $f72b75cf796873c7$var$set(key, value) {\n    try {\n        window.localStorage[`emoji-mart.${key}`] = JSON.stringify(value);\n    } catch (error) {}\n}\nfunction $f72b75cf796873c7$var$get(key) {\n    try {\n        const value = window.localStorage[`emoji-mart.${key}`];\n        if (value) return JSON.parse(value);\n    } catch (error) {}\n}\nvar $f72b75cf796873c7$export$2e2bcd8739ae039 = {\n    set: $f72b75cf796873c7$var$set,\n    get: $f72b75cf796873c7$var$get\n};\n\n\nconst $c84d045dcc34faf5$var$CACHE = new Map();\nconst $c84d045dcc34faf5$var$VERSIONS = [\n    {\n        v: 15,\n        emoji: \"\\uD83E\\uDEE8\"\n    },\n    {\n        v: 14,\n        emoji: \"\\uD83E\\uDEE0\"\n    },\n    {\n        v: 13.1,\n        emoji: \"\\uD83D\\uDE36\\u200D\\uD83C\\uDF2B\\uFE0F\"\n    },\n    {\n        v: 13,\n        emoji: \"\\uD83E\\uDD78\"\n    },\n    {\n        v: 12.1,\n        emoji: \"\\uD83E\\uDDD1\\u200D\\uD83E\\uDDB0\"\n    },\n    {\n        v: 12,\n        emoji: \"\\uD83E\\uDD71\"\n    },\n    {\n        v: 11,\n        emoji: \"\\uD83E\\uDD70\"\n    },\n    {\n        v: 5,\n        emoji: \"\\uD83E\\uDD29\"\n    },\n    {\n        v: 4,\n        emoji: \"\\uD83D\\uDC71\\u200D\\u2640\\uFE0F\"\n    },\n    {\n        v: 3,\n        emoji: \"\\uD83E\\uDD23\"\n    },\n    {\n        v: 2,\n        emoji: \"\\uD83D\\uDC4B\\uD83C\\uDFFB\"\n    },\n    {\n        v: 1,\n        emoji: \"\\uD83D\\uDE43\"\n    }, \n];\nfunction $c84d045dcc34faf5$var$latestVersion() {\n    for (const { v: v , emoji: emoji  } of $c84d045dcc34faf5$var$VERSIONS){\n        if ($c84d045dcc34faf5$var$isSupported(emoji)) return v;\n    }\n}\nfunction $c84d045dcc34faf5$var$noCountryFlags() {\n    if ($c84d045dcc34faf5$var$isSupported(\"\\uD83C\\uDDE8\\uD83C\\uDDE6\")) return false;\n    return true;\n}\nfunction $c84d045dcc34faf5$var$isSupported(emoji) {\n    if ($c84d045dcc34faf5$var$CACHE.has(emoji)) return $c84d045dcc34faf5$var$CACHE.get(emoji);\n    const supported = $c84d045dcc34faf5$var$isEmojiSupported(emoji);\n    $c84d045dcc34faf5$var$CACHE.set(emoji, supported);\n    return supported;\n}\n// https://github.com/koala-interactive/is-emoji-supported\nconst $c84d045dcc34faf5$var$isEmojiSupported = (()=>{\n    let ctx = null;\n    try {\n        if (!navigator.userAgent.includes(\"jsdom\")) ctx = document.createElement(\"canvas\").getContext(\"2d\", {\n            willReadFrequently: true\n        });\n    } catch  {}\n    // Not in browser env\n    if (!ctx) return ()=>false;\n    const CANVAS_HEIGHT = 25;\n    const CANVAS_WIDTH = 20;\n    const textSize = Math.floor(CANVAS_HEIGHT / 2);\n    // Initialize convas context\n    ctx.font = textSize + \"px Arial, Sans-Serif\";\n    ctx.textBaseline = \"top\";\n    ctx.canvas.width = CANVAS_WIDTH * 2;\n    ctx.canvas.height = CANVAS_HEIGHT;\n    return (unicode)=>{\n        ctx.clearRect(0, 0, CANVAS_WIDTH * 2, CANVAS_HEIGHT);\n        // Draw in red on the left\n        ctx.fillStyle = \"#FF0000\";\n        ctx.fillText(unicode, 0, 22);\n        // Draw in blue on right\n        ctx.fillStyle = \"#0000FF\";\n        ctx.fillText(unicode, CANVAS_WIDTH, 22);\n        const a = ctx.getImageData(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT).data;\n        const count = a.length;\n        let i = 0;\n        // Search the first visible pixel\n        for(; i < count && !a[i + 3]; i += 4);\n        // No visible pixel\n        if (i >= count) return false;\n        // Emoji has immutable color, so we check the color of the emoji in two different colors\n        // the result show be the same.\n        const x = CANVAS_WIDTH + i / 4 % CANVAS_WIDTH;\n        const y = Math.floor(i / 4 / CANVAS_WIDTH);\n        const b = ctx.getImageData(x, y, 1, 1).data;\n        if (a[i] !== b[0] || a[i + 2] !== b[2]) return false;\n        // Some emojis are a contraction of different ones, so if it's not\n        // supported, it will show multiple characters\n        if (ctx.measureText(unicode).width >= CANVAS_WIDTH) return false;\n        // Supported\n        return true;\n    };\n})();\nvar $c84d045dcc34faf5$export$2e2bcd8739ae039 = {\n    latestVersion: $c84d045dcc34faf5$var$latestVersion,\n    noCountryFlags: $c84d045dcc34faf5$var$noCountryFlags\n};\n\n\n\nconst $b22cfd0a55410b4f$var$DEFAULTS = [\n    \"+1\",\n    \"grinning\",\n    \"kissing_heart\",\n    \"heart_eyes\",\n    \"laughing\",\n    \"stuck_out_tongue_winking_eye\",\n    \"sweat_smile\",\n    \"joy\",\n    \"scream\",\n    \"disappointed\",\n    \"unamused\",\n    \"weary\",\n    \"sob\",\n    \"sunglasses\",\n    \"heart\", \n];\nlet $b22cfd0a55410b4f$var$Index = null;\nfunction $b22cfd0a55410b4f$var$add(emoji) {\n    $b22cfd0a55410b4f$var$Index || ($b22cfd0a55410b4f$var$Index = (0, $f72b75cf796873c7$export$2e2bcd8739ae039).get(\"frequently\") || {});\n    const emojiId = emoji.id || emoji;\n    if (!emojiId) return;\n    $b22cfd0a55410b4f$var$Index[emojiId] || ($b22cfd0a55410b4f$var$Index[emojiId] = 0);\n    $b22cfd0a55410b4f$var$Index[emojiId] += 1;\n    (0, $f72b75cf796873c7$export$2e2bcd8739ae039).set(\"last\", emojiId);\n    (0, $f72b75cf796873c7$export$2e2bcd8739ae039).set(\"frequently\", $b22cfd0a55410b4f$var$Index);\n}\nfunction $b22cfd0a55410b4f$var$get({ maxFrequentRows: maxFrequentRows , perLine: perLine  }) {\n    if (!maxFrequentRows) return [];\n    $b22cfd0a55410b4f$var$Index || ($b22cfd0a55410b4f$var$Index = (0, $f72b75cf796873c7$export$2e2bcd8739ae039).get(\"frequently\"));\n    let emojiIds = [];\n    if (!$b22cfd0a55410b4f$var$Index) {\n        $b22cfd0a55410b4f$var$Index = {};\n        for(let i in $b22cfd0a55410b4f$var$DEFAULTS.slice(0, perLine)){\n            const emojiId = $b22cfd0a55410b4f$var$DEFAULTS[i];\n            $b22cfd0a55410b4f$var$Index[emojiId] = perLine - i;\n            emojiIds.push(emojiId);\n        }\n        return emojiIds;\n    }\n    const max = maxFrequentRows * perLine;\n    const last = (0, $f72b75cf796873c7$export$2e2bcd8739ae039).get(\"last\");\n    for(let emojiId in $b22cfd0a55410b4f$var$Index)emojiIds.push(emojiId);\n    emojiIds.sort((a, b)=>{\n        const aScore = $b22cfd0a55410b4f$var$Index[b];\n        const bScore = $b22cfd0a55410b4f$var$Index[a];\n        if (aScore == bScore) return a.localeCompare(b);\n        return aScore - bScore;\n    });\n    if (emojiIds.length > max) {\n        const removedIds = emojiIds.slice(max);\n        emojiIds = emojiIds.slice(0, max);\n        for (let removedId of removedIds){\n            if (removedId == last) continue;\n            delete $b22cfd0a55410b4f$var$Index[removedId];\n        }\n        if (last && emojiIds.indexOf(last) == -1) {\n            delete $b22cfd0a55410b4f$var$Index[emojiIds[emojiIds.length - 1]];\n            emojiIds.splice(-1, 1, last);\n        }\n        (0, $f72b75cf796873c7$export$2e2bcd8739ae039).set(\"frequently\", $b22cfd0a55410b4f$var$Index);\n    }\n    return emojiIds;\n}\nvar $b22cfd0a55410b4f$export$2e2bcd8739ae039 = {\n    add: $b22cfd0a55410b4f$var$add,\n    get: $b22cfd0a55410b4f$var$get,\n    DEFAULTS: $b22cfd0a55410b4f$var$DEFAULTS\n};\n\n\nvar $8d50d93417ef682a$exports = {};\n$8d50d93417ef682a$exports = JSON.parse('{\"search\":\"Search\",\"search_no_results_1\":\"Oh no!\",\"search_no_results_2\":\"That emoji couldn\\u2019t be found\",\"pick\":\"Pick an emoji\\u2026\",\"add_custom\":\"Add custom emoji\",\"categories\":{\"activity\":\"Activity\",\"custom\":\"Custom\",\"flags\":\"Flags\",\"foods\":\"Food & Drink\",\"frequent\":\"Frequently used\",\"nature\":\"Animals & Nature\",\"objects\":\"Objects\",\"people\":\"Smileys & People\",\"places\":\"Travel & Places\",\"search\":\"Search Results\",\"symbols\":\"Symbols\"},\"skins\":{\"1\":\"Default\",\"2\":\"Light\",\"3\":\"Medium-Light\",\"4\":\"Medium\",\"5\":\"Medium-Dark\",\"6\":\"Dark\",\"choose\":\"Choose default skin tone\"}}');\n\n\nvar $b247ea80b67298d5$export$2e2bcd8739ae039 = {\n    autoFocus: {\n        value: false\n    },\n    dynamicWidth: {\n        value: false\n    },\n    emojiButtonColors: {\n        value: null\n    },\n    emojiButtonRadius: {\n        value: \"100%\"\n    },\n    emojiButtonSize: {\n        value: 36\n    },\n    emojiSize: {\n        value: 24\n    },\n    emojiVersion: {\n        value: 15,\n        choices: [\n            1,\n            2,\n            3,\n            4,\n            5,\n            11,\n            12,\n            12.1,\n            13,\n            13.1,\n            14,\n            15\n        ]\n    },\n    exceptEmojis: {\n        value: []\n    },\n    icons: {\n        value: \"auto\",\n        choices: [\n            \"auto\",\n            \"outline\",\n            \"solid\"\n        ]\n    },\n    locale: {\n        value: \"en\",\n        choices: [\n            \"en\",\n            \"ar\",\n            \"be\",\n            \"cs\",\n            \"de\",\n            \"es\",\n            \"fa\",\n            \"fi\",\n            \"fr\",\n            \"hi\",\n            \"it\",\n            \"ja\",\n            \"ko\",\n            \"nl\",\n            \"pl\",\n            \"pt\",\n            \"ru\",\n            \"sa\",\n            \"tr\",\n            \"uk\",\n            \"vi\",\n            \"zh\", \n        ]\n    },\n    maxFrequentRows: {\n        value: 4\n    },\n    navPosition: {\n        value: \"top\",\n        choices: [\n            \"top\",\n            \"bottom\",\n            \"none\"\n        ]\n    },\n    noCountryFlags: {\n        value: false\n    },\n    noResultsEmoji: {\n        value: null\n    },\n    perLine: {\n        value: 9\n    },\n    previewEmoji: {\n        value: null\n    },\n    previewPosition: {\n        value: \"bottom\",\n        choices: [\n            \"top\",\n            \"bottom\",\n            \"none\"\n        ]\n    },\n    searchPosition: {\n        value: \"sticky\",\n        choices: [\n            \"sticky\",\n            \"static\",\n            \"none\"\n        ]\n    },\n    set: {\n        value: \"native\",\n        choices: [\n            \"native\",\n            \"apple\",\n            \"facebook\",\n            \"google\",\n            \"twitter\"\n        ]\n    },\n    skin: {\n        value: 1,\n        choices: [\n            1,\n            2,\n            3,\n            4,\n            5,\n            6\n        ]\n    },\n    skinTonePosition: {\n        value: \"preview\",\n        choices: [\n            \"preview\",\n            \"search\",\n            \"none\"\n        ]\n    },\n    theme: {\n        value: \"auto\",\n        choices: [\n            \"auto\",\n            \"light\",\n            \"dark\"\n        ]\n    },\n    // Data\n    categories: null,\n    categoryIcons: null,\n    custom: null,\n    data: null,\n    i18n: null,\n    // Callbacks\n    getImageURL: null,\n    getSpritesheetURL: null,\n    onAddCustomEmoji: null,\n    onClickOutside: null,\n    onEmojiSelect: null,\n    // Deprecated\n    stickySearch: {\n        deprecated: true,\n        value: true\n    }\n};\n\n\n\nlet $7adb23b0109cc36a$export$dbe3113d60765c1a = null;\nlet $7adb23b0109cc36a$export$2d0294657ab35f1b = null;\nconst $7adb23b0109cc36a$var$fetchCache = {};\nasync function $7adb23b0109cc36a$var$fetchJSON(src) {\n    if ($7adb23b0109cc36a$var$fetchCache[src]) return $7adb23b0109cc36a$var$fetchCache[src];\n    const response = await fetch(src);\n    const json = await response.json();\n    $7adb23b0109cc36a$var$fetchCache[src] = json;\n    return json;\n}\nlet $7adb23b0109cc36a$var$promise = null;\nlet $7adb23b0109cc36a$var$initiated = false;\nlet $7adb23b0109cc36a$var$initCallback = null;\nlet $7adb23b0109cc36a$var$initialized = false;\nfunction $7adb23b0109cc36a$export$2cd8252107eb640b(options, { caller: caller  } = {}) {\n    $7adb23b0109cc36a$var$promise || ($7adb23b0109cc36a$var$promise = new Promise((resolve)=>{\n        $7adb23b0109cc36a$var$initCallback = resolve;\n    }));\n    if (options) $7adb23b0109cc36a$var$_init(options);\n    else if (caller && !$7adb23b0109cc36a$var$initialized) console.warn(`\\`${caller}\\` requires data to be initialized first. Promise will be pending until \\`init\\` is called.`);\n    return $7adb23b0109cc36a$var$promise;\n}\nasync function $7adb23b0109cc36a$var$_init(props) {\n    $7adb23b0109cc36a$var$initialized = true;\n    let { emojiVersion: emojiVersion , set: set , locale: locale  } = props;\n    emojiVersion || (emojiVersion = (0, $b247ea80b67298d5$export$2e2bcd8739ae039).emojiVersion.value);\n    set || (set = (0, $b247ea80b67298d5$export$2e2bcd8739ae039).set.value);\n    locale || (locale = (0, $b247ea80b67298d5$export$2e2bcd8739ae039).locale.value);\n    if (!$7adb23b0109cc36a$export$2d0294657ab35f1b) {\n        $7adb23b0109cc36a$export$2d0294657ab35f1b = (typeof props.data === \"function\" ? await props.data() : props.data) || await $7adb23b0109cc36a$var$fetchJSON(`https://cdn.jsdelivr.net/npm/@emoji-mart/data@latest/sets/${emojiVersion}/${set}.json`);\n        $7adb23b0109cc36a$export$2d0294657ab35f1b.emoticons = {};\n        $7adb23b0109cc36a$export$2d0294657ab35f1b.natives = {};\n        $7adb23b0109cc36a$export$2d0294657ab35f1b.categories.unshift({\n            id: \"frequent\",\n            emojis: []\n        });\n        for(const alias in $7adb23b0109cc36a$export$2d0294657ab35f1b.aliases){\n            const emojiId = $7adb23b0109cc36a$export$2d0294657ab35f1b.aliases[alias];\n            const emoji = $7adb23b0109cc36a$export$2d0294657ab35f1b.emojis[emojiId];\n            if (!emoji) continue;\n            emoji.aliases || (emoji.aliases = []);\n            emoji.aliases.push(alias);\n        }\n        $7adb23b0109cc36a$export$2d0294657ab35f1b.originalCategories = $7adb23b0109cc36a$export$2d0294657ab35f1b.categories;\n    } else $7adb23b0109cc36a$export$2d0294657ab35f1b.categories = $7adb23b0109cc36a$export$2d0294657ab35f1b.categories.filter((c)=>{\n        const isCustom = !!c.name;\n        if (!isCustom) return true;\n        return false;\n    });\n    $7adb23b0109cc36a$export$dbe3113d60765c1a = (typeof props.i18n === \"function\" ? await props.i18n() : props.i18n) || (locale == \"en\" ? (0, (/*@__PURE__*/$parcel$interopDefault($8d50d93417ef682a$exports))) : await $7adb23b0109cc36a$var$fetchJSON(`https://cdn.jsdelivr.net/npm/@emoji-mart/data@latest/i18n/${locale}.json`));\n    if (props.custom) for(let i in props.custom){\n        i = parseInt(i);\n        const category = props.custom[i];\n        const prevCategory = props.custom[i - 1];\n        if (!category.emojis || !category.emojis.length) continue;\n        category.id || (category.id = `custom_${i + 1}`);\n        category.name || (category.name = $7adb23b0109cc36a$export$dbe3113d60765c1a.categories.custom);\n        if (prevCategory && !category.icon) category.target = prevCategory.target || prevCategory;\n        $7adb23b0109cc36a$export$2d0294657ab35f1b.categories.push(category);\n        for (const emoji of category.emojis)$7adb23b0109cc36a$export$2d0294657ab35f1b.emojis[emoji.id] = emoji;\n    }\n    if (props.categories) $7adb23b0109cc36a$export$2d0294657ab35f1b.categories = $7adb23b0109cc36a$export$2d0294657ab35f1b.originalCategories.filter((c)=>{\n        return props.categories.indexOf(c.id) != -1;\n    }).sort((c1, c2)=>{\n        const i1 = props.categories.indexOf(c1.id);\n        const i2 = props.categories.indexOf(c2.id);\n        return i1 - i2;\n    });\n    let latestVersionSupport = null;\n    let noCountryFlags = null;\n    if (set == \"native\") {\n        latestVersionSupport = (0, $c84d045dcc34faf5$export$2e2bcd8739ae039).latestVersion();\n        noCountryFlags = props.noCountryFlags || (0, $c84d045dcc34faf5$export$2e2bcd8739ae039).noCountryFlags();\n    }\n    let categoryIndex = $7adb23b0109cc36a$export$2d0294657ab35f1b.categories.length;\n    let resetSearchIndex = false;\n    while(categoryIndex--){\n        const category = $7adb23b0109cc36a$export$2d0294657ab35f1b.categories[categoryIndex];\n        if (category.id == \"frequent\") {\n            let { maxFrequentRows: maxFrequentRows , perLine: perLine  } = props;\n            maxFrequentRows = maxFrequentRows >= 0 ? maxFrequentRows : (0, $b247ea80b67298d5$export$2e2bcd8739ae039).maxFrequentRows.value;\n            perLine || (perLine = (0, $b247ea80b67298d5$export$2e2bcd8739ae039).perLine.value);\n            category.emojis = (0, $b22cfd0a55410b4f$export$2e2bcd8739ae039).get({\n                maxFrequentRows: maxFrequentRows,\n                perLine: perLine\n            });\n        }\n        if (!category.emojis || !category.emojis.length) {\n            $7adb23b0109cc36a$export$2d0294657ab35f1b.categories.splice(categoryIndex, 1);\n            continue;\n        }\n        const { categoryIcons: categoryIcons  } = props;\n        if (categoryIcons) {\n            const icon = categoryIcons[category.id];\n            if (icon && !category.icon) category.icon = icon;\n        }\n        let emojiIndex = category.emojis.length;\n        while(emojiIndex--){\n            const emojiId = category.emojis[emojiIndex];\n            const emoji = emojiId.id ? emojiId : $7adb23b0109cc36a$export$2d0294657ab35f1b.emojis[emojiId];\n            const ignore = ()=>{\n                category.emojis.splice(emojiIndex, 1);\n            };\n            if (!emoji || props.exceptEmojis && props.exceptEmojis.includes(emoji.id)) {\n                ignore();\n                continue;\n            }\n            if (latestVersionSupport && emoji.version > latestVersionSupport) {\n                ignore();\n                continue;\n            }\n            if (noCountryFlags && category.id == \"flags\") {\n                if (!(0, $e6eae5155b87f591$export$bcb25aa587e9cb13).includes(emoji.id)) {\n                    ignore();\n                    continue;\n                }\n            }\n            if (!emoji.search) {\n                resetSearchIndex = true;\n                emoji.search = \",\" + [\n                    [\n                        emoji.id,\n                        false\n                    ],\n                    [\n                        emoji.name,\n                        true\n                    ],\n                    [\n                        emoji.keywords,\n                        false\n                    ],\n                    [\n                        emoji.emoticons,\n                        false\n                    ], \n                ].map(([strings, split])=>{\n                    if (!strings) return;\n                    return (Array.isArray(strings) ? strings : [\n                        strings\n                    ]).map((string)=>{\n                        return (split ? string.split(/[-|_|\\s]+/) : [\n                            string\n                        ]).map((s)=>s.toLowerCase());\n                    }).flat();\n                }).flat().filter((a)=>a && a.trim()).join(\",\");\n                if (emoji.emoticons) for (const emoticon of emoji.emoticons){\n                    if ($7adb23b0109cc36a$export$2d0294657ab35f1b.emoticons[emoticon]) continue;\n                    $7adb23b0109cc36a$export$2d0294657ab35f1b.emoticons[emoticon] = emoji.id;\n                }\n                let skinIndex = 0;\n                for (const skin of emoji.skins){\n                    if (!skin) continue;\n                    skinIndex++;\n                    const { native: native  } = skin;\n                    if (native) {\n                        $7adb23b0109cc36a$export$2d0294657ab35f1b.natives[native] = emoji.id;\n                        emoji.search += `,${native}`;\n                    }\n                    const skinShortcodes = skinIndex == 1 ? \"\" : `:skin-tone-${skinIndex}:`;\n                    skin.shortcodes = `:${emoji.id}:${skinShortcodes}`;\n                }\n            }\n        }\n    }\n    if (resetSearchIndex) (0, $c4d155af13ad4d4b$export$2e2bcd8739ae039).reset();\n    $7adb23b0109cc36a$var$initCallback();\n}\nfunction $7adb23b0109cc36a$export$75fe5f91d452f94b(props, defaultProps, element) {\n    props || (props = {});\n    const _props = {};\n    for(let k in defaultProps)_props[k] = $7adb23b0109cc36a$export$88c9ddb45cea7241(k, props, defaultProps, element);\n    return _props;\n}\nfunction $7adb23b0109cc36a$export$88c9ddb45cea7241(propName, props, defaultProps, element) {\n    const defaults = defaultProps[propName];\n    let value = element && element.getAttribute(propName) || (props[propName] != null && props[propName] != undefined ? props[propName] : null);\n    if (!defaults) return value;\n    if (value != null && defaults.value && typeof defaults.value != typeof value) {\n        if (typeof defaults.value == \"boolean\") value = value == \"false\" ? false : true;\n        else value = defaults.value.constructor(value);\n    }\n    if (defaults.transform && value) value = defaults.transform(value);\n    if (value == null || defaults.choices && defaults.choices.indexOf(value) == -1) value = defaults.value;\n    return value;\n}\n\n\nconst $c4d155af13ad4d4b$var$SHORTCODES_REGEX = /^(?:\\:([^\\:]+)\\:)(?:\\:skin-tone-(\\d)\\:)?$/;\nlet $c4d155af13ad4d4b$var$Pool = null;\nfunction $c4d155af13ad4d4b$var$get(emojiId) {\n    if (emojiId.id) return emojiId;\n    return (0, $7adb23b0109cc36a$export$2d0294657ab35f1b).emojis[emojiId] || (0, $7adb23b0109cc36a$export$2d0294657ab35f1b).emojis[(0, $7adb23b0109cc36a$export$2d0294657ab35f1b).aliases[emojiId]] || (0, $7adb23b0109cc36a$export$2d0294657ab35f1b).emojis[(0, $7adb23b0109cc36a$export$2d0294657ab35f1b).natives[emojiId]];\n}\nfunction $c4d155af13ad4d4b$var$reset() {\n    $c4d155af13ad4d4b$var$Pool = null;\n}\nasync function $c4d155af13ad4d4b$var$search(value, { maxResults: maxResults , caller: caller  } = {}) {\n    if (!value || !value.trim().length) return null;\n    maxResults || (maxResults = 90);\n    await (0, $7adb23b0109cc36a$export$2cd8252107eb640b)(null, {\n        caller: caller || \"SearchIndex.search\"\n    });\n    const values = value.toLowerCase().replace(/(\\w)-/, \"$1 \").split(/[\\s|,]+/).filter((word, i, words)=>{\n        return word.trim() && words.indexOf(word) == i;\n    });\n    if (!values.length) return;\n    let pool = $c4d155af13ad4d4b$var$Pool || ($c4d155af13ad4d4b$var$Pool = Object.values((0, $7adb23b0109cc36a$export$2d0294657ab35f1b).emojis));\n    let results, scores;\n    for (const value1 of values){\n        if (!pool.length) break;\n        results = [];\n        scores = {};\n        for (const emoji of pool){\n            if (!emoji.search) continue;\n            const score = emoji.search.indexOf(`,${value1}`);\n            if (score == -1) continue;\n            results.push(emoji);\n            scores[emoji.id] || (scores[emoji.id] = 0);\n            scores[emoji.id] += emoji.id == value1 ? 0 : score + 1;\n        }\n        pool = results;\n    }\n    if (results.length < 2) return results;\n    results.sort((a, b)=>{\n        const aScore = scores[a.id];\n        const bScore = scores[b.id];\n        if (aScore == bScore) return a.id.localeCompare(b.id);\n        return aScore - bScore;\n    });\n    if (results.length > maxResults) results = results.slice(0, maxResults);\n    return results;\n}\nvar $c4d155af13ad4d4b$export$2e2bcd8739ae039 = {\n    search: $c4d155af13ad4d4b$var$search,\n    get: $c4d155af13ad4d4b$var$get,\n    reset: $c4d155af13ad4d4b$var$reset,\n    SHORTCODES_REGEX: $c4d155af13ad4d4b$var$SHORTCODES_REGEX\n};\n\n\nconst $e6eae5155b87f591$export$bcb25aa587e9cb13 = [\n    \"checkered_flag\",\n    \"crossed_flags\",\n    \"pirate_flag\",\n    \"rainbow-flag\",\n    \"transgender_flag\",\n    \"triangular_flag_on_post\",\n    \"waving_black_flag\",\n    \"waving_white_flag\", \n];\n\n\nfunction $693b183b0a78708f$export$9cb4719e2e525b7a(a, b) {\n    return Array.isArray(a) && Array.isArray(b) && a.length === b.length && a.every((val, index)=>val == b[index]);\n}\nasync function $693b183b0a78708f$export$e772c8ff12451969(frames = 1) {\n    for(let _ in [\n        ...Array(frames).keys()\n    ])await new Promise(requestAnimationFrame);\n}\nfunction $693b183b0a78708f$export$d10ac59fbe52a745(emoji, { skinIndex: skinIndex = 0  } = {}) {\n    const skin = emoji.skins[skinIndex] || (()=>{\n        skinIndex = 0;\n        return emoji.skins[skinIndex];\n    })();\n    const emojiData = {\n        id: emoji.id,\n        name: emoji.name,\n        native: skin.native,\n        unified: skin.unified,\n        keywords: emoji.keywords,\n        shortcodes: skin.shortcodes || emoji.shortcodes\n    };\n    if (emoji.skins.length > 1) emojiData.skin = skinIndex + 1;\n    if (skin.src) emojiData.src = skin.src;\n    if (emoji.aliases && emoji.aliases.length) emojiData.aliases = emoji.aliases;\n    if (emoji.emoticons && emoji.emoticons.length) emojiData.emoticons = emoji.emoticons;\n    return emojiData;\n}\nasync function $693b183b0a78708f$export$5ef5574deca44bc0(nativeString) {\n    const results = await (0, $c4d155af13ad4d4b$export$2e2bcd8739ae039).search(nativeString, {\n        maxResults: 1,\n        caller: \"getEmojiDataFromNative\"\n    });\n    if (!results || !results.length) return null;\n    const emoji = results[0];\n    let skinIndex = 0;\n    for (let skin of emoji.skins){\n        if (skin.native == nativeString) break;\n        skinIndex++;\n    }\n    return $693b183b0a78708f$export$d10ac59fbe52a745(emoji, {\n        skinIndex: skinIndex\n    });\n}\n\n\n\n\n\nconst $fcccfb36ed0cde68$var$categories = {\n    activity: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M12 0C5.373 0 0 5.372 0 12c0 6.627 5.373 12 12 12 6.628 0 12-5.373 12-12 0-6.628-5.372-12-12-12m9.949 11H17.05c.224-2.527 1.232-4.773 1.968-6.113A9.966 9.966 0 0 1 21.949 11M13 11V2.051a9.945 9.945 0 0 1 4.432 1.564c-.858 1.491-2.156 4.22-2.392 7.385H13zm-2 0H8.961c-.238-3.165-1.536-5.894-2.393-7.385A9.95 9.95 0 0 1 11 2.051V11zm0 2v8.949a9.937 9.937 0 0 1-4.432-1.564c.857-1.492 2.155-4.221 2.393-7.385H11zm4.04 0c.236 3.164 1.534 5.893 2.392 7.385A9.92 9.92 0 0 1 13 21.949V13h2.04zM4.982 4.887C5.718 6.227 6.726 8.473 6.951 11h-4.9a9.977 9.977 0 0 1 2.931-6.113M2.051 13h4.9c-.226 2.527-1.233 4.771-1.969 6.113A9.972 9.972 0 0 1 2.051 13m16.967 6.113c-.735-1.342-1.744-3.586-1.968-6.113h4.899a9.961 9.961 0 0 1-2.931 6.113\"\n            })\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 512 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M16.17 337.5c0 44.98 7.565 83.54 13.98 107.9C35.22 464.3 50.46 496 174.9 496c9.566 0 19.59-.4707 29.84-1.271L17.33 307.3C16.53 317.6 16.17 327.7 16.17 337.5zM495.8 174.5c0-44.98-7.565-83.53-13.98-107.9c-4.688-17.54-18.34-31.23-36.04-35.95C435.5 27.91 392.9 16 337 16c-9.564 0-19.59 .4707-29.84 1.271l187.5 187.5C495.5 194.4 495.8 184.3 495.8 174.5zM26.77 248.8l236.3 236.3c142-36.1 203.9-150.4 222.2-221.1L248.9 26.87C106.9 62.96 45.07 177.2 26.77 248.8zM256 335.1c0 9.141-7.474 16-16 16c-4.094 0-8.188-1.564-11.31-4.689L164.7 283.3C161.6 280.2 160 276.1 160 271.1c0-8.529 6.865-16 16-16c4.095 0 8.189 1.562 11.31 4.688l64.01 64C254.4 327.8 256 331.9 256 335.1zM304 287.1c0 9.141-7.474 16-16 16c-4.094 0-8.188-1.564-11.31-4.689L212.7 235.3C209.6 232.2 208 228.1 208 223.1c0-9.141 7.473-16 16-16c4.094 0 8.188 1.562 11.31 4.688l64.01 64.01C302.5 279.8 304 283.9 304 287.1zM256 175.1c0-9.141 7.473-16 16-16c4.094 0 8.188 1.562 11.31 4.688l64.01 64.01c3.125 3.125 4.688 7.219 4.688 11.31c0 9.133-7.468 16-16 16c-4.094 0-8.189-1.562-11.31-4.688l-64.01-64.01C257.6 184.2 256 180.1 256 175.1z\"\n            })\n        })\n    },\n    custom: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 448 512\",\n        children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n            d: \"M417.1 368c-5.937 10.27-16.69 16-27.75 16c-5.422 0-10.92-1.375-15.97-4.281L256 311.4V448c0 17.67-14.33 32-31.1 32S192 465.7 192 448V311.4l-118.3 68.29C68.67 382.6 63.17 384 57.75 384c-11.06 0-21.81-5.734-27.75-16c-8.828-15.31-3.594-34.88 11.72-43.72L159.1 256L41.72 187.7C26.41 178.9 21.17 159.3 29.1 144C36.63 132.5 49.26 126.7 61.65 128.2C65.78 128.7 69.88 130.1 73.72 132.3L192 200.6V64c0-17.67 14.33-32 32-32S256 46.33 256 64v136.6l118.3-68.29c3.838-2.213 7.939-3.539 12.07-4.051C398.7 126.7 411.4 132.5 417.1 144c8.828 15.31 3.594 34.88-11.72 43.72L288 256l118.3 68.28C421.6 333.1 426.8 352.7 417.1 368z\"\n        })\n    }),\n    flags: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M0 0l6.084 24H8L1.916 0zM21 5h-4l-1-4H4l3 12h3l1 4h13L21 5zM6.563 3h7.875l2 8H8.563l-2-8zm8.832 10l-2.856 1.904L12.063 13h3.332zM19 13l-1.5-6h1.938l2 8H16l3-2z\"\n            })\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 512 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M64 496C64 504.8 56.75 512 48 512h-32C7.25 512 0 504.8 0 496V32c0-17.75 14.25-32 32-32s32 14.25 32 32V496zM476.3 0c-6.365 0-13.01 1.35-19.34 4.233c-45.69 20.86-79.56 27.94-107.8 27.94c-59.96 0-94.81-31.86-163.9-31.87C160.9 .3055 131.6 4.867 96 15.75v350.5c32-9.984 59.87-14.1 84.85-14.1c73.63 0 124.9 31.78 198.6 31.78c31.91 0 68.02-5.971 111.1-23.09C504.1 355.9 512 344.4 512 332.1V30.73C512 11.1 495.3 0 476.3 0z\"\n            })\n        })\n    },\n    foods: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M17 4.978c-1.838 0-2.876.396-3.68.934.513-1.172 1.768-2.934 4.68-2.934a1 1 0 0 0 0-2c-2.921 0-4.629 1.365-5.547 2.512-.064.078-.119.162-.18.244C11.73 1.838 10.798.023 9.207.023 8.579.022 7.85.306 7 .978 5.027 2.54 5.329 3.902 6.492 4.999 3.609 5.222 0 7.352 0 12.969c0 4.582 4.961 11.009 9 11.009 1.975 0 2.371-.486 3-1 .629.514 1.025 1 3 1 4.039 0 9-6.418 9-11 0-5.953-4.055-8-7-8M8.242 2.546c.641-.508.943-.523.965-.523.426.169.975 1.405 1.357 3.055-1.527-.629-2.741-1.352-2.98-1.846.059-.112.241-.356.658-.686M15 21.978c-1.08 0-1.21-.109-1.559-.402l-.176-.146c-.367-.302-.816-.452-1.266-.452s-.898.15-1.266.452l-.176.146c-.347.292-.477.402-1.557.402-2.813 0-7-5.389-7-9.009 0-5.823 4.488-5.991 5-5.991 1.939 0 2.484.471 3.387 1.251l.323.276a1.995 1.995 0 0 0 2.58 0l.323-.276c.902-.78 1.447-1.251 3.387-1.251.512 0 5 .168 5 6 0 3.617-4.187 9-7 9\"\n            })\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 512 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M481.9 270.1C490.9 279.1 496 291.3 496 304C496 316.7 490.9 328.9 481.9 337.9C472.9 346.9 460.7 352 448 352H64C51.27 352 39.06 346.9 30.06 337.9C21.06 328.9 16 316.7 16 304C16 291.3 21.06 279.1 30.06 270.1C39.06 261.1 51.27 256 64 256H448C460.7 256 472.9 261.1 481.9 270.1zM475.3 388.7C478.3 391.7 480 395.8 480 400V416C480 432.1 473.3 449.3 461.3 461.3C449.3 473.3 432.1 480 416 480H96C79.03 480 62.75 473.3 50.75 461.3C38.74 449.3 32 432.1 32 416V400C32 395.8 33.69 391.7 36.69 388.7C39.69 385.7 43.76 384 48 384H464C468.2 384 472.3 385.7 475.3 388.7zM50.39 220.8C45.93 218.6 42.03 215.5 38.97 211.6C35.91 207.7 33.79 203.2 32.75 198.4C31.71 193.5 31.8 188.5 32.99 183.7C54.98 97.02 146.5 32 256 32C365.5 32 457 97.02 479 183.7C480.2 188.5 480.3 193.5 479.2 198.4C478.2 203.2 476.1 207.7 473 211.6C469.1 215.5 466.1 218.6 461.6 220.8C457.2 222.9 452.3 224 447.3 224H64.67C59.73 224 54.84 222.9 50.39 220.8zM372.7 116.7C369.7 119.7 368 123.8 368 128C368 131.2 368.9 134.3 370.7 136.9C372.5 139.5 374.1 141.6 377.9 142.8C380.8 143.1 384 144.3 387.1 143.7C390.2 143.1 393.1 141.6 395.3 139.3C397.6 137.1 399.1 134.2 399.7 131.1C400.3 128 399.1 124.8 398.8 121.9C397.6 118.1 395.5 116.5 392.9 114.7C390.3 112.9 387.2 111.1 384 111.1C379.8 111.1 375.7 113.7 372.7 116.7V116.7zM244.7 84.69C241.7 87.69 240 91.76 240 96C240 99.16 240.9 102.3 242.7 104.9C244.5 107.5 246.1 109.6 249.9 110.8C252.8 111.1 256 112.3 259.1 111.7C262.2 111.1 265.1 109.6 267.3 107.3C269.6 105.1 271.1 102.2 271.7 99.12C272.3 96.02 271.1 92.8 270.8 89.88C269.6 86.95 267.5 84.45 264.9 82.7C262.3 80.94 259.2 79.1 256 79.1C251.8 79.1 247.7 81.69 244.7 84.69V84.69zM116.7 116.7C113.7 119.7 112 123.8 112 128C112 131.2 112.9 134.3 114.7 136.9C116.5 139.5 118.1 141.6 121.9 142.8C124.8 143.1 128 144.3 131.1 143.7C134.2 143.1 137.1 141.6 139.3 139.3C141.6 137.1 143.1 134.2 143.7 131.1C144.3 128 143.1 124.8 142.8 121.9C141.6 118.1 139.5 116.5 136.9 114.7C134.3 112.9 131.2 111.1 128 111.1C123.8 111.1 119.7 113.7 116.7 116.7L116.7 116.7z\"\n            })\n        })\n    },\n    frequent: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M13 4h-2l-.001 7H9v2h2v2h2v-2h4v-2h-4z\"\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10\"\n                })\n            ]\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 512 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512zM232 256C232 264 236 271.5 242.7 275.1L338.7 339.1C349.7 347.3 364.6 344.3 371.1 333.3C379.3 322.3 376.3 307.4 365.3 300L280 243.2V120C280 106.7 269.3 96 255.1 96C242.7 96 231.1 106.7 231.1 120L232 256z\"\n            })\n        })\n    },\n    nature: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M15.5 8a1.5 1.5 0 1 0 .001 3.001A1.5 1.5 0 0 0 15.5 8M8.5 8a1.5 1.5 0 1 0 .001 3.001A1.5 1.5 0 0 0 8.5 8\"\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M18.933 0h-.027c-.97 0-2.138.787-3.018 1.497-1.274-.374-2.612-.51-3.887-.51-1.285 0-2.616.133-3.874.517C7.245.79 6.069 0 5.093 0h-.027C3.352 0 .07 2.67.002 7.026c-.039 2.479.276 4.238 1.04 5.013.254.258.882.677 1.295.882.191 3.177.922 5.238 2.536 6.38.897.637 2.187.949 3.2 1.102C8.04 20.6 8 20.795 8 21c0 1.773 2.35 3 4 3 1.648 0 4-1.227 4-3 0-.201-.038-.393-.072-.586 2.573-.385 5.435-1.877 5.925-7.587.396-.22.887-.568 1.104-.788.763-.774 1.079-2.534 1.04-5.013C23.929 2.67 20.646 0 18.933 0M3.223 9.135c-.237.281-.837 1.155-.884 1.238-.15-.41-.368-1.349-.337-3.291.051-3.281 2.478-4.972 3.091-5.031.256.015.731.27 1.265.646-1.11 1.171-2.275 2.915-2.352 5.125-.133.546-.398.858-.783 1.313M12 22c-.901 0-1.954-.693-2-1 0-.654.475-1.236 1-1.602V20a1 1 0 1 0 2 0v-.602c.524.365 1 .947 1 1.602-.046.307-1.099 1-2 1m3-3.48v.02a4.752 4.752 0 0 0-1.262-1.02c1.092-.516 2.239-1.334 2.239-2.217 0-1.842-1.781-2.195-3.977-2.195-2.196 0-3.978.354-3.978 2.195 0 .883 1.148 1.701 2.238 2.217A4.8 4.8 0 0 0 9 18.539v-.025c-1-.076-2.182-.281-2.973-.842-1.301-.92-1.838-3.045-1.853-6.478l.023-.041c.496-.826 1.49-1.45 1.804-3.102 0-2.047 1.357-3.631 2.362-4.522C9.37 3.178 10.555 3 11.948 3c1.447 0 2.685.192 3.733.57 1 .9 2.316 2.465 2.316 4.48.313 1.651 1.307 2.275 1.803 3.102.035.058.068.117.102.178-.059 5.967-1.949 7.01-4.902 7.19m6.628-8.202c-.037-.065-.074-.13-.113-.195a7.587 7.587 0 0 0-.739-.987c-.385-.455-.648-.768-.782-1.313-.076-2.209-1.241-3.954-2.353-5.124.531-.376 1.004-.63 1.261-.647.636.071 3.044 1.764 3.096 5.031.027 1.81-.347 3.218-.37 3.235\"\n                })\n            ]\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 576 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M332.7 19.85C334.6 8.395 344.5 0 356.1 0C363.6 0 370.6 3.52 375.1 9.502L392 32H444.1C456.8 32 469.1 37.06 478.1 46.06L496 64H552C565.3 64 576 74.75 576 88V112C576 156.2 540.2 192 496 192H426.7L421.6 222.5L309.6 158.5L332.7 19.85zM448 64C439.2 64 432 71.16 432 80C432 88.84 439.2 96 448 96C456.8 96 464 88.84 464 80C464 71.16 456.8 64 448 64zM416 256.1V480C416 497.7 401.7 512 384 512H352C334.3 512 320 497.7 320 480V364.8C295.1 377.1 268.8 384 240 384C211.2 384 184 377.1 160 364.8V480C160 497.7 145.7 512 128 512H96C78.33 512 64 497.7 64 480V249.8C35.23 238.9 12.64 214.5 4.836 183.3L.9558 167.8C-3.331 150.6 7.094 133.2 24.24 128.1C41.38 124.7 58.76 135.1 63.05 152.2L66.93 167.8C70.49 182 83.29 191.1 97.97 191.1H303.8L416 256.1z\"\n            })\n        })\n    },\n    objects: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M12 0a9 9 0 0 0-5 16.482V21s2.035 3 5 3 5-3 5-3v-4.518A9 9 0 0 0 12 0zm0 2c3.86 0 7 3.141 7 7s-3.14 7-7 7-7-3.141-7-7 3.14-7 7-7zM9 17.477c.94.332 1.946.523 3 .523s2.06-.19 3-.523v.834c-.91.436-1.925.689-3 .689a6.924 6.924 0 0 1-3-.69v-.833zm.236 3.07A8.854 8.854 0 0 0 12 21c.965 0 1.888-.167 2.758-.451C14.155 21.173 13.153 22 12 22c-1.102 0-2.117-.789-2.764-1.453z\"\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M14.745 12.449h-.004c-.852-.024-1.188-.858-1.577-1.824-.421-1.061-.703-1.561-1.182-1.566h-.009c-.481 0-.783.497-1.235 1.537-.436.982-.801 1.811-1.636 1.791l-.276-.043c-.565-.171-.853-.691-1.284-1.794-.125-.313-.202-.632-.27-.913-.051-.213-.127-.53-.195-.634C7.067 9.004 7.039 9 6.99 9A1 1 0 0 1 7 7h.01c1.662.017 2.015 1.373 2.198 2.134.486-.981 1.304-2.058 2.797-2.075 1.531.018 2.28 1.153 2.731 2.141l.002-.008C14.944 8.424 15.327 7 16.979 7h.032A1 1 0 1 1 17 9h-.011c-.149.076-.256.474-.319.709a6.484 6.484 0 0 1-.311.951c-.429.973-.79 1.789-1.614 1.789\"\n                })\n            ]\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 384 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M112.1 454.3c0 6.297 1.816 12.44 5.284 17.69l17.14 25.69c5.25 7.875 17.17 14.28 26.64 14.28h61.67c9.438 0 21.36-6.401 26.61-14.28l17.08-25.68c2.938-4.438 5.348-12.37 5.348-17.7L272 415.1h-160L112.1 454.3zM191.4 .0132C89.44 .3257 16 82.97 16 175.1c0 44.38 16.44 84.84 43.56 115.8c16.53 18.84 42.34 58.23 52.22 91.45c.0313 .25 .0938 .5166 .125 .7823h160.2c.0313-.2656 .0938-.5166 .125-.7823c9.875-33.22 35.69-72.61 52.22-91.45C351.6 260.8 368 220.4 368 175.1C368 78.61 288.9-.2837 191.4 .0132zM192 96.01c-44.13 0-80 35.89-80 79.1C112 184.8 104.8 192 96 192S80 184.8 80 176c0-61.76 50.25-111.1 112-111.1c8.844 0 16 7.159 16 16S200.8 96.01 192 96.01z\"\n            })\n        })\n    },\n    people: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10\"\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M8 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 8 7M16 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 16 7M15.232 15c-.693 1.195-1.87 2-3.349 2-1.477 0-2.655-.805-3.347-2H15m3-2H6a6 6 0 1 0 12 0\"\n                })\n            ]\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 512 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256zM256 432C332.1 432 396.2 382 415.2 314.1C419.1 300.4 407.8 288 393.6 288H118.4C104.2 288 92.92 300.4 96.76 314.1C115.8 382 179.9 432 256 432V432zM176.4 160C158.7 160 144.4 174.3 144.4 192C144.4 209.7 158.7 224 176.4 224C194 224 208.4 209.7 208.4 192C208.4 174.3 194 160 176.4 160zM336.4 224C354 224 368.4 209.7 368.4 192C368.4 174.3 354 160 336.4 160C318.7 160 304.4 174.3 304.4 192C304.4 209.7 318.7 224 336.4 224z\"\n            })\n        })\n    },\n    places: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M6.5 12C5.122 12 4 13.121 4 14.5S5.122 17 6.5 17 9 15.879 9 14.5 7.878 12 6.5 12m0 3c-.275 0-.5-.225-.5-.5s.225-.5.5-.5.5.225.5.5-.225.5-.5.5M17.5 12c-1.378 0-2.5 1.121-2.5 2.5s1.122 2.5 2.5 2.5 2.5-1.121 2.5-2.5-1.122-2.5-2.5-2.5m0 3c-.275 0-.5-.225-.5-.5s.225-.5.5-.5.5.225.5.5-.225.5-.5.5\"\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                    d: \"M22.482 9.494l-1.039-.346L21.4 9h.6c.552 0 1-.439 1-.992 0-.006-.003-.008-.003-.008H23c0-1-.889-2-1.984-2h-.642l-.731-1.717C19.262 3.012 18.091 2 16.764 2H7.236C5.909 2 4.738 3.012 4.357 4.283L3.626 6h-.642C1.889 6 1 7 1 8h.003S1 8.002 1 8.008C1 8.561 1.448 9 2 9h.6l-.043.148-1.039.346a2.001 2.001 0 0 0-1.359 2.097l.751 7.508a1 1 0 0 0 .994.901H3v1c0 1.103.896 2 2 2h2c1.104 0 2-.897 2-2v-1h6v1c0 1.103.896 2 2 2h2c1.104 0 2-.897 2-2v-1h1.096a.999.999 0 0 0 .994-.901l.751-7.508a2.001 2.001 0 0 0-1.359-2.097M6.273 4.857C6.402 4.43 6.788 4 7.236 4h9.527c.448 0 .834.43.963.857L19.313 9H4.688l1.585-4.143zM7 21H5v-1h2v1zm12 0h-2v-1h2v1zm2.189-3H2.811l-.662-6.607L3 11h18l.852.393L21.189 18z\"\n                })\n            ]\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 512 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M39.61 196.8L74.8 96.29C88.27 57.78 124.6 32 165.4 32H346.6C387.4 32 423.7 57.78 437.2 96.29L472.4 196.8C495.6 206.4 512 229.3 512 256V448C512 465.7 497.7 480 480 480H448C430.3 480 416 465.7 416 448V400H96V448C96 465.7 81.67 480 64 480H32C14.33 480 0 465.7 0 448V256C0 229.3 16.36 206.4 39.61 196.8V196.8zM109.1 192H402.9L376.8 117.4C372.3 104.6 360.2 96 346.6 96H165.4C151.8 96 139.7 104.6 135.2 117.4L109.1 192zM96 256C78.33 256 64 270.3 64 288C64 305.7 78.33 320 96 320C113.7 320 128 305.7 128 288C128 270.3 113.7 256 96 256zM416 320C433.7 320 448 305.7 448 288C448 270.3 433.7 256 416 256C398.3 256 384 270.3 384 288C384 305.7 398.3 320 416 320z\"\n            })\n        })\n    },\n    symbols: {\n        outline: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M0 0h11v2H0zM4 11h3V6h4V4H0v2h4zM15.5 17c1.381 0 2.5-1.116 2.5-2.493s-1.119-2.493-2.5-2.493S13 13.13 13 14.507 14.119 17 15.5 17m0-2.986c.276 0 .5.222.5.493 0 .272-.224.493-.5.493s-.5-.221-.5-.493.224-.493.5-.493M21.5 19.014c-1.381 0-2.5 1.116-2.5 2.493S20.119 24 21.5 24s2.5-1.116 2.5-2.493-1.119-2.493-2.5-2.493m0 2.986a.497.497 0 0 1-.5-.493c0-.271.224-.493.5-.493s.5.222.5.493a.497.497 0 0 1-.5.493M22 13l-9 9 1.513 1.5 8.99-9.009zM17 11c2.209 0 4-1.119 4-2.5V2s.985-.161 1.498.949C23.01 4.055 23 6 23 6s1-1.119 1-3.135C24-.02 21 0 21 0h-2v6.347A5.853 5.853 0 0 0 17 6c-2.209 0-4 1.119-4 2.5s1.791 2.5 4 2.5M10.297 20.482l-1.475-1.585a47.54 47.54 0 0 1-1.442 1.129c-.307-.288-.989-1.016-2.045-2.183.902-.836 1.479-1.466 1.729-1.892s.376-.871.376-1.336c0-.592-.273-1.178-.818-1.759-.546-.581-1.329-.871-2.349-.871-1.008 0-1.79.293-2.344.879-.556.587-.832 1.181-.832 1.784 0 .813.419 1.748 1.256 2.805-.847.614-1.444 1.208-1.794 1.784a3.465 3.465 0 0 0-.523 1.833c0 .857.308 1.56.924 2.107.616.549 1.423.823 2.42.823 1.173 0 2.444-.379 3.813-1.137L8.235 24h2.819l-2.09-2.383 1.333-1.135zm-6.736-6.389a1.02 1.02 0 0 1 .73-.286c.31 0 .559.085.747.254a.849.849 0 0 1 .283.659c0 .518-.419 1.112-1.257 1.784-.536-.651-.805-1.231-.805-1.742a.901.901 0 0 1 .302-.669M3.74 22c-.427 0-.778-.116-1.057-.349-.279-.232-.418-.487-.418-.766 0-.594.509-1.288 1.527-2.083.968 1.134 1.717 1.946 2.248 2.438-.921.507-1.686.76-2.3.76\"\n            })\n        }),\n        solid: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 512 512\",\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n                d: \"M500.3 7.251C507.7 13.33 512 22.41 512 31.1V175.1C512 202.5 483.3 223.1 447.1 223.1C412.7 223.1 383.1 202.5 383.1 175.1C383.1 149.5 412.7 127.1 447.1 127.1V71.03L351.1 90.23V207.1C351.1 234.5 323.3 255.1 287.1 255.1C252.7 255.1 223.1 234.5 223.1 207.1C223.1 181.5 252.7 159.1 287.1 159.1V63.1C287.1 48.74 298.8 35.61 313.7 32.62L473.7 .6198C483.1-1.261 492.9 1.173 500.3 7.251H500.3zM74.66 303.1L86.5 286.2C92.43 277.3 102.4 271.1 113.1 271.1H174.9C185.6 271.1 195.6 277.3 201.5 286.2L213.3 303.1H239.1C266.5 303.1 287.1 325.5 287.1 351.1V463.1C287.1 490.5 266.5 511.1 239.1 511.1H47.1C21.49 511.1-.0019 490.5-.0019 463.1V351.1C-.0019 325.5 21.49 303.1 47.1 303.1H74.66zM143.1 359.1C117.5 359.1 95.1 381.5 95.1 407.1C95.1 434.5 117.5 455.1 143.1 455.1C170.5 455.1 191.1 434.5 191.1 407.1C191.1 381.5 170.5 359.1 143.1 359.1zM440.3 367.1H496C502.7 367.1 508.6 372.1 510.1 378.4C513.3 384.6 511.6 391.7 506.5 396L378.5 508C372.9 512.1 364.6 513.3 358.6 508.9C352.6 504.6 350.3 496.6 353.3 489.7L391.7 399.1H336C329.3 399.1 323.4 395.9 321 389.6C318.7 383.4 320.4 376.3 325.5 371.1L453.5 259.1C459.1 255 467.4 254.7 473.4 259.1C479.4 263.4 481.6 271.4 478.7 278.3L440.3 367.1zM116.7 219.1L19.85 119.2C-8.112 90.26-6.614 42.31 24.85 15.34C51.82-8.137 93.26-3.642 118.2 21.83L128.2 32.32L137.7 21.83C162.7-3.642 203.6-8.137 231.6 15.34C262.6 42.31 264.1 90.26 236.1 119.2L139.7 219.1C133.2 225.6 122.7 225.6 116.7 219.1H116.7z\"\n            })\n        })\n    }\n};\nconst $fcccfb36ed0cde68$var$search = {\n    loupe: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 20 20\",\n        children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n            d: \"M12.9 14.32a8 8 0 1 1 1.41-1.41l5.35 5.33-1.42 1.42-5.33-5.34zM8 14A6 6 0 1 0 8 2a6 6 0 0 0 0 12z\"\n        })\n    }),\n    delete: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 20 20\",\n        children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"path\", {\n            d: \"M10 8.586L2.929 1.515 1.515 2.929 8.586 10l-7.071 7.071 1.414 1.414L10 11.414l7.071 7.071 1.414-1.414L11.414 10l7.071-7.071-1.414-1.414L10 8.586z\"\n        })\n    })\n};\nvar $fcccfb36ed0cde68$export$2e2bcd8739ae039 = {\n    categories: $fcccfb36ed0cde68$var$categories,\n    search: $fcccfb36ed0cde68$var$search\n};\n\n\n\n\n\nfunction $254755d3f438722f$export$2e2bcd8739ae039(props) {\n    let { id: id , skin: skin , emoji: emoji  } = props;\n    if (props.shortcodes) {\n        const matches = props.shortcodes.match((0, $c4d155af13ad4d4b$export$2e2bcd8739ae039).SHORTCODES_REGEX);\n        if (matches) {\n            id = matches[1];\n            if (matches[2]) skin = matches[2];\n        }\n    }\n    emoji || (emoji = (0, $c4d155af13ad4d4b$export$2e2bcd8739ae039).get(id || props.native));\n    if (!emoji) return props.fallback;\n    const emojiSkin = emoji.skins[skin - 1] || emoji.skins[0];\n    const imageSrc = emojiSkin.src || (props.set != \"native\" && !props.spritesheet ? typeof props.getImageURL === \"function\" ? props.getImageURL(props.set, emojiSkin.unified) : `https://cdn.jsdelivr.net/npm/emoji-datasource-${props.set}@15.0.1/img/${props.set}/64/${emojiSkin.unified}.png` : undefined);\n    const spritesheetSrc = typeof props.getSpritesheetURL === \"function\" ? props.getSpritesheetURL(props.set) : `https://cdn.jsdelivr.net/npm/emoji-datasource-${props.set}@15.0.1/img/${props.set}/sheets-256/64.png`;\n    return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n        class: \"emoji-mart-emoji\",\n        \"data-emoji-set\": props.set,\n        children: imageSrc ? /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"img\", {\n            style: {\n                maxWidth: props.size || \"1em\",\n                maxHeight: props.size || \"1em\",\n                display: \"inline-block\"\n            },\n            alt: emojiSkin.native || emojiSkin.shortcodes,\n            src: imageSrc\n        }) : props.set == \"native\" ? /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n            style: {\n                fontSize: props.size,\n                fontFamily: '\"EmojiMart\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Segoe UI\", \"Apple Color Emoji\", \"Twemoji Mozilla\", \"Noto Color Emoji\", \"Android Emoji\"'\n            },\n            children: emojiSkin.native\n        }) : /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n            style: {\n                display: \"block\",\n                width: props.size,\n                height: props.size,\n                backgroundImage: `url(${spritesheetSrc})`,\n                backgroundSize: `${100 * (0, $7adb23b0109cc36a$export$2d0294657ab35f1b).sheet.cols}% ${100 * (0, $7adb23b0109cc36a$export$2d0294657ab35f1b).sheet.rows}%`,\n                backgroundPosition: `${100 / ((0, $7adb23b0109cc36a$export$2d0294657ab35f1b).sheet.cols - 1) * emojiSkin.x}% ${100 / ((0, $7adb23b0109cc36a$export$2d0294657ab35f1b).sheet.rows - 1) * emojiSkin.y}%`\n            }\n        })\n    });\n}\n\n\n\n\n\n\n\nconst $6f57cc9cd54c5aaa$var$WindowHTMLElement = typeof window !== \"undefined\" && window.HTMLElement ? window.HTMLElement : Object;\nclass $6f57cc9cd54c5aaa$export$2e2bcd8739ae039 extends $6f57cc9cd54c5aaa$var$WindowHTMLElement {\n    static get observedAttributes() {\n        return Object.keys(this.Props);\n    }\n    update(props = {}) {\n        for(let k in props)this.attributeChangedCallback(k, null, props[k]);\n    }\n    attributeChangedCallback(attr, _, newValue) {\n        if (!this.component) return;\n        const value = (0, $7adb23b0109cc36a$export$88c9ddb45cea7241)(attr, {\n            [attr]: newValue\n        }, this.constructor.Props, this);\n        if (this.component.componentWillReceiveProps) this.component.componentWillReceiveProps({\n            [attr]: value\n        });\n        else {\n            this.component.props[attr] = value;\n            this.component.forceUpdate();\n        }\n    }\n    disconnectedCallback() {\n        this.disconnected = true;\n        if (this.component && this.component.unregister) this.component.unregister();\n    }\n    constructor(props = {}){\n        super();\n        this.props = props;\n        if (props.parent || props.ref) {\n            let ref = null;\n            const parent = props.parent || (ref = props.ref && props.ref.current);\n            if (ref) ref.innerHTML = \"\";\n            if (parent) parent.appendChild(this);\n        }\n    }\n}\n\n\n\nclass $26f27c338a96b1a6$export$2e2bcd8739ae039 extends (0, $6f57cc9cd54c5aaa$export$2e2bcd8739ae039) {\n    setShadow() {\n        this.attachShadow({\n            mode: \"open\"\n        });\n    }\n    injectStyles(styles) {\n        if (!styles) return;\n        const style = document.createElement(\"style\");\n        style.textContent = styles;\n        this.shadowRoot.insertBefore(style, this.shadowRoot.firstChild);\n    }\n    constructor(props, { styles: styles  } = {}){\n        super(props);\n        this.setShadow();\n        this.injectStyles(styles);\n    }\n}\n\n\n\n\n\n\nvar $3d90f6e46fb2dd47$export$2e2bcd8739ae039 = {\n    fallback: \"\",\n    id: \"\",\n    native: \"\",\n    shortcodes: \"\",\n    size: {\n        value: \"\",\n        transform: (value)=>{\n            // If the value is a number, then we assume it’s a pixel value.\n            if (!/\\D/.test(value)) return `${value}px`;\n            return value;\n        }\n    },\n    // Shared\n    set: (0, $b247ea80b67298d5$export$2e2bcd8739ae039).set,\n    skin: (0, $b247ea80b67298d5$export$2e2bcd8739ae039).skin\n};\n\n\nclass $331b4160623139bf$export$2e2bcd8739ae039 extends (0, $6f57cc9cd54c5aaa$export$2e2bcd8739ae039) {\n    async connectedCallback() {\n        const props = (0, $7adb23b0109cc36a$export$75fe5f91d452f94b)(this.props, (0, $3d90f6e46fb2dd47$export$2e2bcd8739ae039), this);\n        props.element = this;\n        props.ref = (component)=>{\n            this.component = component;\n        };\n        await (0, $7adb23b0109cc36a$export$2cd8252107eb640b)();\n        if (this.disconnected) return;\n        (0, $fb96b826c0c5f37a$export$b3890eb0ae9dca99)(/*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)((0, $254755d3f438722f$export$2e2bcd8739ae039), {\n            ...props\n        }), this);\n    }\n    constructor(props){\n        super(props);\n    }\n}\n(0, $c770c458706daa72$export$2e2bcd8739ae039)($331b4160623139bf$export$2e2bcd8739ae039, \"Props\", (0, $3d90f6e46fb2dd47$export$2e2bcd8739ae039));\nif (typeof customElements !== \"undefined\" && !customElements.get(\"em-emoji\")) customElements.define(\"em-emoji\", $331b4160623139bf$export$2e2bcd8739ae039);\n\n\n\n\n\n\nvar $1a9a8ef576b7773d$var$t, $1a9a8ef576b7773d$var$u, $1a9a8ef576b7773d$var$r, $1a9a8ef576b7773d$var$o = 0, $1a9a8ef576b7773d$var$i = [], $1a9a8ef576b7773d$var$c = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__b, $1a9a8ef576b7773d$var$f = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__r, $1a9a8ef576b7773d$var$e = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).diffed, $1a9a8ef576b7773d$var$a = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__c, $1a9a8ef576b7773d$var$v = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).unmount;\nfunction $1a9a8ef576b7773d$var$m(t1, r1) {\n    (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__h && (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__h($1a9a8ef576b7773d$var$u, t1, $1a9a8ef576b7773d$var$o || r1), $1a9a8ef576b7773d$var$o = 0;\n    var i1 = $1a9a8ef576b7773d$var$u.__H || ($1a9a8ef576b7773d$var$u.__H = {\n        __: [],\n        __h: []\n    });\n    return t1 >= i1.__.length && i1.__.push({}), i1.__[t1];\n}\nfunction $1a9a8ef576b7773d$export$60241385465d0a34(n1) {\n    return $1a9a8ef576b7773d$var$o = 1, $1a9a8ef576b7773d$export$13e3392192263954($1a9a8ef576b7773d$var$w, n1);\n}\nfunction $1a9a8ef576b7773d$export$13e3392192263954(n2, r2, o1) {\n    var i2 = $1a9a8ef576b7773d$var$m($1a9a8ef576b7773d$var$t++, 2);\n    return i2.t = n2, i2.__c || (i2.__ = [\n        o1 ? o1(r2) : $1a9a8ef576b7773d$var$w(void 0, r2),\n        function(n3) {\n            var t2 = i2.t(i2.__[0], n3);\n            i2.__[0] !== t2 && (i2.__ = [\n                t2,\n                i2.__[1]\n            ], i2.__c.setState({}));\n        }\n    ], i2.__c = $1a9a8ef576b7773d$var$u), i2.__;\n}\nfunction $1a9a8ef576b7773d$export$6d9c69b0de29b591(r3, o2) {\n    var i3 = $1a9a8ef576b7773d$var$m($1a9a8ef576b7773d$var$t++, 3);\n    !(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__s && $1a9a8ef576b7773d$var$k(i3.__H, o2) && (i3.__ = r3, i3.__H = o2, $1a9a8ef576b7773d$var$u.__H.__h.push(i3));\n}\nfunction $1a9a8ef576b7773d$export$e5c5a5f917a5871c(r4, o3) {\n    var i4 = $1a9a8ef576b7773d$var$m($1a9a8ef576b7773d$var$t++, 4);\n    !(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__s && $1a9a8ef576b7773d$var$k(i4.__H, o3) && (i4.__ = r4, i4.__H = o3, $1a9a8ef576b7773d$var$u.__h.push(i4));\n}\nfunction $1a9a8ef576b7773d$export$b8f5890fc79d6aca(n4) {\n    return $1a9a8ef576b7773d$var$o = 5, $1a9a8ef576b7773d$export$1538c33de8887b59(function() {\n        return {\n            current: n4\n        };\n    }, []);\n}\nfunction $1a9a8ef576b7773d$export$d5a552a76deda3c2(n5, t3, u1) {\n    $1a9a8ef576b7773d$var$o = 6, $1a9a8ef576b7773d$export$e5c5a5f917a5871c(function() {\n        \"function\" == typeof n5 ? n5(t3()) : n5 && (n5.current = t3());\n    }, null == u1 ? u1 : u1.concat(n5));\n}\nfunction $1a9a8ef576b7773d$export$1538c33de8887b59(n6, u2) {\n    var r5 = $1a9a8ef576b7773d$var$m($1a9a8ef576b7773d$var$t++, 7);\n    return $1a9a8ef576b7773d$var$k(r5.__H, u2) && (r5.__ = n6(), r5.__H = u2, r5.__h = n6), r5.__;\n}\nfunction $1a9a8ef576b7773d$export$35808ee640e87ca7(n7, t4) {\n    return $1a9a8ef576b7773d$var$o = 8, $1a9a8ef576b7773d$export$1538c33de8887b59(function() {\n        return n7;\n    }, t4);\n}\nfunction $1a9a8ef576b7773d$export$fae74005e78b1a27(n8) {\n    var r6 = $1a9a8ef576b7773d$var$u.context[n8.__c], o4 = $1a9a8ef576b7773d$var$m($1a9a8ef576b7773d$var$t++, 9);\n    return o4.c = n8, r6 ? (null == o4.__ && (o4.__ = !0, r6.sub($1a9a8ef576b7773d$var$u)), r6.props.value) : n8.__;\n}\nfunction $1a9a8ef576b7773d$export$dc8fbce3eb94dc1e(t5, u3) {\n    (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).useDebugValue && (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).useDebugValue(u3 ? u3(t5) : t5);\n}\nfunction $1a9a8ef576b7773d$export$c052f6604b7d51fe(n9) {\n    var r7 = $1a9a8ef576b7773d$var$m($1a9a8ef576b7773d$var$t++, 10), o5 = $1a9a8ef576b7773d$export$60241385465d0a34();\n    return r7.__ = n9, $1a9a8ef576b7773d$var$u.componentDidCatch || ($1a9a8ef576b7773d$var$u.componentDidCatch = function(n10) {\n        r7.__ && r7.__(n10), o5[1](n10);\n    }), [\n        o5[0],\n        function() {\n            o5[1](void 0);\n        }\n    ];\n}\nfunction $1a9a8ef576b7773d$var$x() {\n    var t6;\n    for($1a9a8ef576b7773d$var$i.sort(function(n11, t7) {\n        return n11.__v.__b - t7.__v.__b;\n    }); t6 = $1a9a8ef576b7773d$var$i.pop();)if (t6.__P) try {\n        t6.__H.__h.forEach($1a9a8ef576b7773d$var$g), t6.__H.__h.forEach($1a9a8ef576b7773d$var$j), t6.__H.__h = [];\n    } catch (u4) {\n        t6.__H.__h = [], (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__e(u4, t6.__v);\n    }\n}\n(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__b = function(n12) {\n    $1a9a8ef576b7773d$var$u = null, $1a9a8ef576b7773d$var$c && $1a9a8ef576b7773d$var$c(n12);\n}, (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__r = function(n13) {\n    $1a9a8ef576b7773d$var$f && $1a9a8ef576b7773d$var$f(n13), $1a9a8ef576b7773d$var$t = 0;\n    var r8 = ($1a9a8ef576b7773d$var$u = n13.__c).__H;\n    r8 && (r8.__h.forEach($1a9a8ef576b7773d$var$g), r8.__h.forEach($1a9a8ef576b7773d$var$j), r8.__h = []);\n}, (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).diffed = function(t8) {\n    $1a9a8ef576b7773d$var$e && $1a9a8ef576b7773d$var$e(t8);\n    var o6 = t8.__c;\n    o6 && o6.__H && o6.__H.__h.length && (1 !== $1a9a8ef576b7773d$var$i.push(o6) && $1a9a8ef576b7773d$var$r === (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).requestAnimationFrame || (($1a9a8ef576b7773d$var$r = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).requestAnimationFrame) || function(n14) {\n        var t9, u5 = function() {\n            clearTimeout(r9), $1a9a8ef576b7773d$var$b && cancelAnimationFrame(t9), setTimeout(n14);\n        }, r9 = setTimeout(u5, 100);\n        $1a9a8ef576b7773d$var$b && (t9 = requestAnimationFrame(u5));\n    })($1a9a8ef576b7773d$var$x)), $1a9a8ef576b7773d$var$u = null;\n}, (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__c = function(t10, u6) {\n    u6.some(function(t11) {\n        try {\n            t11.__h.forEach($1a9a8ef576b7773d$var$g), t11.__h = t11.__h.filter(function(n15) {\n                return !n15.__ || $1a9a8ef576b7773d$var$j(n15);\n            });\n        } catch (r10) {\n            u6.some(function(n16) {\n                n16.__h && (n16.__h = []);\n            }), u6 = [], (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__e(r10, t11.__v);\n        }\n    }), $1a9a8ef576b7773d$var$a && $1a9a8ef576b7773d$var$a(t10, u6);\n}, (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).unmount = function(t12) {\n    $1a9a8ef576b7773d$var$v && $1a9a8ef576b7773d$var$v(t12);\n    var u7, r11 = t12.__c;\n    r11 && r11.__H && (r11.__H.__.forEach(function(n17) {\n        try {\n            $1a9a8ef576b7773d$var$g(n17);\n        } catch (n18) {\n            u7 = n18;\n        }\n    }), u7 && (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__e(u7, r11.__v));\n};\nvar $1a9a8ef576b7773d$var$b = \"function\" == typeof requestAnimationFrame;\nfunction $1a9a8ef576b7773d$var$g(n19) {\n    var t13 = $1a9a8ef576b7773d$var$u, r12 = n19.__c;\n    \"function\" == typeof r12 && (n19.__c = void 0, r12()), $1a9a8ef576b7773d$var$u = t13;\n}\nfunction $1a9a8ef576b7773d$var$j(n20) {\n    var t14 = $1a9a8ef576b7773d$var$u;\n    n20.__c = n20.__(), $1a9a8ef576b7773d$var$u = t14;\n}\nfunction $1a9a8ef576b7773d$var$k(n21, t15) {\n    return !n21 || n21.length !== t15.length || t15.some(function(t16, u8) {\n        return t16 !== n21[u8];\n    });\n}\nfunction $1a9a8ef576b7773d$var$w(n22, t17) {\n    return \"function\" == typeof t17 ? t17(n22) : t17;\n}\n\n\n\n\n\nfunction $dc040a17866866fa$var$S(n1, t1) {\n    for(var e1 in t1)n1[e1] = t1[e1];\n    return n1;\n}\nfunction $dc040a17866866fa$var$C(n2, t2) {\n    for(var e2 in n2)if (\"__source\" !== e2 && !(e2 in t2)) return !0;\n    for(var r1 in t2)if (\"__source\" !== r1 && n2[r1] !== t2[r1]) return !0;\n    return !1;\n}\nfunction $dc040a17866866fa$export$221d75b3f55bb0bd(n3) {\n    this.props = n3;\n}\nfunction $dc040a17866866fa$export$7c73462e0d25e514(n4, t3) {\n    function e3(n5) {\n        var e4 = this.props.ref, r3 = e4 == n5.ref;\n        return !r3 && e4 && (e4.call ? e4(null) : e4.current = null), t3 ? !t3(this.props, n5) || !r3 : $dc040a17866866fa$var$C(this.props, n5);\n    }\n    function r2(t4) {\n        return this.shouldComponentUpdate = e3, (0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d)(n4, t4);\n    }\n    return r2.displayName = \"Memo(\" + (n4.displayName || n4.name) + \")\", r2.prototype.isReactComponent = !0, r2.__f = !0, r2;\n}\n($dc040a17866866fa$export$221d75b3f55bb0bd.prototype = new (0, $fb96b826c0c5f37a$export$16fa2f45be04daa8)).isPureReactComponent = !0, $dc040a17866866fa$export$221d75b3f55bb0bd.prototype.shouldComponentUpdate = function(n6, t5) {\n    return $dc040a17866866fa$var$C(this.props, n6) || $dc040a17866866fa$var$C(this.state, t5);\n};\nvar $dc040a17866866fa$var$w = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__b;\n(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__b = function(n7) {\n    n7.type && n7.type.__f && n7.ref && (n7.props.ref = n7.ref, n7.ref = null), $dc040a17866866fa$var$w && $dc040a17866866fa$var$w(n7);\n};\nvar $dc040a17866866fa$var$R = \"undefined\" != typeof Symbol && Symbol.for && Symbol.for(\"react.forward_ref\") || 3911;\nfunction $dc040a17866866fa$export$257a8862b851cb5b(n8) {\n    function t6(t7, e5) {\n        var r4 = $dc040a17866866fa$var$S({}, t7);\n        return delete r4.ref, n8(r4, (e5 = t7.ref || e5) && (\"object\" != typeof e5 || \"current\" in e5) ? e5 : null);\n    }\n    return t6.$$typeof = $dc040a17866866fa$var$R, t6.render = t6, t6.prototype.isReactComponent = t6.__f = !0, t6.displayName = \"ForwardRef(\" + (n8.displayName || n8.name) + \")\", t6;\n}\nvar $dc040a17866866fa$var$N = function(n9, t8) {\n    return null == n9 ? null : (0, $fb96b826c0c5f37a$export$47e4c5b300681277)((0, $fb96b826c0c5f37a$export$47e4c5b300681277)(n9).map(t8));\n}, $dc040a17866866fa$export$dca3b0875bd9a954 = {\n    map: $dc040a17866866fa$var$N,\n    forEach: $dc040a17866866fa$var$N,\n    count: function(n10) {\n        return n10 ? (0, $fb96b826c0c5f37a$export$47e4c5b300681277)(n10).length : 0;\n    },\n    only: function(n11) {\n        var t9 = (0, $fb96b826c0c5f37a$export$47e4c5b300681277)(n11);\n        if (1 !== t9.length) throw \"Children.only\";\n        return t9[0];\n    },\n    toArray: (0, $fb96b826c0c5f37a$export$47e4c5b300681277)\n}, $dc040a17866866fa$var$A = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__e;\n(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__e = function(n12, t10, e6) {\n    if (n12.then) {\n        for(var r5, u1 = t10; u1 = u1.__;)if ((r5 = u1.__c) && r5.__c) return null == t10.__e && (t10.__e = e6.__e, t10.__k = e6.__k), r5.__c(n12, t10);\n    }\n    $dc040a17866866fa$var$A(n12, t10, e6);\n};\nvar $dc040a17866866fa$var$O = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).unmount;\nfunction $dc040a17866866fa$export$74bf444e3cd11ea5() {\n    this.__u = 0, this.t = null, this.__b = null;\n}\nfunction $dc040a17866866fa$var$U(n13) {\n    var t11 = n13.__.__c;\n    return t11 && t11.__e && t11.__e(n13);\n}\nfunction $dc040a17866866fa$export$488013bae63b21da(n14) {\n    var t12, e7, r6;\n    function u2(u3) {\n        if (t12 || (t12 = n14()).then(function(n15) {\n            e7 = n15.default || n15;\n        }, function(n16) {\n            r6 = n16;\n        }), r6) throw r6;\n        if (!e7) throw t12;\n        return (0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d)(e7, u3);\n    }\n    return u2.displayName = \"Lazy\", u2.__f = !0, u2;\n}\nfunction $dc040a17866866fa$export$998bcd577473dd93() {\n    this.u = null, this.o = null;\n}\n(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).unmount = function(n17) {\n    var t13 = n17.__c;\n    t13 && t13.__R && t13.__R(), t13 && !0 === n17.__h && (n17.type = null), $dc040a17866866fa$var$O && $dc040a17866866fa$var$O(n17);\n}, ($dc040a17866866fa$export$74bf444e3cd11ea5.prototype = new (0, $fb96b826c0c5f37a$export$16fa2f45be04daa8)).__c = function(n18, t14) {\n    var e8 = t14.__c, r7 = this;\n    null == r7.t && (r7.t = []), r7.t.push(e8);\n    var u4 = $dc040a17866866fa$var$U(r7.__v), o1 = !1, i1 = function() {\n        o1 || (o1 = !0, e8.__R = null, u4 ? u4(l1) : l1());\n    };\n    e8.__R = i1;\n    var l1 = function() {\n        if (!--r7.__u) {\n            if (r7.state.__e) {\n                var n19 = r7.state.__e;\n                r7.__v.__k[0] = function n22(t17, e9, r8) {\n                    return t17 && (t17.__v = null, t17.__k = t17.__k && t17.__k.map(function(t18) {\n                        return n22(t18, e9, r8);\n                    }), t17.__c && t17.__c.__P === e9 && (t17.__e && r8.insertBefore(t17.__e, t17.__d), t17.__c.__e = !0, t17.__c.__P = r8)), t17;\n                }(n19, n19.__c.__P, n19.__c.__O);\n            }\n            var t15;\n            for(r7.setState({\n                __e: r7.__b = null\n            }); t15 = r7.t.pop();)t15.forceUpdate();\n        }\n    }, c1 = !0 === t14.__h;\n    (r7.__u++) || c1 || r7.setState({\n        __e: r7.__b = r7.__v.__k[0]\n    }), n18.then(i1, i1);\n}, $dc040a17866866fa$export$74bf444e3cd11ea5.prototype.componentWillUnmount = function() {\n    this.t = [];\n}, $dc040a17866866fa$export$74bf444e3cd11ea5.prototype.render = function(n23, t19) {\n    if (this.__b) {\n        if (this.__v.__k) {\n            var e10 = document.createElement(\"div\"), r9 = this.__v.__k[0].__c;\n            this.__v.__k[0] = function n24(t20, e13, r12) {\n                return t20 && (t20.__c && t20.__c.__H && (t20.__c.__H.__.forEach(function(n25) {\n                    \"function\" == typeof n25.__c && n25.__c();\n                }), t20.__c.__H = null), null != (t20 = $dc040a17866866fa$var$S({}, t20)).__c && (t20.__c.__P === r12 && (t20.__c.__P = e13), t20.__c = null), t20.__k = t20.__k && t20.__k.map(function(t21) {\n                    return n24(t21, e13, r12);\n                })), t20;\n            }(this.__b, e10, r9.__O = r9.__P);\n        }\n        this.__b = null;\n    }\n    var u5 = t19.__e && (0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d)((0, $fb96b826c0c5f37a$export$ffb0004e005737fa), null, n23.fallback);\n    return u5 && (u5.__h = null), [\n        (0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d)((0, $fb96b826c0c5f37a$export$ffb0004e005737fa), null, t19.__e ? null : n23.children),\n        u5\n    ];\n};\nvar $dc040a17866866fa$var$T = function(n26, t22, e14) {\n    if (++e14[1] === e14[0] && n26.o.delete(t22), n26.props.revealOrder && (\"t\" !== n26.props.revealOrder[0] || !n26.o.size)) for(e14 = n26.u; e14;){\n        for(; e14.length > 3;)e14.pop()();\n        if (e14[1] < e14[0]) break;\n        n26.u = e14 = e14[2];\n    }\n};\nfunction $dc040a17866866fa$var$D(n27) {\n    return this.getChildContext = function() {\n        return n27.context;\n    }, n27.children;\n}\nfunction $dc040a17866866fa$var$I(n28) {\n    var t23 = this, e15 = n28.i;\n    t23.componentWillUnmount = function() {\n        (0, $fb96b826c0c5f37a$export$b3890eb0ae9dca99)(null, t23.l), t23.l = null, t23.i = null;\n    }, t23.i && t23.i !== e15 && t23.componentWillUnmount(), n28.__v ? (t23.l || (t23.i = e15, t23.l = {\n        nodeType: 1,\n        parentNode: e15,\n        childNodes: [],\n        appendChild: function(n29) {\n            this.childNodes.push(n29), t23.i.appendChild(n29);\n        },\n        insertBefore: function(n30, e) {\n            this.childNodes.push(n30), t23.i.appendChild(n30);\n        },\n        removeChild: function(n31) {\n            this.childNodes.splice(this.childNodes.indexOf(n31) >>> 1, 1), t23.i.removeChild(n31);\n        }\n    }), (0, $fb96b826c0c5f37a$export$b3890eb0ae9dca99)((0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d)($dc040a17866866fa$var$D, {\n        context: t23.context\n    }, n28.__v), t23.l)) : t23.l && t23.componentWillUnmount();\n}\nfunction $dc040a17866866fa$export$d39a5bbd09211389(n32, t24) {\n    return (0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d)($dc040a17866866fa$var$I, {\n        __v: n32,\n        i: t24\n    });\n}\n($dc040a17866866fa$export$998bcd577473dd93.prototype = new (0, $fb96b826c0c5f37a$export$16fa2f45be04daa8)).__e = function(n33) {\n    var t25 = this, e16 = $dc040a17866866fa$var$U(t25.__v), r13 = t25.o.get(n33);\n    return r13[0]++, function(u6) {\n        var o2 = function() {\n            t25.props.revealOrder ? (r13.push(u6), $dc040a17866866fa$var$T(t25, n33, r13)) : u6();\n        };\n        e16 ? e16(o2) : o2();\n    };\n}, $dc040a17866866fa$export$998bcd577473dd93.prototype.render = function(n34) {\n    this.u = null, this.o = new Map;\n    var t26 = (0, $fb96b826c0c5f37a$export$47e4c5b300681277)(n34.children);\n    n34.revealOrder && \"b\" === n34.revealOrder[0] && t26.reverse();\n    for(var e17 = t26.length; e17--;)this.o.set(t26[e17], this.u = [\n        1,\n        0,\n        this.u\n    ]);\n    return n34.children;\n}, $dc040a17866866fa$export$998bcd577473dd93.prototype.componentDidUpdate = $dc040a17866866fa$export$998bcd577473dd93.prototype.componentDidMount = function() {\n    var n35 = this;\n    this.o.forEach(function(t27, e18) {\n        $dc040a17866866fa$var$T(n35, e18, t27);\n    });\n};\nvar $dc040a17866866fa$var$j = \"undefined\" != typeof Symbol && Symbol.for && Symbol.for(\"react.element\") || 60103, $dc040a17866866fa$var$P = /^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|marker(?!H|W|U)|overline|paint|stop|strikethrough|stroke|text(?!L)|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/, $dc040a17866866fa$var$V = \"undefined\" != typeof document, $dc040a17866866fa$var$z = function(n36) {\n    return (\"undefined\" != typeof Symbol && \"symbol\" == typeof Symbol() ? /fil|che|rad/i : /fil|che|ra/i).test(n36);\n};\nfunction $dc040a17866866fa$export$b3890eb0ae9dca99(n37, t28, e19) {\n    return null == t28.__k && (t28.textContent = \"\"), (0, $fb96b826c0c5f37a$export$b3890eb0ae9dca99)(n37, t28), \"function\" == typeof e19 && e19(), n37 ? n37.__c : null;\n}\nfunction $dc040a17866866fa$export$fa8d919ba61d84db(n38, t29, e20) {\n    return (0, $fb96b826c0c5f37a$export$fa8d919ba61d84db)(n38, t29), \"function\" == typeof e20 && e20(), n38 ? n38.__c : null;\n}\n(0, $fb96b826c0c5f37a$export$16fa2f45be04daa8).prototype.isReactComponent = {}, [\n    \"componentWillMount\",\n    \"componentWillReceiveProps\",\n    \"componentWillUpdate\"\n].forEach(function(n39) {\n    Object.defineProperty((0, $fb96b826c0c5f37a$export$16fa2f45be04daa8).prototype, n39, {\n        configurable: !0,\n        get: function() {\n            return this[\"UNSAFE_\" + n39];\n        },\n        set: function(t30) {\n            Object.defineProperty(this, n39, {\n                configurable: !0,\n                writable: !0,\n                value: t30\n            });\n        }\n    });\n});\nvar $dc040a17866866fa$var$H = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).event;\nfunction $dc040a17866866fa$var$Z() {}\nfunction $dc040a17866866fa$var$Y() {\n    return this.cancelBubble;\n}\nfunction $dc040a17866866fa$var$q() {\n    return this.defaultPrevented;\n}\n(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).event = function(n40) {\n    return $dc040a17866866fa$var$H && (n40 = $dc040a17866866fa$var$H(n40)), n40.persist = $dc040a17866866fa$var$Z, n40.isPropagationStopped = $dc040a17866866fa$var$Y, n40.isDefaultPrevented = $dc040a17866866fa$var$q, n40.nativeEvent = n40;\n};\nvar $dc040a17866866fa$var$G, $dc040a17866866fa$var$J = {\n    configurable: !0,\n    get: function() {\n        return this.class;\n    }\n}, $dc040a17866866fa$var$K = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).vnode;\n(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).vnode = function(n41) {\n    var t31 = n41.type, e21 = n41.props, r14 = e21;\n    if (\"string\" == typeof t31) {\n        var u7 = -1 === t31.indexOf(\"-\");\n        for(var o3 in r14 = {}, e21){\n            var i2 = e21[o3];\n            $dc040a17866866fa$var$V && \"children\" === o3 && \"noscript\" === t31 || \"value\" === o3 && \"defaultValue\" in e21 && null == i2 || (\"defaultValue\" === o3 && \"value\" in e21 && null == e21.value ? o3 = \"value\" : \"download\" === o3 && !0 === i2 ? i2 = \"\" : /ondoubleclick/i.test(o3) ? o3 = \"ondblclick\" : /^onchange(textarea|input)/i.test(o3 + t31) && !$dc040a17866866fa$var$z(e21.type) ? o3 = \"oninput\" : /^onfocus$/i.test(o3) ? o3 = \"onfocusin\" : /^onblur$/i.test(o3) ? o3 = \"onfocusout\" : /^on(Ani|Tra|Tou|BeforeInp)/.test(o3) ? o3 = o3.toLowerCase() : u7 && $dc040a17866866fa$var$P.test(o3) ? o3 = o3.replace(/[A-Z0-9]/, \"-$&\").toLowerCase() : null === i2 && (i2 = void 0), r14[o3] = i2);\n        }\n        \"select\" == t31 && r14.multiple && Array.isArray(r14.value) && (r14.value = (0, $fb96b826c0c5f37a$export$47e4c5b300681277)(e21.children).forEach(function(n42) {\n            n42.props.selected = -1 != r14.value.indexOf(n42.props.value);\n        })), \"select\" == t31 && null != r14.defaultValue && (r14.value = (0, $fb96b826c0c5f37a$export$47e4c5b300681277)(e21.children).forEach(function(n43) {\n            n43.props.selected = r14.multiple ? -1 != r14.defaultValue.indexOf(n43.props.value) : r14.defaultValue == n43.props.value;\n        })), n41.props = r14, e21.class != e21.className && ($dc040a17866866fa$var$J.enumerable = \"className\" in e21, null != e21.className && (r14.class = e21.className), Object.defineProperty(r14, \"className\", $dc040a17866866fa$var$J));\n    }\n    n41.$$typeof = $dc040a17866866fa$var$j, $dc040a17866866fa$var$K && $dc040a17866866fa$var$K(n41);\n};\nvar $dc040a17866866fa$var$Q = (0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__r;\n(0, $fb96b826c0c5f37a$export$41c562ebe57d11e2).__r = function(n44) {\n    $dc040a17866866fa$var$Q && $dc040a17866866fa$var$Q(n44), $dc040a17866866fa$var$G = n44.__c;\n};\nvar $dc040a17866866fa$export$ae55be85d98224ed = {\n    ReactCurrentDispatcher: {\n        current: {\n            readContext: function(n45) {\n                return $dc040a17866866fa$var$G.__n[n45.__c].props.value;\n            }\n        }\n    }\n}, $dc040a17866866fa$export$83d89fbfd8236492 = \"17.0.2\";\nfunction $dc040a17866866fa$export$d38cd72104c1f0e9(n46) {\n    return (0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d).bind(null, n46);\n}\nfunction $dc040a17866866fa$export$a8257692ac88316c(n47) {\n    return !!n47 && n47.$$typeof === $dc040a17866866fa$var$j;\n}\nfunction $dc040a17866866fa$export$e530037191fcd5d7(n48) {\n    return $dc040a17866866fa$export$a8257692ac88316c(n48) ? (0, $fb96b826c0c5f37a$export$e530037191fcd5d7).apply(null, arguments) : n48;\n}\nfunction $dc040a17866866fa$export$502457920280e6be(n49) {\n    return !!n49.__k && ((0, $fb96b826c0c5f37a$export$b3890eb0ae9dca99)(null, n49), !0);\n}\nfunction $dc040a17866866fa$export$466bfc07425424d5(n50) {\n    return n50 && (n50.base || 1 === n50.nodeType && n50) || null;\n}\nvar $dc040a17866866fa$export$c78a37762a8d58e1 = function(n51, t32) {\n    return n51(t32);\n}, $dc040a17866866fa$export$cd75ccfd720a3cd4 = function(n52, t33) {\n    return n52(t33);\n}, $dc040a17866866fa$export$5f8d39834fd61797 = (0, $fb96b826c0c5f37a$export$ffb0004e005737fa);\nvar $dc040a17866866fa$export$2e2bcd8739ae039 = {\n    useState: (0, $1a9a8ef576b7773d$export$60241385465d0a34),\n    useReducer: (0, $1a9a8ef576b7773d$export$13e3392192263954),\n    useEffect: (0, $1a9a8ef576b7773d$export$6d9c69b0de29b591),\n    useLayoutEffect: (0, $1a9a8ef576b7773d$export$e5c5a5f917a5871c),\n    useRef: (0, $1a9a8ef576b7773d$export$b8f5890fc79d6aca),\n    useImperativeHandle: (0, $1a9a8ef576b7773d$export$d5a552a76deda3c2),\n    useMemo: (0, $1a9a8ef576b7773d$export$1538c33de8887b59),\n    useCallback: (0, $1a9a8ef576b7773d$export$35808ee640e87ca7),\n    useContext: (0, $1a9a8ef576b7773d$export$fae74005e78b1a27),\n    useDebugValue: (0, $1a9a8ef576b7773d$export$dc8fbce3eb94dc1e),\n    version: \"17.0.2\",\n    Children: $dc040a17866866fa$export$dca3b0875bd9a954,\n    render: $dc040a17866866fa$export$b3890eb0ae9dca99,\n    hydrate: $dc040a17866866fa$export$fa8d919ba61d84db,\n    unmountComponentAtNode: $dc040a17866866fa$export$502457920280e6be,\n    createPortal: $dc040a17866866fa$export$d39a5bbd09211389,\n    createElement: (0, $fb96b826c0c5f37a$export$c8a8987d4410bf2d),\n    createContext: (0, $fb96b826c0c5f37a$export$fd42f52fd3ae1109),\n    createFactory: $dc040a17866866fa$export$d38cd72104c1f0e9,\n    cloneElement: $dc040a17866866fa$export$e530037191fcd5d7,\n    createRef: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43),\n    Fragment: (0, $fb96b826c0c5f37a$export$ffb0004e005737fa),\n    isValidElement: $dc040a17866866fa$export$a8257692ac88316c,\n    findDOMNode: $dc040a17866866fa$export$466bfc07425424d5,\n    Component: (0, $fb96b826c0c5f37a$export$16fa2f45be04daa8),\n    PureComponent: $dc040a17866866fa$export$221d75b3f55bb0bd,\n    memo: $dc040a17866866fa$export$7c73462e0d25e514,\n    forwardRef: $dc040a17866866fa$export$257a8862b851cb5b,\n    flushSync: $dc040a17866866fa$export$cd75ccfd720a3cd4,\n    unstable_batchedUpdates: $dc040a17866866fa$export$c78a37762a8d58e1,\n    StrictMode: (0, $fb96b826c0c5f37a$export$ffb0004e005737fa),\n    Suspense: $dc040a17866866fa$export$74bf444e3cd11ea5,\n    SuspenseList: $dc040a17866866fa$export$998bcd577473dd93,\n    lazy: $dc040a17866866fa$export$488013bae63b21da,\n    __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED: $dc040a17866866fa$export$ae55be85d98224ed\n};\n\n\n\n\nconst $ec8c39fdad15601a$var$THEME_ICONS = {\n    light: \"outline\",\n    dark: \"solid\"\n};\nclass $ec8c39fdad15601a$export$2e2bcd8739ae039 extends (0, $dc040a17866866fa$export$221d75b3f55bb0bd) {\n    renderIcon(category) {\n        const { icon: icon  } = category;\n        if (icon) {\n            if (icon.svg) return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n                class: \"flex\",\n                dangerouslySetInnerHTML: {\n                    __html: icon.svg\n                }\n            });\n            if (icon.src) return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"img\", {\n                src: icon.src\n            });\n        }\n        const categoryIcons = (0, $fcccfb36ed0cde68$export$2e2bcd8739ae039).categories[category.id] || (0, $fcccfb36ed0cde68$export$2e2bcd8739ae039).categories.custom;\n        const style = this.props.icons == \"auto\" ? $ec8c39fdad15601a$var$THEME_ICONS[this.props.theme] : this.props.icons;\n        return categoryIcons[style] || categoryIcons;\n    }\n    render() {\n        let selectedCategoryIndex = null;\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"nav\", {\n            id: \"nav\",\n            class: \"padding\",\n            \"data-position\": this.props.position,\n            dir: this.props.dir,\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                class: \"flex relative\",\n                children: [\n                    this.categories.map((category, i)=>{\n                        const title = category.name || (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).categories[category.id];\n                        const selected = !this.props.unfocused && category.id == this.state.categoryId;\n                        if (selected) selectedCategoryIndex = i;\n                        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"button\", {\n                            \"aria-label\": title,\n                            \"aria-selected\": selected || undefined,\n                            title: title,\n                            type: \"button\",\n                            class: \"flex flex-grow flex-center\",\n                            onMouseDown: (e)=>e.preventDefault(),\n                            onClick: ()=>{\n                                this.props.onClick({\n                                    category: category,\n                                    i: i\n                                });\n                            },\n                            children: this.renderIcon(category)\n                        });\n                    }),\n                    /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                        class: \"bar\",\n                        style: {\n                            width: `${100 / this.categories.length}%`,\n                            opacity: selectedCategoryIndex == null ? 0 : 1,\n                            transform: this.props.dir === \"rtl\" ? `scaleX(-1) translateX(${selectedCategoryIndex * 100}%)` : `translateX(${selectedCategoryIndex * 100}%)`\n                        }\n                    })\n                ]\n            })\n        });\n    }\n    constructor(){\n        super();\n        this.categories = (0, $7adb23b0109cc36a$export$2d0294657ab35f1b).categories.filter((category)=>{\n            return !category.target;\n        });\n        this.state = {\n            categoryId: this.categories[0].id\n        };\n    }\n}\n\n\n\n\n\nclass $e0d4dda61265ff1e$export$2e2bcd8739ae039 extends (0, $dc040a17866866fa$export$221d75b3f55bb0bd) {\n    shouldComponentUpdate(nextProps) {\n        for(let k in nextProps){\n            if (k == \"children\") continue;\n            if (nextProps[k] != this.props[k]) return true;\n        }\n        return false;\n    }\n    render() {\n        return this.props.children;\n    }\n}\n\n\n\n\nconst $89bd6bb200cc8fef$var$Performance = {\n    rowsPerRender: 10\n};\nclass $89bd6bb200cc8fef$export$2e2bcd8739ae039 extends (0, $fb96b826c0c5f37a$export$16fa2f45be04daa8) {\n    getInitialState(props = this.props) {\n        return {\n            skin: (0, $f72b75cf796873c7$export$2e2bcd8739ae039).get(\"skin\") || props.skin,\n            theme: this.initTheme(props.theme)\n        };\n    }\n    componentWillMount() {\n        this.dir = (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).rtl ? \"rtl\" : \"ltr\";\n        this.refs = {\n            menu: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)(),\n            navigation: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)(),\n            scroll: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)(),\n            search: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)(),\n            searchInput: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)(),\n            skinToneButton: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)(),\n            skinToneRadio: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)()\n        };\n        this.initGrid();\n        if (this.props.stickySearch == false && this.props.searchPosition == \"sticky\") {\n            console.warn(\"[EmojiMart] Deprecation warning: `stickySearch` has been renamed `searchPosition`.\");\n            this.props.searchPosition = \"static\";\n        }\n    }\n    componentDidMount() {\n        this.register();\n        this.shadowRoot = this.base.parentNode;\n        if (this.props.autoFocus) {\n            const { searchInput: searchInput  } = this.refs;\n            if (searchInput.current) searchInput.current.focus();\n        }\n    }\n    componentWillReceiveProps(nextProps) {\n        this.nextState || (this.nextState = {});\n        for(const k1 in nextProps)this.nextState[k1] = nextProps[k1];\n        clearTimeout(this.nextStateTimer);\n        this.nextStateTimer = setTimeout(()=>{\n            let requiresGridReset = false;\n            for(const k in this.nextState){\n                this.props[k] = this.nextState[k];\n                if (k === \"custom\" || k === \"categories\") requiresGridReset = true;\n            }\n            delete this.nextState;\n            const nextState = this.getInitialState();\n            if (requiresGridReset) return this.reset(nextState);\n            this.setState(nextState);\n        });\n    }\n    componentWillUnmount() {\n        this.unregister();\n    }\n    async reset(nextState = {}) {\n        await (0, $7adb23b0109cc36a$export$2cd8252107eb640b)(this.props);\n        this.initGrid();\n        this.unobserve();\n        this.setState(nextState, ()=>{\n            this.observeCategories();\n            this.observeRows();\n        });\n    }\n    register() {\n        document.addEventListener(\"click\", this.handleClickOutside);\n        this.observe();\n    }\n    unregister() {\n        document.removeEventListener(\"click\", this.handleClickOutside);\n        this.darkMedia?.removeEventListener(\"change\", this.darkMediaCallback);\n        this.unobserve();\n    }\n    observe() {\n        this.observeCategories();\n        this.observeRows();\n    }\n    unobserve({ except: except = []  } = {}) {\n        if (!Array.isArray(except)) except = [\n            except\n        ];\n        for (const observer of this.observers){\n            if (except.includes(observer)) continue;\n            observer.disconnect();\n        }\n        this.observers = [].concat(except);\n    }\n    initGrid() {\n        const { categories: categories  } = (0, $7adb23b0109cc36a$export$2d0294657ab35f1b);\n        this.refs.categories = new Map();\n        const navKey = (0, $7adb23b0109cc36a$export$2d0294657ab35f1b).categories.map((category)=>category.id).join(\",\");\n        if (this.navKey && this.navKey != navKey) this.refs.scroll.current && (this.refs.scroll.current.scrollTop = 0);\n        this.navKey = navKey;\n        this.grid = [];\n        this.grid.setsize = 0;\n        const addRow = (rows, category)=>{\n            const row = [];\n            row.__categoryId = category.id;\n            row.__index = rows.length;\n            this.grid.push(row);\n            const rowIndex = this.grid.length - 1;\n            const rowRef = rowIndex % $89bd6bb200cc8fef$var$Performance.rowsPerRender ? {} : (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)();\n            rowRef.index = rowIndex;\n            rowRef.posinset = this.grid.setsize + 1;\n            rows.push(rowRef);\n            return row;\n        };\n        for (let category1 of categories){\n            const rows = [];\n            let row = addRow(rows, category1);\n            for (let emoji of category1.emojis){\n                if (row.length == this.getPerLine()) row = addRow(rows, category1);\n                this.grid.setsize += 1;\n                row.push(emoji);\n            }\n            this.refs.categories.set(category1.id, {\n                root: (0, $fb96b826c0c5f37a$export$7d1e3a5e95ceca43)(),\n                rows: rows\n            });\n        }\n    }\n    initTheme(theme) {\n        if (theme != \"auto\") return theme;\n        if (!this.darkMedia) {\n            this.darkMedia = matchMedia(\"(prefers-color-scheme: dark)\");\n            if (this.darkMedia.media.match(/^not/)) return \"light\";\n            this.darkMedia.addEventListener(\"change\", this.darkMediaCallback);\n        }\n        return this.darkMedia.matches ? \"dark\" : \"light\";\n    }\n    initDynamicPerLine(props = this.props) {\n        if (!props.dynamicWidth) return;\n        const { element: element , emojiButtonSize: emojiButtonSize  } = props;\n        const calculatePerLine = ()=>{\n            const { width: width  } = element.getBoundingClientRect();\n            return Math.floor(width / emojiButtonSize);\n        };\n        const observer = new ResizeObserver(()=>{\n            this.unobserve({\n                except: observer\n            });\n            this.setState({\n                perLine: calculatePerLine()\n            }, ()=>{\n                this.initGrid();\n                this.forceUpdate(()=>{\n                    this.observeCategories();\n                    this.observeRows();\n                });\n            });\n        });\n        observer.observe(element);\n        this.observers.push(observer);\n        return calculatePerLine();\n    }\n    getPerLine() {\n        return this.state.perLine || this.props.perLine;\n    }\n    getEmojiByPos([p1, p2]) {\n        const grid = this.state.searchResults || this.grid;\n        const emoji = grid[p1] && grid[p1][p2];\n        if (!emoji) return;\n        return (0, $c4d155af13ad4d4b$export$2e2bcd8739ae039).get(emoji);\n    }\n    observeCategories() {\n        const navigation = this.refs.navigation.current;\n        if (!navigation) return;\n        const visibleCategories = new Map();\n        const setFocusedCategory = (categoryId)=>{\n            if (categoryId != navigation.state.categoryId) navigation.setState({\n                categoryId: categoryId\n            });\n        };\n        const observerOptions = {\n            root: this.refs.scroll.current,\n            threshold: [\n                0.0,\n                1.0\n            ]\n        };\n        const observer = new IntersectionObserver((entries)=>{\n            for (const entry of entries){\n                const id = entry.target.dataset.id;\n                visibleCategories.set(id, entry.intersectionRatio);\n            }\n            const ratios = [\n                ...visibleCategories\n            ];\n            for (const [id, ratio] of ratios)if (ratio) {\n                setFocusedCategory(id);\n                break;\n            }\n        }, observerOptions);\n        for (const { root: root  } of this.refs.categories.values())observer.observe(root.current);\n        this.observers.push(observer);\n    }\n    observeRows() {\n        const visibleRows = {\n            ...this.state.visibleRows\n        };\n        const observer = new IntersectionObserver((entries)=>{\n            for (const entry of entries){\n                const index = parseInt(entry.target.dataset.index);\n                if (entry.isIntersecting) visibleRows[index] = true;\n                else delete visibleRows[index];\n            }\n            this.setState({\n                visibleRows: visibleRows\n            });\n        }, {\n            root: this.refs.scroll.current,\n            rootMargin: `${this.props.emojiButtonSize * ($89bd6bb200cc8fef$var$Performance.rowsPerRender + 5)}px 0px ${this.props.emojiButtonSize * $89bd6bb200cc8fef$var$Performance.rowsPerRender}px`\n        });\n        for (const { rows: rows  } of this.refs.categories.values()){\n            for (const row of rows)if (row.current) observer.observe(row.current);\n        }\n        this.observers.push(observer);\n    }\n    preventDefault(e) {\n        e.preventDefault();\n    }\n    unfocusSearch() {\n        const input = this.refs.searchInput.current;\n        if (!input) return;\n        input.blur();\n    }\n    navigate({ e: e , input: input , left: left , right: right , up: up , down: down  }) {\n        const grid = this.state.searchResults || this.grid;\n        if (!grid.length) return;\n        let [p1, p2] = this.state.pos;\n        const pos = (()=>{\n            if (p1 == 0) {\n                if (p2 == 0 && !e.repeat && (left || up)) return null;\n            }\n            if (p1 == -1) {\n                if (!e.repeat && (right || down) && input.selectionStart == input.value.length) return [\n                    0,\n                    0\n                ];\n                return null;\n            }\n            if (left || right) {\n                let row = grid[p1];\n                const increment = left ? -1 : 1;\n                p2 += increment;\n                if (!row[p2]) {\n                    p1 += increment;\n                    row = grid[p1];\n                    if (!row) {\n                        p1 = left ? 0 : grid.length - 1;\n                        p2 = left ? 0 : grid[p1].length - 1;\n                        return [\n                            p1,\n                            p2\n                        ];\n                    }\n                    p2 = left ? row.length - 1 : 0;\n                }\n                return [\n                    p1,\n                    p2\n                ];\n            }\n            if (up || down) {\n                p1 += up ? -1 : 1;\n                const row = grid[p1];\n                if (!row) {\n                    p1 = up ? 0 : grid.length - 1;\n                    p2 = up ? 0 : grid[p1].length - 1;\n                    return [\n                        p1,\n                        p2\n                    ];\n                }\n                if (!row[p2]) p2 = row.length - 1;\n                return [\n                    p1,\n                    p2\n                ];\n            }\n        })();\n        if (pos) e.preventDefault();\n        else {\n            if (this.state.pos[0] > -1) this.setState({\n                pos: [\n                    -1,\n                    -1\n                ]\n            });\n            return;\n        }\n        this.setState({\n            pos: pos,\n            keyboard: true\n        }, ()=>{\n            this.scrollTo({\n                row: pos[0]\n            });\n        });\n    }\n    scrollTo({ categoryId: categoryId , row: row  }) {\n        const grid = this.state.searchResults || this.grid;\n        if (!grid.length) return;\n        const scroll = this.refs.scroll.current;\n        const scrollRect = scroll.getBoundingClientRect();\n        let scrollTop = 0;\n        if (row >= 0) categoryId = grid[row].__categoryId;\n        if (categoryId) {\n            const ref = this.refs[categoryId] || this.refs.categories.get(categoryId).root;\n            const categoryRect = ref.current.getBoundingClientRect();\n            scrollTop = categoryRect.top - (scrollRect.top - scroll.scrollTop) + 1;\n        }\n        if (row >= 0) {\n            if (!row) scrollTop = 0;\n            else {\n                const rowIndex = grid[row].__index;\n                const rowTop = scrollTop + rowIndex * this.props.emojiButtonSize;\n                const rowBot = rowTop + this.props.emojiButtonSize + this.props.emojiButtonSize * 0.88;\n                if (rowTop < scroll.scrollTop) scrollTop = rowTop;\n                else if (rowBot > scroll.scrollTop + scrollRect.height) scrollTop = rowBot - scrollRect.height;\n                else return;\n            }\n        }\n        this.ignoreMouse();\n        scroll.scrollTop = scrollTop;\n    }\n    ignoreMouse() {\n        this.mouseIsIgnored = true;\n        clearTimeout(this.ignoreMouseTimer);\n        this.ignoreMouseTimer = setTimeout(()=>{\n            delete this.mouseIsIgnored;\n        }, 100);\n    }\n    handleEmojiOver(pos) {\n        if (this.mouseIsIgnored || this.state.showSkins) return;\n        this.setState({\n            pos: pos || [\n                -1,\n                -1\n            ],\n            keyboard: false\n        });\n    }\n    handleEmojiClick({ e: e , emoji: emoji , pos: pos  }) {\n        if (!this.props.onEmojiSelect) return;\n        if (!emoji && pos) emoji = this.getEmojiByPos(pos);\n        if (emoji) {\n            const emojiData = (0, $693b183b0a78708f$export$d10ac59fbe52a745)(emoji, {\n                skinIndex: this.state.skin - 1\n            });\n            if (this.props.maxFrequentRows) (0, $b22cfd0a55410b4f$export$2e2bcd8739ae039).add(emojiData, this.props);\n            this.props.onEmojiSelect(emojiData, e);\n        }\n    }\n    closeSkins() {\n        if (!this.state.showSkins) return;\n        this.setState({\n            showSkins: null,\n            tempSkin: null\n        });\n        this.base.removeEventListener(\"click\", this.handleBaseClick);\n        this.base.removeEventListener(\"keydown\", this.handleBaseKeydown);\n    }\n    handleSkinMouseOver(tempSkin) {\n        this.setState({\n            tempSkin: tempSkin\n        });\n    }\n    handleSkinClick(skin) {\n        this.ignoreMouse();\n        this.closeSkins();\n        this.setState({\n            skin: skin,\n            tempSkin: null\n        });\n        (0, $f72b75cf796873c7$export$2e2bcd8739ae039).set(\"skin\", skin);\n    }\n    renderNav() {\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)((0, $ec8c39fdad15601a$export$2e2bcd8739ae039), {\n            ref: this.refs.navigation,\n            icons: this.props.icons,\n            theme: this.state.theme,\n            dir: this.dir,\n            unfocused: !!this.state.searchResults,\n            position: this.props.navPosition,\n            onClick: this.handleCategoryClick\n        }, this.navKey);\n    }\n    renderPreview() {\n        const emoji = this.getEmojiByPos(this.state.pos);\n        const noSearchResults = this.state.searchResults && !this.state.searchResults.length;\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n            id: \"preview\",\n            class: \"flex flex-middle\",\n            dir: this.dir,\n            \"data-position\": this.props.previewPosition,\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    class: \"flex flex-middle flex-grow\",\n                    children: [\n                        /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                            class: \"flex flex-auto flex-middle flex-center\",\n                            style: {\n                                height: this.props.emojiButtonSize,\n                                fontSize: this.props.emojiButtonSize\n                            },\n                            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)((0, $254755d3f438722f$export$2e2bcd8739ae039), {\n                                emoji: emoji,\n                                id: noSearchResults ? this.props.noResultsEmoji || \"cry\" : this.props.previewEmoji || (this.props.previewPosition == \"top\" ? \"point_down\" : \"point_up\"),\n                                set: this.props.set,\n                                size: this.props.emojiButtonSize,\n                                skin: this.state.tempSkin || this.state.skin,\n                                spritesheet: true,\n                                getSpritesheetURL: this.props.getSpritesheetURL\n                            })\n                        }),\n                        /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                            class: `margin-${this.dir[0]}`,\n                            children: emoji || noSearchResults ? /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                                class: `padding-${this.dir[2]} align-${this.dir[0]}`,\n                                children: [\n                                    /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                                        class: \"preview-title ellipsis\",\n                                        children: emoji ? emoji.name : (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).search_no_results_1\n                                    }),\n                                    /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                                        class: \"preview-subtitle ellipsis color-c\",\n                                        children: emoji ? emoji.skins[0].shortcodes : (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).search_no_results_2\n                                    })\n                                ]\n                            }) : /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                                class: \"preview-placeholder color-c\",\n                                children: (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).pick\n                            })\n                        })\n                    ]\n                }),\n                !emoji && this.props.skinTonePosition == \"preview\" && this.renderSkinToneButton()\n            ]\n        });\n    }\n    renderEmojiButton(emoji, { pos: pos , posinset: posinset , grid: grid  }) {\n        const size = this.props.emojiButtonSize;\n        const skin = this.state.tempSkin || this.state.skin;\n        const emojiSkin = emoji.skins[skin - 1] || emoji.skins[0];\n        const native = emojiSkin.native;\n        const selected = (0, $693b183b0a78708f$export$9cb4719e2e525b7a)(this.state.pos, pos);\n        const key = pos.concat(emoji.id).join(\"\");\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)((0, $e0d4dda61265ff1e$export$2e2bcd8739ae039), {\n            selected: selected,\n            skin: skin,\n            size: size,\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"button\", {\n                \"aria-label\": native,\n                \"aria-selected\": selected || undefined,\n                \"aria-posinset\": posinset,\n                \"aria-setsize\": grid.setsize,\n                \"data-keyboard\": this.state.keyboard,\n                title: this.props.previewPosition == \"none\" ? emoji.name : undefined,\n                type: \"button\",\n                class: \"flex flex-center flex-middle\",\n                tabindex: \"-1\",\n                onClick: (e)=>this.handleEmojiClick({\n                        e: e,\n                        emoji: emoji\n                    }),\n                onMouseEnter: ()=>this.handleEmojiOver(pos),\n                onMouseLeave: ()=>this.handleEmojiOver(),\n                style: {\n                    width: this.props.emojiButtonSize,\n                    height: this.props.emojiButtonSize,\n                    fontSize: this.props.emojiSize,\n                    lineHeight: 0\n                },\n                children: [\n                    /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                        \"aria-hidden\": \"true\",\n                        class: \"background\",\n                        style: {\n                            borderRadius: this.props.emojiButtonRadius,\n                            backgroundColor: this.props.emojiButtonColors ? this.props.emojiButtonColors[(posinset - 1) % this.props.emojiButtonColors.length] : undefined\n                        }\n                    }),\n                    /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)((0, $254755d3f438722f$export$2e2bcd8739ae039), {\n                        emoji: emoji,\n                        set: this.props.set,\n                        size: this.props.emojiSize,\n                        skin: skin,\n                        spritesheet: true,\n                        getSpritesheetURL: this.props.getSpritesheetURL\n                    })\n                ]\n            })\n        }, key);\n    }\n    renderSearch() {\n        const renderSkinTone = this.props.previewPosition == \"none\" || this.props.skinTonePosition == \"search\";\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    class: \"spacer\"\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    class: \"flex flex-middle\",\n                    children: [\n                        /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                            class: \"search relative flex-grow\",\n                            children: [\n                                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"input\", {\n                                    type: \"search\",\n                                    ref: this.refs.searchInput,\n                                    placeholder: (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).search,\n                                    onClick: this.handleSearchClick,\n                                    onInput: this.handleSearchInput,\n                                    onKeyDown: this.handleSearchKeyDown,\n                                    autoComplete: \"off\"\n                                }),\n                                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n                                    class: \"icon loupe flex\",\n                                    children: (0, $fcccfb36ed0cde68$export$2e2bcd8739ae039).search.loupe\n                                }),\n                                this.state.searchResults && /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"button\", {\n                                    title: \"Clear\",\n                                    \"aria-label\": \"Clear\",\n                                    type: \"button\",\n                                    class: \"icon delete flex\",\n                                    onClick: this.clearSearch,\n                                    onMouseDown: this.preventDefault,\n                                    children: (0, $fcccfb36ed0cde68$export$2e2bcd8739ae039).search.delete\n                                })\n                            ]\n                        }),\n                        renderSkinTone && this.renderSkinToneButton()\n                    ]\n                })\n            ]\n        });\n    }\n    renderSearchResults() {\n        const { searchResults: searchResults  } = this.state;\n        if (!searchResults) return null;\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n            class: \"category\",\n            ref: this.refs.search,\n            children: [\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    class: `sticky padding-small align-${this.dir[0]}`,\n                    children: (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).categories.search\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    children: !searchResults.length ? /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                        class: `padding-small align-${this.dir[0]}`,\n                        children: this.props.onAddCustomEmoji && /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"a\", {\n                            onClick: this.props.onAddCustomEmoji,\n                            children: (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).add_custom\n                        })\n                    }) : searchResults.map((row, i)=>{\n                        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                            class: \"flex\",\n                            children: row.map((emoji, ii)=>{\n                                return this.renderEmojiButton(emoji, {\n                                    pos: [\n                                        i,\n                                        ii\n                                    ],\n                                    posinset: i * this.props.perLine + ii + 1,\n                                    grid: searchResults\n                                });\n                            })\n                        });\n                    })\n                })\n            ]\n        });\n    }\n    renderCategories() {\n        const { categories: categories  } = (0, $7adb23b0109cc36a$export$2d0294657ab35f1b);\n        const hidden = !!this.state.searchResults;\n        const perLine = this.getPerLine();\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n            style: {\n                visibility: hidden ? \"hidden\" : undefined,\n                display: hidden ? \"none\" : undefined,\n                height: \"100%\"\n            },\n            children: categories.map((category)=>{\n                const { root: root , rows: rows  } = this.refs.categories.get(category.id);\n                return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    \"data-id\": category.target ? category.target.id : category.id,\n                    class: \"category\",\n                    ref: root,\n                    children: [\n                        /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                            class: `sticky padding-small align-${this.dir[0]}`,\n                            children: category.name || (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).categories[category.id]\n                        }),\n                        /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                            class: \"relative\",\n                            style: {\n                                height: rows.length * this.props.emojiButtonSize\n                            },\n                            children: rows.map((row, i)=>{\n                                const targetRow = row.index - row.index % $89bd6bb200cc8fef$var$Performance.rowsPerRender;\n                                const visible = this.state.visibleRows[targetRow];\n                                const ref = \"current\" in row ? row : undefined;\n                                if (!visible && !ref) return null;\n                                const start = i * perLine;\n                                const end = start + perLine;\n                                const emojiIds = category.emojis.slice(start, end);\n                                if (emojiIds.length < perLine) emojiIds.push(...new Array(perLine - emojiIds.length));\n                                return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                                    \"data-index\": row.index,\n                                    ref: ref,\n                                    class: \"flex row\",\n                                    style: {\n                                        top: i * this.props.emojiButtonSize\n                                    },\n                                    children: visible && emojiIds.map((emojiId, ii)=>{\n                                        if (!emojiId) return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                                            style: {\n                                                width: this.props.emojiButtonSize,\n                                                height: this.props.emojiButtonSize\n                                            }\n                                        });\n                                        const emoji = (0, $c4d155af13ad4d4b$export$2e2bcd8739ae039).get(emojiId);\n                                        return this.renderEmojiButton(emoji, {\n                                            pos: [\n                                                row.index,\n                                                ii\n                                            ],\n                                            posinset: row.posinset + ii,\n                                            grid: this.grid\n                                        });\n                                    })\n                                }, row.index);\n                            })\n                        })\n                    ]\n                });\n            })\n        });\n    }\n    renderSkinToneButton() {\n        if (this.props.skinTonePosition == \"none\") return null;\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n            class: \"flex flex-auto flex-center flex-middle\",\n            style: {\n                position: \"relative\",\n                width: this.props.emojiButtonSize,\n                height: this.props.emojiButtonSize\n            },\n            children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"button\", {\n                type: \"button\",\n                ref: this.refs.skinToneButton,\n                class: \"skin-tone-button flex flex-auto flex-center flex-middle\",\n                \"aria-selected\": this.state.showSkins ? \"\" : undefined,\n                \"aria-label\": (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).skins.choose,\n                title: (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).skins.choose,\n                onClick: this.openSkins,\n                style: {\n                    width: this.props.emojiSize,\n                    height: this.props.emojiSize\n                },\n                children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n                    class: `skin-tone skin-tone-${this.state.skin}`\n                })\n            })\n        });\n    }\n    renderLiveRegion() {\n        const emoji = this.getEmojiByPos(this.state.pos);\n        const contents = emoji ? emoji.name : \"\";\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n            \"aria-live\": \"polite\",\n            class: \"sr-only\",\n            children: contents\n        });\n    }\n    renderSkins() {\n        const skinToneButton = this.refs.skinToneButton.current;\n        const skinToneButtonRect = skinToneButton.getBoundingClientRect();\n        const baseRect = this.base.getBoundingClientRect();\n        const position = {};\n        if (this.dir == \"ltr\") position.right = baseRect.right - skinToneButtonRect.right - 3;\n        else position.left = skinToneButtonRect.left - baseRect.left - 3;\n        if (this.props.previewPosition == \"bottom\" && this.props.skinTonePosition == \"preview\") position.bottom = baseRect.bottom - skinToneButtonRect.top + 6;\n        else {\n            position.top = skinToneButtonRect.bottom - baseRect.top + 3;\n            position.bottom = \"auto\";\n        }\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n            ref: this.refs.menu,\n            role: \"radiogroup\",\n            dir: this.dir,\n            \"aria-label\": (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).skins.choose,\n            class: \"menu hidden\",\n            \"data-position\": position.top ? \"top\" : \"bottom\",\n            style: position,\n            children: [\n                ...Array(6).keys()\n            ].map((i)=>{\n                const skin = i + 1;\n                const checked = this.state.skin == skin;\n                return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"input\", {\n                            type: \"radio\",\n                            name: \"skin-tone\",\n                            value: skin,\n                            \"aria-label\": (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).skins[skin],\n                            ref: checked ? this.refs.skinToneRadio : null,\n                            defaultChecked: checked,\n                            onChange: ()=>this.handleSkinMouseOver(skin),\n                            onKeyDown: (e)=>{\n                                if (e.code == \"Enter\" || e.code == \"Space\" || e.code == \"Tab\") {\n                                    e.preventDefault();\n                                    this.handleSkinClick(skin);\n                                }\n                            }\n                        }),\n                        /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"button\", {\n                            \"aria-hidden\": \"true\",\n                            tabindex: \"-1\",\n                            onClick: ()=>this.handleSkinClick(skin),\n                            onMouseEnter: ()=>this.handleSkinMouseOver(skin),\n                            onMouseLeave: ()=>this.handleSkinMouseOver(),\n                            class: \"option flex flex-grow flex-middle\",\n                            children: [\n                                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n                                    class: `skin-tone skin-tone-${skin}`\n                                }),\n                                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"span\", {\n                                    class: \"margin-small-lr\",\n                                    children: (0, $7adb23b0109cc36a$export$dbe3113d60765c1a).skins[skin]\n                                })\n                            ]\n                        })\n                    ]\n                });\n            })\n        });\n    }\n    render() {\n        const lineWidth = this.props.perLine * this.props.emojiButtonSize;\n        return /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"section\", {\n            id: \"root\",\n            class: \"flex flex-column\",\n            dir: this.dir,\n            style: {\n                width: this.props.dynamicWidth ? \"100%\" : `calc(${lineWidth}px + (var(--padding) + var(--sidebar-width)))`\n            },\n            \"data-emoji-set\": this.props.set,\n            \"data-theme\": this.state.theme,\n            \"data-menu\": this.state.showSkins ? \"\" : undefined,\n            children: [\n                this.props.previewPosition == \"top\" && this.renderPreview(),\n                this.props.navPosition == \"top\" && this.renderNav(),\n                this.props.searchPosition == \"sticky\" && /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    class: \"padding-lr\",\n                    children: this.renderSearch()\n                }),\n                /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                    ref: this.refs.scroll,\n                    class: \"scroll flex-grow padding-lr\",\n                    children: /*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)(\"div\", {\n                        style: {\n                            width: this.props.dynamicWidth ? \"100%\" : lineWidth,\n                            height: \"100%\"\n                        },\n                        children: [\n                            this.props.searchPosition == \"static\" && this.renderSearch(),\n                            this.renderSearchResults(),\n                            this.renderCategories()\n                        ]\n                    })\n                }),\n                this.props.navPosition == \"bottom\" && this.renderNav(),\n                this.props.previewPosition == \"bottom\" && this.renderPreview(),\n                this.state.showSkins && this.renderSkins(),\n                this.renderLiveRegion()\n            ]\n        });\n    }\n    constructor(props){\n        super();\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"darkMediaCallback\", ()=>{\n            if (this.props.theme != \"auto\") return;\n            this.setState({\n                theme: this.darkMedia.matches ? \"dark\" : \"light\"\n            });\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"handleClickOutside\", (e)=>{\n            const { element: element  } = this.props;\n            if (e.target != element) {\n                if (this.state.showSkins) this.closeSkins();\n                if (this.props.onClickOutside) this.props.onClickOutside(e);\n            }\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"handleBaseClick\", (e)=>{\n            if (!this.state.showSkins) return;\n            if (!e.target.closest(\".menu\")) {\n                e.preventDefault();\n                e.stopImmediatePropagation();\n                this.closeSkins();\n            }\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"handleBaseKeydown\", (e)=>{\n            if (!this.state.showSkins) return;\n            if (e.key == \"Escape\") {\n                e.preventDefault();\n                e.stopImmediatePropagation();\n                this.closeSkins();\n            }\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"handleSearchClick\", ()=>{\n            const emoji = this.getEmojiByPos(this.state.pos);\n            if (!emoji) return;\n            this.setState({\n                pos: [\n                    -1,\n                    -1\n                ]\n            });\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"handleSearchInput\", async ()=>{\n            const input = this.refs.searchInput.current;\n            if (!input) return;\n            const { value: value  } = input;\n            const searchResults = await (0, $c4d155af13ad4d4b$export$2e2bcd8739ae039).search(value);\n            const afterRender = ()=>{\n                if (!this.refs.scroll.current) return;\n                this.refs.scroll.current.scrollTop = 0;\n            };\n            if (!searchResults) return this.setState({\n                searchResults: searchResults,\n                pos: [\n                    -1,\n                    -1\n                ]\n            }, afterRender);\n            const pos = input.selectionStart == input.value.length ? [\n                0,\n                0\n            ] : [\n                -1,\n                -1\n            ];\n            const grid = [];\n            grid.setsize = searchResults.length;\n            let row = null;\n            for (let emoji of searchResults){\n                if (!grid.length || row.length == this.getPerLine()) {\n                    row = [];\n                    row.__categoryId = \"search\";\n                    row.__index = grid.length;\n                    grid.push(row);\n                }\n                row.push(emoji);\n            }\n            this.ignoreMouse();\n            this.setState({\n                searchResults: grid,\n                pos: pos\n            }, afterRender);\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"handleSearchKeyDown\", (e)=>{\n            // const specialKey = e.altKey || e.ctrlKey || e.metaKey\n            const input = e.currentTarget;\n            e.stopImmediatePropagation();\n            switch(e.key){\n                case \"ArrowLeft\":\n                    // if (specialKey) return\n                    // e.preventDefault()\n                    this.navigate({\n                        e: e,\n                        input: input,\n                        left: true\n                    });\n                    break;\n                case \"ArrowRight\":\n                    // if (specialKey) return\n                    // e.preventDefault()\n                    this.navigate({\n                        e: e,\n                        input: input,\n                        right: true\n                    });\n                    break;\n                case \"ArrowUp\":\n                    // if (specialKey) return\n                    // e.preventDefault()\n                    this.navigate({\n                        e: e,\n                        input: input,\n                        up: true\n                    });\n                    break;\n                case \"ArrowDown\":\n                    // if (specialKey) return\n                    // e.preventDefault()\n                    this.navigate({\n                        e: e,\n                        input: input,\n                        down: true\n                    });\n                    break;\n                case \"Enter\":\n                    e.preventDefault();\n                    this.handleEmojiClick({\n                        e: e,\n                        pos: this.state.pos\n                    });\n                    break;\n                case \"Escape\":\n                    e.preventDefault();\n                    if (this.state.searchResults) this.clearSearch();\n                    else this.unfocusSearch();\n                    break;\n                default:\n                    break;\n            }\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"clearSearch\", ()=>{\n            const input = this.refs.searchInput.current;\n            if (!input) return;\n            input.value = \"\";\n            input.focus();\n            this.handleSearchInput();\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"handleCategoryClick\", ({ category: category , i: i  })=>{\n            this.scrollTo(i == 0 ? {\n                row: -1\n            } : {\n                categoryId: category.id\n            });\n        });\n        (0, $c770c458706daa72$export$2e2bcd8739ae039)(this, \"openSkins\", (e)=>{\n            const { currentTarget: currentTarget  } = e;\n            const rect = currentTarget.getBoundingClientRect();\n            this.setState({\n                showSkins: rect\n            }, async ()=>{\n                // Firefox requires 2 frames for the transition to consistenly work\n                await (0, $693b183b0a78708f$export$e772c8ff12451969)(2);\n                const menu = this.refs.menu.current;\n                if (!menu) return;\n                menu.classList.remove(\"hidden\");\n                this.refs.skinToneRadio.current.focus();\n                this.base.addEventListener(\"click\", this.handleBaseClick, true);\n                this.base.addEventListener(\"keydown\", this.handleBaseKeydown, true);\n            });\n        });\n        this.observers = [];\n        this.state = {\n            pos: [\n                -1,\n                -1\n            ],\n            perLine: this.initDynamicPerLine(props),\n            visibleRows: {\n                0: true\n            },\n            ...this.getInitialState(props)\n        };\n    }\n}\n\n\n\n\n\n\n\n\n\nclass $efa000751917694d$export$2e2bcd8739ae039 extends (0, $26f27c338a96b1a6$export$2e2bcd8739ae039) {\n    async connectedCallback() {\n        const props = (0, $7adb23b0109cc36a$export$75fe5f91d452f94b)(this.props, (0, $b247ea80b67298d5$export$2e2bcd8739ae039), this);\n        props.element = this;\n        props.ref = (component)=>{\n            this.component = component;\n        };\n        await (0, $7adb23b0109cc36a$export$2cd8252107eb640b)(props);\n        if (this.disconnected) return;\n        (0, $fb96b826c0c5f37a$export$b3890eb0ae9dca99)(/*#__PURE__*/ (0, $bd9dd35321b03dd4$export$34b9dba7ce09269b)((0, $89bd6bb200cc8fef$export$2e2bcd8739ae039), {\n            ...props\n        }), this.shadowRoot);\n    }\n    constructor(props){\n        super(props, {\n            styles: (0, (/*@__PURE__*/$parcel$interopDefault($329d53ba9fd7125f$exports)))\n        });\n    }\n}\n(0, $c770c458706daa72$export$2e2bcd8739ae039)($efa000751917694d$export$2e2bcd8739ae039, \"Props\", (0, $b247ea80b67298d5$export$2e2bcd8739ae039));\nif (typeof customElements !== \"undefined\" && !customElements.get(\"em-emoji-picker\")) customElements.define(\"em-emoji-picker\", $efa000751917694d$export$2e2bcd8739ae039);\n\n\nvar $329d53ba9fd7125f$exports = {};\n$329d53ba9fd7125f$exports = \":host {\\n  width: min-content;\\n  height: 435px;\\n  min-height: 230px;\\n  border-radius: var(--border-radius);\\n  box-shadow: var(--shadow);\\n  --border-radius: 10px;\\n  --category-icon-size: 18px;\\n  --font-family: -apple-system, BlinkMacSystemFont, \\\"Helvetica Neue\\\", sans-serif;\\n  --font-size: 15px;\\n  --preview-placeholder-size: 21px;\\n  --preview-title-size: 1.1em;\\n  --preview-subtitle-size: .9em;\\n  --shadow-color: 0deg 0% 0%;\\n  --shadow: .3px .5px 2.7px hsl(var(--shadow-color) / .14), .4px .8px 1px -3.2px hsl(var(--shadow-color) / .14), 1px 2px 2.5px -4.5px hsl(var(--shadow-color) / .14);\\n  display: flex;\\n}\\n\\n[data-theme=\\\"light\\\"] {\\n  --em-rgb-color: var(--rgb-color, 34, 36, 39);\\n  --em-rgb-accent: var(--rgb-accent, 34, 102, 237);\\n  --em-rgb-background: var(--rgb-background, 255, 255, 255);\\n  --em-rgb-input: var(--rgb-input, 255, 255, 255);\\n  --em-color-border: var(--color-border, rgba(0, 0, 0, .05));\\n  --em-color-border-over: var(--color-border-over, rgba(0, 0, 0, .1));\\n}\\n\\n[data-theme=\\\"dark\\\"] {\\n  --em-rgb-color: var(--rgb-color, 222, 222, 221);\\n  --em-rgb-accent: var(--rgb-accent, 58, 130, 247);\\n  --em-rgb-background: var(--rgb-background, 21, 22, 23);\\n  --em-rgb-input: var(--rgb-input, 0, 0, 0);\\n  --em-color-border: var(--color-border, rgba(255, 255, 255, .1));\\n  --em-color-border-over: var(--color-border-over, rgba(255, 255, 255, .2));\\n}\\n\\n#root {\\n  --color-a: rgb(var(--em-rgb-color));\\n  --color-b: rgba(var(--em-rgb-color), .65);\\n  --color-c: rgba(var(--em-rgb-color), .45);\\n  --padding: 12px;\\n  --padding-small: calc(var(--padding) / 2);\\n  --sidebar-width: 16px;\\n  --duration: 225ms;\\n  --duration-fast: 125ms;\\n  --duration-instant: 50ms;\\n  --easing: cubic-bezier(.4, 0, .2, 1);\\n  width: 100%;\\n  text-align: left;\\n  border-radius: var(--border-radius);\\n  background-color: rgb(var(--em-rgb-background));\\n  position: relative;\\n}\\n\\n@media (prefers-reduced-motion) {\\n  #root {\\n    --duration: 0;\\n    --duration-fast: 0;\\n    --duration-instant: 0;\\n  }\\n}\\n\\n#root[data-menu] button {\\n  cursor: auto;\\n}\\n\\n#root[data-menu] .menu button {\\n  cursor: pointer;\\n}\\n\\n:host, #root, input, button {\\n  color: rgb(var(--em-rgb-color));\\n  font-family: var(--font-family);\\n  font-size: var(--font-size);\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n  line-height: normal;\\n}\\n\\n*, :before, :after {\\n  box-sizing: border-box;\\n  min-width: 0;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n.relative {\\n  position: relative;\\n}\\n\\n.flex {\\n  display: flex;\\n}\\n\\n.flex-auto {\\n  flex: none;\\n}\\n\\n.flex-center {\\n  justify-content: center;\\n}\\n\\n.flex-column {\\n  flex-direction: column;\\n}\\n\\n.flex-grow {\\n  flex: auto;\\n}\\n\\n.flex-middle {\\n  align-items: center;\\n}\\n\\n.flex-wrap {\\n  flex-wrap: wrap;\\n}\\n\\n.padding {\\n  padding: var(--padding);\\n}\\n\\n.padding-t {\\n  padding-top: var(--padding);\\n}\\n\\n.padding-lr {\\n  padding-left: var(--padding);\\n  padding-right: var(--padding);\\n}\\n\\n.padding-r {\\n  padding-right: var(--padding);\\n}\\n\\n.padding-small {\\n  padding: var(--padding-small);\\n}\\n\\n.padding-small-b {\\n  padding-bottom: var(--padding-small);\\n}\\n\\n.padding-small-lr {\\n  padding-left: var(--padding-small);\\n  padding-right: var(--padding-small);\\n}\\n\\n.margin {\\n  margin: var(--padding);\\n}\\n\\n.margin-r {\\n  margin-right: var(--padding);\\n}\\n\\n.margin-l {\\n  margin-left: var(--padding);\\n}\\n\\n.margin-small-l {\\n  margin-left: var(--padding-small);\\n}\\n\\n.margin-small-lr {\\n  margin-left: var(--padding-small);\\n  margin-right: var(--padding-small);\\n}\\n\\n.align-l {\\n  text-align: left;\\n}\\n\\n.align-r {\\n  text-align: right;\\n}\\n\\n.color-a {\\n  color: var(--color-a);\\n}\\n\\n.color-b {\\n  color: var(--color-b);\\n}\\n\\n.color-c {\\n  color: var(--color-c);\\n}\\n\\n.ellipsis {\\n  white-space: nowrap;\\n  max-width: 100%;\\n  width: auto;\\n  text-overflow: ellipsis;\\n  overflow: hidden;\\n}\\n\\n.sr-only {\\n  width: 1px;\\n  height: 1px;\\n  position: absolute;\\n  top: auto;\\n  left: -10000px;\\n  overflow: hidden;\\n}\\n\\na {\\n  cursor: pointer;\\n  color: rgb(var(--em-rgb-accent));\\n}\\n\\na:hover {\\n  text-decoration: underline;\\n}\\n\\n.spacer {\\n  height: 10px;\\n}\\n\\n[dir=\\\"rtl\\\"] .scroll {\\n  padding-left: 0;\\n  padding-right: var(--padding);\\n}\\n\\n.scroll {\\n  padding-right: 0;\\n  overflow-x: hidden;\\n  overflow-y: auto;\\n}\\n\\n.scroll::-webkit-scrollbar {\\n  width: var(--sidebar-width);\\n  height: var(--sidebar-width);\\n}\\n\\n.scroll::-webkit-scrollbar-track {\\n  border: 0;\\n}\\n\\n.scroll::-webkit-scrollbar-button {\\n  width: 0;\\n  height: 0;\\n  display: none;\\n}\\n\\n.scroll::-webkit-scrollbar-corner {\\n  background-color: rgba(0, 0, 0, 0);\\n}\\n\\n.scroll::-webkit-scrollbar-thumb {\\n  min-height: 20%;\\n  min-height: 65px;\\n  border: 4px solid rgb(var(--em-rgb-background));\\n  border-radius: 8px;\\n}\\n\\n.scroll::-webkit-scrollbar-thumb:hover {\\n  background-color: var(--em-color-border-over) !important;\\n}\\n\\n.scroll:hover::-webkit-scrollbar-thumb {\\n  background-color: var(--em-color-border);\\n}\\n\\n.sticky {\\n  z-index: 1;\\n  background-color: rgba(var(--em-rgb-background), .9);\\n  -webkit-backdrop-filter: blur(4px);\\n  backdrop-filter: blur(4px);\\n  font-weight: 500;\\n  position: sticky;\\n  top: -1px;\\n}\\n\\n[dir=\\\"rtl\\\"] .search input[type=\\\"search\\\"] {\\n  padding: 10px 2.2em 10px 2em;\\n}\\n\\n[dir=\\\"rtl\\\"] .search .loupe {\\n  left: auto;\\n  right: .7em;\\n}\\n\\n[dir=\\\"rtl\\\"] .search .delete {\\n  left: .7em;\\n  right: auto;\\n}\\n\\n.search {\\n  z-index: 2;\\n  position: relative;\\n}\\n\\n.search input, .search button {\\n  font-size: calc(var(--font-size)  - 1px);\\n}\\n\\n.search input[type=\\\"search\\\"] {\\n  width: 100%;\\n  background-color: var(--em-color-border);\\n  transition-duration: var(--duration);\\n  transition-property: background-color, box-shadow;\\n  transition-timing-function: var(--easing);\\n  border: 0;\\n  border-radius: 10px;\\n  outline: 0;\\n  padding: 10px 2em 10px 2.2em;\\n  display: block;\\n}\\n\\n.search input[type=\\\"search\\\"]::-ms-input-placeholder {\\n  color: inherit;\\n  opacity: .6;\\n}\\n\\n.search input[type=\\\"search\\\"]::placeholder {\\n  color: inherit;\\n  opacity: .6;\\n}\\n\\n.search input[type=\\\"search\\\"], .search input[type=\\\"search\\\"]::-webkit-search-decoration, .search input[type=\\\"search\\\"]::-webkit-search-cancel-button, .search input[type=\\\"search\\\"]::-webkit-search-results-button, .search input[type=\\\"search\\\"]::-webkit-search-results-decoration {\\n  -webkit-appearance: none;\\n  -ms-appearance: none;\\n  appearance: none;\\n}\\n\\n.search input[type=\\\"search\\\"]:focus {\\n  background-color: rgb(var(--em-rgb-input));\\n  box-shadow: inset 0 0 0 1px rgb(var(--em-rgb-accent)), 0 1px 3px rgba(65, 69, 73, .2);\\n}\\n\\n.search .icon {\\n  z-index: 1;\\n  color: rgba(var(--em-rgb-color), .7);\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n}\\n\\n.search .loupe {\\n  pointer-events: none;\\n  left: .7em;\\n}\\n\\n.search .delete {\\n  right: .7em;\\n}\\n\\nsvg {\\n  fill: currentColor;\\n  width: 1em;\\n  height: 1em;\\n}\\n\\nbutton {\\n  -webkit-appearance: none;\\n  -ms-appearance: none;\\n  appearance: none;\\n  cursor: pointer;\\n  color: currentColor;\\n  background-color: rgba(0, 0, 0, 0);\\n  border: 0;\\n}\\n\\n#nav {\\n  z-index: 2;\\n  padding-top: 12px;\\n  padding-bottom: 12px;\\n  padding-right: var(--sidebar-width);\\n  position: relative;\\n}\\n\\n#nav button {\\n  color: var(--color-b);\\n  transition: color var(--duration) var(--easing);\\n}\\n\\n#nav button:hover {\\n  color: var(--color-a);\\n}\\n\\n#nav svg, #nav img {\\n  width: var(--category-icon-size);\\n  height: var(--category-icon-size);\\n}\\n\\n#nav[dir=\\\"rtl\\\"] .bar {\\n  left: auto;\\n  right: 0;\\n}\\n\\n#nav .bar {\\n  width: 100%;\\n  height: 3px;\\n  background-color: rgb(var(--em-rgb-accent));\\n  transition: transform var(--duration) var(--easing);\\n  border-radius: 3px 3px 0 0;\\n  position: absolute;\\n  bottom: -12px;\\n  left: 0;\\n}\\n\\n#nav button[aria-selected] {\\n  color: rgb(var(--em-rgb-accent));\\n}\\n\\n#preview {\\n  z-index: 2;\\n  padding: calc(var(--padding)  + 4px) var(--padding);\\n  padding-right: var(--sidebar-width);\\n  position: relative;\\n}\\n\\n#preview .preview-placeholder {\\n  font-size: var(--preview-placeholder-size);\\n}\\n\\n#preview .preview-title {\\n  font-size: var(--preview-title-size);\\n}\\n\\n#preview .preview-subtitle {\\n  font-size: var(--preview-subtitle-size);\\n}\\n\\n#nav:before, #preview:before {\\n  content: \\\"\\\";\\n  height: 2px;\\n  position: absolute;\\n  left: 0;\\n  right: 0;\\n}\\n\\n#nav[data-position=\\\"top\\\"]:before, #preview[data-position=\\\"top\\\"]:before {\\n  background: linear-gradient(to bottom, var(--em-color-border), transparent);\\n  top: 100%;\\n}\\n\\n#nav[data-position=\\\"bottom\\\"]:before, #preview[data-position=\\\"bottom\\\"]:before {\\n  background: linear-gradient(to top, var(--em-color-border), transparent);\\n  bottom: 100%;\\n}\\n\\n.category:last-child {\\n  min-height: calc(100% + 1px);\\n}\\n\\n.category button {\\n  font-family: -apple-system, BlinkMacSystemFont, Helvetica Neue, sans-serif;\\n  position: relative;\\n}\\n\\n.category button > * {\\n  position: relative;\\n}\\n\\n.category button .background {\\n  opacity: 0;\\n  background-color: var(--em-color-border);\\n  transition: opacity var(--duration-fast) var(--easing) var(--duration-instant);\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n}\\n\\n.category button:hover .background {\\n  transition-duration: var(--duration-instant);\\n  transition-delay: 0s;\\n}\\n\\n.category button[aria-selected] .background {\\n  opacity: 1;\\n}\\n\\n.category button[data-keyboard] .background {\\n  transition: none;\\n}\\n\\n.row {\\n  width: 100%;\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n}\\n\\n.skin-tone-button {\\n  border: 1px solid rgba(0, 0, 0, 0);\\n  border-radius: 100%;\\n}\\n\\n.skin-tone-button:hover {\\n  border-color: var(--em-color-border);\\n}\\n\\n.skin-tone-button:active .skin-tone {\\n  transform: scale(.85) !important;\\n}\\n\\n.skin-tone-button .skin-tone {\\n  transition: transform var(--duration) var(--easing);\\n}\\n\\n.skin-tone-button[aria-selected] {\\n  background-color: var(--em-color-border);\\n  border-top-color: rgba(0, 0, 0, .05);\\n  border-bottom-color: rgba(0, 0, 0, 0);\\n  border-left-width: 0;\\n  border-right-width: 0;\\n}\\n\\n.skin-tone-button[aria-selected] .skin-tone {\\n  transform: scale(.9);\\n}\\n\\n.menu {\\n  z-index: 2;\\n  white-space: nowrap;\\n  border: 1px solid var(--em-color-border);\\n  background-color: rgba(var(--em-rgb-background), .9);\\n  -webkit-backdrop-filter: blur(4px);\\n  backdrop-filter: blur(4px);\\n  transition-property: opacity, transform;\\n  transition-duration: var(--duration);\\n  transition-timing-function: var(--easing);\\n  border-radius: 10px;\\n  padding: 4px;\\n  position: absolute;\\n  box-shadow: 1px 1px 5px rgba(0, 0, 0, .05);\\n}\\n\\n.menu.hidden {\\n  opacity: 0;\\n}\\n\\n.menu[data-position=\\\"bottom\\\"] {\\n  transform-origin: 100% 100%;\\n}\\n\\n.menu[data-position=\\\"bottom\\\"].hidden {\\n  transform: scale(.9)rotate(-3deg)translateY(5%);\\n}\\n\\n.menu[data-position=\\\"top\\\"] {\\n  transform-origin: 100% 0;\\n}\\n\\n.menu[data-position=\\\"top\\\"].hidden {\\n  transform: scale(.9)rotate(3deg)translateY(-5%);\\n}\\n\\n.menu input[type=\\\"radio\\\"] {\\n  clip: rect(0 0 0 0);\\n  width: 1px;\\n  height: 1px;\\n  border: 0;\\n  margin: 0;\\n  padding: 0;\\n  position: absolute;\\n  overflow: hidden;\\n}\\n\\n.menu input[type=\\\"radio\\\"]:checked + .option {\\n  box-shadow: 0 0 0 2px rgb(var(--em-rgb-accent));\\n}\\n\\n.option {\\n  width: 100%;\\n  border-radius: 6px;\\n  padding: 4px 6px;\\n}\\n\\n.option:hover {\\n  color: #fff;\\n  background-color: rgb(var(--em-rgb-accent));\\n}\\n\\n.skin-tone {\\n  width: 16px;\\n  height: 16px;\\n  border-radius: 100%;\\n  display: inline-block;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.skin-tone:after {\\n  content: \\\"\\\";\\n  mix-blend-mode: overlay;\\n  background: linear-gradient(rgba(255, 255, 255, .2), rgba(0, 0, 0, 0));\\n  border: 1px solid rgba(0, 0, 0, .8);\\n  border-radius: 100%;\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  box-shadow: inset 0 -2px 3px #000, inset 0 1px 2px #fff;\\n}\\n\\n.skin-tone-1 {\\n  background-color: #ffc93a;\\n}\\n\\n.skin-tone-2 {\\n  background-color: #ffdab7;\\n}\\n\\n.skin-tone-3 {\\n  background-color: #e7b98f;\\n}\\n\\n.skin-tone-4 {\\n  background-color: #c88c61;\\n}\\n\\n.skin-tone-5 {\\n  background-color: #a46134;\\n}\\n\\n.skin-tone-6 {\\n  background-color: #5d4437;\\n}\\n\\n[data-index] {\\n  justify-content: space-between;\\n}\\n\\n[data-emoji-set=\\\"twitter\\\"] .skin-tone:after {\\n  box-shadow: none;\\n  border-color: rgba(0, 0, 0, .5);\\n}\\n\\n[data-emoji-set=\\\"twitter\\\"] .skin-tone-1 {\\n  background-color: #fade72;\\n}\\n\\n[data-emoji-set=\\\"twitter\\\"] .skin-tone-2 {\\n  background-color: #f3dfd0;\\n}\\n\\n[data-emoji-set=\\\"twitter\\\"] .skin-tone-3 {\\n  background-color: #eed3a8;\\n}\\n\\n[data-emoji-set=\\\"twitter\\\"] .skin-tone-4 {\\n  background-color: #cfad8d;\\n}\\n\\n[data-emoji-set=\\\"twitter\\\"] .skin-tone-5 {\\n  background-color: #a8805d;\\n}\\n\\n[data-emoji-set=\\\"twitter\\\"] .skin-tone-6 {\\n  background-color: #765542;\\n}\\n\\n[data-emoji-set=\\\"google\\\"] .skin-tone:after {\\n  box-shadow: inset 0 0 2px 2px rgba(0, 0, 0, .4);\\n}\\n\\n[data-emoji-set=\\\"google\\\"] .skin-tone-1 {\\n  background-color: #f5c748;\\n}\\n\\n[data-emoji-set=\\\"google\\\"] .skin-tone-2 {\\n  background-color: #f1d5aa;\\n}\\n\\n[data-emoji-set=\\\"google\\\"] .skin-tone-3 {\\n  background-color: #d4b48d;\\n}\\n\\n[data-emoji-set=\\\"google\\\"] .skin-tone-4 {\\n  background-color: #aa876b;\\n}\\n\\n[data-emoji-set=\\\"google\\\"] .skin-tone-5 {\\n  background-color: #916544;\\n}\\n\\n[data-emoji-set=\\\"google\\\"] .skin-tone-6 {\\n  background-color: #61493f;\\n}\\n\\n[data-emoji-set=\\\"facebook\\\"] .skin-tone:after {\\n  border-color: rgba(0, 0, 0, .4);\\n  box-shadow: inset 0 -2px 3px #000, inset 0 1px 4px #fff;\\n}\\n\\n[data-emoji-set=\\\"facebook\\\"] .skin-tone-1 {\\n  background-color: #f5c748;\\n}\\n\\n[data-emoji-set=\\\"facebook\\\"] .skin-tone-2 {\\n  background-color: #f1d5aa;\\n}\\n\\n[data-emoji-set=\\\"facebook\\\"] .skin-tone-3 {\\n  background-color: #d4b48d;\\n}\\n\\n[data-emoji-set=\\\"facebook\\\"] .skin-tone-4 {\\n  background-color: #aa876b;\\n}\\n\\n[data-emoji-set=\\\"facebook\\\"] .skin-tone-5 {\\n  background-color: #916544;\\n}\\n\\n[data-emoji-set=\\\"facebook\\\"] .skin-tone-6 {\\n  background-color: #61493f;\\n}\\n\\n\";\n\n\n\n\n\n\n\n\n\n\n\n//# sourceMappingURL=module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/emoji-mart/dist/module.js\n"));

/***/ })

}]);