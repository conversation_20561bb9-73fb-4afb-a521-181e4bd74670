"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rope-sequence";
exports.ids = ["vendor-chunks/rope-sequence"];
exports.modules = {

/***/ "(ssr)/./node_modules/rope-sequence/dist/index.js":
/*!**************************************************!*\
  !*** ./node_modules/rope-sequence/dist/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar GOOD_LEAF_SIZE = 200;\n\n// :: class<T> A rope sequence is a persistent sequence data structure\n// that supports appending, prepending, and slicing without doing a\n// full copy. It is represented as a mostly-balanced tree.\nvar RopeSequence = function RopeSequence () {};\n\nRopeSequence.prototype.append = function append (other) {\n  if (!other.length) { return this }\n  other = RopeSequence.from(other);\n\n  return (!this.length && other) ||\n    (other.length < GOOD_LEAF_SIZE && this.leafAppend(other)) ||\n    (this.length < GOOD_LEAF_SIZE && other.leafPrepend(this)) ||\n    this.appendInner(other)\n};\n\n// :: (union<[T], RopeSequence<T>>) → RopeSequence<T>\n// Prepend an array or other rope to this one, returning a new rope.\nRopeSequence.prototype.prepend = function prepend (other) {\n  if (!other.length) { return this }\n  return RopeSequence.from(other).append(this)\n};\n\nRopeSequence.prototype.appendInner = function appendInner (other) {\n  return new Append(this, other)\n};\n\n// :: (?number, ?number) → RopeSequence<T>\n// Create a rope repesenting a sub-sequence of this rope.\nRopeSequence.prototype.slice = function slice (from, to) {\n    if ( from === void 0 ) from = 0;\n    if ( to === void 0 ) to = this.length;\n\n  if (from >= to) { return RopeSequence.empty }\n  return this.sliceInner(Math.max(0, from), Math.min(this.length, to))\n};\n\n// :: (number) → T\n// Retrieve the element at the given position from this rope.\nRopeSequence.prototype.get = function get (i) {\n  if (i < 0 || i >= this.length) { return undefined }\n  return this.getInner(i)\n};\n\n// :: ((element: T, index: number) → ?bool, ?number, ?number)\n// Call the given function for each element between the given\n// indices. This tends to be more efficient than looping over the\n// indices and calling `get`, because it doesn't have to descend the\n// tree for every element.\nRopeSequence.prototype.forEach = function forEach (f, from, to) {\n    if ( from === void 0 ) from = 0;\n    if ( to === void 0 ) to = this.length;\n\n  if (from <= to)\n    { this.forEachInner(f, from, to, 0); }\n  else\n    { this.forEachInvertedInner(f, from, to, 0); }\n};\n\n// :: ((element: T, index: number) → U, ?number, ?number) → [U]\n// Map the given functions over the elements of the rope, producing\n// a flat array.\nRopeSequence.prototype.map = function map (f, from, to) {\n    if ( from === void 0 ) from = 0;\n    if ( to === void 0 ) to = this.length;\n\n  var result = [];\n  this.forEach(function (elt, i) { return result.push(f(elt, i)); }, from, to);\n  return result\n};\n\n// :: (?union<[T], RopeSequence<T>>) → RopeSequence<T>\n// Create a rope representing the given array, or return the rope\n// itself if a rope was given.\nRopeSequence.from = function from (values) {\n  if (values instanceof RopeSequence) { return values }\n  return values && values.length ? new Leaf(values) : RopeSequence.empty\n};\n\nvar Leaf = /*@__PURE__*/(function (RopeSequence) {\n  function Leaf(values) {\n    RopeSequence.call(this);\n    this.values = values;\n  }\n\n  if ( RopeSequence ) Leaf.__proto__ = RopeSequence;\n  Leaf.prototype = Object.create( RopeSequence && RopeSequence.prototype );\n  Leaf.prototype.constructor = Leaf;\n\n  var prototypeAccessors = { length: { configurable: true },depth: { configurable: true } };\n\n  Leaf.prototype.flatten = function flatten () {\n    return this.values\n  };\n\n  Leaf.prototype.sliceInner = function sliceInner (from, to) {\n    if (from == 0 && to == this.length) { return this }\n    return new Leaf(this.values.slice(from, to))\n  };\n\n  Leaf.prototype.getInner = function getInner (i) {\n    return this.values[i]\n  };\n\n  Leaf.prototype.forEachInner = function forEachInner (f, from, to, start) {\n    for (var i = from; i < to; i++)\n      { if (f(this.values[i], start + i) === false) { return false } }\n  };\n\n  Leaf.prototype.forEachInvertedInner = function forEachInvertedInner (f, from, to, start) {\n    for (var i = from - 1; i >= to; i--)\n      { if (f(this.values[i], start + i) === false) { return false } }\n  };\n\n  Leaf.prototype.leafAppend = function leafAppend (other) {\n    if (this.length + other.length <= GOOD_LEAF_SIZE)\n      { return new Leaf(this.values.concat(other.flatten())) }\n  };\n\n  Leaf.prototype.leafPrepend = function leafPrepend (other) {\n    if (this.length + other.length <= GOOD_LEAF_SIZE)\n      { return new Leaf(other.flatten().concat(this.values)) }\n  };\n\n  prototypeAccessors.length.get = function () { return this.values.length };\n\n  prototypeAccessors.depth.get = function () { return 0 };\n\n  Object.defineProperties( Leaf.prototype, prototypeAccessors );\n\n  return Leaf;\n}(RopeSequence));\n\n// :: RopeSequence\n// The empty rope sequence.\nRopeSequence.empty = new Leaf([]);\n\nvar Append = /*@__PURE__*/(function (RopeSequence) {\n  function Append(left, right) {\n    RopeSequence.call(this);\n    this.left = left;\n    this.right = right;\n    this.length = left.length + right.length;\n    this.depth = Math.max(left.depth, right.depth) + 1;\n  }\n\n  if ( RopeSequence ) Append.__proto__ = RopeSequence;\n  Append.prototype = Object.create( RopeSequence && RopeSequence.prototype );\n  Append.prototype.constructor = Append;\n\n  Append.prototype.flatten = function flatten () {\n    return this.left.flatten().concat(this.right.flatten())\n  };\n\n  Append.prototype.getInner = function getInner (i) {\n    return i < this.left.length ? this.left.get(i) : this.right.get(i - this.left.length)\n  };\n\n  Append.prototype.forEachInner = function forEachInner (f, from, to, start) {\n    var leftLen = this.left.length;\n    if (from < leftLen &&\n        this.left.forEachInner(f, from, Math.min(to, leftLen), start) === false)\n      { return false }\n    if (to > leftLen &&\n        this.right.forEachInner(f, Math.max(from - leftLen, 0), Math.min(this.length, to) - leftLen, start + leftLen) === false)\n      { return false }\n  };\n\n  Append.prototype.forEachInvertedInner = function forEachInvertedInner (f, from, to, start) {\n    var leftLen = this.left.length;\n    if (from > leftLen &&\n        this.right.forEachInvertedInner(f, from - leftLen, Math.max(to, leftLen) - leftLen, start + leftLen) === false)\n      { return false }\n    if (to < leftLen &&\n        this.left.forEachInvertedInner(f, Math.min(from, leftLen), to, start) === false)\n      { return false }\n  };\n\n  Append.prototype.sliceInner = function sliceInner (from, to) {\n    if (from == 0 && to == this.length) { return this }\n    var leftLen = this.left.length;\n    if (to <= leftLen) { return this.left.slice(from, to) }\n    if (from >= leftLen) { return this.right.slice(from - leftLen, to - leftLen) }\n    return this.left.slice(from, leftLen).append(this.right.slice(0, to - leftLen))\n  };\n\n  Append.prototype.leafAppend = function leafAppend (other) {\n    var inner = this.right.leafAppend(other);\n    if (inner) { return new Append(this.left, inner) }\n  };\n\n  Append.prototype.leafPrepend = function leafPrepend (other) {\n    var inner = this.left.leafPrepend(other);\n    if (inner) { return new Append(inner, this.right) }\n  };\n\n  Append.prototype.appendInner = function appendInner (other) {\n    if (this.left.depth >= Math.max(this.right.depth, other.depth) + 1)\n      { return new Append(this.left, new Append(this.right, other)) }\n    return new Append(this, other)\n  };\n\n  return Append;\n}(RopeSequence));\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RopeSequence);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rope-sequence/dist/index.js\n");

/***/ })

};
;