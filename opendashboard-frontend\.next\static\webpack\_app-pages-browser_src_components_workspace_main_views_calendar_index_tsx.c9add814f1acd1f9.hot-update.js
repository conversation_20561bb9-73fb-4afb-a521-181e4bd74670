/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// import React, { useMemo } from 'react';\n// import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameDay, isToday } from 'date-fns';\n// import { cn } from '@/lib/utils';\n// import { Button } from '@/components/ui/button';\n// import { PlusIcon } from '@heroicons/react/24/outline';\n// import { ScrollArea } from '@/components/ui/scroll-area';\n// import { CalendarEvent } from '@/typings/page';\n// import { useMaybeRecord } from '@/providers/record';\n// import { CalendarEventItem } from './CalendarEventItem';\n// import { NoEvents } from './NoEvents';\n// import { CalendarSideCard } from './CalendarSideCard';\n// import { useDroppable } from '@dnd-kit/core';\n// interface MonthViewProps {\n//   selectedDate: Date;\n//   events: CalendarEvent[];\n//   selectedEvent: string | null;\n//   setSelectedEvent: (id: string) => void;\n//   setSelectedDate: (date: Date) => void;\n//   openAddEventForm: (date: Date) => void;\n//   canEditData: boolean;\n//   handleEventClick: (event: CalendarEvent) => void;\n//   activeDragData: any;\n// }\n// const DayCell = ({\n//   date,\n//   children,\n//   onClick,\n//   isCurrentMonth\n// }: {\n//   date: Date;\n//   children: React.ReactNode;\n//   onClick: () => void;\n//   isCurrentMonth: boolean;\n// }) => {\n//   const { setNodeRef, isOver } = useDroppable({\n//     id: `daycell-${format(date, 'yyyy-MM-dd')}`,\n//     data: {\n//       date: date,\n//       type: 'daycell'\n//     }\n//   });\n//   return (\n//     <div\n//       ref={setNodeRef}\n//       onClick={onClick}\n//       className={cn(\n//         \"border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[120px] lg:p-3 lg:min-h-[140px]\",\n//         isCurrentMonth\n//           ? \"bg-white hover:bg-neutral-50\"\n//           : \"bg-neutral-100 hover:bg-neutral-200\",\n//         isOver && \"bg-blue-50 border-blue-200\"\n//       )}\n//     >\n//       {children}\n//     </div>\n//   );\n// };\n// // New helper function to process events for the entire month\n// const useMonthEvents = (weeks: Date[][], events: CalendarEvent[]) => {\n//   return useMemo(() => {\n//     const positionedEventsByWeek = new Map<number, any[]>();\n//     const allEventsByDay = new Map<string, any[]>();\n//     weeks.forEach((week, weekIndex) => {\n//       const weekStart = week[0];\n//       const weekEnd = week[6];\n//       const weekEvents = events.filter(event => {\n//         const eventStart = new Date(event.start);\n//         const eventEnd = new Date(event.end);\n//         return eventStart <= weekEnd && eventEnd >= weekStart;\n//       });\n//       const spanningEvents: any[] = [];\n//       weekEvents.forEach(event => {\n//         const eventStart = new Date(event.start);\n//         const eventEnd = new Date(event.end);\n//         const startDayIndex = week.findIndex(day => isSameDay(day, eventStart));\n//         const endDayIndex = week.findIndex(day => isSameDay(day, eventEnd));\n//         const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || (eventStart < weekStart && eventEnd > weekEnd);\n//         if (eventSpansWeek) {\n//           const start = startDayIndex !== -1 ? startDayIndex : 0;\n//           const end = endDayIndex !== -1 ? endDayIndex : 6;\n//           spanningEvents.push({\n//             event,\n//             startDayIndex: start,\n//             endDayIndex: end,\n//             colSpan: end - start + 1,\n//           });\n//         }\n//       });\n//       const positioned: any[] = [];\n//       const rows: any[][] = [];\n//       const sortedEvents = spanningEvents.sort((a, b) => a.startDayIndex - b.startDayIndex || b.colSpan - a.colSpan);\n//       sortedEvents.forEach(event => {\n//         let assigned = false;\n//         for (let i = 0; i < rows.length; i++) {\n//           const row = rows[i];\n//           if (!row.some(e => event.startDayIndex <= e.endDayIndex && event.endDayIndex >= e.startDayIndex)) {\n//             row.push(event);\n//             positioned.push({ ...event, row: i });\n//             assigned = true;\n//             break;\n//           }\n//         }\n//         if (!assigned) {\n//           rows.push([event]);\n//           positioned.push({ ...event, row: rows.length - 1 });\n//         }\n//       });\n//       positionedEventsByWeek.set(weekIndex, positioned);\n//       // Track all events that appear in each day cell\n//       week.forEach((day, dayIndex) => {\n//         const dayKey = format(day, 'yyyy-MM-dd');\n//         const dayEvents: any[] = [];\n//         positioned.forEach(pe => {\n//           // Include events that start on this day\n//           if (pe.startDayIndex === dayIndex) {\n//             dayEvents.push({\n//               ...pe,\n//               showTitle: true, // Show title on start day\n//               isDraggable: true // Draggable on start day\n//             });\n//           }\n//           // Include events that continue on this day (multi-day events)\n//           else if (pe.startDayIndex < dayIndex && pe.endDayIndex >= dayIndex) {\n//             // Check if this is the first day of a new week AND the event started in a previous week\n//             const isFirstDayOfWeek = dayIndex === 0;\n//             const eventStartedInPreviousWeek = pe.startDayIndex < 0; // Event started before this week\n//             dayEvents.push({\n//               ...pe,\n//               showTitle: isFirstDayOfWeek, // Show title only on first day of week\n//               isDraggable: false // Never draggable on continuation days\n//             });\n//           }\n//         });\n//         allEventsByDay.set(dayKey, dayEvents);\n//       });\n//     });\n//     return { positionedEventsByWeek, allEventsByDay };\n//   }, [weeks, events]);\n// };\n// export const MonthView: React.FC<MonthViewProps> = ({\n//   selectedDate,\n//   events,\n//   selectedEvent,\n//   setSelectedEvent,\n//   setSelectedDate,\n//   openAddEventForm,\n//   canEditData,\n//   handleEventClick,\n//   activeDragData,\n// }) => {\n//   const maybeRecord = useMaybeRecord();\n//   const isInRecordTab = !!maybeRecord;\n//   // Memoize month calculations\n//   const monthCalculations = useMemo(() => {\n//     const monthStart = startOfMonth(selectedDate);\n//     const monthEnd = endOfMonth(selectedDate);\n//     const startDay = startOfWeek(monthStart, { weekStartsOn: 0 });\n//     const endDay = endOfWeek(monthEnd, { weekStartsOn: 0 });\n//     const days = [];\n//     let day = startDay;\n//     while (day <= endDay) {\n//       days.push(day);\n//       day = addDays(day, 1);\n//     }\n//     const weeks = [];\n//     for (let i = 0; i < days.length; i += 7) {\n//       weeks.push(days.slice(i, i + 7));\n//     }\n//     return { monthStart, monthEnd, startDay, endDay, days, weeks };\n//   }, [selectedDate]);\n//   // Memoize month events\n//   const monthEvents = useMemo(() => \n//     events.filter(event => {\n//       const eventStart = new Date(event.start);\n//       return eventStart >= monthCalculations.startDay && \n//              eventStart <= monthCalculations.endDay;\n//     }), \n//     [events, monthCalculations.startDay, monthCalculations.endDay]\n//   );\n//   const { positionedEventsByWeek, allEventsByDay } = useMonthEvents(monthCalculations.weeks, events);\n//   // Render empty state when no events\n//   const renderEmptyState = () => (\n//     <div className=\"flex flex-col h-full bg-background\">\n//       <div className=\"grid grid-cols-7 border-b border-neutral-300 bg-white\">\n//         {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(dayName => (\n//           <div key={dayName} className={cn(\n//             \"text-center font-semibold text-black\",\n//             \"py-2 text-xs\"\n//           )}>\n//             {dayName.substring(0, 3)}\n//           </div>\n//         ))}\n//       </div>\n//       <NoEvents\n//         title=\"No events this month\"\n//         message={`${format(selectedDate, 'MMMM yyyy')} is completely free. Start planning your month!`}\n//         showCreateButton={canEditData}\n//         onCreate={() => openAddEventForm(selectedDate)}\n//       />\n//     </div>\n//   );\n//   // Render day cell content\n//   const renderDayCellContent = (day: Date, dayEvents: any[]) => {\n//     const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n//     const isCurrentDay = isToday(day);\n//     const MAX_VISIBLE_EVENTS = 4;\n//     const ROW_HEIGHT = 28;\n//     // Get all events for this specific day (including multi-day events that continue here)\n//     const dayKey = format(day, 'yyyy-MM-dd');\n//     const allDayEvents = allEventsByDay.get(dayKey) || [];\n//     // Sort events by row and limit to visible events\n//     const sortedEvents = allDayEvents.sort((a, b) => a.row - b.row);\n//     const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);\n//     const hasMore = sortedEvents.length > MAX_VISIBLE_EVENTS;\n//     // FIXED HEIGHT CALCULATION: Always use 5 rows (4 events + 1 \"+ more\" row)\n//     // This prevents cell expansion regardless of how many events there are\n//     const containerHeight = (MAX_VISIBLE_EVENTS + 1) * ROW_HEIGHT; // 5 * 28 = 140px\n//     return (\n//       <>\n//         <div className=\"flex items-center justify-between mb-2\">\n//           <span className={cn(\n//             \"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\",\n//             isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"\n//           )}>\n//             {format(day, 'd')}\n//           </span>\n//           {canEditData && isCurrentMonth && (\n//             <Button\n//               variant=\"ghost\"\n//               className=\"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\"\n//               onClick={(e) => {\n//                 e.stopPropagation();\n//                 setTimeout(() => openAddEventForm(day), 150);\n//               }}\n//             >\n//               <PlusIcon className=\"h-3 w-3 text-black\" />\n//             </Button>\n//           )}\n//         </div>\n//         <div className=\"relative\" style={{ height: `${containerHeight}px` }}>\n//           {visibleEvents.map(pe => (\n//             <div\n//               key={pe.event.id}\n//               className=\"absolute\"\n//               style={{\n//                 top: `${pe.row * ROW_HEIGHT}px`,\n//                 left: '2px',\n//                 width: pe.colSpan > 1 \n//                   ? `calc(${pe.colSpan * 100}% + ${(pe.colSpan - 1) * 19}px)`\n//                   : 'calc(100% - 4px)',\n//                 zIndex: 10 + pe.row,\n//               }}\n//             >\n//               <CalendarEventItem\n//                 event={pe.event}\n//                 view=\"month\"\n//                 onClick={(e) => {\n//                   e.stopPropagation();\n//                   setSelectedEvent(pe.event.id);\n//                   handleEventClick(pe.event);\n//                 }}\n//                 isDragging={activeDragData?.payload?.id === pe.event.id}\n//                 showTitle={pe.showTitle}\n//                 isDraggable={pe.isDraggable}\n//               />\n//             </div>\n//           ))}\n//           {hasMore && (\n//             <div \n//               className=\"text-black hover:text-black font-medium text-xs cursor-pointer\"\n//               style={{\n//                 position: 'absolute',\n//                 top: `${MAX_VISIBLE_EVENTS * ROW_HEIGHT}px`,\n//                 left: '2px',\n//               }}\n//               onClick={(e) => {\n//                 e.stopPropagation();\n//                 setSelectedDate(day);\n//               }}\n//             >\n//               + {sortedEvents.length - MAX_VISIBLE_EVENTS} more\n//             </div>\n//           )}\n//         </div>\n//       </>\n//     );\n//   };\n//   // Render main view\n//   return (\n//     <div className=\"h-full bg-background flex flex-col lg:flex-row\">\n//       <div className=\"flex-1 flex flex-col min-h-0\">\n//         {/* Day Headers */}\n//         <div className=\"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\">\n//           {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(dayName => (\n//             <div key={dayName} className={cn(\n//               \"text-center font-semibold text-black\",\n//               \"py-2 text-xs\"\n//             )}>\n//               {dayName.substring(0, 3)}\n//             </div>\n//           ))}\n//         </div>\n//         {/* Month Grid */}\n//         <ScrollArea className=\"flex-1\">\n//           <div className=\"grid grid-cols-7 border-neutral-300 border-b\">\n//             {monthCalculations.weeks.map((week, weekIndex) =>\n//               week.map((day, dayIndex) => {\n//                 const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n//                 return (\n//                   <DayCell\n//                     key={`${weekIndex}-${dayIndex}`}\n//                     date={day}\n//                     isCurrentMonth={isCurrentMonth}\n//                     onClick={() => setSelectedDate(day)}\n//                   >\n//                     {renderDayCellContent(day, [])}\n//                   </DayCell>\n//                 );\n//               }),\n//             )}\n//           </div>\n//         </ScrollArea>\n//       </div>\n//       <CalendarSideCard\n//         selectedDate={selectedDate}\n//         events={events}\n//         selectedEvent={selectedEvent}\n//         setSelectedEvent={setSelectedEvent}\n//         handleEventClick={handleEventClick}\n//       />\n//     </div>\n//   );\n// };\n// function isMultiDay(event: CalendarEvent): boolean {\n//   return !isSameDay(new Date(event.start), new Date(event.end));\n// }\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx":
/*!****************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/index.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarView: function() { return /* binding */ CalendarView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _providers_template__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/template */ \"(app-pages-browser)/./src/providers/template.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/workspace/main/views/table */ \"(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/providers/stackedPeek */ \"(app-pages-browser)/./src/providers/stackedPeek.tsx\");\n/* harmony import */ var _components_DayView__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./components/DayView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx\");\n/* harmony import */ var _components_WeekView__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./components/WeekView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\");\n/* harmony import */ var _components_MonthView__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./components/MonthView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\");\n/* harmony import */ var _components_MonthView__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(_components_MonthView__WEBPACK_IMPORTED_MODULE_21__);\n/* harmony import */ var _components_CalendarEventItem__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./components/CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/custom-ui/customSelect */ \"(app-pages-browser)/./src/components/custom-ui/customSelect.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./components/CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInMilliseconds/index.js\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInDays/index.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Custom modifier to restrict dragging to the calendar main content area only\nconst restrictToCalendarContainer = (param)=>{\n    let { transform, draggingNodeRect, windowRect } = param;\n    if (!draggingNodeRect || !windowRect) {\n        return transform;\n    }\n    // Find the calendar main content container (the div that contains the views)\n    const calendarContainer = document.querySelector('[data-calendar-content=\"true\"]');\n    if (!calendarContainer) {\n        return transform;\n    }\n    const containerRect = calendarContainer.getBoundingClientRect();\n    // Calculate the boundaries relative to the window\n    const minX = containerRect.left;\n    const maxX = containerRect.right - draggingNodeRect.width;\n    const minY = containerRect.top;\n    const maxY = containerRect.bottom - draggingNodeRect.height;\n    // Get current pointer position\n    const currentX = transform.x + draggingNodeRect.left;\n    const currentY = transform.y + draggingNodeRect.top;\n    // Constrain the position\n    const constrainedX = Math.min(Math.max(currentX, minX), maxX);\n    const constrainedY = Math.min(Math.max(currentY, minY), maxY);\n    return {\n        ...transform,\n        x: constrainedX - draggingNodeRect.left,\n        y: constrainedY - draggingNodeRect.top\n    };\n};\n// Custom hook to track previous value\nconst usePrevious = (value)=>{\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        ref.current = value;\n    });\n    return ref.current;\n};\n_s(usePrevious, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nconst CalendarView = (props)=>{\n    _s1();\n    const { databaseStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace)();\n    const { definition } = props;\n    const { accessLevel } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage)();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_16__.useScreenSize)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_17__.useMaybeRecord)();\n    const maybeShared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared)();\n    const maybeTemplate = (0,_providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate)();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [viewType, setViewType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"week\");\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSideCalendar, setShowSideCalendar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!isMobile);\n    const [activeDragData, setActiveDragData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const savedScrollTop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const pointerCoordinates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const isInRecordTab = !!maybeRecord;\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const databaseId = definition.databaseId;\n    const database = databaseStore[definition.databaseId];\n    const isPublishedView = !!maybeShared;\n    const editable = !definition.lockContent && !isPublishedView && !!accessLevel;\n    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    const { createRecords, updateRecordValues, setPeekRecordId, peekRecordId, refreshDatabase, deleteRecords } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews)();\n    const { sorts, filter, search } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering)();\n    const { selectedIds, setSelectedIds } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewSelection)();\n    const prevPeekRecordId = usePrevious(peekRecordId);\n    const { openRecord } = (0,_providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__.useStackedPeek)();\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.PointerSensor, {\n        activationConstraint: {\n            distance: 8\n        }\n    }), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.TouchSensor, {\n        activationConstraint: {\n            delay: 150,\n            tolerance: 5\n        }\n    }));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n        const container = document.getElementById(containerId);\n        if (container) {\n            requestAnimationFrame(()=>{\n                container.scrollTop = savedScrollTop.current;\n            });\n        }\n    }, [\n        selectedEvent,\n        viewType\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowSideCalendar(!isMobile);\n    }, [\n        isMobile\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only refresh if the peek view was open and is now closed.\n        if (prevPeekRecordId && !peekRecordId) {\n            console.log(\"Peek view was closed, refreshing calendar data\");\n            refreshDatabase(definition.databaseId);\n        }\n    }, [\n        peekRecordId,\n        prevPeekRecordId,\n        definition.databaseId,\n        refreshDatabase\n    ]);\n    if (!database) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__.PageLoader, {\n        size: \"full\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 175,\n        columnNumber: 25\n    }, undefined);\n    const getEvents = ()=>{\n        var _workspace_workspaceMember, _database_database;\n        if (!database) return [];\n        const { rows } = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.filterAndSortRecords)(database, members, databaseStore, definition.filter || {\n            match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All,\n            conditions: []\n        }, filter, sorts.length ? sorts : definition.sorts || [], (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspaceMember = workspace.workspaceMember) === null || _workspace_workspaceMember === void 0 ? void 0 : _workspace_workspaceMember.userId) || \"\", (database === null || database === void 0 ? void 0 : (_database_database = database.database) === null || _database_database === void 0 ? void 0 : _database_database.id) || \"\");\n        const filteredRows = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.searchFilteredRecords)(search || \"\", rows);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        return filteredRows.map((row)=>{\n            const startValue = row.processedRecord.processedRecordValues[definition.eventStartColumnId];\n            let startDate;\n            if (startValue && typeof startValue === \"string\") {\n                startDate = new Date(startValue);\n            } else {\n                startDate = new Date();\n            }\n            let endDate;\n            if (definition.eventEndColumnId) {\n                const endValue = row.processedRecord.processedRecordValues[definition.eventEndColumnId];\n                if (endValue && typeof endValue === \"string\") {\n                    endDate = new Date(endValue);\n                } else {\n                    endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n                }\n            } else {\n                endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n            }\n            const title = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getRecordTitle)(row.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts);\n            return {\n                id: row.id,\n                title,\n                start: startDate,\n                end: endDate,\n                record: row.record,\n                processedRecord: row.processedRecord\n            };\n        });\n    };\n    const getFilteredEvents = ()=>{\n        const baseEvents = getEvents();\n        if (!searchTerm.trim()) {\n            return baseEvents;\n        }\n        return baseEvents.filter((event)=>{\n            const searchLower = searchTerm.toLowerCase();\n            return event.title.toLowerCase().includes(searchLower);\n        });\n    };\n    const onDragStart = (event)=>{\n        if (event.active.data.current) {\n            var _event_active_rect_current_translated, _event_active_rect_current, _event_active_rect_current_translated1, _event_active_rect_current_translated2;\n            const initialPointerY = pointerCoordinates.current.y;\n            var _event_active_rect_current_translated_top;\n            const initialEventTop = (_event_active_rect_current_translated_top = (_event_active_rect_current = event.active.rect.current) === null || _event_active_rect_current === void 0 ? void 0 : (_event_active_rect_current_translated = _event_active_rect_current.translated) === null || _event_active_rect_current_translated === void 0 ? void 0 : _event_active_rect_current_translated.top) !== null && _event_active_rect_current_translated_top !== void 0 ? _event_active_rect_current_translated_top : 0;\n            const grabOffsetY = initialPointerY - initialEventTop;\n            setActiveDragData({\n                ...event.active.data.current,\n                grabOffsetY,\n                width: (_event_active_rect_current_translated1 = event.active.rect.current.translated) === null || _event_active_rect_current_translated1 === void 0 ? void 0 : _event_active_rect_current_translated1.width,\n                height: (_event_active_rect_current_translated2 = event.active.rect.current.translated) === null || _event_active_rect_current_translated2 === void 0 ? void 0 : _event_active_rect_current_translated2.height\n            });\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"touchmove\", handleTouchMove);\n        }\n    };\n    const onDragEnd = async (param)=>{\n        let { active, over } = param;\n        document.removeEventListener(\"mousemove\", handleMouseMove);\n        document.removeEventListener(\"touchmove\", handleTouchMove);\n        setActiveDragData(null);\n        console.log(\"[DragEnd] Fired\", {\n            active,\n            over\n        });\n        if (!over || !active || !canEditData || active.id === over.id) {\n            console.log(\"[DragEnd] Exiting: Invalid drop conditions.\", {\n                over,\n                active,\n                canEditData\n            });\n            return;\n        }\n        const activeData = active.data.current;\n        const overData = over.data.current;\n        if (!activeData || !overData) {\n            console.log(\"[DragEnd] Exiting: Missing active or over data.\");\n            return;\n        }\n        console.log(\"[DragEnd] Active Data:\", activeData);\n        console.log(\"[DragEnd] Over Data:\", overData);\n        const { payload, type } = activeData;\n        const eventToUpdate = type === \"segment\" ? payload.originalEvent : payload;\n        console.log(\"[DragEnd] Event to Update:\", eventToUpdate);\n        const originalStart = new Date(eventToUpdate.start);\n        const originalEnd = new Date(eventToUpdate.end);\n        const duration = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_27__[\"default\"])(originalEnd, originalStart);\n        console.log(\"[DragEnd] Original Times:\", {\n            originalStart,\n            originalEnd,\n            duration\n        });\n        let newStart;\n        if (overData.type.startsWith(\"allday\")) {\n            console.log(\"[DragEnd] Logic branch: All-day\");\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(originalStart, dayDifference);\n            // For all-day drops, normalize the time to the start of the day\n            newStart.setHours(0, 0, 0, 0);\n        } else if (overData.type.startsWith(\"timeslot\")) {\n            console.log(\"[DragEnd] Logic branch: Timeslot\");\n            newStart = new Date(overData.date);\n            // Enhanced Minute-level precision calculation\n            const grabOffsetY = activeData.grabOffsetY || 0;\n            const slotTop = over.rect.top;\n            const pointerY = pointerCoordinates.current.y;\n            // Adjust pointer position based on initial grab point\n            const adjustedPointerY = pointerY - grabOffsetY;\n            const offsetY = adjustedPointerY - slotTop;\n            const slotHeight = over.rect.height; // Should be 60px\n            const minutesFromTop = offsetY / slotHeight * 60;\n            // Snap to nearest 5-minute interval\n            const snappedMinutes = Math.round(minutesFromTop / 5) * 5;\n            const newMinutes = Math.max(0, Math.min(59, snappedMinutes));\n            newStart.setHours(overData.hour, newMinutes, originalStart.getSeconds(), originalStart.getMilliseconds());\n            console.log(\"[DragEnd] Minute Precision Calc:\", {\n                slotTop,\n                pointerY,\n                offsetY,\n                slotHeight,\n                minutesFromTop,\n                newMinutes\n            });\n        } else if (overData.type === \"daycell\") {\n            console.log(\"[DragEnd] Logic branch: Day Cell (Month)\");\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(originalStart, dayDifference);\n        } else {\n            console.log(\"[DragEnd] Exiting: Invalid drop target type.\");\n            return; // Invalid drop target\n        }\n        const newEnd = new Date(newStart.getTime() + duration);\n        console.log(\"[DragEnd] New Times:\", {\n            newStart,\n            newEnd\n        });\n        const recordId = eventToUpdate.record.id;\n        const newValues = {\n            [definition.eventStartColumnId]: newStart.toISOString(),\n            ...definition.eventEndColumnId && {\n                [definition.eventEndColumnId]: newEnd.toISOString()\n            }\n        };\n        console.log(\"[DragEnd] Update Payload:\", {\n            recordId,\n            newValues\n        });\n        try {\n            console.log(\"[DragEnd] Calling updateRecordValues...\");\n            await updateRecordValues(definition.databaseId, [\n                recordId\n            ], newValues);\n            console.log(\"[DragEnd] Update successful.\");\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.success('Event \"'.concat(eventToUpdate.title, '\" updated.'));\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to update event.\");\n            console.error(\"[DragEnd] Error updating event:\", error);\n        }\n    };\n    const handleMouseMove = (event)=>{\n        pointerCoordinates.current = {\n            x: event.clientX,\n            y: event.clientY\n        };\n    };\n    const handleTouchMove = (event)=>{\n        const touch = event.touches[0];\n        pointerCoordinates.current = {\n            x: touch.clientX,\n            y: touch.clientY\n        };\n    };\n    const events = getFilteredEvents();\n    const goToToday = ()=>setSelectedDate(new Date());\n    const goToPrevious = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(prevDate, -1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const goToNext = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(prevDate, 1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const handleRequestCreateEvent = function(date) {\n        let useSystemTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (useSystemTime) {\n            const now = new Date();\n            const newDate = new Date(date);\n            newDate.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());\n            handleCreateEvent(newDate);\n        } else {\n            handleCreateEvent(date);\n        }\n    };\n    const handleCreateEvent = async function() {\n        let date = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : new Date();\n        if (!canEditData) return;\n        const startTime = new Date(date);\n        const endTime = new Date(startTime.getTime() + (definition.defaultDuration || 30) * 60000);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        try {\n            const recordValues = {\n                [definition.eventStartColumnId]: startTime.toISOString(),\n                ...definition.eventEndColumnId && {\n                    [definition.eventEndColumnId]: endTime.toISOString()\n                }\n            };\n            if (titleColOpts.titleColId) {\n                recordValues[titleColOpts.titleColId] = \"New Event\";\n            }\n            const result = await createRecords(definition.databaseId, [\n                recordValues\n            ]);\n            if (result && result.records && result.records.length > 0) {\n                const newRecordId = result.records[0].id;\n                if (newRecordId) {\n                    await refreshDatabase(definition.databaseId);\n                    setPeekRecordId(newRecordId);\n                    sonner__WEBPACK_IMPORTED_MODULE_14__.toast.success(\"New event created\");\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Error accessing the new event\");\n                }\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to create event properly\");\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to create event\");\n        }\n    };\n    const handleEventClick = (event)=>{\n        if (event && event.id) {\n            const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n            const container = document.getElementById(containerId);\n            if (container) {\n                savedScrollTop.current = container.scrollTop;\n            }\n            openRecord(event.id, event.record.databaseId);\n            setSelectedEvent(event.id);\n        }\n    };\n    const getHeaderDateDisplay = ()=>{\n        switch(viewType){\n            case \"day\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n            case \"week\":\n                return \"\".concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(selectedDate, -selectedDate.getDay()), \"MMM d\"), \" - \").concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(selectedDate, 6 - selectedDate.getDay()), \"MMM d, yyyy\"));\n            case \"month\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM yyyy\");\n            default:\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n        }\n    };\n    const handleEventDelete = async (event)=>{\n        if (!canEditData) return;\n        try {\n            await deleteRecords(database.database.id, [\n                event.record.id\n            ]);\n        } catch (error) {\n            console.error(\"Error deleting event:\", error);\n        }\n    };\n    const viewTypeOptions = [\n        {\n            id: \"day\",\n            value: \"day\",\n            title: \"Day\",\n            data: \"day\"\n        },\n        {\n            id: \"week\",\n            value: \"week\",\n            title: \"Week\",\n            data: \"week\"\n        },\n        {\n            id: \"month\",\n            value: \"month\",\n            title: \"Month\",\n            data: \"month\"\n        }\n    ];\n    const selectedViewOption = viewType === \"day\" ? [\n        \"day\"\n    ] : viewType === \"week\" ? [\n        \"week\"\n    ] : [\n        \"month\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"border-b border-neutral-300 bg-white\", isInRecordTab && \"py-1\"),\n                children: [\n                    isMobile ? /* Mobile Header Layout */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"p-2\", isInRecordTab && \"py-1\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black truncate flex-1 mr-2\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(!showSideCalendar),\n                                        className: \"rounded-full h-8 px-3 text-xs text-black hover:bg-gray-50\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 12\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToToday,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: \"Today\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToPrevious,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleLeftIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 18\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToNext,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleRightIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 18\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                                options: viewTypeOptions,\n                                                selectedIds: selectedViewOption,\n                                                onChange: (selected)=>{\n                                                    if (selected.length > 0) {\n                                                        setViewType(selected[0]);\n                                                    }\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black w-20\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                placeholder: \"View\",\n                                                hideSearch: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 550,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 20\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 18\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 12\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 10\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"flex items-center justify-between px-4\", isInRecordTab ? \"py-1\" : \"py-2\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: goToToday,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToPrevious,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleLeftIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToNext,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleRightIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 14\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 585,\n                                columnNumber: 10\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                placeholder: \"Search events...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-48 pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                                className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                        options: viewTypeOptions,\n                                        selectedIds: selectedViewOption,\n                                        onChange: (selected)=>{\n                                            if (selected.length > 0) {\n                                                setViewType(selected[0]);\n                                            }\n                                        },\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6 w-20\" : \"h-8 w-28\"),\n                                        placeholder: \"View\",\n                                        hideSearch: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 637,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black gap-1\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Add Event\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 35\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 624,\n                                columnNumber: 10\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 581,\n                        columnNumber: 8\n                    }, undefined),\n                    isMobile && !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                    placeholder: \"Search events...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"w-full pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 672,\n                                    columnNumber: 12\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                    className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 678,\n                                    columnNumber: 12\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                            lineNumber: 671,\n                            columnNumber: 10\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 670,\n                        columnNumber: 8\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 492,\n                columnNumber: 6\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex min-h-0\",\n                children: [\n                    showSideCalendar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"flex-none bg-white\", isMobile ? \"w-full absolute z-50 backdrop-blur-sm h-full shadow-lg\" : \"w-fit border-r border-neutral-300\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 border-b border-neutral-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 10\n                                    }, undefined),\n                                    isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(false),\n                                        className: \"rounded-full h-8 w-8 p-1 text-black hover:bg-neutral-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Close\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            \"\\xd7\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 691,\n                                columnNumber: 8\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                mode: \"single\",\n                                selected: selectedDate,\n                                onSelect: (date)=>{\n                                    if (date) {\n                                        setSelectedDate(date);\n                                        if (isMobile) {\n                                            setShowSideCalendar(false);\n                                        }\n                                    }\n                                },\n                                className: \"rounded-md border-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 704,\n                                columnNumber: 8\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 687,\n                        columnNumber: 6\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DndContext, {\n                        sensors: sensors,\n                        onDragStart: onDragStart,\n                        onDragEnd: onDragEnd,\n                        modifiers: [\n                            restrictToCalendarContainer\n                        ],\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                \"data-calendar-content\": \"true\",\n                                children: [\n                                    viewType === \"day\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DayView__WEBPACK_IMPORTED_MODULE_19__.DayView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 728,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"week\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WeekView__WEBPACK_IMPORTED_MODULE_20__.WeekView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 741,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"month\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MonthView__WEBPACK_IMPORTED_MODULE_21__.MonthView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: (date)=>handleRequestCreateEvent(date, true),\n                                        canEditData: canEditData,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 755,\n                                        columnNumber: 7\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 726,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DragOverlay, {\n                                dropAnimation: null,\n                                children: activeDragData && activeDragData.type === \"segment\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_26__.CalendarEventSegment, {\n                                    segment: activeDragData.payload,\n                                    view: viewType === \"day\" ? \"day\" : \"week\",\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 771,\n                                    columnNumber: 11\n                                }, undefined) : activeDragData && activeDragData.type === \"event\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CalendarEventItem__WEBPACK_IMPORTED_MODULE_22__.CalendarEventItem, {\n                                    event: activeDragData.payload,\n                                    view: \"month\",\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 781,\n                                    columnNumber: 11\n                                }, undefined) : null\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 769,\n                                columnNumber: 6\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 720,\n                        columnNumber: 6\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 685,\n                columnNumber: 4\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 491,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CalendarView, \"gC+3ORgbOSOWj17lR2zBDE1vWrs=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace,\n        _providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage,\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_16__.useScreenSize,\n        _providers_record__WEBPACK_IMPORTED_MODULE_17__.useMaybeRecord,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared,\n        _providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewSelection,\n        usePrevious,\n        _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__.useStackedPeek,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors\n    ];\n});\n_c = CalendarView;\nvar _c;\n$RefreshReg$(_c, \"CalendarView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL3ZpZXdzL2NhbGVuZGFyL2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUF3RTtBQUNuQjtBQUN5QjtBQUNuQjtBQUNoQjtBQUlTO0FBQ0k7QUFDeUI7QUFDcUI7QUFDbEQ7QUFDSjtBQUVxRTtBQUN5QztBQUNoSDtBQUNmO0FBQ0U7QUFFc0I7QUFDSDtBQUNLO0FBR1Y7QUFDRTtBQUNFO0FBQ2dCO0FBQ29EO0FBSXBEO0FBVzVDO0FBR2tEO0FBQ0g7QUFFdEUsOEVBQThFO0FBQzlFLE1BQU1rRCw4QkFBOEI7UUFBQyxFQUFFQyxTQUFTLEVBQUVDLGdCQUFnQixFQUFFQyxVQUFVLEVBQU87SUFDbkYsSUFBSSxDQUFDRCxvQkFBb0IsQ0FBQ0MsWUFBWTtRQUNwQyxPQUFPRjtJQUNUO0lBRUEsNkVBQTZFO0lBQzdFLE1BQU1HLG9CQUFvQkMsU0FBU0MsYUFBYSxDQUFDO0lBQ2pELElBQUksQ0FBQ0YsbUJBQW1CO1FBQ3RCLE9BQU9IO0lBQ1Q7SUFFQSxNQUFNTSxnQkFBZ0JILGtCQUFrQkkscUJBQXFCO0lBRTdELGtEQUFrRDtJQUNsRCxNQUFNQyxPQUFPRixjQUFjRyxJQUFJO0lBQy9CLE1BQU1DLE9BQU9KLGNBQWNLLEtBQUssR0FBR1YsaUJBQWlCVyxLQUFLO0lBQ3pELE1BQU1DLE9BQU9QLGNBQWNRLEdBQUc7SUFDOUIsTUFBTUMsT0FBT1QsY0FBY1UsTUFBTSxHQUFHZixpQkFBaUJnQixNQUFNO0lBRTNELCtCQUErQjtJQUMvQixNQUFNQyxXQUFXbEIsVUFBVW1CLENBQUMsR0FBR2xCLGlCQUFpQlEsSUFBSTtJQUNwRCxNQUFNVyxXQUFXcEIsVUFBVXFCLENBQUMsR0FBR3BCLGlCQUFpQmEsR0FBRztJQUVuRCx5QkFBeUI7SUFDekIsTUFBTVEsZUFBZUMsS0FBS0MsR0FBRyxDQUFDRCxLQUFLRSxHQUFHLENBQUNQLFVBQVVWLE9BQU9FO0lBQ3hELE1BQU1nQixlQUFlSCxLQUFLQyxHQUFHLENBQUNELEtBQUtFLEdBQUcsQ0FBQ0wsVUFBVVAsT0FBT0U7SUFFeEQsT0FBTztRQUNMLEdBQUdmLFNBQVM7UUFDWm1CLEdBQUdHLGVBQWVyQixpQkFBaUJRLElBQUk7UUFDdkNZLEdBQUdLLGVBQWV6QixpQkFBaUJhLEdBQUc7SUFDeEM7QUFDRjtBQUlBLHNDQUFzQztBQUN0QyxNQUFNYSxjQUFjLENBQUtDOztJQUN2QixNQUFNQyxNQUFNOUUsNkNBQU1BO0lBQ2xCQyxnREFBU0EsQ0FBQztRQUNSNkUsSUFBSUMsT0FBTyxHQUFHRjtJQUNoQjtJQUNBLE9BQU9DLElBQUlDLE9BQU87QUFDcEI7R0FOTUg7QUFRQyxNQUFNSSxlQUFlLENBQUNDOztJQUMzQixNQUFNLEVBQUVDLGFBQWEsRUFBRUMsT0FBTyxFQUFFQyxTQUFTLEVBQUUsR0FBR2xGLGtFQUFZQTtJQUMxRCxNQUFNLEVBQUVtRixVQUFVLEVBQUUsR0FBR0o7SUFDdkIsTUFBTSxFQUFFSyxXQUFXLEVBQUUsR0FBR2pGLHdEQUFPQTtJQUMvQixNQUFNLEVBQUVrRixRQUFRLEVBQUUsR0FBRzFELHFFQUFhQTtJQUNsQyxNQUFNMkQsY0FBYzFELGtFQUFjQTtJQUVsQyxNQUFNMkQsY0FBY25GLGlFQUFjQTtJQUNsQyxNQUFNb0YsZ0JBQWdCbkYscUVBQWdCQTtJQUV0QyxNQUFNLENBQUNvRixjQUFjQyxnQkFBZ0IsR0FBRzdGLCtDQUFRQSxDQUFPLElBQUk4RjtJQUMzRCxNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR2hHLCtDQUFRQSxDQUFtQjtJQUMzRCxNQUFNLENBQUNpRyxlQUFlQyxpQkFBaUIsR0FBR2xHLCtDQUFRQSxDQUFnQjtJQUNsRSxNQUFNLENBQUNtRyxZQUFZQyxjQUFjLEdBQUdwRywrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNxRyxrQkFBa0JDLG9CQUFvQixHQUFHdEcsK0NBQVFBLENBQUMsQ0FBQ3dGO0lBQzFELE1BQU0sQ0FBQ2UsZ0JBQWdCQyxrQkFBa0IsR0FBR3hHLCtDQUFRQSxDQUFhO0lBQ2pFLE1BQU15RyxpQkFBaUJ4Ryw2Q0FBTUEsQ0FBQztJQUM5QixNQUFNeUcscUJBQXFCekcsNkNBQU1BLENBQUM7UUFBRW9FLEdBQUc7UUFBR0UsR0FBRztJQUFFO0lBRS9DLE1BQU1vQyxnQkFBZ0IsQ0FBQyxDQUFDbEI7SUFFeEJILFdBQVdzQixNQUFNLEdBQUd0QixXQUFXc0IsTUFBTSxJQUFJO1FBQUVDLFlBQVksRUFBRTtRQUFFQyxPQUFPMUcscUVBQUtBLENBQUMyRyxHQUFHO0lBQUM7SUFDNUV6QixXQUFXMEIsS0FBSyxHQUFHMUIsV0FBVzBCLEtBQUssSUFBSSxFQUFFO0lBRXpDLE1BQU1DLGFBQWEzQixXQUFXMkIsVUFBVTtJQUN4QyxNQUFNQyxXQUFXL0IsYUFBYSxDQUFDRyxXQUFXMkIsVUFBVSxDQUFDO0lBRXJELE1BQU1FLGtCQUFrQixDQUFDLENBQUN6QjtJQUMxQixNQUFNMEIsV0FBVyxDQUFDOUIsV0FBVytCLFdBQVcsSUFBSSxDQUFDRixtQkFBbUIsQ0FBQyxDQUFDNUI7SUFFbEUsSUFBSStCLG1CQUFtQixDQUFDLENBQUUsRUFBQzNCLGlCQUFpQixDQUFDRCxlQUFlLENBQUN5QixtQkFBbUIsQ0FBQzdCLFdBQVcrQixXQUFXLElBQUk5QixlQUFlNkIsUUFBTztJQUNqSSxJQUFJRyxjQUFjLENBQUMsQ0FBRSxFQUFDNUIsaUJBQWlCLENBQUNELGVBQWUsQ0FBQ3lCLG1CQUFtQixDQUFDN0IsV0FBVytCLFdBQVcsSUFBSTlCLGVBQWU2QixRQUFPO0lBRXhILE1BQU0sRUFBRUksYUFBYSxFQUFFQyxrQkFBa0IsRUFBRUMsZUFBZSxFQUFFQyxZQUFZLEVBQUVDLGVBQWUsRUFBRUMsYUFBYSxFQUFFLEdBQUdwSCwwREFBUUE7SUFDdkgsTUFBTSxFQUFFdUcsS0FBSyxFQUFFSixNQUFNLEVBQUVrQixNQUFNLEVBQUUsR0FBR3BILGtFQUFnQkE7SUFDbEQsTUFBTSxFQUFFcUgsV0FBVyxFQUFFQyxjQUFjLEVBQUUsR0FBR3JILGtFQUFnQkE7SUFDMUQsTUFBTXNILG1CQUFtQnBELFlBQVk4QztJQUNyQyxNQUFNLEVBQUVPLFVBQVUsRUFBRSxHQUFHbEcsdUVBQWNBO0lBRXJDLE1BQU1tRyxVQUFVdEYsMERBQVVBLENBQ3hCRCx5REFBU0EsQ0FBQ0YseURBQWFBLEVBQUU7UUFDdkIwRixzQkFBc0I7WUFDcEJDLFVBQVU7UUFDWjtJQUNGLElBQ0F6Rix5REFBU0EsQ0FBQ0QsdURBQVdBLEVBQUU7UUFDckJ5RixzQkFBc0I7WUFDcEJFLE9BQU87WUFDUEMsV0FBVztRQUNiO0lBQ0Y7SUFHRnJJLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTXNJLGNBQWN6QyxhQUFhLFFBQVEsdUJBQXVCO1FBQ2hFLE1BQU0wQyxZQUFZbkYsU0FBU29GLGNBQWMsQ0FBQ0Y7UUFFMUMsSUFBSUMsV0FBVztZQUNiRSxzQkFBc0I7Z0JBQ3BCRixVQUFVRyxTQUFTLEdBQUduQyxlQUFlekIsT0FBTztZQUM5QztRQUNGO0lBQ0YsR0FBRztRQUFDaUI7UUFBZUY7S0FBUztJQUU1QjdGLGdEQUFTQSxDQUFDO1FBQ1JvRyxvQkFBb0IsQ0FBQ2Q7SUFDdkIsR0FBRztRQUFDQTtLQUFTO0lBR2J0RixnREFBU0EsQ0FBQztRQUNSLDREQUE0RDtRQUM1RCxJQUFJK0gsb0JBQW9CLENBQUNOLGNBQWM7WUFDckNrQixRQUFRQyxHQUFHLENBQUM7WUFDWmxCLGdCQUFnQnRDLFdBQVcyQixVQUFVO1FBQ3ZDO0lBQ0YsR0FBRztRQUFDVTtRQUFjTTtRQUFrQjNDLFdBQVcyQixVQUFVO1FBQUVXO0tBQWdCO0lBRTNFLElBQUksQ0FBQ1YsVUFBVSxxQkFBTyw4REFBQzdHLG9FQUFVQTtRQUFDMEksTUFBSzs7Ozs7O0lBRXZDLE1BQU1DLFlBQVk7WUFVZDNELDRCQUNBNkI7UUFWRixJQUFJLENBQUNBLFVBQVUsT0FBTyxFQUFFO1FBRXhCLE1BQU0sRUFBRStCLElBQUksRUFBRSxHQUFHckksNEZBQW9CQSxDQUNuQ3NHLFVBQ0E5QixTQUNBRCxlQUNBRyxXQUFXc0IsTUFBTSxJQUFJO1lBQUVFLE9BQU8xRyxxRUFBS0EsQ0FBQzJHLEdBQUc7WUFBRUYsWUFBWSxFQUFFO1FBQUMsR0FDeERELFFBQ0FJLE1BQU1rQyxNQUFNLEdBQUdsQyxRQUFTMUIsV0FBVzBCLEtBQUssSUFBSSxFQUFFLEVBQzlDM0IsQ0FBQUEsc0JBQUFBLGlDQUFBQSw2QkFBQUEsVUFBVzhELGVBQWUsY0FBMUI5RCxpREFBQUEsMkJBQTRCK0QsTUFBTSxLQUFJLElBQ3RDbEMsQ0FBQUEscUJBQUFBLGdDQUFBQSxxQkFBQUEsU0FBVUEsUUFBUSxjQUFsQkEseUNBQUFBLG1CQUFvQm1DLEVBQUUsS0FBSTtRQUc1QixNQUFNQyxlQUFlekksNkZBQXFCQSxDQUFDaUgsVUFBVSxJQUFJbUI7UUFDekQsTUFBTU0sZUFBZWxILHFIQUFtQkEsQ0FBQzZFLFNBQVNBLFFBQVE7UUFFMUQsT0FBT29DLGFBQWFFLEdBQUcsQ0FBQ0MsQ0FBQUE7WUFDdEIsTUFBTUMsYUFBYUQsSUFBSUUsZUFBZSxDQUFDQyxxQkFBcUIsQ0FBQ3RFLFdBQVd1RSxrQkFBa0IsQ0FBQztZQUMzRixJQUFJQztZQUVKLElBQUlKLGNBQWMsT0FBT0EsZUFBZSxVQUFVO2dCQUNoREksWUFBWSxJQUFJaEUsS0FBSzREO1lBQ3ZCLE9BQU87Z0JBQ0xJLFlBQVksSUFBSWhFO1lBQ2xCO1lBRUEsSUFBSWlFO1lBQ0osSUFBSXpFLFdBQVcwRSxnQkFBZ0IsRUFBRTtnQkFDL0IsTUFBTUMsV0FBV1IsSUFBSUUsZUFBZSxDQUFDQyxxQkFBcUIsQ0FBQ3RFLFdBQVcwRSxnQkFBZ0IsQ0FBQztnQkFDdkYsSUFBSUMsWUFBWSxPQUFPQSxhQUFhLFVBQVU7b0JBQzVDRixVQUFVLElBQUlqRSxLQUFLbUU7Z0JBQ3JCLE9BQU87b0JBQ0xGLFVBQVUsSUFBSWpFLEtBQUtnRSxVQUFVSSxPQUFPLEtBQUssQ0FBQzVFLFdBQVc2RSxlQUFlLElBQUksRUFBQyxJQUFLO2dCQUNoRjtZQUNGLE9BQU87Z0JBQ0xKLFVBQVUsSUFBSWpFLEtBQUtnRSxVQUFVSSxPQUFPLEtBQUssQ0FBQzVFLFdBQVc2RSxlQUFlLElBQUksRUFBQyxJQUFLO1lBQ2hGO1lBRUEsTUFBTUMsUUFBUTlILGdIQUFjQSxDQUMxQm1ILElBQUlZLE1BQU0sRUFDVmQsYUFBYWUsVUFBVSxFQUN2QmYsYUFBYWdCLFlBQVksRUFDekJoQixhQUFhaUIsVUFBVTtZQUd6QixPQUFPO2dCQUNMbkIsSUFBSUksSUFBSUosRUFBRTtnQkFDVmU7Z0JBQ0FLLE9BQU9YO2dCQUNQWSxLQUFLWDtnQkFDTE0sUUFBUVosSUFBSVksTUFBTTtnQkFDbEJWLGlCQUFpQkYsSUFBSUUsZUFBZTtZQUN0QztRQUNGO0lBQ0Y7SUFFQSxNQUFNZ0Isb0JBQW9CO1FBQ3hCLE1BQU1DLGFBQWE1QjtRQUVuQixJQUFJLENBQUM3QyxXQUFXMEUsSUFBSSxJQUFJO1lBQ3RCLE9BQU9EO1FBQ1Q7UUFFQSxPQUFPQSxXQUFXaEUsTUFBTSxDQUFDa0UsQ0FBQUE7WUFDdkIsTUFBTUMsY0FBYzVFLFdBQVc2RSxXQUFXO1lBQzFDLE9BQU9GLE1BQU1WLEtBQUssQ0FBQ1ksV0FBVyxHQUFHQyxRQUFRLENBQUNGO1FBQzVDO0lBQ0Y7SUFFQSxNQUFNRyxjQUFjLENBQUNKO1FBQ25CLElBQUlBLE1BQU1LLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDcEcsT0FBTyxFQUFFO2dCQUVMOEYsdUNBQUFBLDRCQU1mQSx3Q0FDQ0E7WUFSVixNQUFNTyxrQkFBa0IzRSxtQkFBbUIxQixPQUFPLENBQUNULENBQUM7Z0JBQzVCdUc7WUFBeEIsTUFBTVEsa0JBQWtCUixDQUFBQSw2Q0FBQUEsNkJBQUFBLE1BQU1LLE1BQU0sQ0FBQ0ksSUFBSSxDQUFDdkcsT0FBTyxjQUF6QjhGLGtEQUFBQSx3Q0FBQUEsMkJBQTJCVSxVQUFVLGNBQXJDViw0REFBQUEsc0NBQXVDOUcsR0FBRyxjQUExQzhHLHVEQUFBQSw0Q0FBOEM7WUFDdEUsTUFBTVcsY0FBY0osa0JBQWtCQztZQUV0QzlFLGtCQUFrQjtnQkFDaEIsR0FBR3NFLE1BQU1LLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDcEcsT0FBTztnQkFDNUJ5RztnQkFDQTNILEtBQUssR0FBRWdILHlDQUFBQSxNQUFNSyxNQUFNLENBQUNJLElBQUksQ0FBQ3ZHLE9BQU8sQ0FBQ3dHLFVBQVUsY0FBcENWLDZEQUFBQSx1Q0FBc0NoSCxLQUFLO2dCQUNsREssTUFBTSxHQUFFMkcseUNBQUFBLE1BQU1LLE1BQU0sQ0FBQ0ksSUFBSSxDQUFDdkcsT0FBTyxDQUFDd0csVUFBVSxjQUFwQ1YsNkRBQUFBLHVDQUFzQzNHLE1BQU07WUFDdEQ7WUFFQWIsU0FBU29JLGdCQUFnQixDQUFDLGFBQWFDO1lBQ3ZDckksU0FBU29JLGdCQUFnQixDQUFDLGFBQWFFO1FBQ3pDO0lBQ0Y7SUFFQSxNQUFNQyxZQUFZO1lBQU8sRUFBRVYsTUFBTSxFQUFFVyxJQUFJLEVBQXlDO1FBQzlFeEksU0FBU3lJLG1CQUFtQixDQUFDLGFBQWFKO1FBQzFDckksU0FBU3lJLG1CQUFtQixDQUFDLGFBQWFIO1FBQzFDcEYsa0JBQWtCO1FBRWxCcUMsUUFBUUMsR0FBRyxDQUFDLG1CQUFtQjtZQUFFcUM7WUFBUVc7UUFBSztRQUU5QyxJQUFJLENBQUNBLFFBQVEsQ0FBQ1gsVUFBVSxDQUFDNUQsZUFBZTRELE9BQU85QixFQUFFLEtBQUt5QyxLQUFLekMsRUFBRSxFQUFFO1lBQzdEUixRQUFRQyxHQUFHLENBQUMsK0NBQStDO2dCQUFFZ0Q7Z0JBQU1YO2dCQUFRNUQ7WUFBWTtZQUN2RjtRQUNGO1FBRUEsTUFBTXlFLGFBQWFiLE9BQU9DLElBQUksQ0FBQ3BHLE9BQU87UUFDdEMsTUFBTWlILFdBQVdILEtBQUtWLElBQUksQ0FBQ3BHLE9BQU87UUFFbEMsSUFBSSxDQUFDZ0gsY0FBYyxDQUFDQyxVQUFVO1lBQzVCcEQsUUFBUUMsR0FBRyxDQUFDO1lBQ1o7UUFDRjtRQUVBRCxRQUFRQyxHQUFHLENBQUMsMEJBQTBCa0Q7UUFDdENuRCxRQUFRQyxHQUFHLENBQUMsd0JBQXdCbUQ7UUFFcEMsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLElBQUksRUFBRSxHQUFHSDtRQUMxQixNQUFNSSxnQkFBK0JELFNBQVMsWUFBWUQsUUFBUUcsYUFBYSxHQUFHSDtRQUNsRnJELFFBQVFDLEdBQUcsQ0FBQyw4QkFBOEJzRDtRQUUxQyxNQUFNRSxnQkFBZ0IsSUFBSXhHLEtBQUtzRyxjQUFjM0IsS0FBSztRQUNsRCxNQUFNOEIsY0FBYyxJQUFJekcsS0FBS3NHLGNBQWMxQixHQUFHO1FBQzlDLE1BQU04QixXQUFXeEosc0hBQXdCQSxDQUFDdUosYUFBYUQ7UUFDdkR6RCxRQUFRQyxHQUFHLENBQUMsNkJBQTZCO1lBQUV3RDtZQUFlQztZQUFhQztRQUFTO1FBRWhGLElBQUlDO1FBRUosSUFBSVIsU0FBU0UsSUFBSSxDQUFDTyxVQUFVLENBQUMsV0FBVztZQUN0QzdELFFBQVFDLEdBQUcsQ0FBQztZQUNaLE1BQU02RCxnQkFBZ0I1SixzSEFBZ0JBLENBQUNrSixTQUFTVyxJQUFJLEVBQUVsTCw0SUFBVUEsQ0FBQzRLO1lBQ2pFRyxXQUFXcEwsNElBQU9BLENBQUNpTCxlQUFlSztZQUNsQyxnRUFBZ0U7WUFDaEVGLFNBQVNJLFFBQVEsQ0FBQyxHQUFHLEdBQUcsR0FBRztRQUM3QixPQUFPLElBQUlaLFNBQVNFLElBQUksQ0FBQ08sVUFBVSxDQUFDLGFBQWE7WUFDL0M3RCxRQUFRQyxHQUFHLENBQUM7WUFDWjJELFdBQVcsSUFBSTNHLEtBQUttRyxTQUFTVyxJQUFJO1lBRWpDLDhDQUE4QztZQUM5QyxNQUFNbkIsY0FBY08sV0FBV1AsV0FBVyxJQUFJO1lBQzlDLE1BQU1xQixVQUFVaEIsS0FBS1AsSUFBSSxDQUFDdkgsR0FBRztZQUM3QixNQUFNK0ksV0FBV3JHLG1CQUFtQjFCLE9BQU8sQ0FBQ1QsQ0FBQztZQUU3QyxzREFBc0Q7WUFDdEQsTUFBTXlJLG1CQUFtQkQsV0FBV3RCO1lBRXBDLE1BQU13QixVQUFVRCxtQkFBbUJGO1lBQ25DLE1BQU1JLGFBQWFwQixLQUFLUCxJQUFJLENBQUNwSCxNQUFNLEVBQUUsaUJBQWlCO1lBQ3RELE1BQU1nSixpQkFBaUIsVUFBV0QsYUFBYztZQUVoRCxvQ0FBb0M7WUFDcEMsTUFBTUUsaUJBQWlCM0ksS0FBSzRJLEtBQUssQ0FBQ0YsaUJBQWlCLEtBQUs7WUFDeEQsTUFBTUcsYUFBYTdJLEtBQUtFLEdBQUcsQ0FBQyxHQUFHRixLQUFLQyxHQUFHLENBQUMsSUFBSTBJO1lBRTVDWCxTQUFTSSxRQUFRLENBQUNaLFNBQVNzQixJQUFJLEVBQUVELFlBQVloQixjQUFja0IsVUFBVSxJQUFJbEIsY0FBY21CLGVBQWU7WUFDdEc1RSxRQUFRQyxHQUFHLENBQUMsb0NBQW9DO2dCQUFFZ0U7Z0JBQVNDO2dCQUFVRTtnQkFBU0M7Z0JBQVlDO2dCQUFnQkc7WUFBVztRQUN2SCxPQUFPLElBQUlyQixTQUFTRSxJQUFJLEtBQUssV0FBVztZQUN0Q3RELFFBQVFDLEdBQUcsQ0FBQztZQUNaLE1BQU02RCxnQkFBZ0I1SixzSEFBZ0JBLENBQUNrSixTQUFTVyxJQUFJLEVBQUVsTCw0SUFBVUEsQ0FBQzRLO1lBQ2pFRyxXQUFXcEwsNElBQU9BLENBQUNpTCxlQUFlSztRQUNwQyxPQUFPO1lBQ0w5RCxRQUFRQyxHQUFHLENBQUM7WUFDWixRQUFRLHNCQUFzQjtRQUNoQztRQUVBLE1BQU00RSxTQUFTLElBQUk1SCxLQUFLMkcsU0FBU3ZDLE9BQU8sS0FBS3NDO1FBQzdDM0QsUUFBUUMsR0FBRyxDQUFDLHdCQUF3QjtZQUFFMkQ7WUFBVWlCO1FBQU87UUFFdkQsTUFBTUMsV0FBV3ZCLGNBQWMvQixNQUFNLENBQUNoQixFQUFFO1FBQ3hDLE1BQU11RSxZQUFZO1lBQ2hCLENBQUN0SSxXQUFXdUUsa0JBQWtCLENBQUMsRUFBRTRDLFNBQVNvQixXQUFXO1lBQ3JELEdBQUl2SSxXQUFXMEUsZ0JBQWdCLElBQUk7Z0JBQUUsQ0FBQzFFLFdBQVcwRSxnQkFBZ0IsQ0FBQyxFQUFFMEQsT0FBT0csV0FBVztZQUFHLENBQUM7UUFDNUY7UUFDQWhGLFFBQVFDLEdBQUcsQ0FBQyw2QkFBNkI7WUFBRTZFO1lBQVVDO1FBQVU7UUFFL0QsSUFBSTtZQUNGL0UsUUFBUUMsR0FBRyxDQUFDO1lBQ1osTUFBTXJCLG1CQUFtQm5DLFdBQVcyQixVQUFVLEVBQUU7Z0JBQUMwRzthQUFTLEVBQUVDO1lBQzVEL0UsUUFBUUMsR0FBRyxDQUFDO1lBQ1psSCwwQ0FBS0EsQ0FBQ2tNLE9BQU8sQ0FBQyxVQUE4QixPQUFwQjFCLGNBQWNoQyxLQUFLLEVBQUM7UUFDOUMsRUFBRSxPQUFPMkQsT0FBTztZQUNkbk0sMENBQUtBLENBQUNtTSxLQUFLLENBQUM7WUFDWmxGLFFBQVFrRixLQUFLLENBQUMsbUNBQW1DQTtRQUNuRDtJQUNGO0lBRUEsTUFBTXBDLGtCQUFrQixDQUFDYjtRQUN2QnBFLG1CQUFtQjFCLE9BQU8sR0FBRztZQUFFWCxHQUFHeUcsTUFBTWtELE9BQU87WUFBRXpKLEdBQUd1RyxNQUFNbUQsT0FBTztRQUFDO0lBQ3BFO0lBQ0EsTUFBTXJDLGtCQUFrQixDQUFDZDtRQUNyQixNQUFNb0QsUUFBUXBELE1BQU1xRCxPQUFPLENBQUMsRUFBRTtRQUM5QnpILG1CQUFtQjFCLE9BQU8sR0FBRztZQUFFWCxHQUFHNkosTUFBTUYsT0FBTztZQUFFekosR0FBRzJKLE1BQU1ELE9BQU87UUFBQztJQUN0RTtJQUdBLE1BQU1HLFNBQVN6RDtJQUVmLE1BQU0wRCxZQUFZLElBQU14SSxnQkFBZ0IsSUFBSUM7SUFFNUMsTUFBTXdJLGVBQWU7UUFDbkIsT0FBUXZJO1lBQ04sS0FBSztnQkFDSEYsZ0JBQWdCMEksQ0FBQUEsV0FBWWxOLDRJQUFPQSxDQUFDa04sVUFBVSxDQUFDO2dCQUMvQztZQUNGLEtBQUs7Z0JBQ0gxSSxnQkFBZ0IwSSxDQUFBQSxXQUFZOU0sNElBQVFBLENBQUM4TSxVQUFVO2dCQUMvQztZQUNGLEtBQUs7Z0JBQ0gxSSxnQkFBZ0IwSSxDQUFBQSxXQUFZaE4sNElBQVNBLENBQUNnTixVQUFVO2dCQUNoRDtRQUNKO0lBQ0Y7SUFFQSxNQUFNQyxXQUFXO1FBQ2YsT0FBUXpJO1lBQ04sS0FBSztnQkFDSEYsZ0JBQWdCMEksQ0FBQUEsV0FBWWxOLDRJQUFPQSxDQUFDa04sVUFBVTtnQkFDOUM7WUFDRixLQUFLO2dCQUNIMUksZ0JBQWdCMEksQ0FBQUEsV0FBWS9NLDRJQUFRQSxDQUFDK00sVUFBVTtnQkFDL0M7WUFDRixLQUFLO2dCQUNIMUksZ0JBQWdCMEksQ0FBQUEsV0FBWWpOLDRJQUFTQSxDQUFDaU4sVUFBVTtnQkFDaEQ7UUFDSjtJQUNGO0lBRUEsTUFBTUUsMkJBQTJCLFNBQUM3QjtZQUFZOEIsaUZBQXlCO1FBQ3JFLElBQUlBLGVBQWU7WUFDakIsTUFBTUMsTUFBTSxJQUFJN0k7WUFDaEIsTUFBTThJLFVBQVUsSUFBSTlJLEtBQUs4RztZQUN6QmdDLFFBQVEvQixRQUFRLENBQUM4QixJQUFJRSxRQUFRLElBQUlGLElBQUlHLFVBQVUsSUFBSUgsSUFBSW5CLFVBQVUsSUFBSW1CLElBQUlsQixlQUFlO1lBQ3hGc0Isa0JBQWtCSDtRQUNwQixPQUFPO1lBQ0xHLGtCQUFrQm5DO1FBQ3BCO0lBQ0Y7SUFFQSxNQUFNbUMsb0JBQW9CO1lBQU9uQyx3RUFBYSxJQUFJOUc7UUFDaEQsSUFBSSxDQUFDeUIsYUFBYTtRQUVsQixNQUFNeUgsWUFBWSxJQUFJbEosS0FBSzhHO1FBQzNCLE1BQU1xQyxVQUFVLElBQUluSixLQUFLa0osVUFBVTlFLE9BQU8sS0FBSyxDQUFDNUUsV0FBVzZFLGVBQWUsSUFBSSxFQUFDLElBQUs7UUFDcEYsTUFBTVosZUFBZWxILHFIQUFtQkEsQ0FBQzZFLFNBQVNBLFFBQVE7UUFFMUQsSUFBSTtZQUNGLE1BQU1nSSxlQUFvQjtnQkFDeEIsQ0FBQzVKLFdBQVd1RSxrQkFBa0IsQ0FBQyxFQUFFbUYsVUFBVW5CLFdBQVc7Z0JBQ3RELEdBQUl2SSxXQUFXMEUsZ0JBQWdCLElBQUk7b0JBQUUsQ0FBQzFFLFdBQVcwRSxnQkFBZ0IsQ0FBQyxFQUFFaUYsUUFBUXBCLFdBQVc7Z0JBQUcsQ0FBQztZQUM3RjtZQUVBLElBQUl0RSxhQUFhZSxVQUFVLEVBQUU7Z0JBQzNCNEUsWUFBWSxDQUFDM0YsYUFBYWUsVUFBVSxDQUFDLEdBQUc7WUFDMUM7WUFFQSxNQUFNNkUsU0FBUyxNQUFNM0gsY0FBY2xDLFdBQVcyQixVQUFVLEVBQUU7Z0JBQUNpSTthQUFhO1lBRXhFLElBQUlDLFVBQVVBLE9BQU9DLE9BQU8sSUFBSUQsT0FBT0MsT0FBTyxDQUFDbEcsTUFBTSxHQUFHLEdBQUc7Z0JBQ3pELE1BQU1tRyxjQUFjRixPQUFPQyxPQUFPLENBQUMsRUFBRSxDQUFDL0YsRUFBRTtnQkFFeEMsSUFBSWdHLGFBQWE7b0JBQ2YsTUFBTXpILGdCQUFnQnRDLFdBQVcyQixVQUFVO29CQUMzQ1MsZ0JBQWdCMkg7b0JBQ2hCek4sMENBQUtBLENBQUNrTSxPQUFPLENBQUM7Z0JBQ2hCLE9BQU87b0JBQ0xsTSwwQ0FBS0EsQ0FBQ21NLEtBQUssQ0FBQztnQkFDZDtZQUNGLE9BQU87Z0JBQ0xuTSwwQ0FBS0EsQ0FBQ21NLEtBQUssQ0FBQztZQUNkO1FBQ0YsRUFBRSxPQUFPQSxPQUFPO1lBQ2RuTSwwQ0FBS0EsQ0FBQ21NLEtBQUssQ0FBQztRQUNkO0lBQ0Y7SUFFQSxNQUFNdUIsbUJBQW1CLENBQUN4RTtRQUN4QixJQUFJQSxTQUFTQSxNQUFNekIsRUFBRSxFQUFFO1lBQ3JCLE1BQU1iLGNBQWN6QyxhQUFhLFFBQVEsdUJBQXVCO1lBQ2hFLE1BQU0wQyxZQUFZbkYsU0FBU29GLGNBQWMsQ0FBQ0Y7WUFDMUMsSUFBSUMsV0FBVztnQkFDYmhDLGVBQWV6QixPQUFPLEdBQUd5RCxVQUFVRyxTQUFTO1lBQzlDO1lBRUFWLFdBQVc0QyxNQUFNekIsRUFBRSxFQUFFeUIsTUFBTVQsTUFBTSxDQUFDcEQsVUFBVTtZQUM1Q2YsaUJBQWlCNEUsTUFBTXpCLEVBQUU7UUFDM0I7SUFDRjtJQUVBLE1BQU1rRyx1QkFBdUI7UUFDM0IsT0FBUXhKO1lBQ04sS0FBSztnQkFDSCxPQUFPM0UsNElBQU1BLENBQUN3RSxjQUFjO1lBQzlCLEtBQUs7Z0JBQ0gsT0FBTyxHQUF1RXhFLE9BQXBFQSw0SUFBTUEsQ0FBQ0MsNElBQU9BLENBQUN1RSxjQUFjLENBQUNBLGFBQWE0SixNQUFNLEtBQUssVUFBUyxPQUEyRSxPQUF0RXBPLDRJQUFNQSxDQUFDQyw0SUFBT0EsQ0FBQ3VFLGNBQWMsSUFBRUEsYUFBYTRKLE1BQU0sS0FBSztZQUN2SSxLQUFLO2dCQUNILE9BQU9wTyw0SUFBTUEsQ0FBQ3dFLGNBQWM7WUFDOUI7Z0JBQ0UsT0FBT3hFLDRJQUFNQSxDQUFDd0UsY0FBYztRQUNoQztJQUNGO0lBRUEsTUFBTTZKLG9CQUFvQixPQUFPM0U7UUFDL0IsSUFBSSxDQUFDdkQsYUFBYTtRQUVsQixJQUFJO1lBQ0YsTUFBTU0sY0FBY1gsU0FBU0EsUUFBUSxDQUFDbUMsRUFBRSxFQUFFO2dCQUFDeUIsTUFBTVQsTUFBTSxDQUFDaEIsRUFBRTthQUFDO1FBQzdELEVBQUUsT0FBTzBFLE9BQU87WUFDZGxGLFFBQVFrRixLQUFLLENBQUMseUJBQXlCQTtRQUN6QztJQUNGO0lBRUEsTUFBTTJCLGtCQUFxQztRQUN6QztZQUFFckcsSUFBSTtZQUFPdkUsT0FBTztZQUFPc0YsT0FBTztZQUFPZ0IsTUFBTTtRQUFNO1FBQ3JEO1lBQUUvQixJQUFJO1lBQVF2RSxPQUFPO1lBQVFzRixPQUFPO1lBQVFnQixNQUFNO1FBQU87UUFDekQ7WUFBRS9CLElBQUk7WUFBU3ZFLE9BQU87WUFBU3NGLE9BQU87WUFBU2dCLE1BQU07UUFBUTtLQUM5RDtJQUVELE1BQU11RSxxQkFBcUI1SixhQUFhLFFBQVE7UUFBQztLQUFNLEdBQUdBLGFBQWEsU0FBUztRQUFDO0tBQU8sR0FBRztRQUFDO0tBQVE7SUFFcEcscUJBQ0UsOERBQUM2SjtRQUFJQyxXQUFVOzswQkFDZCw4REFBQ0Q7Z0JBQUlDLFdBQVdoTywrQ0FBRUEsQ0FDaEIsd0NBQ0E4RSxpQkFBaUI7O29CQUVoQm5CLFdBQ0Msd0JBQXdCLGlCQUN4Qiw4REFBQ29LO3dCQUFJQyxXQUFXaE8sK0NBQUVBLENBQUMsT0FBTzhFLGlCQUFpQjs7MENBQ3pDLDhEQUFDaUo7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDQzt3Q0FBR0QsV0FBVTtrREFDWE47Ozs7OztrREFFSCw4REFBQ3hPLDBEQUFNQTt3Q0FDTGdQLFNBQVE7d0NBQ1JDLFNBQVMsSUFBTTFKLG9CQUFvQixDQUFDRDt3Q0FDcEN3SixXQUFVO2tEQUNYOzs7Ozs7Ozs7Ozs7MENBS0gsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDOU8sMERBQU1BO2dEQUNMZ1AsU0FBUTtnREFDUkMsU0FBUzNCO2dEQUNUd0IsV0FBV2hPLCtDQUFFQSxDQUNYLHlEQUNBOEUsZ0JBQWdCLFFBQVE7MERBRTNCOzs7Ozs7MERBR0QsOERBQUNpSjtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUM5TywwREFBTUE7d0RBQ0xnUCxTQUFRO3dEQUNSQyxTQUFTMUI7d0RBQ1R1QixXQUFXaE8sK0NBQUVBLENBQ1gsZ0RBQ0E4RSxnQkFBZ0IsWUFBWTtrRUFHOUIsNEVBQUMzRixnRkFBYUE7NERBQUM2TyxXQUFVOzs7Ozs7Ozs7OztrRUFFM0IsOERBQUM5TywwREFBTUE7d0RBQ0xnUCxTQUFRO3dEQUNSQyxTQUFTeEI7d0RBQ1RxQixXQUFXaE8sK0NBQUVBLENBQ1gsZ0RBQ0E4RSxnQkFBZ0IsWUFBWTtrRUFHOUIsNEVBQUMxRixpRkFBY0E7NERBQUM0TyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFLaEMsOERBQUNEO3dDQUFJQyxXQUFVOzswREFFYiw4REFBQ3ROLDZFQUFZQTtnREFDWDBOLFNBQVNQO2dEQUNUM0gsYUFBYTRIO2dEQUNiTyxVQUFVLENBQUNDO29EQUNULElBQUlBLFNBQVNqSCxNQUFNLEdBQUcsR0FBRzt3REFDdkJsRCxZQUFZbUssUUFBUSxDQUFDLEVBQUU7b0RBQ3pCO2dEQUNGO2dEQUNBTixXQUFXaE8sK0NBQUVBLENBQ1gsMEZBQ0E4RSxnQkFBZ0IsUUFBUTtnREFFMUJ5SixhQUFZO2dEQUNaQyxZQUFZOzs7Ozs7NENBR2I5SSw2QkFDQyw4REFBQ3hHLDBEQUFNQTtnREFDTGlQLFNBQVMsSUFBTXZCLHlCQUF5QjdJLGNBQWM7Z0RBQ3REaUssV0FBV2hPLCtDQUFFQSxDQUNYLDRGQUNBOEUsZ0JBQWdCLFFBQVE7MERBRzFCLDRFQUFDekYsMkVBQVFBO29EQUFDMk8sV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFPaEMsOERBQUNEO3dCQUFJQyxXQUFXaE8sK0NBQUVBLENBQ2hCLDBDQUNBOEUsZ0JBQWdCLFNBQVM7OzBDQUV6Qiw4REFBQ2lKO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQzlPLDBEQUFNQTt3Q0FDTGdQLFNBQVE7d0NBQ1JDLFNBQVMzQjt3Q0FDVHdCLFdBQVdoTywrQ0FBRUEsQ0FDWCx5REFDQThFLGdCQUFnQixRQUFRO2tEQUUzQjs7Ozs7O2tEQUdELDhEQUFDaUo7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDOU8sMERBQU1BO2dEQUNMZ1AsU0FBUTtnREFDUkMsU0FBUzFCO2dEQUNUdUIsV0FBV2hPLCtDQUFFQSxDQUNYLGdEQUNBOEUsZ0JBQWdCLFlBQVk7MERBRzlCLDRFQUFDM0YsZ0ZBQWFBO29EQUFDNk8sV0FBVTs7Ozs7Ozs7Ozs7MERBRTNCLDhEQUFDOU8sMERBQU1BO2dEQUNMZ1AsU0FBUTtnREFDUkMsU0FBU3hCO2dEQUNUcUIsV0FBV2hPLCtDQUFFQSxDQUNYLGdEQUNBOEUsZ0JBQWdCLFlBQVk7MERBRzlCLDRFQUFDMUYsaUZBQWNBO29EQUFDNE8sV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSTlCLDhEQUFDQzt3Q0FBR0QsV0FBVTtrREFDWE47Ozs7Ozs7Ozs7OzswQ0FJTCw4REFBQ0s7Z0NBQUlDLFdBQVU7O29DQUNaLENBQUNsSiwrQkFDQSw4REFBQ2lKO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ2xPLHdEQUFLQTtnREFDSnlPLGFBQVk7Z0RBQ1p0TCxPQUFPcUI7Z0RBQ1ArSixVQUFVLENBQUNJLElBQU1sSyxjQUFja0ssRUFBRUMsTUFBTSxDQUFDekwsS0FBSztnREFDN0MrSyxXQUFVOzs7Ozs7MERBRVosOERBQUMxTyxzRkFBbUJBO2dEQUFDME8sV0FBVTs7Ozs7Ozs7Ozs7O2tEQUluQyw4REFBQ3ROLDZFQUFZQTt3Q0FDWDBOLFNBQVNQO3dDQUNUM0gsYUFBYTRIO3dDQUNiTyxVQUFVLENBQUNDOzRDQUNULElBQUlBLFNBQVNqSCxNQUFNLEdBQUcsR0FBRztnREFDdkJsRCxZQUFZbUssUUFBUSxDQUFDLEVBQUU7NENBQ3pCO3dDQUNGO3dDQUNBTixXQUFXaE8sK0NBQUVBLENBQ1gscUZBQ0E4RSxnQkFBZ0IsYUFBYTt3Q0FFL0J5SixhQUFZO3dDQUNaQyxZQUFZOzs7Ozs7b0NBR2I5SSw2QkFDQyw4REFBQ3hHLDBEQUFNQTt3Q0FDTGlQLFNBQVMsSUFBTXZCLHlCQUF5QjdJLGNBQWM7d0NBQ3REaUssV0FBV2hPLCtDQUFFQSxDQUNYLGtHQUNBOEUsZ0JBQWdCLFFBQVE7OzBEQUcxQiw4REFBQ3pGLDJFQUFRQTtnREFBQzJPLFdBQVU7Ozs7Ozs0Q0FDbkIsQ0FBQ2xKLCtCQUFpQiw4REFBQzZKOzBEQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBT2xDaEwsWUFBWSxDQUFDbUIsK0JBQ1osOERBQUNpSjt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDbE8sd0RBQUtBO29DQUNKeU8sYUFBWTtvQ0FDWnRMLE9BQU9xQjtvQ0FDUCtKLFVBQVUsQ0FBQ0ksSUFBTWxLLGNBQWNrSyxFQUFFQyxNQUFNLENBQUN6TCxLQUFLO29DQUM3QytLLFdBQVU7Ozs7Ozs4Q0FFWiw4REFBQzFPLHNGQUFtQkE7b0NBQUMwTyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPdkMsOERBQUNEO2dCQUFJQyxXQUFVOztvQkFDWnhKLGtDQUNELDhEQUFDdUo7d0JBQUlDLFdBQVdoTywrQ0FBRUEsQ0FDaEIsc0JBQ0EyRCxXQUFXLDJEQUEyRDs7MENBRXRFLDhEQUFDb0s7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDWTt3Q0FBR1osV0FBVTtrREFBbUM7Ozs7OztvQ0FDaERySywwQkFDQyw4REFBQ3pFLDBEQUFNQTt3Q0FDTGdQLFNBQVE7d0NBQ1JDLFNBQVMsSUFBTTFKLG9CQUFvQjt3Q0FDbkN1SixXQUFVOzswREFFViw4REFBQ1c7Z0RBQUtYLFdBQVU7MERBQVU7Ozs7Ozs0Q0FBWTs7Ozs7Ozs7Ozs7OzswQ0FLNUMsOERBQUMvTyw4REFBUUE7Z0NBQ1A0UCxNQUFLO2dDQUNMUCxVQUFVdks7Z0NBQ1YrSyxVQUFVLENBQUMvRDtvQ0FDVCxJQUFJQSxNQUFNO3dDQUNSL0csZ0JBQWdCK0c7d0NBQ2hCLElBQUlwSCxVQUFVOzRDQUNaYyxvQkFBb0I7d0NBQ3RCO29DQUNGO2dDQUNGO2dDQUNBdUosV0FBVTs7Ozs7Ozs7Ozs7O2tDQUtkLDhEQUFDck4sc0RBQVVBO3dCQUNUMkYsU0FBU0E7d0JBQ1QrQyxhQUFhQTt3QkFDYlcsV0FBV0E7d0JBQ1grRSxXQUFXOzRCQUFDM047eUJBQTRCOzswQ0FFMUMsOERBQUMyTTtnQ0FBSUMsV0FBVTtnQ0FBaUJnQix5QkFBc0I7O29DQUNuRDlLLGFBQWEsdUJBQ2YsOERBQUM5RCx5REFBT0E7d0NBQ04yRCxjQUFjQTt3Q0FDZHdJLFFBQVFBO3dDQUNSbkksZUFBZUE7d0NBQ2ZDLGtCQUFrQkE7d0NBQ2xCNEssa0JBQWtCckM7d0NBQ2xCbEgsYUFBYUE7d0NBQ2JkLGdCQUFnQkE7d0NBQ2hCNkksa0JBQWtCQTt3Q0FDbEIvSSxnQkFBZ0JBOzs7Ozs7b0NBR25CUixhQUFhLHdCQUNaLDhEQUFDN0QsMkRBQVFBO3dDQUNQMEQsY0FBY0E7d0NBQ2R3SSxRQUFRQTt3Q0FDUm5JLGVBQWVBO3dDQUNmQyxrQkFBa0JBO3dDQUNsQkwsaUJBQWlCQTt3Q0FDakJpTCxrQkFBa0JyQzt3Q0FDbEJsSCxhQUFhQTt3Q0FDYmQsZ0JBQWdCQTt3Q0FDaEI2SSxrQkFBa0JBO3dDQUNsQi9JLGdCQUFnQkE7Ozs7OztvQ0FHbkJSLGFBQWEseUJBQ1osOERBQUM1RCw2REFBU0E7d0NBQ1J5RCxjQUFjQTt3Q0FDZHdJLFFBQVFBO3dDQUNSbkksZUFBZUE7d0NBQ2ZDLGtCQUFrQkE7d0NBQ2xCTCxpQkFBaUJBO3dDQUNqQmlMLGtCQUFrQixDQUFDbEUsT0FBUzZCLHlCQUF5QjdCLE1BQU07d0NBQzNEckYsYUFBYUE7d0NBQ2IrSCxrQkFBa0JBO3dDQUNsQi9JLGdCQUFnQkE7Ozs7Ozs7Ozs7OzswQ0FLbkIsOERBQUM5RCx1REFBV0E7Z0NBQUNzTyxlQUFlOzBDQUMxQnhLLGtCQUFrQkEsZUFBZTRGLElBQUksS0FBSywwQkFDdkMsOERBQUNySixtRkFBb0JBO29DQUNuQmtPLFNBQVN6SyxlQUFlMkYsT0FBTztvQ0FDL0IrRSxNQUFNbEwsYUFBYSxRQUFRLFFBQVE7b0NBQ25DaUssU0FBUyxLQUFPO29DQUNoQmtCLE9BQU87d0NBQ0xwTixPQUFPeUMsZUFBZXpDLEtBQUs7d0NBQzNCSyxRQUFRb0MsZUFBZXBDLE1BQU07b0NBQy9COzs7OztnREFFQW9DLGtCQUFrQkEsZUFBZTRGLElBQUksS0FBSyx3QkFDNUMsOERBQUMvSiw2RUFBaUJBO29DQUNoQjBJLE9BQU92RSxlQUFlMkYsT0FBTztvQ0FDN0IrRSxNQUFLO29DQUNMakIsU0FBUyxLQUFPO29DQUNoQmtCLE9BQU87d0NBQ0xwTixPQUFPeUMsZUFBZXpDLEtBQUs7d0NBQzNCSyxRQUFRb0MsZUFBZXBDLE1BQU07b0NBQy9COzs7OztnREFFQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTVosRUFBRTtJQTFyQldjOztRQUNtQzlFLDhEQUFZQTtRQUVsQ0csb0RBQU9BO1FBQ1Z3QixpRUFBYUE7UUFDZEMsOERBQWNBO1FBRWR4Qiw2REFBY0E7UUFDWkMsaUVBQWdCQTtRQXlCMkVDLHNEQUFRQTtRQUNyRkMsOERBQWdCQTtRQUNWQyw4REFBZ0JBO1FBQ2pDa0U7UUFDRjdDLG1FQUFjQTtRQUVyQmEsc0RBQVVBOzs7S0F2Q2ZvQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy93b3Jrc3BhY2UvbWFpbi92aWV3cy9jYWxlbmRhci9pbmRleC50c3g/YjE2NCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZVJlZiwgdXNlRWZmZWN0LCB1c2VDYWxsYmFjayB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgdXNlV29ya3NwYWNlIH0gZnJvbSBcIkAvcHJvdmlkZXJzL3dvcmtzcGFjZVwiO1xuaW1wb3J0IHsgTWF0Y2gsIFByb2Nlc3NlZERiUmVjb3JkIH0gZnJvbSBcIm9wZW5kYi1hcHAtZGItdXRpbHMvbGliL3R5cGluZ3MvZGJcIjtcbmltcG9ydCB7IFBhZ2VMb2FkZXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL2N1c3RvbS11aS9sb2FkZXJcIjtcbmltcG9ydCB7IHVzZVBhZ2UgfSBmcm9tIFwiQC9wcm92aWRlcnMvcGFnZVwiO1xuaW1wb3J0IHsgQ2FsZW5kYXJWaWV3RGVmaW5pdGlvbiwgVmlld0RlZmluaXRpb24gfSBmcm9tIFwib3BlbmRiLWFwcC1kYi11dGlscy9saWIvdHlwaW5ncy92aWV3XCI7XG5pbXBvcnQgeyBSZWNvcmQgfSBmcm9tIFwiQC90eXBpbmdzL2RhdGFiYXNlXCI7XG5pbXBvcnQgeyBWaWV3IH0gZnJvbSBcIkAvdHlwaW5ncy9wYWdlXCI7XG5pbXBvcnQgeyB1c2VNYXliZVNoYXJlZCB9IGZyb20gXCJAL3Byb3ZpZGVycy9zaGFyZWRcIjtcbmltcG9ydCB7IHVzZU1heWJlVGVtcGxhdGUgfSBmcm9tIFwiQC9wcm92aWRlcnMvdGVtcGxhdGVcIjtcbmltcG9ydCB7IHVzZVZpZXdzLCB1c2VWaWV3RmlsdGVyaW5nLCB1c2VWaWV3U2VsZWN0aW9uIH0gZnJvbSBcIkAvcHJvdmlkZXJzL3ZpZXdzXCI7XG5pbXBvcnQgeyBmaWx0ZXJBbmRTb3J0UmVjb3Jkcywgc2VhcmNoRmlsdGVyZWRSZWNvcmRzIH0gZnJvbSBcIkAvY29tcG9uZW50cy93b3Jrc3BhY2UvbWFpbi92aWV3cy90YWJsZVwiO1xuaW1wb3J0IHsgQ2FsZW5kYXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2NhbGVuZGFyXCI7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiO1xuaW1wb3J0IHsgRHJvcGRvd25NZW51LCBEcm9wZG93bk1lbnVDb250ZW50LCBEcm9wZG93bk1lbnVJdGVtLCBEcm9wZG93bk1lbnVUcmlnZ2VyIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9kcm9wZG93bi1tZW51XCI7XG5pbXBvcnQgeyBBbmdsZUxlZnRJY29uLCBBbmdsZVJpZ2h0SWNvbiwgUGx1c0ljb24sIE1hZ25pZnlpbmdHbGFzc0ljb24gfSBmcm9tIFwiQC9jb21wb25lbnRzL2ljb25zL0ZvbnRBd2Vzb21lUmVndWxhclwiO1xuaW1wb3J0IHsgZm9ybWF0LCBhZGREYXlzLCBhZGRNb250aHMsIHN1Yk1vbnRocywgYWRkV2Vla3MsIHN1YldlZWtzLCBhZGRIb3Vycywgc2V0SG91cnMsIHNldE1pbnV0ZXMsIHNldFNlY29uZHMsIHNldE1pbGxpc2Vjb25kcywgc3RhcnRPZkRheSB9IGZyb20gXCJkYXRlLWZuc1wiO1xuaW1wb3J0IHsgSW5wdXQgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2lucHV0XCI7XG5pbXBvcnQgeyB0b2FzdCB9IGZyb20gXCJzb25uZXJcIjtcbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCI7XG5pbXBvcnQgeyB1c2VBbGVydCB9IGZyb20gXCJAL3Byb3ZpZGVycy9hbGVydFwiO1xuaW1wb3J0IHsgdXNlU2NyZWVuU2l6ZSB9IGZyb20gXCJAL3Byb3ZpZGVycy9zY3JlZW5TaXplXCI7XG5pbXBvcnQgeyB1c2VNYXliZVJlY29yZCB9IGZyb20gXCJAL3Byb3ZpZGVycy9yZWNvcmRcIjtcbmltcG9ydCB7IHVzZVN0YWNrZWRQZWVrIH0gZnJvbSBcIkAvcHJvdmlkZXJzL3N0YWNrZWRQZWVrXCI7XG5cblxuaW1wb3J0IHsgRGF5VmlldyB9IGZyb20gXCIuL2NvbXBvbmVudHMvRGF5Vmlld1wiO1xuaW1wb3J0IHsgV2Vla1ZpZXcgfSBmcm9tIFwiLi9jb21wb25lbnRzL1dlZWtWaWV3XCI7XG5pbXBvcnQgeyBNb250aFZpZXcgfSBmcm9tIFwiLi9jb21wb25lbnRzL01vbnRoVmlld1wiO1xuaW1wb3J0IHsgQ2FsZW5kYXJFdmVudEl0ZW0gfSBmcm9tIFwiLi9jb21wb25lbnRzL0NhbGVuZGFyRXZlbnRJdGVtXCI7XG5pbXBvcnQgeyBnZXREYXRhYmFzZVRpdGxlQ29sLCBnZXRSZWNvcmRUaXRsZSB9IGZyb20gJ0AvY29tcG9uZW50cy93b3Jrc3BhY2UvbWFpbi92aWV3cy9mb3JtL2NvbXBvbmVudHMvZWxlbWVudC9saW5rZWQnO1xuaW1wb3J0IHsgQ2FsZW5kYXJWaWV3UmVuZGVyUHJvcHMsIENhbGVuZGFyRXZlbnQsIENhbGVuZGFyVmlld1R5cGUgfSBmcm9tICdAL3R5cGluZ3MvcGFnZSc7XG5pbXBvcnQgeyBTaGVldCwgU2hlZXRDb250ZW50IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3NoZWV0JztcbmltcG9ydCB7IENhbGVuZGFySWNvbiB9IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSc7XG5pbXBvcnQgeyBDdXN0b21TZWxlY3QgfSBmcm9tICdAL2NvbXBvbmVudHMvY3VzdG9tLXVpL2N1c3RvbVNlbGVjdCc7XG5pbXBvcnQgeyBUYWdJdGVtIH0gZnJvbSAnQC9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL3ZpZXdzL3RhYmxlL3JlbmRlcmVyL2NvbW1vbi90YWcnO1xuaW1wb3J0IHtcbiAgRG5kQ29udGV4dCxcbiAgRHJhZ092ZXJsYXksXG4gIFBvaW50ZXJTZW5zb3IsXG4gIFRvdWNoU2Vuc29yLFxuICB1c2VTZW5zb3IsXG4gIHVzZVNlbnNvcnMsXG4gIEFjdGl2ZSxcbiAgT3ZlclxufSBmcm9tICdAZG5kLWtpdC9jb3JlJztcbmltcG9ydCB7IHJlc3RyaWN0VG9XaW5kb3dFZGdlcyB9IGZyb20gJ0BkbmQta2l0L21vZGlmaWVycyc7XG5pbXBvcnQgeyBFdmVudFNlZ21lbnQgfSBmcm9tICdAL3V0aWxzL211bHRpRGF5RXZlbnRVdGlscyc7XG5pbXBvcnQgeyBDYWxlbmRhckV2ZW50U2VnbWVudCB9IGZyb20gJy4vY29tcG9uZW50cy9DYWxlbmRhckV2ZW50U2VnbWVudCc7XG5pbXBvcnQgeyBkaWZmZXJlbmNlSW5EYXlzLCBkaWZmZXJlbmNlSW5NaWxsaXNlY29uZHMgfSBmcm9tICdkYXRlLWZucyc7XG5cbi8vIEN1c3RvbSBtb2RpZmllciB0byByZXN0cmljdCBkcmFnZ2luZyB0byB0aGUgY2FsZW5kYXIgbWFpbiBjb250ZW50IGFyZWEgb25seVxuY29uc3QgcmVzdHJpY3RUb0NhbGVuZGFyQ29udGFpbmVyID0gKHsgdHJhbnNmb3JtLCBkcmFnZ2luZ05vZGVSZWN0LCB3aW5kb3dSZWN0IH06IGFueSkgPT4ge1xuICBpZiAoIWRyYWdnaW5nTm9kZVJlY3QgfHwgIXdpbmRvd1JlY3QpIHtcbiAgICByZXR1cm4gdHJhbnNmb3JtO1xuICB9XG5cbiAgLy8gRmluZCB0aGUgY2FsZW5kYXIgbWFpbiBjb250ZW50IGNvbnRhaW5lciAodGhlIGRpdiB0aGF0IGNvbnRhaW5zIHRoZSB2aWV3cylcbiAgY29uc3QgY2FsZW5kYXJDb250YWluZXIgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCdbZGF0YS1jYWxlbmRhci1jb250ZW50PVwidHJ1ZVwiXScpO1xuICBpZiAoIWNhbGVuZGFyQ29udGFpbmVyKSB7XG4gICAgcmV0dXJuIHRyYW5zZm9ybTtcbiAgfVxuXG4gIGNvbnN0IGNvbnRhaW5lclJlY3QgPSBjYWxlbmRhckNvbnRhaW5lci5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTtcbiAgXG4gIC8vIENhbGN1bGF0ZSB0aGUgYm91bmRhcmllcyByZWxhdGl2ZSB0byB0aGUgd2luZG93XG4gIGNvbnN0IG1pblggPSBjb250YWluZXJSZWN0LmxlZnQ7XG4gIGNvbnN0IG1heFggPSBjb250YWluZXJSZWN0LnJpZ2h0IC0gZHJhZ2dpbmdOb2RlUmVjdC53aWR0aDtcbiAgY29uc3QgbWluWSA9IGNvbnRhaW5lclJlY3QudG9wO1xuICBjb25zdCBtYXhZID0gY29udGFpbmVyUmVjdC5ib3R0b20gLSBkcmFnZ2luZ05vZGVSZWN0LmhlaWdodDtcblxuICAvLyBHZXQgY3VycmVudCBwb2ludGVyIHBvc2l0aW9uXG4gIGNvbnN0IGN1cnJlbnRYID0gdHJhbnNmb3JtLnggKyBkcmFnZ2luZ05vZGVSZWN0LmxlZnQ7XG4gIGNvbnN0IGN1cnJlbnRZID0gdHJhbnNmb3JtLnkgKyBkcmFnZ2luZ05vZGVSZWN0LnRvcDtcblxuICAvLyBDb25zdHJhaW4gdGhlIHBvc2l0aW9uXG4gIGNvbnN0IGNvbnN0cmFpbmVkWCA9IE1hdGgubWluKE1hdGgubWF4KGN1cnJlbnRYLCBtaW5YKSwgbWF4WCk7XG4gIGNvbnN0IGNvbnN0cmFpbmVkWSA9IE1hdGgubWluKE1hdGgubWF4KGN1cnJlbnRZLCBtaW5ZKSwgbWF4WSk7XG5cbiAgcmV0dXJuIHtcbiAgICAuLi50cmFuc2Zvcm0sXG4gICAgeDogY29uc3RyYWluZWRYIC0gZHJhZ2dpbmdOb2RlUmVjdC5sZWZ0LFxuICAgIHk6IGNvbnN0cmFpbmVkWSAtIGRyYWdnaW5nTm9kZVJlY3QudG9wLFxuICB9O1xufTtcblxuXG5cbi8vIEN1c3RvbSBob29rIHRvIHRyYWNrIHByZXZpb3VzIHZhbHVlXG5jb25zdCB1c2VQcmV2aW91cyA9IDxULD4odmFsdWU6IFQpID0+IHtcbiAgY29uc3QgcmVmID0gdXNlUmVmPFQ+KCk7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgcmVmLmN1cnJlbnQgPSB2YWx1ZTtcbiAgfSk7XG4gIHJldHVybiByZWYuY3VycmVudDtcbn07XG5cbmV4cG9ydCBjb25zdCBDYWxlbmRhclZpZXcgPSAocHJvcHM6IENhbGVuZGFyVmlld1JlbmRlclByb3BzKSA9PiB7XG4gIGNvbnN0IHsgZGF0YWJhc2VTdG9yZSwgbWVtYmVycywgd29ya3NwYWNlIH0gPSB1c2VXb3Jrc3BhY2UoKTtcbiAgY29uc3QgeyBkZWZpbml0aW9uIH0gPSBwcm9wcztcbiAgY29uc3QgeyBhY2Nlc3NMZXZlbCB9ID0gdXNlUGFnZSgpO1xuICBjb25zdCB7IGlzTW9iaWxlIH0gPSB1c2VTY3JlZW5TaXplKCk7XG4gIGNvbnN0IG1heWJlUmVjb3JkID0gdXNlTWF5YmVSZWNvcmQoKTsgXG5cbiAgY29uc3QgbWF5YmVTaGFyZWQgPSB1c2VNYXliZVNoYXJlZCgpO1xuICBjb25zdCBtYXliZVRlbXBsYXRlID0gdXNlTWF5YmVUZW1wbGF0ZSgpO1xuXG4gIGNvbnN0IFtzZWxlY3RlZERhdGUsIHNldFNlbGVjdGVkRGF0ZV0gPSB1c2VTdGF0ZTxEYXRlPihuZXcgRGF0ZSgpKTtcbiAgY29uc3QgW3ZpZXdUeXBlLCBzZXRWaWV3VHlwZV0gPSB1c2VTdGF0ZTxDYWxlbmRhclZpZXdUeXBlPihcIndlZWtcIik7XG4gIGNvbnN0IFtzZWxlY3RlZEV2ZW50LCBzZXRTZWxlY3RlZEV2ZW50XSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbc2VhcmNoVGVybSwgc2V0U2VhcmNoVGVybV0gPSB1c2VTdGF0ZShcIlwiKTtcbiAgY29uc3QgW3Nob3dTaWRlQ2FsZW5kYXIsIHNldFNob3dTaWRlQ2FsZW5kYXJdID0gdXNlU3RhdGUoIWlzTW9iaWxlKTtcbiAgY29uc3QgW2FjdGl2ZURyYWdEYXRhLCBzZXRBY3RpdmVEcmFnRGF0YV0gPSB1c2VTdGF0ZTxhbnkgfCBudWxsPihudWxsKTtcbiAgY29uc3Qgc2F2ZWRTY3JvbGxUb3AgPSB1c2VSZWYoMCk7XG4gIGNvbnN0IHBvaW50ZXJDb29yZGluYXRlcyA9IHVzZVJlZih7IHg6IDAsIHk6IDAgfSk7XG5cbiAgY29uc3QgaXNJblJlY29yZFRhYiA9ICEhbWF5YmVSZWNvcmQ7XG5cbiAgZGVmaW5pdGlvbi5maWx0ZXIgPSBkZWZpbml0aW9uLmZpbHRlciB8fCB7IGNvbmRpdGlvbnM6IFtdLCBtYXRjaDogTWF0Y2guQWxsIH07XG4gIGRlZmluaXRpb24uc29ydHMgPSBkZWZpbml0aW9uLnNvcnRzIHx8IFtdO1xuXG4gIGNvbnN0IGRhdGFiYXNlSWQgPSBkZWZpbml0aW9uLmRhdGFiYXNlSWQ7XG4gIGNvbnN0IGRhdGFiYXNlID0gZGF0YWJhc2VTdG9yZVtkZWZpbml0aW9uLmRhdGFiYXNlSWRdO1xuXG4gIGNvbnN0IGlzUHVibGlzaGVkVmlldyA9ICEhbWF5YmVTaGFyZWQ7XG4gIGNvbnN0IGVkaXRhYmxlID0gIWRlZmluaXRpb24ubG9ja0NvbnRlbnQgJiYgIWlzUHVibGlzaGVkVmlldyAmJiAhIWFjY2Vzc0xldmVsO1xuXG4gIGxldCBjYW5FZGl0U3RydWN0dXJlID0gISEoIW1heWJlVGVtcGxhdGUgJiYgIW1heWJlU2hhcmVkICYmICFpc1B1Ymxpc2hlZFZpZXcgJiYgIWRlZmluaXRpb24ubG9ja0NvbnRlbnQgJiYgYWNjZXNzTGV2ZWwgJiYgZWRpdGFibGUpO1xuICBsZXQgY2FuRWRpdERhdGEgPSAhISghbWF5YmVUZW1wbGF0ZSAmJiAhbWF5YmVTaGFyZWQgJiYgIWlzUHVibGlzaGVkVmlldyAmJiAhZGVmaW5pdGlvbi5sb2NrQ29udGVudCAmJiBhY2Nlc3NMZXZlbCAmJiBlZGl0YWJsZSk7XG5cbiAgICAgIGNvbnN0IHsgY3JlYXRlUmVjb3JkcywgdXBkYXRlUmVjb3JkVmFsdWVzLCBzZXRQZWVrUmVjb3JkSWQsIHBlZWtSZWNvcmRJZCwgcmVmcmVzaERhdGFiYXNlLCBkZWxldGVSZWNvcmRzIH0gPSB1c2VWaWV3cygpO1xuICAgIGNvbnN0IHsgc29ydHMsIGZpbHRlciwgc2VhcmNoIH0gPSB1c2VWaWV3RmlsdGVyaW5nKCk7XG4gICAgY29uc3QgeyBzZWxlY3RlZElkcywgc2V0U2VsZWN0ZWRJZHMgfSA9IHVzZVZpZXdTZWxlY3Rpb24oKTtcbiAgY29uc3QgcHJldlBlZWtSZWNvcmRJZCA9IHVzZVByZXZpb3VzKHBlZWtSZWNvcmRJZCk7XG4gIGNvbnN0IHsgb3BlblJlY29yZCB9ID0gdXNlU3RhY2tlZFBlZWsoKTtcblxuICBjb25zdCBzZW5zb3JzID0gdXNlU2Vuc29ycyhcbiAgICB1c2VTZW5zb3IoUG9pbnRlclNlbnNvciwge1xuICAgICAgYWN0aXZhdGlvbkNvbnN0cmFpbnQ6IHtcbiAgICAgICAgZGlzdGFuY2U6IDgsXG4gICAgICB9LFxuICAgIH0pLFxuICAgIHVzZVNlbnNvcihUb3VjaFNlbnNvciwge1xuICAgICAgYWN0aXZhdGlvbkNvbnN0cmFpbnQ6IHtcbiAgICAgICAgZGVsYXk6IDE1MCxcbiAgICAgICAgdG9sZXJhbmNlOiA1LFxuICAgICAgfSxcbiAgICB9KVxuICApO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgY29udGFpbmVySWQgPSB2aWV3VHlwZSA9PT0gJ2RheScgPyAnZGF5LXZpZXctY29udGFpbmVyJyA6ICd3ZWVrLXZpZXctY29udGFpbmVyJztcbiAgICBjb25zdCBjb250YWluZXIgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChjb250YWluZXJJZCk7XG5cbiAgICBpZiAoY29udGFpbmVyKSB7XG4gICAgICByZXF1ZXN0QW5pbWF0aW9uRnJhbWUoKCkgPT4ge1xuICAgICAgICBjb250YWluZXIuc2Nyb2xsVG9wID0gc2F2ZWRTY3JvbGxUb3AuY3VycmVudDtcbiAgICAgIH0pO1xuICAgIH1cbiAgfSwgW3NlbGVjdGVkRXZlbnQsIHZpZXdUeXBlXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzZXRTaG93U2lkZUNhbGVuZGFyKCFpc01vYmlsZSk7XG4gIH0sIFtpc01vYmlsZV0pO1xuXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBPbmx5IHJlZnJlc2ggaWYgdGhlIHBlZWsgdmlldyB3YXMgb3BlbiBhbmQgaXMgbm93IGNsb3NlZC5cbiAgICBpZiAocHJldlBlZWtSZWNvcmRJZCAmJiAhcGVla1JlY29yZElkKSB7XG4gICAgICBjb25zb2xlLmxvZyhcIlBlZWsgdmlldyB3YXMgY2xvc2VkLCByZWZyZXNoaW5nIGNhbGVuZGFyIGRhdGFcIik7XG4gICAgICByZWZyZXNoRGF0YWJhc2UoZGVmaW5pdGlvbi5kYXRhYmFzZUlkKTtcbiAgICB9XG4gIH0sIFtwZWVrUmVjb3JkSWQsIHByZXZQZWVrUmVjb3JkSWQsIGRlZmluaXRpb24uZGF0YWJhc2VJZCwgcmVmcmVzaERhdGFiYXNlXSk7XG5cbiAgaWYgKCFkYXRhYmFzZSkgcmV0dXJuIDxQYWdlTG9hZGVyIHNpemU9XCJmdWxsXCIgLz47XG5cbiAgY29uc3QgZ2V0RXZlbnRzID0gKCk6IENhbGVuZGFyRXZlbnRbXSA9PiB7XG4gICAgaWYgKCFkYXRhYmFzZSkgcmV0dXJuIFtdO1xuXG4gICAgY29uc3QgeyByb3dzIH0gPSBmaWx0ZXJBbmRTb3J0UmVjb3JkcyhcbiAgICAgIGRhdGFiYXNlLFxuICAgICAgbWVtYmVycyxcbiAgICAgIGRhdGFiYXNlU3RvcmUsXG4gICAgICBkZWZpbml0aW9uLmZpbHRlciB8fCB7IG1hdGNoOiBNYXRjaC5BbGwsIGNvbmRpdGlvbnM6IFtdIH0sXG4gICAgICBmaWx0ZXIsXG4gICAgICBzb3J0cy5sZW5ndGggPyBzb3J0cyA6IChkZWZpbml0aW9uLnNvcnRzIHx8IFtdKSxcbiAgICAgIHdvcmtzcGFjZT8ud29ya3NwYWNlTWVtYmVyPy51c2VySWQgfHwgJycsXG4gICAgICBkYXRhYmFzZT8uZGF0YWJhc2U/LmlkIHx8ICcnXG4gICAgKTtcblxuICAgIGNvbnN0IGZpbHRlcmVkUm93cyA9IHNlYXJjaEZpbHRlcmVkUmVjb3JkcyhzZWFyY2ggfHwgXCJcIiwgcm93cyk7XG4gICAgY29uc3QgdGl0bGVDb2xPcHRzID0gZ2V0RGF0YWJhc2VUaXRsZUNvbChkYXRhYmFzZS5kYXRhYmFzZSk7XG5cbiAgICByZXR1cm4gZmlsdGVyZWRSb3dzLm1hcChyb3cgPT4ge1xuICAgICAgY29uc3Qgc3RhcnRWYWx1ZSA9IHJvdy5wcm9jZXNzZWRSZWNvcmQucHJvY2Vzc2VkUmVjb3JkVmFsdWVzW2RlZmluaXRpb24uZXZlbnRTdGFydENvbHVtbklkXTtcbiAgICAgIGxldCBzdGFydERhdGU6IERhdGU7XG5cbiAgICAgIGlmIChzdGFydFZhbHVlICYmIHR5cGVvZiBzdGFydFZhbHVlID09PSAnc3RyaW5nJykge1xuICAgICAgICBzdGFydERhdGUgPSBuZXcgRGF0ZShzdGFydFZhbHVlKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHN0YXJ0RGF0ZSA9IG5ldyBEYXRlKCk7XG4gICAgICB9XG5cbiAgICAgIGxldCBlbmREYXRlOiBEYXRlO1xuICAgICAgaWYgKGRlZmluaXRpb24uZXZlbnRFbmRDb2x1bW5JZCkge1xuICAgICAgICBjb25zdCBlbmRWYWx1ZSA9IHJvdy5wcm9jZXNzZWRSZWNvcmQucHJvY2Vzc2VkUmVjb3JkVmFsdWVzW2RlZmluaXRpb24uZXZlbnRFbmRDb2x1bW5JZF07XG4gICAgICAgIGlmIChlbmRWYWx1ZSAmJiB0eXBlb2YgZW5kVmFsdWUgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgICAgZW5kRGF0ZSA9IG5ldyBEYXRlKGVuZFZhbHVlKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBlbmREYXRlID0gbmV3IERhdGUoc3RhcnREYXRlLmdldFRpbWUoKSArIChkZWZpbml0aW9uLmRlZmF1bHREdXJhdGlvbiB8fCAzMCkgKiA2MDAwMCk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGVuZERhdGUgPSBuZXcgRGF0ZShzdGFydERhdGUuZ2V0VGltZSgpICsgKGRlZmluaXRpb24uZGVmYXVsdER1cmF0aW9uIHx8IDMwKSAqIDYwMDAwKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgdGl0bGUgPSBnZXRSZWNvcmRUaXRsZShcbiAgICAgICAgcm93LnJlY29yZCxcbiAgICAgICAgdGl0bGVDb2xPcHRzLnRpdGxlQ29sSWQsXG4gICAgICAgIHRpdGxlQ29sT3B0cy5kZWZhdWx0VGl0bGUsXG4gICAgICAgIHRpdGxlQ29sT3B0cy5pc0NvbnRhY3RzXG4gICAgICApO1xuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBpZDogcm93LmlkLFxuICAgICAgICB0aXRsZSxcbiAgICAgICAgc3RhcnQ6IHN0YXJ0RGF0ZSxcbiAgICAgICAgZW5kOiBlbmREYXRlLFxuICAgICAgICByZWNvcmQ6IHJvdy5yZWNvcmQsXG4gICAgICAgIHByb2Nlc3NlZFJlY29yZDogcm93LnByb2Nlc3NlZFJlY29yZFxuICAgICAgfTtcbiAgICB9KTtcbiAgfTtcblxuICBjb25zdCBnZXRGaWx0ZXJlZEV2ZW50cyA9ICgpID0+IHtcbiAgICBjb25zdCBiYXNlRXZlbnRzID0gZ2V0RXZlbnRzKCk7XG5cbiAgICBpZiAoIXNlYXJjaFRlcm0udHJpbSgpKSB7XG4gICAgICByZXR1cm4gYmFzZUV2ZW50cztcbiAgICB9XG5cbiAgICByZXR1cm4gYmFzZUV2ZW50cy5maWx0ZXIoZXZlbnQgPT4ge1xuICAgICAgY29uc3Qgc2VhcmNoTG93ZXIgPSBzZWFyY2hUZXJtLnRvTG93ZXJDYXNlKCk7XG4gICAgICByZXR1cm4gZXZlbnQudGl0bGUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hMb3dlcik7XG4gICAgfSk7XG4gIH07XG5cbiAgY29uc3Qgb25EcmFnU3RhcnQgPSAoZXZlbnQ6IHsgYWN0aXZlOiBBY3RpdmUgfSkgPT4ge1xuICAgIGlmIChldmVudC5hY3RpdmUuZGF0YS5jdXJyZW50KSB7XG4gICAgICBjb25zdCBpbml0aWFsUG9pbnRlclkgPSBwb2ludGVyQ29vcmRpbmF0ZXMuY3VycmVudC55O1xuICAgICAgY29uc3QgaW5pdGlhbEV2ZW50VG9wID0gZXZlbnQuYWN0aXZlLnJlY3QuY3VycmVudD8udHJhbnNsYXRlZD8udG9wID8/IDA7XG4gICAgICBjb25zdCBncmFiT2Zmc2V0WSA9IGluaXRpYWxQb2ludGVyWSAtIGluaXRpYWxFdmVudFRvcDtcbiAgICAgIFxuICAgICAgc2V0QWN0aXZlRHJhZ0RhdGEoe1xuICAgICAgICAuLi5ldmVudC5hY3RpdmUuZGF0YS5jdXJyZW50LFxuICAgICAgICBncmFiT2Zmc2V0WSxcbiAgICAgICAgd2lkdGg6IGV2ZW50LmFjdGl2ZS5yZWN0LmN1cnJlbnQudHJhbnNsYXRlZD8ud2lkdGgsXG4gICAgICAgIGhlaWdodDogZXZlbnQuYWN0aXZlLnJlY3QuY3VycmVudC50cmFuc2xhdGVkPy5oZWlnaHQsXG4gICAgICB9KTtcblxuICAgICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignbW91c2Vtb3ZlJywgaGFuZGxlTW91c2VNb3ZlKTtcbiAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ3RvdWNobW92ZScsIGhhbmRsZVRvdWNoTW92ZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IG9uRHJhZ0VuZCA9IGFzeW5jICh7IGFjdGl2ZSwgb3ZlciB9OiB7IGFjdGl2ZTogQWN0aXZlLCBvdmVyOiBPdmVyIHwgbnVsbCB9KSA9PiB7XG4gICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2Vtb3ZlJywgaGFuZGxlTW91c2VNb3ZlKTtcbiAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCd0b3VjaG1vdmUnLCBoYW5kbGVUb3VjaE1vdmUpO1xuICAgIHNldEFjdGl2ZURyYWdEYXRhKG51bGwpO1xuICBcbiAgICBjb25zb2xlLmxvZygnW0RyYWdFbmRdIEZpcmVkJywgeyBhY3RpdmUsIG92ZXIgfSk7XG5cbiAgICBpZiAoIW92ZXIgfHwgIWFjdGl2ZSB8fCAhY2FuRWRpdERhdGEgfHwgYWN0aXZlLmlkID09PSBvdmVyLmlkKSB7XG4gICAgICBjb25zb2xlLmxvZygnW0RyYWdFbmRdIEV4aXRpbmc6IEludmFsaWQgZHJvcCBjb25kaXRpb25zLicsIHsgb3ZlciwgYWN0aXZlLCBjYW5FZGl0RGF0YSB9KTtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gIFxuICAgIGNvbnN0IGFjdGl2ZURhdGEgPSBhY3RpdmUuZGF0YS5jdXJyZW50O1xuICAgIGNvbnN0IG92ZXJEYXRhID0gb3Zlci5kYXRhLmN1cnJlbnQ7XG4gIFxuICAgIGlmICghYWN0aXZlRGF0YSB8fCAhb3ZlckRhdGEpIHtcbiAgICAgIGNvbnNvbGUubG9nKCdbRHJhZ0VuZF0gRXhpdGluZzogTWlzc2luZyBhY3RpdmUgb3Igb3ZlciBkYXRhLicpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBcbiAgICBjb25zb2xlLmxvZygnW0RyYWdFbmRdIEFjdGl2ZSBEYXRhOicsIGFjdGl2ZURhdGEpO1xuICAgIGNvbnNvbGUubG9nKCdbRHJhZ0VuZF0gT3ZlciBEYXRhOicsIG92ZXJEYXRhKTtcbiAgXG4gICAgY29uc3QgeyBwYXlsb2FkLCB0eXBlIH0gPSBhY3RpdmVEYXRhO1xuICAgIGNvbnN0IGV2ZW50VG9VcGRhdGU6IENhbGVuZGFyRXZlbnQgPSB0eXBlID09PSAnc2VnbWVudCcgPyBwYXlsb2FkLm9yaWdpbmFsRXZlbnQgOiBwYXlsb2FkO1xuICAgIGNvbnNvbGUubG9nKCdbRHJhZ0VuZF0gRXZlbnQgdG8gVXBkYXRlOicsIGV2ZW50VG9VcGRhdGUpO1xuICBcbiAgICBjb25zdCBvcmlnaW5hbFN0YXJ0ID0gbmV3IERhdGUoZXZlbnRUb1VwZGF0ZS5zdGFydCk7XG4gICAgY29uc3Qgb3JpZ2luYWxFbmQgPSBuZXcgRGF0ZShldmVudFRvVXBkYXRlLmVuZCk7XG4gICAgY29uc3QgZHVyYXRpb24gPSBkaWZmZXJlbmNlSW5NaWxsaXNlY29uZHMob3JpZ2luYWxFbmQsIG9yaWdpbmFsU3RhcnQpO1xuICAgIGNvbnNvbGUubG9nKCdbRHJhZ0VuZF0gT3JpZ2luYWwgVGltZXM6JywgeyBvcmlnaW5hbFN0YXJ0LCBvcmlnaW5hbEVuZCwgZHVyYXRpb24gfSk7XG4gIFxuICAgIGxldCBuZXdTdGFydDogRGF0ZTtcbiAgXG4gICAgaWYgKG92ZXJEYXRhLnR5cGUuc3RhcnRzV2l0aCgnYWxsZGF5JykpIHtcbiAgICAgIGNvbnNvbGUubG9nKCdbRHJhZ0VuZF0gTG9naWMgYnJhbmNoOiBBbGwtZGF5Jyk7XG4gICAgICBjb25zdCBkYXlEaWZmZXJlbmNlID0gZGlmZmVyZW5jZUluRGF5cyhvdmVyRGF0YS5kYXRlLCBzdGFydE9mRGF5KG9yaWdpbmFsU3RhcnQpKTtcbiAgICAgIG5ld1N0YXJ0ID0gYWRkRGF5cyhvcmlnaW5hbFN0YXJ0LCBkYXlEaWZmZXJlbmNlKTtcbiAgICAgIC8vIEZvciBhbGwtZGF5IGRyb3BzLCBub3JtYWxpemUgdGhlIHRpbWUgdG8gdGhlIHN0YXJ0IG9mIHRoZSBkYXlcbiAgICAgIG5ld1N0YXJ0LnNldEhvdXJzKDAsIDAsIDAsIDApO1xuICAgIH0gZWxzZSBpZiAob3ZlckRhdGEudHlwZS5zdGFydHNXaXRoKCd0aW1lc2xvdCcpKSB7XG4gICAgICBjb25zb2xlLmxvZygnW0RyYWdFbmRdIExvZ2ljIGJyYW5jaDogVGltZXNsb3QnKTtcbiAgICAgIG5ld1N0YXJ0ID0gbmV3IERhdGUob3ZlckRhdGEuZGF0ZSk7XG4gICAgICBcbiAgICAgIC8vIEVuaGFuY2VkIE1pbnV0ZS1sZXZlbCBwcmVjaXNpb24gY2FsY3VsYXRpb25cbiAgICAgIGNvbnN0IGdyYWJPZmZzZXRZID0gYWN0aXZlRGF0YS5ncmFiT2Zmc2V0WSB8fCAwO1xuICAgICAgY29uc3Qgc2xvdFRvcCA9IG92ZXIucmVjdC50b3A7XG4gICAgICBjb25zdCBwb2ludGVyWSA9IHBvaW50ZXJDb29yZGluYXRlcy5jdXJyZW50Lnk7XG4gICAgICBcbiAgICAgIC8vIEFkanVzdCBwb2ludGVyIHBvc2l0aW9uIGJhc2VkIG9uIGluaXRpYWwgZ3JhYiBwb2ludFxuICAgICAgY29uc3QgYWRqdXN0ZWRQb2ludGVyWSA9IHBvaW50ZXJZIC0gZ3JhYk9mZnNldFk7XG4gICAgICBcbiAgICAgIGNvbnN0IG9mZnNldFkgPSBhZGp1c3RlZFBvaW50ZXJZIC0gc2xvdFRvcDtcbiAgICAgIGNvbnN0IHNsb3RIZWlnaHQgPSBvdmVyLnJlY3QuaGVpZ2h0OyAvLyBTaG91bGQgYmUgNjBweFxuICAgICAgY29uc3QgbWludXRlc0Zyb21Ub3AgPSAob2Zmc2V0WSAvIHNsb3RIZWlnaHQpICogNjA7XG5cbiAgICAgIC8vIFNuYXAgdG8gbmVhcmVzdCA1LW1pbnV0ZSBpbnRlcnZhbFxuICAgICAgY29uc3Qgc25hcHBlZE1pbnV0ZXMgPSBNYXRoLnJvdW5kKG1pbnV0ZXNGcm9tVG9wIC8gNSkgKiA1O1xuICAgICAgY29uc3QgbmV3TWludXRlcyA9IE1hdGgubWF4KDAsIE1hdGgubWluKDU5LCBzbmFwcGVkTWludXRlcykpO1xuXG4gICAgICBuZXdTdGFydC5zZXRIb3VycyhvdmVyRGF0YS5ob3VyLCBuZXdNaW51dGVzLCBvcmlnaW5hbFN0YXJ0LmdldFNlY29uZHMoKSwgb3JpZ2luYWxTdGFydC5nZXRNaWxsaXNlY29uZHMoKSk7XG4gICAgICBjb25zb2xlLmxvZygnW0RyYWdFbmRdIE1pbnV0ZSBQcmVjaXNpb24gQ2FsYzonLCB7IHNsb3RUb3AsIHBvaW50ZXJZLCBvZmZzZXRZLCBzbG90SGVpZ2h0LCBtaW51dGVzRnJvbVRvcCwgbmV3TWludXRlcyB9KTtcbiAgICB9IGVsc2UgaWYgKG92ZXJEYXRhLnR5cGUgPT09ICdkYXljZWxsJykge1xuICAgICAgY29uc29sZS5sb2coJ1tEcmFnRW5kXSBMb2dpYyBicmFuY2g6IERheSBDZWxsIChNb250aCknKTtcbiAgICAgIGNvbnN0IGRheURpZmZlcmVuY2UgPSBkaWZmZXJlbmNlSW5EYXlzKG92ZXJEYXRhLmRhdGUsIHN0YXJ0T2ZEYXkob3JpZ2luYWxTdGFydCkpO1xuICAgICAgbmV3U3RhcnQgPSBhZGREYXlzKG9yaWdpbmFsU3RhcnQsIGRheURpZmZlcmVuY2UpO1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zb2xlLmxvZygnW0RyYWdFbmRdIEV4aXRpbmc6IEludmFsaWQgZHJvcCB0YXJnZXQgdHlwZS4nKTtcbiAgICAgIHJldHVybjsgLy8gSW52YWxpZCBkcm9wIHRhcmdldFxuICAgIH1cbiAgXG4gICAgY29uc3QgbmV3RW5kID0gbmV3IERhdGUobmV3U3RhcnQuZ2V0VGltZSgpICsgZHVyYXRpb24pO1xuICAgIGNvbnNvbGUubG9nKCdbRHJhZ0VuZF0gTmV3IFRpbWVzOicsIHsgbmV3U3RhcnQsIG5ld0VuZCB9KTtcbiAgXG4gICAgY29uc3QgcmVjb3JkSWQgPSBldmVudFRvVXBkYXRlLnJlY29yZC5pZDtcbiAgICBjb25zdCBuZXdWYWx1ZXMgPSB7XG4gICAgICBbZGVmaW5pdGlvbi5ldmVudFN0YXJ0Q29sdW1uSWRdOiBuZXdTdGFydC50b0lTT1N0cmluZygpLFxuICAgICAgLi4uKGRlZmluaXRpb24uZXZlbnRFbmRDb2x1bW5JZCAmJiB7IFtkZWZpbml0aW9uLmV2ZW50RW5kQ29sdW1uSWRdOiBuZXdFbmQudG9JU09TdHJpbmcoKSB9KSxcbiAgICB9O1xuICAgIGNvbnNvbGUubG9nKCdbRHJhZ0VuZF0gVXBkYXRlIFBheWxvYWQ6JywgeyByZWNvcmRJZCwgbmV3VmFsdWVzIH0pO1xuICBcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ1tEcmFnRW5kXSBDYWxsaW5nIHVwZGF0ZVJlY29yZFZhbHVlcy4uLicpO1xuICAgICAgYXdhaXQgdXBkYXRlUmVjb3JkVmFsdWVzKGRlZmluaXRpb24uZGF0YWJhc2VJZCwgW3JlY29yZElkXSwgbmV3VmFsdWVzKTtcbiAgICAgIGNvbnNvbGUubG9nKCdbRHJhZ0VuZF0gVXBkYXRlIHN1Y2Nlc3NmdWwuJyk7XG4gICAgICB0b2FzdC5zdWNjZXNzKGBFdmVudCBcIiR7ZXZlbnRUb1VwZGF0ZS50aXRsZX1cIiB1cGRhdGVkLmApO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICB0b2FzdC5lcnJvcihcIkZhaWxlZCB0byB1cGRhdGUgZXZlbnQuXCIpO1xuICAgICAgY29uc29sZS5lcnJvcihcIltEcmFnRW5kXSBFcnJvciB1cGRhdGluZyBldmVudDpcIiwgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVNb3VzZU1vdmUgPSAoZXZlbnQ6IE1vdXNlRXZlbnQpID0+IHtcbiAgICBwb2ludGVyQ29vcmRpbmF0ZXMuY3VycmVudCA9IHsgeDogZXZlbnQuY2xpZW50WCwgeTogZXZlbnQuY2xpZW50WSB9O1xuICB9O1xuICBjb25zdCBoYW5kbGVUb3VjaE1vdmUgPSAoZXZlbnQ6IFRvdWNoRXZlbnQpID0+IHtcbiAgICAgIGNvbnN0IHRvdWNoID0gZXZlbnQudG91Y2hlc1swXTtcbiAgICAgIHBvaW50ZXJDb29yZGluYXRlcy5jdXJyZW50ID0geyB4OiB0b3VjaC5jbGllbnRYLCB5OiB0b3VjaC5jbGllbnRZIH07XG4gIH07XG5cblxuICBjb25zdCBldmVudHMgPSBnZXRGaWx0ZXJlZEV2ZW50cygpO1xuXG4gIGNvbnN0IGdvVG9Ub2RheSA9ICgpID0+IHNldFNlbGVjdGVkRGF0ZShuZXcgRGF0ZSgpKTtcblxuICBjb25zdCBnb1RvUHJldmlvdXMgPSAoKSA9PiB7XG4gICAgc3dpdGNoICh2aWV3VHlwZSkge1xuICAgICAgY2FzZSBcImRheVwiOlxuICAgICAgICBzZXRTZWxlY3RlZERhdGUocHJldkRhdGUgPT4gYWRkRGF5cyhwcmV2RGF0ZSwgLTEpKTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlIFwid2Vla1wiOlxuICAgICAgICBzZXRTZWxlY3RlZERhdGUocHJldkRhdGUgPT4gc3ViV2Vla3MocHJldkRhdGUsIDEpKTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlIFwibW9udGhcIjpcbiAgICAgICAgc2V0U2VsZWN0ZWREYXRlKHByZXZEYXRlID0+IHN1Yk1vbnRocyhwcmV2RGF0ZSwgMSkpO1xuICAgICAgICBicmVhaztcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZ29Ub05leHQgPSAoKSA9PiB7XG4gICAgc3dpdGNoICh2aWV3VHlwZSkge1xuICAgICAgY2FzZSBcImRheVwiOlxuICAgICAgICBzZXRTZWxlY3RlZERhdGUocHJldkRhdGUgPT4gYWRkRGF5cyhwcmV2RGF0ZSwgMSkpO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgXCJ3ZWVrXCI6XG4gICAgICAgIHNldFNlbGVjdGVkRGF0ZShwcmV2RGF0ZSA9PiBhZGRXZWVrcyhwcmV2RGF0ZSwgMSkpO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgXCJtb250aFwiOlxuICAgICAgICBzZXRTZWxlY3RlZERhdGUocHJldkRhdGUgPT4gYWRkTW9udGhzKHByZXZEYXRlLCAxKSk7XG4gICAgICAgIGJyZWFrO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVSZXF1ZXN0Q3JlYXRlRXZlbnQgPSAoZGF0ZTogRGF0ZSwgdXNlU3lzdGVtVGltZTogYm9vbGVhbiA9IGZhbHNlKSA9PiB7XG4gICAgaWYgKHVzZVN5c3RlbVRpbWUpIHtcbiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XG4gICAgICBjb25zdCBuZXdEYXRlID0gbmV3IERhdGUoZGF0ZSk7XG4gICAgICBuZXdEYXRlLnNldEhvdXJzKG5vdy5nZXRIb3VycygpLCBub3cuZ2V0TWludXRlcygpLCBub3cuZ2V0U2Vjb25kcygpLCBub3cuZ2V0TWlsbGlzZWNvbmRzKCkpO1xuICAgICAgaGFuZGxlQ3JlYXRlRXZlbnQobmV3RGF0ZSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGhhbmRsZUNyZWF0ZUV2ZW50KGRhdGUpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVDcmVhdGVFdmVudCA9IGFzeW5jIChkYXRlOiBEYXRlID0gbmV3IERhdGUoKSkgPT4ge1xuICAgIGlmICghY2FuRWRpdERhdGEpIHJldHVybjtcblxuICAgIGNvbnN0IHN0YXJ0VGltZSA9IG5ldyBEYXRlKGRhdGUpO1xuICAgIGNvbnN0IGVuZFRpbWUgPSBuZXcgRGF0ZShzdGFydFRpbWUuZ2V0VGltZSgpICsgKGRlZmluaXRpb24uZGVmYXVsdER1cmF0aW9uIHx8IDMwKSAqIDYwMDAwKTtcbiAgICBjb25zdCB0aXRsZUNvbE9wdHMgPSBnZXREYXRhYmFzZVRpdGxlQ29sKGRhdGFiYXNlLmRhdGFiYXNlKTtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZWNvcmRWYWx1ZXM6IGFueSA9IHtcbiAgICAgICAgW2RlZmluaXRpb24uZXZlbnRTdGFydENvbHVtbklkXTogc3RhcnRUaW1lLnRvSVNPU3RyaW5nKCksXG4gICAgICAgIC4uLihkZWZpbml0aW9uLmV2ZW50RW5kQ29sdW1uSWQgJiYgeyBbZGVmaW5pdGlvbi5ldmVudEVuZENvbHVtbklkXTogZW5kVGltZS50b0lTT1N0cmluZygpIH0pXG4gICAgICB9O1xuXG4gICAgICBpZiAodGl0bGVDb2xPcHRzLnRpdGxlQ29sSWQpIHtcbiAgICAgICAgcmVjb3JkVmFsdWVzW3RpdGxlQ29sT3B0cy50aXRsZUNvbElkXSA9IFwiTmV3IEV2ZW50XCI7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGNyZWF0ZVJlY29yZHMoZGVmaW5pdGlvbi5kYXRhYmFzZUlkLCBbcmVjb3JkVmFsdWVzXSk7XG5cbiAgICAgIGlmIChyZXN1bHQgJiYgcmVzdWx0LnJlY29yZHMgJiYgcmVzdWx0LnJlY29yZHMubGVuZ3RoID4gMCkge1xuICAgICAgICBjb25zdCBuZXdSZWNvcmRJZCA9IHJlc3VsdC5yZWNvcmRzWzBdLmlkO1xuXG4gICAgICAgIGlmIChuZXdSZWNvcmRJZCkge1xuICAgICAgICAgIGF3YWl0IHJlZnJlc2hEYXRhYmFzZShkZWZpbml0aW9uLmRhdGFiYXNlSWQpO1xuICAgICAgICAgIHNldFBlZWtSZWNvcmRJZChuZXdSZWNvcmRJZCk7XG4gICAgICAgICAgdG9hc3Quc3VjY2VzcyhcIk5ldyBldmVudCBjcmVhdGVkXCIpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHRvYXN0LmVycm9yKFwiRXJyb3IgYWNjZXNzaW5nIHRoZSBuZXcgZXZlbnRcIik7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRvYXN0LmVycm9yKFwiRmFpbGVkIHRvIGNyZWF0ZSBldmVudCBwcm9wZXJseVwiKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdG9hc3QuZXJyb3IoXCJGYWlsZWQgdG8gY3JlYXRlIGV2ZW50XCIpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVFdmVudENsaWNrID0gKGV2ZW50OiBDYWxlbmRhckV2ZW50KSA9PiB7XG4gICAgaWYgKGV2ZW50ICYmIGV2ZW50LmlkKSB7XG4gICAgICBjb25zdCBjb250YWluZXJJZCA9IHZpZXdUeXBlID09PSAnZGF5JyA/ICdkYXktdmlldy1jb250YWluZXInIDogJ3dlZWstdmlldy1jb250YWluZXInO1xuICAgICAgY29uc3QgY29udGFpbmVyID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoY29udGFpbmVySWQpO1xuICAgICAgaWYgKGNvbnRhaW5lcikge1xuICAgICAgICBzYXZlZFNjcm9sbFRvcC5jdXJyZW50ID0gY29udGFpbmVyLnNjcm9sbFRvcDtcbiAgICAgIH1cblxuICAgICAgb3BlblJlY29yZChldmVudC5pZCwgZXZlbnQucmVjb3JkLmRhdGFiYXNlSWQpO1xuICAgICAgc2V0U2VsZWN0ZWRFdmVudChldmVudC5pZCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGdldEhlYWRlckRhdGVEaXNwbGF5ID0gKCkgPT4ge1xuICAgIHN3aXRjaCAodmlld1R5cGUpIHtcbiAgICAgIGNhc2UgXCJkYXlcIjpcbiAgICAgICAgcmV0dXJuIGZvcm1hdChzZWxlY3RlZERhdGUsICdNTU1NIGQsIHl5eXknKTtcbiAgICAgIGNhc2UgXCJ3ZWVrXCI6XG4gICAgICAgIHJldHVybiBgJHtmb3JtYXQoYWRkRGF5cyhzZWxlY3RlZERhdGUsIC1zZWxlY3RlZERhdGUuZ2V0RGF5KCkpLCAnTU1NIGQnKX0gLSAke2Zvcm1hdChhZGREYXlzKHNlbGVjdGVkRGF0ZSwgNi1zZWxlY3RlZERhdGUuZ2V0RGF5KCkpLCAnTU1NIGQsIHl5eXknKX1gO1xuICAgICAgY2FzZSBcIm1vbnRoXCI6XG4gICAgICAgIHJldHVybiBmb3JtYXQoc2VsZWN0ZWREYXRlLCAnTU1NTSB5eXl5Jyk7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gZm9ybWF0KHNlbGVjdGVkRGF0ZSwgJ01NTU0gZCwgeXl5eScpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVFdmVudERlbGV0ZSA9IGFzeW5jIChldmVudDogQ2FsZW5kYXJFdmVudCkgPT4ge1xuICAgIGlmICghY2FuRWRpdERhdGEpIHJldHVybjtcbiAgICBcbiAgICB0cnkge1xuICAgICAgYXdhaXQgZGVsZXRlUmVjb3JkcyhkYXRhYmFzZS5kYXRhYmFzZS5pZCwgW2V2ZW50LnJlY29yZC5pZF0pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBkZWxldGluZyBldmVudDonLCBlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHZpZXdUeXBlT3B0aW9uczogVGFnSXRlbTxzdHJpbmc+W10gPSBbXG4gICAgeyBpZDogJ2RheScsIHZhbHVlOiAnZGF5JywgdGl0bGU6ICdEYXknLCBkYXRhOiAnZGF5JyB9LFxuICAgIHsgaWQ6ICd3ZWVrJywgdmFsdWU6ICd3ZWVrJywgdGl0bGU6ICdXZWVrJywgZGF0YTogJ3dlZWsnIH0sXG4gICAgeyBpZDogJ21vbnRoJywgdmFsdWU6ICdtb250aCcsIHRpdGxlOiAnTW9udGgnLCBkYXRhOiAnbW9udGgnIH1cbiAgXTtcblxuICBjb25zdCBzZWxlY3RlZFZpZXdPcHRpb24gPSB2aWV3VHlwZSA9PT0gJ2RheScgPyBbJ2RheSddIDogdmlld1R5cGUgPT09ICd3ZWVrJyA/IFsnd2VlayddIDogWydtb250aCddO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJoLWZ1bGwgZmxleCBmbGV4LWNvbCBiZy13aGl0ZVwiPlxuICAgICA8ZGl2IGNsYXNzTmFtZT17Y24oXG4gICAgICAgXCJib3JkZXItYiBib3JkZXItbmV1dHJhbC0zMDAgYmctd2hpdGVcIixcbiAgICAgICBpc0luUmVjb3JkVGFiICYmIFwicHktMVwiIFxuICAgICApfT5cbiAgICAgICB7aXNNb2JpbGUgPyAoXG4gICAgICAgICAvKiBNb2JpbGUgSGVhZGVyIExheW91dCAqL1xuICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2NuKFwicC0yXCIsIGlzSW5SZWNvcmRUYWIgJiYgXCJweS0xXCIpfT5cbiAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItMlwiPlxuICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgdGV4dC1ibGFjayB0cnVuY2F0ZSBmbGV4LTEgbXItMlwiPlxuICAgICAgICAgICAgICAge2dldEhlYWRlckRhdGVEaXNwbGF5KCl9XG4gICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd1NpZGVDYWxlbmRhcighc2hvd1NpZGVDYWxlbmRhcil9XG4gICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLWZ1bGwgaC04IHB4LTMgdGV4dC14cyB0ZXh0LWJsYWNrIGhvdmVyOmJnLWdyYXktNTBcIlxuICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgIENhbGVuZGFyXG4gICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTFcIj5cbiAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgb25DbGljaz17Z29Ub1RvZGF5fVxuICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICAgIFwicm91bmRlZC1mdWxsIHB4LTMgdGV4dC14cyB0ZXh0LWJsYWNrIGhvdmVyOmJnLWdyYXktNTBcIixcbiAgICAgICAgICAgICAgICAgICBpc0luUmVjb3JkVGFiID8gXCJoLTZcIiA6IFwiaC04XCJcbiAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgVG9kYXlcbiAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtnb1RvUHJldmlvdXN9XG4gICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICAgIFwicm91bmRlZC1mdWxsIHAtMSB0ZXh0LWJsYWNrIGhvdmVyOmJnLWdyYXktNTBcIixcbiAgICAgICAgICAgICAgICAgICAgIGlzSW5SZWNvcmRUYWIgPyBcImgtNiB3LTZcIiA6IFwiaC04IHctOFwiXG4gICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgIDxBbmdsZUxlZnRJY29uIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgb25DbGljaz17Z29Ub05leHR9XG4gICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICAgIFwicm91bmRlZC1mdWxsIHAtMSB0ZXh0LWJsYWNrIGhvdmVyOmJnLWdyYXktNTBcIixcbiAgICAgICAgICAgICAgICAgICAgIGlzSW5SZWNvcmRUYWIgPyBcImgtNiB3LTZcIiA6IFwiaC04IHctOFwiXG4gICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgIDxBbmdsZVJpZ2h0SWNvbiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xXCI+XG4gICAgICAgICAgICAgICB7LyogVmlldyBUeXBlIFNlbGVjdG9yICovfVxuICAgICAgICAgICAgICAgPEN1c3RvbVNlbGVjdFxuICAgICAgICAgICAgICAgICBvcHRpb25zPXt2aWV3VHlwZU9wdGlvbnN9XG4gICAgICAgICAgICAgICAgIHNlbGVjdGVkSWRzPXtzZWxlY3RlZFZpZXdPcHRpb259XG4gICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoc2VsZWN0ZWQpID0+IHtcbiAgICAgICAgICAgICAgICAgICBpZiAoc2VsZWN0ZWQubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgICAgICAgICAgc2V0Vmlld1R5cGUoc2VsZWN0ZWRbMF0gYXMgQ2FsZW5kYXJWaWV3VHlwZSk7XG4gICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICAgIFwicHgtMyB0ZXh0LXhzIGJvcmRlci1uZXV0cmFsLTMwMCByb3VuZGVkLWZ1bGwgYmctd2hpdGUgaG92ZXI6YmctZ3JheS01MCB0ZXh0LWJsYWNrIHctMjBcIixcbiAgICAgICAgICAgICAgICAgICBpc0luUmVjb3JkVGFiID8gXCJoLTZcIiA6IFwiaC04XCJcbiAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJWaWV3XCJcbiAgICAgICAgICAgICAgICAgaGlkZVNlYXJjaD17dHJ1ZX1cbiAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgIHtjYW5FZGl0RGF0YSAmJiAoXG4gICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVSZXF1ZXN0Q3JlYXRlRXZlbnQoc2VsZWN0ZWREYXRlLCB0cnVlKX1cbiAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICAgICAgXCJyb3VuZGVkLWZ1bGwgcHgtMyB0ZXh0LXhzIGJvcmRlciBib3JkZXItbmV1dHJhbC0zMDAgYmctd2hpdGUgaG92ZXI6YmctZ3JheS01MCB0ZXh0LWJsYWNrXCIsXG4gICAgICAgICAgICAgICAgICAgICBpc0luUmVjb3JkVGFiID8gXCJoLTZcIiA6IFwiaC04XCJcbiAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgPFBsdXNJY29uIGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPlxuICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgKSA6IChcbiAgICAgICA8ZGl2IGNsYXNzTmFtZT17Y24oXG4gICAgICAgICBcImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBweC00XCIsXG4gICAgICAgICBpc0luUmVjb3JkVGFiID8gXCJweS0xXCIgOiBcInB5LTJcIlxuICAgICAgICl9PlxuICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgb25DbGljaz17Z29Ub1RvZGF5fVxuICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICBcInJvdW5kZWQtZnVsbCBweC0zIHRleHQteHMgdGV4dC1ibGFjayBob3ZlcjpiZy1ncmF5LTUwXCIsXG4gICAgICAgICAgICAgICBpc0luUmVjb3JkVGFiID8gXCJoLTZcIiA6IFwiaC04XCJcbiAgICAgICAgICAgICApfVxuICAgICAgICAgICA+XG4gICAgICAgICAgICAgVG9kYXlcbiAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgb25DbGljaz17Z29Ub1ByZXZpb3VzfVxuICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgXCJyb3VuZGVkLWZ1bGwgcC0xIHRleHQtYmxhY2sgaG92ZXI6YmctZ3JheS01MFwiLFxuICAgICAgICAgICAgICAgICBpc0luUmVjb3JkVGFiID8gXCJoLTYgdy02XCIgOiBcImgtOCB3LThcIlxuICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICA8QW5nbGVMZWZ0SWNvbiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgb25DbGljaz17Z29Ub05leHR9XG4gICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICBcInJvdW5kZWQtZnVsbCBwLTEgdGV4dC1ibGFjayBob3ZlcjpiZy1ncmF5LTUwXCIsXG4gICAgICAgICAgICAgICAgIGlzSW5SZWNvcmRUYWIgPyBcImgtNiB3LTZcIiA6IFwiaC04IHctOFwiXG4gICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgIDxBbmdsZVJpZ2h0SWNvbiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1zZW1pYm9sZCB0ZXh0LWJsYWNrXCI+XG4gICAgICAgICAgICAge2dldEhlYWRlckRhdGVEaXNwbGF5KCl9XG4gICAgICAgICAgIDwvaDE+XG4gICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgeyFpc0luUmVjb3JkVGFiICYmIChcbiAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWFyY2ggZXZlbnRzLi4uXCJcbiAgICAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaFRlcm19XG4gICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VhcmNoVGVybShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNDggcHItOCBoLTggdGV4dC14cyBib3JkZXItbmV1dHJhbC0zMDAgYmctdHJhbnNwYXJlbnQgc2hhZG93LW5vbmVcIlxuICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgIDxNYWduaWZ5aW5nR2xhc3NJY29uIGNsYXNzTmFtZT1cImgtMyB3LTMgYWJzb2x1dGUgcmlnaHQtMiB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIHRleHQtbmV1dHJhbC00MDBcIiAvPlxuICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICApfVxuXG4gICAgICAgICAgIDxDdXN0b21TZWxlY3RcbiAgICAgICAgICAgICBvcHRpb25zPXt2aWV3VHlwZU9wdGlvbnN9XG4gICAgICAgICAgICAgc2VsZWN0ZWRJZHM9e3NlbGVjdGVkVmlld09wdGlvbn1cbiAgICAgICAgICAgICBvbkNoYW5nZT17KHNlbGVjdGVkKSA9PiB7XG4gICAgICAgICAgICAgICBpZiAoc2VsZWN0ZWQubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgICAgICBzZXRWaWV3VHlwZShzZWxlY3RlZFswXSBhcyBDYWxlbmRhclZpZXdUeXBlKTtcbiAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICBcInB4LTMgdGV4dC14cyBib3JkZXItbmV1dHJhbC0zMDAgcm91bmRlZC1mdWxsIGJnLXdoaXRlIGhvdmVyOmJnLWdyYXktNTAgdGV4dC1ibGFja1wiLFxuICAgICAgICAgICAgICAgaXNJblJlY29yZFRhYiA/IFwiaC02IHctMjBcIiA6IFwiaC04IHctMjhcIlxuICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJWaWV3XCJcbiAgICAgICAgICAgICBoaWRlU2VhcmNoPXt0cnVlfVxuICAgICAgICAgICAvPlxuXG4gICAgICAgICAgIHtjYW5FZGl0RGF0YSAmJiAoXG4gICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlUmVxdWVzdENyZWF0ZUV2ZW50KHNlbGVjdGVkRGF0ZSwgdHJ1ZSl9XG4gICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICBcInJvdW5kZWQtZnVsbCBweC0zIHRleHQteHMgYm9yZGVyIGJvcmRlci1uZXV0cmFsLTMwMCBiZy13aGl0ZSBob3ZlcjpiZy1ncmF5LTUwIHRleHQtYmxhY2sgZ2FwLTFcIixcbiAgICAgICAgICAgICAgICAgaXNJblJlY29yZFRhYiA/IFwiaC02XCIgOiBcImgtOFwiXG4gICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgIDxQbHVzSWNvbiBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgIHshaXNJblJlY29yZFRhYiAmJiA8c3Bhbj5BZGQgRXZlbnQ8L3NwYW4+fVxuICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICApfVxuICAgICAgICAgPC9kaXY+XG4gICAgICAgPC9kaXY+XG4gICAgICl9XG5cbiAgICAge2lzTW9iaWxlICYmICFpc0luUmVjb3JkVGFiICYmIChcbiAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTIgcGItMlwiPlxuICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlYXJjaCBldmVudHMuLi5cIlxuICAgICAgICAgICAgIHZhbHVlPXtzZWFyY2hUZXJtfVxuICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VhcmNoVGVybShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHByLTggaC04IHRleHQteHMgYm9yZGVyLW5ldXRyYWwtMzAwIGJnLXRyYW5zcGFyZW50IHNoYWRvdy1ub25lXCJcbiAgICAgICAgICAgLz5cbiAgICAgICAgICAgPE1hZ25pZnlpbmdHbGFzc0ljb24gY2xhc3NOYW1lPVwiaC0zIHctMyBhYnNvbHV0ZSByaWdodC0yIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzIgdGV4dC1uZXV0cmFsLTQwMFwiIC8+XG4gICAgICAgICA8L2Rpdj5cbiAgICAgICA8L2Rpdj5cbiAgICAgKX1cbiAgIDwvZGl2PlxuXG4gICB7LyogTWFpbiBjb250ZW50ICovfVxuICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBtaW4taC0wXCI+XG4gICAgIHtzaG93U2lkZUNhbGVuZGFyICYmIChcbiAgICAgPGRpdiBjbGFzc05hbWU9e2NuKFxuICAgICAgIFwiZmxleC1ub25lIGJnLXdoaXRlXCIsXG4gICAgICAgaXNNb2JpbGUgPyBcInctZnVsbCBhYnNvbHV0ZSB6LTUwIGJhY2tkcm9wLWJsdXItc20gaC1mdWxsIHNoYWRvdy1sZ1wiIDogXCJ3LWZpdCBib3JkZXItciBib3JkZXItbmV1dHJhbC0zMDBcIlxuICAgICApfT5cbiAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBwLTIgYm9yZGVyLWIgYm9yZGVyLW5ldXRyYWwtMzAwXCI+XG4gICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LXNlbWlib2xkIHRleHQtYmxhY2tcIj5DYWxlbmRhcjwvaDM+XG4gICAgICAgICB7aXNNb2JpbGUgJiYgKFxuICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93U2lkZUNhbGVuZGFyKGZhbHNlKX1cbiAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLWZ1bGwgaC04IHctOCBwLTEgdGV4dC1ibGFjayBob3ZlcjpiZy1uZXV0cmFsLTEwMFwiXG4gICAgICAgICAgID5cbiAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzci1vbmx5XCI+Q2xvc2U8L3NwYW4+XG4gICAgICAgICAgICAgw5dcbiAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICApfVxuICAgICAgIDwvZGl2PlxuICAgICAgIDxDYWxlbmRhclxuICAgICAgICAgbW9kZT1cInNpbmdsZVwiXG4gICAgICAgICBzZWxlY3RlZD17c2VsZWN0ZWREYXRlfVxuICAgICAgICAgb25TZWxlY3Q9eyhkYXRlKSA9PiB7XG4gICAgICAgICAgIGlmIChkYXRlKSB7XG4gICAgICAgICAgICAgc2V0U2VsZWN0ZWREYXRlKGRhdGUpO1xuICAgICAgICAgICAgIGlmIChpc01vYmlsZSkge1xuICAgICAgICAgICAgICAgc2V0U2hvd1NpZGVDYWxlbmRhcihmYWxzZSk7XG4gICAgICAgICAgICAgfVxuICAgICAgICAgICB9XG4gICAgICAgICB9fVxuICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC1tZCBib3JkZXItMFwiXG4gICAgICAgLz5cbiAgICAgPC9kaXY+XG4gICAgICl9XG4gICAgIFxuICAgICA8RG5kQ29udGV4dFxuICAgICAgIHNlbnNvcnM9e3NlbnNvcnN9XG4gICAgICAgb25EcmFnU3RhcnQ9e29uRHJhZ1N0YXJ0fVxuICAgICAgIG9uRHJhZ0VuZD17b25EcmFnRW5kfVxuICAgICAgIG1vZGlmaWVycz17W3Jlc3RyaWN0VG9DYWxlbmRhckNvbnRhaW5lcl19XG4gICAgID5cbiAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiIGRhdGEtY2FsZW5kYXItY29udGVudD1cInRydWVcIj5cbiAgICAgICB7dmlld1R5cGUgPT09ICdkYXknICYmIChcbiAgICAgIDxEYXlWaWV3XG4gICAgICAgIHNlbGVjdGVkRGF0ZT17c2VsZWN0ZWREYXRlfVxuICAgICAgICBldmVudHM9e2V2ZW50c31cbiAgICAgICAgc2VsZWN0ZWRFdmVudD17c2VsZWN0ZWRFdmVudH1cbiAgICAgICAgc2V0U2VsZWN0ZWRFdmVudD17c2V0U2VsZWN0ZWRFdmVudH1cbiAgICAgICAgb3BlbkFkZEV2ZW50Rm9ybT17aGFuZGxlUmVxdWVzdENyZWF0ZUV2ZW50fVxuICAgICAgICBjYW5FZGl0RGF0YT17Y2FuRWRpdERhdGF9XG4gICAgICAgIHNhdmVkU2Nyb2xsVG9wPXtzYXZlZFNjcm9sbFRvcH1cbiAgICAgICAgaGFuZGxlRXZlbnRDbGljaz17aGFuZGxlRXZlbnRDbGlja31cbiAgICAgICAgYWN0aXZlRHJhZ0RhdGE9e2FjdGl2ZURyYWdEYXRhfVxuICAgICAgLz5cbiAgICApfVxuICAgIHt2aWV3VHlwZSA9PT0gJ3dlZWsnICYmIChcbiAgICAgIDxXZWVrVmlld1xuICAgICAgICBzZWxlY3RlZERhdGU9e3NlbGVjdGVkRGF0ZX1cbiAgICAgICAgZXZlbnRzPXtldmVudHN9XG4gICAgICAgIHNlbGVjdGVkRXZlbnQ9e3NlbGVjdGVkRXZlbnR9XG4gICAgICAgIHNldFNlbGVjdGVkRXZlbnQ9e3NldFNlbGVjdGVkRXZlbnR9XG4gICAgICAgIHNldFNlbGVjdGVkRGF0ZT17c2V0U2VsZWN0ZWREYXRlfVxuICAgICAgICBvcGVuQWRkRXZlbnRGb3JtPXtoYW5kbGVSZXF1ZXN0Q3JlYXRlRXZlbnR9XG4gICAgICAgIGNhbkVkaXREYXRhPXtjYW5FZGl0RGF0YX1cbiAgICAgICAgc2F2ZWRTY3JvbGxUb3A9e3NhdmVkU2Nyb2xsVG9wfVxuICAgICAgICBoYW5kbGVFdmVudENsaWNrPXtoYW5kbGVFdmVudENsaWNrfVxuICAgICAgICBhY3RpdmVEcmFnRGF0YT17YWN0aXZlRHJhZ0RhdGF9XG4gICAgICAvPlxuICAgICl9XG4gICAge3ZpZXdUeXBlID09PSAnbW9udGgnICYmIChcbiAgICAgIDxNb250aFZpZXdcbiAgICAgICAgc2VsZWN0ZWREYXRlPXtzZWxlY3RlZERhdGV9XG4gICAgICAgIGV2ZW50cz17ZXZlbnRzfVxuICAgICAgICBzZWxlY3RlZEV2ZW50PXtzZWxlY3RlZEV2ZW50fVxuICAgICAgICBzZXRTZWxlY3RlZEV2ZW50PXtzZXRTZWxlY3RlZEV2ZW50fVxuICAgICAgICBzZXRTZWxlY3RlZERhdGU9e3NldFNlbGVjdGVkRGF0ZX1cbiAgICAgICAgb3BlbkFkZEV2ZW50Rm9ybT17KGRhdGUpID0+IGhhbmRsZVJlcXVlc3RDcmVhdGVFdmVudChkYXRlLCB0cnVlKX1cbiAgICAgICAgY2FuRWRpdERhdGE9e2NhbkVkaXREYXRhfVxuICAgICAgICBoYW5kbGVFdmVudENsaWNrPXtoYW5kbGVFdmVudENsaWNrfVxuICAgICAgICBhY3RpdmVEcmFnRGF0YT17YWN0aXZlRHJhZ0RhdGF9XG4gICAgICAvPlxuICAgICl9XG4gICAgIDwvZGl2PlxuXG4gICAgIDxEcmFnT3ZlcmxheSBkcm9wQW5pbWF0aW9uPXtudWxsfT5cbiAgICAgIHthY3RpdmVEcmFnRGF0YSAmJiBhY3RpdmVEcmFnRGF0YS50eXBlID09PSAnc2VnbWVudCcgPyAoXG4gICAgICAgICAgPENhbGVuZGFyRXZlbnRTZWdtZW50XG4gICAgICAgICAgICBzZWdtZW50PXthY3RpdmVEcmFnRGF0YS5wYXlsb2FkIGFzIEV2ZW50U2VnbWVudH1cbiAgICAgICAgICAgIHZpZXc9e3ZpZXdUeXBlID09PSAnZGF5JyA/ICdkYXknIDogJ3dlZWsnfVxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge319XG4gICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICB3aWR0aDogYWN0aXZlRHJhZ0RhdGEud2lkdGgsXG4gICAgICAgICAgICAgIGhlaWdodDogYWN0aXZlRHJhZ0RhdGEuaGVpZ2h0LFxuICAgICAgICAgICAgfX1cbiAgICAgICAgICAvPlxuICAgICAgICApIDogYWN0aXZlRHJhZ0RhdGEgJiYgYWN0aXZlRHJhZ0RhdGEudHlwZSA9PT0gJ2V2ZW50JyA/IChcbiAgICAgICAgICA8Q2FsZW5kYXJFdmVudEl0ZW1cbiAgICAgICAgICAgIGV2ZW50PXthY3RpdmVEcmFnRGF0YS5wYXlsb2FkIGFzIENhbGVuZGFyRXZlbnR9XG4gICAgICAgICAgICB2aWV3PSdtb250aCdcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHt9fVxuICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgd2lkdGg6IGFjdGl2ZURyYWdEYXRhLndpZHRoLFxuICAgICAgICAgICAgICBoZWlnaHQ6IGFjdGl2ZURyYWdEYXRhLmhlaWdodCxcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgLz5cbiAgICAgICAgKSA6IG51bGx9XG4gICAgICA8L0RyYWdPdmVybGF5PlxuICAgPC9EbmRDb250ZXh0PlxuICAgPC9kaXY+XG4gIDwvZGl2PlxuICApO1xufTsiXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZVJlZiIsInVzZUVmZmVjdCIsInVzZVdvcmtzcGFjZSIsIk1hdGNoIiwiUGFnZUxvYWRlciIsInVzZVBhZ2UiLCJ1c2VNYXliZVNoYXJlZCIsInVzZU1heWJlVGVtcGxhdGUiLCJ1c2VWaWV3cyIsInVzZVZpZXdGaWx0ZXJpbmciLCJ1c2VWaWV3U2VsZWN0aW9uIiwiZmlsdGVyQW5kU29ydFJlY29yZHMiLCJzZWFyY2hGaWx0ZXJlZFJlY29yZHMiLCJDYWxlbmRhciIsIkJ1dHRvbiIsIkFuZ2xlTGVmdEljb24iLCJBbmdsZVJpZ2h0SWNvbiIsIlBsdXNJY29uIiwiTWFnbmlmeWluZ0dsYXNzSWNvbiIsImZvcm1hdCIsImFkZERheXMiLCJhZGRNb250aHMiLCJzdWJNb250aHMiLCJhZGRXZWVrcyIsInN1YldlZWtzIiwic3RhcnRPZkRheSIsIklucHV0IiwidG9hc3QiLCJjbiIsInVzZVNjcmVlblNpemUiLCJ1c2VNYXliZVJlY29yZCIsInVzZVN0YWNrZWRQZWVrIiwiRGF5VmlldyIsIldlZWtWaWV3IiwiTW9udGhWaWV3IiwiQ2FsZW5kYXJFdmVudEl0ZW0iLCJnZXREYXRhYmFzZVRpdGxlQ29sIiwiZ2V0UmVjb3JkVGl0bGUiLCJDdXN0b21TZWxlY3QiLCJEbmRDb250ZXh0IiwiRHJhZ092ZXJsYXkiLCJQb2ludGVyU2Vuc29yIiwiVG91Y2hTZW5zb3IiLCJ1c2VTZW5zb3IiLCJ1c2VTZW5zb3JzIiwiQ2FsZW5kYXJFdmVudFNlZ21lbnQiLCJkaWZmZXJlbmNlSW5EYXlzIiwiZGlmZmVyZW5jZUluTWlsbGlzZWNvbmRzIiwicmVzdHJpY3RUb0NhbGVuZGFyQ29udGFpbmVyIiwidHJhbnNmb3JtIiwiZHJhZ2dpbmdOb2RlUmVjdCIsIndpbmRvd1JlY3QiLCJjYWxlbmRhckNvbnRhaW5lciIsImRvY3VtZW50IiwicXVlcnlTZWxlY3RvciIsImNvbnRhaW5lclJlY3QiLCJnZXRCb3VuZGluZ0NsaWVudFJlY3QiLCJtaW5YIiwibGVmdCIsIm1heFgiLCJyaWdodCIsIndpZHRoIiwibWluWSIsInRvcCIsIm1heFkiLCJib3R0b20iLCJoZWlnaHQiLCJjdXJyZW50WCIsIngiLCJjdXJyZW50WSIsInkiLCJjb25zdHJhaW5lZFgiLCJNYXRoIiwibWluIiwibWF4IiwiY29uc3RyYWluZWRZIiwidXNlUHJldmlvdXMiLCJ2YWx1ZSIsInJlZiIsImN1cnJlbnQiLCJDYWxlbmRhclZpZXciLCJwcm9wcyIsImRhdGFiYXNlU3RvcmUiLCJtZW1iZXJzIiwid29ya3NwYWNlIiwiZGVmaW5pdGlvbiIsImFjY2Vzc0xldmVsIiwiaXNNb2JpbGUiLCJtYXliZVJlY29yZCIsIm1heWJlU2hhcmVkIiwibWF5YmVUZW1wbGF0ZSIsInNlbGVjdGVkRGF0ZSIsInNldFNlbGVjdGVkRGF0ZSIsIkRhdGUiLCJ2aWV3VHlwZSIsInNldFZpZXdUeXBlIiwic2VsZWN0ZWRFdmVudCIsInNldFNlbGVjdGVkRXZlbnQiLCJzZWFyY2hUZXJtIiwic2V0U2VhcmNoVGVybSIsInNob3dTaWRlQ2FsZW5kYXIiLCJzZXRTaG93U2lkZUNhbGVuZGFyIiwiYWN0aXZlRHJhZ0RhdGEiLCJzZXRBY3RpdmVEcmFnRGF0YSIsInNhdmVkU2Nyb2xsVG9wIiwicG9pbnRlckNvb3JkaW5hdGVzIiwiaXNJblJlY29yZFRhYiIsImZpbHRlciIsImNvbmRpdGlvbnMiLCJtYXRjaCIsIkFsbCIsInNvcnRzIiwiZGF0YWJhc2VJZCIsImRhdGFiYXNlIiwiaXNQdWJsaXNoZWRWaWV3IiwiZWRpdGFibGUiLCJsb2NrQ29udGVudCIsImNhbkVkaXRTdHJ1Y3R1cmUiLCJjYW5FZGl0RGF0YSIsImNyZWF0ZVJlY29yZHMiLCJ1cGRhdGVSZWNvcmRWYWx1ZXMiLCJzZXRQZWVrUmVjb3JkSWQiLCJwZWVrUmVjb3JkSWQiLCJyZWZyZXNoRGF0YWJhc2UiLCJkZWxldGVSZWNvcmRzIiwic2VhcmNoIiwic2VsZWN0ZWRJZHMiLCJzZXRTZWxlY3RlZElkcyIsInByZXZQZWVrUmVjb3JkSWQiLCJvcGVuUmVjb3JkIiwic2Vuc29ycyIsImFjdGl2YXRpb25Db25zdHJhaW50IiwiZGlzdGFuY2UiLCJkZWxheSIsInRvbGVyYW5jZSIsImNvbnRhaW5lcklkIiwiY29udGFpbmVyIiwiZ2V0RWxlbWVudEJ5SWQiLCJyZXF1ZXN0QW5pbWF0aW9uRnJhbWUiLCJzY3JvbGxUb3AiLCJjb25zb2xlIiwibG9nIiwic2l6ZSIsImdldEV2ZW50cyIsInJvd3MiLCJsZW5ndGgiLCJ3b3Jrc3BhY2VNZW1iZXIiLCJ1c2VySWQiLCJpZCIsImZpbHRlcmVkUm93cyIsInRpdGxlQ29sT3B0cyIsIm1hcCIsInJvdyIsInN0YXJ0VmFsdWUiLCJwcm9jZXNzZWRSZWNvcmQiLCJwcm9jZXNzZWRSZWNvcmRWYWx1ZXMiLCJldmVudFN0YXJ0Q29sdW1uSWQiLCJzdGFydERhdGUiLCJlbmREYXRlIiwiZXZlbnRFbmRDb2x1bW5JZCIsImVuZFZhbHVlIiwiZ2V0VGltZSIsImRlZmF1bHREdXJhdGlvbiIsInRpdGxlIiwicmVjb3JkIiwidGl0bGVDb2xJZCIsImRlZmF1bHRUaXRsZSIsImlzQ29udGFjdHMiLCJzdGFydCIsImVuZCIsImdldEZpbHRlcmVkRXZlbnRzIiwiYmFzZUV2ZW50cyIsInRyaW0iLCJldmVudCIsInNlYXJjaExvd2VyIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsIm9uRHJhZ1N0YXJ0IiwiYWN0aXZlIiwiZGF0YSIsImluaXRpYWxQb2ludGVyWSIsImluaXRpYWxFdmVudFRvcCIsInJlY3QiLCJ0cmFuc2xhdGVkIiwiZ3JhYk9mZnNldFkiLCJhZGRFdmVudExpc3RlbmVyIiwiaGFuZGxlTW91c2VNb3ZlIiwiaGFuZGxlVG91Y2hNb3ZlIiwib25EcmFnRW5kIiwib3ZlciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJhY3RpdmVEYXRhIiwib3ZlckRhdGEiLCJwYXlsb2FkIiwidHlwZSIsImV2ZW50VG9VcGRhdGUiLCJvcmlnaW5hbEV2ZW50Iiwib3JpZ2luYWxTdGFydCIsIm9yaWdpbmFsRW5kIiwiZHVyYXRpb24iLCJuZXdTdGFydCIsInN0YXJ0c1dpdGgiLCJkYXlEaWZmZXJlbmNlIiwiZGF0ZSIsInNldEhvdXJzIiwic2xvdFRvcCIsInBvaW50ZXJZIiwiYWRqdXN0ZWRQb2ludGVyWSIsIm9mZnNldFkiLCJzbG90SGVpZ2h0IiwibWludXRlc0Zyb21Ub3AiLCJzbmFwcGVkTWludXRlcyIsInJvdW5kIiwibmV3TWludXRlcyIsImhvdXIiLCJnZXRTZWNvbmRzIiwiZ2V0TWlsbGlzZWNvbmRzIiwibmV3RW5kIiwicmVjb3JkSWQiLCJuZXdWYWx1ZXMiLCJ0b0lTT1N0cmluZyIsInN1Y2Nlc3MiLCJlcnJvciIsImNsaWVudFgiLCJjbGllbnRZIiwidG91Y2giLCJ0b3VjaGVzIiwiZXZlbnRzIiwiZ29Ub1RvZGF5IiwiZ29Ub1ByZXZpb3VzIiwicHJldkRhdGUiLCJnb1RvTmV4dCIsImhhbmRsZVJlcXVlc3RDcmVhdGVFdmVudCIsInVzZVN5c3RlbVRpbWUiLCJub3ciLCJuZXdEYXRlIiwiZ2V0SG91cnMiLCJnZXRNaW51dGVzIiwiaGFuZGxlQ3JlYXRlRXZlbnQiLCJzdGFydFRpbWUiLCJlbmRUaW1lIiwicmVjb3JkVmFsdWVzIiwicmVzdWx0IiwicmVjb3JkcyIsIm5ld1JlY29yZElkIiwiaGFuZGxlRXZlbnRDbGljayIsImdldEhlYWRlckRhdGVEaXNwbGF5IiwiZ2V0RGF5IiwiaGFuZGxlRXZlbnREZWxldGUiLCJ2aWV3VHlwZU9wdGlvbnMiLCJzZWxlY3RlZFZpZXdPcHRpb24iLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsInZhcmlhbnQiLCJvbkNsaWNrIiwib3B0aW9ucyIsIm9uQ2hhbmdlIiwic2VsZWN0ZWQiLCJwbGFjZWhvbGRlciIsImhpZGVTZWFyY2giLCJlIiwidGFyZ2V0Iiwic3BhbiIsImgzIiwibW9kZSIsIm9uU2VsZWN0IiwibW9kaWZpZXJzIiwiZGF0YS1jYWxlbmRhci1jb250ZW50Iiwib3BlbkFkZEV2ZW50Rm9ybSIsImRyb3BBbmltYXRpb24iLCJzZWdtZW50IiwidmlldyIsInN0eWxlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx\n"));

/***/ })

});