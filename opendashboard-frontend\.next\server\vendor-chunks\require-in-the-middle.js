"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/require-in-the-middle";
exports.ids = ["vendor-chunks/require-in-the-middle"];
exports.modules = {

/***/ "(ssr)/./node_modules/require-in-the-middle/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/require-in-the-middle/index.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst path = __webpack_require__(/*! path */ \"path\")\nconst Module = __webpack_require__(/*! module */ \"module\")\nconst debug = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/debug/src/index.js\")('require-in-the-middle')\nconst moduleDetailsFromPath = __webpack_require__(/*! module-details-from-path */ \"(ssr)/./node_modules/module-details-from-path/index.js\")\n\n// Using the default export is discouraged, but kept for backward compatibility.\n// Use this instead:\n//    const { Hook } = require('require-in-the-middle')\nmodule.exports = Hook\nmodule.exports.Hook = Hook\n\nlet builtinModules // Set<string>\n\n/**\n * Is the given module a \"core\" module?\n * https://nodejs.org/api/modules.html#core-modules\n *\n * @type {(moduleName: string) => boolean}\n */\nlet isCore\nif (Module.isBuiltin) { // Added in node v18.6.0, v16.17.0\n  isCore = Module.isBuiltin\n} else if (Module.builtinModules) { // Added in node v9.3.0, v8.10.0, v6.13.0\n  isCore = moduleName => {\n    if (moduleName.startsWith('node:')) {\n      return true\n    }\n\n    if (builtinModules === undefined) {\n      builtinModules = new Set(Module.builtinModules)\n    }\n\n    return builtinModules.has(moduleName)\n  }\n} else {\n  const _resolve = __webpack_require__(/*! resolve */ \"(ssr)/./node_modules/resolve/index.js\")\n  const [major, minor] = process.versions.node.split('.').map(Number)\n  if (major === 8 && minor < 8) {\n    // For node versions `[8.0, 8.8)` the \"http2\" module was built-in but\n    // behind the `--expose-http2` flag. `resolve` only considers unflagged\n    // modules to be core: https://github.com/browserify/resolve/issues/139\n    // However, for `ExportsCache` to work for \"http2\" we need it to be\n    // considered core.\n    isCore = moduleName => {\n      if (moduleName === 'http2') {\n        return true\n      }\n      // Prefer `resolve.core` lookup to `resolve.isCore(moduleName)` because\n      // the latter is doing version range matches for every call.\n      return !!_resolve.core[moduleName]\n    }\n  } else {\n    isCore = moduleName => {\n      // Prefer `resolve.core` lookup to `resolve.isCore(moduleName)` because\n      // the latter is doing version range matches for every call.\n      return !!_resolve.core[moduleName]\n    }\n  }\n}\n\nlet _resolve\n\nfunction resolve (moduleName, basedir) {\n  // Feature detection: This property was added in Node.js 8.9.0, the same time\n  // as the `paths` options argument was added to the `require.resolve` function,\n  // which is the one we want\n  if (!_resolve) {\n    // require.resolve might be undefined when using Node SEA mode:\n    // https://nodejs.org/api/single-executable-applications.html\n    // Also see https://github.com/nodejs/require-in-the-middle/issues/105\n    if ( true && __webpack_require__(\"(ssr)/./node_modules/require-in-the-middle sync recursive\").resolve.paths) {\n      _resolve = function (moduleName, basedir) {\n        return __webpack_require__(\"(ssr)/./node_modules/require-in-the-middle sync recursive\").resolve(moduleName, { paths: [basedir] })\n      }\n    } else {\n      const resolve = __webpack_require__(/*! resolve */ \"(ssr)/./node_modules/resolve/index.js\")\n      _resolve = function (moduleName, basedir) {\n        return resolve.sync(moduleName, { basedir })\n      }\n    }\n  }\n\n  return _resolve(moduleName, basedir)\n}\n\n// 'foo/bar.js' or 'foo/bar/index.js' => 'foo/bar'\nconst normalize = /([/\\\\]index)?(\\.js)?$/\n\n// Cache `onrequire`-patched exports for modules.\n//\n// Exports for built-in (a.k.a. \"core\") modules are stored in an internal Map.\n//\n// Exports for non-core modules are stored on a private field on the `Module`\n// object in `require.cache`. This allows users to delete from `require.cache`\n// to trigger a re-load (and re-run of the hook's `onrequire`) of a module the\n// next time it is required.\n// https://nodejs.org/docs/latest/api/all.html#all_modules_requirecache\n//\n// In some special cases -- e.g. some other `require()` hook swapping out\n// `Module._cache` like `@babel/register` -- a non-core module won't be in\n// `require.cache`. In that case this falls back to caching on the internal Map.\nclass ExportsCache {\n  constructor () {\n    this._localCache = new Map() // <module filename or id> -> <exports>\n    this._kRitmExports = Symbol('RitmExports')\n  }\n\n  has (filename, isBuiltin) {\n    if (this._localCache.has(filename)) {\n      return true\n    } else if (!isBuiltin) {\n      const mod = __webpack_require__.c[filename]\n      return !!(mod && this._kRitmExports in mod)\n    } else {\n      return false\n    }\n  }\n\n  get (filename, isBuiltin) {\n    const cachedExports = this._localCache.get(filename)\n    if (cachedExports !== undefined) {\n      return cachedExports\n    } else if (!isBuiltin) {\n      const mod = __webpack_require__.c[filename]\n      return (mod && mod[this._kRitmExports])\n    }\n  }\n\n  set (filename, exports, isBuiltin) {\n    if (isBuiltin) {\n      this._localCache.set(filename, exports)\n    } else if (filename in __webpack_require__.c) {\n      __webpack_require__.c[filename][this._kRitmExports] = exports\n    } else {\n      debug('non-core module is unexpectedly not in require.cache: \"%s\"', filename)\n      this._localCache.set(filename, exports)\n    }\n  }\n}\n\nfunction Hook (modules, options, onrequire) {\n  if ((this instanceof Hook) === false) return new Hook(modules, options, onrequire)\n  if (typeof modules === 'function') {\n    onrequire = modules\n    modules = null\n    options = null\n  } else if (typeof options === 'function') {\n    onrequire = options\n    options = null\n  }\n\n  if (typeof Module._resolveFilename !== 'function') {\n    console.error('Error: Expected Module._resolveFilename to be a function (was: %s) - aborting!', typeof Module._resolveFilename)\n    console.error('Please report this error as an issue related to Node.js %s at %s', process.version, (__webpack_require__(/*! ./package.json */ \"(ssr)/./node_modules/require-in-the-middle/package.json\").bugs.url))\n    return\n  }\n\n  this._cache = new ExportsCache()\n\n  this._unhooked = false\n  this._origRequire = Module.prototype.require\n\n  const self = this\n  const patching = new Set()\n  const internals = options ? options.internals === true : false\n  const hasWhitelist = Array.isArray(modules)\n\n  debug('registering require hook')\n\n  this._require = Module.prototype.require = function (id) {\n    if (self._unhooked === true) {\n      // if the patched require function could not be removed because\n      // someone else patched it after it was patched here, we just\n      // abort and pass the request onwards to the original require\n      debug('ignoring require call - module is soft-unhooked')\n      return self._origRequire.apply(this, arguments)\n    }\n\n    return patchedRequire.call(this, arguments, false)\n  }\n\n  if (typeof process.getBuiltinModule === 'function') {\n    this._origGetBuiltinModule = process.getBuiltinModule\n    this._getBuiltinModule = process.getBuiltinModule = function (id) {\n      if (self._unhooked === true) {\n        // if the patched process.getBuiltinModule function could not be removed because\n        // someone else patched it after it was patched here, we just abort and pass the\n        // request onwards to the original process.getBuiltinModule\n        debug('ignoring process.getBuiltinModule call - module is soft-unhooked')\n        return self._origGetBuiltinModule.apply(this, arguments)\n      }\n\n      return patchedRequire.call(this, arguments, true)\n    }\n  }\n\n  // Preserve the original require/process.getBuiltinModule arguments in `args`\n  function patchedRequire (args, coreOnly) {\n    const id = args[0]\n    const core = isCore(id)\n    let filename // the string used for caching\n    if (core) {\n      filename = id\n      // If this is a builtin module that can be identified both as 'foo' and\n      // 'node:foo', then prefer 'foo' as the caching key.\n      if (id.startsWith('node:')) {\n        const idWithoutPrefix = id.slice(5)\n        if (isCore(idWithoutPrefix)) {\n          filename = idWithoutPrefix\n        }\n      }\n    } else if (coreOnly) {\n      // `coreOnly` is `true` if this was a call to `process.getBuiltinModule`, in which case\n      // we don't want to return anything if the requested `id` isn't a core module. Falling\n      // back to default behaviour, which at the time of this wrting is simply returning `undefined`\n      debug('call to process.getBuiltinModule with unknown built-in id')\n      return self._origGetBuiltinModule.apply(this, args)\n    } else {\n      try {\n        filename = Module._resolveFilename(id, this)\n      } catch (resolveErr) {\n        // If someone *else* monkey-patches before this monkey-patch, then that\n        // code might expect `require(someId)` to get through so it can be\n        // handled, even if `someId` cannot be resolved to a filename. In this\n        // case, instead of throwing we defer to the underlying `require`.\n        //\n        // For example the Azure Functions Node.js worker module does this,\n        // where `@azure/functions-core` resolves to an internal object.\n        // https://github.com/Azure/azure-functions-nodejs-worker/blob/v3.5.2/src/setupCoreModule.ts#L46-L54\n        debug('Module._resolveFilename(\"%s\") threw %j, calling original Module.require', id, resolveErr.message)\n        return self._origRequire.apply(this, args)\n      }\n    }\n\n    let moduleName, basedir\n\n    debug('processing %s module require(\\'%s\\'): %s', core === true ? 'core' : 'non-core', id, filename)\n\n    // return known patched modules immediately\n    if (self._cache.has(filename, core) === true) {\n      debug('returning already patched cached module: %s', filename)\n      return self._cache.get(filename, core)\n    }\n\n    // Check if this module has a patcher in-progress already.\n    // Otherwise, mark this module as patching in-progress.\n    const isPatching = patching.has(filename)\n    if (isPatching === false) {\n      patching.add(filename)\n    }\n\n    const exports = coreOnly\n      ? self._origGetBuiltinModule.apply(this, args)\n      : self._origRequire.apply(this, args)\n\n    // If it's already patched, just return it as-is.\n    if (isPatching === true) {\n      debug('module is in the process of being patched already - ignoring: %s', filename)\n      return exports\n    }\n\n    // The module has already been loaded,\n    // so the patching mark can be cleaned up.\n    patching.delete(filename)\n\n    if (core === true) {\n      if (hasWhitelist === true && modules.includes(filename) === false) {\n        debug('ignoring core module not on whitelist: %s', filename)\n        return exports // abort if module name isn't on whitelist\n      }\n      moduleName = filename\n    } else if (hasWhitelist === true && modules.includes(filename)) {\n      // whitelist includes the absolute path to the file including extension\n      const parsedPath = path.parse(filename)\n      moduleName = parsedPath.name\n      basedir = parsedPath.dir\n    } else {\n      const stat = moduleDetailsFromPath(filename)\n      if (stat === undefined) {\n        debug('could not parse filename: %s', filename)\n        return exports // abort if filename could not be parsed\n      }\n      moduleName = stat.name\n      basedir = stat.basedir\n\n      // Ex: require('foo/lib/../bar.js')\n      // moduleName = 'foo'\n      // fullModuleName = 'foo/bar'\n      const fullModuleName = resolveModuleName(stat)\n\n      debug('resolved filename to module: %s (id: %s, resolved: %s, basedir: %s)', moduleName, id, fullModuleName, basedir)\n\n      let matchFound = false\n      if (hasWhitelist) {\n        if (!id.startsWith('.') && modules.includes(id)) {\n          // Not starting with '.' means `id` is identifying a module path,\n          // as opposed to a local file path. (Note: I'm not sure about\n          // absolute paths, but those are handled above.)\n          // If this `id` is in `modules`, then this could be a match to an\n          // package \"exports\" entry point that wouldn't otherwise match below.\n          moduleName = id\n          matchFound = true\n        }\n\n        // abort if module name isn't on whitelist\n        if (!modules.includes(moduleName) && !modules.includes(fullModuleName)) {\n          return exports\n        }\n\n        if (modules.includes(fullModuleName) && fullModuleName !== moduleName) {\n          // if we get to this point, it means that we're requiring a whitelisted sub-module\n          moduleName = fullModuleName\n          matchFound = true\n        }\n      }\n\n      if (!matchFound) {\n        // figure out if this is the main module file, or a file inside the module\n        let res\n        try {\n          res = resolve(moduleName, basedir)\n        } catch (e) {\n          debug('could not resolve module: %s', moduleName)\n          self._cache.set(filename, exports, core)\n          return exports // abort if module could not be resolved (e.g. no main in package.json and no index.js file)\n        }\n\n        if (res !== filename) {\n          // this is a module-internal file\n          if (internals === true) {\n            // use the module-relative path to the file, prefixed by original module name\n            moduleName = moduleName + path.sep + path.relative(basedir, filename)\n            debug('preparing to process require of internal file: %s', moduleName)\n          } else {\n            debug('ignoring require of non-main module file: %s', res)\n            self._cache.set(filename, exports, core)\n            return exports // abort if not main module file\n          }\n        }\n      }\n    }\n\n    // ensure that the cache entry is assigned a value before calling\n    // onrequire, in case calling onrequire requires the same module.\n    self._cache.set(filename, exports, core)\n    debug('calling require hook: %s', moduleName)\n    const patchedExports = onrequire(exports, moduleName, basedir)\n    self._cache.set(filename, patchedExports, core)\n\n    debug('returning module: %s', moduleName)\n    return patchedExports\n  }\n}\n\nHook.prototype.unhook = function () {\n  this._unhooked = true\n\n  if (this._require === Module.prototype.require) {\n    Module.prototype.require = this._origRequire\n    debug('require unhook successful')\n  } else {\n    debug('require unhook unsuccessful')\n  }\n\n  if (process.getBuiltinModule !== undefined) {\n    if (this._getBuiltinModule === process.getBuiltinModule) {\n      process.getBuiltinModule = this._origGetBuiltinModule\n      debug('process.getBuiltinModule unhook successful')\n    } else {\n      debug('process.getBuiltinModule unhook unsuccessful')\n    }\n  }\n}\n\nfunction resolveModuleName (stat) {\n  const normalizedPath = path.sep !== '/' ? stat.path.split(path.sep).join('/') : stat.path\n  return path.posix.join(stat.name, normalizedPath).replace(normalize, '')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/require-in-the-middle/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/require-in-the-middle/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/require-in-the-middle/index.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst path = __webpack_require__(/*! path */ \"path\")\nconst Module = __webpack_require__(/*! module */ \"module\")\nconst debug = __webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\")('require-in-the-middle')\nconst moduleDetailsFromPath = __webpack_require__(/*! module-details-from-path */ \"(rsc)/./node_modules/module-details-from-path/index.js\")\n\n// Using the default export is discouraged, but kept for backward compatibility.\n// Use this instead:\n//    const { Hook } = require('require-in-the-middle')\nmodule.exports = Hook\nmodule.exports.Hook = Hook\n\nlet builtinModules // Set<string>\n\n/**\n * Is the given module a \"core\" module?\n * https://nodejs.org/api/modules.html#core-modules\n *\n * @type {(moduleName: string) => boolean}\n */\nlet isCore\nif (Module.isBuiltin) { // Added in node v18.6.0, v16.17.0\n  isCore = Module.isBuiltin\n} else if (Module.builtinModules) { // Added in node v9.3.0, v8.10.0, v6.13.0\n  isCore = moduleName => {\n    if (moduleName.startsWith('node:')) {\n      return true\n    }\n\n    if (builtinModules === undefined) {\n      builtinModules = new Set(Module.builtinModules)\n    }\n\n    return builtinModules.has(moduleName)\n  }\n} else {\n  const _resolve = __webpack_require__(/*! resolve */ \"(rsc)/./node_modules/resolve/index.js\")\n  const [major, minor] = process.versions.node.split('.').map(Number)\n  if (major === 8 && minor < 8) {\n    // For node versions `[8.0, 8.8)` the \"http2\" module was built-in but\n    // behind the `--expose-http2` flag. `resolve` only considers unflagged\n    // modules to be core: https://github.com/browserify/resolve/issues/139\n    // However, for `ExportsCache` to work for \"http2\" we need it to be\n    // considered core.\n    isCore = moduleName => {\n      if (moduleName === 'http2') {\n        return true\n      }\n      // Prefer `resolve.core` lookup to `resolve.isCore(moduleName)` because\n      // the latter is doing version range matches for every call.\n      return !!_resolve.core[moduleName]\n    }\n  } else {\n    isCore = moduleName => {\n      // Prefer `resolve.core` lookup to `resolve.isCore(moduleName)` because\n      // the latter is doing version range matches for every call.\n      return !!_resolve.core[moduleName]\n    }\n  }\n}\n\nlet _resolve\n\nfunction resolve (moduleName, basedir) {\n  // Feature detection: This property was added in Node.js 8.9.0, the same time\n  // as the `paths` options argument was added to the `require.resolve` function,\n  // which is the one we want\n  if (!_resolve) {\n    // require.resolve might be undefined when using Node SEA mode:\n    // https://nodejs.org/api/single-executable-applications.html\n    // Also see https://github.com/nodejs/require-in-the-middle/issues/105\n    if ( true && __webpack_require__(\"(rsc)/./node_modules/require-in-the-middle sync recursive\").resolve.paths) {\n      _resolve = function (moduleName, basedir) {\n        return __webpack_require__(\"(rsc)/./node_modules/require-in-the-middle sync recursive\").resolve(moduleName, { paths: [basedir] })\n      }\n    } else {\n      const resolve = __webpack_require__(/*! resolve */ \"(rsc)/./node_modules/resolve/index.js\")\n      _resolve = function (moduleName, basedir) {\n        return resolve.sync(moduleName, { basedir })\n      }\n    }\n  }\n\n  return _resolve(moduleName, basedir)\n}\n\n// 'foo/bar.js' or 'foo/bar/index.js' => 'foo/bar'\nconst normalize = /([/\\\\]index)?(\\.js)?$/\n\n// Cache `onrequire`-patched exports for modules.\n//\n// Exports for built-in (a.k.a. \"core\") modules are stored in an internal Map.\n//\n// Exports for non-core modules are stored on a private field on the `Module`\n// object in `require.cache`. This allows users to delete from `require.cache`\n// to trigger a re-load (and re-run of the hook's `onrequire`) of a module the\n// next time it is required.\n// https://nodejs.org/docs/latest/api/all.html#all_modules_requirecache\n//\n// In some special cases -- e.g. some other `require()` hook swapping out\n// `Module._cache` like `@babel/register` -- a non-core module won't be in\n// `require.cache`. In that case this falls back to caching on the internal Map.\nclass ExportsCache {\n  constructor () {\n    this._localCache = new Map() // <module filename or id> -> <exports>\n    this._kRitmExports = Symbol('RitmExports')\n  }\n\n  has (filename, isBuiltin) {\n    if (this._localCache.has(filename)) {\n      return true\n    } else if (!isBuiltin) {\n      const mod = __webpack_require__.c[filename]\n      return !!(mod && this._kRitmExports in mod)\n    } else {\n      return false\n    }\n  }\n\n  get (filename, isBuiltin) {\n    const cachedExports = this._localCache.get(filename)\n    if (cachedExports !== undefined) {\n      return cachedExports\n    } else if (!isBuiltin) {\n      const mod = __webpack_require__.c[filename]\n      return (mod && mod[this._kRitmExports])\n    }\n  }\n\n  set (filename, exports, isBuiltin) {\n    if (isBuiltin) {\n      this._localCache.set(filename, exports)\n    } else if (filename in __webpack_require__.c) {\n      __webpack_require__.c[filename][this._kRitmExports] = exports\n    } else {\n      debug('non-core module is unexpectedly not in require.cache: \"%s\"', filename)\n      this._localCache.set(filename, exports)\n    }\n  }\n}\n\nfunction Hook (modules, options, onrequire) {\n  if ((this instanceof Hook) === false) return new Hook(modules, options, onrequire)\n  if (typeof modules === 'function') {\n    onrequire = modules\n    modules = null\n    options = null\n  } else if (typeof options === 'function') {\n    onrequire = options\n    options = null\n  }\n\n  if (typeof Module._resolveFilename !== 'function') {\n    console.error('Error: Expected Module._resolveFilename to be a function (was: %s) - aborting!', typeof Module._resolveFilename)\n    console.error('Please report this error as an issue related to Node.js %s at %s', process.version, (__webpack_require__(/*! ./package.json */ \"(rsc)/./node_modules/require-in-the-middle/package.json\").bugs.url))\n    return\n  }\n\n  this._cache = new ExportsCache()\n\n  this._unhooked = false\n  this._origRequire = Module.prototype.require\n\n  const self = this\n  const patching = new Set()\n  const internals = options ? options.internals === true : false\n  const hasWhitelist = Array.isArray(modules)\n\n  debug('registering require hook')\n\n  this._require = Module.prototype.require = function (id) {\n    if (self._unhooked === true) {\n      // if the patched require function could not be removed because\n      // someone else patched it after it was patched here, we just\n      // abort and pass the request onwards to the original require\n      debug('ignoring require call - module is soft-unhooked')\n      return self._origRequire.apply(this, arguments)\n    }\n\n    return patchedRequire.call(this, arguments, false)\n  }\n\n  if (typeof process.getBuiltinModule === 'function') {\n    this._origGetBuiltinModule = process.getBuiltinModule\n    this._getBuiltinModule = process.getBuiltinModule = function (id) {\n      if (self._unhooked === true) {\n        // if the patched process.getBuiltinModule function could not be removed because\n        // someone else patched it after it was patched here, we just abort and pass the\n        // request onwards to the original process.getBuiltinModule\n        debug('ignoring process.getBuiltinModule call - module is soft-unhooked')\n        return self._origGetBuiltinModule.apply(this, arguments)\n      }\n\n      return patchedRequire.call(this, arguments, true)\n    }\n  }\n\n  // Preserve the original require/process.getBuiltinModule arguments in `args`\n  function patchedRequire (args, coreOnly) {\n    const id = args[0]\n    const core = isCore(id)\n    let filename // the string used for caching\n    if (core) {\n      filename = id\n      // If this is a builtin module that can be identified both as 'foo' and\n      // 'node:foo', then prefer 'foo' as the caching key.\n      if (id.startsWith('node:')) {\n        const idWithoutPrefix = id.slice(5)\n        if (isCore(idWithoutPrefix)) {\n          filename = idWithoutPrefix\n        }\n      }\n    } else if (coreOnly) {\n      // `coreOnly` is `true` if this was a call to `process.getBuiltinModule`, in which case\n      // we don't want to return anything if the requested `id` isn't a core module. Falling\n      // back to default behaviour, which at the time of this wrting is simply returning `undefined`\n      debug('call to process.getBuiltinModule with unknown built-in id')\n      return self._origGetBuiltinModule.apply(this, args)\n    } else {\n      try {\n        filename = Module._resolveFilename(id, this)\n      } catch (resolveErr) {\n        // If someone *else* monkey-patches before this monkey-patch, then that\n        // code might expect `require(someId)` to get through so it can be\n        // handled, even if `someId` cannot be resolved to a filename. In this\n        // case, instead of throwing we defer to the underlying `require`.\n        //\n        // For example the Azure Functions Node.js worker module does this,\n        // where `@azure/functions-core` resolves to an internal object.\n        // https://github.com/Azure/azure-functions-nodejs-worker/blob/v3.5.2/src/setupCoreModule.ts#L46-L54\n        debug('Module._resolveFilename(\"%s\") threw %j, calling original Module.require', id, resolveErr.message)\n        return self._origRequire.apply(this, args)\n      }\n    }\n\n    let moduleName, basedir\n\n    debug('processing %s module require(\\'%s\\'): %s', core === true ? 'core' : 'non-core', id, filename)\n\n    // return known patched modules immediately\n    if (self._cache.has(filename, core) === true) {\n      debug('returning already patched cached module: %s', filename)\n      return self._cache.get(filename, core)\n    }\n\n    // Check if this module has a patcher in-progress already.\n    // Otherwise, mark this module as patching in-progress.\n    const isPatching = patching.has(filename)\n    if (isPatching === false) {\n      patching.add(filename)\n    }\n\n    const exports = coreOnly\n      ? self._origGetBuiltinModule.apply(this, args)\n      : self._origRequire.apply(this, args)\n\n    // If it's already patched, just return it as-is.\n    if (isPatching === true) {\n      debug('module is in the process of being patched already - ignoring: %s', filename)\n      return exports\n    }\n\n    // The module has already been loaded,\n    // so the patching mark can be cleaned up.\n    patching.delete(filename)\n\n    if (core === true) {\n      if (hasWhitelist === true && modules.includes(filename) === false) {\n        debug('ignoring core module not on whitelist: %s', filename)\n        return exports // abort if module name isn't on whitelist\n      }\n      moduleName = filename\n    } else if (hasWhitelist === true && modules.includes(filename)) {\n      // whitelist includes the absolute path to the file including extension\n      const parsedPath = path.parse(filename)\n      moduleName = parsedPath.name\n      basedir = parsedPath.dir\n    } else {\n      const stat = moduleDetailsFromPath(filename)\n      if (stat === undefined) {\n        debug('could not parse filename: %s', filename)\n        return exports // abort if filename could not be parsed\n      }\n      moduleName = stat.name\n      basedir = stat.basedir\n\n      // Ex: require('foo/lib/../bar.js')\n      // moduleName = 'foo'\n      // fullModuleName = 'foo/bar'\n      const fullModuleName = resolveModuleName(stat)\n\n      debug('resolved filename to module: %s (id: %s, resolved: %s, basedir: %s)', moduleName, id, fullModuleName, basedir)\n\n      let matchFound = false\n      if (hasWhitelist) {\n        if (!id.startsWith('.') && modules.includes(id)) {\n          // Not starting with '.' means `id` is identifying a module path,\n          // as opposed to a local file path. (Note: I'm not sure about\n          // absolute paths, but those are handled above.)\n          // If this `id` is in `modules`, then this could be a match to an\n          // package \"exports\" entry point that wouldn't otherwise match below.\n          moduleName = id\n          matchFound = true\n        }\n\n        // abort if module name isn't on whitelist\n        if (!modules.includes(moduleName) && !modules.includes(fullModuleName)) {\n          return exports\n        }\n\n        if (modules.includes(fullModuleName) && fullModuleName !== moduleName) {\n          // if we get to this point, it means that we're requiring a whitelisted sub-module\n          moduleName = fullModuleName\n          matchFound = true\n        }\n      }\n\n      if (!matchFound) {\n        // figure out if this is the main module file, or a file inside the module\n        let res\n        try {\n          res = resolve(moduleName, basedir)\n        } catch (e) {\n          debug('could not resolve module: %s', moduleName)\n          self._cache.set(filename, exports, core)\n          return exports // abort if module could not be resolved (e.g. no main in package.json and no index.js file)\n        }\n\n        if (res !== filename) {\n          // this is a module-internal file\n          if (internals === true) {\n            // use the module-relative path to the file, prefixed by original module name\n            moduleName = moduleName + path.sep + path.relative(basedir, filename)\n            debug('preparing to process require of internal file: %s', moduleName)\n          } else {\n            debug('ignoring require of non-main module file: %s', res)\n            self._cache.set(filename, exports, core)\n            return exports // abort if not main module file\n          }\n        }\n      }\n    }\n\n    // ensure that the cache entry is assigned a value before calling\n    // onrequire, in case calling onrequire requires the same module.\n    self._cache.set(filename, exports, core)\n    debug('calling require hook: %s', moduleName)\n    const patchedExports = onrequire(exports, moduleName, basedir)\n    self._cache.set(filename, patchedExports, core)\n\n    debug('returning module: %s', moduleName)\n    return patchedExports\n  }\n}\n\nHook.prototype.unhook = function () {\n  this._unhooked = true\n\n  if (this._require === Module.prototype.require) {\n    Module.prototype.require = this._origRequire\n    debug('require unhook successful')\n  } else {\n    debug('require unhook unsuccessful')\n  }\n\n  if (process.getBuiltinModule !== undefined) {\n    if (this._getBuiltinModule === process.getBuiltinModule) {\n      process.getBuiltinModule = this._origGetBuiltinModule\n      debug('process.getBuiltinModule unhook successful')\n    } else {\n      debug('process.getBuiltinModule unhook unsuccessful')\n    }\n  }\n}\n\nfunction resolveModuleName (stat) {\n  const normalizedPath = path.sep !== '/' ? stat.path.split(path.sep).join('/') : stat.path\n  return path.posix.join(stat.name, normalizedPath).replace(normalize, '')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/require-in-the-middle/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/require-in-the-middle/package.json":
/*!*********************************************************!*\
  !*** ./node_modules/require-in-the-middle/package.json ***!
  \*********************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"require-in-the-middle","version":"7.5.2","description":"Module to hook into the Node.js require function","main":"index.js","types":"types/index.d.ts","dependencies":{"debug":"^4.3.5","module-details-from-path":"^1.0.3","resolve":"^1.22.8"},"devDependencies":{"@babel/core":"^7.9.0","@babel/preset-env":"^7.9.5","@babel/preset-typescript":"^7.9.0","@babel/register":"^7.9.0","ipp-printer":"^1.0.0","patterns":"^1.0.3","roundround":"^0.2.0","semver":"^6.3.0","standard":"^14.3.1","tape":"^4.11.0"},"scripts":{"test":"npm run test:lint && npm run test:tape && npm run test:babel","test:lint":"standard","test:tape":"tape test/*.js","test:babel":"node test/babel/babel-register.js"},"repository":{"type":"git","url":"git+https://github.com/nodejs/require-in-the-middle.git"},"keywords":["require","hook","shim","shimmer","shimming","patch","monkey","monkeypatch","module","load"],"files":["types"],"author":"Thomas Watson Steen <<EMAIL>> (https://twitter.com/wa7son)","license":"MIT","bugs":{"url":"https://github.com/nodejs/require-in-the-middle/issues"},"homepage":"https://github.com/nodejs/require-in-the-middle#readme","engines":{"node":">=8.6.0"}}');

/***/ }),

/***/ "(ssr)/./node_modules/require-in-the-middle/package.json":
/*!*********************************************************!*\
  !*** ./node_modules/require-in-the-middle/package.json ***!
  \*********************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"require-in-the-middle","version":"7.5.2","description":"Module to hook into the Node.js require function","main":"index.js","types":"types/index.d.ts","dependencies":{"debug":"^4.3.5","module-details-from-path":"^1.0.3","resolve":"^1.22.8"},"devDependencies":{"@babel/core":"^7.9.0","@babel/preset-env":"^7.9.5","@babel/preset-typescript":"^7.9.0","@babel/register":"^7.9.0","ipp-printer":"^1.0.0","patterns":"^1.0.3","roundround":"^0.2.0","semver":"^6.3.0","standard":"^14.3.1","tape":"^4.11.0"},"scripts":{"test":"npm run test:lint && npm run test:tape && npm run test:babel","test:lint":"standard","test:tape":"tape test/*.js","test:babel":"node test/babel/babel-register.js"},"repository":{"type":"git","url":"git+https://github.com/nodejs/require-in-the-middle.git"},"keywords":["require","hook","shim","shimmer","shimming","patch","monkey","monkeypatch","module","load"],"files":["types"],"author":"Thomas Watson Steen <<EMAIL>> (https://twitter.com/wa7son)","license":"MIT","bugs":{"url":"https://github.com/nodejs/require-in-the-middle/issues"},"homepage":"https://github.com/nodejs/require-in-the-middle#readme","engines":{"node":">=8.6.0"}}');

/***/ })

};
;