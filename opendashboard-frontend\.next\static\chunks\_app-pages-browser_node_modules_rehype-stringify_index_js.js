"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_rehype-stringify_index_js"],{

/***/ "(app-pages-browser)/./node_modules/ccount/index.js":
/*!**************************************!*\
  !*** ./node_modules/ccount/index.js ***!
  \**************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ccount: function() { return /* binding */ ccount; }\n/* harmony export */ });\n/**\n * Count how often a character (or substring) is used in a string.\n *\n * @param {string} value\n *   Value to search in.\n * @param {string} character\n *   Character (or substring) to look for.\n * @return {number}\n *   Number of times `character` occurred in `value`.\n */\nfunction ccount(value, character) {\n  const source = String(value)\n\n  if (typeof character !== 'string') {\n    throw new TypeError('Expected character')\n  }\n\n  let count = 0\n  let index = source.indexOf(character)\n\n  while (index !== -1) {\n    count++\n    index = source.indexOf(character, index + character.length)\n  }\n\n  return count\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9jY291bnQvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQjtBQUNBLFdBQVcsUUFBUTtBQUNuQjtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ087QUFDUDs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9jY291bnQvaW5kZXguanM/MGUwMCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvdW50IGhvdyBvZnRlbiBhIGNoYXJhY3RlciAob3Igc3Vic3RyaW5nKSBpcyB1c2VkIGluIGEgc3RyaW5nLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSB2YWx1ZVxuICogICBWYWx1ZSB0byBzZWFyY2ggaW4uXG4gKiBAcGFyYW0ge3N0cmluZ30gY2hhcmFjdGVyXG4gKiAgIENoYXJhY3RlciAob3Igc3Vic3RyaW5nKSB0byBsb29rIGZvci5cbiAqIEByZXR1cm4ge251bWJlcn1cbiAqICAgTnVtYmVyIG9mIHRpbWVzIGBjaGFyYWN0ZXJgIG9jY3VycmVkIGluIGB2YWx1ZWAuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjY291bnQodmFsdWUsIGNoYXJhY3Rlcikge1xuICBjb25zdCBzb3VyY2UgPSBTdHJpbmcodmFsdWUpXG5cbiAgaWYgKHR5cGVvZiBjaGFyYWN0ZXIgIT09ICdzdHJpbmcnKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcignRXhwZWN0ZWQgY2hhcmFjdGVyJylcbiAgfVxuXG4gIGxldCBjb3VudCA9IDBcbiAgbGV0IGluZGV4ID0gc291cmNlLmluZGV4T2YoY2hhcmFjdGVyKVxuXG4gIHdoaWxlIChpbmRleCAhPT0gLTEpIHtcbiAgICBjb3VudCsrXG4gICAgaW5kZXggPSBzb3VyY2UuaW5kZXhPZihjaGFyYWN0ZXIsIGluZGV4ICsgY2hhcmFjdGVyLmxlbmd0aClcbiAgfVxuXG4gIHJldHVybiBjb3VudFxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/ccount/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/character-entities-html4/index.js":
/*!********************************************************!*\
  !*** ./node_modules/character-entities-html4/index.js ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   characterEntitiesHtml4: function() { return /* binding */ characterEntitiesHtml4; }\n/* harmony export */ });\n/**\n * Map of named character references from HTML 4.\n *\n * @type {Record<string, string>}\n */\nconst characterEntitiesHtml4 = {\n  nbsp: ' ',\n  iexcl: '¡',\n  cent: '¢',\n  pound: '£',\n  curren: '¤',\n  yen: '¥',\n  brvbar: '¦',\n  sect: '§',\n  uml: '¨',\n  copy: '©',\n  ordf: 'ª',\n  laquo: '«',\n  not: '¬',\n  shy: '­',\n  reg: '®',\n  macr: '¯',\n  deg: '°',\n  plusmn: '±',\n  sup2: '²',\n  sup3: '³',\n  acute: '´',\n  micro: 'µ',\n  para: '¶',\n  middot: '·',\n  cedil: '¸',\n  sup1: '¹',\n  ordm: 'º',\n  raquo: '»',\n  frac14: '¼',\n  frac12: '½',\n  frac34: '¾',\n  iquest: '¿',\n  Agrave: 'À',\n  Aacute: 'Á',\n  Acirc: 'Â',\n  Atilde: 'Ã',\n  Auml: 'Ä',\n  Aring: 'Å',\n  AElig: 'Æ',\n  Ccedil: 'Ç',\n  Egrave: 'È',\n  Eacute: 'É',\n  Ecirc: 'Ê',\n  Euml: 'Ë',\n  Igrave: 'Ì',\n  Iacute: 'Í',\n  Icirc: 'Î',\n  Iuml: 'Ï',\n  ETH: 'Ð',\n  Ntilde: 'Ñ',\n  Ograve: 'Ò',\n  Oacute: 'Ó',\n  Ocirc: 'Ô',\n  Otilde: 'Õ',\n  Ouml: 'Ö',\n  times: '×',\n  Oslash: 'Ø',\n  Ugrave: 'Ù',\n  Uacute: 'Ú',\n  Ucirc: 'Û',\n  Uuml: 'Ü',\n  Yacute: 'Ý',\n  THORN: 'Þ',\n  szlig: 'ß',\n  agrave: 'à',\n  aacute: 'á',\n  acirc: 'â',\n  atilde: 'ã',\n  auml: 'ä',\n  aring: 'å',\n  aelig: 'æ',\n  ccedil: 'ç',\n  egrave: 'è',\n  eacute: 'é',\n  ecirc: 'ê',\n  euml: 'ë',\n  igrave: 'ì',\n  iacute: 'í',\n  icirc: 'î',\n  iuml: 'ï',\n  eth: 'ð',\n  ntilde: 'ñ',\n  ograve: 'ò',\n  oacute: 'ó',\n  ocirc: 'ô',\n  otilde: 'õ',\n  ouml: 'ö',\n  divide: '÷',\n  oslash: 'ø',\n  ugrave: 'ù',\n  uacute: 'ú',\n  ucirc: 'û',\n  uuml: 'ü',\n  yacute: 'ý',\n  thorn: 'þ',\n  yuml: 'ÿ',\n  fnof: 'ƒ',\n  Alpha: 'Α',\n  Beta: 'Β',\n  Gamma: 'Γ',\n  Delta: 'Δ',\n  Epsilon: 'Ε',\n  Zeta: 'Ζ',\n  Eta: 'Η',\n  Theta: 'Θ',\n  Iota: 'Ι',\n  Kappa: 'Κ',\n  Lambda: 'Λ',\n  Mu: 'Μ',\n  Nu: 'Ν',\n  Xi: 'Ξ',\n  Omicron: 'Ο',\n  Pi: 'Π',\n  Rho: 'Ρ',\n  Sigma: 'Σ',\n  Tau: 'Τ',\n  Upsilon: 'Υ',\n  Phi: 'Φ',\n  Chi: 'Χ',\n  Psi: 'Ψ',\n  Omega: 'Ω',\n  alpha: 'α',\n  beta: 'β',\n  gamma: 'γ',\n  delta: 'δ',\n  epsilon: 'ε',\n  zeta: 'ζ',\n  eta: 'η',\n  theta: 'θ',\n  iota: 'ι',\n  kappa: 'κ',\n  lambda: 'λ',\n  mu: 'μ',\n  nu: 'ν',\n  xi: 'ξ',\n  omicron: 'ο',\n  pi: 'π',\n  rho: 'ρ',\n  sigmaf: 'ς',\n  sigma: 'σ',\n  tau: 'τ',\n  upsilon: 'υ',\n  phi: 'φ',\n  chi: 'χ',\n  psi: 'ψ',\n  omega: 'ω',\n  thetasym: 'ϑ',\n  upsih: 'ϒ',\n  piv: 'ϖ',\n  bull: '•',\n  hellip: '…',\n  prime: '′',\n  Prime: '″',\n  oline: '‾',\n  frasl: '⁄',\n  weierp: '℘',\n  image: 'ℑ',\n  real: 'ℜ',\n  trade: '™',\n  alefsym: 'ℵ',\n  larr: '←',\n  uarr: '↑',\n  rarr: '→',\n  darr: '↓',\n  harr: '↔',\n  crarr: '↵',\n  lArr: '⇐',\n  uArr: '⇑',\n  rArr: '⇒',\n  dArr: '⇓',\n  hArr: '⇔',\n  forall: '∀',\n  part: '∂',\n  exist: '∃',\n  empty: '∅',\n  nabla: '∇',\n  isin: '∈',\n  notin: '∉',\n  ni: '∋',\n  prod: '∏',\n  sum: '∑',\n  minus: '−',\n  lowast: '∗',\n  radic: '√',\n  prop: '∝',\n  infin: '∞',\n  ang: '∠',\n  and: '∧',\n  or: '∨',\n  cap: '∩',\n  cup: '∪',\n  int: '∫',\n  there4: '∴',\n  sim: '∼',\n  cong: '≅',\n  asymp: '≈',\n  ne: '≠',\n  equiv: '≡',\n  le: '≤',\n  ge: '≥',\n  sub: '⊂',\n  sup: '⊃',\n  nsub: '⊄',\n  sube: '⊆',\n  supe: '⊇',\n  oplus: '⊕',\n  otimes: '⊗',\n  perp: '⊥',\n  sdot: '⋅',\n  lceil: '⌈',\n  rceil: '⌉',\n  lfloor: '⌊',\n  rfloor: '⌋',\n  lang: '〈',\n  rang: '〉',\n  loz: '◊',\n  spades: '♠',\n  clubs: '♣',\n  hearts: '♥',\n  diams: '♦',\n  quot: '\"',\n  amp: '&',\n  lt: '<',\n  gt: '>',\n  OElig: 'Œ',\n  oelig: 'œ',\n  Scaron: 'Š',\n  scaron: 'š',\n  Yuml: 'Ÿ',\n  circ: 'ˆ',\n  tilde: '˜',\n  ensp: ' ',\n  emsp: ' ',\n  thinsp: ' ',\n  zwnj: '‌',\n  zwj: '‍',\n  lrm: '‎',\n  rlm: '‏',\n  ndash: '–',\n  mdash: '—',\n  lsquo: '‘',\n  rsquo: '’',\n  sbquo: '‚',\n  ldquo: '“',\n  rdquo: '”',\n  bdquo: '„',\n  dagger: '†',\n  Dagger: '‡',\n  permil: '‰',\n  lsaquo: '‹',\n  rsaquo: '›',\n  euro: '€'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/character-entities-html4/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/character-entities-legacy/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/character-entities-legacy/index.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   characterEntitiesLegacy: function() { return /* binding */ characterEntitiesLegacy; }\n/* harmony export */ });\n/**\n * List of legacy HTML named character references that don’t need a trailing semicolon.\n *\n * @type {Array<string>}\n */\nconst characterEntitiesLegacy = [\n  'AElig',\n  'AMP',\n  'Aacute',\n  'Acirc',\n  'Agrave',\n  'Aring',\n  'Atilde',\n  'Auml',\n  'COPY',\n  'Ccedil',\n  'ETH',\n  'Eacute',\n  'Ecirc',\n  'Egrave',\n  'Euml',\n  'GT',\n  'Iacute',\n  'Icirc',\n  'Igrave',\n  'Iuml',\n  'LT',\n  'Ntilde',\n  'Oacute',\n  'Ocirc',\n  'Ograve',\n  'Oslash',\n  'Otilde',\n  'Ouml',\n  'QUOT',\n  'REG',\n  'THORN',\n  'Uacute',\n  'Ucirc',\n  'Ugrave',\n  'Uuml',\n  'Yacute',\n  'aacute',\n  'acirc',\n  'acute',\n  'aelig',\n  'agrave',\n  'amp',\n  'aring',\n  'atilde',\n  'auml',\n  'brvbar',\n  'ccedil',\n  'cedil',\n  'cent',\n  'copy',\n  'curren',\n  'deg',\n  'divide',\n  'eacute',\n  'ecirc',\n  'egrave',\n  'eth',\n  'euml',\n  'frac12',\n  'frac14',\n  'frac34',\n  'gt',\n  'iacute',\n  'icirc',\n  'iexcl',\n  'igrave',\n  'iquest',\n  'iuml',\n  'laquo',\n  'lt',\n  'macr',\n  'micro',\n  'middot',\n  'nbsp',\n  'not',\n  'ntilde',\n  'oacute',\n  'ocirc',\n  'ograve',\n  'ordf',\n  'ordm',\n  'oslash',\n  'otilde',\n  'ouml',\n  'para',\n  'plusmn',\n  'pound',\n  'quot',\n  'raquo',\n  'reg',\n  'sect',\n  'shy',\n  'sup1',\n  'sup2',\n  'sup3',\n  'szlig',\n  'thorn',\n  'times',\n  'uacute',\n  'ucirc',\n  'ugrave',\n  'uml',\n  'uuml',\n  'yacute',\n  'yen',\n  'yuml'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/character-entities-legacy/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/comma-separated-tokens/index.js":
/*!******************************************************!*\
  !*** ./node_modules/comma-separated-tokens/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: function() { return /* binding */ parse; },\n/* harmony export */   stringify: function() { return /* binding */ stringify; }\n/* harmony export */ });\n/**\n * @typedef Options\n *   Configuration for `stringify`.\n * @property {boolean} [padLeft=true]\n *   Whether to pad a space before a token.\n * @property {boolean} [padRight=false]\n *   Whether to pad a space after a token.\n */\n\n/**\n * @typedef {Options} StringifyOptions\n *   Please use `StringifyOptions` instead.\n */\n\n/**\n * Parse comma-separated tokens to an array.\n *\n * @param {string} value\n *   Comma-separated tokens.\n * @returns {Array<string>}\n *   List of tokens.\n */\nfunction parse(value) {\n  /** @type {Array<string>} */\n  const tokens = []\n  const input = String(value || '')\n  let index = input.indexOf(',')\n  let start = 0\n  /** @type {boolean} */\n  let end = false\n\n  while (!end) {\n    if (index === -1) {\n      index = input.length\n      end = true\n    }\n\n    const token = input.slice(start, index).trim()\n\n    if (token || !end) {\n      tokens.push(token)\n    }\n\n    start = index + 1\n    index = input.indexOf(',', start)\n  }\n\n  return tokens\n}\n\n/**\n * Serialize an array of strings or numbers to comma-separated tokens.\n *\n * @param {Array<string|number>} values\n *   List of tokens.\n * @param {Options} [options]\n *   Configuration for `stringify` (optional).\n * @returns {string}\n *   Comma-separated tokens.\n */\nfunction stringify(values, options) {\n  const settings = options || {}\n\n  // Ensure the last empty entry is seen.\n  const input = values[values.length - 1] === '' ? [...values, ''] : values\n\n  return input\n    .join(\n      (settings.padRight ? ' ' : '') +\n        ',' +\n        (settings.padLeft === false ? '' : ' ')\n    )\n    .trim()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/comma-separated-tokens/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-to-html/lib/handle/comment.js":
/*!**************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/comment.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   comment: function() { return /* binding */ comment; }\n/* harmony export */ });\n/* harmony import */ var stringify_entities__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stringify-entities */ \"(app-pages-browser)/./node_modules/stringify-entities/lib/index.js\");\n/**\n * @typedef {import('../types.js').Comment} Comment\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n */\n\n\n\n/**\n * Serialize a comment.\n *\n * @param {Comment} node\n *   Node to handle.\n * @param {number | undefined} _1\n *   Index of `node` in `parent.\n * @param {Parent | undefined} _2\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction comment(node, _1, _2, state) {\n  // See: <https://html.spec.whatwg.org/multipage/syntax.html#comments>\n  return state.settings.bogusComments\n    ? '<?' +\n        (0,stringify_entities__WEBPACK_IMPORTED_MODULE_0__.stringifyEntities)(\n          node.value,\n          Object.assign({}, state.settings.characterReferences, {subset: ['>']})\n        ) +\n        '>'\n    : '<!--' + node.value.replace(/^>|^->|<!--|-->|--!>|<!-$/g, encode) + '-->'\n\n  /**\n   * @param {string} $0\n   */\n  function encode($0) {\n    return (0,stringify_entities__WEBPACK_IMPORTED_MODULE_0__.stringifyEntities)(\n      $0,\n      Object.assign({}, state.settings.characterReferences, {\n        subset: ['<', '>']\n      })\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-to-html/lib/handle/comment.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-to-html/lib/handle/doctype.js":
/*!**************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/doctype.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   doctype: function() { return /* binding */ doctype; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').DocType} DocType\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n */\n\n/**\n * Serialize a doctype.\n *\n * @param {DocType} _1\n *   Node to handle.\n * @param {number | undefined} _2\n *   Index of `node` in `parent.\n * @param {Parent | undefined} _3\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction doctype(_1, _2, _3, state) {\n  return (\n    '<!' +\n    (state.settings.upperDoctype ? 'DOCTYPE' : 'doctype') +\n    (state.settings.tightDoctype ? '' : ' ') +\n    'html>'\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8taHRtbC9saWIvaGFuZGxlL2RvY3R5cGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSwrQkFBK0I7QUFDNUMsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSw2QkFBNkI7QUFDMUM7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbGliL2hhbmRsZS9kb2N0eXBlLmpzPzcyODQiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkRvY1R5cGV9IERvY1R5cGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuUGFyZW50fSBQYXJlbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLyoqXG4gKiBTZXJpYWxpemUgYSBkb2N0eXBlLlxuICpcbiAqIEBwYXJhbSB7RG9jVHlwZX0gXzFcbiAqICAgTm9kZSB0byBoYW5kbGUuXG4gKiBAcGFyYW0ge251bWJlciB8IHVuZGVmaW5lZH0gXzJcbiAqICAgSW5kZXggb2YgYG5vZGVgIGluIGBwYXJlbnQuXG4gKiBAcGFyYW0ge1BhcmVudCB8IHVuZGVmaW5lZH0gXzNcbiAqICAgUGFyZW50IG9mIGBub2RlYC5cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZCBhYm91dCB0aGUgY3VycmVudCBzdGF0ZS5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIFNlcmlhbGl6ZWQgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGRvY3R5cGUoXzEsIF8yLCBfMywgc3RhdGUpIHtcbiAgcmV0dXJuIChcbiAgICAnPCEnICtcbiAgICAoc3RhdGUuc2V0dGluZ3MudXBwZXJEb2N0eXBlID8gJ0RPQ1RZUEUnIDogJ2RvY3R5cGUnKSArXG4gICAgKHN0YXRlLnNldHRpbmdzLnRpZ2h0RG9jdHlwZSA/ICcnIDogJyAnKSArXG4gICAgJ2h0bWw+J1xuICApXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-to-html/lib/handle/doctype.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-to-html/lib/handle/element.js":
/*!**************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/element.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   element: function() { return /* binding */ element; }\n/* harmony export */ });\n/* harmony import */ var ccount__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ccount */ \"(app-pages-browser)/./node_modules/ccount/index.js\");\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! comma-separated-tokens */ \"(app-pages-browser)/./node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! property-information */ \"(app-pages-browser)/./node_modules/property-information/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! property-information */ \"(app-pages-browser)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! space-separated-tokens */ \"(app-pages-browser)/./node_modules/space-separated-tokens/index.js\");\n/* harmony import */ var stringify_entities__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! stringify-entities */ \"(app-pages-browser)/./node_modules/stringify-entities/lib/index.js\");\n/* harmony import */ var _omission_opening_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../omission/opening.js */ \"(app-pages-browser)/./node_modules/hast-util-to-html/lib/omission/opening.js\");\n/* harmony import */ var _omission_closing_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../omission/closing.js */ \"(app-pages-browser)/./node_modules/hast-util-to-html/lib/omission/closing.js\");\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Properties} Properties\n * @typedef {import('../types.js').PropertyValue} PropertyValue\n */\n\n\n\n\n\n\n\n\n\n/**\n * Maps of subsets.\n *\n * Each value is a matrix of tuples.\n * The value at `0` causes parse errors, the value at `1` is valid.\n * Of both, the value at `0` is unsafe, and the value at `1` is safe.\n *\n * @type {Record<'name' | 'unquoted' | 'single' | 'double', Array<[Array<string>, Array<string>]>>}\n */\nconst constants = {\n  // See: <https://html.spec.whatwg.org/#attribute-name-state>.\n  name: [\n    ['\\t\\n\\f\\r &/=>'.split(''), '\\t\\n\\f\\r \"&\\'/=>`'.split('')],\n    ['\\0\\t\\n\\f\\r \"&\\'/<=>'.split(''), '\\0\\t\\n\\f\\r \"&\\'/<=>`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(unquoted)-state>.\n  unquoted: [\n    ['\\t\\n\\f\\r &>'.split(''), '\\0\\t\\n\\f\\r \"&\\'<=>`'.split('')],\n    ['\\0\\t\\n\\f\\r \"&\\'<=>`'.split(''), '\\0\\t\\n\\f\\r \"&\\'<=>`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(single-quoted)-state>.\n  single: [\n    [\"&'\".split(''), '\"&\\'`'.split('')],\n    [\"\\0&'\".split(''), '\\0\"&\\'`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(double-quoted)-state>.\n  double: [\n    ['\"&'.split(''), '\"&\\'`'.split('')],\n    ['\\0\"&'.split(''), '\\0\"&\\'`'.split('')]\n  ]\n}\n\n/**\n * Serialize an element node.\n *\n * @param {Element} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parent | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\n// eslint-disable-next-line complexity\nfunction element(node, index, parent, state) {\n  const schema = state.schema\n  const omit = schema.space === 'svg' ? false : state.settings.omitOptionalTags\n  let selfClosing =\n    schema.space === 'svg'\n      ? state.settings.closeEmptyElements\n      : state.settings.voids.includes(node.tagName.toLowerCase())\n  /** @type {Array<string>} */\n  const parts = []\n  /** @type {string} */\n  let last\n\n  if (schema.space === 'html' && node.tagName === 'svg') {\n    state.schema = property_information__WEBPACK_IMPORTED_MODULE_0__.svg\n  }\n\n  const attrs = serializeAttributes(state, node.properties)\n\n  const content = state.all(\n    schema.space === 'html' && node.tagName === 'template' ? node.content : node\n  )\n\n  state.schema = schema\n\n  // If the node is categorised as void, but it has children, remove the\n  // categorisation.\n  // This enables for example `menuitem`s, which are void in W3C HTML but not\n  // void in WHATWG HTML, to be stringified properly.\n  if (content) selfClosing = false\n\n  if (attrs || !omit || !(0,_omission_opening_js__WEBPACK_IMPORTED_MODULE_1__.opening)(node, index, parent)) {\n    parts.push('<', node.tagName, attrs ? ' ' + attrs : '')\n\n    if (\n      selfClosing &&\n      (schema.space === 'svg' || state.settings.closeSelfClosing)\n    ) {\n      last = attrs.charAt(attrs.length - 1)\n      if (\n        !state.settings.tightSelfClosing ||\n        last === '/' ||\n        (last && last !== '\"' && last !== \"'\")\n      ) {\n        parts.push(' ')\n      }\n\n      parts.push('/')\n    }\n\n    parts.push('>')\n  }\n\n  parts.push(content)\n\n  if (!selfClosing && (!omit || !(0,_omission_closing_js__WEBPACK_IMPORTED_MODULE_2__.closing)(node, index, parent))) {\n    parts.push('</' + node.tagName + '>')\n  }\n\n  return parts.join('')\n}\n\n/**\n * @param {State} state\n * @param {Properties | null | undefined} props\n * @returns {string}\n */\nfunction serializeAttributes(state, props) {\n  /** @type {Array<string>} */\n  const values = []\n  let index = -1\n  /** @type {string} */\n  let key\n\n  if (props) {\n    for (key in props) {\n      if (props[key] !== undefined && props[key] !== null) {\n        const value = serializeAttribute(state, key, props[key])\n        if (value) values.push(value)\n      }\n    }\n  }\n\n  while (++index < values.length) {\n    const last = state.settings.tightAttributes\n      ? values[index].charAt(values[index].length - 1)\n      : null\n\n    // In tight mode, don’t add a space after quoted attributes.\n    if (index !== values.length - 1 && last !== '\"' && last !== \"'\") {\n      values[index] += ' '\n    }\n  }\n\n  return values.join('')\n}\n\n/**\n * @param {State} state\n * @param {string} key\n * @param {PropertyValue} value\n * @returns {string}\n */\n// eslint-disable-next-line complexity\nfunction serializeAttribute(state, key, value) {\n  const info = (0,property_information__WEBPACK_IMPORTED_MODULE_3__.find)(state.schema, key)\n  const x =\n    state.settings.allowParseErrors && state.schema.space === 'html' ? 0 : 1\n  const y = state.settings.allowDangerousCharacters ? 0 : 1\n  let quote = state.quote\n  /** @type {string | undefined} */\n  let result\n\n  if (info.overloadedBoolean && (value === info.attribute || value === '')) {\n    value = true\n  } else if (\n    info.boolean ||\n    (info.overloadedBoolean && typeof value !== 'string')\n  ) {\n    value = Boolean(value)\n  }\n\n  if (\n    value === undefined ||\n    value === null ||\n    value === false ||\n    (typeof value === 'number' && Number.isNaN(value))\n  ) {\n    return ''\n  }\n\n  const name = (0,stringify_entities__WEBPACK_IMPORTED_MODULE_4__.stringifyEntities)(\n    info.attribute,\n    Object.assign({}, state.settings.characterReferences, {\n      // Always encode without parse errors in non-HTML.\n      subset: constants.name[x][y]\n    })\n  )\n\n  // No value.\n  // There is currently only one boolean property in SVG: `[download]` on\n  // `<a>`.\n  // This property does not seem to work in browsers (Firefox, Safari, Chrome),\n  // so I can’t test if dropping the value works.\n  // But I assume that it should:\n  //\n  // ```html\n  // <!doctype html>\n  // <svg viewBox=\"0 0 100 100\">\n  //   <a href=https://example.com download>\n  //     <circle cx=50 cy=40 r=35 />\n  //   </a>\n  // </svg>\n  // ```\n  //\n  // See: <https://github.com/wooorm/property-information/blob/main/lib/svg.js>\n  if (value === true) return name\n\n  // `spaces` doesn’t accept a second argument, but it’s given here just to\n  // keep the code cleaner.\n  value = Array.isArray(value)\n    ? (info.commaSeparated ? comma_separated_tokens__WEBPACK_IMPORTED_MODULE_5__.stringify : space_separated_tokens__WEBPACK_IMPORTED_MODULE_6__.stringify)(value, {\n        padLeft: !state.settings.tightCommaSeparatedLists\n      })\n    : String(value)\n\n  if (state.settings.collapseEmptyAttributes && !value) return name\n\n  // Check unquoted value.\n  if (state.settings.preferUnquoted) {\n    result = (0,stringify_entities__WEBPACK_IMPORTED_MODULE_4__.stringifyEntities)(\n      value,\n      Object.assign({}, state.settings.characterReferences, {\n        subset: constants.unquoted[x][y],\n        attribute: true\n      })\n    )\n  }\n\n  // If we don’t want unquoted, or if `value` contains character references when\n  // unquoted…\n  if (result !== value) {\n    // If the alternative is less common than `quote`, switch.\n    if (\n      state.settings.quoteSmart &&\n      (0,ccount__WEBPACK_IMPORTED_MODULE_7__.ccount)(value, quote) > (0,ccount__WEBPACK_IMPORTED_MODULE_7__.ccount)(value, state.alternative)\n    ) {\n      quote = state.alternative\n    }\n\n    result =\n      quote +\n      (0,stringify_entities__WEBPACK_IMPORTED_MODULE_4__.stringifyEntities)(\n        value,\n        Object.assign({}, state.settings.characterReferences, {\n          // Always encode without parse errors in non-HTML.\n          subset: (quote === \"'\" ? constants.single : constants.double)[x][y],\n          attribute: true\n        })\n      ) +\n      quote\n  }\n\n  // Don’t add a `=` for unquoted empties.\n  return name + (result ? '=' + result : result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-to-html/lib/handle/element.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-to-html/lib/handle/index.js":
/*!************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/index.js ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handle: function() { return /* binding */ handle; }\n/* harmony export */ });\n/* harmony import */ var zwitch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zwitch */ \"(app-pages-browser)/./node_modules/zwitch/index.js\");\n/* harmony import */ var _comment_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./comment.js */ \"(app-pages-browser)/./node_modules/hast-util-to-html/lib/handle/comment.js\");\n/* harmony import */ var _doctype_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./doctype.js */ \"(app-pages-browser)/./node_modules/hast-util-to-html/lib/handle/doctype.js\");\n/* harmony import */ var _element_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./element.js */ \"(app-pages-browser)/./node_modules/hast-util-to-html/lib/handle/element.js\");\n/* harmony import */ var _raw_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./raw.js */ \"(app-pages-browser)/./node_modules/hast-util-to-html/lib/handle/raw.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./root.js */ \"(app-pages-browser)/./node_modules/hast-util-to-html/lib/handle/root.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./text.js */ \"(app-pages-browser)/./node_modules/hast-util-to-html/lib/handle/text.js\");\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Node} Node\n * @typedef {import('../types.js').Parent} Parent\n */\n\n\n\n\n\n\n\n\n\n/**\n * @type {(node: Node, index: number | undefined, parent: Parent | undefined, state: State) => string}\n */\nconst handle = (0,zwitch__WEBPACK_IMPORTED_MODULE_0__.zwitch)('type', {\n  invalid,\n  unknown,\n  handlers: {comment: _comment_js__WEBPACK_IMPORTED_MODULE_1__.comment, doctype: _doctype_js__WEBPACK_IMPORTED_MODULE_2__.doctype, element: _element_js__WEBPACK_IMPORTED_MODULE_3__.element, raw: _raw_js__WEBPACK_IMPORTED_MODULE_4__.raw, root: _root_js__WEBPACK_IMPORTED_MODULE_5__.root, text: _text_js__WEBPACK_IMPORTED_MODULE_6__.text}\n})\n\n/**\n * Fail when a non-node is found in the tree.\n *\n * @param {unknown} node\n *   Unknown value.\n * @returns {never}\n *   Never.\n */\nfunction invalid(node) {\n  throw new Error('Expected node, not `' + node + '`')\n}\n\n/**\n * Fail when a node with an unknown type is found in the tree.\n *\n * @param {unknown} node\n *  Unknown node.\n * @returns {never}\n *   Never.\n */\nfunction unknown(node) {\n  // @ts-expect-error: `type` is defined.\n  throw new Error('Cannot compile unknown node `' + node.type + '`')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8taHRtbC9saWIvaGFuZGxlL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQUE7QUFDQSxhQUFhLDZCQUE2QjtBQUMxQyxhQUFhLDRCQUE0QjtBQUN6QyxhQUFhLDhCQUE4QjtBQUMzQzs7QUFFNkI7QUFDTztBQUNBO0FBQ0E7QUFDUjtBQUNFO0FBQ0E7O0FBRTlCO0FBQ0EsVUFBVTtBQUNWO0FBQ08sZUFBZSw4Q0FBTTtBQUM1QjtBQUNBO0FBQ0EsYUFBYSxPQUFPLDJEQUFTLDJEQUFTLHVEQUFLLGdEQUFNLGtEQUFNO0FBQ3ZELENBQUM7O0FBRUQ7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbGliL2hhbmRsZS9pbmRleC5qcz9jNjczIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5TdGF0ZX0gU3RhdGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuTm9kZX0gTm9kZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5QYXJlbnR9IFBhcmVudFxuICovXG5cbmltcG9ydCB7endpdGNofSBmcm9tICd6d2l0Y2gnXG5pbXBvcnQge2NvbW1lbnR9IGZyb20gJy4vY29tbWVudC5qcydcbmltcG9ydCB7ZG9jdHlwZX0gZnJvbSAnLi9kb2N0eXBlLmpzJ1xuaW1wb3J0IHtlbGVtZW50fSBmcm9tICcuL2VsZW1lbnQuanMnXG5pbXBvcnQge3Jhd30gZnJvbSAnLi9yYXcuanMnXG5pbXBvcnQge3Jvb3R9IGZyb20gJy4vcm9vdC5qcydcbmltcG9ydCB7dGV4dH0gZnJvbSAnLi90ZXh0LmpzJ1xuXG4vKipcbiAqIEB0eXBlIHsobm9kZTogTm9kZSwgaW5kZXg6IG51bWJlciB8IHVuZGVmaW5lZCwgcGFyZW50OiBQYXJlbnQgfCB1bmRlZmluZWQsIHN0YXRlOiBTdGF0ZSkgPT4gc3RyaW5nfVxuICovXG5leHBvcnQgY29uc3QgaGFuZGxlID0gendpdGNoKCd0eXBlJywge1xuICBpbnZhbGlkLFxuICB1bmtub3duLFxuICBoYW5kbGVyczoge2NvbW1lbnQsIGRvY3R5cGUsIGVsZW1lbnQsIHJhdywgcm9vdCwgdGV4dH1cbn0pXG5cbi8qKlxuICogRmFpbCB3aGVuIGEgbm9uLW5vZGUgaXMgZm91bmQgaW4gdGhlIHRyZWUuXG4gKlxuICogQHBhcmFtIHt1bmtub3dufSBub2RlXG4gKiAgIFVua25vd24gdmFsdWUuXG4gKiBAcmV0dXJucyB7bmV2ZXJ9XG4gKiAgIE5ldmVyLlxuICovXG5mdW5jdGlvbiBpbnZhbGlkKG5vZGUpIHtcbiAgdGhyb3cgbmV3IEVycm9yKCdFeHBlY3RlZCBub2RlLCBub3QgYCcgKyBub2RlICsgJ2AnKVxufVxuXG4vKipcbiAqIEZhaWwgd2hlbiBhIG5vZGUgd2l0aCBhbiB1bmtub3duIHR5cGUgaXMgZm91bmQgaW4gdGhlIHRyZWUuXG4gKlxuICogQHBhcmFtIHt1bmtub3dufSBub2RlXG4gKiAgVW5rbm93biBub2RlLlxuICogQHJldHVybnMge25ldmVyfVxuICogICBOZXZlci5cbiAqL1xuZnVuY3Rpb24gdW5rbm93bihub2RlKSB7XG4gIC8vIEB0cy1leHBlY3QtZXJyb3I6IGB0eXBlYCBpcyBkZWZpbmVkLlxuICB0aHJvdyBuZXcgRXJyb3IoJ0Nhbm5vdCBjb21waWxlIHVua25vd24gbm9kZSBgJyArIG5vZGUudHlwZSArICdgJylcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-to-html/lib/handle/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-to-html/lib/handle/raw.js":
/*!**********************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/raw.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   raw: function() { return /* binding */ raw; }\n/* harmony export */ });\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./text.js */ \"(app-pages-browser)/./node_modules/hast-util-to-html/lib/handle/text.js\");\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').Raw} Raw\n */\n\n\n\n/**\n * Serialize a raw node.\n *\n * @param {Raw} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parent | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction raw(node, index, parent, state) {\n  return state.settings.allowDangerousHtml\n    ? node.value\n    : (0,_text_js__WEBPACK_IMPORTED_MODULE_0__.text)(node, index, parent, state)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8taHRtbC9saWIvaGFuZGxlL3Jhdy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSw2QkFBNkI7QUFDMUMsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSwyQkFBMkI7QUFDeEM7O0FBRThCOztBQUU5QjtBQUNBO0FBQ0E7QUFDQSxXQUFXLEtBQUs7QUFDaEI7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSxNQUFNLDhDQUFJO0FBQ1YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1odG1sL2xpYi9oYW5kbGUvcmF3LmpzPzAyYTAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlN0YXRlfSBTdGF0ZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5QYXJlbnR9IFBhcmVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5SYXd9IFJhd1xuICovXG5cbmltcG9ydCB7dGV4dH0gZnJvbSAnLi90ZXh0LmpzJ1xuXG4vKipcbiAqIFNlcmlhbGl6ZSBhIHJhdyBub2RlLlxuICpcbiAqIEBwYXJhbSB7UmF3fSBub2RlXG4gKiAgIE5vZGUgdG8gaGFuZGxlLlxuICogQHBhcmFtIHtudW1iZXIgfCB1bmRlZmluZWR9IGluZGV4XG4gKiAgIEluZGV4IG9mIGBub2RlYCBpbiBgcGFyZW50LlxuICogQHBhcmFtIHtQYXJlbnQgfCB1bmRlZmluZWR9IHBhcmVudFxuICogICBQYXJlbnQgb2YgYG5vZGVgLlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kIGFib3V0IHRoZSBjdXJyZW50IHN0YXRlLlxuICogQHJldHVybnMge3N0cmluZ31cbiAqICAgU2VyaWFsaXplZCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gcmF3KG5vZGUsIGluZGV4LCBwYXJlbnQsIHN0YXRlKSB7XG4gIHJldHVybiBzdGF0ZS5zZXR0aW5ncy5hbGxvd0Rhbmdlcm91c0h0bWxcbiAgICA/IG5vZGUudmFsdWVcbiAgICA6IHRleHQobm9kZSwgaW5kZXgsIHBhcmVudCwgc3RhdGUpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-to-html/lib/handle/raw.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-to-html/lib/handle/root.js":
/*!***********************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/root.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: function() { return /* binding */ root; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Root} Root\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n */\n\n/**\n * Serialize a root.\n *\n * @param {Root} node\n *   Node to handle.\n * @param {number | undefined} _1\n *   Index of `node` in `parent.\n * @param {Parent | undefined} _2\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction root(node, _1, _2, state) {\n  return state.all(node)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8taHRtbC9saWIvaGFuZGxlL3Jvb3QuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSw0QkFBNEI7QUFDekMsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSw2QkFBNkI7QUFDMUM7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8taHRtbC9saWIvaGFuZGxlL3Jvb3QuanM/MjdhYSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuUm9vdH0gUm9vdFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5QYXJlbnR9IFBhcmVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5TdGF0ZX0gU3RhdGVcbiAqL1xuXG4vKipcbiAqIFNlcmlhbGl6ZSBhIHJvb3QuXG4gKlxuICogQHBhcmFtIHtSb290fSBub2RlXG4gKiAgIE5vZGUgdG8gaGFuZGxlLlxuICogQHBhcmFtIHtudW1iZXIgfCB1bmRlZmluZWR9IF8xXG4gKiAgIEluZGV4IG9mIGBub2RlYCBpbiBgcGFyZW50LlxuICogQHBhcmFtIHtQYXJlbnQgfCB1bmRlZmluZWR9IF8yXG4gKiAgIFBhcmVudCBvZiBgbm9kZWAuXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQgYWJvdXQgdGhlIGN1cnJlbnQgc3RhdGUuXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICogICBTZXJpYWxpemVkIG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiByb290KG5vZGUsIF8xLCBfMiwgc3RhdGUpIHtcbiAgcmV0dXJuIHN0YXRlLmFsbChub2RlKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-to-html/lib/handle/root.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-to-html/lib/handle/text.js":
/*!***********************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/text.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: function() { return /* binding */ text; }\n/* harmony export */ });\n/* harmony import */ var stringify_entities__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stringify-entities */ \"(app-pages-browser)/./node_modules/stringify-entities/lib/index.js\");\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').Raw} Raw\n * @typedef {import('../types.js').Text} Text\n */\n\n\n\n/**\n * Serialize a text node.\n *\n * @param {Text | Raw} node\n *   Node to handle.\n * @param {number | undefined} _\n *   Index of `node` in `parent.\n * @param {Parent | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction text(node, _, parent, state) {\n  // Check if content of `node` should be escaped.\n  return parent &&\n    parent.type === 'element' &&\n    (parent.tagName === 'script' || parent.tagName === 'style')\n    ? node.value\n    : (0,stringify_entities__WEBPACK_IMPORTED_MODULE_0__.stringifyEntities)(\n        node.value,\n        Object.assign({}, state.settings.characterReferences, {\n          subset: ['<', '&']\n        })\n      )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-to-html/lib/handle/text.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-to-html/lib/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/index.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   all: function() { return /* binding */ all; },\n/* harmony export */   toHtml: function() { return /* binding */ toHtml; }\n/* harmony export */ });\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(app-pages-browser)/./node_modules/property-information/index.js\");\n/* harmony import */ var html_void_elements__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! html-void-elements */ \"(app-pages-browser)/./node_modules/html-void-elements/index.js\");\n/* harmony import */ var _handle_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./handle/index.js */ \"(app-pages-browser)/./node_modules/hast-util-to-html/lib/handle/index.js\");\n/**\n * @typedef {import('./types.js').Node} Node\n * @typedef {import('./types.js').Parent} Parent\n * @typedef {import('./types.js').Content} Content\n * @typedef {import('./types.js').Options} Options\n * @typedef {import('./types.js').State} State\n */\n\n\n\n\n\n/**\n * Serialize hast as HTML.\n *\n * @param {Node | Array<Content>} tree\n *   Tree to serialize.\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {string}\n *   Serialized HTML.\n */\n// eslint-disable-next-line complexity\nfunction toHtml(tree, options) {\n  const options_ = options || {}\n  const quote = options_.quote || '\"'\n  const alternative = quote === '\"' ? \"'\" : '\"'\n\n  if (quote !== '\"' && quote !== \"'\") {\n    throw new Error('Invalid quote `' + quote + '`, expected `\\'` or `\"`')\n  }\n\n  /** @type {State} */\n  const state = {\n    one,\n    all,\n    settings: {\n      omitOptionalTags: options_.omitOptionalTags || false,\n      allowParseErrors: options_.allowParseErrors || false,\n      allowDangerousCharacters: options_.allowDangerousCharacters || false,\n      quoteSmart: options_.quoteSmart || false,\n      preferUnquoted: options_.preferUnquoted || false,\n      tightAttributes: options_.tightAttributes || false,\n      upperDoctype: options_.upperDoctype || false,\n      tightDoctype: options_.tightDoctype || false,\n      bogusComments: options_.bogusComments || false,\n      tightCommaSeparatedLists: options_.tightCommaSeparatedLists || false,\n      tightSelfClosing: options_.tightSelfClosing || false,\n      collapseEmptyAttributes: options_.collapseEmptyAttributes || false,\n      allowDangerousHtml: options_.allowDangerousHtml || false,\n      voids: options_.voids || html_void_elements__WEBPACK_IMPORTED_MODULE_0__.htmlVoidElements,\n      characterReferences:\n        options_.characterReferences || options_.entities || {},\n      closeSelfClosing: options_.closeSelfClosing || false,\n      closeEmptyElements: options_.closeEmptyElements || false\n    },\n    schema: options_.space === 'svg' ? property_information__WEBPACK_IMPORTED_MODULE_1__.svg : property_information__WEBPACK_IMPORTED_MODULE_1__.html,\n    quote,\n    alternative\n  }\n\n  return state.one(\n    Array.isArray(tree) ? {type: 'root', children: tree} : tree,\n    undefined,\n    undefined\n  )\n}\n\n/**\n * Serialize a node.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Node} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parent | undefined} parent\n *   Parent of `node`.\n * @returns {string}\n *   Serialized node.\n */\nfunction one(node, index, parent) {\n  return (0,_handle_index_js__WEBPACK_IMPORTED_MODULE_2__.handle)(node, index, parent, this)\n}\n\n/**\n * Serialize all children of `parent`.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Parent | undefined} parent\n *   Parent whose children to serialize.\n * @returns {string}\n */\nfunction all(parent) {\n  /** @type {Array<string>} */\n  const results = []\n  const children = (parent && parent.children) || []\n  let index = -1\n\n  while (++index < children.length) {\n    results[index] = this.one(children[index], index, parent)\n  }\n\n  return results.join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8taHRtbC9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTtBQUNBLGFBQWEsMkJBQTJCO0FBQ3hDLGFBQWEsNkJBQTZCO0FBQzFDLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsNEJBQTRCO0FBQ3pDOztBQUU4QztBQUNLO0FBQ1g7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLFdBQVcsdUJBQXVCO0FBQ2xDO0FBQ0EsV0FBVyw0QkFBNEI7QUFDdkM7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBLGFBQWEsT0FBTztBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLGdFQUFnQjtBQUMvQztBQUNBLCtEQUErRDtBQUMvRDtBQUNBO0FBQ0EsS0FBSztBQUNMLHVDQUF1QyxxREFBRyxHQUFHLHNEQUFJO0FBQ2pEO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLDJCQUEyQiw4QkFBOEI7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxTQUFTLHdEQUFNO0FBQ2Y7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxhQUFhO0FBQ2I7QUFDTztBQUNQLGFBQWEsZUFBZTtBQUM1QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1odG1sL2xpYi9pbmRleC5qcz8xYjQ5Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi90eXBlcy5qcycpLk5vZGV9IE5vZGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4vdHlwZXMuanMnKS5QYXJlbnR9IFBhcmVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi90eXBlcy5qcycpLkNvbnRlbnR9IENvbnRlbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4vdHlwZXMuanMnKS5PcHRpb25zfSBPcHRpb25zXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL3R5cGVzLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuaW1wb3J0IHtodG1sLCBzdmd9IGZyb20gJ3Byb3BlcnR5LWluZm9ybWF0aW9uJ1xuaW1wb3J0IHtodG1sVm9pZEVsZW1lbnRzfSBmcm9tICdodG1sLXZvaWQtZWxlbWVudHMnXG5pbXBvcnQge2hhbmRsZX0gZnJvbSAnLi9oYW5kbGUvaW5kZXguanMnXG5cbi8qKlxuICogU2VyaWFsaXplIGhhc3QgYXMgSFRNTC5cbiAqXG4gKiBAcGFyYW0ge05vZGUgfCBBcnJheTxDb250ZW50Pn0gdHJlZVxuICogICBUcmVlIHRvIHNlcmlhbGl6ZS5cbiAqIEBwYXJhbSB7T3B0aW9ucyB8IG51bGwgfCB1bmRlZmluZWR9IFtvcHRpb25zXVxuICogICBDb25maWd1cmF0aW9uLlxuICogQHJldHVybnMge3N0cmluZ31cbiAqICAgU2VyaWFsaXplZCBIVE1MLlxuICovXG4vLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgY29tcGxleGl0eVxuZXhwb3J0IGZ1bmN0aW9uIHRvSHRtbCh0cmVlLCBvcHRpb25zKSB7XG4gIGNvbnN0IG9wdGlvbnNfID0gb3B0aW9ucyB8fCB7fVxuICBjb25zdCBxdW90ZSA9IG9wdGlvbnNfLnF1b3RlIHx8ICdcIidcbiAgY29uc3QgYWx0ZXJuYXRpdmUgPSBxdW90ZSA9PT0gJ1wiJyA/IFwiJ1wiIDogJ1wiJ1xuXG4gIGlmIChxdW90ZSAhPT0gJ1wiJyAmJiBxdW90ZSAhPT0gXCInXCIpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgcXVvdGUgYCcgKyBxdW90ZSArICdgLCBleHBlY3RlZCBgXFwnYCBvciBgXCJgJylcbiAgfVxuXG4gIC8qKiBAdHlwZSB7U3RhdGV9ICovXG4gIGNvbnN0IHN0YXRlID0ge1xuICAgIG9uZSxcbiAgICBhbGwsXG4gICAgc2V0dGluZ3M6IHtcbiAgICAgIG9taXRPcHRpb25hbFRhZ3M6IG9wdGlvbnNfLm9taXRPcHRpb25hbFRhZ3MgfHwgZmFsc2UsXG4gICAgICBhbGxvd1BhcnNlRXJyb3JzOiBvcHRpb25zXy5hbGxvd1BhcnNlRXJyb3JzIHx8IGZhbHNlLFxuICAgICAgYWxsb3dEYW5nZXJvdXNDaGFyYWN0ZXJzOiBvcHRpb25zXy5hbGxvd0Rhbmdlcm91c0NoYXJhY3RlcnMgfHwgZmFsc2UsXG4gICAgICBxdW90ZVNtYXJ0OiBvcHRpb25zXy5xdW90ZVNtYXJ0IHx8IGZhbHNlLFxuICAgICAgcHJlZmVyVW5xdW90ZWQ6IG9wdGlvbnNfLnByZWZlclVucXVvdGVkIHx8IGZhbHNlLFxuICAgICAgdGlnaHRBdHRyaWJ1dGVzOiBvcHRpb25zXy50aWdodEF0dHJpYnV0ZXMgfHwgZmFsc2UsXG4gICAgICB1cHBlckRvY3R5cGU6IG9wdGlvbnNfLnVwcGVyRG9jdHlwZSB8fCBmYWxzZSxcbiAgICAgIHRpZ2h0RG9jdHlwZTogb3B0aW9uc18udGlnaHREb2N0eXBlIHx8IGZhbHNlLFxuICAgICAgYm9ndXNDb21tZW50czogb3B0aW9uc18uYm9ndXNDb21tZW50cyB8fCBmYWxzZSxcbiAgICAgIHRpZ2h0Q29tbWFTZXBhcmF0ZWRMaXN0czogb3B0aW9uc18udGlnaHRDb21tYVNlcGFyYXRlZExpc3RzIHx8IGZhbHNlLFxuICAgICAgdGlnaHRTZWxmQ2xvc2luZzogb3B0aW9uc18udGlnaHRTZWxmQ2xvc2luZyB8fCBmYWxzZSxcbiAgICAgIGNvbGxhcHNlRW1wdHlBdHRyaWJ1dGVzOiBvcHRpb25zXy5jb2xsYXBzZUVtcHR5QXR0cmlidXRlcyB8fCBmYWxzZSxcbiAgICAgIGFsbG93RGFuZ2Vyb3VzSHRtbDogb3B0aW9uc18uYWxsb3dEYW5nZXJvdXNIdG1sIHx8IGZhbHNlLFxuICAgICAgdm9pZHM6IG9wdGlvbnNfLnZvaWRzIHx8IGh0bWxWb2lkRWxlbWVudHMsXG4gICAgICBjaGFyYWN0ZXJSZWZlcmVuY2VzOlxuICAgICAgICBvcHRpb25zXy5jaGFyYWN0ZXJSZWZlcmVuY2VzIHx8IG9wdGlvbnNfLmVudGl0aWVzIHx8IHt9LFxuICAgICAgY2xvc2VTZWxmQ2xvc2luZzogb3B0aW9uc18uY2xvc2VTZWxmQ2xvc2luZyB8fCBmYWxzZSxcbiAgICAgIGNsb3NlRW1wdHlFbGVtZW50czogb3B0aW9uc18uY2xvc2VFbXB0eUVsZW1lbnRzIHx8IGZhbHNlXG4gICAgfSxcbiAgICBzY2hlbWE6IG9wdGlvbnNfLnNwYWNlID09PSAnc3ZnJyA/IHN2ZyA6IGh0bWwsXG4gICAgcXVvdGUsXG4gICAgYWx0ZXJuYXRpdmVcbiAgfVxuXG4gIHJldHVybiBzdGF0ZS5vbmUoXG4gICAgQXJyYXkuaXNBcnJheSh0cmVlKSA/IHt0eXBlOiAncm9vdCcsIGNoaWxkcmVuOiB0cmVlfSA6IHRyZWUsXG4gICAgdW5kZWZpbmVkLFxuICAgIHVuZGVmaW5lZFxuICApXG59XG5cbi8qKlxuICogU2VyaWFsaXplIGEgbm9kZS5cbiAqXG4gKiBAdGhpcyB7U3RhdGV9XG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZCBhYm91dCB0aGUgY3VycmVudCBzdGF0ZS5cbiAqIEBwYXJhbSB7Tm9kZX0gbm9kZVxuICogICBOb2RlIHRvIGhhbmRsZS5cbiAqIEBwYXJhbSB7bnVtYmVyIHwgdW5kZWZpbmVkfSBpbmRleFxuICogICBJbmRleCBvZiBgbm9kZWAgaW4gYHBhcmVudC5cbiAqIEBwYXJhbSB7UGFyZW50IHwgdW5kZWZpbmVkfSBwYXJlbnRcbiAqICAgUGFyZW50IG9mIGBub2RlYC5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIFNlcmlhbGl6ZWQgbm9kZS5cbiAqL1xuZnVuY3Rpb24gb25lKG5vZGUsIGluZGV4LCBwYXJlbnQpIHtcbiAgcmV0dXJuIGhhbmRsZShub2RlLCBpbmRleCwgcGFyZW50LCB0aGlzKVxufVxuXG4vKipcbiAqIFNlcmlhbGl6ZSBhbGwgY2hpbGRyZW4gb2YgYHBhcmVudGAuXG4gKlxuICogQHRoaXMge1N0YXRlfVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQgYWJvdXQgdGhlIGN1cnJlbnQgc3RhdGUuXG4gKiBAcGFyYW0ge1BhcmVudCB8IHVuZGVmaW5lZH0gcGFyZW50XG4gKiAgIFBhcmVudCB3aG9zZSBjaGlsZHJlbiB0byBzZXJpYWxpemUuXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gYWxsKHBhcmVudCkge1xuICAvKiogQHR5cGUge0FycmF5PHN0cmluZz59ICovXG4gIGNvbnN0IHJlc3VsdHMgPSBbXVxuICBjb25zdCBjaGlsZHJlbiA9IChwYXJlbnQgJiYgcGFyZW50LmNoaWxkcmVuKSB8fCBbXVxuICBsZXQgaW5kZXggPSAtMVxuXG4gIHdoaWxlICgrK2luZGV4IDwgY2hpbGRyZW4ubGVuZ3RoKSB7XG4gICAgcmVzdWx0c1tpbmRleF0gPSB0aGlzLm9uZShjaGlsZHJlbltpbmRleF0sIGluZGV4LCBwYXJlbnQpXG4gIH1cblxuICByZXR1cm4gcmVzdWx0cy5qb2luKCcnKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-to-html/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-to-html/lib/omission/closing.js":
/*!****************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/omission/closing.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closing: function() { return /* binding */ closing; }\n/* harmony export */ });\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-whitespace */ \"(app-pages-browser)/./node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js\");\n/* harmony import */ var _util_siblings_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/siblings.js */ \"(app-pages-browser)/./node_modules/hast-util-to-html/lib/omission/util/siblings.js\");\n/* harmony import */ var _omission_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./omission.js */ \"(app-pages-browser)/./node_modules/hast-util-to-html/lib/omission/omission.js\");\n/**\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Parent} Parent\n */\n\n\n\n\n\nconst closing = (0,_omission_js__WEBPACK_IMPORTED_MODULE_0__.omission)({\n  html,\n  head: headOrColgroupOrCaption,\n  body,\n  p,\n  li,\n  dt,\n  dd,\n  rt: rubyElement,\n  rp: rubyElement,\n  optgroup,\n  option,\n  menuitem,\n  colgroup: headOrColgroupOrCaption,\n  caption: headOrColgroupOrCaption,\n  thead,\n  tbody,\n  tfoot,\n  tr,\n  td: cells,\n  th: cells\n})\n\n/**\n * Macro for `</head>`, `</colgroup>`, and `</caption>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction headOrColgroupOrCaption(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index, true)\n  return (\n    !next ||\n    (next.type !== 'comment' &&\n      !(next.type === 'text' && (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__.whitespace)(next.value.charAt(0))))\n  )\n}\n\n/**\n * Whether to omit `</html>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction html(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || next.type !== 'comment'\n}\n\n/**\n * Whether to omit `</body>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction body(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || next.type !== 'comment'\n}\n\n/**\n * Whether to omit `</p>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\n// eslint-disable-next-line complexity\nfunction p(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return next\n    ? next.type === 'element' &&\n        (next.tagName === 'address' ||\n          next.tagName === 'article' ||\n          next.tagName === 'aside' ||\n          next.tagName === 'blockquote' ||\n          next.tagName === 'details' ||\n          next.tagName === 'div' ||\n          next.tagName === 'dl' ||\n          next.tagName === 'fieldset' ||\n          next.tagName === 'figcaption' ||\n          next.tagName === 'figure' ||\n          next.tagName === 'footer' ||\n          next.tagName === 'form' ||\n          next.tagName === 'h1' ||\n          next.tagName === 'h2' ||\n          next.tagName === 'h3' ||\n          next.tagName === 'h4' ||\n          next.tagName === 'h5' ||\n          next.tagName === 'h6' ||\n          next.tagName === 'header' ||\n          next.tagName === 'hgroup' ||\n          next.tagName === 'hr' ||\n          next.tagName === 'main' ||\n          next.tagName === 'menu' ||\n          next.tagName === 'nav' ||\n          next.tagName === 'ol' ||\n          next.tagName === 'p' ||\n          next.tagName === 'pre' ||\n          next.tagName === 'section' ||\n          next.tagName === 'table' ||\n          next.tagName === 'ul')\n    : !parent ||\n        // Confusing parent.\n        !(\n          parent.type === 'element' &&\n          (parent.tagName === 'a' ||\n            parent.tagName === 'audio' ||\n            parent.tagName === 'del' ||\n            parent.tagName === 'ins' ||\n            parent.tagName === 'map' ||\n            parent.tagName === 'noscript' ||\n            parent.tagName === 'video')\n        )\n}\n\n/**\n * Whether to omit `</li>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction li(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'li')\n}\n\n/**\n * Whether to omit `</dt>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction dt(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    next &&\n    next.type === 'element' &&\n    (next.tagName === 'dt' || next.tagName === 'dd')\n  )\n}\n\n/**\n * Whether to omit `</dd>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction dd(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'dt' || next.tagName === 'dd'))\n  )\n}\n\n/**\n * Whether to omit `</rt>` or `</rp>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction rubyElement(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'rp' || next.tagName === 'rt'))\n  )\n}\n\n/**\n * Whether to omit `</optgroup>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction optgroup(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'optgroup')\n}\n\n/**\n * Whether to omit `</option>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction option(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'option' || next.tagName === 'optgroup'))\n  )\n}\n\n/**\n * Whether to omit `</menuitem>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction menuitem(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'menuitem' ||\n        next.tagName === 'hr' ||\n        next.tagName === 'menu'))\n  )\n}\n\n/**\n * Whether to omit `</thead>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction thead(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    next &&\n    next.type === 'element' &&\n    (next.tagName === 'tbody' || next.tagName === 'tfoot')\n  )\n}\n\n/**\n * Whether to omit `</tbody>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tbody(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'tbody' || next.tagName === 'tfoot'))\n  )\n}\n\n/**\n * Whether to omit `</tfoot>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tfoot(_, index, parent) {\n  return !(0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n}\n\n/**\n * Whether to omit `</tr>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tr(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'tr')\n}\n\n/**\n * Whether to omit `</td>` or `</th>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction cells(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'td' || next.tagName === 'th'))\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-to-html/lib/omission/closing.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-to-html/lib/omission/omission.js":
/*!*****************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/omission/omission.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   omission: function() { return /* binding */ omission; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').OmitHandle} OmitHandle\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * Factory to check if a given node can have a tag omitted.\n *\n * @param {Record<string, OmitHandle>} handlers\n *   Omission handlers, where each key is a tag name, and each value is the\n *   corresponding handler.\n * @returns {OmitHandle}\n *   Whether to omit a tag of an element.\n */\nfunction omission(handlers) {\n  return omit\n\n  /**\n   * Check if a given node can have a tag omitted.\n   *\n   * @type {OmitHandle}\n   */\n  function omit(node, index, parent) {\n    return (\n      own.call(handlers, node.tagName) &&\n      handlers[node.tagName](node, index, parent)\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8taHRtbC9saWIvb21pc3Npb24vb21pc3Npb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSxrQ0FBa0M7QUFDL0M7O0FBRUEsY0FBYzs7QUFFZDtBQUNBO0FBQ0E7QUFDQSxXQUFXLDRCQUE0QjtBQUN2QztBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8taHRtbC9saWIvb21pc3Npb24vb21pc3Npb24uanM/NWE1MCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuT21pdEhhbmRsZX0gT21pdEhhbmRsZVxuICovXG5cbmNvbnN0IG93biA9IHt9Lmhhc093blByb3BlcnR5XG5cbi8qKlxuICogRmFjdG9yeSB0byBjaGVjayBpZiBhIGdpdmVuIG5vZGUgY2FuIGhhdmUgYSB0YWcgb21pdHRlZC5cbiAqXG4gKiBAcGFyYW0ge1JlY29yZDxzdHJpbmcsIE9taXRIYW5kbGU+fSBoYW5kbGVyc1xuICogICBPbWlzc2lvbiBoYW5kbGVycywgd2hlcmUgZWFjaCBrZXkgaXMgYSB0YWcgbmFtZSwgYW5kIGVhY2ggdmFsdWUgaXMgdGhlXG4gKiAgIGNvcnJlc3BvbmRpbmcgaGFuZGxlci5cbiAqIEByZXR1cm5zIHtPbWl0SGFuZGxlfVxuICogICBXaGV0aGVyIHRvIG9taXQgYSB0YWcgb2YgYW4gZWxlbWVudC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIG9taXNzaW9uKGhhbmRsZXJzKSB7XG4gIHJldHVybiBvbWl0XG5cbiAgLyoqXG4gICAqIENoZWNrIGlmIGEgZ2l2ZW4gbm9kZSBjYW4gaGF2ZSBhIHRhZyBvbWl0dGVkLlxuICAgKlxuICAgKiBAdHlwZSB7T21pdEhhbmRsZX1cbiAgICovXG4gIGZ1bmN0aW9uIG9taXQobm9kZSwgaW5kZXgsIHBhcmVudCkge1xuICAgIHJldHVybiAoXG4gICAgICBvd24uY2FsbChoYW5kbGVycywgbm9kZS50YWdOYW1lKSAmJlxuICAgICAgaGFuZGxlcnNbbm9kZS50YWdOYW1lXShub2RlLCBpbmRleCwgcGFyZW50KVxuICAgIClcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-to-html/lib/omission/omission.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-to-html/lib/omission/opening.js":
/*!****************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/omission/opening.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   opening: function() { return /* binding */ opening; }\n/* harmony export */ });\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-whitespace */ \"(app-pages-browser)/./node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js\");\n/* harmony import */ var _util_siblings_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/siblings.js */ \"(app-pages-browser)/./node_modules/hast-util-to-html/lib/omission/util/siblings.js\");\n/* harmony import */ var _closing_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./closing.js */ \"(app-pages-browser)/./node_modules/hast-util-to-html/lib/omission/closing.js\");\n/* harmony import */ var _omission_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./omission.js */ \"(app-pages-browser)/./node_modules/hast-util-to-html/lib/omission/omission.js\");\n/**\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').Content} Content\n */\n\n\n\n\n\n\nconst opening = (0,_omission_js__WEBPACK_IMPORTED_MODULE_0__.omission)({\n  html,\n  head,\n  body,\n  colgroup,\n  tbody\n})\n\n/**\n * Whether to omit `<html>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction html(node) {\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1)\n  return !head || head.type !== 'comment'\n}\n\n/**\n * Whether to omit `<head>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction head(node) {\n  const children = node.children\n  /** @type {Array<string>} */\n  const seen = []\n  let index = -1\n\n  while (++index < children.length) {\n    const child = children[index]\n    if (\n      child.type === 'element' &&\n      (child.tagName === 'title' || child.tagName === 'base')\n    ) {\n      if (seen.includes(child.tagName)) return false\n      seen.push(child.tagName)\n    }\n  }\n\n  return children.length > 0\n}\n\n/**\n * Whether to omit `<body>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction body(node) {\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1, true)\n\n  return (\n    !head ||\n    (head.type !== 'comment' &&\n      !(head.type === 'text' && (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__.whitespace)(head.value.charAt(0))) &&\n      !(\n        head.type === 'element' &&\n        (head.tagName === 'meta' ||\n          head.tagName === 'link' ||\n          head.tagName === 'script' ||\n          head.tagName === 'style' ||\n          head.tagName === 'template')\n      ))\n  )\n}\n\n/**\n * Whether to omit `<colgroup>`.\n * The spec describes some logic for the opening tag, but it’s easier to\n * implement in the closing tag, to the same effect, so we handle it there\n * instead.\n *\n * @param {Element} node\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction colgroup(node, index, parent) {\n  const previous = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingBefore)(parent, index)\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1, true)\n\n  // Previous colgroup was already omitted.\n  if (\n    parent &&\n    previous &&\n    previous.type === 'element' &&\n    previous.tagName === 'colgroup' &&\n    (0,_closing_js__WEBPACK_IMPORTED_MODULE_3__.closing)(previous, parent.children.indexOf(previous), parent)\n  ) {\n    return false\n  }\n\n  return head && head.type === 'element' && head.tagName === 'col'\n}\n\n/**\n * Whether to omit `<tbody>`.\n *\n * @param {Element} node\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction tbody(node, index, parent) {\n  const previous = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingBefore)(parent, index)\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1)\n\n  // Previous table section was already omitted.\n  if (\n    parent &&\n    previous &&\n    previous.type === 'element' &&\n    (previous.tagName === 'thead' || previous.tagName === 'tbody') &&\n    (0,_closing_js__WEBPACK_IMPORTED_MODULE_3__.closing)(previous, parent.children.indexOf(previous), parent)\n  ) {\n    return false\n  }\n\n  return head && head.type === 'element' && head.tagName === 'tr'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-to-html/lib/omission/opening.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-to-html/lib/omission/util/siblings.js":
/*!**********************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/omission/util/siblings.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   siblingAfter: function() { return /* binding */ siblingAfter; },\n/* harmony export */   siblingBefore: function() { return /* binding */ siblingBefore; }\n/* harmony export */ });\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-whitespace */ \"(app-pages-browser)/./node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js\");\n/**\n * @typedef {import('../../types.js').Parent} Parent\n * @typedef {import('../../types.js').Content} Content\n */\n\n\n\nconst siblingAfter = siblings(1)\nconst siblingBefore = siblings(-1)\n\n/**\n * Factory to check siblings in a direction.\n *\n * @param {number} increment\n */\nfunction siblings(increment) {\n  return sibling\n\n  /**\n   * Find applicable siblings in a direction.\n   *\n   * @param {Parent | null | undefined} parent\n   * @param {number | null | undefined} index\n   * @param {boolean | null | undefined} [includeWhitespace=false]\n   * @returns {Content}\n   */\n  function sibling(parent, index, includeWhitespace) {\n    const siblings = parent ? parent.children : []\n    let offset = (index || 0) + increment\n    let next = siblings && siblings[offset]\n\n    if (!includeWhitespace) {\n      while (next && (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_0__.whitespace)(next)) {\n        offset += increment\n        next = siblings[offset]\n      }\n    }\n\n    return next\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-to-html/lib/omission/util/siblings.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   whitespace: function() { return /* binding */ whitespace; }\n/* harmony export */ });\n/**\n * Check if the given value is *inter-element whitespace*.\n *\n * @param {unknown} thing\n *   Thing to check (typically `Node` or `string`).\n * @returns {boolean}\n *   Whether the `value` is inter-element whitespace (`boolean`): consisting of\n *   zero or more of space, tab (`\\t`), line feed (`\\n`), carriage return\n *   (`\\r`), or form feed (`\\f`).\n *   If a node is passed it must be a `Text` node, whose `value` field is\n *   checked.\n */\nfunction whitespace(thing) {\n  /** @type {string} */\n  const value =\n    // @ts-expect-error looks like a node.\n    thing && typeof thing === 'object' && thing.type === 'text'\n      ? // @ts-expect-error looks like a text.\n        thing.value || ''\n      : thing\n\n  // HTML whitespace expression.\n  // See <https://infra.spec.whatwg.org/#ascii-whitespace>.\n  return typeof value === 'string' && value.replace(/[ \\t\\n\\f\\r]/g, '') === ''\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8taHRtbC9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXdoaXRlc3BhY2UvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLGFBQWEsUUFBUTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1odG1sL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtd2hpdGVzcGFjZS9pbmRleC5qcz9mYjAzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ2hlY2sgaWYgdGhlIGdpdmVuIHZhbHVlIGlzICppbnRlci1lbGVtZW50IHdoaXRlc3BhY2UqLlxuICpcbiAqIEBwYXJhbSB7dW5rbm93bn0gdGhpbmdcbiAqICAgVGhpbmcgdG8gY2hlY2sgKHR5cGljYWxseSBgTm9kZWAgb3IgYHN0cmluZ2ApLlxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKiAgIFdoZXRoZXIgdGhlIGB2YWx1ZWAgaXMgaW50ZXItZWxlbWVudCB3aGl0ZXNwYWNlIChgYm9vbGVhbmApOiBjb25zaXN0aW5nIG9mXG4gKiAgIHplcm8gb3IgbW9yZSBvZiBzcGFjZSwgdGFiIChgXFx0YCksIGxpbmUgZmVlZCAoYFxcbmApLCBjYXJyaWFnZSByZXR1cm5cbiAqICAgKGBcXHJgKSwgb3IgZm9ybSBmZWVkIChgXFxmYCkuXG4gKiAgIElmIGEgbm9kZSBpcyBwYXNzZWQgaXQgbXVzdCBiZSBhIGBUZXh0YCBub2RlLCB3aG9zZSBgdmFsdWVgIGZpZWxkIGlzXG4gKiAgIGNoZWNrZWQuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB3aGl0ZXNwYWNlKHRoaW5nKSB7XG4gIC8qKiBAdHlwZSB7c3RyaW5nfSAqL1xuICBjb25zdCB2YWx1ZSA9XG4gICAgLy8gQHRzLWV4cGVjdC1lcnJvciBsb29rcyBsaWtlIGEgbm9kZS5cbiAgICB0aGluZyAmJiB0eXBlb2YgdGhpbmcgPT09ICdvYmplY3QnICYmIHRoaW5nLnR5cGUgPT09ICd0ZXh0J1xuICAgICAgPyAvLyBAdHMtZXhwZWN0LWVycm9yIGxvb2tzIGxpa2UgYSB0ZXh0LlxuICAgICAgICB0aGluZy52YWx1ZSB8fCAnJ1xuICAgICAgOiB0aGluZ1xuXG4gIC8vIEhUTUwgd2hpdGVzcGFjZSBleHByZXNzaW9uLlxuICAvLyBTZWUgPGh0dHBzOi8vaW5mcmEuc3BlYy53aGF0d2cub3JnLyNhc2NpaS13aGl0ZXNwYWNlPi5cbiAgcmV0dXJuIHR5cGVvZiB2YWx1ZSA9PT0gJ3N0cmluZycgJiYgdmFsdWUucmVwbGFjZSgvWyBcXHRcXG5cXGZcXHJdL2csICcnKSA9PT0gJydcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/html-void-elements/index.js":
/*!**************************************************!*\
  !*** ./node_modules/html-void-elements/index.js ***!
  \**************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   htmlVoidElements: function() { return /* binding */ htmlVoidElements; }\n/* harmony export */ });\n/**\n * List of HTML void tag names.\n *\n * @type {Array<string>}\n */\nconst htmlVoidElements = [\n  'area',\n  'base',\n  'basefont',\n  'bgsound',\n  'br',\n  'col',\n  'command',\n  'embed',\n  'frame',\n  'hr',\n  'image',\n  'img',\n  'input',\n  'isindex',\n  'keygen',\n  'link',\n  'menuitem',\n  'meta',\n  'nextid',\n  'param',\n  'source',\n  'track',\n  'wbr'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9odG1sLXZvaWQtZWxlbWVudHMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9odG1sLXZvaWQtZWxlbWVudHMvaW5kZXguanM/NjcxNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIExpc3Qgb2YgSFRNTCB2b2lkIHRhZyBuYW1lcy5cbiAqXG4gKiBAdHlwZSB7QXJyYXk8c3RyaW5nPn1cbiAqL1xuZXhwb3J0IGNvbnN0IGh0bWxWb2lkRWxlbWVudHMgPSBbXG4gICdhcmVhJyxcbiAgJ2Jhc2UnLFxuICAnYmFzZWZvbnQnLFxuICAnYmdzb3VuZCcsXG4gICdicicsXG4gICdjb2wnLFxuICAnY29tbWFuZCcsXG4gICdlbWJlZCcsXG4gICdmcmFtZScsXG4gICdocicsXG4gICdpbWFnZScsXG4gICdpbWcnLFxuICAnaW5wdXQnLFxuICAnaXNpbmRleCcsXG4gICdrZXlnZW4nLFxuICAnbGluaycsXG4gICdtZW51aXRlbScsXG4gICdtZXRhJyxcbiAgJ25leHRpZCcsXG4gICdwYXJhbScsXG4gICdzb3VyY2UnLFxuICAndHJhY2snLFxuICAnd2JyJ1xuXVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/html-void-elements/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/property-information/index.js":
/*!****************************************************!*\
  !*** ./node_modules/property-information/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   find: function() { return /* reexport safe */ _lib_find_js__WEBPACK_IMPORTED_MODULE_0__.find; },\n/* harmony export */   hastToReact: function() { return /* reexport safe */ _lib_hast_to_react_js__WEBPACK_IMPORTED_MODULE_1__.hastToReact; },\n/* harmony export */   html: function() { return /* binding */ html; },\n/* harmony export */   normalize: function() { return /* reexport safe */ _lib_normalize_js__WEBPACK_IMPORTED_MODULE_2__.normalize; },\n/* harmony export */   svg: function() { return /* binding */ svg; }\n/* harmony export */ });\n/* harmony import */ var _lib_util_merge_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/util/merge.js */ \"(app-pages-browser)/./node_modules/property-information/lib/util/merge.js\");\n/* harmony import */ var _lib_xlink_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/xlink.js */ \"(app-pages-browser)/./node_modules/property-information/lib/xlink.js\");\n/* harmony import */ var _lib_xml_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/xml.js */ \"(app-pages-browser)/./node_modules/property-information/lib/xml.js\");\n/* harmony import */ var _lib_xmlns_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/xmlns.js */ \"(app-pages-browser)/./node_modules/property-information/lib/xmlns.js\");\n/* harmony import */ var _lib_aria_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/aria.js */ \"(app-pages-browser)/./node_modules/property-information/lib/aria.js\");\n/* harmony import */ var _lib_html_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./lib/html.js */ \"(app-pages-browser)/./node_modules/property-information/lib/html.js\");\n/* harmony import */ var _lib_svg_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./lib/svg.js */ \"(app-pages-browser)/./node_modules/property-information/lib/svg.js\");\n/* harmony import */ var _lib_find_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/find.js */ \"(app-pages-browser)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var _lib_hast_to_react_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/hast-to-react.js */ \"(app-pages-browser)/./node_modules/property-information/lib/hast-to-react.js\");\n/* harmony import */ var _lib_normalize_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/normalize.js */ \"(app-pages-browser)/./node_modules/property-information/lib/normalize.js\");\n/**\n * @typedef {import('./lib/util/info.js').Info} Info\n * @typedef {import('./lib/util/schema.js').Schema} Schema\n */\n\n\n\n\n\n\n\n\n\n\n\n\nconst html = (0,_lib_util_merge_js__WEBPACK_IMPORTED_MODULE_3__.merge)([_lib_xml_js__WEBPACK_IMPORTED_MODULE_4__.xml, _lib_xlink_js__WEBPACK_IMPORTED_MODULE_5__.xlink, _lib_xmlns_js__WEBPACK_IMPORTED_MODULE_6__.xmlns, _lib_aria_js__WEBPACK_IMPORTED_MODULE_7__.aria, _lib_html_js__WEBPACK_IMPORTED_MODULE_8__.html], 'html')\nconst svg = (0,_lib_util_merge_js__WEBPACK_IMPORTED_MODULE_3__.merge)([_lib_xml_js__WEBPACK_IMPORTED_MODULE_4__.xml, _lib_xlink_js__WEBPACK_IMPORTED_MODULE_5__.xlink, _lib_xmlns_js__WEBPACK_IMPORTED_MODULE_6__.xmlns, _lib_aria_js__WEBPACK_IMPORTED_MODULE_7__.aria, _lib_svg_js__WEBPACK_IMPORTED_MODULE_9__.svg], 'svg')\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBLGFBQWEsbUNBQW1DO0FBQ2hELGFBQWEsdUNBQXVDO0FBQ3BEOztBQUV5QztBQUNMO0FBQ0o7QUFDSTtBQUNGO0FBQ1k7QUFDSDs7QUFFVDtBQUNnQjtBQUNOO0FBQ3JDLGFBQWEseURBQUssRUFBRSw0Q0FBRyxFQUFFLGdEQUFLLEVBQUUsZ0RBQUssRUFBRSw4Q0FBSSxFQUFFLDhDQUFRO0FBQ3JELFlBQVkseURBQUssRUFBRSw0Q0FBRyxFQUFFLGdEQUFLLEVBQUUsZ0RBQUssRUFBRSw4Q0FBSSxFQUFFLDRDQUFPIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9pbmRleC5qcz81MzdjIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi9saWIvdXRpbC9pbmZvLmpzJykuSW5mb30gSW5mb1xuICogQHR5cGVkZWYge2ltcG9ydCgnLi9saWIvdXRpbC9zY2hlbWEuanMnKS5TY2hlbWF9IFNjaGVtYVxuICovXG5cbmltcG9ydCB7bWVyZ2V9IGZyb20gJy4vbGliL3V0aWwvbWVyZ2UuanMnXG5pbXBvcnQge3hsaW5rfSBmcm9tICcuL2xpYi94bGluay5qcydcbmltcG9ydCB7eG1sfSBmcm9tICcuL2xpYi94bWwuanMnXG5pbXBvcnQge3htbG5zfSBmcm9tICcuL2xpYi94bWxucy5qcydcbmltcG9ydCB7YXJpYX0gZnJvbSAnLi9saWIvYXJpYS5qcydcbmltcG9ydCB7aHRtbCBhcyBodG1sQmFzZX0gZnJvbSAnLi9saWIvaHRtbC5qcydcbmltcG9ydCB7c3ZnIGFzIHN2Z0Jhc2V9IGZyb20gJy4vbGliL3N2Zy5qcydcblxuZXhwb3J0IHtmaW5kfSBmcm9tICcuL2xpYi9maW5kLmpzJ1xuZXhwb3J0IHtoYXN0VG9SZWFjdH0gZnJvbSAnLi9saWIvaGFzdC10by1yZWFjdC5qcydcbmV4cG9ydCB7bm9ybWFsaXplfSBmcm9tICcuL2xpYi9ub3JtYWxpemUuanMnXG5leHBvcnQgY29uc3QgaHRtbCA9IG1lcmdlKFt4bWwsIHhsaW5rLCB4bWxucywgYXJpYSwgaHRtbEJhc2VdLCAnaHRtbCcpXG5leHBvcnQgY29uc3Qgc3ZnID0gbWVyZ2UoW3htbCwgeGxpbmssIHhtbG5zLCBhcmlhLCBzdmdCYXNlXSwgJ3N2ZycpXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/property-information/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/property-information/lib/aria.js":
/*!*******************************************************!*\
  !*** ./node_modules/property-information/lib/aria.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aria: function() { return /* binding */ aria; }\n/* harmony export */ });\n/* harmony import */ var _util_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/types.js */ \"(app-pages-browser)/./node_modules/property-information/lib/util/types.js\");\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(app-pages-browser)/./node_modules/property-information/lib/util/create.js\");\n\n\n\nconst aria = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  transform(_, prop) {\n    return prop === 'role' ? prop : 'aria-' + prop.slice(4).toLowerCase()\n  },\n  properties: {\n    ariaActiveDescendant: null,\n    ariaAtomic: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaAutoComplete: null,\n    ariaBusy: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaChecked: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaColCount: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaColIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaColSpan: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaControls: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaCurrent: null,\n    ariaDescribedBy: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaDetails: null,\n    ariaDisabled: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaDropEffect: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaErrorMessage: null,\n    ariaExpanded: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaFlowTo: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaGrabbed: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaHasPopup: null,\n    ariaHidden: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaInvalid: null,\n    ariaKeyShortcuts: null,\n    ariaLabel: null,\n    ariaLabelledBy: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaLevel: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaLive: null,\n    ariaModal: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaMultiLine: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaMultiSelectable: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaOrientation: null,\n    ariaOwns: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaPlaceholder: null,\n    ariaPosInSet: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaPressed: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaReadOnly: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaRelevant: null,\n    ariaRequired: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaRoleDescription: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaRowCount: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaRowIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaRowSpan: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaSelected: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaSetSize: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaSort: null,\n    ariaValueMax: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaValueMin: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaValueNow: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaValueText: null,\n    role: null\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/property-information/lib/aria.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/property-information/lib/find.js":
/*!*******************************************************!*\
  !*** ./node_modules/property-information/lib/find.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   find: function() { return /* binding */ find; }\n/* harmony export */ });\n/* harmony import */ var _normalize_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./normalize.js */ \"(app-pages-browser)/./node_modules/property-information/lib/normalize.js\");\n/* harmony import */ var _util_defined_info_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/defined-info.js */ \"(app-pages-browser)/./node_modules/property-information/lib/util/defined-info.js\");\n/* harmony import */ var _util_info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/info.js */ \"(app-pages-browser)/./node_modules/property-information/lib/util/info.js\");\n/**\n * @typedef {import('./util/schema.js').Schema} Schema\n */\n\n\n\n\n\nconst valid = /^data[-\\w.:]+$/i\nconst dash = /-[a-z]/g\nconst cap = /[A-Z]/g\n\n/**\n * @param {Schema} schema\n * @param {string} value\n * @returns {Info}\n */\nfunction find(schema, value) {\n  const normal = (0,_normalize_js__WEBPACK_IMPORTED_MODULE_0__.normalize)(value)\n  let prop = value\n  let Type = _util_info_js__WEBPACK_IMPORTED_MODULE_1__.Info\n\n  if (normal in schema.normal) {\n    return schema.property[schema.normal[normal]]\n  }\n\n  if (normal.length > 4 && normal.slice(0, 4) === 'data' && valid.test(value)) {\n    // Attribute or property.\n    if (value.charAt(4) === '-') {\n      // Turn it into a property.\n      const rest = value.slice(5).replace(dash, camelcase)\n      prop = 'data' + rest.charAt(0).toUpperCase() + rest.slice(1)\n    } else {\n      // Turn it into an attribute.\n      const rest = value.slice(4)\n\n      if (!dash.test(rest)) {\n        let dashes = rest.replace(cap, kebab)\n\n        if (dashes.charAt(0) !== '-') {\n          dashes = '-' + dashes\n        }\n\n        value = 'data' + dashes\n      }\n    }\n\n    Type = _util_defined_info_js__WEBPACK_IMPORTED_MODULE_2__.DefinedInfo\n  }\n\n  return new Type(prop, value)\n}\n\n/**\n * @param {string} $0\n * @returns {string}\n */\nfunction kebab($0) {\n  return '-' + $0.toLowerCase()\n}\n\n/**\n * @param {string} $0\n * @returns {string}\n */\nfunction camelcase($0) {\n  return $0.charAt(1).toUpperCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/property-information/lib/find.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/property-information/lib/hast-to-react.js":
/*!****************************************************************!*\
  !*** ./node_modules/property-information/lib/hast-to-react.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hastToReact: function() { return /* binding */ hastToReact; }\n/* harmony export */ });\n/**\n * `hast` is close to `React`, but differs in a couple of cases.\n *\n * To get a React property from a hast property, check if it is in\n * `hastToReact`, if it is, then use the corresponding value,\n * otherwise, use the hast property.\n *\n * @type {Record<string, string>}\n */\nconst hastToReact = {\n  classId: 'classID',\n  dataType: 'datatype',\n  itemId: 'itemID',\n  strokeDashArray: 'strokeDasharray',\n  strokeDashOffset: 'strokeDashoffset',\n  strokeLineCap: 'strokeLinecap',\n  strokeLineJoin: 'strokeLinejoin',\n  strokeMiterLimit: 'strokeMiterlimit',\n  typeOf: 'typeof',\n  xLinkActuate: 'xlinkActuate',\n  xLinkArcRole: 'xlinkArcrole',\n  xLinkHref: 'xlinkHref',\n  xLinkRole: 'xlinkRole',\n  xLinkShow: 'xlinkShow',\n  xLinkTitle: 'xlinkTitle',\n  xLinkType: 'xlinkType',\n  xmlnsXLink: 'xmlnsXlink'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIvaGFzdC10by1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL2hhc3QtdG8tcmVhY3QuanM/YTA0MSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIGBoYXN0YCBpcyBjbG9zZSB0byBgUmVhY3RgLCBidXQgZGlmZmVycyBpbiBhIGNvdXBsZSBvZiBjYXNlcy5cbiAqXG4gKiBUbyBnZXQgYSBSZWFjdCBwcm9wZXJ0eSBmcm9tIGEgaGFzdCBwcm9wZXJ0eSwgY2hlY2sgaWYgaXQgaXMgaW5cbiAqIGBoYXN0VG9SZWFjdGAsIGlmIGl0IGlzLCB0aGVuIHVzZSB0aGUgY29ycmVzcG9uZGluZyB2YWx1ZSxcbiAqIG90aGVyd2lzZSwgdXNlIHRoZSBoYXN0IHByb3BlcnR5LlxuICpcbiAqIEB0eXBlIHtSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+fVxuICovXG5leHBvcnQgY29uc3QgaGFzdFRvUmVhY3QgPSB7XG4gIGNsYXNzSWQ6ICdjbGFzc0lEJyxcbiAgZGF0YVR5cGU6ICdkYXRhdHlwZScsXG4gIGl0ZW1JZDogJ2l0ZW1JRCcsXG4gIHN0cm9rZURhc2hBcnJheTogJ3N0cm9rZURhc2hhcnJheScsXG4gIHN0cm9rZURhc2hPZmZzZXQ6ICdzdHJva2VEYXNob2Zmc2V0JyxcbiAgc3Ryb2tlTGluZUNhcDogJ3N0cm9rZUxpbmVjYXAnLFxuICBzdHJva2VMaW5lSm9pbjogJ3N0cm9rZUxpbmVqb2luJyxcbiAgc3Ryb2tlTWl0ZXJMaW1pdDogJ3N0cm9rZU1pdGVybGltaXQnLFxuICB0eXBlT2Y6ICd0eXBlb2YnLFxuICB4TGlua0FjdHVhdGU6ICd4bGlua0FjdHVhdGUnLFxuICB4TGlua0FyY1JvbGU6ICd4bGlua0FyY3JvbGUnLFxuICB4TGlua0hyZWY6ICd4bGlua0hyZWYnLFxuICB4TGlua1JvbGU6ICd4bGlua1JvbGUnLFxuICB4TGlua1Nob3c6ICd4bGlua1Nob3cnLFxuICB4TGlua1RpdGxlOiAneGxpbmtUaXRsZScsXG4gIHhMaW5rVHlwZTogJ3hsaW5rVHlwZScsXG4gIHhtbG5zWExpbms6ICd4bWxuc1hsaW5rJ1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/property-information/lib/hast-to-react.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/property-information/lib/html.js":
/*!*******************************************************!*\
  !*** ./node_modules/property-information/lib/html.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   html: function() { return /* binding */ html; }\n/* harmony export */ });\n/* harmony import */ var _util_types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/types.js */ \"(app-pages-browser)/./node_modules/property-information/lib/util/types.js\");\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(app-pages-browser)/./node_modules/property-information/lib/util/create.js\");\n/* harmony import */ var _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/case-insensitive-transform.js */ \"(app-pages-browser)/./node_modules/property-information/lib/util/case-insensitive-transform.js\");\n\n\n\n\nconst html = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  space: 'html',\n  attributes: {\n    acceptcharset: 'accept-charset',\n    classname: 'class',\n    htmlfor: 'for',\n    httpequiv: 'http-equiv'\n  },\n  transform: _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__.caseInsensitiveTransform,\n  mustUseProperty: ['checked', 'multiple', 'muted', 'selected'],\n  properties: {\n    // Standard Properties.\n    abbr: null,\n    accept: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaSeparated,\n    acceptCharset: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    accessKey: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    action: null,\n    allow: null,\n    allowFullScreen: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    allowPaymentRequest: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    allowUserMedia: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    alt: null,\n    as: null,\n    async: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    autoCapitalize: null,\n    autoComplete: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    autoFocus: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    autoPlay: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    blocking: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    capture: null,\n    charSet: null,\n    checked: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    cite: null,\n    className: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    cols: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    colSpan: null,\n    content: null,\n    contentEditable: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.booleanish,\n    controls: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    controlsList: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    coords: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number | _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaSeparated,\n    crossOrigin: null,\n    data: null,\n    dateTime: null,\n    decoding: null,\n    default: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    defer: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    dir: null,\n    dirName: null,\n    disabled: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    download: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.overloadedBoolean,\n    draggable: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.booleanish,\n    encType: null,\n    enterKeyHint: null,\n    fetchPriority: null,\n    form: null,\n    formAction: null,\n    formEncType: null,\n    formMethod: null,\n    formNoValidate: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    formTarget: null,\n    headers: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    height: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    hidden: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    high: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    href: null,\n    hrefLang: null,\n    htmlFor: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    httpEquiv: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    id: null,\n    imageSizes: null,\n    imageSrcSet: null,\n    inert: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    inputMode: null,\n    integrity: null,\n    is: null,\n    isMap: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    itemId: null,\n    itemProp: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    itemRef: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    itemScope: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    itemType: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    kind: null,\n    label: null,\n    lang: null,\n    language: null,\n    list: null,\n    loading: null,\n    loop: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    low: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    manifest: null,\n    max: null,\n    maxLength: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    media: null,\n    method: null,\n    min: null,\n    minLength: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    multiple: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    muted: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    name: null,\n    nonce: null,\n    noModule: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    noValidate: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    onAbort: null,\n    onAfterPrint: null,\n    onAuxClick: null,\n    onBeforeMatch: null,\n    onBeforePrint: null,\n    onBeforeToggle: null,\n    onBeforeUnload: null,\n    onBlur: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onContextLost: null,\n    onContextMenu: null,\n    onContextRestored: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFormData: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLanguageChange: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadEnd: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMessageError: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRejectionHandled: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onScrollEnd: null,\n    onSecurityPolicyViolation: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onSlotChange: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnhandledRejection: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onWheel: null,\n    open: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    optimum: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    pattern: null,\n    ping: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    placeholder: null,\n    playsInline: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    popover: null,\n    popoverTarget: null,\n    popoverTargetAction: null,\n    poster: null,\n    preload: null,\n    readOnly: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    referrerPolicy: null,\n    rel: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    required: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    reversed: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    rows: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    rowSpan: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    sandbox: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    scope: null,\n    scoped: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    seamless: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    selected: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    shadowRootClonable: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    shadowRootDelegatesFocus: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    shadowRootMode: null,\n    shape: null,\n    size: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    sizes: null,\n    slot: null,\n    span: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    spellCheck: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.booleanish,\n    src: null,\n    srcDoc: null,\n    srcLang: null,\n    srcSet: null,\n    start: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    step: null,\n    style: null,\n    tabIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    target: null,\n    title: null,\n    translate: null,\n    type: null,\n    typeMustMatch: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    useMap: null,\n    value: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.booleanish,\n    width: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    wrap: null,\n    writingSuggestions: null,\n\n    // Legacy.\n    // See: https://html.spec.whatwg.org/#other-elements,-attributes-and-apis\n    align: null, // Several. Use CSS `text-align` instead,\n    aLink: null, // `<body>`. Use CSS `a:active {color}` instead\n    archive: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated, // `<object>`. List of URIs to archives\n    axis: null, // `<td>` and `<th>`. Use `scope` on `<th>`\n    background: null, // `<body>`. Use CSS `background-image` instead\n    bgColor: null, // `<body>` and table elements. Use CSS `background-color` instead\n    border: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<table>`. Use CSS `border-width` instead,\n    borderColor: null, // `<table>`. Use CSS `border-color` instead,\n    bottomMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<body>`\n    cellPadding: null, // `<table>`\n    cellSpacing: null, // `<table>`\n    char: null, // Several table elements. When `align=char`, sets the character to align on\n    charOff: null, // Several table elements. When `char`, offsets the alignment\n    classId: null, // `<object>`\n    clear: null, // `<br>`. Use CSS `clear` instead\n    code: null, // `<object>`\n    codeBase: null, // `<object>`\n    codeType: null, // `<object>`\n    color: null, // `<font>` and `<hr>`. Use CSS instead\n    compact: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean, // Lists. Use CSS to reduce space between items instead\n    declare: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean, // `<object>`\n    event: null, // `<script>`\n    face: null, // `<font>`. Use CSS instead\n    frame: null, // `<table>`\n    frameBorder: null, // `<iframe>`. Use CSS `border` instead\n    hSpace: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<img>` and `<object>`\n    leftMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<body>`\n    link: null, // `<body>`. Use CSS `a:link {color: *}` instead\n    longDesc: null, // `<frame>`, `<iframe>`, and `<img>`. Use an `<a>`\n    lowSrc: null, // `<img>`. Use a `<picture>`\n    marginHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<body>`\n    marginWidth: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<body>`\n    noResize: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean, // `<frame>`\n    noHref: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean, // `<area>`. Use no href instead of an explicit `nohref`\n    noShade: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean, // `<hr>`. Use background-color and height instead of borders\n    noWrap: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean, // `<td>` and `<th>`\n    object: null, // `<applet>`\n    profile: null, // `<head>`\n    prompt: null, // `<isindex>`\n    rev: null, // `<link>`\n    rightMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<body>`\n    rules: null, // `<table>`\n    scheme: null, // `<meta>`\n    scrolling: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.booleanish, // `<frame>`. Use overflow in the child context\n    standby: null, // `<object>`\n    summary: null, // `<table>`\n    text: null, // `<body>`. Use CSS `color` instead\n    topMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<body>`\n    valueType: null, // `<param>`\n    version: null, // `<html>`. Use a doctype.\n    vAlign: null, // Several. Use CSS `vertical-align` instead\n    vLink: null, // `<body>`. Use CSS `a:visited {color}` instead\n    vSpace: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<img>` and `<object>`\n\n    // Non-standard Properties.\n    allowTransparency: null,\n    autoCorrect: null,\n    autoSave: null,\n    disablePictureInPicture: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    disableRemotePlayback: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    prefix: null,\n    property: null,\n    results: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    security: null,\n    unselectable: null\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIvaHRtbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBT3dCO0FBQ2U7QUFDc0M7O0FBRXRFLGFBQWEsdURBQU07QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILGFBQWEseUZBQXdCO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSwwREFBYztBQUMxQixtQkFBbUIsMERBQWM7QUFDakMsZUFBZSwwREFBYztBQUM3QjtBQUNBO0FBQ0EscUJBQXFCLG1EQUFPO0FBQzVCLHlCQUF5QixtREFBTztBQUNoQyxvQkFBb0IsbURBQU87QUFDM0I7QUFDQTtBQUNBLFdBQVcsbURBQU87QUFDbEI7QUFDQSxrQkFBa0IsMERBQWM7QUFDaEMsZUFBZSxtREFBTztBQUN0QixjQUFjLG1EQUFPO0FBQ3JCLGNBQWMsMERBQWM7QUFDNUI7QUFDQTtBQUNBLGFBQWEsbURBQU87QUFDcEI7QUFDQSxlQUFlLDBEQUFjO0FBQzdCLFVBQVUsa0RBQU07QUFDaEI7QUFDQTtBQUNBLHFCQUFxQixzREFBVTtBQUMvQixjQUFjLG1EQUFPO0FBQ3JCLGtCQUFrQiwwREFBYztBQUNoQyxZQUFZLGtEQUFNLEdBQUcsMERBQWM7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLG1EQUFPO0FBQ3BCLFdBQVcsbURBQU87QUFDbEI7QUFDQTtBQUNBLGNBQWMsbURBQU87QUFDckIsY0FBYyw2REFBaUI7QUFDL0IsZUFBZSxzREFBVTtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixtREFBTztBQUMzQjtBQUNBLGFBQWEsMERBQWM7QUFDM0IsWUFBWSxrREFBTTtBQUNsQixZQUFZLG1EQUFPO0FBQ25CLFVBQVUsa0RBQU07QUFDaEI7QUFDQTtBQUNBLGFBQWEsMERBQWM7QUFDM0IsZUFBZSwwREFBYztBQUM3QjtBQUNBO0FBQ0E7QUFDQSxXQUFXLG1EQUFPO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBLFdBQVcsbURBQU87QUFDbEI7QUFDQSxjQUFjLDBEQUFjO0FBQzVCLGFBQWEsMERBQWM7QUFDM0IsZUFBZSxtREFBTztBQUN0QixjQUFjLDBEQUFjO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVUsbURBQU87QUFDakIsU0FBUyxrREFBTTtBQUNmO0FBQ0E7QUFDQSxlQUFlLGtEQUFNO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBLGVBQWUsa0RBQU07QUFDckIsY0FBYyxtREFBTztBQUNyQixXQUFXLG1EQUFPO0FBQ2xCO0FBQ0E7QUFDQSxjQUFjLG1EQUFPO0FBQ3JCLGdCQUFnQixtREFBTztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVUsbURBQU87QUFDakIsYUFBYSxrREFBTTtBQUNuQjtBQUNBLFVBQVUsMERBQWM7QUFDeEI7QUFDQSxpQkFBaUIsbURBQU87QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsbURBQU87QUFDckI7QUFDQSxTQUFTLDBEQUFjO0FBQ3ZCLGNBQWMsbURBQU87QUFDckIsY0FBYyxtREFBTztBQUNyQixVQUFVLGtEQUFNO0FBQ2hCLGFBQWEsa0RBQU07QUFDbkIsYUFBYSwwREFBYztBQUMzQjtBQUNBLFlBQVksbURBQU87QUFDbkIsY0FBYyxtREFBTztBQUNyQixjQUFjLG1EQUFPO0FBQ3JCLHdCQUF3QixtREFBTztBQUMvQiw4QkFBOEIsbURBQU87QUFDckM7QUFDQTtBQUNBLFVBQVUsa0RBQU07QUFDaEI7QUFDQTtBQUNBLFVBQVUsa0RBQU07QUFDaEIsZ0JBQWdCLHNEQUFVO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxrREFBTTtBQUNqQjtBQUNBO0FBQ0EsY0FBYyxrREFBTTtBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixtREFBTztBQUMxQjtBQUNBLFdBQVcsc0RBQVU7QUFDckIsV0FBVyxrREFBTTtBQUNqQjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGlEQUFpRCxNQUFNO0FBQ3ZELGFBQWEsMERBQWM7QUFDM0I7QUFDQTtBQUNBO0FBQ0EsWUFBWSxrREFBTTtBQUNsQjtBQUNBLGtCQUFrQixrREFBTTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsbURBQU87QUFDcEIsYUFBYSxtREFBTztBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksa0RBQU07QUFDbEIsZ0JBQWdCLGtEQUFNO0FBQ3RCLDhDQUE4QyxTQUFTO0FBQ3ZEO0FBQ0E7QUFDQSxrQkFBa0Isa0RBQU07QUFDeEIsaUJBQWlCLGtEQUFNO0FBQ3ZCLGNBQWMsbURBQU87QUFDckIsWUFBWSxtREFBTztBQUNuQixhQUFhLG1EQUFPO0FBQ3BCLFlBQVksbURBQU87QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsa0RBQU07QUFDdkI7QUFDQTtBQUNBLGVBQWUsc0RBQVU7QUFDekI7QUFDQTtBQUNBO0FBQ0EsZUFBZSxrREFBTTtBQUNyQjtBQUNBO0FBQ0E7QUFDQSxrREFBa0QsTUFBTTtBQUN4RCxZQUFZLGtEQUFNOztBQUVsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QixtREFBTztBQUNwQywyQkFBMkIsbURBQU87QUFDbEM7QUFDQTtBQUNBLGFBQWEsa0RBQU07QUFDbkI7QUFDQTtBQUNBO0FBQ0EsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL2h0bWwuanM/ZDM3MyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICBib29sZWFuLFxuICBvdmVybG9hZGVkQm9vbGVhbixcbiAgYm9vbGVhbmlzaCxcbiAgbnVtYmVyLFxuICBzcGFjZVNlcGFyYXRlZCxcbiAgY29tbWFTZXBhcmF0ZWRcbn0gZnJvbSAnLi91dGlsL3R5cGVzLmpzJ1xuaW1wb3J0IHtjcmVhdGV9IGZyb20gJy4vdXRpbC9jcmVhdGUuanMnXG5pbXBvcnQge2Nhc2VJbnNlbnNpdGl2ZVRyYW5zZm9ybX0gZnJvbSAnLi91dGlsL2Nhc2UtaW5zZW5zaXRpdmUtdHJhbnNmb3JtLmpzJ1xuXG5leHBvcnQgY29uc3QgaHRtbCA9IGNyZWF0ZSh7XG4gIHNwYWNlOiAnaHRtbCcsXG4gIGF0dHJpYnV0ZXM6IHtcbiAgICBhY2NlcHRjaGFyc2V0OiAnYWNjZXB0LWNoYXJzZXQnLFxuICAgIGNsYXNzbmFtZTogJ2NsYXNzJyxcbiAgICBodG1sZm9yOiAnZm9yJyxcbiAgICBodHRwZXF1aXY6ICdodHRwLWVxdWl2J1xuICB9LFxuICB0cmFuc2Zvcm06IGNhc2VJbnNlbnNpdGl2ZVRyYW5zZm9ybSxcbiAgbXVzdFVzZVByb3BlcnR5OiBbJ2NoZWNrZWQnLCAnbXVsdGlwbGUnLCAnbXV0ZWQnLCAnc2VsZWN0ZWQnXSxcbiAgcHJvcGVydGllczoge1xuICAgIC8vIFN0YW5kYXJkIFByb3BlcnRpZXMuXG4gICAgYWJicjogbnVsbCxcbiAgICBhY2NlcHQ6IGNvbW1hU2VwYXJhdGVkLFxuICAgIGFjY2VwdENoYXJzZXQ6IHNwYWNlU2VwYXJhdGVkLFxuICAgIGFjY2Vzc0tleTogc3BhY2VTZXBhcmF0ZWQsXG4gICAgYWN0aW9uOiBudWxsLFxuICAgIGFsbG93OiBudWxsLFxuICAgIGFsbG93RnVsbFNjcmVlbjogYm9vbGVhbixcbiAgICBhbGxvd1BheW1lbnRSZXF1ZXN0OiBib29sZWFuLFxuICAgIGFsbG93VXNlck1lZGlhOiBib29sZWFuLFxuICAgIGFsdDogbnVsbCxcbiAgICBhczogbnVsbCxcbiAgICBhc3luYzogYm9vbGVhbixcbiAgICBhdXRvQ2FwaXRhbGl6ZTogbnVsbCxcbiAgICBhdXRvQ29tcGxldGU6IHNwYWNlU2VwYXJhdGVkLFxuICAgIGF1dG9Gb2N1czogYm9vbGVhbixcbiAgICBhdXRvUGxheTogYm9vbGVhbixcbiAgICBibG9ja2luZzogc3BhY2VTZXBhcmF0ZWQsXG4gICAgY2FwdHVyZTogbnVsbCxcbiAgICBjaGFyU2V0OiBudWxsLFxuICAgIGNoZWNrZWQ6IGJvb2xlYW4sXG4gICAgY2l0ZTogbnVsbCxcbiAgICBjbGFzc05hbWU6IHNwYWNlU2VwYXJhdGVkLFxuICAgIGNvbHM6IG51bWJlcixcbiAgICBjb2xTcGFuOiBudWxsLFxuICAgIGNvbnRlbnQ6IG51bGwsXG4gICAgY29udGVudEVkaXRhYmxlOiBib29sZWFuaXNoLFxuICAgIGNvbnRyb2xzOiBib29sZWFuLFxuICAgIGNvbnRyb2xzTGlzdDogc3BhY2VTZXBhcmF0ZWQsXG4gICAgY29vcmRzOiBudW1iZXIgfCBjb21tYVNlcGFyYXRlZCxcbiAgICBjcm9zc09yaWdpbjogbnVsbCxcbiAgICBkYXRhOiBudWxsLFxuICAgIGRhdGVUaW1lOiBudWxsLFxuICAgIGRlY29kaW5nOiBudWxsLFxuICAgIGRlZmF1bHQ6IGJvb2xlYW4sXG4gICAgZGVmZXI6IGJvb2xlYW4sXG4gICAgZGlyOiBudWxsLFxuICAgIGRpck5hbWU6IG51bGwsXG4gICAgZGlzYWJsZWQ6IGJvb2xlYW4sXG4gICAgZG93bmxvYWQ6IG92ZXJsb2FkZWRCb29sZWFuLFxuICAgIGRyYWdnYWJsZTogYm9vbGVhbmlzaCxcbiAgICBlbmNUeXBlOiBudWxsLFxuICAgIGVudGVyS2V5SGludDogbnVsbCxcbiAgICBmZXRjaFByaW9yaXR5OiBudWxsLFxuICAgIGZvcm06IG51bGwsXG4gICAgZm9ybUFjdGlvbjogbnVsbCxcbiAgICBmb3JtRW5jVHlwZTogbnVsbCxcbiAgICBmb3JtTWV0aG9kOiBudWxsLFxuICAgIGZvcm1Ob1ZhbGlkYXRlOiBib29sZWFuLFxuICAgIGZvcm1UYXJnZXQ6IG51bGwsXG4gICAgaGVhZGVyczogc3BhY2VTZXBhcmF0ZWQsXG4gICAgaGVpZ2h0OiBudW1iZXIsXG4gICAgaGlkZGVuOiBib29sZWFuLFxuICAgIGhpZ2g6IG51bWJlcixcbiAgICBocmVmOiBudWxsLFxuICAgIGhyZWZMYW5nOiBudWxsLFxuICAgIGh0bWxGb3I6IHNwYWNlU2VwYXJhdGVkLFxuICAgIGh0dHBFcXVpdjogc3BhY2VTZXBhcmF0ZWQsXG4gICAgaWQ6IG51bGwsXG4gICAgaW1hZ2VTaXplczogbnVsbCxcbiAgICBpbWFnZVNyY1NldDogbnVsbCxcbiAgICBpbmVydDogYm9vbGVhbixcbiAgICBpbnB1dE1vZGU6IG51bGwsXG4gICAgaW50ZWdyaXR5OiBudWxsLFxuICAgIGlzOiBudWxsLFxuICAgIGlzTWFwOiBib29sZWFuLFxuICAgIGl0ZW1JZDogbnVsbCxcbiAgICBpdGVtUHJvcDogc3BhY2VTZXBhcmF0ZWQsXG4gICAgaXRlbVJlZjogc3BhY2VTZXBhcmF0ZWQsXG4gICAgaXRlbVNjb3BlOiBib29sZWFuLFxuICAgIGl0ZW1UeXBlOiBzcGFjZVNlcGFyYXRlZCxcbiAgICBraW5kOiBudWxsLFxuICAgIGxhYmVsOiBudWxsLFxuICAgIGxhbmc6IG51bGwsXG4gICAgbGFuZ3VhZ2U6IG51bGwsXG4gICAgbGlzdDogbnVsbCxcbiAgICBsb2FkaW5nOiBudWxsLFxuICAgIGxvb3A6IGJvb2xlYW4sXG4gICAgbG93OiBudW1iZXIsXG4gICAgbWFuaWZlc3Q6IG51bGwsXG4gICAgbWF4OiBudWxsLFxuICAgIG1heExlbmd0aDogbnVtYmVyLFxuICAgIG1lZGlhOiBudWxsLFxuICAgIG1ldGhvZDogbnVsbCxcbiAgICBtaW46IG51bGwsXG4gICAgbWluTGVuZ3RoOiBudW1iZXIsXG4gICAgbXVsdGlwbGU6IGJvb2xlYW4sXG4gICAgbXV0ZWQ6IGJvb2xlYW4sXG4gICAgbmFtZTogbnVsbCxcbiAgICBub25jZTogbnVsbCxcbiAgICBub01vZHVsZTogYm9vbGVhbixcbiAgICBub1ZhbGlkYXRlOiBib29sZWFuLFxuICAgIG9uQWJvcnQ6IG51bGwsXG4gICAgb25BZnRlclByaW50OiBudWxsLFxuICAgIG9uQXV4Q2xpY2s6IG51bGwsXG4gICAgb25CZWZvcmVNYXRjaDogbnVsbCxcbiAgICBvbkJlZm9yZVByaW50OiBudWxsLFxuICAgIG9uQmVmb3JlVG9nZ2xlOiBudWxsLFxuICAgIG9uQmVmb3JlVW5sb2FkOiBudWxsLFxuICAgIG9uQmx1cjogbnVsbCxcbiAgICBvbkNhbmNlbDogbnVsbCxcbiAgICBvbkNhblBsYXk6IG51bGwsXG4gICAgb25DYW5QbGF5VGhyb3VnaDogbnVsbCxcbiAgICBvbkNoYW5nZTogbnVsbCxcbiAgICBvbkNsaWNrOiBudWxsLFxuICAgIG9uQ2xvc2U6IG51bGwsXG4gICAgb25Db250ZXh0TG9zdDogbnVsbCxcbiAgICBvbkNvbnRleHRNZW51OiBudWxsLFxuICAgIG9uQ29udGV4dFJlc3RvcmVkOiBudWxsLFxuICAgIG9uQ29weTogbnVsbCxcbiAgICBvbkN1ZUNoYW5nZTogbnVsbCxcbiAgICBvbkN1dDogbnVsbCxcbiAgICBvbkRibENsaWNrOiBudWxsLFxuICAgIG9uRHJhZzogbnVsbCxcbiAgICBvbkRyYWdFbmQ6IG51bGwsXG4gICAgb25EcmFnRW50ZXI6IG51bGwsXG4gICAgb25EcmFnRXhpdDogbnVsbCxcbiAgICBvbkRyYWdMZWF2ZTogbnVsbCxcbiAgICBvbkRyYWdPdmVyOiBudWxsLFxuICAgIG9uRHJhZ1N0YXJ0OiBudWxsLFxuICAgIG9uRHJvcDogbnVsbCxcbiAgICBvbkR1cmF0aW9uQ2hhbmdlOiBudWxsLFxuICAgIG9uRW1wdGllZDogbnVsbCxcbiAgICBvbkVuZGVkOiBudWxsLFxuICAgIG9uRXJyb3I6IG51bGwsXG4gICAgb25Gb2N1czogbnVsbCxcbiAgICBvbkZvcm1EYXRhOiBudWxsLFxuICAgIG9uSGFzaENoYW5nZTogbnVsbCxcbiAgICBvbklucHV0OiBudWxsLFxuICAgIG9uSW52YWxpZDogbnVsbCxcbiAgICBvbktleURvd246IG51bGwsXG4gICAgb25LZXlQcmVzczogbnVsbCxcbiAgICBvbktleVVwOiBudWxsLFxuICAgIG9uTGFuZ3VhZ2VDaGFuZ2U6IG51bGwsXG4gICAgb25Mb2FkOiBudWxsLFxuICAgIG9uTG9hZGVkRGF0YTogbnVsbCxcbiAgICBvbkxvYWRlZE1ldGFkYXRhOiBudWxsLFxuICAgIG9uTG9hZEVuZDogbnVsbCxcbiAgICBvbkxvYWRTdGFydDogbnVsbCxcbiAgICBvbk1lc3NhZ2U6IG51bGwsXG4gICAgb25NZXNzYWdlRXJyb3I6IG51bGwsXG4gICAgb25Nb3VzZURvd246IG51bGwsXG4gICAgb25Nb3VzZUVudGVyOiBudWxsLFxuICAgIG9uTW91c2VMZWF2ZTogbnVsbCxcbiAgICBvbk1vdXNlTW92ZTogbnVsbCxcbiAgICBvbk1vdXNlT3V0OiBudWxsLFxuICAgIG9uTW91c2VPdmVyOiBudWxsLFxuICAgIG9uTW91c2VVcDogbnVsbCxcbiAgICBvbk9mZmxpbmU6IG51bGwsXG4gICAgb25PbmxpbmU6IG51bGwsXG4gICAgb25QYWdlSGlkZTogbnVsbCxcbiAgICBvblBhZ2VTaG93OiBudWxsLFxuICAgIG9uUGFzdGU6IG51bGwsXG4gICAgb25QYXVzZTogbnVsbCxcbiAgICBvblBsYXk6IG51bGwsXG4gICAgb25QbGF5aW5nOiBudWxsLFxuICAgIG9uUG9wU3RhdGU6IG51bGwsXG4gICAgb25Qcm9ncmVzczogbnVsbCxcbiAgICBvblJhdGVDaGFuZ2U6IG51bGwsXG4gICAgb25SZWplY3Rpb25IYW5kbGVkOiBudWxsLFxuICAgIG9uUmVzZXQ6IG51bGwsXG4gICAgb25SZXNpemU6IG51bGwsXG4gICAgb25TY3JvbGw6IG51bGwsXG4gICAgb25TY3JvbGxFbmQ6IG51bGwsXG4gICAgb25TZWN1cml0eVBvbGljeVZpb2xhdGlvbjogbnVsbCxcbiAgICBvblNlZWtlZDogbnVsbCxcbiAgICBvblNlZWtpbmc6IG51bGwsXG4gICAgb25TZWxlY3Q6IG51bGwsXG4gICAgb25TbG90Q2hhbmdlOiBudWxsLFxuICAgIG9uU3RhbGxlZDogbnVsbCxcbiAgICBvblN0b3JhZ2U6IG51bGwsXG4gICAgb25TdWJtaXQ6IG51bGwsXG4gICAgb25TdXNwZW5kOiBudWxsLFxuICAgIG9uVGltZVVwZGF0ZTogbnVsbCxcbiAgICBvblRvZ2dsZTogbnVsbCxcbiAgICBvblVuaGFuZGxlZFJlamVjdGlvbjogbnVsbCxcbiAgICBvblVubG9hZDogbnVsbCxcbiAgICBvblZvbHVtZUNoYW5nZTogbnVsbCxcbiAgICBvbldhaXRpbmc6IG51bGwsXG4gICAgb25XaGVlbDogbnVsbCxcbiAgICBvcGVuOiBib29sZWFuLFxuICAgIG9wdGltdW06IG51bWJlcixcbiAgICBwYXR0ZXJuOiBudWxsLFxuICAgIHBpbmc6IHNwYWNlU2VwYXJhdGVkLFxuICAgIHBsYWNlaG9sZGVyOiBudWxsLFxuICAgIHBsYXlzSW5saW5lOiBib29sZWFuLFxuICAgIHBvcG92ZXI6IG51bGwsXG4gICAgcG9wb3ZlclRhcmdldDogbnVsbCxcbiAgICBwb3BvdmVyVGFyZ2V0QWN0aW9uOiBudWxsLFxuICAgIHBvc3RlcjogbnVsbCxcbiAgICBwcmVsb2FkOiBudWxsLFxuICAgIHJlYWRPbmx5OiBib29sZWFuLFxuICAgIHJlZmVycmVyUG9saWN5OiBudWxsLFxuICAgIHJlbDogc3BhY2VTZXBhcmF0ZWQsXG4gICAgcmVxdWlyZWQ6IGJvb2xlYW4sXG4gICAgcmV2ZXJzZWQ6IGJvb2xlYW4sXG4gICAgcm93czogbnVtYmVyLFxuICAgIHJvd1NwYW46IG51bWJlcixcbiAgICBzYW5kYm94OiBzcGFjZVNlcGFyYXRlZCxcbiAgICBzY29wZTogbnVsbCxcbiAgICBzY29wZWQ6IGJvb2xlYW4sXG4gICAgc2VhbWxlc3M6IGJvb2xlYW4sXG4gICAgc2VsZWN0ZWQ6IGJvb2xlYW4sXG4gICAgc2hhZG93Um9vdENsb25hYmxlOiBib29sZWFuLFxuICAgIHNoYWRvd1Jvb3REZWxlZ2F0ZXNGb2N1czogYm9vbGVhbixcbiAgICBzaGFkb3dSb290TW9kZTogbnVsbCxcbiAgICBzaGFwZTogbnVsbCxcbiAgICBzaXplOiBudW1iZXIsXG4gICAgc2l6ZXM6IG51bGwsXG4gICAgc2xvdDogbnVsbCxcbiAgICBzcGFuOiBudW1iZXIsXG4gICAgc3BlbGxDaGVjazogYm9vbGVhbmlzaCxcbiAgICBzcmM6IG51bGwsXG4gICAgc3JjRG9jOiBudWxsLFxuICAgIHNyY0xhbmc6IG51bGwsXG4gICAgc3JjU2V0OiBudWxsLFxuICAgIHN0YXJ0OiBudW1iZXIsXG4gICAgc3RlcDogbnVsbCxcbiAgICBzdHlsZTogbnVsbCxcbiAgICB0YWJJbmRleDogbnVtYmVyLFxuICAgIHRhcmdldDogbnVsbCxcbiAgICB0aXRsZTogbnVsbCxcbiAgICB0cmFuc2xhdGU6IG51bGwsXG4gICAgdHlwZTogbnVsbCxcbiAgICB0eXBlTXVzdE1hdGNoOiBib29sZWFuLFxuICAgIHVzZU1hcDogbnVsbCxcbiAgICB2YWx1ZTogYm9vbGVhbmlzaCxcbiAgICB3aWR0aDogbnVtYmVyLFxuICAgIHdyYXA6IG51bGwsXG4gICAgd3JpdGluZ1N1Z2dlc3Rpb25zOiBudWxsLFxuXG4gICAgLy8gTGVnYWN5LlxuICAgIC8vIFNlZTogaHR0cHM6Ly9odG1sLnNwZWMud2hhdHdnLm9yZy8jb3RoZXItZWxlbWVudHMsLWF0dHJpYnV0ZXMtYW5kLWFwaXNcbiAgICBhbGlnbjogbnVsbCwgLy8gU2V2ZXJhbC4gVXNlIENTUyBgdGV4dC1hbGlnbmAgaW5zdGVhZCxcbiAgICBhTGluazogbnVsbCwgLy8gYDxib2R5PmAuIFVzZSBDU1MgYGE6YWN0aXZlIHtjb2xvcn1gIGluc3RlYWRcbiAgICBhcmNoaXZlOiBzcGFjZVNlcGFyYXRlZCwgLy8gYDxvYmplY3Q+YC4gTGlzdCBvZiBVUklzIHRvIGFyY2hpdmVzXG4gICAgYXhpczogbnVsbCwgLy8gYDx0ZD5gIGFuZCBgPHRoPmAuIFVzZSBgc2NvcGVgIG9uIGA8dGg+YFxuICAgIGJhY2tncm91bmQ6IG51bGwsIC8vIGA8Ym9keT5gLiBVc2UgQ1NTIGBiYWNrZ3JvdW5kLWltYWdlYCBpbnN0ZWFkXG4gICAgYmdDb2xvcjogbnVsbCwgLy8gYDxib2R5PmAgYW5kIHRhYmxlIGVsZW1lbnRzLiBVc2UgQ1NTIGBiYWNrZ3JvdW5kLWNvbG9yYCBpbnN0ZWFkXG4gICAgYm9yZGVyOiBudW1iZXIsIC8vIGA8dGFibGU+YC4gVXNlIENTUyBgYm9yZGVyLXdpZHRoYCBpbnN0ZWFkLFxuICAgIGJvcmRlckNvbG9yOiBudWxsLCAvLyBgPHRhYmxlPmAuIFVzZSBDU1MgYGJvcmRlci1jb2xvcmAgaW5zdGVhZCxcbiAgICBib3R0b21NYXJnaW46IG51bWJlciwgLy8gYDxib2R5PmBcbiAgICBjZWxsUGFkZGluZzogbnVsbCwgLy8gYDx0YWJsZT5gXG4gICAgY2VsbFNwYWNpbmc6IG51bGwsIC8vIGA8dGFibGU+YFxuICAgIGNoYXI6IG51bGwsIC8vIFNldmVyYWwgdGFibGUgZWxlbWVudHMuIFdoZW4gYGFsaWduPWNoYXJgLCBzZXRzIHRoZSBjaGFyYWN0ZXIgdG8gYWxpZ24gb25cbiAgICBjaGFyT2ZmOiBudWxsLCAvLyBTZXZlcmFsIHRhYmxlIGVsZW1lbnRzLiBXaGVuIGBjaGFyYCwgb2Zmc2V0cyB0aGUgYWxpZ25tZW50XG4gICAgY2xhc3NJZDogbnVsbCwgLy8gYDxvYmplY3Q+YFxuICAgIGNsZWFyOiBudWxsLCAvLyBgPGJyPmAuIFVzZSBDU1MgYGNsZWFyYCBpbnN0ZWFkXG4gICAgY29kZTogbnVsbCwgLy8gYDxvYmplY3Q+YFxuICAgIGNvZGVCYXNlOiBudWxsLCAvLyBgPG9iamVjdD5gXG4gICAgY29kZVR5cGU6IG51bGwsIC8vIGA8b2JqZWN0PmBcbiAgICBjb2xvcjogbnVsbCwgLy8gYDxmb250PmAgYW5kIGA8aHI+YC4gVXNlIENTUyBpbnN0ZWFkXG4gICAgY29tcGFjdDogYm9vbGVhbiwgLy8gTGlzdHMuIFVzZSBDU1MgdG8gcmVkdWNlIHNwYWNlIGJldHdlZW4gaXRlbXMgaW5zdGVhZFxuICAgIGRlY2xhcmU6IGJvb2xlYW4sIC8vIGA8b2JqZWN0PmBcbiAgICBldmVudDogbnVsbCwgLy8gYDxzY3JpcHQ+YFxuICAgIGZhY2U6IG51bGwsIC8vIGA8Zm9udD5gLiBVc2UgQ1NTIGluc3RlYWRcbiAgICBmcmFtZTogbnVsbCwgLy8gYDx0YWJsZT5gXG4gICAgZnJhbWVCb3JkZXI6IG51bGwsIC8vIGA8aWZyYW1lPmAuIFVzZSBDU1MgYGJvcmRlcmAgaW5zdGVhZFxuICAgIGhTcGFjZTogbnVtYmVyLCAvLyBgPGltZz5gIGFuZCBgPG9iamVjdD5gXG4gICAgbGVmdE1hcmdpbjogbnVtYmVyLCAvLyBgPGJvZHk+YFxuICAgIGxpbms6IG51bGwsIC8vIGA8Ym9keT5gLiBVc2UgQ1NTIGBhOmxpbmsge2NvbG9yOiAqfWAgaW5zdGVhZFxuICAgIGxvbmdEZXNjOiBudWxsLCAvLyBgPGZyYW1lPmAsIGA8aWZyYW1lPmAsIGFuZCBgPGltZz5gLiBVc2UgYW4gYDxhPmBcbiAgICBsb3dTcmM6IG51bGwsIC8vIGA8aW1nPmAuIFVzZSBhIGA8cGljdHVyZT5gXG4gICAgbWFyZ2luSGVpZ2h0OiBudW1iZXIsIC8vIGA8Ym9keT5gXG4gICAgbWFyZ2luV2lkdGg6IG51bWJlciwgLy8gYDxib2R5PmBcbiAgICBub1Jlc2l6ZTogYm9vbGVhbiwgLy8gYDxmcmFtZT5gXG4gICAgbm9IcmVmOiBib29sZWFuLCAvLyBgPGFyZWE+YC4gVXNlIG5vIGhyZWYgaW5zdGVhZCBvZiBhbiBleHBsaWNpdCBgbm9ocmVmYFxuICAgIG5vU2hhZGU6IGJvb2xlYW4sIC8vIGA8aHI+YC4gVXNlIGJhY2tncm91bmQtY29sb3IgYW5kIGhlaWdodCBpbnN0ZWFkIG9mIGJvcmRlcnNcbiAgICBub1dyYXA6IGJvb2xlYW4sIC8vIGA8dGQ+YCBhbmQgYDx0aD5gXG4gICAgb2JqZWN0OiBudWxsLCAvLyBgPGFwcGxldD5gXG4gICAgcHJvZmlsZTogbnVsbCwgLy8gYDxoZWFkPmBcbiAgICBwcm9tcHQ6IG51bGwsIC8vIGA8aXNpbmRleD5gXG4gICAgcmV2OiBudWxsLCAvLyBgPGxpbms+YFxuICAgIHJpZ2h0TWFyZ2luOiBudW1iZXIsIC8vIGA8Ym9keT5gXG4gICAgcnVsZXM6IG51bGwsIC8vIGA8dGFibGU+YFxuICAgIHNjaGVtZTogbnVsbCwgLy8gYDxtZXRhPmBcbiAgICBzY3JvbGxpbmc6IGJvb2xlYW5pc2gsIC8vIGA8ZnJhbWU+YC4gVXNlIG92ZXJmbG93IGluIHRoZSBjaGlsZCBjb250ZXh0XG4gICAgc3RhbmRieTogbnVsbCwgLy8gYDxvYmplY3Q+YFxuICAgIHN1bW1hcnk6IG51bGwsIC8vIGA8dGFibGU+YFxuICAgIHRleHQ6IG51bGwsIC8vIGA8Ym9keT5gLiBVc2UgQ1NTIGBjb2xvcmAgaW5zdGVhZFxuICAgIHRvcE1hcmdpbjogbnVtYmVyLCAvLyBgPGJvZHk+YFxuICAgIHZhbHVlVHlwZTogbnVsbCwgLy8gYDxwYXJhbT5gXG4gICAgdmVyc2lvbjogbnVsbCwgLy8gYDxodG1sPmAuIFVzZSBhIGRvY3R5cGUuXG4gICAgdkFsaWduOiBudWxsLCAvLyBTZXZlcmFsLiBVc2UgQ1NTIGB2ZXJ0aWNhbC1hbGlnbmAgaW5zdGVhZFxuICAgIHZMaW5rOiBudWxsLCAvLyBgPGJvZHk+YC4gVXNlIENTUyBgYTp2aXNpdGVkIHtjb2xvcn1gIGluc3RlYWRcbiAgICB2U3BhY2U6IG51bWJlciwgLy8gYDxpbWc+YCBhbmQgYDxvYmplY3Q+YFxuXG4gICAgLy8gTm9uLXN0YW5kYXJkIFByb3BlcnRpZXMuXG4gICAgYWxsb3dUcmFuc3BhcmVuY3k6IG51bGwsXG4gICAgYXV0b0NvcnJlY3Q6IG51bGwsXG4gICAgYXV0b1NhdmU6IG51bGwsXG4gICAgZGlzYWJsZVBpY3R1cmVJblBpY3R1cmU6IGJvb2xlYW4sXG4gICAgZGlzYWJsZVJlbW90ZVBsYXliYWNrOiBib29sZWFuLFxuICAgIHByZWZpeDogbnVsbCxcbiAgICBwcm9wZXJ0eTogbnVsbCxcbiAgICByZXN1bHRzOiBudW1iZXIsXG4gICAgc2VjdXJpdHk6IG51bGwsXG4gICAgdW5zZWxlY3RhYmxlOiBudWxsXG4gIH1cbn0pXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/property-information/lib/html.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/property-information/lib/normalize.js":
/*!************************************************************!*\
  !*** ./node_modules/property-information/lib/normalize.js ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   normalize: function() { return /* binding */ normalize; }\n/* harmony export */ });\n/**\n * @param {string} value\n * @returns {string}\n */\nfunction normalize(value) {\n  return value.toLowerCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIvbm9ybWFsaXplLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi9ub3JtYWxpemUuanM/MjYxMSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBwYXJhbSB7c3RyaW5nfSB2YWx1ZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIG5vcm1hbGl6ZSh2YWx1ZSkge1xuICByZXR1cm4gdmFsdWUudG9Mb3dlckNhc2UoKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/property-information/lib/normalize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/property-information/lib/svg.js":
/*!******************************************************!*\
  !*** ./node_modules/property-information/lib/svg.js ***!
  \******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   svg: function() { return /* binding */ svg; }\n/* harmony export */ });\n/* harmony import */ var _util_types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/types.js */ \"(app-pages-browser)/./node_modules/property-information/lib/util/types.js\");\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(app-pages-browser)/./node_modules/property-information/lib/util/create.js\");\n/* harmony import */ var _util_case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/case-sensitive-transform.js */ \"(app-pages-browser)/./node_modules/property-information/lib/util/case-sensitive-transform.js\");\n\n\n\n\nconst svg = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  space: 'svg',\n  attributes: {\n    accentHeight: 'accent-height',\n    alignmentBaseline: 'alignment-baseline',\n    arabicForm: 'arabic-form',\n    baselineShift: 'baseline-shift',\n    capHeight: 'cap-height',\n    className: 'class',\n    clipPath: 'clip-path',\n    clipRule: 'clip-rule',\n    colorInterpolation: 'color-interpolation',\n    colorInterpolationFilters: 'color-interpolation-filters',\n    colorProfile: 'color-profile',\n    colorRendering: 'color-rendering',\n    crossOrigin: 'crossorigin',\n    dataType: 'datatype',\n    dominantBaseline: 'dominant-baseline',\n    enableBackground: 'enable-background',\n    fillOpacity: 'fill-opacity',\n    fillRule: 'fill-rule',\n    floodColor: 'flood-color',\n    floodOpacity: 'flood-opacity',\n    fontFamily: 'font-family',\n    fontSize: 'font-size',\n    fontSizeAdjust: 'font-size-adjust',\n    fontStretch: 'font-stretch',\n    fontStyle: 'font-style',\n    fontVariant: 'font-variant',\n    fontWeight: 'font-weight',\n    glyphName: 'glyph-name',\n    glyphOrientationHorizontal: 'glyph-orientation-horizontal',\n    glyphOrientationVertical: 'glyph-orientation-vertical',\n    hrefLang: 'hreflang',\n    horizAdvX: 'horiz-adv-x',\n    horizOriginX: 'horiz-origin-x',\n    horizOriginY: 'horiz-origin-y',\n    imageRendering: 'image-rendering',\n    letterSpacing: 'letter-spacing',\n    lightingColor: 'lighting-color',\n    markerEnd: 'marker-end',\n    markerMid: 'marker-mid',\n    markerStart: 'marker-start',\n    navDown: 'nav-down',\n    navDownLeft: 'nav-down-left',\n    navDownRight: 'nav-down-right',\n    navLeft: 'nav-left',\n    navNext: 'nav-next',\n    navPrev: 'nav-prev',\n    navRight: 'nav-right',\n    navUp: 'nav-up',\n    navUpLeft: 'nav-up-left',\n    navUpRight: 'nav-up-right',\n    onAbort: 'onabort',\n    onActivate: 'onactivate',\n    onAfterPrint: 'onafterprint',\n    onBeforePrint: 'onbeforeprint',\n    onBegin: 'onbegin',\n    onCancel: 'oncancel',\n    onCanPlay: 'oncanplay',\n    onCanPlayThrough: 'oncanplaythrough',\n    onChange: 'onchange',\n    onClick: 'onclick',\n    onClose: 'onclose',\n    onCopy: 'oncopy',\n    onCueChange: 'oncuechange',\n    onCut: 'oncut',\n    onDblClick: 'ondblclick',\n    onDrag: 'ondrag',\n    onDragEnd: 'ondragend',\n    onDragEnter: 'ondragenter',\n    onDragExit: 'ondragexit',\n    onDragLeave: 'ondragleave',\n    onDragOver: 'ondragover',\n    onDragStart: 'ondragstart',\n    onDrop: 'ondrop',\n    onDurationChange: 'ondurationchange',\n    onEmptied: 'onemptied',\n    onEnd: 'onend',\n    onEnded: 'onended',\n    onError: 'onerror',\n    onFocus: 'onfocus',\n    onFocusIn: 'onfocusin',\n    onFocusOut: 'onfocusout',\n    onHashChange: 'onhashchange',\n    onInput: 'oninput',\n    onInvalid: 'oninvalid',\n    onKeyDown: 'onkeydown',\n    onKeyPress: 'onkeypress',\n    onKeyUp: 'onkeyup',\n    onLoad: 'onload',\n    onLoadedData: 'onloadeddata',\n    onLoadedMetadata: 'onloadedmetadata',\n    onLoadStart: 'onloadstart',\n    onMessage: 'onmessage',\n    onMouseDown: 'onmousedown',\n    onMouseEnter: 'onmouseenter',\n    onMouseLeave: 'onmouseleave',\n    onMouseMove: 'onmousemove',\n    onMouseOut: 'onmouseout',\n    onMouseOver: 'onmouseover',\n    onMouseUp: 'onmouseup',\n    onMouseWheel: 'onmousewheel',\n    onOffline: 'onoffline',\n    onOnline: 'ononline',\n    onPageHide: 'onpagehide',\n    onPageShow: 'onpageshow',\n    onPaste: 'onpaste',\n    onPause: 'onpause',\n    onPlay: 'onplay',\n    onPlaying: 'onplaying',\n    onPopState: 'onpopstate',\n    onProgress: 'onprogress',\n    onRateChange: 'onratechange',\n    onRepeat: 'onrepeat',\n    onReset: 'onreset',\n    onResize: 'onresize',\n    onScroll: 'onscroll',\n    onSeeked: 'onseeked',\n    onSeeking: 'onseeking',\n    onSelect: 'onselect',\n    onShow: 'onshow',\n    onStalled: 'onstalled',\n    onStorage: 'onstorage',\n    onSubmit: 'onsubmit',\n    onSuspend: 'onsuspend',\n    onTimeUpdate: 'ontimeupdate',\n    onToggle: 'ontoggle',\n    onUnload: 'onunload',\n    onVolumeChange: 'onvolumechange',\n    onWaiting: 'onwaiting',\n    onZoom: 'onzoom',\n    overlinePosition: 'overline-position',\n    overlineThickness: 'overline-thickness',\n    paintOrder: 'paint-order',\n    panose1: 'panose-1',\n    pointerEvents: 'pointer-events',\n    referrerPolicy: 'referrerpolicy',\n    renderingIntent: 'rendering-intent',\n    shapeRendering: 'shape-rendering',\n    stopColor: 'stop-color',\n    stopOpacity: 'stop-opacity',\n    strikethroughPosition: 'strikethrough-position',\n    strikethroughThickness: 'strikethrough-thickness',\n    strokeDashArray: 'stroke-dasharray',\n    strokeDashOffset: 'stroke-dashoffset',\n    strokeLineCap: 'stroke-linecap',\n    strokeLineJoin: 'stroke-linejoin',\n    strokeMiterLimit: 'stroke-miterlimit',\n    strokeOpacity: 'stroke-opacity',\n    strokeWidth: 'stroke-width',\n    tabIndex: 'tabindex',\n    textAnchor: 'text-anchor',\n    textDecoration: 'text-decoration',\n    textRendering: 'text-rendering',\n    transformOrigin: 'transform-origin',\n    typeOf: 'typeof',\n    underlinePosition: 'underline-position',\n    underlineThickness: 'underline-thickness',\n    unicodeBidi: 'unicode-bidi',\n    unicodeRange: 'unicode-range',\n    unitsPerEm: 'units-per-em',\n    vAlphabetic: 'v-alphabetic',\n    vHanging: 'v-hanging',\n    vIdeographic: 'v-ideographic',\n    vMathematical: 'v-mathematical',\n    vectorEffect: 'vector-effect',\n    vertAdvY: 'vert-adv-y',\n    vertOriginX: 'vert-origin-x',\n    vertOriginY: 'vert-origin-y',\n    wordSpacing: 'word-spacing',\n    writingMode: 'writing-mode',\n    xHeight: 'x-height',\n    // These were camelcased in Tiny. Now lowercased in SVG 2\n    playbackOrder: 'playbackorder',\n    timelineBegin: 'timelinebegin'\n  },\n  transform: _util_case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__.caseSensitiveTransform,\n  properties: {\n    about: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    accentHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    accumulate: null,\n    additive: null,\n    alignmentBaseline: null,\n    alphabetic: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    amplitude: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    arabicForm: null,\n    ascent: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    attributeName: null,\n    attributeType: null,\n    azimuth: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    bandwidth: null,\n    baselineShift: null,\n    baseFrequency: null,\n    baseProfile: null,\n    bbox: null,\n    begin: null,\n    bias: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    by: null,\n    calcMode: null,\n    capHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    className: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    clip: null,\n    clipPath: null,\n    clipPathUnits: null,\n    clipRule: null,\n    color: null,\n    colorInterpolation: null,\n    colorInterpolationFilters: null,\n    colorProfile: null,\n    colorRendering: null,\n    content: null,\n    contentScriptType: null,\n    contentStyleType: null,\n    crossOrigin: null,\n    cursor: null,\n    cx: null,\n    cy: null,\n    d: null,\n    dataType: null,\n    defaultAction: null,\n    descent: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    diffuseConstant: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    direction: null,\n    display: null,\n    dur: null,\n    divisor: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    dominantBaseline: null,\n    download: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    dx: null,\n    dy: null,\n    edgeMode: null,\n    editable: null,\n    elevation: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    enableBackground: null,\n    end: null,\n    event: null,\n    exponent: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    externalResourcesRequired: null,\n    fill: null,\n    fillOpacity: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    fillRule: null,\n    filter: null,\n    filterRes: null,\n    filterUnits: null,\n    floodColor: null,\n    floodOpacity: null,\n    focusable: null,\n    focusHighlight: null,\n    fontFamily: null,\n    fontSize: null,\n    fontSizeAdjust: null,\n    fontStretch: null,\n    fontStyle: null,\n    fontVariant: null,\n    fontWeight: null,\n    format: null,\n    fr: null,\n    from: null,\n    fx: null,\n    fy: null,\n    g1: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaSeparated,\n    g2: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaSeparated,\n    glyphName: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaSeparated,\n    glyphOrientationHorizontal: null,\n    glyphOrientationVertical: null,\n    glyphRef: null,\n    gradientTransform: null,\n    gradientUnits: null,\n    handler: null,\n    hanging: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    hatchContentUnits: null,\n    hatchUnits: null,\n    height: null,\n    href: null,\n    hrefLang: null,\n    horizAdvX: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    horizOriginX: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    horizOriginY: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    id: null,\n    ideographic: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    imageRendering: null,\n    initialVisibility: null,\n    in: null,\n    in2: null,\n    intercept: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    k: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    k1: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    k2: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    k3: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    k4: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    kernelMatrix: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    kernelUnitLength: null,\n    keyPoints: null, // SEMI_COLON_SEPARATED\n    keySplines: null, // SEMI_COLON_SEPARATED\n    keyTimes: null, // SEMI_COLON_SEPARATED\n    kerning: null,\n    lang: null,\n    lengthAdjust: null,\n    letterSpacing: null,\n    lightingColor: null,\n    limitingConeAngle: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    local: null,\n    markerEnd: null,\n    markerMid: null,\n    markerStart: null,\n    markerHeight: null,\n    markerUnits: null,\n    markerWidth: null,\n    mask: null,\n    maskContentUnits: null,\n    maskUnits: null,\n    mathematical: null,\n    max: null,\n    media: null,\n    mediaCharacterEncoding: null,\n    mediaContentEncodings: null,\n    mediaSize: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    mediaTime: null,\n    method: null,\n    min: null,\n    mode: null,\n    name: null,\n    navDown: null,\n    navDownLeft: null,\n    navDownRight: null,\n    navLeft: null,\n    navNext: null,\n    navPrev: null,\n    navRight: null,\n    navUp: null,\n    navUpLeft: null,\n    navUpRight: null,\n    numOctaves: null,\n    observer: null,\n    offset: null,\n    onAbort: null,\n    onActivate: null,\n    onAfterPrint: null,\n    onBeforePrint: null,\n    onBegin: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnd: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFocusIn: null,\n    onFocusOut: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onMouseWheel: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRepeat: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onShow: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onZoom: null,\n    opacity: null,\n    operator: null,\n    order: null,\n    orient: null,\n    orientation: null,\n    origin: null,\n    overflow: null,\n    overlay: null,\n    overlinePosition: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    overlineThickness: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    paintOrder: null,\n    panose1: null,\n    path: null,\n    pathLength: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    patternContentUnits: null,\n    patternTransform: null,\n    patternUnits: null,\n    phase: null,\n    ping: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    pitch: null,\n    playbackOrder: null,\n    pointerEvents: null,\n    points: null,\n    pointsAtX: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    pointsAtY: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    pointsAtZ: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    preserveAlpha: null,\n    preserveAspectRatio: null,\n    primitiveUnits: null,\n    propagate: null,\n    property: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    r: null,\n    radius: null,\n    referrerPolicy: null,\n    refX: null,\n    refY: null,\n    rel: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    rev: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    renderingIntent: null,\n    repeatCount: null,\n    repeatDur: null,\n    requiredExtensions: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    requiredFeatures: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    requiredFonts: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    requiredFormats: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    resource: null,\n    restart: null,\n    result: null,\n    rotate: null,\n    rx: null,\n    ry: null,\n    scale: null,\n    seed: null,\n    shapeRendering: null,\n    side: null,\n    slope: null,\n    snapshotTime: null,\n    specularConstant: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    specularExponent: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    spreadMethod: null,\n    spacing: null,\n    startOffset: null,\n    stdDeviation: null,\n    stemh: null,\n    stemv: null,\n    stitchTiles: null,\n    stopColor: null,\n    stopOpacity: null,\n    strikethroughPosition: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    strikethroughThickness: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    string: null,\n    stroke: null,\n    strokeDashArray: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    strokeDashOffset: null,\n    strokeLineCap: null,\n    strokeLineJoin: null,\n    strokeMiterLimit: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    strokeOpacity: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    strokeWidth: null,\n    style: null,\n    surfaceScale: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    syncBehavior: null,\n    syncBehaviorDefault: null,\n    syncMaster: null,\n    syncTolerance: null,\n    syncToleranceDefault: null,\n    systemLanguage: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    tabIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    tableValues: null,\n    target: null,\n    targetX: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    targetY: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    textAnchor: null,\n    textDecoration: null,\n    textRendering: null,\n    textLength: null,\n    timelineBegin: null,\n    title: null,\n    transformBehavior: null,\n    type: null,\n    typeOf: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    to: null,\n    transform: null,\n    transformOrigin: null,\n    u1: null,\n    u2: null,\n    underlinePosition: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    underlineThickness: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    unicode: null,\n    unicodeBidi: null,\n    unicodeRange: null,\n    unitsPerEm: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    values: null,\n    vAlphabetic: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    vMathematical: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    vectorEffect: null,\n    vHanging: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    vIdeographic: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    version: null,\n    vertAdvY: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    vertOriginX: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    vertOriginY: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    viewBox: null,\n    viewTarget: null,\n    visibility: null,\n    width: null,\n    widths: null,\n    wordSpacing: null,\n    writingMode: null,\n    x: null,\n    x1: null,\n    x2: null,\n    xChannelSelector: null,\n    xHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    y: null,\n    y1: null,\n    y2: null,\n    yChannelSelector: null,\n    z: null,\n    zoomAndPan: null\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/property-information/lib/svg.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/property-information/lib/util/case-insensitive-transform.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/property-information/lib/util/case-insensitive-transform.js ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   caseInsensitiveTransform: function() { return /* binding */ caseInsensitiveTransform; }\n/* harmony export */ });\n/* harmony import */ var _case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./case-sensitive-transform.js */ \"(app-pages-browser)/./node_modules/property-information/lib/util/case-sensitive-transform.js\");\n\n\n/**\n * @param {Record<string, string>} attributes\n * @param {string} property\n * @returns {string}\n */\nfunction caseInsensitiveTransform(attributes, property) {\n  return (0,_case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_0__.caseSensitiveTransform)(attributes, property.toLowerCase())\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIvdXRpbC9jYXNlLWluc2Vuc2l0aXZlLXRyYW5zZm9ybS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFvRTs7QUFFcEU7QUFDQSxXQUFXLHdCQUF3QjtBQUNuQyxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ087QUFDUCxTQUFTLG9GQUFzQjtBQUMvQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvY2FzZS1pbnNlbnNpdGl2ZS10cmFuc2Zvcm0uanM/ODQ1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2Nhc2VTZW5zaXRpdmVUcmFuc2Zvcm19IGZyb20gJy4vY2FzZS1zZW5zaXRpdmUtdHJhbnNmb3JtLmpzJ1xuXG4vKipcbiAqIEBwYXJhbSB7UmVjb3JkPHN0cmluZywgc3RyaW5nPn0gYXR0cmlidXRlc1xuICogQHBhcmFtIHtzdHJpbmd9IHByb3BlcnR5XG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2FzZUluc2Vuc2l0aXZlVHJhbnNmb3JtKGF0dHJpYnV0ZXMsIHByb3BlcnR5KSB7XG4gIHJldHVybiBjYXNlU2Vuc2l0aXZlVHJhbnNmb3JtKGF0dHJpYnV0ZXMsIHByb3BlcnR5LnRvTG93ZXJDYXNlKCkpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/property-information/lib/util/case-insensitive-transform.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/property-information/lib/util/case-sensitive-transform.js":
/*!********************************************************************************!*\
  !*** ./node_modules/property-information/lib/util/case-sensitive-transform.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   caseSensitiveTransform: function() { return /* binding */ caseSensitiveTransform; }\n/* harmony export */ });\n/**\n * @param {Record<string, string>} attributes\n * @param {string} attribute\n * @returns {string}\n */\nfunction caseSensitiveTransform(attributes, attribute) {\n  return attribute in attributes ? attributes[attribute] : attribute\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIvdXRpbC9jYXNlLXNlbnNpdGl2ZS10cmFuc2Zvcm0uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsV0FBVyx3QkFBd0I7QUFDbkMsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvY2FzZS1zZW5zaXRpdmUtdHJhbnNmb3JtLmpzP2YzZmUiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAcGFyYW0ge1JlY29yZDxzdHJpbmcsIHN0cmluZz59IGF0dHJpYnV0ZXNcbiAqIEBwYXJhbSB7c3RyaW5nfSBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjYXNlU2Vuc2l0aXZlVHJhbnNmb3JtKGF0dHJpYnV0ZXMsIGF0dHJpYnV0ZSkge1xuICByZXR1cm4gYXR0cmlidXRlIGluIGF0dHJpYnV0ZXMgPyBhdHRyaWJ1dGVzW2F0dHJpYnV0ZV0gOiBhdHRyaWJ1dGVcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/property-information/lib/util/case-sensitive-transform.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/property-information/lib/util/create.js":
/*!**************************************************************!*\
  !*** ./node_modules/property-information/lib/util/create.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: function() { return /* binding */ create; }\n/* harmony export */ });\n/* harmony import */ var _normalize_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../normalize.js */ \"(app-pages-browser)/./node_modules/property-information/lib/normalize.js\");\n/* harmony import */ var _schema_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./schema.js */ \"(app-pages-browser)/./node_modules/property-information/lib/util/schema.js\");\n/* harmony import */ var _defined_info_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defined-info.js */ \"(app-pages-browser)/./node_modules/property-information/lib/util/defined-info.js\");\n/**\n * @typedef {import('./schema.js').Properties} Properties\n * @typedef {import('./schema.js').Normal} Normal\n *\n * @typedef {Record<string, string>} Attributes\n *\n * @typedef {Object} Definition\n * @property {Record<string, number|null>} properties\n * @property {(attributes: Attributes, property: string) => string} transform\n * @property {string} [space]\n * @property {Attributes} [attributes]\n * @property {Array<string>} [mustUseProperty]\n */\n\n\n\n\n\nconst own = {}.hasOwnProperty\n\n/**\n * @param {Definition} definition\n * @returns {Schema}\n */\nfunction create(definition) {\n  /** @type {Properties} */\n  const property = {}\n  /** @type {Normal} */\n  const normal = {}\n  /** @type {string} */\n  let prop\n\n  for (prop in definition.properties) {\n    if (own.call(definition.properties, prop)) {\n      const value = definition.properties[prop]\n      const info = new _defined_info_js__WEBPACK_IMPORTED_MODULE_0__.DefinedInfo(\n        prop,\n        definition.transform(definition.attributes || {}, prop),\n        value,\n        definition.space\n      )\n\n      if (\n        definition.mustUseProperty &&\n        definition.mustUseProperty.includes(prop)\n      ) {\n        info.mustUseProperty = true\n      }\n\n      property[prop] = info\n\n      normal[(0,_normalize_js__WEBPACK_IMPORTED_MODULE_1__.normalize)(prop)] = prop\n      normal[(0,_normalize_js__WEBPACK_IMPORTED_MODULE_1__.normalize)(info.attribute)] = prop\n    }\n  }\n\n  return new _schema_js__WEBPACK_IMPORTED_MODULE_2__.Schema(property, normal, definition.space)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/property-information/lib/util/create.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/property-information/lib/util/defined-info.js":
/*!********************************************************************!*\
  !*** ./node_modules/property-information/lib/util/defined-info.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefinedInfo: function() { return /* binding */ DefinedInfo; }\n/* harmony export */ });\n/* harmony import */ var _info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./info.js */ \"(app-pages-browser)/./node_modules/property-information/lib/util/info.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(app-pages-browser)/./node_modules/property-information/lib/util/types.js\");\n\n\n\n/** @type {Array<keyof types>} */\n// @ts-expect-error: hush.\nconst checks = Object.keys(_types_js__WEBPACK_IMPORTED_MODULE_0__)\n\nclass DefinedInfo extends _info_js__WEBPACK_IMPORTED_MODULE_1__.Info {\n  /**\n   * @constructor\n   * @param {string} property\n   * @param {string} attribute\n   * @param {number|null} [mask]\n   * @param {string} [space]\n   */\n  constructor(property, attribute, mask, space) {\n    let index = -1\n\n    super(property, attribute)\n\n    mark(this, 'space', space)\n\n    if (typeof mask === 'number') {\n      while (++index < checks.length) {\n        const check = checks[index]\n        mark(this, checks[index], (mask & _types_js__WEBPACK_IMPORTED_MODULE_0__[check]) === _types_js__WEBPACK_IMPORTED_MODULE_0__[check])\n      }\n    }\n  }\n}\n\nDefinedInfo.prototype.defined = true\n\n/**\n * @param {DefinedInfo} values\n * @param {string} key\n * @param {unknown} value\n */\nfunction mark(values, key, value) {\n  if (value) {\n    // @ts-expect-error: assume `value` matches the expected value of `key`.\n    values[key] = value\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/property-information/lib/util/defined-info.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/property-information/lib/util/info.js":
/*!************************************************************!*\
  !*** ./node_modules/property-information/lib/util/info.js ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Info: function() { return /* binding */ Info; }\n/* harmony export */ });\nclass Info {\n  /**\n   * @constructor\n   * @param {string} property\n   * @param {string} attribute\n   */\n  constructor(property, attribute) {\n    /** @type {string} */\n    this.property = property\n    /** @type {string} */\n    this.attribute = attribute\n  }\n}\n\n/** @type {string|null} */\nInfo.prototype.space = null\nInfo.prototype.boolean = false\nInfo.prototype.booleanish = false\nInfo.prototype.overloadedBoolean = false\nInfo.prototype.number = false\nInfo.prototype.commaSeparated = false\nInfo.prototype.spaceSeparated = false\nInfo.prototype.commaOrSpaceSeparated = false\nInfo.prototype.mustUseProperty = false\nInfo.prototype.defined = false\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIvdXRpbC9pbmZvLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQSxhQUFhLFFBQVE7QUFDckIsYUFBYSxRQUFRO0FBQ3JCO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkI7QUFDQSxlQUFlLFFBQVE7QUFDdkI7QUFDQTtBQUNBOztBQUVBLFdBQVcsYUFBYTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvaW5mby5qcz83NWJkIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjbGFzcyBJbmZvIHtcbiAgLyoqXG4gICAqIEBjb25zdHJ1Y3RvclxuICAgKiBAcGFyYW0ge3N0cmluZ30gcHJvcGVydHlcbiAgICogQHBhcmFtIHtzdHJpbmd9IGF0dHJpYnV0ZVxuICAgKi9cbiAgY29uc3RydWN0b3IocHJvcGVydHksIGF0dHJpYnV0ZSkge1xuICAgIC8qKiBAdHlwZSB7c3RyaW5nfSAqL1xuICAgIHRoaXMucHJvcGVydHkgPSBwcm9wZXJ0eVxuICAgIC8qKiBAdHlwZSB7c3RyaW5nfSAqL1xuICAgIHRoaXMuYXR0cmlidXRlID0gYXR0cmlidXRlXG4gIH1cbn1cblxuLyoqIEB0eXBlIHtzdHJpbmd8bnVsbH0gKi9cbkluZm8ucHJvdG90eXBlLnNwYWNlID0gbnVsbFxuSW5mby5wcm90b3R5cGUuYm9vbGVhbiA9IGZhbHNlXG5JbmZvLnByb3RvdHlwZS5ib29sZWFuaXNoID0gZmFsc2VcbkluZm8ucHJvdG90eXBlLm92ZXJsb2FkZWRCb29sZWFuID0gZmFsc2VcbkluZm8ucHJvdG90eXBlLm51bWJlciA9IGZhbHNlXG5JbmZvLnByb3RvdHlwZS5jb21tYVNlcGFyYXRlZCA9IGZhbHNlXG5JbmZvLnByb3RvdHlwZS5zcGFjZVNlcGFyYXRlZCA9IGZhbHNlXG5JbmZvLnByb3RvdHlwZS5jb21tYU9yU3BhY2VTZXBhcmF0ZWQgPSBmYWxzZVxuSW5mby5wcm90b3R5cGUubXVzdFVzZVByb3BlcnR5ID0gZmFsc2VcbkluZm8ucHJvdG90eXBlLmRlZmluZWQgPSBmYWxzZVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/property-information/lib/util/info.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/property-information/lib/util/merge.js":
/*!*************************************************************!*\
  !*** ./node_modules/property-information/lib/util/merge.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   merge: function() { return /* binding */ merge; }\n/* harmony export */ });\n/* harmony import */ var _schema_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./schema.js */ \"(app-pages-browser)/./node_modules/property-information/lib/util/schema.js\");\n/**\n * @typedef {import('./schema.js').Properties} Properties\n * @typedef {import('./schema.js').Normal} Normal\n */\n\n\n\n/**\n * @param {Schema[]} definitions\n * @param {string} [space]\n * @returns {Schema}\n */\nfunction merge(definitions, space) {\n  /** @type {Properties} */\n  const property = {}\n  /** @type {Normal} */\n  const normal = {}\n  let index = -1\n\n  while (++index < definitions.length) {\n    Object.assign(property, definitions[index].property)\n    Object.assign(normal, definitions[index].normal)\n  }\n\n  return new _schema_js__WEBPACK_IMPORTED_MODULE_0__.Schema(property, normal, space)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIvdXRpbC9tZXJnZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSxrQ0FBa0M7QUFDL0MsYUFBYSw4QkFBOEI7QUFDM0M7O0FBRWtDOztBQUVsQztBQUNBLFdBQVcsVUFBVTtBQUNyQixXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ087QUFDUCxhQUFhLFlBQVk7QUFDekI7QUFDQSxhQUFhLFFBQVE7QUFDckI7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxhQUFhLDhDQUFNO0FBQ25CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIvdXRpbC9tZXJnZS5qcz8yODk2Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi9zY2hlbWEuanMnKS5Qcm9wZXJ0aWVzfSBQcm9wZXJ0aWVzXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL3NjaGVtYS5qcycpLk5vcm1hbH0gTm9ybWFsXG4gKi9cblxuaW1wb3J0IHtTY2hlbWF9IGZyb20gJy4vc2NoZW1hLmpzJ1xuXG4vKipcbiAqIEBwYXJhbSB7U2NoZW1hW119IGRlZmluaXRpb25zXG4gKiBAcGFyYW0ge3N0cmluZ30gW3NwYWNlXVxuICogQHJldHVybnMge1NjaGVtYX1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIG1lcmdlKGRlZmluaXRpb25zLCBzcGFjZSkge1xuICAvKiogQHR5cGUge1Byb3BlcnRpZXN9ICovXG4gIGNvbnN0IHByb3BlcnR5ID0ge31cbiAgLyoqIEB0eXBlIHtOb3JtYWx9ICovXG4gIGNvbnN0IG5vcm1hbCA9IHt9XG4gIGxldCBpbmRleCA9IC0xXG5cbiAgd2hpbGUgKCsraW5kZXggPCBkZWZpbml0aW9ucy5sZW5ndGgpIHtcbiAgICBPYmplY3QuYXNzaWduKHByb3BlcnR5LCBkZWZpbml0aW9uc1tpbmRleF0ucHJvcGVydHkpXG4gICAgT2JqZWN0LmFzc2lnbihub3JtYWwsIGRlZmluaXRpb25zW2luZGV4XS5ub3JtYWwpXG4gIH1cblxuICByZXR1cm4gbmV3IFNjaGVtYShwcm9wZXJ0eSwgbm9ybWFsLCBzcGFjZSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/property-information/lib/util/merge.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/property-information/lib/util/schema.js":
/*!**************************************************************!*\
  !*** ./node_modules/property-information/lib/util/schema.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Schema: function() { return /* binding */ Schema; }\n/* harmony export */ });\n/**\n * @typedef {import('./info.js').Info} Info\n * @typedef {Record<string, Info>} Properties\n * @typedef {Record<string, string>} Normal\n */\n\nclass Schema {\n  /**\n   * @constructor\n   * @param {Properties} property\n   * @param {Normal} normal\n   * @param {string} [space]\n   */\n  constructor(property, normal, space) {\n    this.property = property\n    this.normal = normal\n    if (space) {\n      this.space = space\n    }\n  }\n}\n\n/** @type {Properties} */\nSchema.prototype.property = {}\n/** @type {Normal} */\nSchema.prototype.normal = {}\n/** @type {string|null} */\nSchema.prototype.space = null\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIvdXRpbC9zY2hlbWEuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSwwQkFBMEI7QUFDdkMsYUFBYSxzQkFBc0I7QUFDbkMsYUFBYSx3QkFBd0I7QUFDckM7O0FBRU87QUFDUDtBQUNBO0FBQ0EsYUFBYSxZQUFZO0FBQ3pCLGFBQWEsUUFBUTtBQUNyQixhQUFhLFFBQVE7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLFdBQVcsWUFBWTtBQUN2QjtBQUNBLFdBQVcsUUFBUTtBQUNuQjtBQUNBLFdBQVcsYUFBYTtBQUN4QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvc2NoZW1hLmpzPzNkNjAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2luZm8uanMnKS5JbmZvfSBJbmZvXG4gKiBAdHlwZWRlZiB7UmVjb3JkPHN0cmluZywgSW5mbz59IFByb3BlcnRpZXNcbiAqIEB0eXBlZGVmIHtSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+fSBOb3JtYWxcbiAqL1xuXG5leHBvcnQgY2xhc3MgU2NoZW1hIHtcbiAgLyoqXG4gICAqIEBjb25zdHJ1Y3RvclxuICAgKiBAcGFyYW0ge1Byb3BlcnRpZXN9IHByb3BlcnR5XG4gICAqIEBwYXJhbSB7Tm9ybWFsfSBub3JtYWxcbiAgICogQHBhcmFtIHtzdHJpbmd9IFtzcGFjZV1cbiAgICovXG4gIGNvbnN0cnVjdG9yKHByb3BlcnR5LCBub3JtYWwsIHNwYWNlKSB7XG4gICAgdGhpcy5wcm9wZXJ0eSA9IHByb3BlcnR5XG4gICAgdGhpcy5ub3JtYWwgPSBub3JtYWxcbiAgICBpZiAoc3BhY2UpIHtcbiAgICAgIHRoaXMuc3BhY2UgPSBzcGFjZVxuICAgIH1cbiAgfVxufVxuXG4vKiogQHR5cGUge1Byb3BlcnRpZXN9ICovXG5TY2hlbWEucHJvdG90eXBlLnByb3BlcnR5ID0ge31cbi8qKiBAdHlwZSB7Tm9ybWFsfSAqL1xuU2NoZW1hLnByb3RvdHlwZS5ub3JtYWwgPSB7fVxuLyoqIEB0eXBlIHtzdHJpbmd8bnVsbH0gKi9cblNjaGVtYS5wcm90b3R5cGUuc3BhY2UgPSBudWxsXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/property-information/lib/util/schema.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/property-information/lib/util/types.js":
/*!*************************************************************!*\
  !*** ./node_modules/property-information/lib/util/types.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   boolean: function() { return /* binding */ boolean; },\n/* harmony export */   booleanish: function() { return /* binding */ booleanish; },\n/* harmony export */   commaOrSpaceSeparated: function() { return /* binding */ commaOrSpaceSeparated; },\n/* harmony export */   commaSeparated: function() { return /* binding */ commaSeparated; },\n/* harmony export */   number: function() { return /* binding */ number; },\n/* harmony export */   overloadedBoolean: function() { return /* binding */ overloadedBoolean; },\n/* harmony export */   spaceSeparated: function() { return /* binding */ spaceSeparated; }\n/* harmony export */ });\nlet powers = 0\n\nconst boolean = increment()\nconst booleanish = increment()\nconst overloadedBoolean = increment()\nconst number = increment()\nconst spaceSeparated = increment()\nconst commaSeparated = increment()\nconst commaOrSpaceSeparated = increment()\n\nfunction increment() {\n  return 2 ** ++powers\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIvdXRpbC90eXBlcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQUE7O0FBRU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRVA7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIvdXRpbC90eXBlcy5qcz9mNmYxIl0sInNvdXJjZXNDb250ZW50IjpbImxldCBwb3dlcnMgPSAwXG5cbmV4cG9ydCBjb25zdCBib29sZWFuID0gaW5jcmVtZW50KClcbmV4cG9ydCBjb25zdCBib29sZWFuaXNoID0gaW5jcmVtZW50KClcbmV4cG9ydCBjb25zdCBvdmVybG9hZGVkQm9vbGVhbiA9IGluY3JlbWVudCgpXG5leHBvcnQgY29uc3QgbnVtYmVyID0gaW5jcmVtZW50KClcbmV4cG9ydCBjb25zdCBzcGFjZVNlcGFyYXRlZCA9IGluY3JlbWVudCgpXG5leHBvcnQgY29uc3QgY29tbWFTZXBhcmF0ZWQgPSBpbmNyZW1lbnQoKVxuZXhwb3J0IGNvbnN0IGNvbW1hT3JTcGFjZVNlcGFyYXRlZCA9IGluY3JlbWVudCgpXG5cbmZ1bmN0aW9uIGluY3JlbWVudCgpIHtcbiAgcmV0dXJuIDIgKiogKytwb3dlcnNcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/property-information/lib/util/types.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/property-information/lib/xlink.js":
/*!********************************************************!*\
  !*** ./node_modules/property-information/lib/xlink.js ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   xlink: function() { return /* binding */ xlink; }\n/* harmony export */ });\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(app-pages-browser)/./node_modules/property-information/lib/util/create.js\");\n\n\nconst xlink = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  space: 'xlink',\n  transform(_, prop) {\n    return 'xlink:' + prop.slice(5).toLowerCase()\n  },\n  properties: {\n    xLinkActuate: null,\n    xLinkArcRole: null,\n    xLinkHref: null,\n    xLinkRole: null,\n    xLinkShow: null,\n    xLinkTitle: null,\n    xLinkType: null\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIveGxpbmsuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBdUM7O0FBRWhDLGNBQWMsdURBQU07QUFDM0I7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi94bGluay5qcz81NTFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Y3JlYXRlfSBmcm9tICcuL3V0aWwvY3JlYXRlLmpzJ1xuXG5leHBvcnQgY29uc3QgeGxpbmsgPSBjcmVhdGUoe1xuICBzcGFjZTogJ3hsaW5rJyxcbiAgdHJhbnNmb3JtKF8sIHByb3ApIHtcbiAgICByZXR1cm4gJ3hsaW5rOicgKyBwcm9wLnNsaWNlKDUpLnRvTG93ZXJDYXNlKClcbiAgfSxcbiAgcHJvcGVydGllczoge1xuICAgIHhMaW5rQWN0dWF0ZTogbnVsbCxcbiAgICB4TGlua0FyY1JvbGU6IG51bGwsXG4gICAgeExpbmtIcmVmOiBudWxsLFxuICAgIHhMaW5rUm9sZTogbnVsbCxcbiAgICB4TGlua1Nob3c6IG51bGwsXG4gICAgeExpbmtUaXRsZTogbnVsbCxcbiAgICB4TGlua1R5cGU6IG51bGxcbiAgfVxufSlcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/property-information/lib/xlink.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/property-information/lib/xml.js":
/*!******************************************************!*\
  !*** ./node_modules/property-information/lib/xml.js ***!
  \******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   xml: function() { return /* binding */ xml; }\n/* harmony export */ });\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(app-pages-browser)/./node_modules/property-information/lib/util/create.js\");\n\n\nconst xml = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  space: 'xml',\n  transform(_, prop) {\n    return 'xml:' + prop.slice(3).toLowerCase()\n  },\n  properties: {xmlLang: null, xmlBase: null, xmlSpace: null}\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIveG1sLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVDOztBQUVoQyxZQUFZLHVEQUFNO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxlQUFlO0FBQ2YsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3htbC5qcz8wNjc1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Y3JlYXRlfSBmcm9tICcuL3V0aWwvY3JlYXRlLmpzJ1xuXG5leHBvcnQgY29uc3QgeG1sID0gY3JlYXRlKHtcbiAgc3BhY2U6ICd4bWwnLFxuICB0cmFuc2Zvcm0oXywgcHJvcCkge1xuICAgIHJldHVybiAneG1sOicgKyBwcm9wLnNsaWNlKDMpLnRvTG93ZXJDYXNlKClcbiAgfSxcbiAgcHJvcGVydGllczoge3htbExhbmc6IG51bGwsIHhtbEJhc2U6IG51bGwsIHhtbFNwYWNlOiBudWxsfVxufSlcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/property-information/lib/xml.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/property-information/lib/xmlns.js":
/*!********************************************************!*\
  !*** ./node_modules/property-information/lib/xmlns.js ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   xmlns: function() { return /* binding */ xmlns; }\n/* harmony export */ });\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(app-pages-browser)/./node_modules/property-information/lib/util/create.js\");\n/* harmony import */ var _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/case-insensitive-transform.js */ \"(app-pages-browser)/./node_modules/property-information/lib/util/case-insensitive-transform.js\");\n\n\n\nconst xmlns = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  space: 'xmlns',\n  attributes: {xmlnsxlink: 'xmlns:xlink'},\n  transform: _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__.caseInsensitiveTransform,\n  properties: {xmlns: null, xmlnsXLink: null}\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIveG1sbnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVDO0FBQ3NDOztBQUV0RSxjQUFjLHVEQUFNO0FBQzNCO0FBQ0EsZUFBZSwwQkFBMEI7QUFDekMsYUFBYSx5RkFBd0I7QUFDckMsZUFBZTtBQUNmLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi94bWxucy5qcz82OGVhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Y3JlYXRlfSBmcm9tICcuL3V0aWwvY3JlYXRlLmpzJ1xuaW1wb3J0IHtjYXNlSW5zZW5zaXRpdmVUcmFuc2Zvcm19IGZyb20gJy4vdXRpbC9jYXNlLWluc2Vuc2l0aXZlLXRyYW5zZm9ybS5qcydcblxuZXhwb3J0IGNvbnN0IHhtbG5zID0gY3JlYXRlKHtcbiAgc3BhY2U6ICd4bWxucycsXG4gIGF0dHJpYnV0ZXM6IHt4bWxuc3hsaW5rOiAneG1sbnM6eGxpbmsnfSxcbiAgdHJhbnNmb3JtOiBjYXNlSW5zZW5zaXRpdmVUcmFuc2Zvcm0sXG4gIHByb3BlcnRpZXM6IHt4bWxuczogbnVsbCwgeG1sbnNYTGluazogbnVsbH1cbn0pXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/property-information/lib/xmlns.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/rehype-stringify/index.js":
/*!************************************************!*\
  !*** ./node_modules/rehype-stringify/index.js ***!
  \************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* reexport safe */ _lib_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _lib_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/index.js */ \"(app-pages-browser)/./node_modules/rehype-stringify/lib/index.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWh5cGUtc3RyaW5naWZ5L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9yZWh5cGUtc3RyaW5naWZ5L2luZGV4LmpzPzVjZmIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHtkZWZhdWx0fSBmcm9tICcuL2xpYi9pbmRleC5qcydcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/rehype-stringify/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/rehype-stringify/lib/index.js":
/*!****************************************************!*\
  !*** ./node_modules/rehype-stringify/lib/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ rehypeStringify; }\n/* harmony export */ });\n/* harmony import */ var hast_util_to_html__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-to-html */ \"(app-pages-browser)/./node_modules/hast-util-to-html/lib/index.js\");\n/**\n * @typedef {import('hast').Root} Root\n * @typedef {Root|Root['children'][number]} Node\n * @typedef {import('hast-util-to-html').Options} Options\n */\n\n\n\n/**\n * @this {import('unified').Processor}\n * @type {import('unified').Plugin<[Options?]|Array<void>, Node, string>}\n */\nfunction rehypeStringify(config) {\n  const processorSettings = /** @type {Options} */ (this.data('settings'))\n  const settings = Object.assign({}, processorSettings, config)\n\n  Object.assign(this, {Compiler: compiler})\n\n  /**\n   * @type {import('unified').CompilerFunction<Node, string>}\n   */\n  function compiler(tree) {\n    return (0,hast_util_to_html__WEBPACK_IMPORTED_MODULE_0__.toHtml)(tree, settings)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWh5cGUtc3RyaW5naWZ5L2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSxxQkFBcUI7QUFDbEMsYUFBYSwrQkFBK0I7QUFDNUMsYUFBYSxxQ0FBcUM7QUFDbEQ7O0FBRXdDOztBQUV4QztBQUNBLFVBQVU7QUFDVixVQUFVO0FBQ1Y7QUFDZTtBQUNmLHVDQUF1QyxTQUFTO0FBQ2hELG1DQUFtQzs7QUFFbkMsdUJBQXVCLG1CQUFtQjs7QUFFMUM7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBLFdBQVcseURBQU07QUFDakI7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvcmVoeXBlLXN0cmluZ2lmeS9saWIvaW5kZXguanM/M2Y1NiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5Sb290fSBSb290XG4gKiBAdHlwZWRlZiB7Um9vdHxSb290WydjaGlsZHJlbiddW251bWJlcl19IE5vZGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QtdXRpbC10by1odG1sJykuT3B0aW9uc30gT3B0aW9uc1xuICovXG5cbmltcG9ydCB7dG9IdG1sfSBmcm9tICdoYXN0LXV0aWwtdG8taHRtbCdcblxuLyoqXG4gKiBAdGhpcyB7aW1wb3J0KCd1bmlmaWVkJykuUHJvY2Vzc29yfVxuICogQHR5cGUge2ltcG9ydCgndW5pZmllZCcpLlBsdWdpbjxbT3B0aW9ucz9dfEFycmF5PHZvaWQ+LCBOb2RlLCBzdHJpbmc+fVxuICovXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiByZWh5cGVTdHJpbmdpZnkoY29uZmlnKSB7XG4gIGNvbnN0IHByb2Nlc3NvclNldHRpbmdzID0gLyoqIEB0eXBlIHtPcHRpb25zfSAqLyAodGhpcy5kYXRhKCdzZXR0aW5ncycpKVxuICBjb25zdCBzZXR0aW5ncyA9IE9iamVjdC5hc3NpZ24oe30sIHByb2Nlc3NvclNldHRpbmdzLCBjb25maWcpXG5cbiAgT2JqZWN0LmFzc2lnbih0aGlzLCB7Q29tcGlsZXI6IGNvbXBpbGVyfSlcblxuICAvKipcbiAgICogQHR5cGUge2ltcG9ydCgndW5pZmllZCcpLkNvbXBpbGVyRnVuY3Rpb248Tm9kZSwgc3RyaW5nPn1cbiAgICovXG4gIGZ1bmN0aW9uIGNvbXBpbGVyKHRyZWUpIHtcbiAgICByZXR1cm4gdG9IdG1sKHRyZWUsIHNldHRpbmdzKVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/rehype-stringify/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/space-separated-tokens/index.js":
/*!******************************************************!*\
  !*** ./node_modules/space-separated-tokens/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: function() { return /* binding */ parse; },\n/* harmony export */   stringify: function() { return /* binding */ stringify; }\n/* harmony export */ });\n/**\n * Parse space-separated tokens to an array of strings.\n *\n * @param {string} value\n *   Space-separated tokens.\n * @returns {Array<string>}\n *   List of tokens.\n */\nfunction parse(value) {\n  const input = String(value || '').trim()\n  return input ? input.split(/[ \\t\\n\\r\\f]+/g) : []\n}\n\n/**\n * Serialize an array of strings as space separated-tokens.\n *\n * @param {Array<string|number>} values\n *   List of tokens.\n * @returns {string}\n *   Space-separated tokens.\n */\nfunction stringify(values) {\n  return values.join(' ').trim()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9zcGFjZS1zZXBhcmF0ZWQtdG9rZW5zL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLHNCQUFzQjtBQUNqQztBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9zcGFjZS1zZXBhcmF0ZWQtdG9rZW5zL2luZGV4LmpzP2ExNGQiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBQYXJzZSBzcGFjZS1zZXBhcmF0ZWQgdG9rZW5zIHRvIGFuIGFycmF5IG9mIHN0cmluZ3MuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IHZhbHVlXG4gKiAgIFNwYWNlLXNlcGFyYXRlZCB0b2tlbnMuXG4gKiBAcmV0dXJucyB7QXJyYXk8c3RyaW5nPn1cbiAqICAgTGlzdCBvZiB0b2tlbnMuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwYXJzZSh2YWx1ZSkge1xuICBjb25zdCBpbnB1dCA9IFN0cmluZyh2YWx1ZSB8fCAnJykudHJpbSgpXG4gIHJldHVybiBpbnB1dCA/IGlucHV0LnNwbGl0KC9bIFxcdFxcblxcclxcZl0rL2cpIDogW11cbn1cblxuLyoqXG4gKiBTZXJpYWxpemUgYW4gYXJyYXkgb2Ygc3RyaW5ncyBhcyBzcGFjZSBzZXBhcmF0ZWQtdG9rZW5zLlxuICpcbiAqIEBwYXJhbSB7QXJyYXk8c3RyaW5nfG51bWJlcj59IHZhbHVlc1xuICogICBMaXN0IG9mIHRva2Vucy5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIFNwYWNlLXNlcGFyYXRlZCB0b2tlbnMuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBzdHJpbmdpZnkodmFsdWVzKSB7XG4gIHJldHVybiB2YWx1ZXMuam9pbignICcpLnRyaW0oKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/space-separated-tokens/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/stringify-entities/lib/constant/dangerous.js":
/*!*******************************************************************!*\
  !*** ./node_modules/stringify-entities/lib/constant/dangerous.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dangerous: function() { return /* binding */ dangerous; }\n/* harmony export */ });\n/**\n * List of legacy (that don’t need a trailing `;`) named references which could,\n * depending on what follows them, turn into a different meaning\n *\n * @type {Array<string>}\n */\nconst dangerous = [\n  'cent',\n  'copy',\n  'divide',\n  'gt',\n  'lt',\n  'not',\n  'para',\n  'times'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9zdHJpbmdpZnktZW50aXRpZXMvbGliL2NvbnN0YW50L2Rhbmdlcm91cy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxnREFBZ0Q7QUFDaEQ7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9zdHJpbmdpZnktZW50aXRpZXMvbGliL2NvbnN0YW50L2Rhbmdlcm91cy5qcz85YzQzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogTGlzdCBvZiBsZWdhY3kgKHRoYXQgZG9u4oCZdCBuZWVkIGEgdHJhaWxpbmcgYDtgKSBuYW1lZCByZWZlcmVuY2VzIHdoaWNoIGNvdWxkLFxuICogZGVwZW5kaW5nIG9uIHdoYXQgZm9sbG93cyB0aGVtLCB0dXJuIGludG8gYSBkaWZmZXJlbnQgbWVhbmluZ1xuICpcbiAqIEB0eXBlIHtBcnJheTxzdHJpbmc+fVxuICovXG5leHBvcnQgY29uc3QgZGFuZ2Vyb3VzID0gW1xuICAnY2VudCcsXG4gICdjb3B5JyxcbiAgJ2RpdmlkZScsXG4gICdndCcsXG4gICdsdCcsXG4gICdub3QnLFxuICAncGFyYScsXG4gICd0aW1lcydcbl1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/stringify-entities/lib/constant/dangerous.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/stringify-entities/lib/core.js":
/*!*****************************************************!*\
  !*** ./node_modules/stringify-entities/lib/core.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   core: function() { return /* binding */ core; }\n/* harmony export */ });\n/**\n * @typedef CoreOptions\n * @property {ReadonlyArray<string>} [subset=[]]\n *   Whether to only escape the given subset of characters.\n * @property {boolean} [escapeOnly=false]\n *   Whether to only escape possibly dangerous characters.\n *   Those characters are `\"`, `&`, `'`, `<`, `>`, and `` ` ``.\n *\n * @typedef FormatOptions\n * @property {(code: number, next: number, options: CoreWithFormatOptions) => string} format\n *   Format strategy.\n *\n * @typedef {CoreOptions & FormatOptions & import('./util/format-smart.js').FormatSmartOptions} CoreWithFormatOptions\n */\n\nconst defaultSubsetRegex = /[\"&'<>`]/g\nconst surrogatePairsRegex = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g\nconst controlCharactersRegex =\n  // eslint-disable-next-line no-control-regex, unicorn/no-hex-escape\n  /[\\x01-\\t\\v\\f\\x0E-\\x1F\\x7F\\x81\\x8D\\x8F\\x90\\x9D\\xA0-\\uFFFF]/g\nconst regexEscapeRegex = /[|\\\\{}()[\\]^$+*?.]/g\n\n/** @type {WeakMap<ReadonlyArray<string>, RegExp>} */\nconst subsetToRegexCache = new WeakMap()\n\n/**\n * Encode certain characters in `value`.\n *\n * @param {string} value\n * @param {CoreWithFormatOptions} options\n * @returns {string}\n */\nfunction core(value, options) {\n  value = value.replace(\n    options.subset\n      ? charactersToExpressionCached(options.subset)\n      : defaultSubsetRegex,\n    basic\n  )\n\n  if (options.subset || options.escapeOnly) {\n    return value\n  }\n\n  return (\n    value\n      // Surrogate pairs.\n      .replace(surrogatePairsRegex, surrogate)\n      // BMP control characters (C0 except for LF, CR, SP; DEL; and some more\n      // non-ASCII ones).\n      .replace(controlCharactersRegex, basic)\n  )\n\n  /**\n   * @param {string} pair\n   * @param {number} index\n   * @param {string} all\n   */\n  function surrogate(pair, index, all) {\n    return options.format(\n      (pair.charCodeAt(0) - 0xd800) * 0x400 +\n        pair.charCodeAt(1) -\n        0xdc00 +\n        0x10000,\n      all.charCodeAt(index + 2),\n      options\n    )\n  }\n\n  /**\n   * @param {string} character\n   * @param {number} index\n   * @param {string} all\n   */\n  function basic(character, index, all) {\n    return options.format(\n      character.charCodeAt(0),\n      all.charCodeAt(index + 1),\n      options\n    )\n  }\n}\n\n/**\n * A wrapper function that caches the result of `charactersToExpression` with a WeakMap.\n * This can improve performance when tooling calls `charactersToExpression` repeatedly\n * with the same subset.\n *\n * @param {ReadonlyArray<string>} subset\n * @returns {RegExp}\n */\nfunction charactersToExpressionCached(subset) {\n  let cached = subsetToRegexCache.get(subset)\n\n  if (!cached) {\n    cached = charactersToExpression(subset)\n    subsetToRegexCache.set(subset, cached)\n  }\n\n  return cached\n}\n\n/**\n * @param {ReadonlyArray<string>} subset\n * @returns {RegExp}\n */\nfunction charactersToExpression(subset) {\n  /** @type {Array<string>} */\n  const groups = []\n  let index = -1\n\n  while (++index < subset.length) {\n    groups.push(subset[index].replace(regexEscapeRegex, '\\\\$&'))\n  }\n\n  return new RegExp('(?:' + groups.join('|') + ')', 'g')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/stringify-entities/lib/core.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/stringify-entities/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/stringify-entities/lib/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stringifyEntities: function() { return /* binding */ stringifyEntities; },\n/* harmony export */   stringifyEntitiesLight: function() { return /* binding */ stringifyEntitiesLight; }\n/* harmony export */ });\n/* harmony import */ var _core_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core.js */ \"(app-pages-browser)/./node_modules/stringify-entities/lib/core.js\");\n/* harmony import */ var _util_format_smart_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/format-smart.js */ \"(app-pages-browser)/./node_modules/stringify-entities/lib/util/format-smart.js\");\n/* harmony import */ var _util_format_basic_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/format-basic.js */ \"(app-pages-browser)/./node_modules/stringify-entities/lib/util/format-basic.js\");\n/**\n * @typedef {import('./core.js').CoreOptions & import('./util/format-smart.js').FormatSmartOptions} Options\n * @typedef {import('./core.js').CoreOptions} LightOptions\n */\n\n\n\n\n\n/**\n * Encode special characters in `value`.\n *\n * @param {string} value\n *   Value to encode.\n * @param {Options} [options]\n *   Configuration.\n * @returns {string}\n *   Encoded value.\n */\nfunction stringifyEntities(value, options) {\n  return (0,_core_js__WEBPACK_IMPORTED_MODULE_0__.core)(value, Object.assign({format: _util_format_smart_js__WEBPACK_IMPORTED_MODULE_1__.formatSmart}, options))\n}\n\n/**\n * Encode special characters in `value` as hexadecimals.\n *\n * @param {string} value\n *   Value to encode.\n * @param {LightOptions} [options]\n *   Configuration.\n * @returns {string}\n *   Encoded value.\n */\nfunction stringifyEntitiesLight(value, options) {\n  return (0,_core_js__WEBPACK_IMPORTED_MODULE_0__.core)(value, Object.assign({format: _util_format_basic_js__WEBPACK_IMPORTED_MODULE_2__.formatBasic}, options))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9zdHJpbmdpZnktZW50aXRpZXMvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDQSxhQUFhLHVGQUF1RjtBQUNwRyxhQUFhLGlDQUFpQztBQUM5Qzs7QUFFOEI7QUFDb0I7QUFDQTs7QUFFbEQ7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQLFNBQVMsOENBQUksdUJBQXVCLFFBQVEsOERBQVcsQ0FBQztBQUN4RDs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQSxXQUFXLGNBQWM7QUFDekI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1AsU0FBUyw4Q0FBSSx1QkFBdUIsUUFBUSw4REFBVyxDQUFDO0FBQ3hEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9zdHJpbmdpZnktZW50aXRpZXMvbGliL2luZGV4LmpzP2VjM2EiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2NvcmUuanMnKS5Db3JlT3B0aW9ucyAmIGltcG9ydCgnLi91dGlsL2Zvcm1hdC1zbWFydC5qcycpLkZvcm1hdFNtYXJ0T3B0aW9uc30gT3B0aW9uc1xuICogQHR5cGVkZWYge2ltcG9ydCgnLi9jb3JlLmpzJykuQ29yZU9wdGlvbnN9IExpZ2h0T3B0aW9uc1xuICovXG5cbmltcG9ydCB7Y29yZX0gZnJvbSAnLi9jb3JlLmpzJ1xuaW1wb3J0IHtmb3JtYXRTbWFydH0gZnJvbSAnLi91dGlsL2Zvcm1hdC1zbWFydC5qcydcbmltcG9ydCB7Zm9ybWF0QmFzaWN9IGZyb20gJy4vdXRpbC9mb3JtYXQtYmFzaWMuanMnXG5cbi8qKlxuICogRW5jb2RlIHNwZWNpYWwgY2hhcmFjdGVycyBpbiBgdmFsdWVgLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSB2YWx1ZVxuICogICBWYWx1ZSB0byBlbmNvZGUuXG4gKiBAcGFyYW0ge09wdGlvbnN9IFtvcHRpb25zXVxuICogICBDb25maWd1cmF0aW9uLlxuICogQHJldHVybnMge3N0cmluZ31cbiAqICAgRW5jb2RlZCB2YWx1ZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHN0cmluZ2lmeUVudGl0aWVzKHZhbHVlLCBvcHRpb25zKSB7XG4gIHJldHVybiBjb3JlKHZhbHVlLCBPYmplY3QuYXNzaWduKHtmb3JtYXQ6IGZvcm1hdFNtYXJ0fSwgb3B0aW9ucykpXG59XG5cbi8qKlxuICogRW5jb2RlIHNwZWNpYWwgY2hhcmFjdGVycyBpbiBgdmFsdWVgIGFzIGhleGFkZWNpbWFscy5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gdmFsdWVcbiAqICAgVmFsdWUgdG8gZW5jb2RlLlxuICogQHBhcmFtIHtMaWdodE9wdGlvbnN9IFtvcHRpb25zXVxuICogICBDb25maWd1cmF0aW9uLlxuICogQHJldHVybnMge3N0cmluZ31cbiAqICAgRW5jb2RlZCB2YWx1ZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHN0cmluZ2lmeUVudGl0aWVzTGlnaHQodmFsdWUsIG9wdGlvbnMpIHtcbiAgcmV0dXJuIGNvcmUodmFsdWUsIE9iamVjdC5hc3NpZ24oe2Zvcm1hdDogZm9ybWF0QmFzaWN9LCBvcHRpb25zKSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/stringify-entities/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/stringify-entities/lib/util/format-basic.js":
/*!******************************************************************!*\
  !*** ./node_modules/stringify-entities/lib/util/format-basic.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatBasic: function() { return /* binding */ formatBasic; }\n/* harmony export */ });\n/**\n * The smallest way to encode a character.\n *\n * @param {number} code\n * @returns {string}\n */\nfunction formatBasic(code) {\n  return '&#x' + code.toString(16).toUpperCase() + ';'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9zdHJpbmdpZnktZW50aXRpZXMvbGliL3V0aWwvZm9ybWF0LWJhc2ljLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ087QUFDUCxxREFBcUQ7QUFDckQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL3N0cmluZ2lmeS1lbnRpdGllcy9saWIvdXRpbC9mb3JtYXQtYmFzaWMuanM/ODNhNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFRoZSBzbWFsbGVzdCB3YXkgdG8gZW5jb2RlIGEgY2hhcmFjdGVyLlxuICpcbiAqIEBwYXJhbSB7bnVtYmVyfSBjb2RlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0QmFzaWMoY29kZSkge1xuICByZXR1cm4gJyYjeCcgKyBjb2RlLnRvU3RyaW5nKDE2KS50b1VwcGVyQ2FzZSgpICsgJzsnXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/stringify-entities/lib/util/format-basic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/stringify-entities/lib/util/format-smart.js":
/*!******************************************************************!*\
  !*** ./node_modules/stringify-entities/lib/util/format-smart.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatSmart: function() { return /* binding */ formatSmart; }\n/* harmony export */ });\n/* harmony import */ var _to_hexadecimal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./to-hexadecimal.js */ \"(app-pages-browser)/./node_modules/stringify-entities/lib/util/to-hexadecimal.js\");\n/* harmony import */ var _to_decimal_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./to-decimal.js */ \"(app-pages-browser)/./node_modules/stringify-entities/lib/util/to-decimal.js\");\n/* harmony import */ var _to_named_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./to-named.js */ \"(app-pages-browser)/./node_modules/stringify-entities/lib/util/to-named.js\");\n/**\n * @typedef FormatSmartOptions\n * @property {boolean} [useNamedReferences=false]\n *   Prefer named character references (`&amp;`) where possible.\n * @property {boolean} [useShortestReferences=false]\n *   Prefer the shortest possible reference, if that results in less bytes.\n *   **Note**: `useNamedReferences` can be omitted when using `useShortestReferences`.\n * @property {boolean} [omitOptionalSemicolons=false]\n *   Whether to omit semicolons when possible.\n *   **Note**: This creates what HTML calls “parse errors” but is otherwise still valid HTML — don’t use this except when building a minifier.\n *   Omitting semicolons is possible for certain named and numeric references in some cases.\n * @property {boolean} [attribute=false]\n *   Create character references which don’t fail in attributes.\n *   **Note**: `attribute` only applies when operating dangerously with\n *   `omitOptionalSemicolons: true`.\n */\n\n\n\n\n\n/**\n * Configurable ways to encode a character yielding pretty or small results.\n *\n * @param {number} code\n * @param {number} next\n * @param {FormatSmartOptions} options\n * @returns {string}\n */\nfunction formatSmart(code, next, options) {\n  let numeric = (0,_to_hexadecimal_js__WEBPACK_IMPORTED_MODULE_0__.toHexadecimal)(code, next, options.omitOptionalSemicolons)\n  /** @type {string|undefined} */\n  let named\n\n  if (options.useNamedReferences || options.useShortestReferences) {\n    named = (0,_to_named_js__WEBPACK_IMPORTED_MODULE_1__.toNamed)(\n      code,\n      next,\n      options.omitOptionalSemicolons,\n      options.attribute\n    )\n  }\n\n  // Use the shortest numeric reference when requested.\n  // A simple algorithm would use decimal for all code points under 100, as\n  // those are shorter than hexadecimal:\n  //\n  // * `&#99;` vs `&#x63;` (decimal shorter)\n  // * `&#100;` vs `&#x64;` (equal)\n  //\n  // However, because we take `next` into consideration when `omit` is used,\n  // And it would be possible that decimals are shorter on bigger values as\n  // well if `next` is hexadecimal but not decimal, we instead compare both.\n  if (\n    (options.useShortestReferences || !named) &&\n    options.useShortestReferences\n  ) {\n    const decimal = (0,_to_decimal_js__WEBPACK_IMPORTED_MODULE_2__.toDecimal)(code, next, options.omitOptionalSemicolons)\n\n    if (decimal.length < numeric.length) {\n      numeric = decimal\n    }\n  }\n\n  return named &&\n    (!options.useShortestReferences || named.length < numeric.length)\n    ? named\n    : numeric\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/stringify-entities/lib/util/format-smart.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/stringify-entities/lib/util/to-decimal.js":
/*!****************************************************************!*\
  !*** ./node_modules/stringify-entities/lib/util/to-decimal.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toDecimal: function() { return /* binding */ toDecimal; }\n/* harmony export */ });\nconst decimalRegex = /\\d/\n\n/**\n * Configurable ways to encode characters as decimal references.\n *\n * @param {number} code\n * @param {number} next\n * @param {boolean|undefined} omit\n * @returns {string}\n */\nfunction toDecimal(code, next, omit) {\n  const value = '&#' + String(code)\n  return omit && next && !decimalRegex.test(String.fromCharCode(next))\n    ? value\n    : value + ';'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9zdHJpbmdpZnktZW50aXRpZXMvbGliL3V0aWwvdG8tZGVjaW1hbC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQixXQUFXLG1CQUFtQjtBQUM5QixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvc3RyaW5naWZ5LWVudGl0aWVzL2xpYi91dGlsL3RvLWRlY2ltYWwuanM/ZmUzYSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBkZWNpbWFsUmVnZXggPSAvXFxkL1xuXG4vKipcbiAqIENvbmZpZ3VyYWJsZSB3YXlzIHRvIGVuY29kZSBjaGFyYWN0ZXJzIGFzIGRlY2ltYWwgcmVmZXJlbmNlcy5cbiAqXG4gKiBAcGFyYW0ge251bWJlcn0gY29kZVxuICogQHBhcmFtIHtudW1iZXJ9IG5leHRcbiAqIEBwYXJhbSB7Ym9vbGVhbnx1bmRlZmluZWR9IG9taXRcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0b0RlY2ltYWwoY29kZSwgbmV4dCwgb21pdCkge1xuICBjb25zdCB2YWx1ZSA9ICcmIycgKyBTdHJpbmcoY29kZSlcbiAgcmV0dXJuIG9taXQgJiYgbmV4dCAmJiAhZGVjaW1hbFJlZ2V4LnRlc3QoU3RyaW5nLmZyb21DaGFyQ29kZShuZXh0KSlcbiAgICA/IHZhbHVlXG4gICAgOiB2YWx1ZSArICc7J1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/stringify-entities/lib/util/to-decimal.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/stringify-entities/lib/util/to-hexadecimal.js":
/*!********************************************************************!*\
  !*** ./node_modules/stringify-entities/lib/util/to-hexadecimal.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toHexadecimal: function() { return /* binding */ toHexadecimal; }\n/* harmony export */ });\nconst hexadecimalRegex = /[\\dA-Fa-f]/\n\n/**\n * Configurable ways to encode characters as hexadecimal references.\n *\n * @param {number} code\n * @param {number} next\n * @param {boolean|undefined} omit\n * @returns {string}\n */\nfunction toHexadecimal(code, next, omit) {\n  const value = '&#x' + code.toString(16).toUpperCase()\n  return omit && next && !hexadecimalRegex.test(String.fromCharCode(next))\n    ? value\n    : value + ';'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9zdHJpbmdpZnktZW50aXRpZXMvbGliL3V0aWwvdG8taGV4YWRlY2ltYWwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLFFBQVE7QUFDbkIsV0FBVyxtQkFBbUI7QUFDOUIsYUFBYTtBQUNiO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL3N0cmluZ2lmeS1lbnRpdGllcy9saWIvdXRpbC90by1oZXhhZGVjaW1hbC5qcz80ZWRhIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGhleGFkZWNpbWFsUmVnZXggPSAvW1xcZEEtRmEtZl0vXG5cbi8qKlxuICogQ29uZmlndXJhYmxlIHdheXMgdG8gZW5jb2RlIGNoYXJhY3RlcnMgYXMgaGV4YWRlY2ltYWwgcmVmZXJlbmNlcy5cbiAqXG4gKiBAcGFyYW0ge251bWJlcn0gY29kZVxuICogQHBhcmFtIHtudW1iZXJ9IG5leHRcbiAqIEBwYXJhbSB7Ym9vbGVhbnx1bmRlZmluZWR9IG9taXRcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0b0hleGFkZWNpbWFsKGNvZGUsIG5leHQsIG9taXQpIHtcbiAgY29uc3QgdmFsdWUgPSAnJiN4JyArIGNvZGUudG9TdHJpbmcoMTYpLnRvVXBwZXJDYXNlKClcbiAgcmV0dXJuIG9taXQgJiYgbmV4dCAmJiAhaGV4YWRlY2ltYWxSZWdleC50ZXN0KFN0cmluZy5mcm9tQ2hhckNvZGUobmV4dCkpXG4gICAgPyB2YWx1ZVxuICAgIDogdmFsdWUgKyAnOydcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/stringify-entities/lib/util/to-hexadecimal.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/stringify-entities/lib/util/to-named.js":
/*!**************************************************************!*\
  !*** ./node_modules/stringify-entities/lib/util/to-named.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toNamed: function() { return /* binding */ toNamed; }\n/* harmony export */ });\n/* harmony import */ var character_entities_legacy__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! character-entities-legacy */ \"(app-pages-browser)/./node_modules/character-entities-legacy/index.js\");\n/* harmony import */ var character_entities_html4__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! character-entities-html4 */ \"(app-pages-browser)/./node_modules/character-entities-html4/index.js\");\n/* harmony import */ var _constant_dangerous_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../constant/dangerous.js */ \"(app-pages-browser)/./node_modules/stringify-entities/lib/constant/dangerous.js\");\n\n\n\n\nconst own = {}.hasOwnProperty\n\n/**\n * `characterEntitiesHtml4` but inverted.\n *\n * @type {Record<string, string>}\n */\nconst characters = {}\n\n/** @type {string} */\nlet key\n\nfor (key in character_entities_html4__WEBPACK_IMPORTED_MODULE_0__.characterEntitiesHtml4) {\n  if (own.call(character_entities_html4__WEBPACK_IMPORTED_MODULE_0__.characterEntitiesHtml4, key)) {\n    characters[character_entities_html4__WEBPACK_IMPORTED_MODULE_0__.characterEntitiesHtml4[key]] = key\n  }\n}\n\nconst notAlphanumericRegex = /[^\\dA-Za-z]/\n\n/**\n * Configurable ways to encode characters as named references.\n *\n * @param {number} code\n * @param {number} next\n * @param {boolean|undefined} omit\n * @param {boolean|undefined} attribute\n * @returns {string}\n */\nfunction toNamed(code, next, omit, attribute) {\n  const character = String.fromCharCode(code)\n\n  if (own.call(characters, character)) {\n    const name = characters[character]\n    const value = '&' + name\n\n    if (\n      omit &&\n      character_entities_legacy__WEBPACK_IMPORTED_MODULE_1__.characterEntitiesLegacy.includes(name) &&\n      !_constant_dangerous_js__WEBPACK_IMPORTED_MODULE_2__.dangerous.includes(name) &&\n      (!attribute ||\n        (next &&\n          next !== 61 /* `=` */ &&\n          notAlphanumericRegex.test(String.fromCharCode(next))))\n    ) {\n      return value\n    }\n\n    return value + ';'\n  }\n\n  return ''\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/stringify-entities/lib/util/to-named.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/zwitch/index.js":
/*!**************************************!*\
  !*** ./node_modules/zwitch/index.js ***!
  \**************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   zwitch: function() { return /* binding */ zwitch; }\n/* harmony export */ });\n/**\n * @callback Handler\n *   Handle a value, with a certain ID field set to a certain value.\n *   The ID field is passed to `zwitch`, and it’s value is this function’s\n *   place on the `handlers` record.\n * @param {...any} parameters\n *   Arbitrary parameters passed to the zwitch.\n *   The first will be an object with a certain ID field set to a certain value.\n * @returns {any}\n *   Anything!\n */\n\n/**\n * @callback UnknownHandler\n *   Handle values that do have a certain ID field, but it’s set to a value\n *   that is not listed in the `handlers` record.\n * @param {unknown} value\n *   An object with a certain ID field set to an unknown value.\n * @param {...any} rest\n *   Arbitrary parameters passed to the zwitch.\n * @returns {any}\n *   Anything!\n */\n\n/**\n * @callback InvalidHandler\n *   Handle values that do not have a certain ID field.\n * @param {unknown} value\n *   Any unknown value.\n * @param {...any} rest\n *   Arbitrary parameters passed to the zwitch.\n * @returns {void|null|undefined|never}\n *   This should crash or return nothing.\n */\n\n/**\n * @template {InvalidHandler} [Invalid=InvalidHandler]\n * @template {UnknownHandler} [Unknown=UnknownHandler]\n * @template {Record<string, Handler>} [Handlers=Record<string, Handler>]\n * @typedef Options\n *   Configuration (required).\n * @property {Invalid} [invalid]\n *   Handler to use for invalid values.\n * @property {Unknown} [unknown]\n *   Handler to use for unknown values.\n * @property {Handlers} [handlers]\n *   Handlers to use.\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * Handle values based on a field.\n *\n * @template {InvalidHandler} [Invalid=InvalidHandler]\n * @template {UnknownHandler} [Unknown=UnknownHandler]\n * @template {Record<string, Handler>} [Handlers=Record<string, Handler>]\n * @param {string} key\n *   Field to switch on.\n * @param {Options<Invalid, Unknown, Handlers>} [options]\n *   Configuration (required).\n * @returns {{unknown: Unknown, invalid: Invalid, handlers: Handlers, (...parameters: Parameters<Handlers[keyof Handlers]>): ReturnType<Handlers[keyof Handlers]>, (...parameters: Parameters<Unknown>): ReturnType<Unknown>}}\n */\nfunction zwitch(key, options) {\n  const settings = options || {}\n\n  /**\n   * Handle one value.\n   *\n   * Based on the bound `key`, a respective handler will be called.\n   * If `value` is not an object, or doesn’t have a `key` property, the special\n   * “invalid” handler will be called.\n   * If `value` has an unknown `key`, the special “unknown” handler will be\n   * called.\n   *\n   * All arguments, and the context object, are passed through to the handler,\n   * and it’s result is returned.\n   *\n   * @this {unknown}\n   *   Any context object.\n   * @param {unknown} [value]\n   *   Any value.\n   * @param {...unknown} parameters\n   *   Arbitrary parameters passed to the zwitch.\n   * @property {Handler} invalid\n   *   Handle for values that do not have a certain ID field.\n   * @property {Handler} unknown\n   *   Handle values that do have a certain ID field, but it’s set to a value\n   *   that is not listed in the `handlers` record.\n   * @property {Handlers} handlers\n   *   Record of handlers.\n   * @returns {unknown}\n   *   Anything.\n   */\n  function one(value, ...parameters) {\n    /** @type {Handler|undefined} */\n    let fn = one.invalid\n    const handlers = one.handlers\n\n    if (value && own.call(value, key)) {\n      // @ts-expect-error Indexable.\n      const id = String(value[key])\n      // @ts-expect-error Indexable.\n      fn = own.call(handlers, id) ? handlers[id] : one.unknown\n    }\n\n    if (fn) {\n      return fn.call(this, value, ...parameters)\n    }\n  }\n\n  one.handlers = settings.handlers || {}\n  one.invalid = settings.invalid\n  one.unknown = settings.unknown\n\n  // @ts-expect-error: matches!\n  return one\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/zwitch/index.js\n"));

/***/ })

}]);