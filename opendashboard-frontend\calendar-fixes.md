# Calendar Drag Overlay Fixes

Based on my analysis of the code, I've identified three main issues with the calendar drag overlay:

## 1. Drag overlay size mismatch

**Problem:** When dragging, the overlay event doesn't match the original event's size.

**Solution:** 
- Ensure the drag overlay uses the exact same dimensions as the original event
- Pass the correct view type to the overlay component
- Add position and transform styles to prevent unwanted transformations

```tsx
<DragOverlay dropAnimation={null}>
  {activeDragData && activeDragData.type === 'segment' ? (
    <CalendarEventSegment
      segment={activeDragData.payload as EventSegment}
      view={viewType === 'day' ? 'day' : 'week'}
      onClick={() => {}}
      style={{
        width: activeDragData.width,
        height: activeDragData.height,
        position: 'relative',
        transform: 'none'
      }}
    />
  ) : activeDragData && activeDragData.type === 'event' ? (
    <CalendarEventItem
      event={activeDragData.payload as CalendarEvent}
      view={viewType} // Use the current view type instead of hardcoding 'month'
      onClick={() => {}}
      style={{
        width: activeDragData.width,
        height: activeDragData.height,
        position: 'relative',
        transform: 'none'
      }}
    />
  ) : null}
</DragOverlay>
```

## 2. Multiple events in cell sizing issue

**Problem:** When there are multiple events in a cell, they become smaller, but the drag overlay shows the event at full size.

**Solution:**
- Modify the `onDragStart` function to capture the exact dimensions of the event being dragged
- Store the original event's size in the activeDragData state

```tsx
const onDragStart = (event: { active: Active }) => {
  if (event.active.data.current) {
    const initialPointerY = pointerCoordinates.current.y;
    const initialEventTop = event.active.rect.current?.translated?.top ?? 0;
    const grabOffsetY = initialPointerY - initialEventTop;
    
    // Get the exact dimensions from the DOM element
    const draggedElement = document.getElementById(`event-${event.active.data.current.payload.id}`);
    const width = draggedElement ? draggedElement.offsetWidth : event.active.rect.current.translated?.width;
    const height = draggedElement ? draggedElement.offsetHeight : event.active.rect.current.translated?.height;
    
    setActiveDragData({
      ...event.active.data.current,
      grabOffsetY,
      width,
      height,
    });

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('touchmove', handleTouchMove);
  }
};
```

## 3. Unintended event movement

**Problem:** Dragging sometimes moves other events' times instead of just the dragged event.

**Solution:**
- Improve the drag end logic to ensure only the dragged event is updated
- Add better validation to prevent unintended updates
- Fix the minute calculation logic to be more precise

```tsx
const onDragEnd = async ({ active, over }: { active: Active, over: Over | null }) => {
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('touchmove', handleTouchMove);
  setActiveDragData(null);

  if (!over || !active || !canEditData || active.id === over.id) {
    return;
  }

  const activeData = active.data.current;
  const overData = over.data.current;

  if (!activeData || !overData) {
    return;
  }
  
  const { payload, type } = activeData;
  const eventToUpdate: CalendarEvent = type === 'segment' ? payload.originalEvent : payload;
  
  // Ensure we're updating the correct event
  if (!eventToUpdate || !eventToUpdate.id) {
    return;
  }

  const originalStart = new Date(eventToUpdate.start);
  const originalEnd = new Date(eventToUpdate.end);
  const duration = differenceInMilliseconds(originalEnd, originalStart);
  
  let newStart: Date;

  if (overData.type.startsWith('allday')) {
    const dayDifference = differenceInDays(overData.date, startOfDay(originalStart));
    newStart = addDays(originalStart, dayDifference);
    // For all-day drops, normalize the time to the start of the day
    newStart.setHours(0, 0, 0, 0);
  } else if (overData.type.startsWith('timeslot')) {
    newStart = new Date(overData.date);
    
    // Enhanced Minute-level precision calculation
    const grabOffsetY = activeData.grabOffsetY || 0;
    const slotTop = over.rect.top;
    const pointerY = pointerCoordinates.current.y;
    
    // Adjust pointer position based on initial grab point
    const adjustedPointerY = pointerY - grabOffsetY;
    
    const offsetY = adjustedPointerY - slotTop;
    const slotHeight = over.rect.height; // Should be 60px
    const minutesFromTop = (offsetY / slotHeight) * 60;

    // Snap to nearest 5-minute interval
    const snappedMinutes = Math.round(minutesFromTop / 5) * 5;
    const newMinutes = Math.max(0, Math.min(59, snappedMinutes));

    newStart.setHours(overData.hour, newMinutes, originalStart.getSeconds(), originalStart.getMilliseconds());
  } else if (overData.type === 'daycell') {
    const dayDifference = differenceInDays(overData.date, startOfDay(originalStart));
    newStart = addDays(originalStart, dayDifference);
  } else {
    return; // Invalid drop target
  }

  const newEnd = new Date(newStart.getTime() + duration);
  
  const recordId = eventToUpdate.record.id;
  const newValues = {
    [definition.eventStartColumnId]: newStart.toISOString(),
    ...(definition.eventEndColumnId && { [definition.eventEndColumnId]: newEnd.toISOString() }),
  };

  try {
    await updateRecordValues(definition.databaseId, [recordId], newValues);
    toast.success(`Event "${eventToUpdate.title}" updated.`);
  } catch (error) {
    toast.error("Failed to update event.");
    console.error("Error updating event:", error);
  }
};
```

## Additional Improvements for CalendarEventItem.tsx

To ensure the drag overlay matches the original event's appearance:

```tsx
// Add unique ID to each event for easier selection during drag
<div
  id={`event-${event.id}`}
  ref={(node) => {
    setNodeRef(node);
    (dragRef as React.MutableRefObject<HTMLDivElement | null>).current = node;
  }}
  style={eventStyles}
  className={eventClasses.baseClasses}
  onClick={onClick}
  onContextMenu={onContextMenu}
  {...(isDraggable ? { ...listeners, ...attributes } : {})}
>
  {renderEventContent()}
</div>
```

## Implementation Steps

1. Update the DragOverlay component in index.tsx to use the correct view type and styling
2. Enhance the onDragStart function to capture exact dimensions
3. Fix the onDragEnd function to ensure only the dragged event is updated
4. Add unique IDs to CalendarEventItem and CalendarEventSegment components
5. Test the changes with different calendar views and event configurations

These changes will ensure that:
- The drag overlay matches the original event's size
- Multiple events in a cell are handled correctly
- Only the dragged event is updated when dropped