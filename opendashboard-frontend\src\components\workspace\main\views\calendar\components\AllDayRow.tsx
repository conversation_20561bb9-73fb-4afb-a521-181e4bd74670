import React, { useMemo } from 'react';
import { format, addDays, startOfWeek, endOfWeek, isSameDay } from 'date-fns';
import { cn } from '@/lib/utils';
import { CalendarEvent } from '@/typings/page';
import { EventSegment } from '@/utils/multiDayEventUtils';
import { CalendarEventSegment } from './CalendarEventSegment';
import { calculateAllDayLayout } from '@/utils/eventCollisionUtils';
import { useDroppable } from '@dnd-kit/core';

interface AllDayRowProps {
  selectedDate: Date;
  segments: EventSegment[];
  selectedEvent: string | null;
  setSelectedEvent: (id: string) => void;
  handleEventClick: (event: CalendarEvent) => void;
  canEditData: boolean;
  openAddEventForm: (date: Date) => void;
  view?: 'day' | 'week';
  activeDragData?: any;
}

interface SpanningEvent {
  segment: EventSegment;
  startDayIndex: number;
  endDayIndex: number;
  colSpan: number;
  isEndOfEvent: boolean;
}

export const AllDayRow: React.FC<AllDayRowProps> = ({
  selectedDate,
  segments,
  selectedEvent,
  setSelectedEvent,
  handleEventClick,
  canEditData,
  openAddEventForm,
  view,
  activeDragData
}) => {
  // Create individual droppable hooks for each possible day
  // IMPORTANT: All hooks must be called before any conditional returns
  const dayViewHook = useDroppable({
    id: `allday-${format(selectedDate, 'yyyy-MM-dd')}`,
    data: {
      date: selectedDate,
      type: 'allday-day'
    }
  });

  // Create hooks for week view days
  const weekDay1 = useDroppable({
    id: `allday-${format(addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 0), 'yyyy-MM-dd')}`,
    data: {
      date: addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 0),
      type: 'allday-week'
    }
  });

  const weekDay2 = useDroppable({
    id: `allday-${format(addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 1), 'yyyy-MM-dd')}`,
    data: {
      date: addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 1),
      type: 'allday-week'
    }
  });

  const weekDay3 = useDroppable({
    id: `allday-${format(addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 2), 'yyyy-MM-dd')}`,
    data: {
      date: addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 2),
      type: 'allday-week'
    }
  });

  const weekDay4 = useDroppable({
    id: `allday-${format(addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 3), 'yyyy-MM-dd')}`,
    data: {
      date: addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 3),
      type: 'allday-week'
    }
  });

  const weekDay5 = useDroppable({
    id: `allday-${format(addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 4), 'yyyy-MM-dd')}`,
    data: {
      date: addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 4),
      type: 'allday-week'
    }
  });

  const weekDay6 = useDroppable({
    id: `allday-${format(addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 5), 'yyyy-MM-dd')}`,
    data: {
      date: addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 5),
      type: 'allday-week'
    }
  });

  const weekDay7 = useDroppable({
    id: `allday-${format(addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 6), 'yyyy-MM-dd')}`,
    data: {
      date: addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 6),
      type: 'allday-week'
    }
  });

  const weekViewHooks = [weekDay1, weekDay2, weekDay3, weekDay4, weekDay5, weekDay6, weekDay7];

  
  if (segments.length === 0 && !activeDragData) { 
    return null;
  }


  const renderDayView = () => (
    <div className="border-b border-neutral-300 bg-white">
      <div className="flex">
        <div className="sticky left-0 bg-white z-10 w-14 lg:w-20 border-r border-neutral-300 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-neutral-500">
          All-day
        </div>
        <div 
          ref={dayViewHook.setNodeRef}
          className={cn(
            "flex-1 relative p-2 space-y-1",
            dayViewHook.isOver && "bg-blue-50"
          )}
        >
          {segments.slice(0, 3).map((segment) => (
            <CalendarEventSegment
              key={segment.id}
              segment={segment}
              style={{ height: '24px', width: '100%' }}
              onClick={(e) => {
                e.stopPropagation();
                setSelectedEvent(segment.originalEventId);
                handleEventClick(segment.originalEvent);
              }}
              view="day"
              isDragging={activeDragData?.payload?.id === segment.id}
            />
          ))}
          {segments.length > 3 && (
            <div className="text-xs text-neutral-600 font-medium cursor-pointer hover:text-neutral-800">
              + {segments.length - 3} more
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderWeekView = () => {
    // Week view specific logic starts here
    const weekDays = Array.from({ length: 7 }, (_, i) => addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), i));

    const spanningEvents = (() => {
      const eventGroups = new Map<string, EventSegment[]>();
      segments.forEach(segment => {
        if (segment.isMultiDay || segment.isAllDay) {
          const eventId = segment.originalEventId;
          if (!eventGroups.has(eventId)) eventGroups.set(eventId, []);
          eventGroups.get(eventId)!.push(segment);
        }
      });

      const spanning: SpanningEvent[] = [];
      eventGroups.forEach((eventSegments) => {
        eventSegments.sort((a, b) => a.date.getTime() - b.date.getTime());
        const firstSegmentInWeek = eventSegments[0];
        const lastSegmentInWeek = eventSegments[eventSegments.length - 1];
        const startDayIndex = weekDays.findIndex(day => isSameDay(day, firstSegmentInWeek.date));
        const endDayIndex = weekDays.findIndex(day => isSameDay(day, lastSegmentInWeek.date));

        if (startDayIndex >= 0 && endDayIndex >= 0) {
          spanning.push({
            segment: firstSegmentInWeek,
            startDayIndex,
            endDayIndex,
            colSpan: endDayIndex - startDayIndex + 1,
            isEndOfEvent: lastSegmentInWeek.isLastSegment,
          });
        }
      });
      return spanning;
    })();

    const positionedEvents = (() => {
      const positioned: Array<SpanningEvent & { row: number }> = [];
      const rows: Array<SpanningEvent[]> = [];
      const sortedEvents = [...spanningEvents].sort((a, b) => a.startDayIndex - b.startDayIndex || b.colSpan - a.colSpan);

      sortedEvents.forEach(event => {
        let assigned = false;
        for (let i = 0; i < rows.length; i++) {
          const row = rows[i];
          if (row.every(rowEvent => event.startDayIndex > rowEvent.endDayIndex || event.endDayIndex < rowEvent.startDayIndex)) {
            row.push(event);
            positioned.push({ ...event, row: i });
            assigned = true;
            break;
          }
        }
        if (!assigned) {
          rows.push([event]);
          positioned.push({ ...event, row: rows.length - 1 });
        }
      });
      return positioned;
    })();

    // Use the new layout calculator
    const { visibleSegments, moreCount } = calculateAllDayLayout(
      positionedEvents.map(e => e.segment), 3
    );

    const visibleEvents = positionedEvents.filter(p => visibleSegments.some(s => s.id === p.segment.id));
    const hasMore = moreCount > 0;

    const firstEventDayIndex = positionedEvents.length > 0 ? Math.min(...positionedEvents.map(e => e.startDayIndex)) : 0;
    
    if (positionedEvents.length === 0) return null;

    const maxRows = positionedEvents.length > 0 ? Math.max(...positionedEvents.map(e => e.row)) + 1 : 0;
    const rowHeight = 28;
    const displayRows = hasMore ? 3.5 : Math.max(1, maxRows); 
    const totalHeight = displayRows * rowHeight + 16;

      return (
    <div 
      data-all-day-row="true"
      className="border-b border-neutral-300 bg-white"
    >
        <div className="flex">
          <div className="sticky left-0 bg-white z-10 w-14 lg:w-20 border-r border-neutral-300 flex items-center justify-end pr-4 pt-2 text-xs font-medium text-neutral-500">
            All-day
          </div>
          <div className="flex-1 relative p-2" style={{ height: `${totalHeight}px` }}>
            <div className="grid grid-cols-7 gap-1 h-full">
              {weekDays.map((day, dayIndex) => {
                const hook = weekViewHooks[dayIndex];

                return (
                <div 
                  ref={hook.setNodeRef}
                  key={dayIndex} 
                  className={cn(
                    "relative cursor-pointer hover:bg-neutral-50 rounded-sm transition-colors",
                    hook.isOver && "bg-blue-50"
                  )}
                  onDoubleClick={() => {
                    if (canEditData) {
                      const newDate = new Date(day);
                      newDate.setHours(9, 0, 0, 0);
                      openAddEventForm(newDate);
                    }
                  }}
                >
                  {visibleEvents
                    .filter(spanningEvent => spanningEvent.startDayIndex === dayIndex)
                    .map((spanningEvent) => (
                      <div
                        key={spanningEvent.segment.id}
                        className="absolute z-10"
                        style={{
                          top: `${spanningEvent.row * rowHeight + 2}px`,
                          left: '0px',
                          width: `calc(${spanningEvent.colSpan * 100}% + ${(spanningEvent.colSpan - 1) * 4}px)`,
                          height: '24px'
                        }}
                      >
                        <CalendarEventSegment
                          segment={spanningEvent.segment}
                          isEndOfEvent={spanningEvent.isEndOfEvent}
                          style={{ height: '24px', width: '100%' }}
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedEvent(spanningEvent.segment.originalEventId);
                            handleEventClick(spanningEvent.segment.originalEvent);
                          }}
                          view={view}
                          isDragging={activeDragData?.payload?.id === spanningEvent.segment.id}
                        />
                      </div>
                    ))}
                  {hasMore && dayIndex === firstEventDayIndex && (
                    <div
                      className="absolute z-10 cursor-pointer text-xs text-gray-600 hover:underline"
                      style={{
                        top: `${3 * rowHeight + 2}px`,
                        left: '4px',
                        right: '4px',
                      }}
                      onClick={() => console.log('More clicked')}
                    >
                      +{moreCount} more
                    </div>
                  )}
                </div>
                )
              })}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return view === 'day' ? renderDayView() : renderWeekView();
}; 