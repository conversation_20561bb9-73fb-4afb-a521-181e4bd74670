"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-to-mdast";
exports.ids = ["vendor-chunks/hast-util-to-mdast"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/all.js":
/*!****************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/all.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   all: () => (/* binding */ all)\n/* harmony export */ });\n/* harmony import */ var _one_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./one.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/one.js\");\n/**\n * @typedef {import('./types.js').H} H\n * @typedef {import('./types.js').Node} Node\n * @typedef {import('./types.js').MdastNode} MdastNode\n */\n\n\n\n/**\n * @param {H} h\n * @param {Node} parent\n * @returns {Array<MdastNode>}\n */\nfunction all(h, parent) {\n  /** @type {Array<Node>} */\n  // @ts-expect-error Assume `parent` is a parent.\n  const nodes = parent.children || []\n  /** @type {Array<MdastNode>} */\n  const values = []\n  let index = -1\n\n  while (++index < nodes.length) {\n    // @ts-expect-error assume `parent` is a parent.\n    const result = (0,_one_js__WEBPACK_IMPORTED_MODULE_0__.one)(h, nodes[index], parent)\n\n    if (Array.isArray(result)) {\n      values.push(...result)\n    } else if (result) {\n      values.push(result)\n    }\n  }\n\n  let start = 0\n  let end = values.length\n\n  while (start < end && values[start].type === 'break') {\n    start++\n  }\n\n  while (end > start && values[end - 1].type === 'break') {\n    end--\n  }\n\n  return start === 0 && end === values.length\n    ? values\n    : values.slice(start, end)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/all.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/a.js":
/*!***********************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/a.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   a: () => (/* binding */ a)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../all.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/all.js\");\n/* harmony import */ var _util_resolve_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/resolve.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/resolve.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Properties} Properties\n */\n\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction a(h, node) {\n  /** @type {Properties} */\n  // @ts-expect-error: `props` are defined.\n  const props = node.properties\n  return h(\n    node,\n    'link',\n    {\n      title: props.title || null,\n      url: (0,_util_resolve_js__WEBPACK_IMPORTED_MODULE_0__.resolve)(h, String(props.href || '') || null)\n    },\n    (0,_all_js__WEBPACK_IMPORTED_MODULE_1__.all)(h, node)\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9hLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSwrQkFBK0I7QUFDNUMsYUFBYSxrQ0FBa0M7QUFDL0M7O0FBRTZCO0FBQ2E7O0FBRTFDO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1AsYUFBYSxZQUFZO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyx5REFBTztBQUNsQixLQUFLO0FBQ0wsSUFBSSw0Q0FBRztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvYS5qcz8yNDM0Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlByb3BlcnRpZXN9IFByb3BlcnRpZXNcbiAqL1xuXG5pbXBvcnQge2FsbH0gZnJvbSAnLi4vYWxsLmpzJ1xuaW1wb3J0IHtyZXNvbHZlfSBmcm9tICcuLi91dGlsL3Jlc29sdmUuanMnXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gYShoLCBub2RlKSB7XG4gIC8qKiBAdHlwZSB7UHJvcGVydGllc30gKi9cbiAgLy8gQHRzLWV4cGVjdC1lcnJvcjogYHByb3BzYCBhcmUgZGVmaW5lZC5cbiAgY29uc3QgcHJvcHMgPSBub2RlLnByb3BlcnRpZXNcbiAgcmV0dXJuIGgoXG4gICAgbm9kZSxcbiAgICAnbGluaycsXG4gICAge1xuICAgICAgdGl0bGU6IHByb3BzLnRpdGxlIHx8IG51bGwsXG4gICAgICB1cmw6IHJlc29sdmUoaCwgU3RyaW5nKHByb3BzLmhyZWYgfHwgJycpIHx8IG51bGwpXG4gICAgfSxcbiAgICBhbGwoaCwgbm9kZSlcbiAgKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/a.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/base.js":
/*!**************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/base.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base: () => (/* binding */ base)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction base(h, node) {\n  if (!h.baseFound) {\n    h.frozenBaseUrl =\n      String((node.properties && node.properties.href) || '') || null\n    h.baseFound = true\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9iYXNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsK0JBQStCO0FBQzVDOztBQUVBO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL2Jhc2UuanM/NmY5OCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSGFuZGxlfSBIYW5kbGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuRWxlbWVudH0gRWxlbWVudFxuICovXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gYmFzZShoLCBub2RlKSB7XG4gIGlmICghaC5iYXNlRm91bmQpIHtcbiAgICBoLmZyb3plbkJhc2VVcmwgPVxuICAgICAgU3RyaW5nKChub2RlLnByb3BlcnRpZXMgJiYgbm9kZS5wcm9wZXJ0aWVzLmhyZWYpIHx8ICcnKSB8fCBudWxsXG4gICAgaC5iYXNlRm91bmQgPSB0cnVlXG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/base.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/blockquote.js":
/*!********************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/blockquote.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blockquote: () => (/* binding */ blockquote)\n/* harmony export */ });\n/* harmony import */ var _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/wrap-children.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap-children.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction blockquote(h, node) {\n  return h(node, 'blockquote', (0,_util_wrap_children_js__WEBPACK_IMPORTED_MODULE_0__.wrapChildren)(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9ibG9ja3F1b3RlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFcUQ7O0FBRXJEO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1AsK0JBQStCLG9FQUFZO0FBQzNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL2Jsb2NrcXVvdGUuanM/NWRlZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSGFuZGxlfSBIYW5kbGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuRWxlbWVudH0gRWxlbWVudFxuICovXG5cbmltcG9ydCB7d3JhcENoaWxkcmVufSBmcm9tICcuLi91dGlsL3dyYXAtY2hpbGRyZW4uanMnXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gYmxvY2txdW90ZShoLCBub2RlKSB7XG4gIHJldHVybiBoKG5vZGUsICdibG9ja3F1b3RlJywgd3JhcENoaWxkcmVuKGgsIG5vZGUpKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/blockquote.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/br.js":
/*!************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/br.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   br: () => (/* binding */ br)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction br(h, node) {\n  return h.wrapText ? h(node, 'break') : h(node, 'text', ' ')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9ici5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFQTtBQUNBLFVBQVU7QUFDVixXQUFXLFNBQVM7QUFDcEI7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvYnIuanM/MzI5NCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSGFuZGxlfSBIYW5kbGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuRWxlbWVudH0gRWxlbWVudFxuICovXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gYnIoaCwgbm9kZSkge1xuICByZXR1cm4gaC53cmFwVGV4dCA/IGgobm9kZSwgJ2JyZWFrJykgOiBoKG5vZGUsICd0ZXh0JywgJyAnKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/br.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/code.js":
/*!**************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/code.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   code: () => (/* binding */ code)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/./node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var trim_trailing_lines__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! trim-trailing-lines */ \"(ssr)/./node_modules/trim-trailing-lines/index.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').ElementChild} ElementChild\n */\n\n\n\n\n\n\nconst prefix = 'language-'\n\nconst pre = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('pre')\nconst isCode = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('code')\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction code(h, node) {\n  const children = node.children\n  let index = -1\n  /** @type {Array<string|number>|undefined} */\n  let classList\n  /** @type {string|undefined} */\n  let lang\n\n  if (pre(node)) {\n    while (++index < children.length) {\n      const child = children[index]\n\n      if (\n        isCode(child) &&\n        child.properties &&\n        child.properties.className &&\n        Array.isArray(child.properties.className)\n      ) {\n        classList = child.properties.className\n        break\n      }\n    }\n  }\n\n  if (classList) {\n    index = -1\n\n    while (++index < classList.length) {\n      if (String(classList[index]).slice(0, prefix.length) === prefix) {\n        lang = String(classList[index]).slice(prefix.length)\n        break\n      }\n    }\n  }\n\n  return h(\n    node,\n    'code',\n    {lang: lang || null, meta: null},\n    (0,trim_trailing_lines__WEBPACK_IMPORTED_MODULE_1__.trimTrailingLines)((0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_2__.wrapText)(h, (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__.toText)(node)))\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/comment.js":
/*!*****************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/comment.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   comment: () => (/* binding */ comment)\n/* harmony export */ });\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Comment} Comment\n */\n\n\n/**\n * @type {Handle}\n * @param {Comment} node\n */\nfunction comment(h, node) {\n  return h(node, 'html', '<!--' + (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__.wrapText)(h, node.value) + '-->')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9jb21tZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1QztBQUM2Qzs7QUFFN0M7QUFDQSxVQUFVO0FBQ1YsV0FBVyxTQUFTO0FBQ3BCO0FBQ087QUFDUCxrQ0FBa0MsNERBQVE7QUFDMUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvY29tbWVudC5qcz9lZDIyIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5Db21tZW50fSBDb21tZW50XG4gKi9cbmltcG9ydCB7d3JhcFRleHR9IGZyb20gJy4uL3V0aWwvd3JhcC10ZXh0LmpzJ1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge0NvbW1lbnR9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNvbW1lbnQoaCwgbm9kZSkge1xuICByZXR1cm4gaChub2RlLCAnaHRtbCcsICc8IS0tJyArIHdyYXBUZXh0KGgsIG5vZGUudmFsdWUpICsgJy0tPicpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/comment.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/del.js":
/*!*************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/del.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   del: () => (/* binding */ del)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction del(h, node) {\n  return h(node, 'delete', (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9kZWwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsK0JBQStCO0FBQzVDOztBQUU2Qjs7QUFFN0I7QUFDQSxVQUFVO0FBQ1YsV0FBVyxTQUFTO0FBQ3BCO0FBQ087QUFDUCwyQkFBMkIsNENBQUc7QUFDOUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvZGVsLmpzP2MwMTciXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqL1xuXG5pbXBvcnQge2FsbH0gZnJvbSAnLi4vYWxsLmpzJ1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge0VsZW1lbnR9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGRlbChoLCBub2RlKSB7XG4gIHJldHVybiBoKG5vZGUsICdkZWxldGUnLCBhbGwoaCwgbm9kZSkpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/del.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/dl.js":
/*!************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/dl.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dl: () => (/* binding */ dl)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var _util_list_items_spread_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/list-items-spread.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/list-items-spread.js\");\n/* harmony import */ var _util_wrap_list_items_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/wrap-list-items.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap-list-items.js\");\n/**\n * @typedef {import('../types.js').H} H\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').ElementChild} ElementChild\n * @typedef {import('../types.js').MdastNode} MdastNode\n * @typedef {import('../types.js').MdastListContent} MdastListContent\n * @typedef {import('../types.js').MdastBlockContent} MdastBlockContent\n * @typedef {import('../types.js').MdastDefinitionContent} MdastDefinitionContent\n *\n * @typedef Group\n * @property {Array<Element>} titles\n * @property {Array<ElementChild>} definitions\n */\n\n\n\n\n\nconst div = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('div')\nconst dt = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('dt')\nconst dd = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('dd')\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction dl(h, node) {\n  const children = node.children\n  let index = -1\n  /** @type {Array<ElementChild>} */\n  let clean = []\n  /** @type {Array<Group>} */\n  const groups = []\n  /** @type {Group} */\n  let group = {titles: [], definitions: []}\n  /** @type {ElementChild} */\n  let child\n  /** @type {Array<MdastBlockContent|MdastDefinitionContent>} */\n  let result\n\n  // Unwrap `<div>`s\n  while (++index < children.length) {\n    child = children[index]\n    clean = clean.concat(div(child) ? child.children : child)\n  }\n\n  index = -1\n\n  // Group titles and definitions.\n  while (++index < clean.length) {\n    child = clean[index]\n\n    if (dt(child)) {\n      if (dd(clean[index - 1])) {\n        groups.push(group)\n        group = {titles: [], definitions: []}\n      }\n\n      group.titles.push(child)\n    } else {\n      group.definitions.push(child)\n    }\n  }\n\n  groups.push(group)\n\n  // Create items.\n  index = -1\n  /** @type {Array<MdastListContent>} */\n  const content = []\n\n  while (++index < groups.length) {\n    result = [\n      ...handle(h, groups[index].titles),\n      ...handle(h, groups[index].definitions)\n    ]\n\n    if (result.length > 0) {\n      content.push({\n        type: 'listItem',\n        spread: result.length > 1,\n        checked: null,\n        children: result\n      })\n    }\n  }\n\n  // Create a list if there are items.\n  if (content.length > 0) {\n    return h(\n      node,\n      'list',\n      {ordered: false, start: null, spread: (0,_util_list_items_spread_js__WEBPACK_IMPORTED_MODULE_1__.listItemsSpread)(content)},\n      content\n    )\n  }\n}\n\n/**\n * @param {H} h\n * @param {Array<ElementChild>} children\n * @returns {Array<MdastBlockContent|MdastDefinitionContent>}\n */\nfunction handle(h, children) {\n  const nodes = (0,_util_wrap_list_items_js__WEBPACK_IMPORTED_MODULE_2__.wrapListItems)(h, {type: 'element', tagName: 'x', children})\n\n  if (nodes.length === 0) {\n    return []\n  }\n\n  if (nodes.length === 1) {\n    return nodes[0].children\n  }\n\n  return [\n    {\n      type: 'list',\n      ordered: false,\n      start: null,\n      spread: (0,_util_list_items_spread_js__WEBPACK_IMPORTED_MODULE_1__.listItemsSpread)(nodes),\n      children: nodes\n    }\n  ]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/dl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/em.js":
/*!************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/em.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   em: () => (/* binding */ em)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction em(h, node) {\n  return h(node, 'emphasis', (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9lbS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSwrQkFBK0I7QUFDNUM7O0FBRTZCOztBQUU3QjtBQUNBLFVBQVU7QUFDVixXQUFXLFNBQVM7QUFDcEI7QUFDTztBQUNQLDZCQUE2Qiw0Q0FBRztBQUNoQyIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9lbS5qcz8zODE3Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKi9cblxuaW1wb3J0IHthbGx9IGZyb20gJy4uL2FsbC5qcydcblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtFbGVtZW50fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBlbShoLCBub2RlKSB7XG4gIHJldHVybiBoKG5vZGUsICdlbXBoYXNpcycsIGFsbChoLCBub2RlKSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/em.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/heading.js":
/*!*****************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/heading.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   heading: () => (/* binding */ heading)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').MdastNode} MdastNode\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction heading(h, node) {\n  // `else` shouldn’t happen, of course…\n  /* c8 ignore next */\n  const depth = Number(node.tagName.charAt(1)) || 1\n  const wrap = h.wrapText\n\n  h.wrapText = false\n  const result = h(node, 'heading', {depth}, (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node))\n  h.wrapText = wrap\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9oZWFkaW5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1QyxhQUFhLGlDQUFpQztBQUM5Qzs7QUFFNkI7O0FBRTdCO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxxQ0FBcUMsTUFBTSxFQUFFLDRDQUFHO0FBQ2hEOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvaGVhZGluZy5qcz8wOGU3Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLk1kYXN0Tm9kZX0gTWRhc3ROb2RlXG4gKi9cblxuaW1wb3J0IHthbGx9IGZyb20gJy4uL2FsbC5qcydcblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtFbGVtZW50fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBoZWFkaW5nKGgsIG5vZGUpIHtcbiAgLy8gYGVsc2VgIHNob3VsZG7igJl0IGhhcHBlbiwgb2YgY291cnNl4oCmXG4gIC8qIGM4IGlnbm9yZSBuZXh0ICovXG4gIGNvbnN0IGRlcHRoID0gTnVtYmVyKG5vZGUudGFnTmFtZS5jaGFyQXQoMSkpIHx8IDFcbiAgY29uc3Qgd3JhcCA9IGgud3JhcFRleHRcblxuICBoLndyYXBUZXh0ID0gZmFsc2VcbiAgY29uc3QgcmVzdWx0ID0gaChub2RlLCAnaGVhZGluZycsIHtkZXB0aH0sIGFsbChoLCBub2RlKSlcbiAgaC53cmFwVGV4dCA9IHdyYXBcblxuICByZXR1cm4gcmVzdWx0XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/heading.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/hr.js":
/*!************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/hr.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hr: () => (/* binding */ hr)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction hr(h, node) {\n  return h(node, 'thematicBreak')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9oci5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFQTtBQUNBLFVBQVU7QUFDVixXQUFXLFNBQVM7QUFDcEI7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvaHIuanM/MmRiZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSGFuZGxlfSBIYW5kbGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuRWxlbWVudH0gRWxlbWVudFxuICovXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gaHIoaCwgbm9kZSkge1xuICByZXR1cm4gaChub2RlLCAndGhlbWF0aWNCcmVhaycpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/hr.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/iframe.js":
/*!****************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/iframe.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   iframe: () => (/* binding */ iframe)\n/* harmony export */ });\n/* harmony import */ var _util_resolve_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/resolve.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/resolve.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Properties} Properties\n */\n\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction iframe(h, node) {\n  /** @type {Properties} */\n  // @ts-expect-error: `props` are defined.\n  const props = node.properties\n  const src = String(props.src || '')\n  const title = String(props.title || '')\n\n  // Only create a link if there is a title.\n  // We can’t use the content of the frame because conforming HTML parsers treat\n  // it as text, whereas legacy parsers treat it as HTML, so it will likely\n  // contain tags that will show up in text.\n  if (src && title) {\n    return {\n      type: 'link',\n      title: null,\n      url: (0,_util_resolve_js__WEBPACK_IMPORTED_MODULE_0__.resolve)(h, src),\n      children: [{type: 'text', value: (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, title)}]\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/iframe.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/img.js":
/*!*************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/img.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   img: () => (/* binding */ img)\n/* harmony export */ });\n/* harmony import */ var _util_resolve_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/resolve.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/resolve.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Properties} Properties\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction img(h, node) {\n  /** @type {Properties} */\n  // @ts-expect-error: `props` are defined.\n  const props = node.properties\n  return h(node, 'image', {\n    url: (0,_util_resolve_js__WEBPACK_IMPORTED_MODULE_0__.resolve)(h, String(props.src || '') || null),\n    title: props.title || null,\n    alt: props.alt || ''\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9pbWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsK0JBQStCO0FBQzVDLGFBQWEsa0NBQWtDO0FBQy9DOztBQUUwQzs7QUFFMUM7QUFDQSxVQUFVO0FBQ1YsV0FBVyxTQUFTO0FBQ3BCO0FBQ087QUFDUCxhQUFhLFlBQVk7QUFDekI7QUFDQTtBQUNBO0FBQ0EsU0FBUyx5REFBTztBQUNoQjtBQUNBO0FBQ0EsR0FBRztBQUNIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL2ltZy5qcz80ODg1Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlByb3BlcnRpZXN9IFByb3BlcnRpZXNcbiAqL1xuXG5pbXBvcnQge3Jlc29sdmV9IGZyb20gJy4uL3V0aWwvcmVzb2x2ZS5qcydcblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtFbGVtZW50fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpbWcoaCwgbm9kZSkge1xuICAvKiogQHR5cGUge1Byb3BlcnRpZXN9ICovXG4gIC8vIEB0cy1leHBlY3QtZXJyb3I6IGBwcm9wc2AgYXJlIGRlZmluZWQuXG4gIGNvbnN0IHByb3BzID0gbm9kZS5wcm9wZXJ0aWVzXG4gIHJldHVybiBoKG5vZGUsICdpbWFnZScsIHtcbiAgICB1cmw6IHJlc29sdmUoaCwgU3RyaW5nKHByb3BzLnNyYyB8fCAnJykgfHwgbnVsbCksXG4gICAgdGl0bGU6IHByb3BzLnRpdGxlIHx8IG51bGwsXG4gICAgYWx0OiBwcm9wcy5hbHQgfHwgJydcbiAgfSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/img.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handlers: () => (/* binding */ handlers)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../all.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/all.js\");\n/* harmony import */ var _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/wrap-children.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap-children.js\");\n/* harmony import */ var _a_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./a.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/a.js\");\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/base.js\");\n/* harmony import */ var _blockquote_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./blockquote.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/blockquote.js\");\n/* harmony import */ var _br_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./br.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/br.js\");\n/* harmony import */ var _code_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./code.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/code.js\");\n/* harmony import */ var _comment_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./comment.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/comment.js\");\n/* harmony import */ var _del_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./del.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/del.js\");\n/* harmony import */ var _dl_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./dl.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/dl.js\");\n/* harmony import */ var _em_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./em.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/em.js\");\n/* harmony import */ var _heading_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./heading.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/heading.js\");\n/* harmony import */ var _hr_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./hr.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/hr.js\");\n/* harmony import */ var _iframe_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./iframe.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/iframe.js\");\n/* harmony import */ var _img_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./img.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/img.js\");\n/* harmony import */ var _inline_code_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./inline-code.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/inline-code.js\");\n/* harmony import */ var _input_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./input.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/input.js\");\n/* harmony import */ var _li_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./li.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/li.js\");\n/* harmony import */ var _list_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./list.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/list.js\");\n/* harmony import */ var _media_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./media.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/media.js\");\n/* harmony import */ var _p_js__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./p.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/p.js\");\n/* harmony import */ var _q_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./q.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/q.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./root.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/root.js\");\n/* harmony import */ var _select_js__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./select.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/select.js\");\n/* harmony import */ var _strong_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./strong.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/strong.js\");\n/* harmony import */ var _table_cell_js__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./table-cell.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/table-cell.js\");\n/* harmony import */ var _table_row_js__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./table-row.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/table-row.js\");\n/* harmony import */ var _table_js__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./table.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/table.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./text.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/text.js\");\n/* harmony import */ var _textarea_js__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./textarea.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/textarea.js\");\n/* harmony import */ var _wbr_js__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./wbr.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/wbr.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst handlers = {\n  root: _root_js__WEBPACK_IMPORTED_MODULE_0__.root,\n  text: _text_js__WEBPACK_IMPORTED_MODULE_1__.text,\n  comment: _comment_js__WEBPACK_IMPORTED_MODULE_2__.comment,\n  doctype: ignore,\n\n  applet: ignore,\n  area: ignore,\n  basefont: ignore,\n  bgsound: ignore,\n  caption: ignore,\n  col: ignore,\n  colgroup: ignore,\n  command: ignore,\n  content: ignore,\n  datalist: ignore,\n  dialog: ignore,\n  element: ignore,\n  embed: ignore,\n  frame: ignore,\n  frameset: ignore,\n  isindex: ignore,\n  keygen: ignore,\n  link: ignore,\n  math: ignore,\n  menu: ignore,\n  menuitem: ignore,\n  meta: ignore,\n  nextid: ignore,\n  noembed: ignore,\n  noframes: ignore,\n  optgroup: ignore,\n  option: ignore,\n  param: ignore,\n  script: ignore,\n  shadow: ignore,\n  source: ignore,\n  spacer: ignore,\n  style: ignore,\n  svg: ignore,\n  template: ignore,\n  title: ignore,\n  track: ignore,\n\n  abbr: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  acronym: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  bdi: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  bdo: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  big: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  blink: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  button: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  canvas: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  cite: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  data: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  details: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  dfn: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  font: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  ins: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  label: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  map: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  marquee: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  meter: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  nobr: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  noscript: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  object: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  output: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  progress: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  rb: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  rbc: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  rp: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  rt: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  rtc: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  ruby: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  slot: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  small: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  span: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  sup: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  sub: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  tbody: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  tfoot: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  thead: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n  time: _all_js__WEBPACK_IMPORTED_MODULE_3__.all,\n\n  address: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  article: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  aside: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  body: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  center: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  div: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  fieldset: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  figcaption: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  figure: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  form: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  footer: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  header: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  hgroup: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  html: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  legend: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  main: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  multicol: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  nav: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  picture: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n  section: _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_4__.wrapChildren,\n\n  a: _a_js__WEBPACK_IMPORTED_MODULE_5__.a,\n  audio: _media_js__WEBPACK_IMPORTED_MODULE_6__.media,\n  b: _strong_js__WEBPACK_IMPORTED_MODULE_7__.strong,\n  base: _base_js__WEBPACK_IMPORTED_MODULE_8__.base,\n  blockquote: _blockquote_js__WEBPACK_IMPORTED_MODULE_9__.blockquote,\n  br: _br_js__WEBPACK_IMPORTED_MODULE_10__.br,\n  code: _inline_code_js__WEBPACK_IMPORTED_MODULE_11__.inlineCode,\n  dir: _list_js__WEBPACK_IMPORTED_MODULE_12__.list,\n  dl: _dl_js__WEBPACK_IMPORTED_MODULE_13__.dl,\n  dt: _li_js__WEBPACK_IMPORTED_MODULE_14__.li,\n  dd: _li_js__WEBPACK_IMPORTED_MODULE_14__.li,\n  del: _del_js__WEBPACK_IMPORTED_MODULE_15__.del,\n  em: _em_js__WEBPACK_IMPORTED_MODULE_16__.em,\n  h1: _heading_js__WEBPACK_IMPORTED_MODULE_17__.heading,\n  h2: _heading_js__WEBPACK_IMPORTED_MODULE_17__.heading,\n  h3: _heading_js__WEBPACK_IMPORTED_MODULE_17__.heading,\n  h4: _heading_js__WEBPACK_IMPORTED_MODULE_17__.heading,\n  h5: _heading_js__WEBPACK_IMPORTED_MODULE_17__.heading,\n  h6: _heading_js__WEBPACK_IMPORTED_MODULE_17__.heading,\n  hr: _hr_js__WEBPACK_IMPORTED_MODULE_18__.hr,\n  i: _em_js__WEBPACK_IMPORTED_MODULE_16__.em,\n  iframe: _iframe_js__WEBPACK_IMPORTED_MODULE_19__.iframe,\n  img: _img_js__WEBPACK_IMPORTED_MODULE_20__.img,\n  image: _img_js__WEBPACK_IMPORTED_MODULE_20__.img,\n  input: _input_js__WEBPACK_IMPORTED_MODULE_21__.input,\n  kbd: _inline_code_js__WEBPACK_IMPORTED_MODULE_11__.inlineCode,\n  li: _li_js__WEBPACK_IMPORTED_MODULE_14__.li,\n  listing: _code_js__WEBPACK_IMPORTED_MODULE_22__.code,\n  mark: _em_js__WEBPACK_IMPORTED_MODULE_16__.em,\n  ol: _list_js__WEBPACK_IMPORTED_MODULE_12__.list,\n  p: _p_js__WEBPACK_IMPORTED_MODULE_23__.p,\n  plaintext: _code_js__WEBPACK_IMPORTED_MODULE_22__.code,\n  pre: _code_js__WEBPACK_IMPORTED_MODULE_22__.code,\n  q: _q_js__WEBPACK_IMPORTED_MODULE_24__.q,\n  s: _del_js__WEBPACK_IMPORTED_MODULE_15__.del,\n  samp: _inline_code_js__WEBPACK_IMPORTED_MODULE_11__.inlineCode,\n  select: _select_js__WEBPACK_IMPORTED_MODULE_25__.select,\n  strike: _del_js__WEBPACK_IMPORTED_MODULE_15__.del,\n  strong: _strong_js__WEBPACK_IMPORTED_MODULE_7__.strong,\n  summary: _p_js__WEBPACK_IMPORTED_MODULE_23__.p,\n  table: _table_js__WEBPACK_IMPORTED_MODULE_26__.table,\n  td: _table_cell_js__WEBPACK_IMPORTED_MODULE_27__.tableCell,\n  textarea: _textarea_js__WEBPACK_IMPORTED_MODULE_28__.textarea,\n  th: _table_cell_js__WEBPACK_IMPORTED_MODULE_27__.tableCell,\n  tr: _table_row_js__WEBPACK_IMPORTED_MODULE_29__.tableRow,\n  tt: _inline_code_js__WEBPACK_IMPORTED_MODULE_11__.inlineCode,\n  u: _em_js__WEBPACK_IMPORTED_MODULE_16__.em,\n  ul: _list_js__WEBPACK_IMPORTED_MODULE_12__.list,\n  var: _inline_code_js__WEBPACK_IMPORTED_MODULE_11__.inlineCode,\n  video: _media_js__WEBPACK_IMPORTED_MODULE_6__.media,\n  wbr: _wbr_js__WEBPACK_IMPORTED_MODULE_30__.wbr,\n  xmp: _code_js__WEBPACK_IMPORTED_MODULE_22__.code\n}\n\nfunction ignore() {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/inline-code.js":
/*!*********************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/inline-code.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inlineCode: () => (/* binding */ inlineCode)\n/* harmony export */ });\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/./node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction inlineCode(h, node) {\n  return h(node, 'inlineCode', (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__.wrapText)(h, (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_1__.toText)(node)))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9pbmxpbmUtY29kZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsK0JBQStCO0FBQzVDOztBQUV3QztBQUNLOztBQUU3QztBQUNBLFVBQVU7QUFDVixXQUFXLFNBQVM7QUFDcEI7QUFDTztBQUNQLCtCQUErQiw0REFBUSxJQUFJLHlEQUFNO0FBQ2pEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL2lubGluZS1jb2RlLmpzP2ZiOTkiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqL1xuXG5pbXBvcnQge3RvVGV4dH0gZnJvbSAnaGFzdC11dGlsLXRvLXRleHQnXG5pbXBvcnQge3dyYXBUZXh0fSBmcm9tICcuLi91dGlsL3dyYXAtdGV4dC5qcydcblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtFbGVtZW50fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpbmxpbmVDb2RlKGgsIG5vZGUpIHtcbiAgcmV0dXJuIGgobm9kZSwgJ2lubGluZUNvZGUnLCB3cmFwVGV4dChoLCB0b1RleHQobm9kZSkpKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/inline-code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/input.js":
/*!***************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/input.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   input: () => (/* binding */ input)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var _util_find_selected_options_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/find-selected-options.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/find-selected-options.js\");\n/* harmony import */ var _util_own_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/own.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/own.js\");\n/* harmony import */ var _util_resolve_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/resolve.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/resolve.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Properties} Properties\n * @typedef {import('../types.js').MdastNode} MdastNode\n */\n\n\n\n\n\n\n\nconst datalist = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('datalist')\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\n// eslint-disable-next-line complexity\nfunction input(h, node) {\n  /** @type {Properties} */\n  // @ts-expect-error: `props` are defined.\n  const props = node.properties\n  let value = String(props.value || props.placeholder || '')\n  /** @type {Array<MdastNode>} */\n  const results = []\n  /** @type {Array<string>} */\n  const texts = []\n  /** @type {Array<[string, string|null]>} */\n  let values = []\n  let index = -1\n  /** @type {string} */\n  let list\n\n  if (props.disabled || props.type === 'hidden' || props.type === 'file') {\n    return\n  }\n\n  if (props.type === 'checkbox' || props.type === 'radio') {\n    return h(\n      node,\n      'text',\n      (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, h[props.checked ? 'checked' : 'unchecked'])\n    )\n  }\n\n  if (props.type === 'image') {\n    return props.alt || value\n      ? h(node, 'image', {\n          url: (0,_util_resolve_js__WEBPACK_IMPORTED_MODULE_2__.resolve)(h, String(props.src || '') || null),\n          title: (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, String(props.title || '')) || null,\n          alt: (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, String(props.alt || value))\n        })\n      : []\n  }\n\n  if (value) {\n    values = [[value, null]]\n  } else if (\n    // `list` is not supported on these types:\n    props.type !== 'password' &&\n    props.type !== 'file' &&\n    props.type !== 'submit' &&\n    props.type !== 'reset' &&\n    props.type !== 'button' &&\n    props.list\n  ) {\n    list = String(props.list).toUpperCase()\n\n    if (_util_own_js__WEBPACK_IMPORTED_MODULE_3__.own.call(h.nodeById, list) && datalist(h.nodeById[list])) {\n      values = (0,_util_find_selected_options_js__WEBPACK_IMPORTED_MODULE_4__.findSelectedOptions)(h, h.nodeById[list], props)\n    }\n  }\n\n  if (values.length === 0) {\n    return\n  }\n\n  // Hide password value.\n  if (props.type === 'password') {\n    // Passwords don’t support `list`.\n    values[0] = ['•'.repeat(values[0][0].length), null]\n  }\n\n  if (props.type === 'url' || props.type === 'email') {\n    while (++index < values.length) {\n      value = (0,_util_resolve_js__WEBPACK_IMPORTED_MODULE_2__.resolve)(h, values[index][0])\n\n      results.push(\n        h(\n          node,\n          'link',\n          {\n            title: null,\n            url: (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, props.type === 'email' ? 'mailto:' + value : value)\n          },\n          [{type: 'text', value: (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, values[index][1] || value)}]\n        )\n      )\n\n      if (index !== values.length - 1) {\n        results.push({type: 'text', value: ', '})\n      }\n    }\n\n    return results\n  }\n\n  while (++index < values.length) {\n    texts.push(\n      values[index][1]\n        ? values[index][1] + ' (' + values[index][0] + ')'\n        : values[index][0]\n    )\n  }\n\n  return h(node, 'text', (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, texts.join(', ')))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/input.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/li.js":
/*!************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/li.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   li: () => (/* binding */ li)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var _util_wrap_children_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/wrap-children.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap-children.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').ElementChild} ElementChild\n * @typedef {import('../types.js').MdastNode} MdastNode\n */\n\n\n\n\nconst p = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('p')\nconst input = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('input')\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction li(h, node) {\n  const head = node.children[0]\n  /** @type {boolean|null} */\n  let checked = null\n  /** @type {ElementChild} */\n  let checkbox\n  /** @type {Element|undefined} */\n  let clone\n\n  // Check if this node starts with a checkbox.\n  if (p(head)) {\n    checkbox = head.children[0]\n\n    if (\n      input(checkbox) &&\n      checkbox.properties &&\n      (checkbox.properties.type === 'checkbox' ||\n        checkbox.properties.type === 'radio')\n    ) {\n      checked = Boolean(checkbox.properties.checked)\n      clone = {\n        ...node,\n        children: [\n          {...head, children: head.children.slice(1)},\n          ...node.children.slice(1)\n        ]\n      }\n    }\n  }\n\n  const content = (0,_util_wrap_children_js__WEBPACK_IMPORTED_MODULE_1__.wrapChildren)(h, clone || node)\n\n  return h(node, 'listItem', {spread: content.length > 1, checked}, content)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/li.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/list.js":
/*!**************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/list.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   list: () => (/* binding */ list)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var hast_util_has_property__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-has-property */ \"(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-has-property/lib/index.js\");\n/* harmony import */ var _util_list_items_spread_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/list-items-spread.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/list-items-spread.js\");\n/* harmony import */ var _util_wrap_list_items_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/wrap-list-items.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap-list-items.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n\n\n\nconst ol = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('ol')\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction list(h, node) {\n  const ordered = ol(node)\n  const children = (0,_util_wrap_list_items_js__WEBPACK_IMPORTED_MODULE_1__.wrapListItems)(h, node)\n  /** @type {number|null} */\n  let start = null\n\n  if (ordered) {\n    start = (0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_2__.hasProperty)(node, 'start')\n      ? // @ts-expect-error: `props` exist.\n        Number.parseInt(String(node.properties.start), 10)\n      : 1\n  }\n\n  return h(\n    node,\n    'list',\n    {ordered, start, spread: (0,_util_list_items_spread_js__WEBPACK_IMPORTED_MODULE_3__.listItemsSpread)(children)},\n    children\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9saXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFbUQ7QUFDRDtBQUNVO0FBQ0o7O0FBRXhELFdBQVcsb0VBQWM7O0FBRXpCO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1A7QUFDQSxtQkFBbUIsdUVBQWE7QUFDaEMsYUFBYSxhQUFhO0FBQzFCOztBQUVBO0FBQ0EsWUFBWSxtRUFBVztBQUN2QjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxLQUFLLHdCQUF3QiwyRUFBZSxXQUFXO0FBQ3ZEO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9saXN0LmpzPzJmZTEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqL1xuXG5pbXBvcnQge2NvbnZlcnRFbGVtZW50fSBmcm9tICdoYXN0LXV0aWwtaXMtZWxlbWVudCdcbmltcG9ydCB7aGFzUHJvcGVydHl9IGZyb20gJ2hhc3QtdXRpbC1oYXMtcHJvcGVydHknXG5pbXBvcnQge2xpc3RJdGVtc1NwcmVhZH0gZnJvbSAnLi4vdXRpbC9saXN0LWl0ZW1zLXNwcmVhZC5qcydcbmltcG9ydCB7d3JhcExpc3RJdGVtc30gZnJvbSAnLi4vdXRpbC93cmFwLWxpc3QtaXRlbXMuanMnXG5cbmNvbnN0IG9sID0gY29udmVydEVsZW1lbnQoJ29sJylcblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtFbGVtZW50fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBsaXN0KGgsIG5vZGUpIHtcbiAgY29uc3Qgb3JkZXJlZCA9IG9sKG5vZGUpXG4gIGNvbnN0IGNoaWxkcmVuID0gd3JhcExpc3RJdGVtcyhoLCBub2RlKVxuICAvKiogQHR5cGUge251bWJlcnxudWxsfSAqL1xuICBsZXQgc3RhcnQgPSBudWxsXG5cbiAgaWYgKG9yZGVyZWQpIHtcbiAgICBzdGFydCA9IGhhc1Byb3BlcnR5KG5vZGUsICdzdGFydCcpXG4gICAgICA/IC8vIEB0cy1leHBlY3QtZXJyb3I6IGBwcm9wc2AgZXhpc3QuXG4gICAgICAgIE51bWJlci5wYXJzZUludChTdHJpbmcobm9kZS5wcm9wZXJ0aWVzLnN0YXJ0KSwgMTApXG4gICAgICA6IDFcbiAgfVxuXG4gIHJldHVybiBoKFxuICAgIG5vZGUsXG4gICAgJ2xpc3QnLFxuICAgIHtvcmRlcmVkLCBzdGFydCwgc3ByZWFkOiBsaXN0SXRlbXNTcHJlYWQoY2hpbGRyZW4pfSxcbiAgICBjaGlsZHJlblxuICApXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/list.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/media.js":
/*!***************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/media.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   media: () => (/* binding */ media)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! mdast-util-to-string */ \"(ssr)/./node_modules/mdast-util-to-string/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/node_modules/unist-util-visit-parents/lib/index.js\");\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../all.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/all.js\");\n/* harmony import */ var _util_resolve_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/resolve.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/resolve.js\");\n/* harmony import */ var _util_wrap_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/wrap.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Properties} Properties\n * @typedef {import('../types.js').ElementChild} ElementChild\n */\n\n\n\n\n\n\n\n\nconst source = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('source')\nconst video = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('video')\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction media(h, node) {\n  let nodes = (0,_all_js__WEBPACK_IMPORTED_MODULE_1__.all)(h, node)\n  /** @type {Properties} */\n  // @ts-expect-error: `props` are defined.\n  const properties = node.properties\n  const poster = video(node) && String(properties.poster || '')\n  let src = String(properties.src || '')\n  let index = -1\n  /** @type {boolean} */\n  let linkInFallbackContent = false\n  /** @type {ElementChild} */\n  let child\n\n  ;(0,unist_util_visit__WEBPACK_IMPORTED_MODULE_2__.visit)({type: 'root', children: nodes}, 'link', findLink)\n\n  // If the content links to something, or if it’s not phrasing…\n  if (linkInFallbackContent || (0,_util_wrap_js__WEBPACK_IMPORTED_MODULE_3__.wrapNeeded)(nodes)) {\n    return nodes\n  }\n\n  // Find the source.\n  while (!src && ++index < node.children.length) {\n    child = node.children[index]\n    if (source(child)) {\n      // @ts-expect-error: `props` are defined.\n      src = String(child.properties.src || '')\n    }\n  }\n\n  // If there’s a poster defined on the video, create an image.\n  if (poster) {\n    nodes = [\n      {\n        type: 'image',\n        title: null,\n        url: (0,_util_resolve_js__WEBPACK_IMPORTED_MODULE_4__.resolve)(h, poster),\n        alt: (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_5__.toString)({children: nodes})\n      }\n    ]\n  }\n\n  // Link to the media resource.\n  return {\n    type: 'link',\n    // @ts-expect-error Types are broken.\n    title: node.properties.title || null,\n    url: (0,_util_resolve_js__WEBPACK_IMPORTED_MODULE_4__.resolve)(h, src),\n    // @ts-expect-error Assume phrasing content.\n    children: nodes\n  }\n\n  function findLink() {\n    linkInFallbackContent = true\n    return unist_util_visit__WEBPACK_IMPORTED_MODULE_6__.EXIT\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/media.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/p.js":
/*!***********************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/p.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   p: () => (/* binding */ p)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction p(h, node) {\n  const nodes = (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node)\n\n  if (nodes.length > 0) {\n    return h(node, 'paragraph', nodes)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9wLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFNkI7O0FBRTdCO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1AsZ0JBQWdCLDRDQUFHOztBQUVuQjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9wLmpzP2NhNGUiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqL1xuXG5pbXBvcnQge2FsbH0gZnJvbSAnLi4vYWxsLmpzJ1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge0VsZW1lbnR9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHAoaCwgbm9kZSkge1xuICBjb25zdCBub2RlcyA9IGFsbChoLCBub2RlKVxuXG4gIGlmIChub2Rlcy5sZW5ndGggPiAwKSB7XG4gICAgcmV0dXJuIGgobm9kZSwgJ3BhcmFncmFwaCcsIG5vZGVzKVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/p.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/q.js":
/*!***********************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/q.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   q: () => (/* binding */ q)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').MdastNode} MdastNode\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction q(h, node) {\n  const expected = h.quotes[h.qNesting % h.quotes.length]\n\n  h.qNesting++\n  const contents = (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node)\n  h.qNesting--\n\n  contents.unshift({type: 'text', value: expected.charAt(0)})\n\n  contents.push({\n    type: 'text',\n    value: expected.length > 1 ? expected.charAt(1) : expected\n  })\n\n  return contents\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9xLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1QyxhQUFhLGlDQUFpQztBQUM5Qzs7QUFFNkI7O0FBRTdCO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1A7O0FBRUE7QUFDQSxtQkFBbUIsNENBQUc7QUFDdEI7O0FBRUEsb0JBQW9CLHdDQUF3Qzs7QUFFNUQ7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL3EuanM/OGUyZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSGFuZGxlfSBIYW5kbGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5NZGFzdE5vZGV9IE1kYXN0Tm9kZVxuICovXG5cbmltcG9ydCB7YWxsfSBmcm9tICcuLi9hbGwuanMnXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gcShoLCBub2RlKSB7XG4gIGNvbnN0IGV4cGVjdGVkID0gaC5xdW90ZXNbaC5xTmVzdGluZyAlIGgucXVvdGVzLmxlbmd0aF1cblxuICBoLnFOZXN0aW5nKytcbiAgY29uc3QgY29udGVudHMgPSBhbGwoaCwgbm9kZSlcbiAgaC5xTmVzdGluZy0tXG5cbiAgY29udGVudHMudW5zaGlmdCh7dHlwZTogJ3RleHQnLCB2YWx1ZTogZXhwZWN0ZWQuY2hhckF0KDApfSlcblxuICBjb250ZW50cy5wdXNoKHtcbiAgICB0eXBlOiAndGV4dCcsXG4gICAgdmFsdWU6IGV4cGVjdGVkLmxlbmd0aCA+IDEgPyBleHBlY3RlZC5jaGFyQXQoMSkgOiBleHBlY3RlZFxuICB9KVxuXG4gIHJldHVybiBjb250ZW50c1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/q.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/root.js":
/*!**************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/root.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: () => (/* binding */ root)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/all.js\");\n/* harmony import */ var _util_wrap_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/wrap.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Root} Root\n */\n\n\n\n\n/**\n * @type {Handle}\n * @param {Root} node\n */\nfunction root(h, node) {\n  let children = (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node)\n\n  if (h.document || (0,_util_wrap_js__WEBPACK_IMPORTED_MODULE_1__.wrapNeeded)(children)) {\n    children = (0,_util_wrap_js__WEBPACK_IMPORTED_MODULE_1__.wrap)(children)\n  }\n\n  return h(node, 'root', children)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9yb290LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSw0QkFBNEI7QUFDekM7O0FBRTZCO0FBQ21COztBQUVoRDtBQUNBLFVBQVU7QUFDVixXQUFXLE1BQU07QUFDakI7QUFDTztBQUNQLGlCQUFpQiw0Q0FBRzs7QUFFcEIsb0JBQW9CLHlEQUFVO0FBQzlCLGVBQWUsbURBQUk7QUFDbkI7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9yb290LmpzPzc4OWYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlJvb3R9IFJvb3RcbiAqL1xuXG5pbXBvcnQge2FsbH0gZnJvbSAnLi4vYWxsLmpzJ1xuaW1wb3J0IHt3cmFwLCB3cmFwTmVlZGVkfSBmcm9tICcuLi91dGlsL3dyYXAuanMnXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7Um9vdH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gcm9vdChoLCBub2RlKSB7XG4gIGxldCBjaGlsZHJlbiA9IGFsbChoLCBub2RlKVxuXG4gIGlmIChoLmRvY3VtZW50IHx8IHdyYXBOZWVkZWQoY2hpbGRyZW4pKSB7XG4gICAgY2hpbGRyZW4gPSB3cmFwKGNoaWxkcmVuKVxuICB9XG5cbiAgcmV0dXJuIGgobm9kZSwgJ3Jvb3QnLCBjaGlsZHJlbilcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/root.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/select.js":
/*!****************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/select.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   select: () => (/* binding */ select)\n/* harmony export */ });\n/* harmony import */ var _util_find_selected_options_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/find-selected-options.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/find-selected-options.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction select(h, node) {\n  const values = (0,_util_find_selected_options_js__WEBPACK_IMPORTED_MODULE_0__.findSelectedOptions)(h, node)\n  let index = -1\n  /** @type {Array<string>} */\n  const results = []\n  /** @type {[string, string|null]} */\n  let value\n\n  while (++index < values.length) {\n    value = values[index]\n    results.push(value[1] ? value[1] + ' (' + value[0] + ')' : value[0])\n  }\n\n  if (results.length > 0) {\n    return h(node, 'text', (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, results.join(', ')))\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9zZWxlY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFb0U7QUFDdkI7O0FBRTdDO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1AsaUJBQWlCLG1GQUFtQjtBQUNwQztBQUNBLGFBQWEsZUFBZTtBQUM1QjtBQUNBLGFBQWEsdUJBQXVCO0FBQ3BDOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsMkJBQTJCLDREQUFRO0FBQ25DO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvc2VsZWN0LmpzPzc0NWIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqL1xuXG5pbXBvcnQge2ZpbmRTZWxlY3RlZE9wdGlvbnN9IGZyb20gJy4uL3V0aWwvZmluZC1zZWxlY3RlZC1vcHRpb25zLmpzJ1xuaW1wb3J0IHt3cmFwVGV4dH0gZnJvbSAnLi4vdXRpbC93cmFwLXRleHQuanMnXG5cbi8qKlxuICogQHR5cGUge0hhbmRsZX1cbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gc2VsZWN0KGgsIG5vZGUpIHtcbiAgY29uc3QgdmFsdWVzID0gZmluZFNlbGVjdGVkT3B0aW9ucyhoLCBub2RlKVxuICBsZXQgaW5kZXggPSAtMVxuICAvKiogQHR5cGUge0FycmF5PHN0cmluZz59ICovXG4gIGNvbnN0IHJlc3VsdHMgPSBbXVxuICAvKiogQHR5cGUge1tzdHJpbmcsIHN0cmluZ3xudWxsXX0gKi9cbiAgbGV0IHZhbHVlXG5cbiAgd2hpbGUgKCsraW5kZXggPCB2YWx1ZXMubGVuZ3RoKSB7XG4gICAgdmFsdWUgPSB2YWx1ZXNbaW5kZXhdXG4gICAgcmVzdWx0cy5wdXNoKHZhbHVlWzFdID8gdmFsdWVbMV0gKyAnICgnICsgdmFsdWVbMF0gKyAnKScgOiB2YWx1ZVswXSlcbiAgfVxuXG4gIGlmIChyZXN1bHRzLmxlbmd0aCA+IDApIHtcbiAgICByZXR1cm4gaChub2RlLCAndGV4dCcsIHdyYXBUZXh0KGgsIHJlc3VsdHMuam9pbignLCAnKSkpXG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/select.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/strong.js":
/*!****************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/strong.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strong: () => (/* binding */ strong)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction strong(h, node) {\n  return h(node, 'strong', (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9zdHJvbmcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsK0JBQStCO0FBQzVDOztBQUU2Qjs7QUFFN0I7QUFDQSxVQUFVO0FBQ1YsV0FBVyxTQUFTO0FBQ3BCO0FBQ087QUFDUCwyQkFBMkIsNENBQUc7QUFDOUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvc3Ryb25nLmpzPzA1ZGIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqL1xuXG5pbXBvcnQge2FsbH0gZnJvbSAnLi4vYWxsLmpzJ1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge0VsZW1lbnR9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHN0cm9uZyhoLCBub2RlKSB7XG4gIHJldHVybiBoKG5vZGUsICdzdHJvbmcnLCBhbGwoaCwgbm9kZSkpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/strong.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/table-cell.js":
/*!********************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/table-cell.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tableCell: () => (/* binding */ tableCell)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').MdastNode} MdastNode\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction tableCell(h, node) {\n  const wrap = h.wrapText\n\n  h.wrapText = false\n\n  const result = h(node, 'tableCell', (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node))\n\n  if (node.properties && (node.properties.rowSpan || node.properties.colSpan)) {\n    const data = result.data || (result.data = {})\n    if (node.properties.rowSpan) data.rowSpan = node.properties.rowSpan\n    if (node.properties.colSpan) data.colSpan = node.properties.colSpan\n  }\n\n  h.wrapText = wrap\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy90YWJsZS1jZWxsLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLCtCQUErQjtBQUM1QyxhQUFhLGlDQUFpQztBQUM5Qzs7QUFFNkI7O0FBRTdCO0FBQ0EsVUFBVTtBQUNWLFdBQVcsU0FBUztBQUNwQjtBQUNPO0FBQ1A7O0FBRUE7O0FBRUEsc0NBQXNDLDRDQUFHOztBQUV6QztBQUNBLGlEQUFpRDtBQUNqRDtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy90YWJsZS1jZWxsLmpzPzExNzIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuTWRhc3ROb2RlfSBNZGFzdE5vZGVcbiAqL1xuXG5pbXBvcnQge2FsbH0gZnJvbSAnLi4vYWxsLmpzJ1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge0VsZW1lbnR9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHRhYmxlQ2VsbChoLCBub2RlKSB7XG4gIGNvbnN0IHdyYXAgPSBoLndyYXBUZXh0XG5cbiAgaC53cmFwVGV4dCA9IGZhbHNlXG5cbiAgY29uc3QgcmVzdWx0ID0gaChub2RlLCAndGFibGVDZWxsJywgYWxsKGgsIG5vZGUpKVxuXG4gIGlmIChub2RlLnByb3BlcnRpZXMgJiYgKG5vZGUucHJvcGVydGllcy5yb3dTcGFuIHx8IG5vZGUucHJvcGVydGllcy5jb2xTcGFuKSkge1xuICAgIGNvbnN0IGRhdGEgPSByZXN1bHQuZGF0YSB8fCAocmVzdWx0LmRhdGEgPSB7fSlcbiAgICBpZiAobm9kZS5wcm9wZXJ0aWVzLnJvd1NwYW4pIGRhdGEucm93U3BhbiA9IG5vZGUucHJvcGVydGllcy5yb3dTcGFuXG4gICAgaWYgKG5vZGUucHJvcGVydGllcy5jb2xTcGFuKSBkYXRhLmNvbFNwYW4gPSBub2RlLnByb3BlcnRpZXMuY29sU3BhblxuICB9XG5cbiAgaC53cmFwVGV4dCA9IHdyYXBcblxuICByZXR1cm4gcmVzdWx0XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/table-cell.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/table-row.js":
/*!*******************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/table-row.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tableRow: () => (/* binding */ tableRow)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction tableRow(h, node) {\n  return h(node, 'tableRow', (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy90YWJsZS1yb3cuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsK0JBQStCO0FBQzVDOztBQUU2Qjs7QUFFN0I7QUFDQSxVQUFVO0FBQ1YsV0FBVyxTQUFTO0FBQ3BCO0FBQ087QUFDUCw2QkFBNkIsNENBQUc7QUFDaEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvaGFuZGxlcnMvdGFibGUtcm93LmpzP2IwMzkiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqL1xuXG5pbXBvcnQge2FsbH0gZnJvbSAnLi4vYWxsLmpzJ1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge0VsZW1lbnR9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHRhYmxlUm93KGgsIG5vZGUpIHtcbiAgcmV0dXJuIGgobm9kZSwgJ3RhYmxlUm93JywgYWxsKGgsIG5vZGUpKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/table-row.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/table.js":
/*!***************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/table.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   table: () => (/* binding */ table)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/./node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/node_modules/unist-util-visit-parents/lib/index.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../all.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').MdastNode} MdastNode\n * @typedef {import('../types.js').MdastTableContent} MdastTableContent\n * @typedef {import('../types.js').MdastRowContent} MdastRowContent\n * @typedef {import('../types.js').MdastPhrasingContent} MdastPhrasingContent\n *\n * @typedef Info\n * @property {Array<string|null>} align\n * @property {boolean} headless\n */\n\n\n\n\n\n\n\nconst thead = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('thead')\nconst tr = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('tr')\nconst cell = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)(['th', 'td'])\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction table(h, node) {\n  if (h.inTable) {\n    return h(node, 'text', (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_2__.toText)(node)))\n  }\n\n  h.inTable = true\n\n  const {headless, align} = inspect(node)\n  const rows = toRows((0,_all_js__WEBPACK_IMPORTED_MODULE_3__.all)(h, node), headless)\n  let columns = 1\n  let rowIndex = -1\n\n  while (++rowIndex < rows.length) {\n    const cells = rows[rowIndex].children\n    let cellIndex = -1\n\n    while (++cellIndex < cells.length) {\n      const cell = cells[cellIndex]\n\n      if (cell.data) {\n        const colSpan = Number.parseInt(String(cell.data.colSpan), 10) || 1\n        const rowSpan = Number.parseInt(String(cell.data.rowSpan), 10) || 1\n\n        if (colSpan > 1 || rowSpan > 1) {\n          let otherRowIndex = rowIndex - 1\n\n          while (++otherRowIndex < rowIndex + rowSpan) {\n            let colIndex = cellIndex - 1\n\n            while (++colIndex < cellIndex + colSpan) {\n              if (!rows[otherRowIndex]) {\n                // Don’t add rows that don’t exist.\n                // Browsers don’t render them either.\n                break\n              }\n\n              /** @type {Array<MdastRowContent>} */\n              const newCells = []\n\n              if (otherRowIndex !== rowIndex || colIndex !== cellIndex) {\n                newCells.push({type: 'tableCell', children: []})\n              }\n\n              rows[otherRowIndex].children.splice(colIndex, 0, ...newCells)\n            }\n          }\n        }\n\n        // Clean the data fields.\n        if ('colSpan' in cell.data) delete cell.data.colSpan\n        if ('rowSpan' in cell.data) delete cell.data.rowSpan\n        if (Object.keys(cell.data).length === 0) delete cell.data\n      }\n    }\n\n    if (cells.length > columns) columns = cells.length\n  }\n\n  // Add extra empty cells.\n  rowIndex = -1\n\n  while (++rowIndex < rows.length) {\n    const cells = rows[rowIndex].children\n    let cellIndex = cells.length - 1\n    while (++cellIndex < columns) {\n      cells.push({type: 'tableCell', children: []})\n    }\n  }\n\n  let alignIndex = align.length - 1\n  while (++alignIndex < columns) {\n    align.push(null)\n  }\n\n  h.inTable = false\n\n  return h(node, 'table', {align}, rows)\n}\n\n/**\n * Infer whether the HTML table has a head and how it aligns.\n *\n * @param {Element} node\n * @returns {Info}\n */\nfunction inspect(node) {\n  let headless = true\n  let rowIndex = 0\n  let cellIndex = 0\n  /** @type {Array<string|null>} */\n  const align = [null]\n\n  ;(0,unist_util_visit__WEBPACK_IMPORTED_MODULE_4__.visit)(node, 'element', (child) => {\n    if (child.tagName === 'table' && node !== child) {\n      return unist_util_visit__WEBPACK_IMPORTED_MODULE_5__.SKIP\n    }\n\n    // If there is a `thead`, assume there is a header row.\n    if (cell(child) && child.properties) {\n      if (!align[cellIndex]) {\n        align[cellIndex] = String(child.properties.align || '') || null\n      }\n\n      // If there is a th in the first row, assume there is a header row.\n      if (headless && rowIndex < 2 && child.tagName === 'th') {\n        headless = false\n      }\n\n      cellIndex++\n    } else if (thead(child)) {\n      headless = false\n    } else if (tr(child)) {\n      rowIndex++\n      cellIndex = 0\n    }\n  })\n\n  return {align, headless}\n}\n\n/**\n * Ensure the rows are properly structured.\n *\n * @param {Array<MdastNode>} children\n * @param {boolean} headless\n * @returns {Array<MdastTableContent>}\n */\nfunction toRows(children, headless) {\n  let index = -1\n  /** @type {Array<MdastTableContent>} */\n  const nodes = []\n  /** @type {Array<MdastRowContent>|undefined} */\n  let queue\n\n  // Add an empty header row.\n  if (headless) {\n    nodes.push({type: 'tableRow', children: []})\n  }\n\n  while (++index < children.length) {\n    const node = children[index]\n\n    if (node.type === 'tableRow') {\n      if (queue) {\n        node.children.unshift(...queue)\n        queue = undefined\n      }\n\n      nodes.push(node)\n    } else {\n      if (!queue) queue = []\n      // @ts-expect-error Assume row content.\n      queue.push(node)\n    }\n  }\n\n  if (queue) {\n    nodes[nodes.length - 1].children.push(...queue)\n  }\n\n  index = -1\n\n  while (++index < nodes.length) {\n    nodes[index].children = toCells(nodes[index].children)\n  }\n\n  return nodes\n}\n\n/**\n * Ensure the cells in a row are properly structured.\n *\n * @param {Array<MdastNode>} children\n * @returns {Array<MdastRowContent>}\n */\nfunction toCells(children) {\n  /** @type {Array<MdastRowContent>} */\n  const nodes = []\n  let index = -1\n  /** @type {MdastNode} */\n  let node\n  /** @type {Array<MdastPhrasingContent>|undefined} */\n  let queue\n\n  while (++index < children.length) {\n    node = children[index]\n\n    if (node.type === 'tableCell') {\n      if (queue) {\n        node.children.unshift(...queue)\n        queue = undefined\n      }\n\n      nodes.push(node)\n    } else {\n      if (!queue) queue = []\n      // @ts-expect-error Assume phrasing content.\n      queue.push(node)\n    }\n  }\n\n  if (queue) {\n    node = nodes[nodes.length - 1]\n\n    if (!node) {\n      node = {type: 'tableCell', children: []}\n      nodes.push(node)\n    }\n\n    node.children.push(...queue)\n  }\n\n  return nodes\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/table.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/text.js":
/*!**************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/text.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Text} Text\n */\n\n\n\n/**\n * @type {Handle}\n * @param {Text} node\n */\nfunction text(h, node) {\n  return h(node, 'text', (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__.wrapText)(h, node.value))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy90ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLDRCQUE0QjtBQUN6Qzs7QUFFNkM7O0FBRTdDO0FBQ0EsVUFBVTtBQUNWLFdBQVcsTUFBTTtBQUNqQjtBQUNPO0FBQ1AseUJBQXlCLDREQUFRO0FBQ2pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL3RleHQuanM/MjQ0NiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSGFuZGxlfSBIYW5kbGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuVGV4dH0gVGV4dFxuICovXG5cbmltcG9ydCB7d3JhcFRleHR9IGZyb20gJy4uL3V0aWwvd3JhcC10ZXh0LmpzJ1xuXG4vKipcbiAqIEB0eXBlIHtIYW5kbGV9XG4gKiBAcGFyYW0ge1RleHR9IG5vZGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHRleHQoaCwgbm9kZSkge1xuICByZXR1cm4gaChub2RlLCAndGV4dCcsIHdyYXBUZXh0KGgsIG5vZGUudmFsdWUpKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/textarea.js":
/*!******************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/textarea.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   textarea: () => (/* binding */ textarea)\n/* harmony export */ });\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/./node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/wrap-text.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n\n\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction textarea(h, node) {\n  return h(node, 'text', (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_0__.wrapText)(h, (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_1__.toText)(node)))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy90ZXh0YXJlYS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsK0JBQStCO0FBQzVDOztBQUV3QztBQUNLOztBQUU3QztBQUNBLFVBQVU7QUFDVixXQUFXLFNBQVM7QUFDcEI7QUFDTztBQUNQLHlCQUF5Qiw0REFBUSxJQUFJLHlEQUFNO0FBQzNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL3RleHRhcmVhLmpzPzNiZGEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkhhbmRsZX0gSGFuZGxlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqL1xuXG5pbXBvcnQge3RvVGV4dH0gZnJvbSAnaGFzdC11dGlsLXRvLXRleHQnXG5pbXBvcnQge3dyYXBUZXh0fSBmcm9tICcuLi91dGlsL3dyYXAtdGV4dC5qcydcblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtFbGVtZW50fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0ZXh0YXJlYShoLCBub2RlKSB7XG4gIHJldHVybiBoKG5vZGUsICd0ZXh0Jywgd3JhcFRleHQoaCwgdG9UZXh0KG5vZGUpKSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/textarea.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/wbr.js":
/*!*************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/wbr.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wbr: () => (/* binding */ wbr)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Element} Element\n */\n\n/**\n * @type {Handle}\n * @param {Element} node\n */\nfunction wbr(h, node) {\n  return h(node, 'text', '\\u200B')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy93YnIuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSwrQkFBK0I7QUFDNUM7O0FBRUE7QUFDQSxVQUFVO0FBQ1YsV0FBVyxTQUFTO0FBQ3BCO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL2hhbmRsZXJzL3dici5qcz9mNGU2Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5FbGVtZW50fSBFbGVtZW50XG4gKi9cblxuLyoqXG4gKiBAdHlwZSB7SGFuZGxlfVxuICogQHBhcmFtIHtFbGVtZW50fSBub2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB3YnIoaCwgbm9kZSkge1xuICByZXR1cm4gaChub2RlLCAndGV4dCcsICdcXHUyMDBCJylcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/wbr.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   all: () => (/* reexport safe */ _all_js__WEBPACK_IMPORTED_MODULE_1__.all),\n/* harmony export */   defaultHandlers: () => (/* reexport safe */ _handlers_index_js__WEBPACK_IMPORTED_MODULE_3__.handlers),\n/* harmony export */   one: () => (/* reexport safe */ _one_js__WEBPACK_IMPORTED_MODULE_0__.one),\n/* harmony export */   toMdast: () => (/* binding */ toMdast)\n/* harmony export */ });\n/* harmony import */ var rehype_minify_whitespace__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rehype-minify-whitespace */ \"(ssr)/./node_modules/rehype-minify-whitespace/index.js\");\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unist-util-is */ \"(ssr)/./node_modules/hast-util-to-mdast/node_modules/unist-util-is/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var _one_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./one.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/one.js\");\n/* harmony import */ var _handlers_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./handlers/index.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/index.js\");\n/* harmony import */ var _util_own_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./util/own.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/own.js\");\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./all.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('./types.js').Node} Node\n * @typedef {import('./types.js').Element} Element\n * @typedef {import('./types.js').Options} Options\n * @typedef {import('./types.js').Properties} Properties\n * @typedef {import('./types.js').H} H\n * @typedef {import('./types.js').HWithoutProps} HWithoutProps\n * @typedef {import('./types.js').HWithProps} HWithProps\n * @typedef {import('./types.js').MdastNode} MdastNode\n * @typedef {import('./types.js').MdastRoot} MdastRoot\n */\n\n\n\n\n\n\n\n\n\n\n\nconst block = (0,unist_util_is__WEBPACK_IMPORTED_MODULE_2__.convert)(['heading', 'paragraph', 'root'])\n\n/**\n * Transform hast to mdast.\n *\n * @param {Node} tree\n *   Tree (hast).\n * @param {Options} [options]\n *   Configuration (optional).\n */\nfunction toMdast(tree, options = {}) {\n  /** @type {Record<string, Element>} */\n  const byId = {}\n  /** @type {MdastNode|MdastRoot} */\n  let mdast\n\n  /**\n   * @type {H}\n   */\n  const h = Object.assign(\n    /**\n     * @type {HWithProps & HWithoutProps}\n     */\n    (\n      /**\n       * @param {Node} node\n       * @param {string} type\n       * @param {Properties|string|Array<Node>} [props]\n       * @param {string|Array<Node>} [children]\n       */\n      (node, type, props, children) => {\n        /** @type {Properties|undefined} */\n        let properties\n\n        if (typeof props === 'string' || Array.isArray(props)) {\n          children = props\n          properties = {}\n        } else {\n          properties = props\n        }\n\n        /** @type {Node} */\n        // @ts-expect-error Assume valid `type` and `children`/`value`.\n        const result = {type, ...properties}\n\n        if (typeof children === 'string') {\n          // @ts-expect-error: Looks like a literal.\n          result.value = children\n        } else if (children) {\n          // @ts-expect-error: Looks like a parent.\n          result.children = children\n        }\n\n        if (node.position) {\n          result.position = node.position\n        }\n\n        return result\n      }\n    ),\n    {\n      nodeById: byId,\n      baseFound: false,\n      inTable: false,\n      wrapText: true,\n      /** @type {string|null} */\n      frozenBaseUrl: null,\n      qNesting: 0,\n      handlers: options.handlers\n        ? {..._handlers_index_js__WEBPACK_IMPORTED_MODULE_3__.handlers, ...options.handlers}\n        : _handlers_index_js__WEBPACK_IMPORTED_MODULE_3__.handlers,\n      document: options.document,\n      checked: options.checked || '[x]',\n      unchecked: options.unchecked || '[ ]',\n      quotes: options.quotes || ['\"']\n    }\n  )\n\n  ;(0,unist_util_visit__WEBPACK_IMPORTED_MODULE_4__.visit)(tree, 'element', (node) => {\n    const id =\n      node.properties &&\n      'id' in node.properties &&\n      String(node.properties.id).toUpperCase()\n\n    if (id && !_util_own_js__WEBPACK_IMPORTED_MODULE_5__.own.call(byId, id)) {\n      byId[id] = node\n    }\n  })\n\n  // @ts-expect-error: does return a transformer, that does accept any node.\n  ;(0,rehype_minify_whitespace__WEBPACK_IMPORTED_MODULE_6__[\"default\"])({newlines: options.newlines === true})(tree)\n\n  const result = (0,_one_js__WEBPACK_IMPORTED_MODULE_0__.one)(h, tree, undefined)\n\n  if (!result) {\n    mdast = {type: 'root', children: []}\n  } else if (Array.isArray(result)) {\n    mdast = {type: 'root', children: result}\n  } else {\n    mdast = result\n  }\n\n  (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_4__.visit)(mdast, 'text', ontext)\n\n  return mdast\n\n  /**\n   * Collapse text nodes, and fix whitespace.\n   * Most of this is taken care of by `rehype-minify-whitespace`, but\n   * we’re generating some whitespace too, and some nodes are in the end\n   * ignored.\n   * So clean up.\n   *\n   * @type {import('unist-util-visit/complex-types').BuildVisitor<MdastRoot, 'text'>}\n   */\n  function ontext(node, index, parent) {\n    /* c8 ignore next 3 */\n    if (index === null || !parent) {\n      return\n    }\n\n    const previous = parent.children[index - 1]\n\n    if (previous && previous.type === node.type) {\n      previous.value += node.value\n      parent.children.splice(index, 1)\n\n      if (previous.position && node.position) {\n        previous.position.end = node.position.end\n      }\n\n      // Iterate over the previous node again, to handle its total value.\n      return index - 1\n    }\n\n    node.value = node.value.replace(/[\\t ]*(\\r?\\n|\\r)[\\t ]*/, '$1')\n\n    // We don’t care about other phrasing nodes in between (e.g., `[ asd ]()`),\n    // as there the whitespace matters.\n    if (parent && block(parent)) {\n      if (!index) {\n        node.value = node.value.replace(/^[\\t ]+/, '')\n      }\n\n      if (index === parent.children.length - 1) {\n        node.value = node.value.replace(/[\\t ]+$/, '')\n      }\n    }\n\n    if (!node.value) {\n      parent.children.splice(index, 1)\n      return index\n    }\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/one.js":
/*!****************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/one.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   one: () => (/* binding */ one)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./all.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/all.js\");\n/* harmony import */ var _util_own_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/own.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/own.js\");\n/* harmony import */ var _util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/wrap-text.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('./types.js').H} H\n * @typedef {import('./types.js').Node} Node\n * @typedef {import('./types.js').Parent} Parent\n * @typedef {import('./types.js').Handle} Handle\n * @typedef {import('./types.js').MdastNode} MdastNode\n */\n\n\n\n\n\n/**\n * @param {H} h\n * @param {Node} node\n * @param {Parent|undefined} parent\n * @returns {MdastNode|Array<MdastNode>|void}\n */\nfunction one(h, node, parent) {\n  /** @type {Handle|undefined} */\n  let fn\n\n  if (node.type === 'element') {\n    if (node.properties && node.properties.dataMdast === 'ignore') {\n      return\n    }\n\n    if (_util_own_js__WEBPACK_IMPORTED_MODULE_0__.own.call(h.handlers, node.tagName)) {\n      fn = h.handlers[node.tagName]\n    }\n  } else if (_util_own_js__WEBPACK_IMPORTED_MODULE_0__.own.call(h.handlers, node.type)) {\n    fn = h.handlers[node.type]\n  }\n\n  if (typeof fn === 'function') {\n    return fn(h, node, parent)\n  }\n\n  return unknown(h, node)\n}\n\n/**\n * @type {Handle}\n * @param {Node} node\n */\nfunction unknown(h, node) {\n  // @ts-expect-error: Looks like a literal.\n  if (typeof node.value === 'string') {\n    // @ts-expect-error: Looks like a literal.\n    return h(node, 'text', (0,_util_wrap_text_js__WEBPACK_IMPORTED_MODULE_1__.wrapText)(h, node.value))\n  }\n\n  return (0,_all_js__WEBPACK_IMPORTED_MODULE_2__.all)(h, node)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/one.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/util/find-selected-options.js":
/*!***************************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/util/find-selected-options.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findSelectedOptions: () => (/* binding */ findSelectedOptions)\n/* harmony export */ });\n/* harmony import */ var hast_util_has_property__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-has-property */ \"(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-has-property/lib/index.js\");\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/./node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var _wrap_text_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./wrap-text.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap-text.js\");\n/**\n * @typedef {import('../types.js').H} H\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Child} Child\n * @typedef {import('../types.js').Properties} Properties\n */\n\n\n\n\n\n\nconst option = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('option')\n\n/**\n * @param {H} h\n * @param {Element} node\n * @param {Properties} [properties]\n * @returns {Array<[string, string|null]>}\n */\nfunction findSelectedOptions(h, node, properties) {\n  /** @type {Properties} */\n  // @ts-expect-error: `props` exist.\n  const props = properties || node.properties\n  let options = findOptions(node)\n  const size =\n    Math.min(Number.parseInt(String(props.size), 10), 0) ||\n    (props.multiple ? 4 : 1)\n  let index = -1\n  /** @type {Array<Element>} */\n  const selectedOptions = []\n  /** @type {Array<[string, string|null]>} */\n  const values = []\n\n  while (++index < options.length) {\n    if ((0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_1__.hasProperty)(options[index], 'selected')) {\n      selectedOptions.push(options[index])\n    }\n  }\n\n  const list = selectedOptions.length > 0 ? selectedOptions : options\n  options = list.slice(0, size)\n  index = -1\n\n  while (++index < options.length) {\n    const option = options[index]\n    const content = (0,_wrap_text_js__WEBPACK_IMPORTED_MODULE_2__.wrapText)(h, (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__.toText)(option))\n    /** @type {Properties} */\n    // @ts-expect-error: `props` exist.\n    const props = option.properties\n    const label = content || String(props.label || '')\n    const value = String(props.value || '') || content\n    values.push([value, label === value ? null : label])\n  }\n\n  return values\n}\n\n/**\n * @param {Parent} node\n */\nfunction findOptions(node) {\n  const children = node.children\n  let index = -1\n  /** @type {Array<Element>} */\n  let results = []\n  /** @type {Child} */\n  let child\n\n  while (++index < children.length) {\n    child = children[index]\n\n    // @ts-expect-error Looks like a parent.\n    if (Array.isArray(child.children)) {\n      // @ts-expect-error Looks like a parent.\n      results = results.concat(findOptions(child))\n    }\n\n    if (option(child) && !(0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_1__.hasProperty)(child, 'disabled')) {\n      results.push(child)\n    }\n  }\n\n  return results\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/util/find-selected-options.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/util/list-items-spread.js":
/*!***********************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/util/list-items-spread.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   listItemsSpread: () => (/* binding */ listItemsSpread)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').MdastListContent} MdastListContent\n */\n\n/**\n * @param {Array<MdastListContent>} children\n * @returns {boolean}\n */\nfunction listItemsSpread(children) {\n  let index = -1\n\n  if (children.length > 1) {\n    while (++index < children.length) {\n      if (children[index].spread) {\n        return true\n      }\n    }\n  }\n\n  return false\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi91dGlsL2xpc3QtaXRlbXMtc3ByZWFkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsd0NBQXdDO0FBQ3JEOztBQUVBO0FBQ0EsV0FBVyx5QkFBeUI7QUFDcEMsYUFBYTtBQUNiO0FBQ087QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL3V0aWwvbGlzdC1pdGVtcy1zcHJlYWQuanM/MTQ5NSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuTWRhc3RMaXN0Q29udGVudH0gTWRhc3RMaXN0Q29udGVudFxuICovXG5cbi8qKlxuICogQHBhcmFtIHtBcnJheTxNZGFzdExpc3RDb250ZW50Pn0gY2hpbGRyZW5cbiAqIEByZXR1cm5zIHtib29sZWFufVxuICovXG5leHBvcnQgZnVuY3Rpb24gbGlzdEl0ZW1zU3ByZWFkKGNoaWxkcmVuKSB7XG4gIGxldCBpbmRleCA9IC0xXG5cbiAgaWYgKGNoaWxkcmVuLmxlbmd0aCA+IDEpIHtcbiAgICB3aGlsZSAoKytpbmRleCA8IGNoaWxkcmVuLmxlbmd0aCkge1xuICAgICAgaWYgKGNoaWxkcmVuW2luZGV4XS5zcHJlYWQpIHtcbiAgICAgICAgcmV0dXJuIHRydWVcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICByZXR1cm4gZmFsc2Vcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/util/list-items-spread.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/util/own.js":
/*!*********************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/util/own.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   own: () => (/* binding */ own)\n/* harmony export */ });\nconst own = {}.hasOwnProperty\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi91dGlsL293bi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU8sY0FBYyIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi91dGlsL293bi5qcz9iZmEyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBvd24gPSB7fS5oYXNPd25Qcm9wZXJ0eVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/util/own.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/util/resolve.js":
/*!*************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/util/resolve.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolve: () => (/* binding */ resolve)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').H} H\n */\n\n/**\n * @param {H} h\n * @param {string|null|undefined} url\n * @returns {string}\n */\nfunction resolve(h, url) {\n  if (url === null || url === undefined) {\n    return ''\n  }\n\n  if (h.frozenBaseUrl) {\n    return String(new URL(url, h.frozenBaseUrl))\n  }\n\n  return url\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi91dGlsL3Jlc29sdmUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSx5QkFBeUI7QUFDdEM7O0FBRUE7QUFDQSxXQUFXLEdBQUc7QUFDZCxXQUFXLHVCQUF1QjtBQUNsQyxhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi91dGlsL3Jlc29sdmUuanM/MDc3NiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSH0gSFxuICovXG5cbi8qKlxuICogQHBhcmFtIHtIfSBoXG4gKiBAcGFyYW0ge3N0cmluZ3xudWxsfHVuZGVmaW5lZH0gdXJsXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gcmVzb2x2ZShoLCB1cmwpIHtcbiAgaWYgKHVybCA9PT0gbnVsbCB8fCB1cmwgPT09IHVuZGVmaW5lZCkge1xuICAgIHJldHVybiAnJ1xuICB9XG5cbiAgaWYgKGguZnJvemVuQmFzZVVybCkge1xuICAgIHJldHVybiBTdHJpbmcobmV3IFVSTCh1cmwsIGguZnJvemVuQmFzZVVybCkpXG4gIH1cblxuICByZXR1cm4gdXJsXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/util/resolve.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap-children.js":
/*!*******************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/util/wrap-children.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wrapChildren: () => (/* binding */ wrapChildren)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../all.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/all.js\");\n/* harmony import */ var _wrap_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./wrap.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap.js\");\n/**\n * @typedef {import('../types.js').H} H\n * @typedef {import('../types.js').Node} Node\n * @typedef {import('../types.js').MdastNode} MdastNode\n */\n\n\n\n\n/**\n * @param {H} h\n * @param {Node} node\n * @returns {Array<MdastNode>}\n */\nfunction wrapChildren(h, node) {\n  return (0,_wrap_js__WEBPACK_IMPORTED_MODULE_0__.wrap)((0,_all_js__WEBPACK_IMPORTED_MODULE_1__.all)(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi91dGlsL3dyYXAtY2hpbGRyZW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQSxhQUFhLHlCQUF5QjtBQUN0QyxhQUFhLDRCQUE0QjtBQUN6QyxhQUFhLGlDQUFpQztBQUM5Qzs7QUFFNkI7QUFDQzs7QUFFOUI7QUFDQSxXQUFXLEdBQUc7QUFDZCxXQUFXLE1BQU07QUFDakIsYUFBYTtBQUNiO0FBQ087QUFDUCxTQUFTLDhDQUFJLENBQUMsNENBQUc7QUFDakIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvdXRpbC93cmFwLWNoaWxkcmVuLmpzPzY0ZGQiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkh9IEhcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuTm9kZX0gTm9kZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5NZGFzdE5vZGV9IE1kYXN0Tm9kZVxuICovXG5cbmltcG9ydCB7YWxsfSBmcm9tICcuLi9hbGwuanMnXG5pbXBvcnQge3dyYXB9IGZyb20gJy4vd3JhcC5qcydcblxuLyoqXG4gKiBAcGFyYW0ge0h9IGhcbiAqIEBwYXJhbSB7Tm9kZX0gbm9kZVxuICogQHJldHVybnMge0FycmF5PE1kYXN0Tm9kZT59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB3cmFwQ2hpbGRyZW4oaCwgbm9kZSkge1xuICByZXR1cm4gd3JhcChhbGwoaCwgbm9kZSkpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap-children.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap-list-items.js":
/*!*********************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/util/wrap-list-items.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wrapListItems: () => (/* binding */ wrapListItems)\n/* harmony export */ });\n/* harmony import */ var _all_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../all.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/all.js\");\n/**\n * @typedef {import('../types.js').H} H\n * @typedef {import('../types.js').Child} Child\n * @typedef {import('../types.js').MdastListContent} MdastListContent\n */\n\n\n\n/**\n * @param {H} h\n * @param {Child} node\n * @returns {Array<MdastListContent>}\n */\nfunction wrapListItems(h, node) {\n  const children = (0,_all_js__WEBPACK_IMPORTED_MODULE_0__.all)(h, node)\n  let index = -1\n\n  while (++index < children.length) {\n    const child = children[index]\n    if (child.type !== 'listItem') {\n      children[index] = {\n        type: 'listItem',\n        spread: false,\n        checked: null,\n        // @ts-expect-error Assume `children[index]` is block content.\n        children: [child]\n      }\n    }\n  }\n\n  // @ts-expect-error Assume all `listItem`s\n  return children\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi91dGlsL3dyYXAtbGlzdC1pdGVtcy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSx5QkFBeUI7QUFDdEMsYUFBYSw2QkFBNkI7QUFDMUMsYUFBYSx3Q0FBd0M7QUFDckQ7O0FBRTZCOztBQUU3QjtBQUNBLFdBQVcsR0FBRztBQUNkLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQLG1CQUFtQiw0Q0FBRztBQUN0Qjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3QvbGliL3V0aWwvd3JhcC1saXN0LWl0ZW1zLmpzPzk5YzMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkh9IEhcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuQ2hpbGR9IENoaWxkXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLk1kYXN0TGlzdENvbnRlbnR9IE1kYXN0TGlzdENvbnRlbnRcbiAqL1xuXG5pbXBvcnQge2FsbH0gZnJvbSAnLi4vYWxsLmpzJ1xuXG4vKipcbiAqIEBwYXJhbSB7SH0gaFxuICogQHBhcmFtIHtDaGlsZH0gbm9kZVxuICogQHJldHVybnMge0FycmF5PE1kYXN0TGlzdENvbnRlbnQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gd3JhcExpc3RJdGVtcyhoLCBub2RlKSB7XG4gIGNvbnN0IGNoaWxkcmVuID0gYWxsKGgsIG5vZGUpXG4gIGxldCBpbmRleCA9IC0xXG5cbiAgd2hpbGUgKCsraW5kZXggPCBjaGlsZHJlbi5sZW5ndGgpIHtcbiAgICBjb25zdCBjaGlsZCA9IGNoaWxkcmVuW2luZGV4XVxuICAgIGlmIChjaGlsZC50eXBlICE9PSAnbGlzdEl0ZW0nKSB7XG4gICAgICBjaGlsZHJlbltpbmRleF0gPSB7XG4gICAgICAgIHR5cGU6ICdsaXN0SXRlbScsXG4gICAgICAgIHNwcmVhZDogZmFsc2UsXG4gICAgICAgIGNoZWNrZWQ6IG51bGwsXG4gICAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgQXNzdW1lIGBjaGlsZHJlbltpbmRleF1gIGlzIGJsb2NrIGNvbnRlbnQuXG4gICAgICAgIGNoaWxkcmVuOiBbY2hpbGRdXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLy8gQHRzLWV4cGVjdC1lcnJvciBBc3N1bWUgYWxsIGBsaXN0SXRlbWBzXG4gIHJldHVybiBjaGlsZHJlblxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap-list-items.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap-text.js":
/*!***************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/util/wrap-text.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wrapText: () => (/* binding */ wrapText)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').H} H\n */\n\n/**\n * @param {H} h\n * @param {string} value\n * @returns {string}\n */\nfunction wrapText(h, value) {\n  return h.wrapText ? value : value.replace(/\\r?\\n|\\r/g, ' ')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi91dGlsL3dyYXAtdGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLHlCQUF5QjtBQUN0Qzs7QUFFQTtBQUNBLFdBQVcsR0FBRztBQUNkLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1tZGFzdC9saWIvdXRpbC93cmFwLXRleHQuanM/N2M0OSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSH0gSFxuICovXG5cbi8qKlxuICogQHBhcmFtIHtIfSBoXG4gKiBAcGFyYW0ge3N0cmluZ30gdmFsdWVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB3cmFwVGV4dChoLCB2YWx1ZSkge1xuICByZXR1cm4gaC53cmFwVGV4dCA/IHZhbHVlIDogdmFsdWUucmVwbGFjZSgvXFxyP1xcbnxcXHIvZywgJyAnKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap-text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap.js":
/*!**********************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/util/wrap.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wrap: () => (/* binding */ wrap),\n/* harmony export */   wrapNeeded: () => (/* binding */ wrapNeeded)\n/* harmony export */ });\n/* harmony import */ var extend__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! extend */ \"(ssr)/./node_modules/extend/index.js\");\n/* harmony import */ var hast_util_phrasing__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-phrasing */ \"(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-phrasing/lib/index.js\");\n/* harmony import */ var mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mdast-util-phrasing */ \"(ssr)/./node_modules/mdast-util-phrasing/lib/index.js\");\n/**\n * @typedef {import('../types.js').MdastNode} MdastNode\n * @typedef {import('../types.js').MdastPhrasingContent} MdastPhrasingContent\n */\n\n\n\n\n\n/**\n * @param {Array<MdastNode>} nodes\n */\nfunction wrap(nodes) {\n  return runs(nodes, onphrasing)\n\n  /**\n   * @param {Array<MdastPhrasingContent>} nodes\n   * @returns {MdastNode|Array<MdastNode>}\n   */\n  function onphrasing(nodes) {\n    const head = nodes[0]\n\n    if (\n      nodes.length === 1 &&\n      head.type === 'text' &&\n      (head.value === ' ' || head.value === '\\n')\n    ) {\n      return []\n    }\n\n    return {type: 'paragraph', children: nodes}\n  }\n}\n\n/**\n * Check if there are non-phrasing mdast nodes returned.\n * This is needed if a fragment is given, which could just be a sentence, and\n * doesn’t need a wrapper paragraph.\n *\n * @param {Array<MdastNode>} nodes\n * @returns {boolean}\n */\nfunction wrapNeeded(nodes) {\n  let index = -1\n  /** @type {MdastNode} */\n  let node\n\n  while (++index < nodes.length) {\n    node = nodes[index]\n\n    if (!phrasing(node) || ('children' in node && wrapNeeded(node.children))) {\n      return true\n    }\n  }\n\n  return false\n}\n\n/**\n * Wrap all runs of mdast phrasing content in `paragraph` nodes.\n *\n * @param {Array<MdastNode>} nodes\n * @param {(nodes: Array<MdastPhrasingContent>) => MdastNode|Array<MdastNode>} onphrasing\n * @param {(node: MdastNode) => MdastNode} [onnonphrasing]\n */\nfunction runs(nodes, onphrasing, onnonphrasing) {\n  const nonphrasing = onnonphrasing || identity\n  /** @type {Array<MdastNode>} */\n  const flattened = flatten(nodes)\n  /** @type {Array<MdastNode>} */\n  let result = []\n  let index = -1\n  /** @type {Array<MdastPhrasingContent>|undefined} */\n  let queue\n  /** @type {MdastNode} */\n  let node\n\n  while (++index < flattened.length) {\n    node = flattened[index]\n\n    if (phrasing(node)) {\n      if (!queue) queue = []\n      queue.push(node)\n    } else {\n      if (queue) {\n        result = result.concat(onphrasing(queue))\n        queue = undefined\n      }\n\n      result = result.concat(nonphrasing(node))\n    }\n  }\n\n  if (queue) {\n    result = result.concat(onphrasing(queue))\n  }\n\n  return result\n}\n\n/**\n * Flatten a list of nodes.\n *\n * @param {Array<MdastNode>} nodes\n * @returns {Array<MdastNode>}\n */\nfunction flatten(nodes) {\n  /** @type {Array<MdastNode>} */\n  let flattened = []\n  let index = -1\n  /** @type {MdastNode} */\n  let node\n\n  while (++index < nodes.length) {\n    node = nodes[index]\n\n    // Straddling: some elements are *weird*.\n    // Namely: `map`, `ins`, `del`, and `a`, as they are hybrid elements.\n    // See: <https://html.spec.whatwg.org/#paragraphs>.\n    // Paragraphs are the weirdest of them all.\n    // See the straddling fixture for more info!\n    // `ins` is ignored in mdast, so we don’t need to worry about that.\n    // `map` maps to its content, so we don’t need to worry about that either.\n    // `del` maps to `delete` and `a` to `link`, so we do handle those.\n    // What we’ll do is split `node` over each of its children.\n    if (\n      (node.type === 'delete' || node.type === 'link') &&\n      wrapNeeded(node.children)\n    ) {\n      flattened = flattened.concat(split(node))\n    } else {\n      flattened.push(node)\n    }\n  }\n\n  return flattened\n}\n\n/**\n * @param {MdastNode} node\n * @returns {Array<MdastNode>}\n */\nfunction split(node) {\n  // @ts-expect-error Assume parent.\n  return runs(node.children, onphrasing, onnonphrasing)\n\n  /**\n   * Use `child`, add `parent` as its first child, put the original children\n   * into `parent`.\n   * If `child` is not a parent, `parent` will not be added.\n   *\n   * @param {MdastNode} child\n   * @returns {MdastNode}\n   */\n  function onnonphrasing(child) {\n    if ('children' in child && 'children' in node) {\n      const {children, ...rest} = node\n      return {\n        ...child,\n        // @ts-expect-error: assume matching parent & child.\n        children: [{...extend__WEBPACK_IMPORTED_MODULE_0__(true, {}, rest), children: child.children}]\n      }\n    }\n\n    return {...child}\n  }\n\n  /**\n   * Use `parent`, put the phrasing run inside it.\n   *\n   * @param {Array<MdastPhrasingContent>} nodes\n   * @returns {MdastNode}\n   */\n  function onphrasing(nodes) {\n    // @ts-expect-error: assume parent.\n    const {children, ...rest} = node\n    // @ts-expect-error: assume matching parent & child.\n    return {...extend__WEBPACK_IMPORTED_MODULE_0__(true, {}, rest), children: nodes}\n  }\n}\n\n/**\n * Check if an mdast node is phrasing.\n *\n * Also supports checking embedded hast fields.\n *\n * @param {MdastNode} node\n * @returns {node is MdastPhrasingContent}\n */\nfunction phrasing(node) {\n  return node.data && node.data.hName\n    ? (0,hast_util_phrasing__WEBPACK_IMPORTED_MODULE_1__.phrasing)({\n        type: 'element',\n        tagName: node.data.hName,\n        properties: {},\n        children: []\n      })\n    : (0,mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_2__.phrasing)(node)\n}\n\n/**\n * @template {unknown} T\n * @param {T} n\n * @returns {T}\n */\nfunction identity(n) {\n  return n\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-embedded/lib/index.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/node_modules/hast-util-embedded/lib/index.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   embedded: () => (/* binding */ embedded)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-is-element/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n */\n\n\n\n/**\n * Check if a node is a *embedded content*.\n *\n * @type {import('hast-util-is-element').AssertPredicate<Element & {tagName: 'audio' | 'canvas' | 'embed' | 'iframe' | 'img' | 'math' | 'object' | 'picture' | 'svg' | 'video'}>}\n * @param value\n *   Thing to check (typically `Node`).\n * @returns\n *   Whether `value` is an element considered embedded content.\n *\n *   The elements `audio`, `canvas`, `embed`, `iframe`, `img`, `math`,\n *   `object`, `picture`, `svg`, and `video` are embedded content.\n */\n// @ts-expect-error Sure, the assertion matches.\nconst embedded = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  'audio',\n  'canvas',\n  'embed',\n  'iframe',\n  'img',\n  'math',\n  'object',\n  'picture',\n  'svg',\n  'video'\n])\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtZW1iZWRkZWQvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLHdCQUF3QjtBQUNyQzs7QUFFbUQ7O0FBRW5EO0FBQ0E7QUFDQTtBQUNBLFVBQVUsMERBQTBELDJHQUEyRztBQUMvSztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTyxpQkFBaUIsb0VBQWM7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtZW1iZWRkZWQvbGliL2luZGV4LmpzPzI2NmIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuRWxlbWVudH0gRWxlbWVudFxuICovXG5cbmltcG9ydCB7Y29udmVydEVsZW1lbnR9IGZyb20gJ2hhc3QtdXRpbC1pcy1lbGVtZW50J1xuXG4vKipcbiAqIENoZWNrIGlmIGEgbm9kZSBpcyBhICplbWJlZGRlZCBjb250ZW50Ki5cbiAqXG4gKiBAdHlwZSB7aW1wb3J0KCdoYXN0LXV0aWwtaXMtZWxlbWVudCcpLkFzc2VydFByZWRpY2F0ZTxFbGVtZW50ICYge3RhZ05hbWU6ICdhdWRpbycgfCAnY2FudmFzJyB8ICdlbWJlZCcgfCAnaWZyYW1lJyB8ICdpbWcnIHwgJ21hdGgnIHwgJ29iamVjdCcgfCAncGljdHVyZScgfCAnc3ZnJyB8ICd2aWRlbyd9Pn1cbiAqIEBwYXJhbSB2YWx1ZVxuICogICBUaGluZyB0byBjaGVjayAodHlwaWNhbGx5IGBOb2RlYCkuXG4gKiBAcmV0dXJuc1xuICogICBXaGV0aGVyIGB2YWx1ZWAgaXMgYW4gZWxlbWVudCBjb25zaWRlcmVkIGVtYmVkZGVkIGNvbnRlbnQuXG4gKlxuICogICBUaGUgZWxlbWVudHMgYGF1ZGlvYCwgYGNhbnZhc2AsIGBlbWJlZGAsIGBpZnJhbWVgLCBgaW1nYCwgYG1hdGhgLFxuICogICBgb2JqZWN0YCwgYHBpY3R1cmVgLCBgc3ZnYCwgYW5kIGB2aWRlb2AgYXJlIGVtYmVkZGVkIGNvbnRlbnQuXG4gKi9cbi8vIEB0cy1leHBlY3QtZXJyb3IgU3VyZSwgdGhlIGFzc2VydGlvbiBtYXRjaGVzLlxuZXhwb3J0IGNvbnN0IGVtYmVkZGVkID0gY29udmVydEVsZW1lbnQoW1xuICAnYXVkaW8nLFxuICAnY2FudmFzJyxcbiAgJ2VtYmVkJyxcbiAgJ2lmcmFtZScsXG4gICdpbWcnLFxuICAnbWF0aCcsXG4gICdvYmplY3QnLFxuICAncGljdHVyZScsXG4gICdzdmcnLFxuICAndmlkZW8nXG5dKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-embedded/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-has-property/lib/index.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/node_modules/hast-util-has-property/lib/index.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasProperty: () => (/* binding */ hasProperty)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Content} Content\n */\n\n/**\n * @typedef {Root | Content} Node\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * Check if `node`is an element and has a `field` property.\n *\n * @param {unknown} node\n *   Thing to check (typically `Element`).\n * @param {unknown} field\n *   Field name to check (typically `string`).\n * @returns {boolean}\n *   Whether `node` is an element that has a `field` property.\n */\nfunction hasProperty(node, field) {\n  const value =\n    typeof field === 'string' &&\n    isNode(node) &&\n    node.type === 'element' &&\n    node.properties &&\n    own.call(node.properties, field) &&\n    node.properties[field]\n\n  return value !== null && value !== undefined && value !== false\n}\n\n/**\n * @param {unknown} value\n * @returns {value is Node}\n */\nfunction isNode(value) {\n  return Boolean(value && typeof value === 'object' && 'type' in value)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-has-property/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-is-body-ok-link/index.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/node_modules/hast-util-is-body-ok-link/index.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBodyOkLink: () => (/* binding */ isBodyOkLink)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var hast_util_has_property__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-has-property */ \"(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-has-property/lib/index.js\");\n/**\n * @fileoverview\n *   Check if a `link` element is “Body OK”.\n * @longdescription\n *   ## Use\n *\n *   ```js\n *   import {h} from 'hastscript'\n *   import {isBodyOkLink} from 'hast-util-is-body-ok-link'\n *\n *   isBodyOkLink(h('link', {itemProp: 'foo'})) //=> true\n *   isBodyOkLink(h('link', {rel: ['stylesheet'], href: 'index.css'})) //=> true\n *   isBodyOkLink(h('link', {rel: ['author'], href: 'index.css'})) //=> false\n *   ```\n *\n *   ## API\n *\n *   ### `isBodyOkLink(node)`\n *\n *   * Return `true` for `link` elements with an `itemProp`\n *   * Return `true` for `link` elements with a `rel` list where one or more\n *     entries are `pingback`, `prefetch`, or `stylesheet`.\n */\n\n\n\n\nconst list = new Set(['pingback', 'prefetch', 'stylesheet'])\n\n/**\n * @typedef {import('hast').Root} Root\n * @typedef {Root|Root['children'][number]} Node\n */\n\n/**\n * Check if a `link` element is “Body OK”.\n *\n * @param {Node} node\n * @returns {boolean}\n */\nfunction isBodyOkLink(node) {\n  if (!(0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.isElement)(node, 'link')) {\n    return false\n  }\n\n  if ((0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_1__.hasProperty)(node, 'itemProp')) {\n    return true\n  }\n\n  const props = node.properties || {}\n  const rel = props.rel || []\n  let index = -1\n\n  if (!Array.isArray(rel) || rel.length === 0) {\n    return false\n  }\n\n  while (++index < rel.length) {\n    if (!list.has(String(rel[index]))) {\n      return false\n    }\n  }\n\n  return true\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-is-body-ok-link/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-is-element/index.js":
/*!************************************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/node_modules/hast-util-is-element/index.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertElement: () => (/* binding */ convertElement),\n/* harmony export */   isElement: () => (/* binding */ isElement)\n/* harmony export */ });\n/**\n * @typedef {import('unist').Parent} Parent\n * @typedef {import('hast').Element} Element\n */\n\n/**\n * @typedef {null | undefined | string | TestFunctionAnything | Array<string | TestFunctionAnything>} Test\n *   Check for an arbitrary element, unaware of TypeScript inferral.\n *\n * @callback TestFunctionAnything\n *   Check if an element passes a test, unaware of TypeScript inferral.\n * @param {Element} element\n *   An element.\n * @param {number | null | undefined} [index]\n *   The element’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The element’s parent.\n * @returns {boolean | void}\n *   Whether this element passes the test.\n */\n\n/**\n * @template {Element} T\n *   Element type.\n * @typedef {T['tagName'] | TestFunctionPredicate<T> | Array<T['tagName'] | TestFunctionPredicate<T>>} PredicateTest\n *   Check for an element that can be inferred by TypeScript.\n */\n\n/**\n * Check if an element passes a certain node test.\n *\n * @template {Element} T\n *   Element type.\n * @callback TestFunctionPredicate\n *   Complex test function for an element that can be inferred by TypeScript.\n * @param {Element} element\n *   An element.\n * @param {number | null | undefined} [index]\n *   The element’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The element’s parent.\n * @returns {element is T}\n *   Whether this element passes the test.\n */\n\n/**\n * @callback AssertAnything\n *   Check that an arbitrary value is an element, unaware of TypeScript inferral.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is an element and passes a test.\n */\n\n/**\n * Check if a node is an element and passes a certain node test\n *\n * @template {Element} T\n *   Element type.\n * @callback AssertPredicate\n *   Check that an arbitrary value is a specific element, aware of TypeScript.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {node is T}\n *   Whether this is an element and passes a test.\n */\n\n/**\n * Check if `node` is an `Element` and whether it passes the given test.\n *\n * @param node\n *   Thing to check, typically `Node`.\n * @param test\n *   A check for a specific element.\n * @param index\n *   The node’s position in its parent.\n * @param parent\n *   The node’s parent.\n * @returns\n *   Whether `node` is an element and passes a test.\n */\nconst isElement =\n  /**\n   * @type {(\n   *   (() => false) &\n   *   (<T extends Element = Element>(node: unknown, test?: PredicateTest<T>, index?: number, parent?: Parent, context?: unknown) => node is T) &\n   *   ((node: unknown, test: Test, index?: number, parent?: Parent, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [node]\n     * @param {Test | undefined} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parent | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function (node, test, index, parent, context) {\n      const check = convertElement(test)\n\n      if (\n        index !== undefined &&\n        index !== null &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite index for child node')\n      }\n\n      if (\n        parent !== undefined &&\n        parent !== null &&\n        (!parent.type || !parent.children)\n      ) {\n        throw new Error('Expected parent node')\n      }\n\n      // @ts-expect-error Looks like a node.\n      if (!node || !node.type || typeof node.type !== 'string') {\n        return false\n      }\n\n      if (\n        (parent === undefined || parent === null) !==\n        (index === undefined || index === null)\n      ) {\n        throw new Error('Expected both parent and index')\n      }\n\n      return check.call(context, node, index, parent)\n    }\n  )\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param test\n *   *  When nullish, checks if `node` is an `Element`.\n *   *  When `string`, works like passing `(element) => element.tagName === test`.\n *   *  When `function` checks if function passed the element is true.\n *   *  When `array`, checks any one of the subtests pass.\n * @returns\n *   An assertion.\n */\nconst convertElement =\n  /**\n   * @type {(\n   *   (<T extends Element>(test: T['tagName'] | TestFunctionPredicate<T>) => AssertPredicate<T>) &\n   *   ((test?: Test) => AssertAnything)\n   * )}\n   */\n  (\n    /**\n     * @param {Test | null | undefined} [test]\n     * @returns {AssertAnything}\n     */\n    function (test) {\n      if (test === undefined || test === null) {\n        return element\n      }\n\n      if (typeof test === 'string') {\n        return tagNameFactory(test)\n      }\n\n      if (typeof test === 'object') {\n        return anyFactory(test)\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      throw new Error('Expected function, string, or array as test')\n    }\n  )\n\n/**\n * Handle multiple tests.\n *\n * @param {Array<string | TestFunctionAnything>} tests\n * @returns {AssertAnything}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<AssertAnything>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convertElement(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].call(this, ...parameters)) {\n        return true\n      }\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn a string into a test for an element with a certain tag name.\n *\n * @param {string} check\n * @returns {AssertAnything}\n */\nfunction tagNameFactory(check) {\n  return tagName\n\n  /**\n   * @param {unknown} node\n   * @returns {boolean}\n   */\n  function tagName(node) {\n    return element(node) && node.tagName === check\n  }\n}\n\n/**\n * Turn a custom test into a test for an element that passes that test.\n *\n * @param {TestFunctionAnything} check\n * @returns {AssertAnything}\n */\nfunction castFactory(check) {\n  return assertion\n\n  /**\n   * @this {unknown}\n   * @param {unknown} node\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function assertion(node, ...parameters) {\n    // @ts-expect-error: fine.\n    return element(node) && Boolean(check.call(this, node, ...parameters))\n  }\n}\n\n/**\n * Make sure something is an element.\n *\n * @param {unknown} node\n * @returns {node is Element}\n */\nfunction element(node) {\n  return Boolean(\n    node &&\n      typeof node === 'object' &&\n      // @ts-expect-error Looks like a node.\n      node.type === 'element' &&\n      // @ts-expect-error Looks like an element.\n      typeof node.tagName === 'string'\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-is-element/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-phrasing/lib/index.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/node_modules/hast-util-phrasing/lib/index.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   phrasing: () => (/* binding */ phrasing)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var hast_util_has_property__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-has-property */ \"(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-has-property/lib/index.js\");\n/* harmony import */ var hast_util_embedded__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-embedded */ \"(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-embedded/lib/index.js\");\n/* harmony import */ var hast_util_is_body_ok_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-is-body-ok-link */ \"(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-is-body-ok-link/index.js\");\n/**\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Content} Content\n */\n\n/**\n * @typedef {Root | Content} Node\n */\n\n\n\n\n\n\nconst basic = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  'a',\n  'abbr',\n  // `area` is in fact only phrasing if it is inside a `map` element.\n  // However, since `area`s are required to be inside a `map` element, and it’s\n  // a rather involved check, it’s ignored here for now.\n  'area',\n  'b',\n  'bdi',\n  'bdo',\n  'br',\n  'button',\n  'cite',\n  'code',\n  'data',\n  'datalist',\n  'del',\n  'dfn',\n  'em',\n  'i',\n  'input',\n  'ins',\n  'kbd',\n  'keygen',\n  'label',\n  'map',\n  'mark',\n  'meter',\n  'noscript',\n  'output',\n  'progress',\n  'q',\n  'ruby',\n  's',\n  'samp',\n  'script',\n  'select',\n  'small',\n  'span',\n  'strong',\n  'sub',\n  'sup',\n  'template',\n  'textarea',\n  'time',\n  'u',\n  'var',\n  'wbr'\n])\n\nconst meta = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('meta')\n\n/**\n * Check if the given value is *phrasing* content.\n *\n * @param {unknown} value\n *   Thing to check, typically `Node`.\n * @returns {boolean}\n *   Whether `value` is phrasing content.\n */\nfunction phrasing(value) {\n  return Boolean(\n    node(value) &&\n      (value.type === 'text' ||\n        basic(value) ||\n        (0,hast_util_embedded__WEBPACK_IMPORTED_MODULE_1__.embedded)(value) ||\n        (0,hast_util_is_body_ok_link__WEBPACK_IMPORTED_MODULE_2__.isBodyOkLink)(value) ||\n        (meta(value) && (0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_3__.hasProperty)(value, 'itemProp')))\n  )\n}\n\n/**\n * @param {unknown} value\n * @returns {value is Node}\n */\nfunction node(value) {\n  // @ts-expect-error: looks like an object.\n  return value && typeof value === 'object' && 'type' in value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/node_modules/hast-util-phrasing/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/node_modules/unist-util-is/lib/index.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/node_modules/unist-util-is/lib/index.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convert: () => (/* binding */ convert),\n/* harmony export */   is: () => (/* binding */ is)\n/* harmony export */ });\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n */\n\n/**\n * @typedef {Record<string, unknown>} Props\n * @typedef {null | undefined | string | Props | TestFunctionAnything | Array<string | Props | TestFunctionAnything>} Test\n *   Check for an arbitrary node, unaware of TypeScript inferral.\n *\n * @callback TestFunctionAnything\n *   Check if a node passes a test, unaware of TypeScript inferral.\n * @param {unknown} this\n *   The given context.\n * @param {Node} node\n *   A node.\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean | void}\n *   Whether this node passes the test.\n */\n\n/**\n * @template {Node} Kind\n *   Node type.\n * @typedef {Kind['type'] | Partial<Kind> | TestFunctionPredicate<Kind> | Array<Kind['type'] | Partial<Kind> | TestFunctionPredicate<Kind>>} PredicateTest\n *   Check for a node that can be inferred by TypeScript.\n */\n\n/**\n * Check if a node passes a certain test.\n *\n * @template {Node} Kind\n *   Node type.\n * @callback TestFunctionPredicate\n *   Complex test function for a node that can be inferred by TypeScript.\n * @param {Node} node\n *   A node.\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {node is Kind}\n *   Whether this node passes the test.\n */\n\n/**\n * @callback AssertAnything\n *   Check that an arbitrary value is a node, unaware of TypeScript inferral.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is a node and passes a test.\n */\n\n/**\n * Check if a node is a node and passes a certain node test.\n *\n * @template {Node} Kind\n *   Node type.\n * @callback AssertPredicate\n *   Check that an arbitrary value is a specific node, aware of TypeScript.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {node is Kind}\n *   Whether this is a node and passes a test.\n */\n\n/**\n * Check if `node` is a `Node` and whether it passes the given test.\n *\n * @param node\n *   Thing to check, typically `Node`.\n * @param test\n *   A check for a specific node.\n * @param index\n *   The node’s position in its parent.\n * @param parent\n *   The node’s parent.\n * @returns\n *   Whether `node` is a node and passes a test.\n */\nconst is =\n  /**\n   * @type {(\n   *   (() => false) &\n   *   (<Kind extends Node = Node>(node: unknown, test: PredicateTest<Kind>, index: number, parent: Parent, context?: unknown) => node is Kind) &\n   *   (<Kind extends Node = Node>(node: unknown, test: PredicateTest<Kind>, index?: null | undefined, parent?: null | undefined, context?: unknown) => node is Kind) &\n   *   ((node: unknown, test: Test, index: number, parent: Parent, context?: unknown) => boolean) &\n   *   ((node: unknown, test?: Test, index?: null | undefined, parent?: null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [node]\n     * @param {Test} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parent | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function is(node, test, index, parent, context) {\n      const check = convert(test)\n\n      if (\n        index !== undefined &&\n        index !== null &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite index')\n      }\n\n      if (\n        parent !== undefined &&\n        parent !== null &&\n        (!is(parent) || !parent.children)\n      ) {\n        throw new Error('Expected parent node')\n      }\n\n      if (\n        (parent === undefined || parent === null) !==\n        (index === undefined || index === null)\n      ) {\n        throw new Error('Expected both parent and index')\n      }\n\n      // @ts-expect-error Looks like a node.\n      return node && node.type && typeof node.type === 'string'\n        ? Boolean(check.call(context, node, index, parent))\n        : false\n    }\n  )\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param test\n *   *   when nullish, checks if `node` is a `Node`.\n *   *   when `string`, works like passing `(node) => node.type === test`.\n *   *   when `function` checks if function passed the node is true.\n *   *   when `object`, checks that all keys in test are in node, and that they have (strictly) equal values.\n *   *   when `array`, checks if any one of the subtests pass.\n * @returns\n *   An assertion.\n */\nconst convert =\n  /**\n   * @type {(\n   *   (<Kind extends Node>(test: PredicateTest<Kind>) => AssertPredicate<Kind>) &\n   *   ((test?: Test) => AssertAnything)\n   * )}\n   */\n  (\n    /**\n     * @param {Test} [test]\n     * @returns {AssertAnything}\n     */\n    function (test) {\n      if (test === undefined || test === null) {\n        return ok\n      }\n\n      if (typeof test === 'string') {\n        return typeFactory(test)\n      }\n\n      if (typeof test === 'object') {\n        return Array.isArray(test) ? anyFactory(test) : propsFactory(test)\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      throw new Error('Expected function, string, or object as test')\n    }\n  )\n\n/**\n * @param {Array<string | Props | TestFunctionAnything>} tests\n * @returns {AssertAnything}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<AssertAnything>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convert(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].call(this, ...parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn an object into a test for a node with a certain fields.\n *\n * @param {Props} check\n * @returns {AssertAnything}\n */\nfunction propsFactory(check) {\n  return castFactory(all)\n\n  /**\n   * @param {Node} node\n   * @returns {boolean}\n   */\n  function all(node) {\n    /** @type {string} */\n    let key\n\n    for (key in check) {\n      // @ts-expect-error: hush, it sure works as an index.\n      if (node[key] !== check[key]) return false\n    }\n\n    return true\n  }\n}\n\n/**\n * Turn a string into a test for a node with a certain type.\n *\n * @param {string} check\n * @returns {AssertAnything}\n */\nfunction typeFactory(check) {\n  return castFactory(type)\n\n  /**\n   * @param {Node} node\n   */\n  function type(node) {\n    return node && node.type === check\n  }\n}\n\n/**\n * Turn a custom test into a test for a node that passes that test.\n *\n * @param {TestFunctionAnything} check\n * @returns {AssertAnything}\n */\nfunction castFactory(check) {\n  return assertion\n\n  /**\n   * @this {unknown}\n   * @param {unknown} node\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function assertion(node, ...parameters) {\n    return Boolean(\n      node &&\n        typeof node === 'object' &&\n        'type' in node &&\n        // @ts-expect-error: fine.\n        Boolean(check.call(this, node, ...parameters))\n    )\n  }\n}\n\nfunction ok() {\n  return true\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L25vZGVfbW9kdWxlcy91bmlzdC11dGlsLWlzL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSxzQkFBc0I7QUFDbkMsYUFBYSx3QkFBd0I7QUFDckM7O0FBRUE7QUFDQSxhQUFhLHlCQUF5QjtBQUN0QyxhQUFhLHlHQUF5RztBQUN0SDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLFdBQVcsTUFBTTtBQUNqQjtBQUNBLFdBQVcsMkJBQTJCO0FBQ3RDO0FBQ0EsV0FBVywyQkFBMkI7QUFDdEM7QUFDQSxhQUFhO0FBQ2I7QUFDQTs7QUFFQTtBQUNBLGNBQWMsTUFBTTtBQUNwQjtBQUNBLGFBQWEsZ0lBQWdJO0FBQzdJO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsY0FBYyxNQUFNO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBLFdBQVcsTUFBTTtBQUNqQjtBQUNBLFdBQVcsMkJBQTJCO0FBQ3RDO0FBQ0EsV0FBVywyQkFBMkI7QUFDdEM7QUFDQSxhQUFhO0FBQ2I7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxXQUFXLDJCQUEyQjtBQUN0QztBQUNBLFdBQVcsMkJBQTJCO0FBQ3RDO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsY0FBYyxNQUFNO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLFdBQVcsMkJBQTJCO0FBQ3RDO0FBQ0EsV0FBVywyQkFBMkI7QUFDdEM7QUFDQSxhQUFhO0FBQ2I7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFNBQVM7QUFDeEIsZUFBZSxNQUFNO0FBQ3JCLGVBQWUsMkJBQTJCO0FBQzFDLGVBQWUsMkJBQTJCO0FBQzFDLGVBQWUsU0FBUztBQUN4QixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLE1BQU07QUFDckIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsV0FBVyw4Q0FBOEM7QUFDekQsYUFBYTtBQUNiO0FBQ0E7QUFDQSxhQUFhLHVCQUF1QjtBQUNwQztBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBLFlBQVk7QUFDWixhQUFhLGdCQUFnQjtBQUM3QixlQUFlO0FBQ2Y7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxhQUFhLE1BQU07QUFDbkIsZUFBZTtBQUNmO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkI7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQTtBQUNBOztBQUVBO0FBQ0EsYUFBYSxNQUFNO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxzQkFBc0I7QUFDakMsYUFBYTtBQUNiO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWixhQUFhLFNBQVM7QUFDdEIsYUFBYSxnQkFBZ0I7QUFDN0IsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tbWRhc3Qvbm9kZV9tb2R1bGVzL3VuaXN0LXV0aWwtaXMvbGliL2luZGV4LmpzP2YyYjIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCd1bmlzdCcpLk5vZGV9IE5vZGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ3VuaXN0JykuUGFyZW50fSBQYXJlbnRcbiAqL1xuXG4vKipcbiAqIEB0eXBlZGVmIHtSZWNvcmQ8c3RyaW5nLCB1bmtub3duPn0gUHJvcHNcbiAqIEB0eXBlZGVmIHtudWxsIHwgdW5kZWZpbmVkIHwgc3RyaW5nIHwgUHJvcHMgfCBUZXN0RnVuY3Rpb25Bbnl0aGluZyB8IEFycmF5PHN0cmluZyB8IFByb3BzIHwgVGVzdEZ1bmN0aW9uQW55dGhpbmc+fSBUZXN0XG4gKiAgIENoZWNrIGZvciBhbiBhcmJpdHJhcnkgbm9kZSwgdW5hd2FyZSBvZiBUeXBlU2NyaXB0IGluZmVycmFsLlxuICpcbiAqIEBjYWxsYmFjayBUZXN0RnVuY3Rpb25Bbnl0aGluZ1xuICogICBDaGVjayBpZiBhIG5vZGUgcGFzc2VzIGEgdGVzdCwgdW5hd2FyZSBvZiBUeXBlU2NyaXB0IGluZmVycmFsLlxuICogQHBhcmFtIHt1bmtub3dufSB0aGlzXG4gKiAgIFRoZSBnaXZlbiBjb250ZXh0LlxuICogQHBhcmFtIHtOb2RlfSBub2RlXG4gKiAgIEEgbm9kZS5cbiAqIEBwYXJhbSB7bnVtYmVyIHwgbnVsbCB8IHVuZGVmaW5lZH0gW2luZGV4XVxuICogICBUaGUgbm9kZeKAmXMgcG9zaXRpb24gaW4gaXRzIHBhcmVudC5cbiAqIEBwYXJhbSB7UGFyZW50IHwgbnVsbCB8IHVuZGVmaW5lZH0gW3BhcmVudF1cbiAqICAgVGhlIG5vZGXigJlzIHBhcmVudC5cbiAqIEByZXR1cm5zIHtib29sZWFuIHwgdm9pZH1cbiAqICAgV2hldGhlciB0aGlzIG5vZGUgcGFzc2VzIHRoZSB0ZXN0LlxuICovXG5cbi8qKlxuICogQHRlbXBsYXRlIHtOb2RlfSBLaW5kXG4gKiAgIE5vZGUgdHlwZS5cbiAqIEB0eXBlZGVmIHtLaW5kWyd0eXBlJ10gfCBQYXJ0aWFsPEtpbmQ+IHwgVGVzdEZ1bmN0aW9uUHJlZGljYXRlPEtpbmQ+IHwgQXJyYXk8S2luZFsndHlwZSddIHwgUGFydGlhbDxLaW5kPiB8IFRlc3RGdW5jdGlvblByZWRpY2F0ZTxLaW5kPj59IFByZWRpY2F0ZVRlc3RcbiAqICAgQ2hlY2sgZm9yIGEgbm9kZSB0aGF0IGNhbiBiZSBpbmZlcnJlZCBieSBUeXBlU2NyaXB0LlxuICovXG5cbi8qKlxuICogQ2hlY2sgaWYgYSBub2RlIHBhc3NlcyBhIGNlcnRhaW4gdGVzdC5cbiAqXG4gKiBAdGVtcGxhdGUge05vZGV9IEtpbmRcbiAqICAgTm9kZSB0eXBlLlxuICogQGNhbGxiYWNrIFRlc3RGdW5jdGlvblByZWRpY2F0ZVxuICogICBDb21wbGV4IHRlc3QgZnVuY3Rpb24gZm9yIGEgbm9kZSB0aGF0IGNhbiBiZSBpbmZlcnJlZCBieSBUeXBlU2NyaXB0LlxuICogQHBhcmFtIHtOb2RlfSBub2RlXG4gKiAgIEEgbm9kZS5cbiAqIEBwYXJhbSB7bnVtYmVyIHwgbnVsbCB8IHVuZGVmaW5lZH0gW2luZGV4XVxuICogICBUaGUgbm9kZeKAmXMgcG9zaXRpb24gaW4gaXRzIHBhcmVudC5cbiAqIEBwYXJhbSB7UGFyZW50IHwgbnVsbCB8IHVuZGVmaW5lZH0gW3BhcmVudF1cbiAqICAgVGhlIG5vZGXigJlzIHBhcmVudC5cbiAqIEByZXR1cm5zIHtub2RlIGlzIEtpbmR9XG4gKiAgIFdoZXRoZXIgdGhpcyBub2RlIHBhc3NlcyB0aGUgdGVzdC5cbiAqL1xuXG4vKipcbiAqIEBjYWxsYmFjayBBc3NlcnRBbnl0aGluZ1xuICogICBDaGVjayB0aGF0IGFuIGFyYml0cmFyeSB2YWx1ZSBpcyBhIG5vZGUsIHVuYXdhcmUgb2YgVHlwZVNjcmlwdCBpbmZlcnJhbC5cbiAqIEBwYXJhbSB7dW5rbm93bn0gW25vZGVdXG4gKiAgIEFueXRoaW5nICh0eXBpY2FsbHkgYSBub2RlKS5cbiAqIEBwYXJhbSB7bnVtYmVyIHwgbnVsbCB8IHVuZGVmaW5lZH0gW2luZGV4XVxuICogICBUaGUgbm9kZeKAmXMgcG9zaXRpb24gaW4gaXRzIHBhcmVudC5cbiAqIEBwYXJhbSB7UGFyZW50IHwgbnVsbCB8IHVuZGVmaW5lZH0gW3BhcmVudF1cbiAqICAgVGhlIG5vZGXigJlzIHBhcmVudC5cbiAqIEByZXR1cm5zIHtib29sZWFufVxuICogICBXaGV0aGVyIHRoaXMgaXMgYSBub2RlIGFuZCBwYXNzZXMgYSB0ZXN0LlxuICovXG5cbi8qKlxuICogQ2hlY2sgaWYgYSBub2RlIGlzIGEgbm9kZSBhbmQgcGFzc2VzIGEgY2VydGFpbiBub2RlIHRlc3QuXG4gKlxuICogQHRlbXBsYXRlIHtOb2RlfSBLaW5kXG4gKiAgIE5vZGUgdHlwZS5cbiAqIEBjYWxsYmFjayBBc3NlcnRQcmVkaWNhdGVcbiAqICAgQ2hlY2sgdGhhdCBhbiBhcmJpdHJhcnkgdmFsdWUgaXMgYSBzcGVjaWZpYyBub2RlLCBhd2FyZSBvZiBUeXBlU2NyaXB0LlxuICogQHBhcmFtIHt1bmtub3dufSBbbm9kZV1cbiAqICAgQW55dGhpbmcgKHR5cGljYWxseSBhIG5vZGUpLlxuICogQHBhcmFtIHtudW1iZXIgfCBudWxsIHwgdW5kZWZpbmVkfSBbaW5kZXhdXG4gKiAgIFRoZSBub2Rl4oCZcyBwb3NpdGlvbiBpbiBpdHMgcGFyZW50LlxuICogQHBhcmFtIHtQYXJlbnQgfCBudWxsIHwgdW5kZWZpbmVkfSBbcGFyZW50XVxuICogICBUaGUgbm9kZeKAmXMgcGFyZW50LlxuICogQHJldHVybnMge25vZGUgaXMgS2luZH1cbiAqICAgV2hldGhlciB0aGlzIGlzIGEgbm9kZSBhbmQgcGFzc2VzIGEgdGVzdC5cbiAqL1xuXG4vKipcbiAqIENoZWNrIGlmIGBub2RlYCBpcyBhIGBOb2RlYCBhbmQgd2hldGhlciBpdCBwYXNzZXMgdGhlIGdpdmVuIHRlc3QuXG4gKlxuICogQHBhcmFtIG5vZGVcbiAqICAgVGhpbmcgdG8gY2hlY2ssIHR5cGljYWxseSBgTm9kZWAuXG4gKiBAcGFyYW0gdGVzdFxuICogICBBIGNoZWNrIGZvciBhIHNwZWNpZmljIG5vZGUuXG4gKiBAcGFyYW0gaW5kZXhcbiAqICAgVGhlIG5vZGXigJlzIHBvc2l0aW9uIGluIGl0cyBwYXJlbnQuXG4gKiBAcGFyYW0gcGFyZW50XG4gKiAgIFRoZSBub2Rl4oCZcyBwYXJlbnQuXG4gKiBAcmV0dXJuc1xuICogICBXaGV0aGVyIGBub2RlYCBpcyBhIG5vZGUgYW5kIHBhc3NlcyBhIHRlc3QuXG4gKi9cbmV4cG9ydCBjb25zdCBpcyA9XG4gIC8qKlxuICAgKiBAdHlwZSB7KFxuICAgKiAgICgoKSA9PiBmYWxzZSkgJlxuICAgKiAgICg8S2luZCBleHRlbmRzIE5vZGUgPSBOb2RlPihub2RlOiB1bmtub3duLCB0ZXN0OiBQcmVkaWNhdGVUZXN0PEtpbmQ+LCBpbmRleDogbnVtYmVyLCBwYXJlbnQ6IFBhcmVudCwgY29udGV4dD86IHVua25vd24pID0+IG5vZGUgaXMgS2luZCkgJlxuICAgKiAgICg8S2luZCBleHRlbmRzIE5vZGUgPSBOb2RlPihub2RlOiB1bmtub3duLCB0ZXN0OiBQcmVkaWNhdGVUZXN0PEtpbmQ+LCBpbmRleD86IG51bGwgfCB1bmRlZmluZWQsIHBhcmVudD86IG51bGwgfCB1bmRlZmluZWQsIGNvbnRleHQ/OiB1bmtub3duKSA9PiBub2RlIGlzIEtpbmQpICZcbiAgICogICAoKG5vZGU6IHVua25vd24sIHRlc3Q6IFRlc3QsIGluZGV4OiBudW1iZXIsIHBhcmVudDogUGFyZW50LCBjb250ZXh0PzogdW5rbm93bikgPT4gYm9vbGVhbikgJlxuICAgKiAgICgobm9kZTogdW5rbm93biwgdGVzdD86IFRlc3QsIGluZGV4PzogbnVsbCB8IHVuZGVmaW5lZCwgcGFyZW50PzogbnVsbCB8IHVuZGVmaW5lZCwgY29udGV4dD86IHVua25vd24pID0+IGJvb2xlYW4pXG4gICAqICl9XG4gICAqL1xuICAoXG4gICAgLyoqXG4gICAgICogQHBhcmFtIHt1bmtub3dufSBbbm9kZV1cbiAgICAgKiBAcGFyYW0ge1Rlc3R9IFt0ZXN0XVxuICAgICAqIEBwYXJhbSB7bnVtYmVyIHwgbnVsbCB8IHVuZGVmaW5lZH0gW2luZGV4XVxuICAgICAqIEBwYXJhbSB7UGFyZW50IHwgbnVsbCB8IHVuZGVmaW5lZH0gW3BhcmVudF1cbiAgICAgKiBAcGFyYW0ge3Vua25vd259IFtjb250ZXh0XVxuICAgICAqIEByZXR1cm5zIHtib29sZWFufVxuICAgICAqL1xuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBtYXgtcGFyYW1zXG4gICAgZnVuY3Rpb24gaXMobm9kZSwgdGVzdCwgaW5kZXgsIHBhcmVudCwgY29udGV4dCkge1xuICAgICAgY29uc3QgY2hlY2sgPSBjb252ZXJ0KHRlc3QpXG5cbiAgICAgIGlmIChcbiAgICAgICAgaW5kZXggIT09IHVuZGVmaW5lZCAmJlxuICAgICAgICBpbmRleCAhPT0gbnVsbCAmJlxuICAgICAgICAodHlwZW9mIGluZGV4ICE9PSAnbnVtYmVyJyB8fFxuICAgICAgICAgIGluZGV4IDwgMCB8fFxuICAgICAgICAgIGluZGV4ID09PSBOdW1iZXIuUE9TSVRJVkVfSU5GSU5JVFkpXG4gICAgICApIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdFeHBlY3RlZCBwb3NpdGl2ZSBmaW5pdGUgaW5kZXgnKVxuICAgICAgfVxuXG4gICAgICBpZiAoXG4gICAgICAgIHBhcmVudCAhPT0gdW5kZWZpbmVkICYmXG4gICAgICAgIHBhcmVudCAhPT0gbnVsbCAmJlxuICAgICAgICAoIWlzKHBhcmVudCkgfHwgIXBhcmVudC5jaGlsZHJlbilcbiAgICAgICkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0V4cGVjdGVkIHBhcmVudCBub2RlJylcbiAgICAgIH1cblxuICAgICAgaWYgKFxuICAgICAgICAocGFyZW50ID09PSB1bmRlZmluZWQgfHwgcGFyZW50ID09PSBudWxsKSAhPT1cbiAgICAgICAgKGluZGV4ID09PSB1bmRlZmluZWQgfHwgaW5kZXggPT09IG51bGwpXG4gICAgICApIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdFeHBlY3RlZCBib3RoIHBhcmVudCBhbmQgaW5kZXgnKVxuICAgICAgfVxuXG4gICAgICAvLyBAdHMtZXhwZWN0LWVycm9yIExvb2tzIGxpa2UgYSBub2RlLlxuICAgICAgcmV0dXJuIG5vZGUgJiYgbm9kZS50eXBlICYmIHR5cGVvZiBub2RlLnR5cGUgPT09ICdzdHJpbmcnXG4gICAgICAgID8gQm9vbGVhbihjaGVjay5jYWxsKGNvbnRleHQsIG5vZGUsIGluZGV4LCBwYXJlbnQpKVxuICAgICAgICA6IGZhbHNlXG4gICAgfVxuICApXG5cbi8qKlxuICogR2VuZXJhdGUgYW4gYXNzZXJ0aW9uIGZyb20gYSB0ZXN0LlxuICpcbiAqIFVzZWZ1bCBpZiB5b3XigJlyZSBnb2luZyB0byB0ZXN0IG1hbnkgbm9kZXMsIGZvciBleGFtcGxlIHdoZW4gY3JlYXRpbmcgYVxuICogdXRpbGl0eSB3aGVyZSBzb21ldGhpbmcgZWxzZSBwYXNzZXMgYSBjb21wYXRpYmxlIHRlc3QuXG4gKlxuICogVGhlIGNyZWF0ZWQgZnVuY3Rpb24gaXMgYSBiaXQgZmFzdGVyIGJlY2F1c2UgaXQgZXhwZWN0cyB2YWxpZCBpbnB1dCBvbmx5OlxuICogYSBgbm9kZWAsIGBpbmRleGAsIGFuZCBgcGFyZW50YC5cbiAqXG4gKiBAcGFyYW0gdGVzdFxuICogICAqICAgd2hlbiBudWxsaXNoLCBjaGVja3MgaWYgYG5vZGVgIGlzIGEgYE5vZGVgLlxuICogICAqICAgd2hlbiBgc3RyaW5nYCwgd29ya3MgbGlrZSBwYXNzaW5nIGAobm9kZSkgPT4gbm9kZS50eXBlID09PSB0ZXN0YC5cbiAqICAgKiAgIHdoZW4gYGZ1bmN0aW9uYCBjaGVja3MgaWYgZnVuY3Rpb24gcGFzc2VkIHRoZSBub2RlIGlzIHRydWUuXG4gKiAgICogICB3aGVuIGBvYmplY3RgLCBjaGVja3MgdGhhdCBhbGwga2V5cyBpbiB0ZXN0IGFyZSBpbiBub2RlLCBhbmQgdGhhdCB0aGV5IGhhdmUgKHN0cmljdGx5KSBlcXVhbCB2YWx1ZXMuXG4gKiAgICogICB3aGVuIGBhcnJheWAsIGNoZWNrcyBpZiBhbnkgb25lIG9mIHRoZSBzdWJ0ZXN0cyBwYXNzLlxuICogQHJldHVybnNcbiAqICAgQW4gYXNzZXJ0aW9uLlxuICovXG5leHBvcnQgY29uc3QgY29udmVydCA9XG4gIC8qKlxuICAgKiBAdHlwZSB7KFxuICAgKiAgICg8S2luZCBleHRlbmRzIE5vZGU+KHRlc3Q6IFByZWRpY2F0ZVRlc3Q8S2luZD4pID0+IEFzc2VydFByZWRpY2F0ZTxLaW5kPikgJlxuICAgKiAgICgodGVzdD86IFRlc3QpID0+IEFzc2VydEFueXRoaW5nKVxuICAgKiApfVxuICAgKi9cbiAgKFxuICAgIC8qKlxuICAgICAqIEBwYXJhbSB7VGVzdH0gW3Rlc3RdXG4gICAgICogQHJldHVybnMge0Fzc2VydEFueXRoaW5nfVxuICAgICAqL1xuICAgIGZ1bmN0aW9uICh0ZXN0KSB7XG4gICAgICBpZiAodGVzdCA9PT0gdW5kZWZpbmVkIHx8IHRlc3QgPT09IG51bGwpIHtcbiAgICAgICAgcmV0dXJuIG9rXG4gICAgICB9XG5cbiAgICAgIGlmICh0eXBlb2YgdGVzdCA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgcmV0dXJuIHR5cGVGYWN0b3J5KHRlc3QpXG4gICAgICB9XG5cbiAgICAgIGlmICh0eXBlb2YgdGVzdCA9PT0gJ29iamVjdCcpIHtcbiAgICAgICAgcmV0dXJuIEFycmF5LmlzQXJyYXkodGVzdCkgPyBhbnlGYWN0b3J5KHRlc3QpIDogcHJvcHNGYWN0b3J5KHRlc3QpXG4gICAgICB9XG5cbiAgICAgIGlmICh0eXBlb2YgdGVzdCA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICByZXR1cm4gY2FzdEZhY3RvcnkodGVzdClcbiAgICAgIH1cblxuICAgICAgdGhyb3cgbmV3IEVycm9yKCdFeHBlY3RlZCBmdW5jdGlvbiwgc3RyaW5nLCBvciBvYmplY3QgYXMgdGVzdCcpXG4gICAgfVxuICApXG5cbi8qKlxuICogQHBhcmFtIHtBcnJheTxzdHJpbmcgfCBQcm9wcyB8IFRlc3RGdW5jdGlvbkFueXRoaW5nPn0gdGVzdHNcbiAqIEByZXR1cm5zIHtBc3NlcnRBbnl0aGluZ31cbiAqL1xuZnVuY3Rpb24gYW55RmFjdG9yeSh0ZXN0cykge1xuICAvKiogQHR5cGUge0FycmF5PEFzc2VydEFueXRoaW5nPn0gKi9cbiAgY29uc3QgY2hlY2tzID0gW11cbiAgbGV0IGluZGV4ID0gLTFcblxuICB3aGlsZSAoKytpbmRleCA8IHRlc3RzLmxlbmd0aCkge1xuICAgIGNoZWNrc1tpbmRleF0gPSBjb252ZXJ0KHRlc3RzW2luZGV4XSlcbiAgfVxuXG4gIHJldHVybiBjYXN0RmFjdG9yeShhbnkpXG5cbiAgLyoqXG4gICAqIEB0aGlzIHt1bmtub3dufVxuICAgKiBAcGFyYW0ge0FycmF5PHVua25vd24+fSBwYXJhbWV0ZXJzXG4gICAqIEByZXR1cm5zIHtib29sZWFufVxuICAgKi9cbiAgZnVuY3Rpb24gYW55KC4uLnBhcmFtZXRlcnMpIHtcbiAgICBsZXQgaW5kZXggPSAtMVxuXG4gICAgd2hpbGUgKCsraW5kZXggPCBjaGVja3MubGVuZ3RoKSB7XG4gICAgICBpZiAoY2hlY2tzW2luZGV4XS5jYWxsKHRoaXMsIC4uLnBhcmFtZXRlcnMpKSByZXR1cm4gdHJ1ZVxuICAgIH1cblxuICAgIHJldHVybiBmYWxzZVxuICB9XG59XG5cbi8qKlxuICogVHVybiBhbiBvYmplY3QgaW50byBhIHRlc3QgZm9yIGEgbm9kZSB3aXRoIGEgY2VydGFpbiBmaWVsZHMuXG4gKlxuICogQHBhcmFtIHtQcm9wc30gY2hlY2tcbiAqIEByZXR1cm5zIHtBc3NlcnRBbnl0aGluZ31cbiAqL1xuZnVuY3Rpb24gcHJvcHNGYWN0b3J5KGNoZWNrKSB7XG4gIHJldHVybiBjYXN0RmFjdG9yeShhbGwpXG5cbiAgLyoqXG4gICAqIEBwYXJhbSB7Tm9kZX0gbm9kZVxuICAgKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAgICovXG4gIGZ1bmN0aW9uIGFsbChub2RlKSB7XG4gICAgLyoqIEB0eXBlIHtzdHJpbmd9ICovXG4gICAgbGV0IGtleVxuXG4gICAgZm9yIChrZXkgaW4gY2hlY2spIHtcbiAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3I6IGh1c2gsIGl0IHN1cmUgd29ya3MgYXMgYW4gaW5kZXguXG4gICAgICBpZiAobm9kZVtrZXldICE9PSBjaGVja1trZXldKSByZXR1cm4gZmFsc2VcbiAgICB9XG5cbiAgICByZXR1cm4gdHJ1ZVxuICB9XG59XG5cbi8qKlxuICogVHVybiBhIHN0cmluZyBpbnRvIGEgdGVzdCBmb3IgYSBub2RlIHdpdGggYSBjZXJ0YWluIHR5cGUuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IGNoZWNrXG4gKiBAcmV0dXJucyB7QXNzZXJ0QW55dGhpbmd9XG4gKi9cbmZ1bmN0aW9uIHR5cGVGYWN0b3J5KGNoZWNrKSB7XG4gIHJldHVybiBjYXN0RmFjdG9yeSh0eXBlKVxuXG4gIC8qKlxuICAgKiBAcGFyYW0ge05vZGV9IG5vZGVcbiAgICovXG4gIGZ1bmN0aW9uIHR5cGUobm9kZSkge1xuICAgIHJldHVybiBub2RlICYmIG5vZGUudHlwZSA9PT0gY2hlY2tcbiAgfVxufVxuXG4vKipcbiAqIFR1cm4gYSBjdXN0b20gdGVzdCBpbnRvIGEgdGVzdCBmb3IgYSBub2RlIHRoYXQgcGFzc2VzIHRoYXQgdGVzdC5cbiAqXG4gKiBAcGFyYW0ge1Rlc3RGdW5jdGlvbkFueXRoaW5nfSBjaGVja1xuICogQHJldHVybnMge0Fzc2VydEFueXRoaW5nfVxuICovXG5mdW5jdGlvbiBjYXN0RmFjdG9yeShjaGVjaykge1xuICByZXR1cm4gYXNzZXJ0aW9uXG5cbiAgLyoqXG4gICAqIEB0aGlzIHt1bmtub3dufVxuICAgKiBAcGFyYW0ge3Vua25vd259IG5vZGVcbiAgICogQHBhcmFtIHtBcnJheTx1bmtub3duPn0gcGFyYW1ldGVyc1xuICAgKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAgICovXG4gIGZ1bmN0aW9uIGFzc2VydGlvbihub2RlLCAuLi5wYXJhbWV0ZXJzKSB7XG4gICAgcmV0dXJuIEJvb2xlYW4oXG4gICAgICBub2RlICYmXG4gICAgICAgIHR5cGVvZiBub2RlID09PSAnb2JqZWN0JyAmJlxuICAgICAgICAndHlwZScgaW4gbm9kZSAmJlxuICAgICAgICAvLyBAdHMtZXhwZWN0LWVycm9yOiBmaW5lLlxuICAgICAgICBCb29sZWFuKGNoZWNrLmNhbGwodGhpcywgbm9kZSwgLi4ucGFyYW1ldGVycykpXG4gICAgKVxuICB9XG59XG5cbmZ1bmN0aW9uIG9rKCkge1xuICByZXR1cm4gdHJ1ZVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/node_modules/unist-util-is/lib/index.js\n");

/***/ })

};
;