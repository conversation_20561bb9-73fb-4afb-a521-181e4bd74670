"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-gfm-task-list-item";
exports.ids = ["vendor-chunks/mdast-util-gfm-task-list-item"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-gfm-task-list-item/lib/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-gfm-task-list-item/lib/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTaskListItemFromMarkdown: () => (/* binding */ gfmTaskListItemFromMarkdown),\n/* harmony export */   gfmTaskListItemToMarkdown: () => (/* binding */ gfmTaskListItemToMarkdown)\n/* harmony export */ });\n/* harmony import */ var mdast_util_to_markdown_lib_handle_list_item_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mdast-util-to-markdown/lib/handle/list-item.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js\");\n/* harmony import */ var mdast_util_to_markdown_lib_util_track_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-to-markdown/lib/util/track.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/track.js\");\n/**\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('mdast').Parent} Parent\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast-util-from-markdown').CompileContext} CompileContext\n * @typedef {import('mdast-util-from-markdown').Extension} FromMarkdownExtension\n * @typedef {import('mdast-util-from-markdown').Handle} FromMarkdownHandle\n * @typedef {import('mdast-util-to-markdown').Options} ToMarkdownExtension\n * @typedef {import('mdast-util-to-markdown').Handle} ToMarkdownHandle\n */\n\n/**\n * @typedef {Extract<Root | Content, Parent>} Parents\n */\n\n\n\n\n// To do: next major: rename `context` -> `state`, `safeOptions` -> `info`, use\n// `track` from `state`.\n// To do: next major: replace exports with functions.\n// To do: next major: use `defaulthandlers.listItem`.\n\n/**\n * Extension for `mdast-util-from-markdown` to enable GFM task list items.\n *\n * @type {FromMarkdownExtension}\n */\nconst gfmTaskListItemFromMarkdown = {\n  exit: {\n    taskListCheckValueChecked: exitCheck,\n    taskListCheckValueUnchecked: exitCheck,\n    paragraph: exitParagraphWithTaskListItem\n  }\n}\n\n/**\n * Extension for `mdast-util-to-markdown` to enable GFM task list items.\n *\n * @type {ToMarkdownExtension}\n */\nconst gfmTaskListItemToMarkdown = {\n  unsafe: [{atBreak: true, character: '-', after: '[:|-]'}],\n  handlers: {listItem: listItemWithTaskListItem}\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitCheck(token) {\n  const node = /** @type {ListItem} */ (this.stack[this.stack.length - 2])\n  // We’re always in a paragraph, in a list item.\n  node.checked = token.type === 'taskListCheckValueChecked'\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitParagraphWithTaskListItem(token) {\n  const parent = /** @type {Parents} */ (this.stack[this.stack.length - 2])\n\n  if (\n    parent &&\n    parent.type === 'listItem' &&\n    typeof parent.checked === 'boolean'\n  ) {\n    const node = /** @type {Paragraph} */ (this.stack[this.stack.length - 1])\n    const head = node.children[0]\n\n    if (head && head.type === 'text') {\n      const siblings = parent.children\n      let index = -1\n      /** @type {Paragraph | undefined} */\n      let firstParaghraph\n\n      while (++index < siblings.length) {\n        const sibling = siblings[index]\n        if (sibling.type === 'paragraph') {\n          firstParaghraph = sibling\n          break\n        }\n      }\n\n      if (firstParaghraph === node) {\n        // Must start with a space or a tab.\n        head.value = head.value.slice(1)\n\n        if (head.value.length === 0) {\n          node.children.shift()\n        } else if (\n          node.position &&\n          head.position &&\n          typeof head.position.start.offset === 'number'\n        ) {\n          head.position.start.column++\n          head.position.start.offset++\n          node.position.start = Object.assign({}, head.position.start)\n        }\n      }\n    }\n  }\n\n  this.exit(token)\n}\n\n/**\n * @type {ToMarkdownHandle}\n * @param {ListItem} node\n */\nfunction listItemWithTaskListItem(node, parent, context, safeOptions) {\n  const head = node.children[0]\n  const checkable =\n    typeof node.checked === 'boolean' && head && head.type === 'paragraph'\n  const checkbox = '[' + (node.checked ? 'x' : ' ') + '] '\n  const tracker = (0,mdast_util_to_markdown_lib_util_track_js__WEBPACK_IMPORTED_MODULE_0__.track)(safeOptions)\n\n  if (checkable) {\n    tracker.move(checkbox)\n  }\n\n  let value = (0,mdast_util_to_markdown_lib_handle_list_item_js__WEBPACK_IMPORTED_MODULE_1__.listItem)(node, parent, context, {\n    ...safeOptions,\n    ...tracker.current()\n  })\n\n  if (checkable) {\n    value = value.replace(/^(?:[*+-]|\\d+\\.)([\\r\\n]| {1,3})/, check)\n  }\n\n  return value\n\n  /**\n   * @param {string} $0\n   * @returns {string}\n   */\n  function check($0) {\n    return $0 + checkbox\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm-task-list-item/lib/index.js\n");

/***/ })

};
;