"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-popper";
exports.ids = ["vendor-chunks/react-popper"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-popper/lib/esm/Manager.js":
/*!******************************************************!*\
  !*** ./node_modules/react-popper/lib/esm/Manager.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Manager: () => (/* binding */ Manager),\n/* harmony export */   ManagerReferenceNodeContext: () => (/* binding */ ManagerReferenceNodeContext),\n/* harmony export */   ManagerReferenceNodeSetterContext: () => (/* binding */ ManagerReferenceNodeSetterContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar ManagerReferenceNodeContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext();\nvar ManagerReferenceNodeSetterContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext();\nfunction Manager(_ref) {\n  var children = _ref.children;\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0__.useState(null),\n      referenceNode = _React$useState[0],\n      setReferenceNode = _React$useState[1];\n\n  var hasUnmounted = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    return function () {\n      hasUnmounted.current = true;\n    };\n  }, []);\n  var handleSetReferenceNode = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (node) {\n    if (!hasUnmounted.current) {\n      setReferenceNode(node);\n    }\n  }, []);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(ManagerReferenceNodeContext.Provider, {\n    value: referenceNode\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(ManagerReferenceNodeSetterContext.Provider, {\n    value: handleSetReferenceNode\n  }, children));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcG9wcGVyL2xpYi9lc20vTWFuYWdlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUErQjtBQUN4QixrQ0FBa0MsZ0RBQW1CO0FBQ3JELHdDQUF3QyxnREFBbUI7QUFDM0Q7QUFDUDs7QUFFQSx3QkFBd0IsMkNBQWM7QUFDdEM7QUFDQTs7QUFFQSxxQkFBcUIseUNBQVk7QUFDakMsRUFBRSw0Q0FBZTtBQUNqQjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsK0JBQStCLDhDQUFpQjtBQUNoRDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsc0JBQXNCLGdEQUFtQjtBQUN6QztBQUNBLEdBQUcsZUFBZSxnREFBbUI7QUFDckM7QUFDQSxHQUFHO0FBQ0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3JlYWN0LXBvcHBlci9saWIvZXNtL01hbmFnZXIuanM/NGZhZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5leHBvcnQgdmFyIE1hbmFnZXJSZWZlcmVuY2VOb2RlQ29udGV4dCA9IFJlYWN0LmNyZWF0ZUNvbnRleHQoKTtcbmV4cG9ydCB2YXIgTWFuYWdlclJlZmVyZW5jZU5vZGVTZXR0ZXJDb250ZXh0ID0gUmVhY3QuY3JlYXRlQ29udGV4dCgpO1xuZXhwb3J0IGZ1bmN0aW9uIE1hbmFnZXIoX3JlZikge1xuICB2YXIgY2hpbGRyZW4gPSBfcmVmLmNoaWxkcmVuO1xuXG4gIHZhciBfUmVhY3QkdXNlU3RhdGUgPSBSZWFjdC51c2VTdGF0ZShudWxsKSxcbiAgICAgIHJlZmVyZW5jZU5vZGUgPSBfUmVhY3QkdXNlU3RhdGVbMF0sXG4gICAgICBzZXRSZWZlcmVuY2VOb2RlID0gX1JlYWN0JHVzZVN0YXRlWzFdO1xuXG4gIHZhciBoYXNVbm1vdW50ZWQgPSBSZWFjdC51c2VSZWYoZmFsc2UpO1xuICBSZWFjdC51c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiBmdW5jdGlvbiAoKSB7XG4gICAgICBoYXNVbm1vdW50ZWQuY3VycmVudCA9IHRydWU7XG4gICAgfTtcbiAgfSwgW10pO1xuICB2YXIgaGFuZGxlU2V0UmVmZXJlbmNlTm9kZSA9IFJlYWN0LnVzZUNhbGxiYWNrKGZ1bmN0aW9uIChub2RlKSB7XG4gICAgaWYgKCFoYXNVbm1vdW50ZWQuY3VycmVudCkge1xuICAgICAgc2V0UmVmZXJlbmNlTm9kZShub2RlKTtcbiAgICB9XG4gIH0sIFtdKTtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KE1hbmFnZXJSZWZlcmVuY2VOb2RlQ29udGV4dC5Qcm92aWRlciwge1xuICAgIHZhbHVlOiByZWZlcmVuY2VOb2RlXG4gIH0sIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KE1hbmFnZXJSZWZlcmVuY2VOb2RlU2V0dGVyQ29udGV4dC5Qcm92aWRlciwge1xuICAgIHZhbHVlOiBoYW5kbGVTZXRSZWZlcmVuY2VOb2RlXG4gIH0sIGNoaWxkcmVuKSk7XG59Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-popper/lib/esm/Manager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-popper/lib/esm/Popper.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-popper/lib/esm/Popper.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Popper: () => (/* binding */ Popper)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Manager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Manager */ \"(ssr)/./node_modules/react-popper/lib/esm/Manager.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-popper/lib/esm/utils.js\");\n/* harmony import */ var _usePopper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./usePopper */ \"(ssr)/./node_modules/react-popper/lib/esm/usePopper.js\");\n\n\n\n\n\nvar NOOP = function NOOP() {\n  return void 0;\n};\n\nvar NOOP_PROMISE = function NOOP_PROMISE() {\n  return Promise.resolve(null);\n};\n\nvar EMPTY_MODIFIERS = [];\nfunction Popper(_ref) {\n  var _ref$placement = _ref.placement,\n      placement = _ref$placement === void 0 ? 'bottom' : _ref$placement,\n      _ref$strategy = _ref.strategy,\n      strategy = _ref$strategy === void 0 ? 'absolute' : _ref$strategy,\n      _ref$modifiers = _ref.modifiers,\n      modifiers = _ref$modifiers === void 0 ? EMPTY_MODIFIERS : _ref$modifiers,\n      referenceElement = _ref.referenceElement,\n      onFirstUpdate = _ref.onFirstUpdate,\n      innerRef = _ref.innerRef,\n      children = _ref.children;\n  var referenceNode = react__WEBPACK_IMPORTED_MODULE_0__.useContext(_Manager__WEBPACK_IMPORTED_MODULE_1__.ManagerReferenceNodeContext);\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0__.useState(null),\n      popperElement = _React$useState[0],\n      setPopperElement = _React$useState[1];\n\n  var _React$useState2 = react__WEBPACK_IMPORTED_MODULE_0__.useState(null),\n      arrowElement = _React$useState2[0],\n      setArrowElement = _React$useState2[1];\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    (0,_utils__WEBPACK_IMPORTED_MODULE_2__.setRef)(innerRef, popperElement);\n  }, [innerRef, popperElement]);\n  var options = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(function () {\n    return {\n      placement: placement,\n      strategy: strategy,\n      onFirstUpdate: onFirstUpdate,\n      modifiers: [].concat(modifiers, [{\n        name: 'arrow',\n        enabled: arrowElement != null,\n        options: {\n          element: arrowElement\n        }\n      }])\n    };\n  }, [placement, strategy, onFirstUpdate, modifiers, arrowElement]);\n\n  var _usePopper = (0,_usePopper__WEBPACK_IMPORTED_MODULE_3__.usePopper)(referenceElement || referenceNode, popperElement, options),\n      state = _usePopper.state,\n      styles = _usePopper.styles,\n      forceUpdate = _usePopper.forceUpdate,\n      update = _usePopper.update;\n\n  var childrenProps = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(function () {\n    return {\n      ref: setPopperElement,\n      style: styles.popper,\n      placement: state ? state.placement : placement,\n      hasPopperEscaped: state && state.modifiersData.hide ? state.modifiersData.hide.hasPopperEscaped : null,\n      isReferenceHidden: state && state.modifiersData.hide ? state.modifiersData.hide.isReferenceHidden : null,\n      arrowProps: {\n        style: styles.arrow,\n        ref: setArrowElement\n      },\n      forceUpdate: forceUpdate || NOOP,\n      update: update || NOOP_PROMISE\n    };\n  }, [setPopperElement, setArrowElement, placement, state, styles, update, forceUpdate]);\n  return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.unwrapArray)(children)(childrenProps);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-popper/lib/esm/Popper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-popper/lib/esm/Reference.js":
/*!********************************************************!*\
  !*** ./node_modules/react-popper/lib/esm/Reference.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Reference: () => (/* binding */ Reference)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(warning__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Manager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Manager */ \"(ssr)/./node_modules/react-popper/lib/esm/Manager.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-popper/lib/esm/utils.js\");\n\n\n\n\nfunction Reference(_ref) {\n  var children = _ref.children,\n      innerRef = _ref.innerRef;\n  var setReferenceNode = react__WEBPACK_IMPORTED_MODULE_0__.useContext(_Manager__WEBPACK_IMPORTED_MODULE_2__.ManagerReferenceNodeSetterContext);\n  var refHandler = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (node) {\n    (0,_utils__WEBPACK_IMPORTED_MODULE_3__.setRef)(innerRef, node);\n    (0,_utils__WEBPACK_IMPORTED_MODULE_3__.safeInvoke)(setReferenceNode, node);\n  }, [innerRef, setReferenceNode]); // ran on unmount\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    return function () {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_3__.setRef)(innerRef, null);\n    };\n  }, []);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    warning__WEBPACK_IMPORTED_MODULE_1___default()(Boolean(setReferenceNode), '`Reference` should not be used outside of a `Manager` component.');\n  }, [setReferenceNode]);\n  return (0,_utils__WEBPACK_IMPORTED_MODULE_3__.unwrapArray)(children)({\n    ref: refHandler\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcG9wcGVyL2xpYi9lc20vUmVmZXJlbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBK0I7QUFDRDtBQUNnQztBQUNKO0FBQ25EO0FBQ1A7QUFDQTtBQUNBLHlCQUF5Qiw2Q0FBZ0IsQ0FBQyx1RUFBaUM7QUFDM0UsbUJBQW1CLDhDQUFpQjtBQUNwQyxJQUFJLDhDQUFNO0FBQ1YsSUFBSSxrREFBVTtBQUNkLEdBQUcsaUNBQWlDO0FBQ3BDOztBQUVBLEVBQUUsNENBQWU7QUFDakI7QUFDQSxhQUFhLDhDQUFNO0FBQ25CO0FBQ0EsR0FBRztBQUNILEVBQUUsNENBQWU7QUFDakIsSUFBSSw4Q0FBTztBQUNYLEdBQUc7QUFDSCxTQUFTLG1EQUFXO0FBQ3BCO0FBQ0EsR0FBRztBQUNIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yZWFjdC1wb3BwZXIvbGliL2VzbS9SZWZlcmVuY2UuanM/MjA4ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgd2FybmluZyBmcm9tICd3YXJuaW5nJztcbmltcG9ydCB7IE1hbmFnZXJSZWZlcmVuY2VOb2RlU2V0dGVyQ29udGV4dCB9IGZyb20gJy4vTWFuYWdlcic7XG5pbXBvcnQgeyBzYWZlSW52b2tlLCB1bndyYXBBcnJheSwgc2V0UmVmIH0gZnJvbSAnLi91dGlscyc7XG5leHBvcnQgZnVuY3Rpb24gUmVmZXJlbmNlKF9yZWYpIHtcbiAgdmFyIGNoaWxkcmVuID0gX3JlZi5jaGlsZHJlbixcbiAgICAgIGlubmVyUmVmID0gX3JlZi5pbm5lclJlZjtcbiAgdmFyIHNldFJlZmVyZW5jZU5vZGUgPSBSZWFjdC51c2VDb250ZXh0KE1hbmFnZXJSZWZlcmVuY2VOb2RlU2V0dGVyQ29udGV4dCk7XG4gIHZhciByZWZIYW5kbGVyID0gUmVhY3QudXNlQ2FsbGJhY2soZnVuY3Rpb24gKG5vZGUpIHtcbiAgICBzZXRSZWYoaW5uZXJSZWYsIG5vZGUpO1xuICAgIHNhZmVJbnZva2Uoc2V0UmVmZXJlbmNlTm9kZSwgbm9kZSk7XG4gIH0sIFtpbm5lclJlZiwgc2V0UmVmZXJlbmNlTm9kZV0pOyAvLyByYW4gb24gdW5tb3VudFxuICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvZXhoYXVzdGl2ZS1kZXBzXG5cbiAgUmVhY3QudXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgcmV0dXJuIHNldFJlZihpbm5lclJlZiwgbnVsbCk7XG4gICAgfTtcbiAgfSwgW10pO1xuICBSZWFjdC51c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIHdhcm5pbmcoQm9vbGVhbihzZXRSZWZlcmVuY2VOb2RlKSwgJ2BSZWZlcmVuY2VgIHNob3VsZCBub3QgYmUgdXNlZCBvdXRzaWRlIG9mIGEgYE1hbmFnZXJgIGNvbXBvbmVudC4nKTtcbiAgfSwgW3NldFJlZmVyZW5jZU5vZGVdKTtcbiAgcmV0dXJuIHVud3JhcEFycmF5KGNoaWxkcmVuKSh7XG4gICAgcmVmOiByZWZIYW5kbGVyXG4gIH0pO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-popper/lib/esm/Reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-popper/lib/esm/usePopper.js":
/*!********************************************************!*\
  !*** ./node_modules/react-popper/lib/esm/usePopper.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePopper: () => (/* binding */ usePopper)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _popperjs_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @popperjs/core */ \"(ssr)/./node_modules/@popperjs/core/lib/popper.js\");\n/* harmony import */ var react_fast_compare__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-fast-compare */ \"(ssr)/./node_modules/react-fast-compare/index.js\");\n/* harmony import */ var react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_fast_compare__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-popper/lib/esm/utils.js\");\n\n\n\n\n\nvar EMPTY_MODIFIERS = [];\nvar usePopper = function usePopper(referenceElement, popperElement, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var prevOptions = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  var optionsWithDefaults = {\n    onFirstUpdate: options.onFirstUpdate,\n    placement: options.placement || 'bottom',\n    strategy: options.strategy || 'absolute',\n    modifiers: options.modifiers || EMPTY_MODIFIERS\n  };\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    styles: {\n      popper: {\n        position: optionsWithDefaults.strategy,\n        left: '0',\n        top: '0'\n      },\n      arrow: {\n        position: 'absolute'\n      }\n    },\n    attributes: {}\n  }),\n      state = _React$useState[0],\n      setState = _React$useState[1];\n\n  var updateStateModifier = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(function () {\n    return {\n      name: 'updateState',\n      enabled: true,\n      phase: 'write',\n      fn: function fn(_ref) {\n        var state = _ref.state;\n        var elements = Object.keys(state.elements);\n        react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(function () {\n          setState({\n            styles: (0,_utils__WEBPACK_IMPORTED_MODULE_3__.fromEntries)(elements.map(function (element) {\n              return [element, state.styles[element] || {}];\n            })),\n            attributes: (0,_utils__WEBPACK_IMPORTED_MODULE_3__.fromEntries)(elements.map(function (element) {\n              return [element, state.attributes[element]];\n            }))\n          });\n        });\n      },\n      requires: ['computeStyles']\n    };\n  }, []);\n  var popperOptions = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(function () {\n    var newOptions = {\n      onFirstUpdate: optionsWithDefaults.onFirstUpdate,\n      placement: optionsWithDefaults.placement,\n      strategy: optionsWithDefaults.strategy,\n      modifiers: [].concat(optionsWithDefaults.modifiers, [updateStateModifier, {\n        name: 'applyStyles',\n        enabled: false\n      }])\n    };\n\n    if (react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(prevOptions.current, newOptions)) {\n      return prevOptions.current || newOptions;\n    } else {\n      prevOptions.current = newOptions;\n      return newOptions;\n    }\n  }, [optionsWithDefaults.onFirstUpdate, optionsWithDefaults.placement, optionsWithDefaults.strategy, optionsWithDefaults.modifiers, updateStateModifier]);\n  var popperInstanceRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n  (0,_utils__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(function () {\n    if (popperInstanceRef.current) {\n      popperInstanceRef.current.setOptions(popperOptions);\n    }\n  }, [popperOptions]);\n  (0,_utils__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(function () {\n    if (referenceElement == null || popperElement == null) {\n      return;\n    }\n\n    var createPopper = options.createPopper || _popperjs_core__WEBPACK_IMPORTED_MODULE_4__.createPopper;\n    var popperInstance = createPopper(referenceElement, popperElement, popperOptions);\n    popperInstanceRef.current = popperInstance;\n    return function () {\n      popperInstance.destroy();\n      popperInstanceRef.current = null;\n    };\n  }, [referenceElement, popperElement, options.createPopper]);\n  return {\n    state: popperInstanceRef.current ? popperInstanceRef.current.state : null,\n    styles: state.styles,\n    attributes: state.attributes,\n    update: popperInstanceRef.current ? popperInstanceRef.current.update : null,\n    forceUpdate: popperInstanceRef.current ? popperInstanceRef.current.forceUpdate : null\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-popper/lib/esm/usePopper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-popper/lib/esm/utils.js":
/*!****************************************************!*\
  !*** ./node_modules/react-popper/lib/esm/utils.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromEntries: () => (/* binding */ fromEntries),\n/* harmony export */   safeInvoke: () => (/* binding */ safeInvoke),\n/* harmony export */   setRef: () => (/* binding */ setRef),\n/* harmony export */   unwrapArray: () => (/* binding */ unwrapArray),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* binding */ useIsomorphicLayoutEffect)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n/**\n * Takes an argument and if it's an array, returns the first item in the array,\n * otherwise returns the argument. Used for Preact compatibility.\n */\nvar unwrapArray = function unwrapArray(arg) {\n  return Array.isArray(arg) ? arg[0] : arg;\n};\n/**\n * Takes a maybe-undefined function and arbitrary args and invokes the function\n * only if it is defined.\n */\n\nvar safeInvoke = function safeInvoke(fn) {\n  if (typeof fn === 'function') {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    return fn.apply(void 0, args);\n  }\n};\n/**\n * Sets a ref using either a ref callback or a ref object\n */\n\nvar setRef = function setRef(ref, node) {\n  // if its a function call it\n  if (typeof ref === 'function') {\n    return safeInvoke(ref, node);\n  } // otherwise we should treat it as a ref object\n  else if (ref != null) {\n      ref.current = node;\n    }\n};\n/**\n * Simple ponyfill for Object.fromEntries\n */\n\nvar fromEntries = function fromEntries(entries) {\n  return entries.reduce(function (acc, _ref) {\n    var key = _ref[0],\n        value = _ref[1];\n    acc[key] = value;\n    return acc;\n  }, {});\n};\n/**\n * Small wrapper around `useLayoutEffect` to get rid of the warning on SSR envs\n */\n\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' && window.document && window.document.createElement ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-popper/lib/esm/utils.js\n");

/***/ })

};
;