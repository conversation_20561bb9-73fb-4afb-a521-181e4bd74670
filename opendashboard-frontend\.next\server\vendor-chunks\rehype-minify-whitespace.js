"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-minify-whitespace";
exports.ids = ["vendor-chunks/rehype-minify-whitespace"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype-minify-whitespace/block.js":
/*!********************************************************!*\
  !*** ./node_modules/rehype-minify-whitespace/block.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blocks: () => (/* binding */ blocks)\n/* harmony export */ });\n// See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\nconst blocks = [\n  'address', // Flow content.\n  'article', // Sections and headings.\n  'aside', // Sections and headings.\n  'blockquote', // Flow content.\n  'body', // Page.\n  'br', // Contribute whitespace intrinsically.\n  'caption', // Similar to block.\n  'center', // Flow content, legacy.\n  'col', // Similar to block.\n  'colgroup', // Similar to block.\n  'dd', // Lists.\n  'dialog', // Flow content.\n  'dir', // Lists, legacy.\n  'div', // Flow content.\n  'dl', // Lists.\n  'dt', // Lists.\n  'figcaption', // Flow content.\n  'figure', // Flow content.\n  'footer', // Flow content.\n  'form', // Flow content.\n  'h1', // Sections and headings.\n  'h2', // Sections and headings.\n  'h3', // Sections and headings.\n  'h4', // Sections and headings.\n  'h5', // Sections and headings.\n  'h6', // Sections and headings.\n  'head', // Page.\n  'header', // Flow content.\n  'hgroup', // Sections and headings.\n  'hr', // Flow content.\n  'html', // Page.\n  'legend', // Flow content.\n  'li', // Block-like.\n  'li', // Similar to block.\n  'listing', // Flow content, legacy\n  'main', // Flow content.\n  'menu', // Lists.\n  'nav', // Sections and headings.\n  'ol', // Lists.\n  'optgroup', // Similar to block.\n  'option', // Similar to block.\n  'p', // Flow content.\n  'plaintext', // Flow content, legacy\n  'pre', // Flow content.\n  'section', // Sections and headings.\n  'summary', // Similar to block.\n  'table', // Similar to block.\n  'tbody', // Similar to block.\n  'td', // Block-like.\n  'td', // Similar to block.\n  'tfoot', // Similar to block.\n  'th', // Block-like.\n  'th', // Similar to block.\n  'thead', // Similar to block.\n  'tr', // Similar to block.\n  'ul', // Lists.\n  'wbr', // Contribute whitespace intrinsically.\n  'xmp' // Flow content, legacy\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-minify-whitespace/block.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rehype-minify-whitespace/content.js":
/*!**********************************************************!*\
  !*** ./node_modules/rehype-minify-whitespace/content.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   content: () => (/* binding */ content)\n/* harmony export */ });\nconst content = [\n  // Form.\n  'button',\n  'input',\n  'select',\n  'textarea'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVoeXBlLW1pbmlmeS13aGl0ZXNwYWNlL2NvbnRlbnQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yZWh5cGUtbWluaWZ5LXdoaXRlc3BhY2UvY29udGVudC5qcz81ZTQ2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBjb250ZW50ID0gW1xuICAvLyBGb3JtLlxuICAnYnV0dG9uJyxcbiAgJ2lucHV0JyxcbiAgJ3NlbGVjdCcsXG4gICd0ZXh0YXJlYSdcbl1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-minify-whitespace/content.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rehype-minify-whitespace/index.js":
/*!********************************************************!*\
  !*** ./node_modules/rehype-minify-whitespace/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rehypeMinifyWhitespace)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/./node_modules/rehype-minify-whitespace/node_modules/hast-util-is-element/index.js\");\n/* harmony import */ var hast_util_embedded__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-embedded */ \"(ssr)/./node_modules/rehype-minify-whitespace/node_modules/hast-util-embedded/lib/index.js\");\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-is */ \"(ssr)/./node_modules/rehype-minify-whitespace/node_modules/unist-util-is/lib/index.js\");\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/./node_modules/rehype-minify-whitespace/node_modules/hast-util-whitespace/index.js\");\n/* harmony import */ var _block_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./block.js */ \"(ssr)/./node_modules/rehype-minify-whitespace/block.js\");\n/* harmony import */ var _content_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./content.js */ \"(ssr)/./node_modules/rehype-minify-whitespace/content.js\");\n/* harmony import */ var _skippable_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./skippable.js */ \"(ssr)/./node_modules/rehype-minify-whitespace/skippable.js\");\n/**\n * rehype plugin to minify whitespace between elements.\n *\n * ## What is this?\n *\n * This package is a plugin that can minify the whitespace between elements.\n *\n * ## When should I use this?\n *\n * You can use this plugin when you want to improve the size of HTML documents.\n *\n * ## API\n *\n * ### `unified().use(rehypeMinifyWhitespace[, options])`\n *\n * Minify whitespace.\n *\n * ##### `options`\n *\n * Configuration (optional).\n *\n * ##### `options.newlines`\n *\n * Whether to collapse runs of whitespace that include line endings to one\n * line ending (`boolean`, default: `false`).\n * The default is to collapse everything to one space.\n *\n * @example\n *   <h1>Heading</h1>\n *   <p><strong>This</strong> and <em>that</em></p>\n */\n\n/**\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {Root|Root['children'][number]} Node\n *\n * @typedef Options\n * @property {boolean} [newlines=false]\n *   If `newlines: true`, collapses whitespace containing newlines to `'\\n'`\n *   instead of `' '`.\n *   The default is to collapse to a single space.\n *\n * @typedef {'pre'|'nowrap'|'pre-wrap'|'normal'} Whitespace\n *\n * @typedef Context\n * @property {ReturnType<collapseFactory>} collapse\n * @property {Whitespace} whitespace\n * @property {boolean} [before]\n * @property {boolean} [after]\n *\n * @typedef Result\n * @property {boolean} remove\n * @property {boolean} ignore\n * @property {boolean} stripAtStart\n */\n\n\n\n\n\n\n\n\n\nconst ignorableNode = (0,unist_util_is__WEBPACK_IMPORTED_MODULE_0__.convert)(['doctype', 'comment'])\n\n/**\n * Minify whitespace.\n *\n * @type {import('unified').Plugin<[Options?]|Array<void>, Root>}\n */\nfunction rehypeMinifyWhitespace(options = {}) {\n  const collapse = collapseFactory(\n    options.newlines ? replaceNewlines : replaceWhitespace\n  )\n\n  return (tree) => {\n    minify(tree, {collapse, whitespace: 'normal'})\n  }\n}\n\n/**\n * @param {Node} node\n * @param {Context} context\n * @returns {Result}\n */\nfunction minify(node, context) {\n  if ('children' in node) {\n    const settings = Object.assign({}, context)\n\n    if (node.type === 'root' || blocklike(node)) {\n      settings.before = true\n      settings.after = true\n    }\n\n    settings.whitespace = inferWhiteSpace(node, context)\n\n    return all(node, settings)\n  }\n\n  if (node.type === 'text') {\n    if (context.whitespace === 'normal') {\n      return minifyText(node, context)\n    }\n\n    // Naïve collapse, but no trimming:\n    if (context.whitespace === 'nowrap') {\n      node.value = context.collapse(node.value)\n    }\n\n    // The `pre-wrap` or `pre` whitespace settings are neither collapsed nor\n    // trimmed.\n  }\n\n  return {remove: false, ignore: ignorableNode(node), stripAtStart: false}\n}\n\n/**\n * @param {Text} node\n * @param {Context} context\n * @returns {Result}\n */\nfunction minifyText(node, context) {\n  const value = context.collapse(node.value)\n  const result = {remove: false, ignore: false, stripAtStart: false}\n  let start = 0\n  let end = value.length\n\n  if (context.before && removable(value.charAt(0))) {\n    start++\n  }\n\n  if (start !== end && removable(value.charAt(end - 1))) {\n    if (context.after) {\n      end--\n    } else {\n      result.stripAtStart = true\n    }\n  }\n\n  if (start === end) {\n    result.remove = true\n  } else {\n    node.value = value.slice(start, end)\n  }\n\n  return result\n}\n\n/**\n * @param {Root|Element} parent\n * @param {Context} context\n * @returns {Result}\n */\nfunction all(parent, context) {\n  let before = context.before\n  const after = context.after\n  const children = parent.children\n  let length = children.length\n  let index = -1\n\n  while (++index < length) {\n    const result = minify(\n      children[index],\n      Object.assign({}, context, {\n        before,\n        after: collapsableAfter(children, index, after)\n      })\n    )\n\n    if (result.remove) {\n      children.splice(index, 1)\n      index--\n      length--\n    } else if (!result.ignore) {\n      before = result.stripAtStart\n    }\n\n    // If this element, such as a `<select>` or `<img>`, contributes content\n    // somehow, allow whitespace again.\n    if (content(children[index])) {\n      before = false\n    }\n  }\n\n  return {remove: false, ignore: false, stripAtStart: Boolean(before || after)}\n}\n\n/**\n * @param {Array<Node>} nodes\n * @param {number} index\n * @param {boolean|undefined} [after]\n * @returns {boolean|undefined}\n */\nfunction collapsableAfter(nodes, index, after) {\n  while (++index < nodes.length) {\n    const node = nodes[index]\n    let result = inferBoundary(node)\n\n    if (result === undefined && 'children' in node && !skippable(node)) {\n      result = collapsableAfter(node.children, -1)\n    }\n\n    if (typeof result === 'boolean') {\n      return result\n    }\n  }\n\n  return after\n}\n\n/**\n * Infer two types of boundaries:\n *\n * 1. `true` — boundary for which whitespace around it does not contribute\n *    anything\n * 2. `false` — boundary for which whitespace around it *does* contribute\n *\n * No result (`undefined`) is returned if it is unknown.\n *\n * @param {Node} node\n * @returns {boolean|undefined}\n */\nfunction inferBoundary(node) {\n  if (node.type === 'element') {\n    if (content(node)) {\n      return false\n    }\n\n    if (blocklike(node)) {\n      return true\n    }\n\n    // Unknown: either depends on siblings if embedded or metadata, or on\n    // children.\n  } else if (node.type === 'text') {\n    if (!(0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_1__.whitespace)(node)) {\n      return false\n    }\n  } else if (!ignorableNode(node)) {\n    return false\n  }\n}\n\n/**\n * Infer whether a node is skippable.\n *\n * @param {Node} node\n * @returns {boolean}\n */\nfunction content(node) {\n  return (0,hast_util_embedded__WEBPACK_IMPORTED_MODULE_2__.embedded)(node) || (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__.isElement)(node, _content_js__WEBPACK_IMPORTED_MODULE_4__.content)\n}\n\n/**\n * See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\n *\n * @param {Element} node\n * @returns {boolean}\n */\nfunction blocklike(node) {\n  return (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__.isElement)(node, _block_js__WEBPACK_IMPORTED_MODULE_5__.blocks)\n}\n\n/**\n * @param {Element|Root} node\n * @returns {boolean}\n */\nfunction skippable(node) {\n  return (\n    Boolean(\n      'properties' in node && node.properties && node.properties.hidden\n    ) ||\n    ignorableNode(node) ||\n    (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__.isElement)(node, _skippable_js__WEBPACK_IMPORTED_MODULE_6__.skippable)\n  )\n}\n\n/**\n * @param {string} character\n * @returns {boolean}\n */\nfunction removable(character) {\n  return character === ' ' || character === '\\n'\n}\n\n/**\n * @param {string} value\n * @returns {string}\n */\nfunction replaceNewlines(value) {\n  const match = /\\r?\\n|\\r/.exec(value)\n  return match ? match[0] : ' '\n}\n\n/**\n * @returns {string}\n */\nfunction replaceWhitespace() {\n  return ' '\n}\n\n/**\n * @param {(value: string) => string} replace\n */\nfunction collapseFactory(replace) {\n  return collapse\n\n  /**\n   * @param {string} value\n   * @returns {string}\n   */\n  function collapse(value) {\n    return String(value).replace(/[\\t\\n\\v\\f\\r ]+/g, replace)\n  }\n}\n\n/**\n * We don’t need to support void elements here (so `nobr wbr` -> `normal` is\n * ignored).\n *\n * @param {Root|Element} node\n * @param {Context} context\n * @returns {Whitespace}\n */\nfunction inferWhiteSpace(node, context) {\n  if ('tagName' in node && node.properties) {\n    switch (node.tagName) {\n      // Whitespace in script/style, while not displayed by CSS as significant,\n      // could have some meaning in JS/CSS, so we can’t touch them.\n      case 'listing':\n      case 'plaintext':\n      case 'script':\n      case 'style':\n      case 'xmp':\n        return 'pre'\n      case 'nobr':\n        return 'nowrap'\n      case 'pre':\n        return node.properties.wrap ? 'pre-wrap' : 'pre'\n      case 'td':\n      case 'th':\n        return node.properties.noWrap ? 'nowrap' : context.whitespace\n      case 'textarea':\n        return 'pre-wrap'\n      default:\n    }\n  }\n\n  return context.whitespace\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-minify-whitespace/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rehype-minify-whitespace/node_modules/hast-util-embedded/lib/index.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/rehype-minify-whitespace/node_modules/hast-util-embedded/lib/index.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   embedded: () => (/* binding */ embedded)\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/./node_modules/rehype-minify-whitespace/node_modules/hast-util-is-element/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n */\n\n\n\n/**\n * Check if a node is a *embedded content*.\n *\n * @type {import('hast-util-is-element').AssertPredicate<Element & {tagName: 'audio' | 'canvas' | 'embed' | 'iframe' | 'img' | 'math' | 'object' | 'picture' | 'svg' | 'video'}>}\n * @param value\n *   Thing to check (typically `Node`).\n * @returns\n *   Whether `value` is an element considered embedded content.\n *\n *   The elements `audio`, `canvas`, `embed`, `iframe`, `img`, `math`,\n *   `object`, `picture`, `svg`, and `video` are embedded content.\n */\n// @ts-expect-error Sure, the assertion matches.\nconst embedded = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  'audio',\n  'canvas',\n  'embed',\n  'iframe',\n  'img',\n  'math',\n  'object',\n  'picture',\n  'svg',\n  'video'\n])\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVoeXBlLW1pbmlmeS13aGl0ZXNwYWNlL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtZW1iZWRkZWQvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLHdCQUF3QjtBQUNyQzs7QUFFbUQ7O0FBRW5EO0FBQ0E7QUFDQTtBQUNBLFVBQVUsMERBQTBELDJHQUEyRztBQUMvSztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTyxpQkFBaUIsb0VBQWM7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVoeXBlLW1pbmlmeS13aGl0ZXNwYWNlL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtZW1iZWRkZWQvbGliL2luZGV4LmpzPzhkYWIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuRWxlbWVudH0gRWxlbWVudFxuICovXG5cbmltcG9ydCB7Y29udmVydEVsZW1lbnR9IGZyb20gJ2hhc3QtdXRpbC1pcy1lbGVtZW50J1xuXG4vKipcbiAqIENoZWNrIGlmIGEgbm9kZSBpcyBhICplbWJlZGRlZCBjb250ZW50Ki5cbiAqXG4gKiBAdHlwZSB7aW1wb3J0KCdoYXN0LXV0aWwtaXMtZWxlbWVudCcpLkFzc2VydFByZWRpY2F0ZTxFbGVtZW50ICYge3RhZ05hbWU6ICdhdWRpbycgfCAnY2FudmFzJyB8ICdlbWJlZCcgfCAnaWZyYW1lJyB8ICdpbWcnIHwgJ21hdGgnIHwgJ29iamVjdCcgfCAncGljdHVyZScgfCAnc3ZnJyB8ICd2aWRlbyd9Pn1cbiAqIEBwYXJhbSB2YWx1ZVxuICogICBUaGluZyB0byBjaGVjayAodHlwaWNhbGx5IGBOb2RlYCkuXG4gKiBAcmV0dXJuc1xuICogICBXaGV0aGVyIGB2YWx1ZWAgaXMgYW4gZWxlbWVudCBjb25zaWRlcmVkIGVtYmVkZGVkIGNvbnRlbnQuXG4gKlxuICogICBUaGUgZWxlbWVudHMgYGF1ZGlvYCwgYGNhbnZhc2AsIGBlbWJlZGAsIGBpZnJhbWVgLCBgaW1nYCwgYG1hdGhgLFxuICogICBgb2JqZWN0YCwgYHBpY3R1cmVgLCBgc3ZnYCwgYW5kIGB2aWRlb2AgYXJlIGVtYmVkZGVkIGNvbnRlbnQuXG4gKi9cbi8vIEB0cy1leHBlY3QtZXJyb3IgU3VyZSwgdGhlIGFzc2VydGlvbiBtYXRjaGVzLlxuZXhwb3J0IGNvbnN0IGVtYmVkZGVkID0gY29udmVydEVsZW1lbnQoW1xuICAnYXVkaW8nLFxuICAnY2FudmFzJyxcbiAgJ2VtYmVkJyxcbiAgJ2lmcmFtZScsXG4gICdpbWcnLFxuICAnbWF0aCcsXG4gICdvYmplY3QnLFxuICAncGljdHVyZScsXG4gICdzdmcnLFxuICAndmlkZW8nXG5dKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-minify-whitespace/node_modules/hast-util-embedded/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rehype-minify-whitespace/node_modules/hast-util-is-element/index.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/rehype-minify-whitespace/node_modules/hast-util-is-element/index.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertElement: () => (/* binding */ convertElement),\n/* harmony export */   isElement: () => (/* binding */ isElement)\n/* harmony export */ });\n/**\n * @typedef {import('unist').Parent} Parent\n * @typedef {import('hast').Element} Element\n */\n\n/**\n * @typedef {null | undefined | string | TestFunctionAnything | Array<string | TestFunctionAnything>} Test\n *   Check for an arbitrary element, unaware of TypeScript inferral.\n *\n * @callback TestFunctionAnything\n *   Check if an element passes a test, unaware of TypeScript inferral.\n * @param {Element} element\n *   An element.\n * @param {number | null | undefined} [index]\n *   The element’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The element’s parent.\n * @returns {boolean | void}\n *   Whether this element passes the test.\n */\n\n/**\n * @template {Element} T\n *   Element type.\n * @typedef {T['tagName'] | TestFunctionPredicate<T> | Array<T['tagName'] | TestFunctionPredicate<T>>} PredicateTest\n *   Check for an element that can be inferred by TypeScript.\n */\n\n/**\n * Check if an element passes a certain node test.\n *\n * @template {Element} T\n *   Element type.\n * @callback TestFunctionPredicate\n *   Complex test function for an element that can be inferred by TypeScript.\n * @param {Element} element\n *   An element.\n * @param {number | null | undefined} [index]\n *   The element’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The element’s parent.\n * @returns {element is T}\n *   Whether this element passes the test.\n */\n\n/**\n * @callback AssertAnything\n *   Check that an arbitrary value is an element, unaware of TypeScript inferral.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is an element and passes a test.\n */\n\n/**\n * Check if a node is an element and passes a certain node test\n *\n * @template {Element} T\n *   Element type.\n * @callback AssertPredicate\n *   Check that an arbitrary value is a specific element, aware of TypeScript.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {node is T}\n *   Whether this is an element and passes a test.\n */\n\n/**\n * Check if `node` is an `Element` and whether it passes the given test.\n *\n * @param node\n *   Thing to check, typically `Node`.\n * @param test\n *   A check for a specific element.\n * @param index\n *   The node’s position in its parent.\n * @param parent\n *   The node’s parent.\n * @returns\n *   Whether `node` is an element and passes a test.\n */\nconst isElement =\n  /**\n   * @type {(\n   *   (() => false) &\n   *   (<T extends Element = Element>(node: unknown, test?: PredicateTest<T>, index?: number, parent?: Parent, context?: unknown) => node is T) &\n   *   ((node: unknown, test: Test, index?: number, parent?: Parent, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [node]\n     * @param {Test | undefined} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parent | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function (node, test, index, parent, context) {\n      const check = convertElement(test)\n\n      if (\n        index !== undefined &&\n        index !== null &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite index for child node')\n      }\n\n      if (\n        parent !== undefined &&\n        parent !== null &&\n        (!parent.type || !parent.children)\n      ) {\n        throw new Error('Expected parent node')\n      }\n\n      // @ts-expect-error Looks like a node.\n      if (!node || !node.type || typeof node.type !== 'string') {\n        return false\n      }\n\n      if (\n        (parent === undefined || parent === null) !==\n        (index === undefined || index === null)\n      ) {\n        throw new Error('Expected both parent and index')\n      }\n\n      return check.call(context, node, index, parent)\n    }\n  )\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param test\n *   *  When nullish, checks if `node` is an `Element`.\n *   *  When `string`, works like passing `(element) => element.tagName === test`.\n *   *  When `function` checks if function passed the element is true.\n *   *  When `array`, checks any one of the subtests pass.\n * @returns\n *   An assertion.\n */\nconst convertElement =\n  /**\n   * @type {(\n   *   (<T extends Element>(test: T['tagName'] | TestFunctionPredicate<T>) => AssertPredicate<T>) &\n   *   ((test?: Test) => AssertAnything)\n   * )}\n   */\n  (\n    /**\n     * @param {Test | null | undefined} [test]\n     * @returns {AssertAnything}\n     */\n    function (test) {\n      if (test === undefined || test === null) {\n        return element\n      }\n\n      if (typeof test === 'string') {\n        return tagNameFactory(test)\n      }\n\n      if (typeof test === 'object') {\n        return anyFactory(test)\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      throw new Error('Expected function, string, or array as test')\n    }\n  )\n\n/**\n * Handle multiple tests.\n *\n * @param {Array<string | TestFunctionAnything>} tests\n * @returns {AssertAnything}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<AssertAnything>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convertElement(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].call(this, ...parameters)) {\n        return true\n      }\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn a string into a test for an element with a certain tag name.\n *\n * @param {string} check\n * @returns {AssertAnything}\n */\nfunction tagNameFactory(check) {\n  return tagName\n\n  /**\n   * @param {unknown} node\n   * @returns {boolean}\n   */\n  function tagName(node) {\n    return element(node) && node.tagName === check\n  }\n}\n\n/**\n * Turn a custom test into a test for an element that passes that test.\n *\n * @param {TestFunctionAnything} check\n * @returns {AssertAnything}\n */\nfunction castFactory(check) {\n  return assertion\n\n  /**\n   * @this {unknown}\n   * @param {unknown} node\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function assertion(node, ...parameters) {\n    // @ts-expect-error: fine.\n    return element(node) && Boolean(check.call(this, node, ...parameters))\n  }\n}\n\n/**\n * Make sure something is an element.\n *\n * @param {unknown} node\n * @returns {node is Element}\n */\nfunction element(node) {\n  return Boolean(\n    node &&\n      typeof node === 'object' &&\n      // @ts-expect-error Looks like a node.\n      node.type === 'element' &&\n      // @ts-expect-error Looks like an element.\n      typeof node.tagName === 'string'\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-minify-whitespace/node_modules/hast-util-is-element/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rehype-minify-whitespace/node_modules/hast-util-whitespace/index.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/rehype-minify-whitespace/node_modules/hast-util-whitespace/index.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   whitespace: () => (/* binding */ whitespace)\n/* harmony export */ });\n/**\n * Check if the given value is *inter-element whitespace*.\n *\n * @param {unknown} thing\n *   Thing to check (typically `Node` or `string`).\n * @returns {boolean}\n *   Whether the `value` is inter-element whitespace (`boolean`): consisting of\n *   zero or more of space, tab (`\\t`), line feed (`\\n`), carriage return\n *   (`\\r`), or form feed (`\\f`).\n *   If a node is passed it must be a `Text` node, whose `value` field is\n *   checked.\n */\nfunction whitespace(thing) {\n  /** @type {string} */\n  const value =\n    // @ts-expect-error looks like a node.\n    thing && typeof thing === 'object' && thing.type === 'text'\n      ? // @ts-expect-error looks like a text.\n        thing.value || ''\n      : thing\n\n  // HTML whitespace expression.\n  // See <https://infra.spec.whatwg.org/#ascii-whitespace>.\n  return typeof value === 'string' && value.replace(/[ \\t\\n\\f\\r]/g, '') === ''\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVoeXBlLW1pbmlmeS13aGl0ZXNwYWNlL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtd2hpdGVzcGFjZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsYUFBYSxRQUFRO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVoeXBlLW1pbmlmeS13aGl0ZXNwYWNlL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtd2hpdGVzcGFjZS9pbmRleC5qcz9hMzdlIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ2hlY2sgaWYgdGhlIGdpdmVuIHZhbHVlIGlzICppbnRlci1lbGVtZW50IHdoaXRlc3BhY2UqLlxuICpcbiAqIEBwYXJhbSB7dW5rbm93bn0gdGhpbmdcbiAqICAgVGhpbmcgdG8gY2hlY2sgKHR5cGljYWxseSBgTm9kZWAgb3IgYHN0cmluZ2ApLlxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKiAgIFdoZXRoZXIgdGhlIGB2YWx1ZWAgaXMgaW50ZXItZWxlbWVudCB3aGl0ZXNwYWNlIChgYm9vbGVhbmApOiBjb25zaXN0aW5nIG9mXG4gKiAgIHplcm8gb3IgbW9yZSBvZiBzcGFjZSwgdGFiIChgXFx0YCksIGxpbmUgZmVlZCAoYFxcbmApLCBjYXJyaWFnZSByZXR1cm5cbiAqICAgKGBcXHJgKSwgb3IgZm9ybSBmZWVkIChgXFxmYCkuXG4gKiAgIElmIGEgbm9kZSBpcyBwYXNzZWQgaXQgbXVzdCBiZSBhIGBUZXh0YCBub2RlLCB3aG9zZSBgdmFsdWVgIGZpZWxkIGlzXG4gKiAgIGNoZWNrZWQuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB3aGl0ZXNwYWNlKHRoaW5nKSB7XG4gIC8qKiBAdHlwZSB7c3RyaW5nfSAqL1xuICBjb25zdCB2YWx1ZSA9XG4gICAgLy8gQHRzLWV4cGVjdC1lcnJvciBsb29rcyBsaWtlIGEgbm9kZS5cbiAgICB0aGluZyAmJiB0eXBlb2YgdGhpbmcgPT09ICdvYmplY3QnICYmIHRoaW5nLnR5cGUgPT09ICd0ZXh0J1xuICAgICAgPyAvLyBAdHMtZXhwZWN0LWVycm9yIGxvb2tzIGxpa2UgYSB0ZXh0LlxuICAgICAgICB0aGluZy52YWx1ZSB8fCAnJ1xuICAgICAgOiB0aGluZ1xuXG4gIC8vIEhUTUwgd2hpdGVzcGFjZSBleHByZXNzaW9uLlxuICAvLyBTZWUgPGh0dHBzOi8vaW5mcmEuc3BlYy53aGF0d2cub3JnLyNhc2NpaS13aGl0ZXNwYWNlPi5cbiAgcmV0dXJuIHR5cGVvZiB2YWx1ZSA9PT0gJ3N0cmluZycgJiYgdmFsdWUucmVwbGFjZSgvWyBcXHRcXG5cXGZcXHJdL2csICcnKSA9PT0gJydcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-minify-whitespace/node_modules/hast-util-whitespace/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rehype-minify-whitespace/node_modules/unist-util-is/lib/index.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/rehype-minify-whitespace/node_modules/unist-util-is/lib/index.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convert: () => (/* binding */ convert),\n/* harmony export */   is: () => (/* binding */ is)\n/* harmony export */ });\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n */\n\n/**\n * @typedef {Record<string, unknown>} Props\n * @typedef {null | undefined | string | Props | TestFunctionAnything | Array<string | Props | TestFunctionAnything>} Test\n *   Check for an arbitrary node, unaware of TypeScript inferral.\n *\n * @callback TestFunctionAnything\n *   Check if a node passes a test, unaware of TypeScript inferral.\n * @param {unknown} this\n *   The given context.\n * @param {Node} node\n *   A node.\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean | void}\n *   Whether this node passes the test.\n */\n\n/**\n * @template {Node} Kind\n *   Node type.\n * @typedef {Kind['type'] | Partial<Kind> | TestFunctionPredicate<Kind> | Array<Kind['type'] | Partial<Kind> | TestFunctionPredicate<Kind>>} PredicateTest\n *   Check for a node that can be inferred by TypeScript.\n */\n\n/**\n * Check if a node passes a certain test.\n *\n * @template {Node} Kind\n *   Node type.\n * @callback TestFunctionPredicate\n *   Complex test function for a node that can be inferred by TypeScript.\n * @param {Node} node\n *   A node.\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {node is Kind}\n *   Whether this node passes the test.\n */\n\n/**\n * @callback AssertAnything\n *   Check that an arbitrary value is a node, unaware of TypeScript inferral.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is a node and passes a test.\n */\n\n/**\n * Check if a node is a node and passes a certain node test.\n *\n * @template {Node} Kind\n *   Node type.\n * @callback AssertPredicate\n *   Check that an arbitrary value is a specific node, aware of TypeScript.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {node is Kind}\n *   Whether this is a node and passes a test.\n */\n\n/**\n * Check if `node` is a `Node` and whether it passes the given test.\n *\n * @param node\n *   Thing to check, typically `Node`.\n * @param test\n *   A check for a specific node.\n * @param index\n *   The node’s position in its parent.\n * @param parent\n *   The node’s parent.\n * @returns\n *   Whether `node` is a node and passes a test.\n */\nconst is =\n  /**\n   * @type {(\n   *   (() => false) &\n   *   (<Kind extends Node = Node>(node: unknown, test: PredicateTest<Kind>, index: number, parent: Parent, context?: unknown) => node is Kind) &\n   *   (<Kind extends Node = Node>(node: unknown, test: PredicateTest<Kind>, index?: null | undefined, parent?: null | undefined, context?: unknown) => node is Kind) &\n   *   ((node: unknown, test: Test, index: number, parent: Parent, context?: unknown) => boolean) &\n   *   ((node: unknown, test?: Test, index?: null | undefined, parent?: null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [node]\n     * @param {Test} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parent | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function is(node, test, index, parent, context) {\n      const check = convert(test)\n\n      if (\n        index !== undefined &&\n        index !== null &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite index')\n      }\n\n      if (\n        parent !== undefined &&\n        parent !== null &&\n        (!is(parent) || !parent.children)\n      ) {\n        throw new Error('Expected parent node')\n      }\n\n      if (\n        (parent === undefined || parent === null) !==\n        (index === undefined || index === null)\n      ) {\n        throw new Error('Expected both parent and index')\n      }\n\n      // @ts-expect-error Looks like a node.\n      return node && node.type && typeof node.type === 'string'\n        ? Boolean(check.call(context, node, index, parent))\n        : false\n    }\n  )\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param test\n *   *   when nullish, checks if `node` is a `Node`.\n *   *   when `string`, works like passing `(node) => node.type === test`.\n *   *   when `function` checks if function passed the node is true.\n *   *   when `object`, checks that all keys in test are in node, and that they have (strictly) equal values.\n *   *   when `array`, checks if any one of the subtests pass.\n * @returns\n *   An assertion.\n */\nconst convert =\n  /**\n   * @type {(\n   *   (<Kind extends Node>(test: PredicateTest<Kind>) => AssertPredicate<Kind>) &\n   *   ((test?: Test) => AssertAnything)\n   * )}\n   */\n  (\n    /**\n     * @param {Test} [test]\n     * @returns {AssertAnything}\n     */\n    function (test) {\n      if (test === undefined || test === null) {\n        return ok\n      }\n\n      if (typeof test === 'string') {\n        return typeFactory(test)\n      }\n\n      if (typeof test === 'object') {\n        return Array.isArray(test) ? anyFactory(test) : propsFactory(test)\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      throw new Error('Expected function, string, or object as test')\n    }\n  )\n\n/**\n * @param {Array<string | Props | TestFunctionAnything>} tests\n * @returns {AssertAnything}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<AssertAnything>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convert(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].call(this, ...parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn an object into a test for a node with a certain fields.\n *\n * @param {Props} check\n * @returns {AssertAnything}\n */\nfunction propsFactory(check) {\n  return castFactory(all)\n\n  /**\n   * @param {Node} node\n   * @returns {boolean}\n   */\n  function all(node) {\n    /** @type {string} */\n    let key\n\n    for (key in check) {\n      // @ts-expect-error: hush, it sure works as an index.\n      if (node[key] !== check[key]) return false\n    }\n\n    return true\n  }\n}\n\n/**\n * Turn a string into a test for a node with a certain type.\n *\n * @param {string} check\n * @returns {AssertAnything}\n */\nfunction typeFactory(check) {\n  return castFactory(type)\n\n  /**\n   * @param {Node} node\n   */\n  function type(node) {\n    return node && node.type === check\n  }\n}\n\n/**\n * Turn a custom test into a test for a node that passes that test.\n *\n * @param {TestFunctionAnything} check\n * @returns {AssertAnything}\n */\nfunction castFactory(check) {\n  return assertion\n\n  /**\n   * @this {unknown}\n   * @param {unknown} node\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function assertion(node, ...parameters) {\n    return Boolean(\n      node &&\n        typeof node === 'object' &&\n        'type' in node &&\n        // @ts-expect-error: fine.\n        Boolean(check.call(this, node, ...parameters))\n    )\n  }\n}\n\nfunction ok() {\n  return true\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-minify-whitespace/node_modules/unist-util-is/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rehype-minify-whitespace/skippable.js":
/*!************************************************************!*\
  !*** ./node_modules/rehype-minify-whitespace/skippable.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   skippable: () => (/* binding */ skippable)\n/* harmony export */ });\nconst skippable = [\n  'area',\n  'base',\n  'basefont',\n  'dialog',\n  'datalist',\n  'head',\n  'link',\n  'meta',\n  'noembed',\n  'noframes',\n  'param',\n  'rp',\n  'script',\n  'source',\n  'style',\n  'template',\n  'track',\n  'title'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVoeXBlLW1pbmlmeS13aGl0ZXNwYWNlL3NraXBwYWJsZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVoeXBlLW1pbmlmeS13aGl0ZXNwYWNlL3NraXBwYWJsZS5qcz83ODZiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBza2lwcGFibGUgPSBbXG4gICdhcmVhJyxcbiAgJ2Jhc2UnLFxuICAnYmFzZWZvbnQnLFxuICAnZGlhbG9nJyxcbiAgJ2RhdGFsaXN0JyxcbiAgJ2hlYWQnLFxuICAnbGluaycsXG4gICdtZXRhJyxcbiAgJ25vZW1iZWQnLFxuICAnbm9mcmFtZXMnLFxuICAncGFyYW0nLFxuICAncnAnLFxuICAnc2NyaXB0JyxcbiAgJ3NvdXJjZScsXG4gICdzdHlsZScsXG4gICd0ZW1wbGF0ZScsXG4gICd0cmFjaycsXG4gICd0aXRsZSdcbl1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-minify-whitespace/skippable.js\n");

/***/ })

};
;