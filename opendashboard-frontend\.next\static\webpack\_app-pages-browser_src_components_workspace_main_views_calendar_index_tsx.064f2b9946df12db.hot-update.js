"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx":
/*!******************************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx ***!
  \******************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarEventSegment: function() { return /* binding */ CalendarEventSegment; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _utils_color__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/color */ \"(app-pages-browser)/./src/utils/color.ts\");\n/* harmony import */ var _utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/dateUtils */ \"(app-pages-browser)/./src/utils/dateUtils.ts\");\n/* harmony import */ var _utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/multiDayEventUtils */ \"(app-pages-browser)/./src/utils/multiDayEventUtils.ts\");\n/* harmony import */ var _MultiDayEventBadge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./MultiDayEventBadge */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MultiDayEventBadge.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CalendarEventSegment = (param)=>{\n    let { segment, style, onClick, onContextMenu, view = \"month\", isEndOfEvent, isDragging } = param;\n    _s();\n    const dragRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { attributes, listeners, setNodeRef, isDragging: dndIsDragging } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_7__.useDraggable)({\n        id: \"segment-\".concat(segment.id),\n        data: {\n            type: \"segment\",\n            payload: segment\n        }\n    });\n    const combinedIsDragging = isDragging || dndIsDragging;\n    // Memoize event calculations\n    const eventDetails = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const eventHeight = (style === null || style === void 0 ? void 0 : style.height) ? parseInt(style.height.toString().replace(\"px\", \"\")) : null;\n        const showTime = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.shouldShowTimeInSegment)(segment, view);\n        const continuationText = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentContinuationText)(segment);\n        // let formattedTime = null;\n        // if (segment.isAllDay) {\n        //   formattedTime = 'All day';\n        // } else if (showTime) {\n        //   formattedTime = formatEventTime(segment.startTime, view, { shortFormat: true });\n        // }\n        return {\n            eventSize: (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.getEventSize)(eventHeight),\n            showTime,\n            continuationText,\n            formattedTime: showTime ? (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.formatEventTime)(segment.startTime, view, {\n                shortFormat: true\n            }) : null\n        };\n    }, [\n        segment,\n        style,\n        view\n    ]);\n    // Memoize styling\n    const eventStyles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const denimColorInfo = (0,_utils_color__WEBPACK_IMPORTED_MODULE_3__.ColorInfo)(\"Denim\");\n        const stylingClasses = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentStylingClasses)(segment);\n        // Extract RGB values from the rgba string and make it fully opaque\n        const rgbMatch = denimColorInfo.bg.match(/rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)/);\n        const opaqueBackground = rgbMatch ? \"rgb(\".concat(rgbMatch[1], \", \").concat(rgbMatch[2], \", \").concat(rgbMatch[3], \")\") : denimColorInfo.bg;\n        return {\n            style: {\n                ...style,\n                backgroundColor: opaqueBackground,\n                minHeight: \"24px\",\n                // Add subtle shadow for better visual depth\n                boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)\",\n                opacity: combinedIsDragging ? 0.5 : 1\n            },\n            classes: stylingClasses\n        };\n    }, [\n        style,\n        segment,\n        combinedIsDragging\n    ]);\n    // Memoize classes\n    const eventClasses = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const baseClasses = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"select-none text-black text-xs overflow-hidden relative\", !combinedIsDragging && \"cursor-pointer\", eventStyles.classes.roundedCorners, eventStyles.classes.continuationIndicator, eventStyles.classes.opacity, \"p-1\");\n        // Month view or small events\n        if (view === \"month\" || eventDetails.eventSize === \"small\") {\n            return {\n                baseClasses,\n                containerClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-1 flex-nowrap\"),\n                titleClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-medium truncate leading-tight text-xs overflow-hidden\", segment.isMultiDay ? \"max-w-[60%]\" : \"max-w-[70%]\"),\n                timeClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"opacity-75 text-xs flex-shrink-0 text-[0.65rem]\"),\n                continuationClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-xs opacity-60 flex-shrink-0 text-[0.6rem]\")\n            };\n        }\n        // Day and Week views for medium/large events\n        return {\n            baseClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, \"p-2\"),\n            containerClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col\", \"space-y-0.5\"),\n            titleClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-medium truncate leading-tight text-xs overflow-hidden\"),\n            timeClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"opacity-75 text-xs\"),\n            continuationClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-xs opacity-60\")\n        };\n    }, [\n        eventDetails,\n        view,\n        segment.isMultiDay,\n        eventStyles.classes,\n        combinedIsDragging\n    ]);\n    // Render event content based on view and size\n    const renderEventContent = ()=>{\n        const event = segment.originalEvent;\n        // Month view or small events - horizontal layout\n        if (view === \"month\" || eventDetails.eventSize === \"small\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: eventClasses.containerClasses,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: eventClasses.titleClasses,\n                        children: event.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, undefined),\n                    eventDetails.showTime && eventDetails.formattedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: eventClasses.timeClasses,\n                        children: eventDetails.formattedTime\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 13\n                    }, undefined),\n                    segment.isMultiDay && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultiDayEventBadge__WEBPACK_IMPORTED_MODULE_6__.MultiDayEventBadge, {\n                        segment: segment,\n                        view: view,\n                        size: \"small\",\n                        className: eventClasses.continuationClasses,\n                        isEndOfEvent: isEndOfEvent\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, undefined);\n        }\n        // Day and Week views - vertical layout\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: eventClasses.containerClasses,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: eventClasses.titleClasses,\n                    children: [\n                        event.title,\n                        segment.isMultiDay && !eventDetails.showTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultiDayEventBadge__WEBPACK_IMPORTED_MODULE_6__.ContinuationArrow, {\n                            direction: segment.isFirstSegment ? \"right\" : segment.isLastSegment ? \"left\" : \"both\",\n                            className: \"ml-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, undefined),\n                (eventDetails.showTime || segment.isAllDay) && eventDetails.formattedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: eventClasses.timeClasses,\n                    children: [\n                        eventDetails.formattedTime,\n                        segment.isMultiDay && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultiDayEventBadge__WEBPACK_IMPORTED_MODULE_6__.ContinuationArrow, {\n                            direction: segment.isFirstSegment ? \"right\" : segment.isLastSegment ? \"left\" : \"both\",\n                            className: \"ml-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 11\n                }, undefined),\n                segment.isMultiDay && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: eventClasses.continuationClasses,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultiDayEventBadge__WEBPACK_IMPORTED_MODULE_6__.MultiDayEventBadge, {\n                        segment: segment,\n                        view: view,\n                        size: eventDetails.eventSize === \"large\" ? \"medium\" : \"small\",\n                        isEndOfEvent: isEndOfEvent\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n            lineNumber: 170,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"event-\".concat(segment.originalEvent.id),\n        ref: (node)=>{\n            setNodeRef(node);\n            dragRef.current = node;\n        },\n        style: eventStyles.style,\n        className: eventClasses.baseClasses,\n        onClick: onClick,\n        onContextMenu: onContextMenu,\n        ...listeners,\n        ...attributes,\n        children: renderEventContent()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n        lineNumber: 206,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CalendarEventSegment, \"kiz8nz2pAp5RwnBQHmiQwwayVv8=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_7__.useDraggable\n    ];\n});\n_c = CalendarEventSegment;\nvar _c;\n$RefreshReg$(_c, \"CalendarEventSegment\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\n"));

/***/ })

});