"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_workspace_main_record_components_recordOverview_tsx"],{

/***/ "(app-pages-browser)/./src/components/workspace/main/common/imageCropperDialog.tsx":
/*!*********************************************************************!*\
  !*** ./src/components/workspace/main/common/imageCropperDialog.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageCropperDialog: function() { return /* binding */ ImageCropperDialog; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-icons */ \"(app-pages-browser)/./node_modules/@radix-ui/react-icons/dist/react-icons.esm.js\");\n/* harmony import */ var _utils_resizeImage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/resizeImage */ \"(app-pages-browser)/./src/utils/resizeImage.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst ImageCropperDialog = (param)=>{\n    let { file, open, onClose, onCrop, type } = param;\n    _s();\n    const [imageUrl, setImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [scale, setScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [initialScale, setInitialScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const uiDimensions = type === \"cover\" ? {\n        width: 500,\n        height: 125\n    } : {\n        width: 200,\n        height: 200\n    };\n    const dimensions = type === \"cover\" ? {\n        width: 1200,\n        height: 300\n    } : {\n        width: 500,\n        height: 500\n    };\n    const resizeOptions = {\n        quality: 80\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (file && open) {\n            const url = URL.createObjectURL(file);\n            setImageUrl(url);\n            setPosition({\n                x: 0,\n                y: 0\n            });\n            setScale(1);\n            const img = new Image();\n            img.src = url;\n            img.onload = ()=>{\n                imageRef.current = img;\n                const scaleX = uiDimensions.width / img.width;\n                const scaleY = uiDimensions.height / img.height;\n                const initialScale = Math.max(scaleX, scaleY);\n                setScale(initialScale);\n                setInitialScale(initialScale);\n                const scaledWidth = img.width * initialScale;\n                const scaledHeight = img.height * initialScale;\n                setPosition({\n                    x: (uiDimensions.width - scaledWidth) / 2,\n                    y: (uiDimensions.height - scaledHeight) / 2\n                });\n                drawImage();\n            };\n            return ()=>{\n                URL.revokeObjectURL(url);\n            };\n        }\n    }, [\n        file,\n        open\n    ]);\n    const drawImage = ()=>{\n        if (!canvasRef.current || !imageRef.current) return;\n        const canvas = canvasRef.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        ctx.clearRect(0, 0, canvas.width, canvas.height);\n        const img = imageRef.current;\n        const scaledWidth = img.width * scale;\n        const scaledHeight = img.height * scale;\n        ctx.drawImage(img, position.x, position.y, scaledWidth, scaledHeight);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        drawImage();\n    }, [\n        position,\n        scale\n    ]);\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setDragStart({\n            x: e.clientX - position.x,\n            y: e.clientY - position.y\n        });\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        setPosition({\n            x: e.clientX - dragStart.x,\n            y: e.clientY - dragStart.y\n        });\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n    };\n    const handleZoomIn = ()=>{\n        if (!imageRef.current) return;\n        const newScale = Math.min(scale + 0.1, 3);\n        const scaleRatio = newScale / scale;\n        const centerX = uiDimensions.width / 2;\n        const centerY = uiDimensions.height / 2;\n        const newPosition = {\n            x: centerX - (centerX - position.x) * scaleRatio,\n            y: centerY - (centerY - position.y) * scaleRatio\n        };\n        setScale(newScale);\n        setPosition(newPosition);\n    };\n    const handleZoomOut = ()=>{\n        if (!imageRef.current) return;\n        if (scale <= initialScale) {\n            setScale(initialScale);\n            const scaledWidth = imageRef.current.width * initialScale;\n            const scaledHeight = imageRef.current.height * initialScale;\n            setPosition({\n                x: (uiDimensions.width - scaledWidth) / 2,\n                y: (uiDimensions.height - scaledHeight) / 2\n            });\n        } else {\n            const newScale = Math.max(scale - 0.1, initialScale);\n            const scaleRatio = newScale / scale;\n            const centerX = uiDimensions.width / 2;\n            const centerY = uiDimensions.height / 2;\n            const newPosition = {\n                x: centerX - (centerX - position.x) * scaleRatio,\n                y: centerY - (centerY - position.y) * scaleRatio\n            };\n            setScale(newScale);\n            setPosition(newPosition);\n        }\n    };\n    const handleCrop = async ()=>{\n        if (!canvasRef.current || !file) return;\n        const canvas = canvasRef.current;\n        const cropCanvas = document.createElement(\"canvas\");\n        cropCanvas.width = uiDimensions.width;\n        cropCanvas.height = uiDimensions.height;\n        const cropCtx = cropCanvas.getContext(\"2d\");\n        if (!cropCtx) return;\n        cropCtx.drawImage(canvas, 0, 0, canvas.width, canvas.height, 0, 0, uiDimensions.width, uiDimensions.height);\n        cropCanvas.toBlob(async (blob)=>{\n            if (blob) {\n                const croppedFile = new File([\n                    blob\n                ], file.name, {\n                    type: file.type,\n                    lastModified: Date.now()\n                });\n                const resizedFile = await (0,_utils_resizeImage__WEBPACK_IMPORTED_MODULE_4__.resizeImage)(croppedFile, dimensions, _utils_resizeImage__WEBPACK_IMPORTED_MODULE_4__.ScaleMode.Fill, resizeOptions);\n                onCrop(resizedFile);\n            }\n        }, file.type);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: open,\n        onOpenChange: ()=>{},\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"sm:max-w-[550px] max-h-[90vh] overflow-y-auto\",\n            hideCloseBtn: true,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    className: \"p-4 border-b flex flex-row items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: onClose,\n                                    className: \"p-1 h-6 w-6 hover:bg-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_5__.Cross2Icon, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\imageCropperDialog.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 29\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\imageCropperDialog.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                                    className: \"text-sm font-semibold\",\n                                    children: [\n                                        \"Adjust \",\n                                        type === \"cover\" ? \"Cover\" : \"Profile\",\n                                        \" Image\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\imageCropperDialog.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\imageCropperDialog.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: onClose,\n                                    size: \"sm\",\n                                    className: \"text-xs rounded-full\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\imageCropperDialog.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleCrop,\n                                    size: \"sm\",\n                                    className: \"text-xs rounded-full bg-black text-white hover:bg-gray-800\",\n                                    children: \"Apply\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\imageCropperDialog.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\imageCropperDialog.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\imageCropperDialog.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center gap-4 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative border border-gray-200 overflow-hidden\",\n                            style: {\n                                width: \"\".concat(uiDimensions.width, \"px\"),\n                                height: \"\".concat(uiDimensions.height, \"px\")\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                                ref: canvasRef,\n                                width: uiDimensions.width,\n                                height: uiDimensions.height,\n                                onMouseDown: handleMouseDown,\n                                onMouseMove: handleMouseMove,\n                                onMouseUp: handleMouseUp,\n                                onMouseLeave: handleMouseUp,\n                                className: \"cursor-move\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\imageCropperDialog.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\imageCropperDialog.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleZoomOut,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"flex font-semibold rounded-full p-1.5 gap-1 h-auto text-xs items-center\",\n                                    children: \"Zoom Out\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\imageCropperDialog.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleZoomIn,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"flex font-semibold rounded-full p-1.5 gap-1 h-auto text-xs items-center\",\n                                    children: \"Zoom In\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\imageCropperDialog.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\imageCropperDialog.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500\",\n                            children: \"Drag to position the image. Use the zoom buttons to resize.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\imageCropperDialog.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\imageCropperDialog.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\imageCropperDialog.tsx\",\n            lineNumber: 201,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\imageCropperDialog.tsx\",\n        lineNumber: 200,\n        columnNumber: 9\n    }, undefined);\n};\n_s(ImageCropperDialog, \"Nx5hEweFLwjf66cgYSa2UnTQr9c=\");\n_c = ImageCropperDialog;\nvar _c;\n$RefreshReg$(_c, \"ImageCropperDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/common/imageCropperDialog.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/common/recordImageUploader.tsx":
/*!**********************************************************************!*\
  !*** ./src/components/workspace/main/common/recordImageUploader.tsx ***!
  \**********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecordImageUploader: function() { return /* binding */ RecordImageUploader; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpTrayIcon_PhotoIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpTrayIcon,PhotoIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpTrayIcon_PhotoIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpTrayIcon,PhotoIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpTrayIcon.js\");\n/* harmony import */ var _providers_alert__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/providers/alert */ \"(app-pages-browser)/./src/providers/alert.tsx\");\n/* harmony import */ var _api_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/api/common */ \"(app-pages-browser)/./src/api/common.ts\");\n/* harmony import */ var _providers_user__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/user */ \"(app-pages-browser)/./src/providers/user.tsx\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var _imageCropperDialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./imageCropperDialog */ \"(app-pages-browser)/./src/components/workspace/main/common/imageCropperDialog.tsx\");\n/* harmony import */ var _api_workspace__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/api/workspace */ \"(app-pages-browser)/./src/api/workspace.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst RecordImageUploader = (param)=>{\n    let { record, type, onImageUpdated, className = \"\", children } = param;\n    _s();\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cropperOpen, setCropperOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_providers_alert__WEBPACK_IMPORTED_MODULE_3__.useAlert)();\n    const { token } = (0,_providers_user__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const { workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_6__.useWorkspace)();\n    const handleClick = ()=>{\n        var _fileInputRef_current;\n        (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n    };\n    const handleFileChange = (e)=>{\n        const files = e.target.files;\n        if (!files || files.length === 0 || !token) return;\n        const file = files[0];\n        if (!file.type.startsWith(\"image/\")) {\n            toast.error(\"Please select an image file\");\n            return;\n        }\n        setSelectedFile(file);\n        setCropperOpen(true);\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const handleCropComplete = async (croppedFile)=>{\n        if (!token) return;\n        setIsUploading(true);\n        setCropperOpen(false);\n        const uploadCallback = {\n            onStart: ()=>{},\n            onProgress: ()=>{},\n            onComplete: async (res)=>{\n                var _res_data_data_meta, _res_data_data_meta1;\n                setIsUploading(false);\n                setSelectedFile(null);\n                if (!res.isSuccess) {\n                    console.error(\"Error uploading image:\", res.error);\n                    toast.error(\"Failed to upload \".concat(type, \" image: \").concat(res.error || (0,_api_common__WEBPACK_IMPORTED_MODULE_4__.defaultAPIMessage)()));\n                    return;\n                }\n                const imageUrl = res.data.data.imageUrl || ((_res_data_data_meta = res.data.data.meta) === null || _res_data_data_meta === void 0 ? void 0 : _res_data_data_meta.coverImage) || ((_res_data_data_meta1 = res.data.data.meta) === null || _res_data_data_meta1 === void 0 ? void 0 : _res_data_data_meta1.profileImage) || \"\";\n                toast.success(\"\".concat(type === \"cover\" ? \"Cover\" : \"Profile\", \" image updated\"));\n                onImageUpdated === null || onImageUpdated === void 0 ? void 0 : onImageUpdated(imageUrl);\n            }\n        };\n        if (type === \"cover\") {\n            (0,_api_workspace__WEBPACK_IMPORTED_MODULE_8__.uploadRecordCoverImage)(token.token, workspace.workspace.id, record.id, record.databaseId, croppedFile, uploadCallback);\n        } else {\n            (0,_api_workspace__WEBPACK_IMPORTED_MODULE_8__.uploadRecordProfileImage)(token.token, workspace.workspace.id, record.id, record.databaseId, croppedFile, uploadCallback);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                onClick: handleClick,\n                className: className,\n                children: children || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    variant: \"outline\",\n                    size: \"sm\",\n                    disabled: isUploading,\n                    className: \"flex font-semibold rounded-full p-1.5 gap-1 h-auto text-xs items-center\",\n                    children: isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Uploading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\recordImageUploader.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 29\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: type === \"cover\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpTrayIcon_PhotoIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\recordImageUploader.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 41\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Change cover\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\recordImageUploader.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 41\n                                }, undefined)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpTrayIcon_PhotoIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\recordImageUploader.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 41\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Upload profile image\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\recordImageUploader.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 41\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\recordImageUploader.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\recordImageUploader.tsx\",\n                lineNumber: 88,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                type: \"file\",\n                ref: fileInputRef,\n                onChange: handleFileChange,\n                accept: \"image/*\",\n                className: \"hidden\",\n                title: \"Upload \".concat(type, \" image\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\recordImageUploader.tsx\",\n                lineNumber: 119,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_imageCropperDialog__WEBPACK_IMPORTED_MODULE_7__.ImageCropperDialog, {\n                file: selectedFile,\n                open: cropperOpen,\n                onClose: ()=>setCropperOpen(false),\n                onCrop: handleCropComplete,\n                type: type\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\recordImageUploader.tsx\",\n                lineNumber: 127,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(RecordImageUploader, \"7mp3dIQOXxS9XIpKpvddDgS6TIU=\", false, function() {\n    return [\n        _providers_alert__WEBPACK_IMPORTED_MODULE_3__.useAlert,\n        _providers_user__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_6__.useWorkspace\n    ];\n});\n_c = RecordImageUploader;\nvar _c;\n$RefreshReg$(_c, \"RecordImageUploader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/common/recordImageUploader.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/record/components/recordOverview.tsx":
/*!****************************************************************************!*\
  !*** ./src/components/workspace/main/record/components/recordOverview.tsx ***!
  \****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecordOverview: function() { return /* binding */ RecordOverview; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_GlobeAltIcon_PencilIcon_PhotoIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=GlobeAltIcon,PencilIcon,PhotoIcon,PlusCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _barrel_optimize_names_GlobeAltIcon_PencilIcon_PhotoIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=GlobeAltIcon,PencilIcon,PhotoIcon,PlusCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_GlobeAltIcon_PencilIcon_PhotoIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=GlobeAltIcon,PencilIcon,PhotoIcon,PlusCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_GlobeAltIcon_PencilIcon_PhotoIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=GlobeAltIcon,PencilIcon,PhotoIcon,PlusCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusCircleIcon.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_workspace_main_views_viewIcon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/workspace/main/views/viewIcon */ \"(app-pages-browser)/./src/components/workspace/main/views/viewIcon.tsx\");\n/* harmony import */ var _utils_demo_links__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/demo/links */ \"(app-pages-browser)/./src/utils/demo/links.ts\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_common_formFieldBody__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/common/formFieldBody */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/common/formFieldBody.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _typings_page__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/typings/page */ \"(app-pages-browser)/./src/typings/page.ts\");\n/* harmony import */ var _utils_environment__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/utils/environment */ \"(app-pages-browser)/./src/utils/environment.ts\");\n/* harmony import */ var _components_workspace_main_emails_sendEmailWrapper__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/workspace/main/emails/sendEmailWrapper */ \"(app-pages-browser)/./src/components/workspace/main/emails/sendEmailWrapper.tsx\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _components_workspace_main_common_recordImageUploader__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/workspace/main/common/recordImageUploader */ \"(app-pages-browser)/./src/components/workspace/main/common/recordImageUploader.tsx\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* __next_internal_client_entry_do_not_use__ RecordOverview auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst RecordOverview = (props)=>{\n    var _record_meta, _record_meta1, _record_meta2, _record_meta3;\n    _s();\n    const { accessLevel } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_12__.usePage)();\n    const { updateRecordValues } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_11__.useViews)();\n    const { recordInfo, database } = (0,_providers_record__WEBPACK_IMPORTED_MODULE_9__.useRecord)();\n    const { members } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_19__.useWorkspace)();\n    const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_17__.getDatabaseTitleCol)(database);\n    const title = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_17__.getRecordTitle)(recordInfo.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts, database, members);\n    const canBeEnriched = false;\n    const isPublished = true;\n    const disabled = !accessLevel || ![\n        _typings_page__WEBPACK_IMPORTED_MODULE_13__.AccessLevel.Full,\n        _typings_page__WEBPACK_IMPORTED_MODULE_13__.AccessLevel.Edit\n    ].includes(accessLevel);\n    const { record } = recordInfo;\n    const toSaveRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const debounceRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(lodash_debounce__WEBPACK_IMPORTED_MODULE_16___default()(async ()=>{\n        const values = toSaveRef.current;\n        if (values) {\n            await updateRecordValues(record.databaseId, [\n                record.id\n            ], values, {});\n            toSaveRef.current = null;\n        }\n    }, 100));\n    const updateValues = async (u)=>{\n        // debounce update values\n        toSaveRef.current = u;\n        debounceRef.current();\n    };\n    const updateTitleFormat = async (titleFormat)=>{\n        await updateRecordValues(record.databaseId, [\n            record.id\n        ], {}, {\n            titleFormat\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        document.title = title;\n    }, [\n        title\n    ]);\n    const coverImage = typeof ((_record_meta = record.meta) === null || _record_meta === void 0 ? void 0 : _record_meta.coverImage) === \"object\" ? record.meta.coverImage.url : ((_record_meta1 = record.meta) === null || _record_meta1 === void 0 ? void 0 : _record_meta1.coverImage) || \"\";\n    const profileImage = typeof ((_record_meta2 = record.meta) === null || _record_meta2 === void 0 ? void 0 : _record_meta2.profileImage) === \"object\" ? record.meta.profileImage.url : ((_record_meta3 = record.meta) === null || _record_meta3 === void 0 ? void 0 : _record_meta3.profileImage) || \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hidden lg:block \".concat(props.isTabbed ? \"!block\" : \"w-96 border-r\", \" h-full border-neutral-300 rOV\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-32 bg-cover bg-center bg-gray-100 relative\",\n                        style: {\n                            backgroundImage: coverImage ? \"url(\".concat(coverImage, \")\") : undefined\n                        },\n                        children: !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_common_recordImageUploader__WEBPACK_IMPORTED_MODULE_18__.RecordImageUploader, {\n                            record: record,\n                            type: \"cover\",\n                            className: \"absolute bottom-2 right-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"secondary\",\n                                size: \"sm\",\n                                className: \"bg-white/80 backdrop-blur-sm hover:bg-white/90\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_GlobeAltIcon_PencilIcon_PhotoIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    coverImage ? \"Change Cover\" : \"Add Cover\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 border-b flex flex-col gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-1 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.Avatar, {\n                                                className: \"mr-1 size-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.AvatarImage, {\n                                                        src: profileImage,\n                                                        alt: title,\n                                                        className: \"!rounded-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 33\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.AvatarFallback, {\n                                                        className: \"!rounded\",\n                                                        children: title[0]\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 33\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 29\n                                            }, undefined),\n                                            !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_common_recordImageUploader__WEBPACK_IMPORTED_MODULE_18__.RecordImageUploader, {\n                                                record: record,\n                                                type: \"profile\",\n                                                className: \"absolute -bottom-1 -right-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"secondary\",\n                                                    size: \"icon\",\n                                                    className: \"rounded-full h-6 w-6 bg-white/80 backdrop-blur-sm hover:bg-white/90\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_GlobeAltIcon_PencilIcon_PhotoIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 37\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-black flex-1 truncate overflow-hidden\",\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    (0,_utils_environment__WEBPACK_IMPORTED_MODULE_14__.isLocal)() && isPublished && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: (e)=>{\n                                            // setCollapse(!collapse)\n                                            e.stopPropagation();\n                                            e.preventDefault();\n                                            alert(\"Show a popup with the fields and a publish button; if published show an input with the link\");\n                                        },\n                                        className: \"size-6 p-1 rounded-full items-center hover:bg-neutral-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_GlobeAltIcon_PencilIcon_PhotoIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"size-4 transition-transform text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 54\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-1 items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_emails_sendEmailWrapper__WEBPACK_IMPORTED_MODULE_15__.SendEmailWrapper, {\n                                    isRecordPage: true,\n                                    database: database,\n                                    recordIds: [\n                                        record.id\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollArea, {\n                            className: \"h-full w-full overflow-x-hidden !max-w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"block max-w-full overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 pb-28 flex flex-col gap-2 max-w-full\",\n                                    children: [\n                                        database.definition.columnIds.map((id)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_form_components_common_formFieldBody__WEBPACK_IMPORTED_MODULE_10__.FormFieldBody, {\n                                                columnsMap: database.definition.columnsMap,\n                                                columnsPropMap: {},\n                                                updateFieldProps: ()=>{},\n                                                activeField: \"\",\n                                                setActiveField: ()=>{},\n                                                values: record.recordValues,\n                                                processedRecordValues: recordInfo.processedRecord.processedRecordValues,\n                                                updateValues: updateValues,\n                                                isEditing: false,\n                                                disabled: disabled,\n                                                databaseId: database.id,\n                                                id: id\n                                            }, id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 74\n                                            }, undefined)),\n                                        (0,_utils_environment__WEBPACK_IMPORTED_MODULE_14__.isLocal)() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2 my-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: \"New field\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenu, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                children: \"Choose fields\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 45\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 41\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuContent, {\n                                                            className: \"w-56 p-0 rounded-none\",\n                                                            align: \"start\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col h-auto max-h-96\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-2 border-b flex-1\",\n                                                                        children: _utils_demo_links__WEBPACK_IMPORTED_MODULE_7__.subLinks.map((link)=>{\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                className: \"text-xs rounded-none p-1.5 px-2 h-auto gap-2 w-full justify-start overflow-hidden\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_viewIcon__WEBPACK_IMPORTED_MODULE_6__.ViewIcon, {\n                                                                                        type: link.type,\n                                                                                        className: \"size-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                                                        lineNumber: 224,\n                                                                                        columnNumber: 61\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex-1 overflow-hidden truncate text-left\",\n                                                                                        children: link.title\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                                                        lineNumber: 225,\n                                                                                        columnNumber: 61\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                                                        className: \"h-4 w-8\",\n                                                                                        thumbClassName: \"!size-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                                                        lineNumber: 228,\n                                                                                        columnNumber: 61\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, link.id, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                                                lineNumber: 221,\n                                                                                columnNumber: 64\n                                                                            }, undefined);\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                                        lineNumber: 219,\n                                                                        columnNumber: 49\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            className: \"text-xs rounded-none p-1.5 h-auto gap-2 w-full justify-start\",\n                                                                            onClick: ()=>{\n                                                                            // setOpen(false)\n                                                                            // props.requestNewView()\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_GlobeAltIcon_PencilIcon_PhotoIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"size-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                                                    lineNumber: 239,\n                                                                                    columnNumber: 57\n                                                                                }, undefined),\n                                                                                \"New View\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                                            lineNumber: 233,\n                                                                            columnNumber: 53\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                                        lineNumber: 232,\n                                                                        columnNumber: 49\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 45\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 41\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 37\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 47\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                lineNumber: 84,\n                columnNumber: 13\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n            lineNumber: 83,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false);\n};\n_s(RecordOverview, \"iYwp0IbCU3agsnPt7KFLPOlVMFM=\", false, function() {\n    return [\n        _providers_page__WEBPACK_IMPORTED_MODULE_12__.usePage,\n        _providers_views__WEBPACK_IMPORTED_MODULE_11__.useViews,\n        _providers_record__WEBPACK_IMPORTED_MODULE_9__.useRecord,\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_19__.useWorkspace\n    ];\n});\n_c = RecordOverview;\nvar _c;\n$RefreshReg$(_c, \"RecordOverview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/record/components/recordOverview.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/demo/links.ts":
/*!*********************************!*\
  !*** ./src/utils/demo/links.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   databaseLinkGroup: function() { return /* binding */ databaseLinkGroup; },\n/* harmony export */   pageLinkGroup: function() { return /* binding */ pageLinkGroup; },\n/* harmony export */   privatePageLinkGroup: function() { return /* binding */ privatePageLinkGroup; },\n/* harmony export */   subLinks: function() { return /* binding */ subLinks; }\n/* harmony export */ });\nconst subLinks = [];\nconst links = [\n    {\n        id: \"contacts\",\n        emoji: \"\\uD83D\\uDCD5\",\n        deletable: false,\n        title: \"Contacts\",\n        editable: false,\n        url: \"/space/databases/contacts\",\n        subLinks\n    },\n    {\n        id: \"companies\",\n        emoji: \"\\uD83D\\uDCBC\",\n        deletable: false,\n        title: \"Companies\",\n        editable: false,\n        url: \"/space/companies\",\n        subLinks\n    },\n    {\n        id: \"properties\",\n        emoji: \"\\uD83C\\uDFE0\",\n        deletable: true,\n        title: \"Properties\",\n        editable: true,\n        url: \"/space/properties\",\n        subLinks\n    },\n    {\n        id: \"leases\",\n        emoji: \"\\uD83D\\uDCC4\",\n        deletable: true,\n        title: \"Leases\",\n        editable: true,\n        url: \"/space/leases\",\n        subLinks\n    }\n];\nconst databaseLinkGroup = {\n    id: \"databases\",\n    links,\n    title: \"Databases\"\n};\nconst pageLinkGroup = {\n    id: \"pages\",\n    links,\n    title: \"Shared\"\n};\nconst privatePageLinkGroup = {\n    id: \"pages\",\n    links,\n    title: \"Private\"\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/demo/links.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/resizeImage.ts":
/*!**********************************!*\
  !*** ./src/utils/resizeImage.ts ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScaleMode: function() { return /* binding */ ScaleMode; },\n/* harmony export */   resizeImage: function() { return /* binding */ resizeImage; }\n/* harmony export */ });\nvar ScaleMode;\n(function(ScaleMode) {\n    ScaleMode[\"Fit\"] = \"fit\";\n    ScaleMode[\"Fill\"] = \"fill\";\n})(ScaleMode || (ScaleMode = {}));\nfunction resizeImage(file, dimension, scaleMode, options) {\n    return new Promise((resolve, reject)=>{\n        const img = new Image();\n        img.src = URL.createObjectURL(file);\n        img.onload = ()=>{\n            const canvas = document.createElement(\"canvas\");\n            const { width, height } = dimension;\n            canvas.width = width;\n            canvas.height = height;\n            const ctx = canvas.getContext(\"2d\");\n            let sx, sy, sWidth, sHeight, dx, dy, dWidth, dHeight;\n            const imgAspectRatio = img.width / img.height;\n            const canvasAspectRatio = width / height;\n            if (imgAspectRatio > canvasAspectRatio && scaleMode === \"fit\" || imgAspectRatio < canvasAspectRatio && scaleMode === \"fill\") {\n                // Adjust based on height\n                sHeight = img.height;\n                sWidth = img.height * canvasAspectRatio;\n                sx = (img.width - sWidth) / 2;\n                sy = 0;\n                dx = dy = 0;\n                dWidth = width;\n                dHeight = height;\n            } else {\n                // Adjust based on width\n                sWidth = img.width;\n                sHeight = img.width / canvasAspectRatio;\n                sx = 0;\n                sy = (img.height - sHeight) / 2;\n                dx = dy = 0;\n                dWidth = width;\n                dHeight = height;\n            }\n            ctx.drawImage(img, sx, sy, sWidth, sHeight, dx, dy, dWidth, dHeight);\n            // Use quality parameter if provided (default is 0.92 in most browsers)\n            const quality = (options === null || options === void 0 ? void 0 : options.quality) ? options.quality / 100 : undefined;\n            canvas.toBlob((blob)=>{\n                if (blob) {\n                    const resizedFile = new File([\n                        blob\n                    ], file.name, {\n                        type: blob.type,\n                        lastModified: new Date().getTime()\n                    });\n                    resolve(resizedFile);\n                } else {\n                    reject(new Error(\"Canvas toBlob failed\"));\n                }\n            }, file.type, quality);\n        };\n        img.onerror = (error)=>{\n            reject(error);\n        };\n    });\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/resizeImage.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpTrayIcon.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ArrowUpTrayIcon.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ArrowUpTrayIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n    }));\n}\n_c = ArrowUpTrayIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ArrowUpTrayIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ArrowUpTrayIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpTrayIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction GlobeAltIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418\"\n    }));\n}\n_c = GlobeAltIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(GlobeAltIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"GlobeAltIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js":
/*!********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction PencilIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125\"\n    }));\n}\n_c = PencilIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(PencilIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"PencilIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction PhotoIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n    }));\n}\n_c = PhotoIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(PhotoIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"PhotoIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\n"));

/***/ })

}]);