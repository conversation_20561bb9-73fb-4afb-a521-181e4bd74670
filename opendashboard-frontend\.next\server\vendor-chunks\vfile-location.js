"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/vfile-location";
exports.ids = ["vendor-chunks/vfile-location"];
exports.modules = {

/***/ "(ssr)/./node_modules/vfile-location/lib/index.js":
/*!**************************************************!*\
  !*** ./node_modules/vfile-location/lib/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   location: () => (/* binding */ location)\n/* harmony export */ });\n/**\n * @typedef {import('vfile').VFile} VFile\n * @typedef {import('vfile').Value} Value\n */\n\n/**\n * @typedef Point\n *   unist point, where `line` and `column` can be `undefined`.\n * @property {number | undefined} line\n *   Line.\n * @property {number | undefined} column\n *   Column.\n * @property {number | undefined} [offset]\n *   Offset.\n *\n * @typedef PointLike\n *   unist point, allowed as input.\n * @property {number | null | undefined} [line]\n *   Line.\n * @property {number | null | undefined} [column]\n *   Column.\n * @property {number | null | undefined} [offset]\n *   Offset.\n *\n * @callback ToPoint\n *   Get a line/column-based `point` from `offset`.\n * @param {number | null | undefined} [offset]\n *   Something that should be an `offset.\n * @returns {Point}\n *   Point, line/column are undefined for invalid or out of bounds input.\n *\n * @callback ToOffset\n *   Get an offset from a line/column-based `point`.\n * @param {Point | null | undefined} [point]\n *   Something that should be a `point.\n * @returns {number}\n *   Offset or `-1` for invalid or out of bounds input.\n *\n * @typedef Location\n *   Accessors for index.\n * @property {ToPoint} toPoint\n *   Get a line/column-based `point` from `offset`.\n * @property {ToOffset} toOffset\n *   Get an offset from a line/column-based `point`.\n */\n\n/**\n * Index the given document so you can translate between line/column and offset\n * based positional info.\n *\n * @param {VFile | Value} file\n *   File to index.\n * @returns {Location}\n *   Accessors for index.\n */\nfunction location(file) {\n  const value = String(file)\n  /** @type {Array<number>} */\n  const indices = []\n  const search = /\\r?\\n|\\r/g\n\n  while (search.test(value)) {\n    indices.push(search.lastIndex)\n  }\n\n  indices.push(value.length + 1)\n\n  return {toPoint, toOffset}\n\n  /** @type {ToPoint} */\n  function toPoint(offset) {\n    let index = -1\n\n    if (\n      typeof offset === 'number' &&\n      offset > -1 &&\n      offset < indices[indices.length - 1]\n    ) {\n      while (++index < indices.length) {\n        if (indices[index] > offset) {\n          return {\n            line: index + 1,\n            column: offset - (index > 0 ? indices[index - 1] : 0) + 1,\n            offset\n          }\n        }\n      }\n    }\n\n    return {line: undefined, column: undefined, offset: undefined}\n  }\n\n  /** @type {ToOffset} */\n  function toOffset(point) {\n    const line = point && point.line\n    const column = point && point.column\n\n    if (\n      typeof line === 'number' &&\n      typeof column === 'number' &&\n      !Number.isNaN(line) &&\n      !Number.isNaN(column) &&\n      line - 1 in indices\n    ) {\n      const offset = (indices[line - 2] || 0) + column - 1 || 0\n\n      if (offset > -1 && offset < indices[indices.length - 1]) {\n        return offset\n      }\n    }\n\n    return -1\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vfile-location/lib/index.js\n");

/***/ })

};
;