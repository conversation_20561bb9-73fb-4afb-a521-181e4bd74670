"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx":
/*!****************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/index.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarView: function() { return /* binding */ CalendarView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _providers_template__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/template */ \"(app-pages-browser)/./src/providers/template.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/workspace/main/views/table */ \"(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/providers/stackedPeek */ \"(app-pages-browser)/./src/providers/stackedPeek.tsx\");\n/* harmony import */ var _components_DayView__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./components/DayView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx\");\n/* harmony import */ var _components_WeekView__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./components/WeekView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\");\n/* harmony import */ var _components_MonthView__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./components/MonthView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\");\n/* harmony import */ var _components_CalendarEventItem__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./components/CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/custom-ui/customSelect */ \"(app-pages-browser)/./src/components/custom-ui/customSelect.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./components/CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInMilliseconds/index.js\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInDays/index.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Custom modifier to restrict dragging to the calendar main content area only\nconst restrictToCalendarContainer = (param)=>{\n    let { transform, draggingNodeRect, windowRect } = param;\n    if (!draggingNodeRect || !windowRect) {\n        return transform;\n    }\n    // Find the calendar main content container (the div that contains the views)\n    const calendarContainer = document.querySelector('[data-calendar-content=\"true\"]');\n    if (!calendarContainer) {\n        return transform;\n    }\n    const containerRect = calendarContainer.getBoundingClientRect();\n    // For month view, we need to account for the side card\n    // The side card is positioned on the right side of the calendar\n    const sideCard = document.querySelector('[data-side-card=\"true\"]');\n    let maxX = containerRect.right - draggingNodeRect.width;\n    if (sideCard) {\n        const sideCardRect = sideCard.getBoundingClientRect();\n        // Restrict dragging to not go past the side card\n        maxX = Math.min(maxX, sideCardRect.left - draggingNodeRect.width);\n    }\n    // Find header areas to prevent dragging over them\n    const timeLabels = document.querySelector('[data-time-labels=\"true\"]');\n    const dayHeaders = document.querySelector('[data-day-headers=\"true\"]');\n    const allDayRow = document.querySelector('[data-all-day-row=\"true\"]');\n    // Calculate the boundaries relative to the window\n    let minX = containerRect.left;\n    let minY = containerRect.top;\n    const maxY = containerRect.bottom - draggingNodeRect.height;\n    // Prevent dragging over time labels (left side in day/week view)\n    if (timeLabels) {\n        const timeLabelsRect = timeLabels.getBoundingClientRect();\n        minX = Math.max(minX, timeLabelsRect.right);\n    }\n    // Prevent dragging over day headers (top of calendar)\n    if (dayHeaders) {\n        const dayHeadersRect = dayHeaders.getBoundingClientRect();\n        minY = Math.max(minY, dayHeadersRect.bottom);\n    }\n    // Prevent dragging over all-day row (if present)\n    if (allDayRow) {\n        const allDayRowRect = allDayRow.getBoundingClientRect();\n        minY = Math.max(minY, allDayRowRect.bottom);\n    }\n    // Get current pointer position\n    const currentX = transform.x + draggingNodeRect.left;\n    const currentY = transform.y + draggingNodeRect.top;\n    // Constrain the position\n    const constrainedX = Math.min(Math.max(currentX, minX), maxX);\n    const constrainedY = Math.min(Math.max(currentY, minY), maxY);\n    return {\n        ...transform,\n        x: constrainedX - draggingNodeRect.left,\n        y: constrainedY - draggingNodeRect.top\n    };\n};\n// Custom hook to track previous value\nconst usePrevious = (value)=>{\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        ref.current = value;\n    });\n    return ref.current;\n};\n_s(usePrevious, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nconst CalendarView = (props)=>{\n    _s1();\n    const { databaseStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace)();\n    const { definition } = props;\n    const { accessLevel } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage)();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_16__.useScreenSize)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_17__.useMaybeRecord)();\n    const maybeShared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared)();\n    const maybeTemplate = (0,_providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate)();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [viewType, setViewType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"week\");\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSideCalendar, setShowSideCalendar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!isMobile);\n    const [activeDragData, setActiveDragData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const savedScrollTop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const pointerCoordinates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const isInRecordTab = !!maybeRecord;\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const databaseId = definition.databaseId;\n    const database = databaseStore[definition.databaseId];\n    const isPublishedView = !!maybeShared;\n    const editable = !definition.lockContent && !isPublishedView && !!accessLevel;\n    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    const { createRecords, updateRecordValues, setPeekRecordId, peekRecordId, refreshDatabase, deleteRecords } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews)();\n    const { sorts, filter, search } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering)();\n    const { selectedIds, setSelectedIds } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewSelection)();\n    const prevPeekRecordId = usePrevious(peekRecordId);\n    const { openRecord } = (0,_providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__.useStackedPeek)();\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.PointerSensor, {\n        activationConstraint: {\n            distance: 8\n        }\n    }), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.TouchSensor, {\n        activationConstraint: {\n            delay: 150,\n            tolerance: 5\n        }\n    }));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n        const container = document.getElementById(containerId);\n        if (container) {\n            requestAnimationFrame(()=>{\n                container.scrollTop = savedScrollTop.current;\n            });\n        }\n    }, [\n        selectedEvent,\n        viewType\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowSideCalendar(!isMobile);\n    }, [\n        isMobile\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only refresh if the peek view was open and is now closed.\n        if (prevPeekRecordId && !peekRecordId) {\n            console.log(\"Peek view was closed, refreshing calendar data\");\n            refreshDatabase(definition.databaseId);\n        }\n    }, [\n        peekRecordId,\n        prevPeekRecordId,\n        definition.databaseId,\n        refreshDatabase\n    ]);\n    if (!database) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__.PageLoader, {\n        size: \"full\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 197,\n        columnNumber: 25\n    }, undefined);\n    const getEvents = ()=>{\n        var _workspace_workspaceMember, _database_database;\n        if (!database) return [];\n        const { rows } = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.filterAndSortRecords)(database, members, databaseStore, definition.filter || {\n            match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All,\n            conditions: []\n        }, filter, sorts.length ? sorts : definition.sorts || [], (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspaceMember = workspace.workspaceMember) === null || _workspace_workspaceMember === void 0 ? void 0 : _workspace_workspaceMember.userId) || \"\", (database === null || database === void 0 ? void 0 : (_database_database = database.database) === null || _database_database === void 0 ? void 0 : _database_database.id) || \"\");\n        const filteredRows = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.searchFilteredRecords)(search || \"\", rows);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        return filteredRows.map((row)=>{\n            const startValue = row.processedRecord.processedRecordValues[definition.eventStartColumnId];\n            let startDate;\n            if (startValue && typeof startValue === \"string\") {\n                startDate = new Date(startValue);\n            } else {\n                startDate = new Date();\n            }\n            let endDate;\n            if (definition.eventEndColumnId) {\n                const endValue = row.processedRecord.processedRecordValues[definition.eventEndColumnId];\n                if (endValue && typeof endValue === \"string\") {\n                    endDate = new Date(endValue);\n                } else {\n                    endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n                }\n            } else {\n                endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n            }\n            const title = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getRecordTitle)(row.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts);\n            return {\n                id: row.id,\n                title,\n                start: startDate,\n                end: endDate,\n                record: row.record,\n                processedRecord: row.processedRecord\n            };\n        });\n    };\n    const getFilteredEvents = ()=>{\n        const baseEvents = getEvents();\n        if (!searchTerm.trim()) {\n            return baseEvents;\n        }\n        return baseEvents.filter((event)=>{\n            const searchLower = searchTerm.toLowerCase();\n            return event.title.toLowerCase().includes(searchLower);\n        });\n    };\n    const onDragStart = (event)=>{\n        if (event.active.data.current) {\n            var _event_active_rect_current_translated, _event_active_rect_current, _event_active_rect_current_translated1, _event_active_rect_current_translated2;\n            const initialPointerY = pointerCoordinates.current.y;\n            var _event_active_rect_current_translated_top;\n            const initialEventTop = (_event_active_rect_current_translated_top = (_event_active_rect_current = event.active.rect.current) === null || _event_active_rect_current === void 0 ? void 0 : (_event_active_rect_current_translated = _event_active_rect_current.translated) === null || _event_active_rect_current_translated === void 0 ? void 0 : _event_active_rect_current_translated.top) !== null && _event_active_rect_current_translated_top !== void 0 ? _event_active_rect_current_translated_top : 0;\n            const grabOffsetY = initialPointerY - initialEventTop;\n            // Get the exact dimensions from the DOM element\n            // Handle both event and segment types\n            const { payload, type } = event.active.data.current;\n            const eventId = type === \"segment\" ? payload.originalEvent.id : payload.id;\n            const draggedElement = document.getElementById(\"event-\".concat(eventId));\n            const width = draggedElement ? draggedElement.offsetWidth : (_event_active_rect_current_translated1 = event.active.rect.current.translated) === null || _event_active_rect_current_translated1 === void 0 ? void 0 : _event_active_rect_current_translated1.width;\n            const height = draggedElement ? draggedElement.offsetHeight : (_event_active_rect_current_translated2 = event.active.rect.current.translated) === null || _event_active_rect_current_translated2 === void 0 ? void 0 : _event_active_rect_current_translated2.height;\n            // Debug logging\n            console.log(\"[DragStart]\", {\n                activeId: event.active.id,\n                type,\n                eventId,\n                elementFound: !!draggedElement,\n                width,\n                height\n            });\n            setActiveDragData({\n                ...event.active.data.current,\n                grabOffsetY,\n                width,\n                height\n            });\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"touchmove\", handleTouchMove);\n        }\n    };\n    const onDragEnd = async (param)=>{\n        let { active, over } = param;\n        document.removeEventListener(\"mousemove\", handleMouseMove);\n        document.removeEventListener(\"touchmove\", handleTouchMove);\n        setActiveDragData(null);\n        // Debug logging\n        console.log(\"[DragEnd]\", {\n            active: active === null || active === void 0 ? void 0 : active.id,\n            over: over === null || over === void 0 ? void 0 : over.id,\n            canEditData,\n            activeId: active === null || active === void 0 ? void 0 : active.id,\n            overId: over === null || over === void 0 ? void 0 : over.id\n        });\n        if (!over || !active || !canEditData || active.id === over.id) {\n            console.log(\"[DragEnd] Early return:\", {\n                over: !!over,\n                active: !!active,\n                canEditData,\n                sameId: (active === null || active === void 0 ? void 0 : active.id) === (over === null || over === void 0 ? void 0 : over.id)\n            });\n            return;\n        }\n        const activeData = active.data.current;\n        const overData = over.data.current;\n        if (!activeData || !overData) {\n            return;\n        }\n        // Additional check: ensure we're only dropping on valid drop zones\n        const validDropTypes = [\n            \"timeslot-day\",\n            \"timeslot-week\",\n            \"allday-day\",\n            \"allday-week\",\n            \"daycell\"\n        ];\n        if (!validDropTypes.includes(overData.type)) {\n            return;\n        }\n        const { payload, type } = activeData;\n        const eventToUpdate = type === \"segment\" ? payload.originalEvent : payload;\n        // Ensure we're updating the correct event\n        if (!eventToUpdate || !eventToUpdate.id) {\n            return;\n        }\n        const originalStart = new Date(eventToUpdate.start);\n        const originalEnd = new Date(eventToUpdate.end);\n        const duration = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_27__[\"default\"])(originalEnd, originalStart);\n        let newStart;\n        if (overData.type.startsWith(\"allday\")) {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(originalStart, dayDifference);\n            // For all-day drops, normalize the time to the start of the day\n            newStart.setHours(0, 0, 0, 0);\n        } else if (overData.type.startsWith(\"timeslot\")) {\n            newStart = new Date(overData.date);\n            // Enhanced Minute-level precision calculation\n            const grabOffsetY = activeData.grabOffsetY || 0;\n            const slotTop = over.rect.top;\n            const pointerY = pointerCoordinates.current.y;\n            // Adjust pointer position based on initial grab point\n            const adjustedPointerY = pointerY - grabOffsetY;\n            const offsetY = adjustedPointerY - slotTop;\n            const slotHeight = over.rect.height; // Should be 60px\n            const minutesFromTop = offsetY / slotHeight * 60;\n            // Snap to nearest 5-minute interval\n            const snappedMinutes = Math.round(minutesFromTop / 5) * 5;\n            const newMinutes = Math.max(0, Math.min(59, snappedMinutes));\n            newStart.setHours(overData.hour, newMinutes, originalStart.getSeconds(), originalStart.getMilliseconds());\n        } else if (overData.type === \"daycell\") {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(originalStart, dayDifference);\n        } else {\n            return; // Invalid drop target\n        }\n        const newEnd = new Date(newStart.getTime() + duration);\n        const recordId = eventToUpdate.record.id;\n        const newValues = {\n            [definition.eventStartColumnId]: newStart.toISOString(),\n            ...definition.eventEndColumnId && {\n                [definition.eventEndColumnId]: newEnd.toISOString()\n            }\n        };\n        try {\n            await updateRecordValues(definition.databaseId, [\n                recordId\n            ], newValues);\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.success('Event \"'.concat(eventToUpdate.title, '\" updated.'));\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to update event.\");\n            console.error(\"Error updating event:\", error);\n        }\n    };\n    const handleMouseMove = (event)=>{\n        pointerCoordinates.current = {\n            x: event.clientX,\n            y: event.clientY\n        };\n    };\n    const handleTouchMove = (event)=>{\n        const touch = event.touches[0];\n        pointerCoordinates.current = {\n            x: touch.clientX,\n            y: touch.clientY\n        };\n    };\n    // Custom collision detection that prioritizes drop zones over events\n    const customCollisionDetection = (args)=>{\n        const { droppableContainers, active } = args;\n        // Filter out event and segment droppables, only keep valid drop zones\n        const validDropZones = Array.from(droppableContainers.values()).filter((container)=>{\n            const data = container.data.current;\n            return data && [\n                \"timeslot-day\",\n                \"timeslot-week\",\n                \"allday-day\",\n                \"allday-week\",\n                \"daycell\"\n            ].includes(data.type);\n        });\n        // Debug logging\n        console.log(\"[CollisionDetection]\", {\n            totalContainers: droppableContainers.size,\n            validDropZones: validDropZones.length,\n            activeId: active === null || active === void 0 ? void 0 : active.id\n        });\n        // If no valid drop zones found, fall back to default collision detection\n        if (validDropZones.length === 0) {\n            console.log(\"[CollisionDetection] No valid drop zones, using pointerWithin\");\n            return (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.pointerWithin)(args);\n        }\n        // Use pointer-based collision detection on filtered containers\n        const collisions = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.pointerWithin)({\n            ...args,\n            droppableContainers: new Map(validDropZones.map((container)=>[\n                    container.id,\n                    container\n                ]))\n        });\n        console.log(\"[CollisionDetection] pointerWithin collisions:\", collisions.length);\n        // If no collisions found with pointerWithin, try rectIntersection for better accuracy\n        if (collisions.length === 0) {\n            const rectCollisions = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.rectIntersection)({\n                ...args,\n                droppableContainers: new Map(validDropZones.map((container)=>[\n                        container.id,\n                        container\n                    ]))\n            });\n            console.log(\"[CollisionDetection] rectIntersection collisions:\", rectCollisions.length);\n            return rectCollisions;\n        }\n        return collisions;\n    };\n    const events = getFilteredEvents();\n    const goToToday = ()=>setSelectedDate(new Date());\n    const goToPrevious = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(prevDate, -1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const goToNext = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(prevDate, 1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const handleRequestCreateEvent = function(date) {\n        let useSystemTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (useSystemTime) {\n            const now = new Date();\n            const newDate = new Date(date);\n            newDate.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());\n            handleCreateEvent(newDate);\n        } else {\n            handleCreateEvent(date);\n        }\n    };\n    const handleCreateEvent = async function() {\n        let date = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : new Date();\n        if (!canEditData) return;\n        const startTime = new Date(date);\n        const endTime = new Date(startTime.getTime() + (definition.defaultDuration || 30) * 60000);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        try {\n            const recordValues = {\n                [definition.eventStartColumnId]: startTime.toISOString(),\n                ...definition.eventEndColumnId && {\n                    [definition.eventEndColumnId]: endTime.toISOString()\n                }\n            };\n            if (titleColOpts.titleColId) {\n                recordValues[titleColOpts.titleColId] = \"New Event\";\n            }\n            const result = await createRecords(definition.databaseId, [\n                recordValues\n            ]);\n            if (result && result.records && result.records.length > 0) {\n                const newRecordId = result.records[0].id;\n                if (newRecordId) {\n                    await refreshDatabase(definition.databaseId);\n                    setPeekRecordId(newRecordId);\n                    sonner__WEBPACK_IMPORTED_MODULE_14__.toast.success(\"New event created\");\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Error accessing the new event\");\n                }\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to create event properly\");\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to create event\");\n        }\n    };\n    const handleEventClick = (event)=>{\n        if (event && event.id) {\n            const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n            const container = document.getElementById(containerId);\n            if (container) {\n                savedScrollTop.current = container.scrollTop;\n            }\n            openRecord(event.id, event.record.databaseId);\n            setSelectedEvent(event.id);\n        }\n    };\n    const getHeaderDateDisplay = ()=>{\n        switch(viewType){\n            case \"day\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n            case \"week\":\n                return \"\".concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(selectedDate, -selectedDate.getDay()), \"MMM d\"), \" - \").concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(selectedDate, 6 - selectedDate.getDay()), \"MMM d, yyyy\"));\n            case \"month\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM yyyy\");\n            default:\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n        }\n    };\n    const handleEventDelete = async (event)=>{\n        if (!canEditData) return;\n        try {\n            await deleteRecords(database.database.id, [\n                event.record.id\n            ]);\n        } catch (error) {\n            console.error(\"Error deleting event:\", error);\n        }\n    };\n    const viewTypeOptions = [\n        {\n            id: \"day\",\n            value: \"day\",\n            title: \"Day\",\n            data: \"day\"\n        },\n        {\n            id: \"week\",\n            value: \"week\",\n            title: \"Week\",\n            data: \"week\"\n        },\n        {\n            id: \"month\",\n            value: \"month\",\n            title: \"Month\",\n            data: \"month\"\n        }\n    ];\n    const selectedViewOption = viewType === \"day\" ? [\n        \"day\"\n    ] : viewType === \"week\" ? [\n        \"week\"\n    ] : [\n        \"month\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"border-b border-neutral-300 bg-white\", isInRecordTab && \"py-1\"),\n                children: [\n                    isMobile ? /* Mobile Header Layout */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"p-2\", isInRecordTab && \"py-1\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black truncate flex-1 mr-2\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(!showSideCalendar),\n                                        className: \"rounded-full h-8 px-3 text-xs text-black hover:bg-gray-50\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 588,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 584,\n                                columnNumber: 12\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToToday,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: \"Today\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToPrevious,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleLeftIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 18\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToNext,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleRightIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 628,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 620,\n                                                        columnNumber: 18\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                                options: viewTypeOptions,\n                                                selectedIds: selectedViewOption,\n                                                onChange: (selected)=>{\n                                                    if (selected.length > 0) {\n                                                        setViewType(selected[0]);\n                                                    }\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black w-20\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                placeholder: \"View\",\n                                                hideSearch: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 659,\n                                                    columnNumber: 20\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 18\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 597,\n                                columnNumber: 12\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 583,\n                        columnNumber: 10\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"flex items-center justify-between px-4\", isInRecordTab ? \"py-1\" : \"py-2\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: goToToday,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 671,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToPrevious,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleLeftIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToNext,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleRightIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 700,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 692,\n                                                columnNumber: 14\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 681,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 670,\n                                columnNumber: 10\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                placeholder: \"Search events...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-48 pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                                className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 718,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 711,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                        options: viewTypeOptions,\n                                        selectedIds: selectedViewOption,\n                                        onChange: (selected)=>{\n                                            if (selected.length > 0) {\n                                                setViewType(selected[0]);\n                                            }\n                                        },\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6 w-20\" : \"h-8 w-28\"),\n                                        placeholder: \"View\",\n                                        hideSearch: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 722,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black gap-1\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 746,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Add Event\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 35\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 739,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 709,\n                                columnNumber: 10\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 666,\n                        columnNumber: 8\n                    }, undefined),\n                    isMobile && !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                    placeholder: \"Search events...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"w-full pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 757,\n                                    columnNumber: 12\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                    className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 763,\n                                    columnNumber: 12\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                            lineNumber: 756,\n                            columnNumber: 10\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 755,\n                        columnNumber: 8\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 577,\n                columnNumber: 6\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex min-h-0\",\n                children: [\n                    showSideCalendar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"flex-none bg-white\", isMobile ? \"w-full absolute z-50 backdrop-blur-sm h-full shadow-lg\" : \"w-fit border-r border-neutral-300\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 border-b border-neutral-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 777,\n                                        columnNumber: 10\n                                    }, undefined),\n                                    isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(false),\n                                        className: \"rounded-full h-8 w-8 p-1 text-black hover:bg-neutral-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Close\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 784,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            \"\\xd7\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 779,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 776,\n                                columnNumber: 8\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                mode: \"single\",\n                                selected: selectedDate,\n                                onSelect: (date)=>{\n                                    if (date) {\n                                        setSelectedDate(date);\n                                        if (isMobile) {\n                                            setShowSideCalendar(false);\n                                        }\n                                    }\n                                },\n                                className: \"rounded-md border-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 789,\n                                columnNumber: 8\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 772,\n                        columnNumber: 6\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DndContext, {\n                        sensors: sensors,\n                        onDragStart: onDragStart,\n                        onDragEnd: onDragEnd,\n                        modifiers: [\n                            restrictToCalendarContainer\n                        ],\n                        collisionDetection: customCollisionDetection,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                \"data-calendar-content\": \"true\",\n                                children: [\n                                    viewType === \"day\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DayView__WEBPACK_IMPORTED_MODULE_19__.DayView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 814,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"week\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WeekView__WEBPACK_IMPORTED_MODULE_20__.WeekView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 827,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"month\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MonthView__WEBPACK_IMPORTED_MODULE_21__.MonthView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: (date)=>handleRequestCreateEvent(date, true),\n                                        canEditData: canEditData,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 841,\n                                        columnNumber: 7\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 812,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DragOverlay, {\n                                dropAnimation: null,\n                                children: activeDragData && activeDragData.type === \"segment\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_26__.CalendarEventSegment, {\n                                    segment: activeDragData.payload,\n                                    view: viewType === \"day\" ? \"day\" : \"week\",\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height,\n                                        position: \"relative\",\n                                        transform: \"none\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 857,\n                                    columnNumber: 11\n                                }, undefined) : activeDragData && activeDragData.type === \"event\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CalendarEventItem__WEBPACK_IMPORTED_MODULE_22__.CalendarEventItem, {\n                                    event: activeDragData.payload,\n                                    view: viewType,\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height,\n                                        position: \"relative\",\n                                        transform: \"none\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 869,\n                                    columnNumber: 11\n                                }, undefined) : null\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 855,\n                                columnNumber: 6\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 805,\n                        columnNumber: 6\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 770,\n                columnNumber: 4\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 576,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CalendarView, \"gC+3ORgbOSOWj17lR2zBDE1vWrs=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace,\n        _providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage,\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_16__.useScreenSize,\n        _providers_record__WEBPACK_IMPORTED_MODULE_17__.useMaybeRecord,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared,\n        _providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewSelection,\n        usePrevious,\n        _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__.useStackedPeek,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors\n    ];\n});\n_c = CalendarView;\nvar _c;\n$RefreshReg$(_c, \"CalendarView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx\n"));

/***/ })

});