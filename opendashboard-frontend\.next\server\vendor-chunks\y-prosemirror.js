"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/y-prosemirror";
exports.ids = ["vendor-chunks/y-prosemirror"];
exports.modules = {

/***/ "(ssr)/./node_modules/y-prosemirror/src/lib.js":
/*!***********************************************!*\
  !*** ./node_modules/y-prosemirror/src/lib.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   absolutePositionToRelativePosition: () => (/* binding */ absolutePositionToRelativePosition),\n/* harmony export */   initProseMirrorDoc: () => (/* binding */ initProseMirrorDoc),\n/* harmony export */   prosemirrorJSONToYDoc: () => (/* binding */ prosemirrorJSONToYDoc),\n/* harmony export */   prosemirrorJSONToYXmlFragment: () => (/* binding */ prosemirrorJSONToYXmlFragment),\n/* harmony export */   prosemirrorToYDoc: () => (/* binding */ prosemirrorToYDoc),\n/* harmony export */   prosemirrorToYXmlFragment: () => (/* binding */ prosemirrorToYXmlFragment),\n/* harmony export */   relativePositionToAbsolutePosition: () => (/* binding */ relativePositionToAbsolutePosition),\n/* harmony export */   setMeta: () => (/* binding */ setMeta),\n/* harmony export */   yDocToProsemirror: () => (/* binding */ yDocToProsemirror),\n/* harmony export */   yDocToProsemirrorJSON: () => (/* binding */ yDocToProsemirrorJSON),\n/* harmony export */   yXmlFragmentToProseMirrorFragment: () => (/* binding */ yXmlFragmentToProseMirrorFragment),\n/* harmony export */   yXmlFragmentToProseMirrorRootNode: () => (/* binding */ yXmlFragmentToProseMirrorRootNode),\n/* harmony export */   yXmlFragmentToProsemirror: () => (/* binding */ yXmlFragmentToProsemirror),\n/* harmony export */   yXmlFragmentToProsemirrorJSON: () => (/* binding */ yXmlFragmentToProsemirrorJSON)\n/* harmony export */ });\n/* harmony import */ var _plugins_sync_plugin_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./plugins/sync-plugin.js */ \"(ssr)/./node_modules/y-prosemirror/src/plugins/sync-plugin.js\");\n/* harmony import */ var _plugins_keys_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./plugins/keys.js */ \"(ssr)/./node_modules/y-prosemirror/src/plugins/keys.js\");\n/* harmony import */ var yjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! yjs */ \"(ssr)/./node_modules/yjs/dist/yjs.mjs\");\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/./node_modules/prosemirror-model/dist/index.js\");\n/* harmony import */ var lib0_error__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lib0/error */ \"(ssr)/./node_modules/lib0/error.js\");\n/* harmony import */ var lib0_map__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lib0/map */ \"(ssr)/./node_modules/lib0/map.js\");\n/* harmony import */ var lib0_eventloop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lib0/eventloop */ \"(ssr)/./node_modules/lib0/eventloop.js\");\n // eslint-disable-line\n\n\n // eslint-disable-line\n // eslint-disable-line\n\n\n\n\n/**\n * Either a node if type is YXmlElement or an Array of text nodes if YXmlText\n * @typedef {Map<Y.AbstractType, Node | Array<Node>>} ProsemirrorMapping\n */\n\n/**\n * Is null if no timeout is in progress.\n * Is defined if a timeout is in progress.\n * Maps from view\n * @type {Map<EditorView, Map<any, any>>|null}\n */\nlet viewsToUpdate = null\n\nconst updateMetas = () => {\n  const ups = /** @type {Map<EditorView, Map<any, any>>} */ (viewsToUpdate)\n  viewsToUpdate = null\n  ups.forEach((metas, view) => {\n    const tr = view.state.tr\n    const syncState = _plugins_keys_js__WEBPACK_IMPORTED_MODULE_0__.ySyncPluginKey.getState(view.state)\n    if (syncState && syncState.binding && !syncState.binding.isDestroyed) {\n      metas.forEach((val, key) => {\n        tr.setMeta(key, val)\n      })\n      view.dispatch(tr)\n    }\n  })\n}\n\nconst setMeta = (view, key, value) => {\n  if (!viewsToUpdate) {\n    viewsToUpdate = new Map()\n    lib0_eventloop__WEBPACK_IMPORTED_MODULE_1__.timeout(0, updateMetas)\n  }\n  lib0_map__WEBPACK_IMPORTED_MODULE_2__.setIfUndefined(viewsToUpdate, view, lib0_map__WEBPACK_IMPORTED_MODULE_2__.create).set(key, value)\n}\n\n/**\n * Transforms a Prosemirror based absolute position to a Yjs Cursor (relative position in the Yjs model).\n *\n * @param {number} pos\n * @param {Y.XmlFragment} type\n * @param {ProsemirrorMapping} mapping\n * @return {any} relative position\n */\nconst absolutePositionToRelativePosition = (pos, type, mapping) => {\n  if (pos === 0) {\n    return yjs__WEBPACK_IMPORTED_MODULE_3__.createRelativePositionFromTypeIndex(type, 0, -1)\n  }\n  /**\n   * @type {any}\n   */\n  let n = type._first === null ? null : /** @type {Y.ContentType} */ (type._first.content).type\n  while (n !== null && type !== n) {\n    if (n instanceof yjs__WEBPACK_IMPORTED_MODULE_3__.XmlText) {\n      if (n._length >= pos) {\n        return yjs__WEBPACK_IMPORTED_MODULE_3__.createRelativePositionFromTypeIndex(n, pos, -1)\n      } else {\n        pos -= n._length\n      }\n      if (n._item !== null && n._item.next !== null) {\n        n = /** @type {Y.ContentType} */ (n._item.next.content).type\n      } else {\n        do {\n          n = n._item === null ? null : n._item.parent\n          pos--\n        } while (n !== type && n !== null && n._item !== null && n._item.next === null)\n        if (n !== null && n !== type) {\n          // @ts-gnore we know that n.next !== null because of above loop conditition\n          n = n._item === null ? null : /** @type {Y.ContentType} */ (/** @type Y.Item */ (n._item.next).content).type\n        }\n      }\n    } else {\n      const pNodeSize = /** @type {any} */ (mapping.get(n) || { nodeSize: 0 }).nodeSize\n      if (n._first !== null && pos < pNodeSize) {\n        n = /** @type {Y.ContentType} */ (n._first.content).type\n        pos--\n      } else {\n        if (pos === 1 && n._length === 0 && pNodeSize > 1) {\n          // edge case, should end in this paragraph\n          return new yjs__WEBPACK_IMPORTED_MODULE_3__.RelativePosition(n._item === null ? null : n._item.id, n._item === null ? yjs__WEBPACK_IMPORTED_MODULE_3__.findRootTypeKey(n) : null, null)\n        }\n        pos -= pNodeSize\n        if (n._item !== null && n._item.next !== null) {\n          n = /** @type {Y.ContentType} */ (n._item.next.content).type\n        } else {\n          if (pos === 0) {\n            // set to end of n.parent\n            n = n._item === null ? n : n._item.parent\n            return new yjs__WEBPACK_IMPORTED_MODULE_3__.RelativePosition(n._item === null ? null : n._item.id, n._item === null ? yjs__WEBPACK_IMPORTED_MODULE_3__.findRootTypeKey(n) : null, null)\n          }\n          do {\n            n = /** @type {Y.Item} */ (n._item).parent\n            pos--\n          } while (n !== type && /** @type {Y.Item} */ (n._item).next === null)\n          // if n is null at this point, we have an unexpected case\n          if (n !== type) {\n            // We know that n._item.next is defined because of above loop condition\n            n = /** @type {Y.ContentType} */ (/** @type {Y.Item} */ (/** @type {Y.Item} */ (n._item).next).content).type\n          }\n        }\n      }\n    }\n    if (n === null) {\n      throw lib0_error__WEBPACK_IMPORTED_MODULE_4__.unexpectedCase()\n    }\n    if (pos === 0 && n.constructor !== yjs__WEBPACK_IMPORTED_MODULE_3__.XmlText && n !== type) { // TODO: set to <= 0\n      return createRelativePosition(n._item.parent, n._item)\n    }\n  }\n  return yjs__WEBPACK_IMPORTED_MODULE_3__.createRelativePositionFromTypeIndex(type, type._length, -1)\n}\n\nconst createRelativePosition = (type, item) => {\n  let typeid = null\n  let tname = null\n  if (type._item === null) {\n    tname = yjs__WEBPACK_IMPORTED_MODULE_3__.findRootTypeKey(type)\n  } else {\n    typeid = yjs__WEBPACK_IMPORTED_MODULE_3__.createID(type._item.id.client, type._item.id.clock)\n  }\n  return new yjs__WEBPACK_IMPORTED_MODULE_3__.RelativePosition(typeid, tname, item.id)\n}\n\n/**\n * @param {Y.Doc} y\n * @param {Y.XmlFragment} documentType Top level type that is bound to pView\n * @param {any} relPos Encoded Yjs based relative position\n * @param {ProsemirrorMapping} mapping\n * @return {null|number}\n */\nconst relativePositionToAbsolutePosition = (y, documentType, relPos, mapping) => {\n  const decodedPos = yjs__WEBPACK_IMPORTED_MODULE_3__.createAbsolutePositionFromRelativePosition(relPos, y)\n  if (decodedPos === null || (decodedPos.type !== documentType && !yjs__WEBPACK_IMPORTED_MODULE_3__.isParentOf(documentType, decodedPos.type._item))) {\n    return null\n  }\n  let type = decodedPos.type\n  let pos = 0\n  if (type.constructor === yjs__WEBPACK_IMPORTED_MODULE_3__.XmlText) {\n    pos = decodedPos.index\n  } else if (type._item === null || !type._item.deleted) {\n    let n = type._first\n    let i = 0\n    while (i < type._length && i < decodedPos.index && n !== null) {\n      if (!n.deleted) {\n        const t = /** @type {Y.ContentType} */ (n.content).type\n        i++\n        if (t instanceof yjs__WEBPACK_IMPORTED_MODULE_3__.XmlText) {\n          pos += t._length\n        } else {\n          pos += /** @type {any} */ (mapping.get(t)).nodeSize\n        }\n      }\n      n = /** @type {Y.Item} */ (n.right)\n    }\n    pos += 1 // increase because we go out of n\n  }\n  while (type !== documentType && type._item !== null) {\n    // @ts-ignore\n    const parent = type._item.parent\n    // @ts-ignore\n    if (parent._item === null || !parent._item.deleted) {\n      pos += 1 // the start tag\n      let n = /** @type {Y.AbstractType} */ (parent)._first\n      // now iterate until we found type\n      while (n !== null) {\n        const contentType = /** @type {Y.ContentType} */ (n.content).type\n        if (contentType === type) {\n          break\n        }\n        if (!n.deleted) {\n          if (contentType instanceof yjs__WEBPACK_IMPORTED_MODULE_3__.XmlText) {\n            pos += contentType._length\n          } else {\n            pos += /** @type {any} */ (mapping.get(contentType)).nodeSize\n          }\n        }\n        n = n.right\n      }\n    }\n    type = /** @type {Y.AbstractType} */ (parent)\n  }\n  return pos - 1 // we don't count the most outer tag, because it is a fragment\n}\n\n/**\n * Utility function for converting an Y.Fragment to a ProseMirror fragment.\n *\n * @param {Y.XmlFragment} yXmlFragment\n * @param {Schema} schema\n */\nconst yXmlFragmentToProseMirrorFragment = (yXmlFragment, schema) => {\n  const fragmentContent = yXmlFragment.toArray().map((t) =>\n    (0,_plugins_sync_plugin_js__WEBPACK_IMPORTED_MODULE_5__.createNodeFromYElement)(\n      /** @type {Y.XmlElement} */ (t),\n      schema,\n      new Map()\n    )\n  ).filter((n) => n !== null)\n  return prosemirror_model__WEBPACK_IMPORTED_MODULE_6__.Fragment.fromArray(fragmentContent)\n}\n\n/**\n * Utility function for converting an Y.Fragment to a ProseMirror node.\n *\n * @param {Y.XmlFragment} yXmlFragment\n * @param {Schema} schema\n */\nconst yXmlFragmentToProseMirrorRootNode = (yXmlFragment, schema) =>\n  schema.topNodeType.create(null, yXmlFragmentToProseMirrorFragment(yXmlFragment, schema))\n\n/**\n * The initial ProseMirror content should be supplied by Yjs. This function transforms a Y.Fragment\n * to a ProseMirror Doc node and creates a mapping that is used by the sync plugin.\n *\n * @param {Y.XmlFragment} yXmlFragment\n * @param {Schema} schema\n */\nconst initProseMirrorDoc = (yXmlFragment, schema) => {\n  /**\n   * @type {ProsemirrorMapping}\n   */\n  const mapping = new Map()\n  const fragmentContent = yXmlFragment.toArray().map((t) =>\n    (0,_plugins_sync_plugin_js__WEBPACK_IMPORTED_MODULE_5__.createNodeFromYElement)(\n      /** @type {Y.XmlElement} */ (t),\n      schema,\n      mapping\n    )\n  ).filter((n) => n !== null)\n  const doc = schema.topNodeType.create(null, prosemirror_model__WEBPACK_IMPORTED_MODULE_6__.Fragment.fromArray(fragmentContent))\n  return { doc, mapping }\n}\n\n/**\n * Utility method to convert a Prosemirror Doc Node into a Y.Doc.\n *\n * This can be used when importing existing content to Y.Doc for the first time,\n * note that this should not be used to rehydrate a Y.Doc from a database once\n * collaboration has begun as all history will be lost\n *\n * @param {Node} doc\n * @param {string} xmlFragment\n * @return {Y.Doc}\n */\nfunction prosemirrorToYDoc (doc, xmlFragment = 'prosemirror') {\n  const ydoc = new yjs__WEBPACK_IMPORTED_MODULE_3__.Doc()\n  const type = /** @type {Y.XmlFragment} */ (ydoc.get(xmlFragment, yjs__WEBPACK_IMPORTED_MODULE_3__.XmlFragment))\n  if (!type.doc) {\n    return ydoc\n  }\n\n  prosemirrorToYXmlFragment(doc, type)\n  return type.doc\n}\n\n/**\n * Utility method to update an empty Y.XmlFragment with content from a Prosemirror Doc Node.\n *\n * This can be used when importing existing content to Y.Doc for the first time,\n * note that this should not be used to rehydrate a Y.Doc from a database once\n * collaboration has begun as all history will be lost\n *\n * Note: The Y.XmlFragment does not need to be part of a Y.Doc document at the time that this\n * method is called, but it must be added before any other operations are performed on it.\n *\n * @param {Node} doc prosemirror document.\n * @param {Y.XmlFragment} [xmlFragment] If supplied, an xml fragment to be\n *   populated from the prosemirror state; otherwise a new XmlFragment will be created.\n * @return {Y.XmlFragment}\n */\nfunction prosemirrorToYXmlFragment (doc, xmlFragment) {\n  const type = xmlFragment || new yjs__WEBPACK_IMPORTED_MODULE_3__.XmlFragment()\n  const ydoc = type.doc ? type.doc : { transact: (transaction) => transaction(undefined) }\n  ;(0,_plugins_sync_plugin_js__WEBPACK_IMPORTED_MODULE_5__.updateYFragment)(ydoc, type, doc, new Map())\n  return type\n}\n\n/**\n * Utility method to convert Prosemirror compatible JSON into a Y.Doc.\n *\n * This can be used when importing existing content to Y.Doc for the first time,\n * note that this should not be used to rehydrate a Y.Doc from a database once\n * collaboration has begun as all history will be lost\n *\n * @param {Schema} schema\n * @param {any} state\n * @param {string} xmlFragment\n * @return {Y.Doc}\n */\nfunction prosemirrorJSONToYDoc (schema, state, xmlFragment = 'prosemirror') {\n  const doc = prosemirror_model__WEBPACK_IMPORTED_MODULE_6__.Node.fromJSON(schema, state)\n  return prosemirrorToYDoc(doc, xmlFragment)\n}\n\n/**\n * Utility method to convert Prosemirror compatible JSON to a Y.XmlFragment\n *\n * This can be used when importing existing content to Y.Doc for the first time,\n * note that this should not be used to rehydrate a Y.Doc from a database once\n * collaboration has begun as all history will be lost\n *\n * @param {Schema} schema\n * @param {any} state\n * @param {Y.XmlFragment} [xmlFragment] If supplied, an xml fragment to be\n *   populated from the prosemirror state; otherwise a new XmlFragment will be created.\n * @return {Y.XmlFragment}\n */\nfunction prosemirrorJSONToYXmlFragment (schema, state, xmlFragment) {\n  const doc = prosemirror_model__WEBPACK_IMPORTED_MODULE_6__.Node.fromJSON(schema, state)\n  return prosemirrorToYXmlFragment(doc, xmlFragment)\n}\n\n/**\n * @deprecated Use `yXmlFragmentToProseMirrorRootNode` instead\n *\n * Utility method to convert a Y.Doc to a Prosemirror Doc node.\n *\n * @param {Schema} schema\n * @param {Y.Doc} ydoc\n * @return {Node}\n */\nfunction yDocToProsemirror (schema, ydoc) {\n  const state = yDocToProsemirrorJSON(ydoc)\n  return prosemirror_model__WEBPACK_IMPORTED_MODULE_6__.Node.fromJSON(schema, state)\n}\n\n/**\n *\n * @deprecated Use `yXmlFragmentToProseMirrorRootNode` instead\n *\n * Utility method to convert a Y.XmlFragment to a Prosemirror Doc node.\n *\n * @param {Schema} schema\n * @param {Y.XmlFragment} xmlFragment\n * @return {Node}\n */\nfunction yXmlFragmentToProsemirror (schema, xmlFragment) {\n  const state = yXmlFragmentToProsemirrorJSON(xmlFragment)\n  return prosemirror_model__WEBPACK_IMPORTED_MODULE_6__.Node.fromJSON(schema, state)\n}\n\n/**\n *\n * @deprecated Use `yXmlFragmentToProseMirrorRootNode` instead\n *\n * Utility method to convert a Y.Doc to Prosemirror compatible JSON.\n *\n * @param {Y.Doc} ydoc\n * @param {string} xmlFragment\n * @return {Record<string, any>}\n */\nfunction yDocToProsemirrorJSON (\n  ydoc,\n  xmlFragment = 'prosemirror'\n) {\n  return yXmlFragmentToProsemirrorJSON(ydoc.getXmlFragment(xmlFragment))\n}\n\n/**\n * @deprecated Use `yXmlFragmentToProseMirrorRootNode` instead\n *\n * Utility method to convert a Y.Doc to Prosemirror compatible JSON.\n *\n * @param {Y.XmlFragment} xmlFragment The fragment, which must be part of a Y.Doc.\n * @return {Record<string, any>}\n */\nfunction yXmlFragmentToProsemirrorJSON (xmlFragment) {\n  const items = xmlFragment.toArray()\n\n  function serialize (item) {\n    /**\n     * @type {Object} NodeObject\n     * @property {string} NodeObject.type\n     * @property {Record<string, string>=} NodeObject.attrs\n     * @property {Array<NodeObject>=} NodeObject.content\n     */\n    let response\n\n    // TODO: Must be a better way to detect text nodes than this\n    if (!item.nodeName) {\n      const delta = item.toDelta()\n      response = delta.map((d) => {\n        const text = {\n          type: 'text',\n          text: d.insert\n        }\n\n        if (d.attributes) {\n          text.marks = Object.keys(d.attributes).map((type) => {\n            const attrs = d.attributes[type]\n            const mark = {\n              type\n            }\n\n            if (Object.keys(attrs)) {\n              mark.attrs = attrs\n            }\n\n            return mark\n          })\n        }\n        return text\n      })\n    } else {\n      response = {\n        type: item.nodeName\n      }\n\n      const attrs = item.getAttributes()\n      if (Object.keys(attrs).length) {\n        response.attrs = attrs\n      }\n\n      const children = item.toArray()\n      if (children.length) {\n        response.content = children.map(serialize).flat()\n      }\n    }\n\n    return response\n  }\n\n  return {\n    type: 'doc',\n    content: items.map(serialize)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMveS1wcm9zZW1pcnJvci9zcmMvbGliLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFrRjtBQUNoQztBQUMxQjtBQUNxQjtBQUNhO0FBQ3ZCO0FBQ0o7QUFDWTs7QUFFM0M7QUFDQTtBQUNBLGFBQWEseUNBQXlDO0FBQ3REOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7O0FBRUE7QUFDQSx5QkFBeUIsZ0NBQWdDO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiw0REFBYztBQUNwQztBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBLEdBQUc7QUFDSDs7QUFFTztBQUNQO0FBQ0E7QUFDQSxJQUFJLG1EQUFpQjtBQUNyQjtBQUNBLEVBQUUsb0RBQWtCLHNCQUFzQiw0Q0FBVTtBQUNwRDs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxlQUFlO0FBQzFCLFdBQVcsb0JBQW9CO0FBQy9CLFlBQVksS0FBSztBQUNqQjtBQUNPO0FBQ1A7QUFDQSxXQUFXLG9FQUFxQztBQUNoRDtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0EsbURBQW1ELGVBQWU7QUFDbEU7QUFDQSxxQkFBcUIsd0NBQVM7QUFDOUI7QUFDQSxlQUFlLG9FQUFxQztBQUNwRCxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLGVBQWU7QUFDdEMsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0EsbURBQW1ELGVBQWU7QUFDbEU7QUFDQTtBQUNBLE1BQU07QUFDTixtQ0FBbUMsS0FBSyx3QkFBd0IsYUFBYTtBQUM3RTtBQUNBLHVCQUF1QixlQUFlO0FBQ3RDO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxxQkFBcUIsaURBQWtCLDBEQUEwRCxnREFBaUI7QUFDbEg7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLGVBQWU7QUFDeEMsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixpREFBa0IsMERBQTBELGdEQUFpQjtBQUNwSDtBQUNBO0FBQ0EsMkJBQTJCLFFBQVE7QUFDbkM7QUFDQSxZQUFZLGdDQUFnQyxRQUFRO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixlQUFlLGVBQWUsUUFBUSxlQUFlLFFBQVE7QUFDeEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksc0RBQW9CO0FBQ2hDO0FBQ0EsdUNBQXVDLHdDQUFTLGtCQUFrQjtBQUNsRTtBQUNBO0FBQ0E7QUFDQSxTQUFTLG9FQUFxQztBQUM5Qzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksZ0RBQWlCO0FBQzdCLElBQUk7QUFDSixhQUFhLHlDQUFVO0FBQ3ZCO0FBQ0EsYUFBYSxpREFBa0I7QUFDL0I7O0FBRUE7QUFDQSxXQUFXLE9BQU87QUFDbEIsV0FBVyxlQUFlO0FBQzFCLFdBQVcsS0FBSztBQUNoQixXQUFXLG9CQUFvQjtBQUMvQixZQUFZO0FBQ1o7QUFDTztBQUNQLHFCQUFxQiwyRUFBNEM7QUFDakUsbUVBQW1FLDJDQUFZO0FBQy9FO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLHdDQUFTO0FBQ3BDO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLGVBQWU7QUFDNUM7QUFDQSx5QkFBeUIsd0NBQVM7QUFDbEM7QUFDQSxVQUFVO0FBQ1YsNEJBQTRCLEtBQUs7QUFDakM7QUFDQTtBQUNBLHFCQUFxQixRQUFRO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixnQkFBZ0I7QUFDekM7QUFDQTtBQUNBLHVDQUF1QyxlQUFlO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLHdDQUFTO0FBQzlDO0FBQ0EsWUFBWTtBQUNaLDhCQUE4QixLQUFLO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsZ0JBQWdCO0FBQ3RDO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGVBQWU7QUFDMUIsV0FBVyxRQUFRO0FBQ25CO0FBQ087QUFDUDtBQUNBLElBQUksK0VBQXNCO0FBQzFCLGlCQUFpQixjQUFjO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyx1REFBUTtBQUNqQjs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGVBQWU7QUFDMUIsV0FBVyxRQUFRO0FBQ25CO0FBQ087QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsZUFBZTtBQUMxQixXQUFXLFFBQVE7QUFDbkI7QUFDTztBQUNQO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBLElBQUksK0VBQXNCO0FBQzFCLGlCQUFpQixjQUFjO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQThDLHVEQUFRO0FBQ3RELFdBQVc7QUFDWDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsTUFBTTtBQUNqQixXQUFXLFFBQVE7QUFDbkIsWUFBWTtBQUNaO0FBQ087QUFDUCxtQkFBbUIsb0NBQUs7QUFDeEIsMEJBQTBCLGVBQWUsMEJBQTBCLDRDQUFhO0FBQ2hGO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLE1BQU07QUFDakIsV0FBVyxlQUFlO0FBQzFCLDJDQUEyQztBQUMzQyxZQUFZO0FBQ1o7QUFDTztBQUNQLGtDQUFrQyw0Q0FBYTtBQUMvQyx1Q0FBdUM7QUFDdkMsRUFBRSx5RUFBZTtBQUNqQjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsS0FBSztBQUNoQixXQUFXLFFBQVE7QUFDbkIsWUFBWTtBQUNaO0FBQ087QUFDUCxjQUFjLG1EQUFJO0FBQ2xCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxLQUFLO0FBQ2hCLFdBQVcsZUFBZTtBQUMxQiwyQ0FBMkM7QUFDM0MsWUFBWTtBQUNaO0FBQ087QUFDUCxjQUFjLG1EQUFJO0FBQ2xCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLE9BQU87QUFDbEIsWUFBWTtBQUNaO0FBQ087QUFDUDtBQUNBLFNBQVMsbURBQUk7QUFDYjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxlQUFlO0FBQzFCLFlBQVk7QUFDWjtBQUNPO0FBQ1A7QUFDQSxTQUFTLG1EQUFJO0FBQ2I7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCLFdBQVcsUUFBUTtBQUNuQixZQUFZO0FBQ1o7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsZUFBZTtBQUMxQixZQUFZO0FBQ1o7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQSxjQUFjLFFBQVE7QUFDdEIsa0JBQWtCLFFBQVE7QUFDMUIsa0JBQWtCLHlCQUF5QjtBQUMzQyxrQkFBa0Isb0JBQW9CO0FBQ3RDO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsTUFBTTtBQUNOO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMveS1wcm9zZW1pcnJvci9zcmMvbGliLmpzPzViMGIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXBkYXRlWUZyYWdtZW50LCBjcmVhdGVOb2RlRnJvbVlFbGVtZW50IH0gZnJvbSAnLi9wbHVnaW5zL3N5bmMtcGx1Z2luLmpzJyAvLyBlc2xpbnQtZGlzYWJsZS1saW5lXG5pbXBvcnQgeyB5U3luY1BsdWdpbktleSB9IGZyb20gJy4vcGx1Z2lucy9rZXlzLmpzJ1xuaW1wb3J0ICogYXMgWSBmcm9tICd5anMnXG5pbXBvcnQgeyBFZGl0b3JWaWV3IH0gZnJvbSAncHJvc2VtaXJyb3ItdmlldycgLy8gZXNsaW50LWRpc2FibGUtbGluZVxuaW1wb3J0IHsgTm9kZSwgU2NoZW1hLCBGcmFnbWVudCB9IGZyb20gJ3Byb3NlbWlycm9yLW1vZGVsJyAvLyBlc2xpbnQtZGlzYWJsZS1saW5lXG5pbXBvcnQgKiBhcyBlcnJvciBmcm9tICdsaWIwL2Vycm9yJ1xuaW1wb3J0ICogYXMgbWFwIGZyb20gJ2xpYjAvbWFwJ1xuaW1wb3J0ICogYXMgZXZlbnRsb29wIGZyb20gJ2xpYjAvZXZlbnRsb29wJ1xuXG4vKipcbiAqIEVpdGhlciBhIG5vZGUgaWYgdHlwZSBpcyBZWG1sRWxlbWVudCBvciBhbiBBcnJheSBvZiB0ZXh0IG5vZGVzIGlmIFlYbWxUZXh0XG4gKiBAdHlwZWRlZiB7TWFwPFkuQWJzdHJhY3RUeXBlLCBOb2RlIHwgQXJyYXk8Tm9kZT4+fSBQcm9zZW1pcnJvck1hcHBpbmdcbiAqL1xuXG4vKipcbiAqIElzIG51bGwgaWYgbm8gdGltZW91dCBpcyBpbiBwcm9ncmVzcy5cbiAqIElzIGRlZmluZWQgaWYgYSB0aW1lb3V0IGlzIGluIHByb2dyZXNzLlxuICogTWFwcyBmcm9tIHZpZXdcbiAqIEB0eXBlIHtNYXA8RWRpdG9yVmlldywgTWFwPGFueSwgYW55Pj58bnVsbH1cbiAqL1xubGV0IHZpZXdzVG9VcGRhdGUgPSBudWxsXG5cbmNvbnN0IHVwZGF0ZU1ldGFzID0gKCkgPT4ge1xuICBjb25zdCB1cHMgPSAvKiogQHR5cGUge01hcDxFZGl0b3JWaWV3LCBNYXA8YW55LCBhbnk+Pn0gKi8gKHZpZXdzVG9VcGRhdGUpXG4gIHZpZXdzVG9VcGRhdGUgPSBudWxsXG4gIHVwcy5mb3JFYWNoKChtZXRhcywgdmlldykgPT4ge1xuICAgIGNvbnN0IHRyID0gdmlldy5zdGF0ZS50clxuICAgIGNvbnN0IHN5bmNTdGF0ZSA9IHlTeW5jUGx1Z2luS2V5LmdldFN0YXRlKHZpZXcuc3RhdGUpXG4gICAgaWYgKHN5bmNTdGF0ZSAmJiBzeW5jU3RhdGUuYmluZGluZyAmJiAhc3luY1N0YXRlLmJpbmRpbmcuaXNEZXN0cm95ZWQpIHtcbiAgICAgIG1ldGFzLmZvckVhY2goKHZhbCwga2V5KSA9PiB7XG4gICAgICAgIHRyLnNldE1ldGEoa2V5LCB2YWwpXG4gICAgICB9KVxuICAgICAgdmlldy5kaXNwYXRjaCh0cilcbiAgICB9XG4gIH0pXG59XG5cbmV4cG9ydCBjb25zdCBzZXRNZXRhID0gKHZpZXcsIGtleSwgdmFsdWUpID0+IHtcbiAgaWYgKCF2aWV3c1RvVXBkYXRlKSB7XG4gICAgdmlld3NUb1VwZGF0ZSA9IG5ldyBNYXAoKVxuICAgIGV2ZW50bG9vcC50aW1lb3V0KDAsIHVwZGF0ZU1ldGFzKVxuICB9XG4gIG1hcC5zZXRJZlVuZGVmaW5lZCh2aWV3c1RvVXBkYXRlLCB2aWV3LCBtYXAuY3JlYXRlKS5zZXQoa2V5LCB2YWx1ZSlcbn1cblxuLyoqXG4gKiBUcmFuc2Zvcm1zIGEgUHJvc2VtaXJyb3IgYmFzZWQgYWJzb2x1dGUgcG9zaXRpb24gdG8gYSBZanMgQ3Vyc29yIChyZWxhdGl2ZSBwb3NpdGlvbiBpbiB0aGUgWWpzIG1vZGVsKS5cbiAqXG4gKiBAcGFyYW0ge251bWJlcn0gcG9zXG4gKiBAcGFyYW0ge1kuWG1sRnJhZ21lbnR9IHR5cGVcbiAqIEBwYXJhbSB7UHJvc2VtaXJyb3JNYXBwaW5nfSBtYXBwaW5nXG4gKiBAcmV0dXJuIHthbnl9IHJlbGF0aXZlIHBvc2l0aW9uXG4gKi9cbmV4cG9ydCBjb25zdCBhYnNvbHV0ZVBvc2l0aW9uVG9SZWxhdGl2ZVBvc2l0aW9uID0gKHBvcywgdHlwZSwgbWFwcGluZykgPT4ge1xuICBpZiAocG9zID09PSAwKSB7XG4gICAgcmV0dXJuIFkuY3JlYXRlUmVsYXRpdmVQb3NpdGlvbkZyb21UeXBlSW5kZXgodHlwZSwgMCwgLTEpXG4gIH1cbiAgLyoqXG4gICAqIEB0eXBlIHthbnl9XG4gICAqL1xuICBsZXQgbiA9IHR5cGUuX2ZpcnN0ID09PSBudWxsID8gbnVsbCA6IC8qKiBAdHlwZSB7WS5Db250ZW50VHlwZX0gKi8gKHR5cGUuX2ZpcnN0LmNvbnRlbnQpLnR5cGVcbiAgd2hpbGUgKG4gIT09IG51bGwgJiYgdHlwZSAhPT0gbikge1xuICAgIGlmIChuIGluc3RhbmNlb2YgWS5YbWxUZXh0KSB7XG4gICAgICBpZiAobi5fbGVuZ3RoID49IHBvcykge1xuICAgICAgICByZXR1cm4gWS5jcmVhdGVSZWxhdGl2ZVBvc2l0aW9uRnJvbVR5cGVJbmRleChuLCBwb3MsIC0xKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcG9zIC09IG4uX2xlbmd0aFxuICAgICAgfVxuICAgICAgaWYgKG4uX2l0ZW0gIT09IG51bGwgJiYgbi5faXRlbS5uZXh0ICE9PSBudWxsKSB7XG4gICAgICAgIG4gPSAvKiogQHR5cGUge1kuQ29udGVudFR5cGV9ICovIChuLl9pdGVtLm5leHQuY29udGVudCkudHlwZVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgZG8ge1xuICAgICAgICAgIG4gPSBuLl9pdGVtID09PSBudWxsID8gbnVsbCA6IG4uX2l0ZW0ucGFyZW50XG4gICAgICAgICAgcG9zLS1cbiAgICAgICAgfSB3aGlsZSAobiAhPT0gdHlwZSAmJiBuICE9PSBudWxsICYmIG4uX2l0ZW0gIT09IG51bGwgJiYgbi5faXRlbS5uZXh0ID09PSBudWxsKVxuICAgICAgICBpZiAobiAhPT0gbnVsbCAmJiBuICE9PSB0eXBlKSB7XG4gICAgICAgICAgLy8gQHRzLWdub3JlIHdlIGtub3cgdGhhdCBuLm5leHQgIT09IG51bGwgYmVjYXVzZSBvZiBhYm92ZSBsb29wIGNvbmRpdGl0aW9uXG4gICAgICAgICAgbiA9IG4uX2l0ZW0gPT09IG51bGwgPyBudWxsIDogLyoqIEB0eXBlIHtZLkNvbnRlbnRUeXBlfSAqLyAoLyoqIEB0eXBlIFkuSXRlbSAqLyAobi5faXRlbS5uZXh0KS5jb250ZW50KS50eXBlXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgY29uc3QgcE5vZGVTaXplID0gLyoqIEB0eXBlIHthbnl9ICovIChtYXBwaW5nLmdldChuKSB8fCB7IG5vZGVTaXplOiAwIH0pLm5vZGVTaXplXG4gICAgICBpZiAobi5fZmlyc3QgIT09IG51bGwgJiYgcG9zIDwgcE5vZGVTaXplKSB7XG4gICAgICAgIG4gPSAvKiogQHR5cGUge1kuQ29udGVudFR5cGV9ICovIChuLl9maXJzdC5jb250ZW50KS50eXBlXG4gICAgICAgIHBvcy0tXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBpZiAocG9zID09PSAxICYmIG4uX2xlbmd0aCA9PT0gMCAmJiBwTm9kZVNpemUgPiAxKSB7XG4gICAgICAgICAgLy8gZWRnZSBjYXNlLCBzaG91bGQgZW5kIGluIHRoaXMgcGFyYWdyYXBoXG4gICAgICAgICAgcmV0dXJuIG5ldyBZLlJlbGF0aXZlUG9zaXRpb24obi5faXRlbSA9PT0gbnVsbCA/IG51bGwgOiBuLl9pdGVtLmlkLCBuLl9pdGVtID09PSBudWxsID8gWS5maW5kUm9vdFR5cGVLZXkobikgOiBudWxsLCBudWxsKVxuICAgICAgICB9XG4gICAgICAgIHBvcyAtPSBwTm9kZVNpemVcbiAgICAgICAgaWYgKG4uX2l0ZW0gIT09IG51bGwgJiYgbi5faXRlbS5uZXh0ICE9PSBudWxsKSB7XG4gICAgICAgICAgbiA9IC8qKiBAdHlwZSB7WS5Db250ZW50VHlwZX0gKi8gKG4uX2l0ZW0ubmV4dC5jb250ZW50KS50eXBlXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgaWYgKHBvcyA9PT0gMCkge1xuICAgICAgICAgICAgLy8gc2V0IHRvIGVuZCBvZiBuLnBhcmVudFxuICAgICAgICAgICAgbiA9IG4uX2l0ZW0gPT09IG51bGwgPyBuIDogbi5faXRlbS5wYXJlbnRcbiAgICAgICAgICAgIHJldHVybiBuZXcgWS5SZWxhdGl2ZVBvc2l0aW9uKG4uX2l0ZW0gPT09IG51bGwgPyBudWxsIDogbi5faXRlbS5pZCwgbi5faXRlbSA9PT0gbnVsbCA/IFkuZmluZFJvb3RUeXBlS2V5KG4pIDogbnVsbCwgbnVsbClcbiAgICAgICAgICB9XG4gICAgICAgICAgZG8ge1xuICAgICAgICAgICAgbiA9IC8qKiBAdHlwZSB7WS5JdGVtfSAqLyAobi5faXRlbSkucGFyZW50XG4gICAgICAgICAgICBwb3MtLVxuICAgICAgICAgIH0gd2hpbGUgKG4gIT09IHR5cGUgJiYgLyoqIEB0eXBlIHtZLkl0ZW19ICovIChuLl9pdGVtKS5uZXh0ID09PSBudWxsKVxuICAgICAgICAgIC8vIGlmIG4gaXMgbnVsbCBhdCB0aGlzIHBvaW50LCB3ZSBoYXZlIGFuIHVuZXhwZWN0ZWQgY2FzZVxuICAgICAgICAgIGlmIChuICE9PSB0eXBlKSB7XG4gICAgICAgICAgICAvLyBXZSBrbm93IHRoYXQgbi5faXRlbS5uZXh0IGlzIGRlZmluZWQgYmVjYXVzZSBvZiBhYm92ZSBsb29wIGNvbmRpdGlvblxuICAgICAgICAgICAgbiA9IC8qKiBAdHlwZSB7WS5Db250ZW50VHlwZX0gKi8gKC8qKiBAdHlwZSB7WS5JdGVtfSAqLyAoLyoqIEB0eXBlIHtZLkl0ZW19ICovIChuLl9pdGVtKS5uZXh0KS5jb250ZW50KS50eXBlXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICAgIGlmIChuID09PSBudWxsKSB7XG4gICAgICB0aHJvdyBlcnJvci51bmV4cGVjdGVkQ2FzZSgpXG4gICAgfVxuICAgIGlmIChwb3MgPT09IDAgJiYgbi5jb25zdHJ1Y3RvciAhPT0gWS5YbWxUZXh0ICYmIG4gIT09IHR5cGUpIHsgLy8gVE9ETzogc2V0IHRvIDw9IDBcbiAgICAgIHJldHVybiBjcmVhdGVSZWxhdGl2ZVBvc2l0aW9uKG4uX2l0ZW0ucGFyZW50LCBuLl9pdGVtKVxuICAgIH1cbiAgfVxuICByZXR1cm4gWS5jcmVhdGVSZWxhdGl2ZVBvc2l0aW9uRnJvbVR5cGVJbmRleCh0eXBlLCB0eXBlLl9sZW5ndGgsIC0xKVxufVxuXG5jb25zdCBjcmVhdGVSZWxhdGl2ZVBvc2l0aW9uID0gKHR5cGUsIGl0ZW0pID0+IHtcbiAgbGV0IHR5cGVpZCA9IG51bGxcbiAgbGV0IHRuYW1lID0gbnVsbFxuICBpZiAodHlwZS5faXRlbSA9PT0gbnVsbCkge1xuICAgIHRuYW1lID0gWS5maW5kUm9vdFR5cGVLZXkodHlwZSlcbiAgfSBlbHNlIHtcbiAgICB0eXBlaWQgPSBZLmNyZWF0ZUlEKHR5cGUuX2l0ZW0uaWQuY2xpZW50LCB0eXBlLl9pdGVtLmlkLmNsb2NrKVxuICB9XG4gIHJldHVybiBuZXcgWS5SZWxhdGl2ZVBvc2l0aW9uKHR5cGVpZCwgdG5hbWUsIGl0ZW0uaWQpXG59XG5cbi8qKlxuICogQHBhcmFtIHtZLkRvY30geVxuICogQHBhcmFtIHtZLlhtbEZyYWdtZW50fSBkb2N1bWVudFR5cGUgVG9wIGxldmVsIHR5cGUgdGhhdCBpcyBib3VuZCB0byBwVmlld1xuICogQHBhcmFtIHthbnl9IHJlbFBvcyBFbmNvZGVkIFlqcyBiYXNlZCByZWxhdGl2ZSBwb3NpdGlvblxuICogQHBhcmFtIHtQcm9zZW1pcnJvck1hcHBpbmd9IG1hcHBpbmdcbiAqIEByZXR1cm4ge251bGx8bnVtYmVyfVxuICovXG5leHBvcnQgY29uc3QgcmVsYXRpdmVQb3NpdGlvblRvQWJzb2x1dGVQb3NpdGlvbiA9ICh5LCBkb2N1bWVudFR5cGUsIHJlbFBvcywgbWFwcGluZykgPT4ge1xuICBjb25zdCBkZWNvZGVkUG9zID0gWS5jcmVhdGVBYnNvbHV0ZVBvc2l0aW9uRnJvbVJlbGF0aXZlUG9zaXRpb24ocmVsUG9zLCB5KVxuICBpZiAoZGVjb2RlZFBvcyA9PT0gbnVsbCB8fCAoZGVjb2RlZFBvcy50eXBlICE9PSBkb2N1bWVudFR5cGUgJiYgIVkuaXNQYXJlbnRPZihkb2N1bWVudFR5cGUsIGRlY29kZWRQb3MudHlwZS5faXRlbSkpKSB7XG4gICAgcmV0dXJuIG51bGxcbiAgfVxuICBsZXQgdHlwZSA9IGRlY29kZWRQb3MudHlwZVxuICBsZXQgcG9zID0gMFxuICBpZiAodHlwZS5jb25zdHJ1Y3RvciA9PT0gWS5YbWxUZXh0KSB7XG4gICAgcG9zID0gZGVjb2RlZFBvcy5pbmRleFxuICB9IGVsc2UgaWYgKHR5cGUuX2l0ZW0gPT09IG51bGwgfHwgIXR5cGUuX2l0ZW0uZGVsZXRlZCkge1xuICAgIGxldCBuID0gdHlwZS5fZmlyc3RcbiAgICBsZXQgaSA9IDBcbiAgICB3aGlsZSAoaSA8IHR5cGUuX2xlbmd0aCAmJiBpIDwgZGVjb2RlZFBvcy5pbmRleCAmJiBuICE9PSBudWxsKSB7XG4gICAgICBpZiAoIW4uZGVsZXRlZCkge1xuICAgICAgICBjb25zdCB0ID0gLyoqIEB0eXBlIHtZLkNvbnRlbnRUeXBlfSAqLyAobi5jb250ZW50KS50eXBlXG4gICAgICAgIGkrK1xuICAgICAgICBpZiAodCBpbnN0YW5jZW9mIFkuWG1sVGV4dCkge1xuICAgICAgICAgIHBvcyArPSB0Ll9sZW5ndGhcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBwb3MgKz0gLyoqIEB0eXBlIHthbnl9ICovIChtYXBwaW5nLmdldCh0KSkubm9kZVNpemVcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgbiA9IC8qKiBAdHlwZSB7WS5JdGVtfSAqLyAobi5yaWdodClcbiAgICB9XG4gICAgcG9zICs9IDEgLy8gaW5jcmVhc2UgYmVjYXVzZSB3ZSBnbyBvdXQgb2YgblxuICB9XG4gIHdoaWxlICh0eXBlICE9PSBkb2N1bWVudFR5cGUgJiYgdHlwZS5faXRlbSAhPT0gbnVsbCkge1xuICAgIC8vIEB0cy1pZ25vcmVcbiAgICBjb25zdCBwYXJlbnQgPSB0eXBlLl9pdGVtLnBhcmVudFxuICAgIC8vIEB0cy1pZ25vcmVcbiAgICBpZiAocGFyZW50Ll9pdGVtID09PSBudWxsIHx8ICFwYXJlbnQuX2l0ZW0uZGVsZXRlZCkge1xuICAgICAgcG9zICs9IDEgLy8gdGhlIHN0YXJ0IHRhZ1xuICAgICAgbGV0IG4gPSAvKiogQHR5cGUge1kuQWJzdHJhY3RUeXBlfSAqLyAocGFyZW50KS5fZmlyc3RcbiAgICAgIC8vIG5vdyBpdGVyYXRlIHVudGlsIHdlIGZvdW5kIHR5cGVcbiAgICAgIHdoaWxlIChuICE9PSBudWxsKSB7XG4gICAgICAgIGNvbnN0IGNvbnRlbnRUeXBlID0gLyoqIEB0eXBlIHtZLkNvbnRlbnRUeXBlfSAqLyAobi5jb250ZW50KS50eXBlXG4gICAgICAgIGlmIChjb250ZW50VHlwZSA9PT0gdHlwZSkge1xuICAgICAgICAgIGJyZWFrXG4gICAgICAgIH1cbiAgICAgICAgaWYgKCFuLmRlbGV0ZWQpIHtcbiAgICAgICAgICBpZiAoY29udGVudFR5cGUgaW5zdGFuY2VvZiBZLlhtbFRleHQpIHtcbiAgICAgICAgICAgIHBvcyArPSBjb250ZW50VHlwZS5fbGVuZ3RoXG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHBvcyArPSAvKiogQHR5cGUge2FueX0gKi8gKG1hcHBpbmcuZ2V0KGNvbnRlbnRUeXBlKSkubm9kZVNpemVcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgbiA9IG4ucmlnaHRcbiAgICAgIH1cbiAgICB9XG4gICAgdHlwZSA9IC8qKiBAdHlwZSB7WS5BYnN0cmFjdFR5cGV9ICovIChwYXJlbnQpXG4gIH1cbiAgcmV0dXJuIHBvcyAtIDEgLy8gd2UgZG9uJ3QgY291bnQgdGhlIG1vc3Qgb3V0ZXIgdGFnLCBiZWNhdXNlIGl0IGlzIGEgZnJhZ21lbnRcbn1cblxuLyoqXG4gKiBVdGlsaXR5IGZ1bmN0aW9uIGZvciBjb252ZXJ0aW5nIGFuIFkuRnJhZ21lbnQgdG8gYSBQcm9zZU1pcnJvciBmcmFnbWVudC5cbiAqXG4gKiBAcGFyYW0ge1kuWG1sRnJhZ21lbnR9IHlYbWxGcmFnbWVudFxuICogQHBhcmFtIHtTY2hlbWF9IHNjaGVtYVxuICovXG5leHBvcnQgY29uc3QgeVhtbEZyYWdtZW50VG9Qcm9zZU1pcnJvckZyYWdtZW50ID0gKHlYbWxGcmFnbWVudCwgc2NoZW1hKSA9PiB7XG4gIGNvbnN0IGZyYWdtZW50Q29udGVudCA9IHlYbWxGcmFnbWVudC50b0FycmF5KCkubWFwKCh0KSA9PlxuICAgIGNyZWF0ZU5vZGVGcm9tWUVsZW1lbnQoXG4gICAgICAvKiogQHR5cGUge1kuWG1sRWxlbWVudH0gKi8gKHQpLFxuICAgICAgc2NoZW1hLFxuICAgICAgbmV3IE1hcCgpXG4gICAgKVxuICApLmZpbHRlcigobikgPT4gbiAhPT0gbnVsbClcbiAgcmV0dXJuIEZyYWdtZW50LmZyb21BcnJheShmcmFnbWVudENvbnRlbnQpXG59XG5cbi8qKlxuICogVXRpbGl0eSBmdW5jdGlvbiBmb3IgY29udmVydGluZyBhbiBZLkZyYWdtZW50IHRvIGEgUHJvc2VNaXJyb3Igbm9kZS5cbiAqXG4gKiBAcGFyYW0ge1kuWG1sRnJhZ21lbnR9IHlYbWxGcmFnbWVudFxuICogQHBhcmFtIHtTY2hlbWF9IHNjaGVtYVxuICovXG5leHBvcnQgY29uc3QgeVhtbEZyYWdtZW50VG9Qcm9zZU1pcnJvclJvb3ROb2RlID0gKHlYbWxGcmFnbWVudCwgc2NoZW1hKSA9PlxuICBzY2hlbWEudG9wTm9kZVR5cGUuY3JlYXRlKG51bGwsIHlYbWxGcmFnbWVudFRvUHJvc2VNaXJyb3JGcmFnbWVudCh5WG1sRnJhZ21lbnQsIHNjaGVtYSkpXG5cbi8qKlxuICogVGhlIGluaXRpYWwgUHJvc2VNaXJyb3IgY29udGVudCBzaG91bGQgYmUgc3VwcGxpZWQgYnkgWWpzLiBUaGlzIGZ1bmN0aW9uIHRyYW5zZm9ybXMgYSBZLkZyYWdtZW50XG4gKiB0byBhIFByb3NlTWlycm9yIERvYyBub2RlIGFuZCBjcmVhdGVzIGEgbWFwcGluZyB0aGF0IGlzIHVzZWQgYnkgdGhlIHN5bmMgcGx1Z2luLlxuICpcbiAqIEBwYXJhbSB7WS5YbWxGcmFnbWVudH0geVhtbEZyYWdtZW50XG4gKiBAcGFyYW0ge1NjaGVtYX0gc2NoZW1hXG4gKi9cbmV4cG9ydCBjb25zdCBpbml0UHJvc2VNaXJyb3JEb2MgPSAoeVhtbEZyYWdtZW50LCBzY2hlbWEpID0+IHtcbiAgLyoqXG4gICAqIEB0eXBlIHtQcm9zZW1pcnJvck1hcHBpbmd9XG4gICAqL1xuICBjb25zdCBtYXBwaW5nID0gbmV3IE1hcCgpXG4gIGNvbnN0IGZyYWdtZW50Q29udGVudCA9IHlYbWxGcmFnbWVudC50b0FycmF5KCkubWFwKCh0KSA9PlxuICAgIGNyZWF0ZU5vZGVGcm9tWUVsZW1lbnQoXG4gICAgICAvKiogQHR5cGUge1kuWG1sRWxlbWVudH0gKi8gKHQpLFxuICAgICAgc2NoZW1hLFxuICAgICAgbWFwcGluZ1xuICAgIClcbiAgKS5maWx0ZXIoKG4pID0+IG4gIT09IG51bGwpXG4gIGNvbnN0IGRvYyA9IHNjaGVtYS50b3BOb2RlVHlwZS5jcmVhdGUobnVsbCwgRnJhZ21lbnQuZnJvbUFycmF5KGZyYWdtZW50Q29udGVudCkpXG4gIHJldHVybiB7IGRvYywgbWFwcGluZyB9XG59XG5cbi8qKlxuICogVXRpbGl0eSBtZXRob2QgdG8gY29udmVydCBhIFByb3NlbWlycm9yIERvYyBOb2RlIGludG8gYSBZLkRvYy5cbiAqXG4gKiBUaGlzIGNhbiBiZSB1c2VkIHdoZW4gaW1wb3J0aW5nIGV4aXN0aW5nIGNvbnRlbnQgdG8gWS5Eb2MgZm9yIHRoZSBmaXJzdCB0aW1lLFxuICogbm90ZSB0aGF0IHRoaXMgc2hvdWxkIG5vdCBiZSB1c2VkIHRvIHJlaHlkcmF0ZSBhIFkuRG9jIGZyb20gYSBkYXRhYmFzZSBvbmNlXG4gKiBjb2xsYWJvcmF0aW9uIGhhcyBiZWd1biBhcyBhbGwgaGlzdG9yeSB3aWxsIGJlIGxvc3RcbiAqXG4gKiBAcGFyYW0ge05vZGV9IGRvY1xuICogQHBhcmFtIHtzdHJpbmd9IHhtbEZyYWdtZW50XG4gKiBAcmV0dXJuIHtZLkRvY31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHByb3NlbWlycm9yVG9ZRG9jIChkb2MsIHhtbEZyYWdtZW50ID0gJ3Byb3NlbWlycm9yJykge1xuICBjb25zdCB5ZG9jID0gbmV3IFkuRG9jKClcbiAgY29uc3QgdHlwZSA9IC8qKiBAdHlwZSB7WS5YbWxGcmFnbWVudH0gKi8gKHlkb2MuZ2V0KHhtbEZyYWdtZW50LCBZLlhtbEZyYWdtZW50KSlcbiAgaWYgKCF0eXBlLmRvYykge1xuICAgIHJldHVybiB5ZG9jXG4gIH1cblxuICBwcm9zZW1pcnJvclRvWVhtbEZyYWdtZW50KGRvYywgdHlwZSlcbiAgcmV0dXJuIHR5cGUuZG9jXG59XG5cbi8qKlxuICogVXRpbGl0eSBtZXRob2QgdG8gdXBkYXRlIGFuIGVtcHR5IFkuWG1sRnJhZ21lbnQgd2l0aCBjb250ZW50IGZyb20gYSBQcm9zZW1pcnJvciBEb2MgTm9kZS5cbiAqXG4gKiBUaGlzIGNhbiBiZSB1c2VkIHdoZW4gaW1wb3J0aW5nIGV4aXN0aW5nIGNvbnRlbnQgdG8gWS5Eb2MgZm9yIHRoZSBmaXJzdCB0aW1lLFxuICogbm90ZSB0aGF0IHRoaXMgc2hvdWxkIG5vdCBiZSB1c2VkIHRvIHJlaHlkcmF0ZSBhIFkuRG9jIGZyb20gYSBkYXRhYmFzZSBvbmNlXG4gKiBjb2xsYWJvcmF0aW9uIGhhcyBiZWd1biBhcyBhbGwgaGlzdG9yeSB3aWxsIGJlIGxvc3RcbiAqXG4gKiBOb3RlOiBUaGUgWS5YbWxGcmFnbWVudCBkb2VzIG5vdCBuZWVkIHRvIGJlIHBhcnQgb2YgYSBZLkRvYyBkb2N1bWVudCBhdCB0aGUgdGltZSB0aGF0IHRoaXNcbiAqIG1ldGhvZCBpcyBjYWxsZWQsIGJ1dCBpdCBtdXN0IGJlIGFkZGVkIGJlZm9yZSBhbnkgb3RoZXIgb3BlcmF0aW9ucyBhcmUgcGVyZm9ybWVkIG9uIGl0LlxuICpcbiAqIEBwYXJhbSB7Tm9kZX0gZG9jIHByb3NlbWlycm9yIGRvY3VtZW50LlxuICogQHBhcmFtIHtZLlhtbEZyYWdtZW50fSBbeG1sRnJhZ21lbnRdIElmIHN1cHBsaWVkLCBhbiB4bWwgZnJhZ21lbnQgdG8gYmVcbiAqICAgcG9wdWxhdGVkIGZyb20gdGhlIHByb3NlbWlycm9yIHN0YXRlOyBvdGhlcndpc2UgYSBuZXcgWG1sRnJhZ21lbnQgd2lsbCBiZSBjcmVhdGVkLlxuICogQHJldHVybiB7WS5YbWxGcmFnbWVudH1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHByb3NlbWlycm9yVG9ZWG1sRnJhZ21lbnQgKGRvYywgeG1sRnJhZ21lbnQpIHtcbiAgY29uc3QgdHlwZSA9IHhtbEZyYWdtZW50IHx8IG5ldyBZLlhtbEZyYWdtZW50KClcbiAgY29uc3QgeWRvYyA9IHR5cGUuZG9jID8gdHlwZS5kb2MgOiB7IHRyYW5zYWN0OiAodHJhbnNhY3Rpb24pID0+IHRyYW5zYWN0aW9uKHVuZGVmaW5lZCkgfVxuICB1cGRhdGVZRnJhZ21lbnQoeWRvYywgdHlwZSwgZG9jLCBuZXcgTWFwKCkpXG4gIHJldHVybiB0eXBlXG59XG5cbi8qKlxuICogVXRpbGl0eSBtZXRob2QgdG8gY29udmVydCBQcm9zZW1pcnJvciBjb21wYXRpYmxlIEpTT04gaW50byBhIFkuRG9jLlxuICpcbiAqIFRoaXMgY2FuIGJlIHVzZWQgd2hlbiBpbXBvcnRpbmcgZXhpc3RpbmcgY29udGVudCB0byBZLkRvYyBmb3IgdGhlIGZpcnN0IHRpbWUsXG4gKiBub3RlIHRoYXQgdGhpcyBzaG91bGQgbm90IGJlIHVzZWQgdG8gcmVoeWRyYXRlIGEgWS5Eb2MgZnJvbSBhIGRhdGFiYXNlIG9uY2VcbiAqIGNvbGxhYm9yYXRpb24gaGFzIGJlZ3VuIGFzIGFsbCBoaXN0b3J5IHdpbGwgYmUgbG9zdFxuICpcbiAqIEBwYXJhbSB7U2NoZW1hfSBzY2hlbWFcbiAqIEBwYXJhbSB7YW55fSBzdGF0ZVxuICogQHBhcmFtIHtzdHJpbmd9IHhtbEZyYWdtZW50XG4gKiBAcmV0dXJuIHtZLkRvY31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHByb3NlbWlycm9ySlNPTlRvWURvYyAoc2NoZW1hLCBzdGF0ZSwgeG1sRnJhZ21lbnQgPSAncHJvc2VtaXJyb3InKSB7XG4gIGNvbnN0IGRvYyA9IE5vZGUuZnJvbUpTT04oc2NoZW1hLCBzdGF0ZSlcbiAgcmV0dXJuIHByb3NlbWlycm9yVG9ZRG9jKGRvYywgeG1sRnJhZ21lbnQpXG59XG5cbi8qKlxuICogVXRpbGl0eSBtZXRob2QgdG8gY29udmVydCBQcm9zZW1pcnJvciBjb21wYXRpYmxlIEpTT04gdG8gYSBZLlhtbEZyYWdtZW50XG4gKlxuICogVGhpcyBjYW4gYmUgdXNlZCB3aGVuIGltcG9ydGluZyBleGlzdGluZyBjb250ZW50IHRvIFkuRG9jIGZvciB0aGUgZmlyc3QgdGltZSxcbiAqIG5vdGUgdGhhdCB0aGlzIHNob3VsZCBub3QgYmUgdXNlZCB0byByZWh5ZHJhdGUgYSBZLkRvYyBmcm9tIGEgZGF0YWJhc2Ugb25jZVxuICogY29sbGFib3JhdGlvbiBoYXMgYmVndW4gYXMgYWxsIGhpc3Rvcnkgd2lsbCBiZSBsb3N0XG4gKlxuICogQHBhcmFtIHtTY2hlbWF9IHNjaGVtYVxuICogQHBhcmFtIHthbnl9IHN0YXRlXG4gKiBAcGFyYW0ge1kuWG1sRnJhZ21lbnR9IFt4bWxGcmFnbWVudF0gSWYgc3VwcGxpZWQsIGFuIHhtbCBmcmFnbWVudCB0byBiZVxuICogICBwb3B1bGF0ZWQgZnJvbSB0aGUgcHJvc2VtaXJyb3Igc3RhdGU7IG90aGVyd2lzZSBhIG5ldyBYbWxGcmFnbWVudCB3aWxsIGJlIGNyZWF0ZWQuXG4gKiBAcmV0dXJuIHtZLlhtbEZyYWdtZW50fVxuICovXG5leHBvcnQgZnVuY3Rpb24gcHJvc2VtaXJyb3JKU09OVG9ZWG1sRnJhZ21lbnQgKHNjaGVtYSwgc3RhdGUsIHhtbEZyYWdtZW50KSB7XG4gIGNvbnN0IGRvYyA9IE5vZGUuZnJvbUpTT04oc2NoZW1hLCBzdGF0ZSlcbiAgcmV0dXJuIHByb3NlbWlycm9yVG9ZWG1sRnJhZ21lbnQoZG9jLCB4bWxGcmFnbWVudClcbn1cblxuLyoqXG4gKiBAZGVwcmVjYXRlZCBVc2UgYHlYbWxGcmFnbWVudFRvUHJvc2VNaXJyb3JSb290Tm9kZWAgaW5zdGVhZFxuICpcbiAqIFV0aWxpdHkgbWV0aG9kIHRvIGNvbnZlcnQgYSBZLkRvYyB0byBhIFByb3NlbWlycm9yIERvYyBub2RlLlxuICpcbiAqIEBwYXJhbSB7U2NoZW1hfSBzY2hlbWFcbiAqIEBwYXJhbSB7WS5Eb2N9IHlkb2NcbiAqIEByZXR1cm4ge05vZGV9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB5RG9jVG9Qcm9zZW1pcnJvciAoc2NoZW1hLCB5ZG9jKSB7XG4gIGNvbnN0IHN0YXRlID0geURvY1RvUHJvc2VtaXJyb3JKU09OKHlkb2MpXG4gIHJldHVybiBOb2RlLmZyb21KU09OKHNjaGVtYSwgc3RhdGUpXG59XG5cbi8qKlxuICpcbiAqIEBkZXByZWNhdGVkIFVzZSBgeVhtbEZyYWdtZW50VG9Qcm9zZU1pcnJvclJvb3ROb2RlYCBpbnN0ZWFkXG4gKlxuICogVXRpbGl0eSBtZXRob2QgdG8gY29udmVydCBhIFkuWG1sRnJhZ21lbnQgdG8gYSBQcm9zZW1pcnJvciBEb2Mgbm9kZS5cbiAqXG4gKiBAcGFyYW0ge1NjaGVtYX0gc2NoZW1hXG4gKiBAcGFyYW0ge1kuWG1sRnJhZ21lbnR9IHhtbEZyYWdtZW50XG4gKiBAcmV0dXJuIHtOb2RlfVxuICovXG5leHBvcnQgZnVuY3Rpb24geVhtbEZyYWdtZW50VG9Qcm9zZW1pcnJvciAoc2NoZW1hLCB4bWxGcmFnbWVudCkge1xuICBjb25zdCBzdGF0ZSA9IHlYbWxGcmFnbWVudFRvUHJvc2VtaXJyb3JKU09OKHhtbEZyYWdtZW50KVxuICByZXR1cm4gTm9kZS5mcm9tSlNPTihzY2hlbWEsIHN0YXRlKVxufVxuXG4vKipcbiAqXG4gKiBAZGVwcmVjYXRlZCBVc2UgYHlYbWxGcmFnbWVudFRvUHJvc2VNaXJyb3JSb290Tm9kZWAgaW5zdGVhZFxuICpcbiAqIFV0aWxpdHkgbWV0aG9kIHRvIGNvbnZlcnQgYSBZLkRvYyB0byBQcm9zZW1pcnJvciBjb21wYXRpYmxlIEpTT04uXG4gKlxuICogQHBhcmFtIHtZLkRvY30geWRvY1xuICogQHBhcmFtIHtzdHJpbmd9IHhtbEZyYWdtZW50XG4gKiBAcmV0dXJuIHtSZWNvcmQ8c3RyaW5nLCBhbnk+fVxuICovXG5leHBvcnQgZnVuY3Rpb24geURvY1RvUHJvc2VtaXJyb3JKU09OIChcbiAgeWRvYyxcbiAgeG1sRnJhZ21lbnQgPSAncHJvc2VtaXJyb3InXG4pIHtcbiAgcmV0dXJuIHlYbWxGcmFnbWVudFRvUHJvc2VtaXJyb3JKU09OKHlkb2MuZ2V0WG1sRnJhZ21lbnQoeG1sRnJhZ21lbnQpKVxufVxuXG4vKipcbiAqIEBkZXByZWNhdGVkIFVzZSBgeVhtbEZyYWdtZW50VG9Qcm9zZU1pcnJvclJvb3ROb2RlYCBpbnN0ZWFkXG4gKlxuICogVXRpbGl0eSBtZXRob2QgdG8gY29udmVydCBhIFkuRG9jIHRvIFByb3NlbWlycm9yIGNvbXBhdGlibGUgSlNPTi5cbiAqXG4gKiBAcGFyYW0ge1kuWG1sRnJhZ21lbnR9IHhtbEZyYWdtZW50IFRoZSBmcmFnbWVudCwgd2hpY2ggbXVzdCBiZSBwYXJ0IG9mIGEgWS5Eb2MuXG4gKiBAcmV0dXJuIHtSZWNvcmQ8c3RyaW5nLCBhbnk+fVxuICovXG5leHBvcnQgZnVuY3Rpb24geVhtbEZyYWdtZW50VG9Qcm9zZW1pcnJvckpTT04gKHhtbEZyYWdtZW50KSB7XG4gIGNvbnN0IGl0ZW1zID0geG1sRnJhZ21lbnQudG9BcnJheSgpXG5cbiAgZnVuY3Rpb24gc2VyaWFsaXplIChpdGVtKSB7XG4gICAgLyoqXG4gICAgICogQHR5cGUge09iamVjdH0gTm9kZU9iamVjdFxuICAgICAqIEBwcm9wZXJ0eSB7c3RyaW5nfSBOb2RlT2JqZWN0LnR5cGVcbiAgICAgKiBAcHJvcGVydHkge1JlY29yZDxzdHJpbmcsIHN0cmluZz49fSBOb2RlT2JqZWN0LmF0dHJzXG4gICAgICogQHByb3BlcnR5IHtBcnJheTxOb2RlT2JqZWN0Pj19IE5vZGVPYmplY3QuY29udGVudFxuICAgICAqL1xuICAgIGxldCByZXNwb25zZVxuXG4gICAgLy8gVE9ETzogTXVzdCBiZSBhIGJldHRlciB3YXkgdG8gZGV0ZWN0IHRleHQgbm9kZXMgdGhhbiB0aGlzXG4gICAgaWYgKCFpdGVtLm5vZGVOYW1lKSB7XG4gICAgICBjb25zdCBkZWx0YSA9IGl0ZW0udG9EZWx0YSgpXG4gICAgICByZXNwb25zZSA9IGRlbHRhLm1hcCgoZCkgPT4ge1xuICAgICAgICBjb25zdCB0ZXh0ID0ge1xuICAgICAgICAgIHR5cGU6ICd0ZXh0JyxcbiAgICAgICAgICB0ZXh0OiBkLmluc2VydFxuICAgICAgICB9XG5cbiAgICAgICAgaWYgKGQuYXR0cmlidXRlcykge1xuICAgICAgICAgIHRleHQubWFya3MgPSBPYmplY3Qua2V5cyhkLmF0dHJpYnV0ZXMpLm1hcCgodHlwZSkgPT4ge1xuICAgICAgICAgICAgY29uc3QgYXR0cnMgPSBkLmF0dHJpYnV0ZXNbdHlwZV1cbiAgICAgICAgICAgIGNvbnN0IG1hcmsgPSB7XG4gICAgICAgICAgICAgIHR5cGVcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgaWYgKE9iamVjdC5rZXlzKGF0dHJzKSkge1xuICAgICAgICAgICAgICBtYXJrLmF0dHJzID0gYXR0cnNcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgcmV0dXJuIG1hcmtcbiAgICAgICAgICB9KVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0ZXh0XG4gICAgICB9KVxuICAgIH0gZWxzZSB7XG4gICAgICByZXNwb25zZSA9IHtcbiAgICAgICAgdHlwZTogaXRlbS5ub2RlTmFtZVxuICAgICAgfVxuXG4gICAgICBjb25zdCBhdHRycyA9IGl0ZW0uZ2V0QXR0cmlidXRlcygpXG4gICAgICBpZiAoT2JqZWN0LmtleXMoYXR0cnMpLmxlbmd0aCkge1xuICAgICAgICByZXNwb25zZS5hdHRycyA9IGF0dHJzXG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGNoaWxkcmVuID0gaXRlbS50b0FycmF5KClcbiAgICAgIGlmIChjaGlsZHJlbi5sZW5ndGgpIHtcbiAgICAgICAgcmVzcG9uc2UuY29udGVudCA9IGNoaWxkcmVuLm1hcChzZXJpYWxpemUpLmZsYXQoKVxuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiByZXNwb25zZVxuICB9XG5cbiAgcmV0dXJuIHtcbiAgICB0eXBlOiAnZG9jJyxcbiAgICBjb250ZW50OiBpdGVtcy5tYXAoc2VyaWFsaXplKVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/y-prosemirror/src/lib.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/y-prosemirror/src/plugins/cursor-plugin.js":
/*!*****************************************************************!*\
  !*** ./node_modules/y-prosemirror/src/plugins/cursor-plugin.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDecorations: () => (/* binding */ createDecorations),\n/* harmony export */   defaultAwarenessStateFilter: () => (/* binding */ defaultAwarenessStateFilter),\n/* harmony export */   defaultCursorBuilder: () => (/* binding */ defaultCursorBuilder),\n/* harmony export */   defaultSelectionBuilder: () => (/* binding */ defaultSelectionBuilder),\n/* harmony export */   yCursorPlugin: () => (/* binding */ yCursorPlugin)\n/* harmony export */ });\n/* harmony import */ var yjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! yjs */ \"(ssr)/./node_modules/yjs/dist/yjs.mjs\");\n/* harmony import */ var prosemirror_view__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prosemirror-view */ \"(ssr)/./node_modules/prosemirror-view/dist/index.js\");\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/./node_modules/prosemirror-state/dist/index.js\");\n/* harmony import */ var y_protocols_awareness__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! y-protocols/awareness */ \"(ssr)/./node_modules/y-protocols/awareness.js\");\n/* harmony import */ var _lib_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib.js */ \"(ssr)/./node_modules/y-prosemirror/src/lib.js\");\n/* harmony import */ var _keys_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./keys.js */ \"(ssr)/./node_modules/y-prosemirror/src/plugins/keys.js\");\n/* harmony import */ var lib0_math__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lib0/math */ \"(ssr)/./node_modules/lib0/math.js\");\n\n // eslint-disable-line\n // eslint-disable-line\n // eslint-disable-line\n\n\n\n\n\n/**\n * Default awareness state filter\n *\n * @param {number} currentClientId current client id\n * @param {number} userClientId user client id\n * @param {any} _user user data\n * @return {boolean}\n */\nconst defaultAwarenessStateFilter = (currentClientId, userClientId, _user) => currentClientId !== userClientId\n\n/**\n * Default generator for a cursor element\n *\n * @param {any} user user data\n * @return {HTMLElement}\n */\nconst defaultCursorBuilder = (user) => {\n  const cursor = document.createElement('span')\n  cursor.classList.add('ProseMirror-yjs-cursor')\n  cursor.setAttribute('style', `border-color: ${user.color}`)\n  const userDiv = document.createElement('div')\n  userDiv.setAttribute('style', `background-color: ${user.color}`)\n  userDiv.insertBefore(document.createTextNode(user.name), null)\n  const nonbreakingSpace1 = document.createTextNode('\\u2060')\n  const nonbreakingSpace2 = document.createTextNode('\\u2060')\n  cursor.insertBefore(nonbreakingSpace1, null)\n  cursor.insertBefore(userDiv, null)\n  cursor.insertBefore(nonbreakingSpace2, null)\n  return cursor\n}\n\n/**\n * Default generator for the selection attributes\n *\n * @param {any} user user data\n * @return {import('prosemirror-view').DecorationAttrs}\n */\nconst defaultSelectionBuilder = (user) => {\n  return {\n    style: `background-color: ${user.color}70`,\n    class: 'ProseMirror-yjs-selection'\n  }\n}\n\nconst rxValidColor = /^#[0-9a-fA-F]{6}$/\n\n/**\n * @param {any} state\n * @param {Awareness} awareness\n * @param {function(number, number, any):boolean} awarenessFilter\n * @param {function({ name: string, color: string }):Element} createCursor\n * @param {function({ name: string, color: string }):import('prosemirror-view').DecorationAttrs} createSelection\n * @return {any} DecorationSet\n */\nconst createDecorations = (\n  state,\n  awareness,\n  awarenessFilter,\n  createCursor,\n  createSelection\n) => {\n  const ystate = _keys_js__WEBPACK_IMPORTED_MODULE_1__.ySyncPluginKey.getState(state)\n  const y = ystate.doc\n  const decorations = []\n  if (\n    ystate.snapshot != null || ystate.prevSnapshot != null ||\n    ystate.binding.mapping.size === 0\n  ) {\n    // do not render cursors while snapshot is active\n    return prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.DecorationSet.create(state.doc, [])\n  }\n  awareness.getStates().forEach((aw, clientId) => {\n    if (!awarenessFilter(y.clientID, clientId, aw)) {\n      return\n    }\n\n    if (aw.cursor != null) {\n      const user = aw.user || {}\n      if (user.color == null) {\n        user.color = '#ffa500'\n      } else if (!rxValidColor.test(user.color)) {\n        // We only support 6-digit RGB colors in y-prosemirror\n        console.warn('A user uses an unsupported color format', user)\n      }\n      if (user.name == null) {\n        user.name = `User: ${clientId}`\n      }\n      let anchor = (0,_lib_js__WEBPACK_IMPORTED_MODULE_3__.relativePositionToAbsolutePosition)(\n        y,\n        ystate.type,\n        yjs__WEBPACK_IMPORTED_MODULE_4__.createRelativePositionFromJSON(aw.cursor.anchor),\n        ystate.binding.mapping\n      )\n      let head = (0,_lib_js__WEBPACK_IMPORTED_MODULE_3__.relativePositionToAbsolutePosition)(\n        y,\n        ystate.type,\n        yjs__WEBPACK_IMPORTED_MODULE_4__.createRelativePositionFromJSON(aw.cursor.head),\n        ystate.binding.mapping\n      )\n      if (anchor !== null && head !== null) {\n        const maxsize = lib0_math__WEBPACK_IMPORTED_MODULE_5__.max(state.doc.content.size - 1, 0)\n        anchor = lib0_math__WEBPACK_IMPORTED_MODULE_5__.min(anchor, maxsize)\n        head = lib0_math__WEBPACK_IMPORTED_MODULE_5__.min(head, maxsize)\n        decorations.push(\n          prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.widget(head, () => createCursor(user), {\n            key: clientId + '',\n            side: 10\n          })\n        )\n        const from = lib0_math__WEBPACK_IMPORTED_MODULE_5__.min(anchor, head)\n        const to = lib0_math__WEBPACK_IMPORTED_MODULE_5__.max(anchor, head)\n        decorations.push(\n          prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.inline(from, to, createSelection(user), {\n            inclusiveEnd: true,\n            inclusiveStart: false\n          })\n        )\n      }\n    }\n  })\n  return prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.DecorationSet.create(state.doc, decorations)\n}\n\n/**\n * A prosemirror plugin that listens to awareness information on Yjs.\n * This requires that a `prosemirrorPlugin` is also bound to the prosemirror.\n *\n * @public\n * @param {Awareness} awareness\n * @param {object} opts\n * @param {function(any, any, any):boolean} [opts.awarenessStateFilter]\n * @param {function(any):HTMLElement} [opts.cursorBuilder]\n * @param {function(any):import('prosemirror-view').DecorationAttrs} [opts.selectionBuilder]\n * @param {function(any):any} [opts.getSelection]\n * @param {string} [cursorStateField] By default all editor bindings use the awareness 'cursor' field to propagate cursor information.\n * @return {any}\n */\nconst yCursorPlugin = (\n  awareness,\n  {\n    awarenessStateFilter = defaultAwarenessStateFilter,\n    cursorBuilder = defaultCursorBuilder,\n    selectionBuilder = defaultSelectionBuilder,\n    getSelection = (state) => state.selection\n  } = {},\n  cursorStateField = 'cursor'\n) =>\n  new prosemirror_state__WEBPACK_IMPORTED_MODULE_6__.Plugin({\n    key: _keys_js__WEBPACK_IMPORTED_MODULE_1__.yCursorPluginKey,\n    state: {\n      init (_, state) {\n        return createDecorations(\n          state,\n          awareness,\n          awarenessStateFilter,\n          cursorBuilder,\n          selectionBuilder\n        )\n      },\n      apply (tr, prevState, _oldState, newState) {\n        const ystate = _keys_js__WEBPACK_IMPORTED_MODULE_1__.ySyncPluginKey.getState(newState)\n        const yCursorState = tr.getMeta(_keys_js__WEBPACK_IMPORTED_MODULE_1__.yCursorPluginKey)\n        if (\n          (ystate && ystate.isChangeOrigin) ||\n          (yCursorState && yCursorState.awarenessUpdated)\n        ) {\n          return createDecorations(\n            newState,\n            awareness,\n            awarenessStateFilter,\n            cursorBuilder,\n            selectionBuilder\n          )\n        }\n        return prevState.map(tr.mapping, tr.doc)\n      }\n    },\n    props: {\n      decorations: (state) => {\n        return _keys_js__WEBPACK_IMPORTED_MODULE_1__.yCursorPluginKey.getState(state)\n      }\n    },\n    view: (view) => {\n      const awarenessListener = () => {\n        // @ts-ignore\n        if (view.docView) {\n          (0,_lib_js__WEBPACK_IMPORTED_MODULE_3__.setMeta)(view, _keys_js__WEBPACK_IMPORTED_MODULE_1__.yCursorPluginKey, { awarenessUpdated: true })\n        }\n      }\n      const updateCursorInfo = () => {\n        const ystate = _keys_js__WEBPACK_IMPORTED_MODULE_1__.ySyncPluginKey.getState(view.state)\n        // @note We make implicit checks when checking for the cursor property\n        const current = awareness.getLocalState() || {}\n        if (view.hasFocus()) {\n          const selection = getSelection(view.state)\n          /**\n           * @type {Y.RelativePosition}\n           */\n          const anchor = (0,_lib_js__WEBPACK_IMPORTED_MODULE_3__.absolutePositionToRelativePosition)(\n            selection.anchor,\n            ystate.type,\n            ystate.binding.mapping\n          )\n          /**\n           * @type {Y.RelativePosition}\n           */\n          const head = (0,_lib_js__WEBPACK_IMPORTED_MODULE_3__.absolutePositionToRelativePosition)(\n            selection.head,\n            ystate.type,\n            ystate.binding.mapping\n          )\n          if (\n            current.cursor == null ||\n            !yjs__WEBPACK_IMPORTED_MODULE_4__.compareRelativePositions(\n              yjs__WEBPACK_IMPORTED_MODULE_4__.createRelativePositionFromJSON(current.cursor.anchor),\n              anchor\n            ) ||\n            !yjs__WEBPACK_IMPORTED_MODULE_4__.compareRelativePositions(\n              yjs__WEBPACK_IMPORTED_MODULE_4__.createRelativePositionFromJSON(current.cursor.head),\n              head\n            )\n          ) {\n            awareness.setLocalStateField(cursorStateField, {\n              anchor,\n              head\n            })\n          }\n        } else if (\n          current.cursor != null &&\n          (0,_lib_js__WEBPACK_IMPORTED_MODULE_3__.relativePositionToAbsolutePosition)(\n            ystate.doc,\n            ystate.type,\n            yjs__WEBPACK_IMPORTED_MODULE_4__.createRelativePositionFromJSON(current.cursor.anchor),\n            ystate.binding.mapping\n          ) !== null\n        ) {\n          // delete cursor information if current cursor information is owned by this editor binding\n          awareness.setLocalStateField(cursorStateField, null)\n        }\n      }\n      awareness.on('change', awarenessListener)\n      view.dom.addEventListener('focusin', updateCursorInfo)\n      view.dom.addEventListener('focusout', updateCursorInfo)\n      return {\n        update: updateCursorInfo,\n        destroy: () => {\n          view.dom.removeEventListener('focusin', updateCursorInfo)\n          view.dom.removeEventListener('focusout', updateCursorInfo)\n          awareness.off('change', awarenessListener)\n          awareness.setLocalStateField(cursorStateField, null)\n        }\n      }\n    }\n  })\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/y-prosemirror/src/plugins/cursor-plugin.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/y-prosemirror/src/plugins/keys.js":
/*!********************************************************!*\
  !*** ./node_modules/y-prosemirror/src/plugins/keys.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   yCursorPluginKey: () => (/* binding */ yCursorPluginKey),\n/* harmony export */   ySyncPluginKey: () => (/* binding */ ySyncPluginKey),\n/* harmony export */   yUndoPluginKey: () => (/* binding */ yUndoPluginKey)\n/* harmony export */ });\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/./node_modules/prosemirror-state/dist/index.js\");\n // eslint-disable-line\n\n/**\n * The unique prosemirror plugin key for syncPlugin\n *\n * @public\n */\nconst ySyncPluginKey = new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey('y-sync')\n\n/**\n * The unique prosemirror plugin key for undoPlugin\n *\n * @public\n */\nconst yUndoPluginKey = new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey('y-undo')\n\n/**\n * The unique prosemirror plugin key for cursorPlugin\n *\n * @public\n */\nconst yCursorPluginKey = new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey('yjs-cursor')\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMveS1wcm9zZW1pcnJvci9zcmMvcGx1Z2lucy9rZXlzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBNkM7O0FBRTdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTywyQkFBMkIsd0RBQVM7O0FBRTNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTywyQkFBMkIsd0RBQVM7O0FBRTNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTyw2QkFBNkIsd0RBQVMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3ktcHJvc2VtaXJyb3Ivc3JjL3BsdWdpbnMva2V5cy5qcz85NDRmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBsdWdpbktleSB9IGZyb20gJ3Byb3NlbWlycm9yLXN0YXRlJyAvLyBlc2xpbnQtZGlzYWJsZS1saW5lXG5cbi8qKlxuICogVGhlIHVuaXF1ZSBwcm9zZW1pcnJvciBwbHVnaW4ga2V5IGZvciBzeW5jUGx1Z2luXG4gKlxuICogQHB1YmxpY1xuICovXG5leHBvcnQgY29uc3QgeVN5bmNQbHVnaW5LZXkgPSBuZXcgUGx1Z2luS2V5KCd5LXN5bmMnKVxuXG4vKipcbiAqIFRoZSB1bmlxdWUgcHJvc2VtaXJyb3IgcGx1Z2luIGtleSBmb3IgdW5kb1BsdWdpblxuICpcbiAqIEBwdWJsaWNcbiAqL1xuZXhwb3J0IGNvbnN0IHlVbmRvUGx1Z2luS2V5ID0gbmV3IFBsdWdpbktleSgneS11bmRvJylcblxuLyoqXG4gKiBUaGUgdW5pcXVlIHByb3NlbWlycm9yIHBsdWdpbiBrZXkgZm9yIGN1cnNvclBsdWdpblxuICpcbiAqIEBwdWJsaWNcbiAqL1xuZXhwb3J0IGNvbnN0IHlDdXJzb3JQbHVnaW5LZXkgPSBuZXcgUGx1Z2luS2V5KCd5anMtY3Vyc29yJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/y-prosemirror/src/plugins/keys.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/y-prosemirror/src/plugins/sync-plugin.js":
/*!***************************************************************!*\
  !*** ./node_modules/y-prosemirror/src/plugins/sync-plugin.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProsemirrorBinding: () => (/* binding */ ProsemirrorBinding),\n/* harmony export */   createNodeFromYElement: () => (/* binding */ createNodeFromYElement),\n/* harmony export */   getRelativeSelection: () => (/* binding */ getRelativeSelection),\n/* harmony export */   isVisible: () => (/* binding */ isVisible),\n/* harmony export */   updateYFragment: () => (/* binding */ updateYFragment),\n/* harmony export */   ySyncPlugin: () => (/* binding */ ySyncPlugin)\n/* harmony export */ });\n/* harmony import */ var lib0_mutex__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lib0/mutex */ \"(ssr)/./node_modules/lib0/mutex.js\");\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/./node_modules/prosemirror-model/dist/index.js\");\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/./node_modules/prosemirror-state/dist/index.js\");\n/* harmony import */ var lib0_math__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! lib0/math */ \"(ssr)/./node_modules/lib0/math.js\");\n/* harmony import */ var lib0_object__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! lib0/object */ \"(ssr)/./node_modules/lib0/object.js\");\n/* harmony import */ var lib0_set__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lib0/set */ \"(ssr)/./node_modules/lib0/set.js\");\n/* harmony import */ var lib0_diff__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! lib0/diff */ \"(ssr)/./node_modules/lib0/diff.js\");\n/* harmony import */ var lib0_error__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! lib0/error */ \"(ssr)/./node_modules/lib0/error.js\");\n/* harmony import */ var _keys_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./keys.js */ \"(ssr)/./node_modules/y-prosemirror/src/plugins/keys.js\");\n/* harmony import */ var yjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! yjs */ \"(ssr)/./node_modules/yjs/dist/yjs.mjs\");\n/* harmony import */ var _lib_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../lib.js */ \"(ssr)/./node_modules/y-prosemirror/src/lib.js\");\n/* harmony import */ var lib0_random__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lib0/random */ \"(ssr)/./node_modules/lib0/random.js\");\n/* harmony import */ var lib0_environment__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lib0/environment */ \"(ssr)/./node_modules/lib0/environment.js\");\n/* harmony import */ var lib0_dom__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! lib0/dom */ \"(ssr)/./node_modules/lib0/dom.js\");\n/* harmony import */ var lib0_eventloop__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lib0/eventloop */ \"(ssr)/./node_modules/lib0/eventloop.js\");\n/**\n * @module bindings/prosemirror\n */\n\n\n\n // eslint-disable-line\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * @param {Y.Item} item\n * @param {Y.Snapshot} [snapshot]\n */\nconst isVisible = (item, snapshot) =>\n  snapshot === undefined\n    ? !item.deleted\n    : (snapshot.sv.has(item.id.client) && /** @type {number} */\n      (snapshot.sv.get(item.id.client)) > item.id.clock &&\n      !yjs__WEBPACK_IMPORTED_MODULE_0__.isDeleted(snapshot.ds, item.id))\n\n/**\n * Either a node if type is YXmlElement or an Array of text nodes if YXmlText\n * @typedef {Map<Y.AbstractType<any>, PModel.Node | Array<PModel.Node>>} ProsemirrorMapping\n */\n\n/**\n * @typedef {Object} ColorDef\n * @property {string} ColorDef.light\n * @property {string} ColorDef.dark\n */\n\n/**\n * @typedef {Object} YSyncOpts\n * @property {Array<ColorDef>} [YSyncOpts.colors]\n * @property {Map<string,ColorDef>} [YSyncOpts.colorMapping]\n * @property {Y.PermanentUserData|null} [YSyncOpts.permanentUserData]\n * @property {ProsemirrorMapping} [YSyncOpts.mapping]\n * @property {function} [YSyncOpts.onFirstRender] Fired when the content from Yjs is initially rendered to ProseMirror\n */\n\n/**\n * @type {Array<ColorDef>}\n */\nconst defaultColors = [{ light: '#ecd44433', dark: '#ecd444' }]\n\n/**\n * @param {Map<string,ColorDef>} colorMapping\n * @param {Array<ColorDef>} colors\n * @param {string} user\n * @return {ColorDef}\n */\nconst getUserColor = (colorMapping, colors, user) => {\n  // @todo do not hit the same color twice if possible\n  if (!colorMapping.has(user)) {\n    if (colorMapping.size < colors.length) {\n      const usedColors = lib0_set__WEBPACK_IMPORTED_MODULE_1__.create()\n      colorMapping.forEach((color) => usedColors.add(color))\n      colors = colors.filter((color) => !usedColors.has(color))\n    }\n    colorMapping.set(user, lib0_random__WEBPACK_IMPORTED_MODULE_2__.oneOf(colors))\n  }\n  return /** @type {ColorDef} */ (colorMapping.get(user))\n}\n\n/**\n * This plugin listens to changes in prosemirror view and keeps yXmlState and view in sync.\n *\n * This plugin also keeps references to the type and the shared document so other plugins can access it.\n * @param {Y.XmlFragment} yXmlFragment\n * @param {YSyncOpts} opts\n * @return {any} Returns a prosemirror plugin that binds to this type\n */\nconst ySyncPlugin = (yXmlFragment, {\n  colors = defaultColors,\n  colorMapping = new Map(),\n  permanentUserData = null,\n  onFirstRender = () => {},\n  mapping\n} = {}) => {\n  let initialContentChanged = false\n  const binding = new ProsemirrorBinding(yXmlFragment, mapping)\n  const plugin = new prosemirror_state__WEBPACK_IMPORTED_MODULE_3__.Plugin({\n    props: {\n      editable: (state) => {\n        const syncState = _keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey.getState(state)\n        return syncState.snapshot == null && syncState.prevSnapshot == null\n      }\n    },\n    key: _keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey,\n    state: {\n      /**\n       * @returns {any}\n       */\n      init: (_initargs, _state) => {\n        return {\n          type: yXmlFragment,\n          doc: yXmlFragment.doc,\n          binding,\n          snapshot: null,\n          prevSnapshot: null,\n          isChangeOrigin: false,\n          isUndoRedoOperation: false,\n          addToHistory: true,\n          colors,\n          colorMapping,\n          permanentUserData\n        }\n      },\n      apply: (tr, pluginState) => {\n        const change = tr.getMeta(_keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey)\n        if (change !== undefined) {\n          pluginState = Object.assign({}, pluginState)\n          for (const key in change) {\n            pluginState[key] = change[key]\n          }\n        }\n        pluginState.addToHistory = tr.getMeta('addToHistory') !== false\n        // always set isChangeOrigin. If undefined, this is not change origin.\n        pluginState.isChangeOrigin = change !== undefined &&\n          !!change.isChangeOrigin\n        pluginState.isUndoRedoOperation = change !== undefined && !!change.isChangeOrigin && !!change.isUndoRedoOperation\n        if (binding.prosemirrorView !== null) {\n          if (\n            change !== undefined &&\n            (change.snapshot != null || change.prevSnapshot != null)\n          ) {\n            // snapshot changed, rerender next\n            lib0_eventloop__WEBPACK_IMPORTED_MODULE_5__.timeout(0, () => {\n              if (binding.prosemirrorView == null) {\n                return\n              }\n              if (change.restore == null) {\n                binding._renderSnapshot(\n                  change.snapshot,\n                  change.prevSnapshot,\n                  pluginState\n                )\n              } else {\n                binding._renderSnapshot(\n                  change.snapshot,\n                  change.snapshot,\n                  pluginState\n                )\n                // reset to current prosemirror state\n                delete pluginState.restore\n                delete pluginState.snapshot\n                delete pluginState.prevSnapshot\n                binding.mux(() => {\n                  binding._prosemirrorChanged(\n                    binding.prosemirrorView.state.doc\n                  )\n                })\n              }\n            })\n          }\n        }\n        return pluginState\n      }\n    },\n    view: (view) => {\n      binding.initView(view)\n      if (mapping == null) {\n        // force rerender to update the bindings mapping\n        binding._forceRerender()\n      }\n      onFirstRender()\n      return {\n        update: () => {\n          const pluginState = plugin.getState(view.state)\n          if (\n            pluginState.snapshot == null && pluginState.prevSnapshot == null\n          ) {\n            if (\n              // If the content doesn't change initially, we don't render anything to Yjs\n              // If the content was cleared by a user action, we want to catch the change and\n              // represent it in Yjs\n              initialContentChanged ||\n              view.state.doc.content.findDiffStart(\n                view.state.doc.type.createAndFill().content\n              ) !== null\n            ) {\n              initialContentChanged = true\n              if (\n                pluginState.addToHistory === false &&\n                !pluginState.isChangeOrigin\n              ) {\n                const yUndoPluginState = _keys_js__WEBPACK_IMPORTED_MODULE_4__.yUndoPluginKey.getState(view.state)\n                /**\n                 * @type {Y.UndoManager}\n                 */\n                const um = yUndoPluginState && yUndoPluginState.undoManager\n                if (um) {\n                  um.stopCapturing()\n                }\n              }\n              binding.mux(() => {\n                /** @type {Y.Doc} */ (pluginState.doc).transact((tr) => {\n                  tr.meta.set('addToHistory', pluginState.addToHistory)\n                  binding._prosemirrorChanged(view.state.doc)\n                }, _keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey)\n              })\n            }\n          }\n        },\n        destroy: () => {\n          binding.destroy()\n        }\n      }\n    }\n  })\n  return plugin\n}\n\n/**\n * @param {any} tr\n * @param {any} relSel\n * @param {ProsemirrorBinding} binding\n */\nconst restoreRelativeSelection = (tr, relSel, binding) => {\n  if (relSel !== null && relSel.anchor !== null && relSel.head !== null) {\n    const anchor = (0,_lib_js__WEBPACK_IMPORTED_MODULE_6__.relativePositionToAbsolutePosition)(\n      binding.doc,\n      binding.type,\n      relSel.anchor,\n      binding.mapping\n    )\n    const head = (0,_lib_js__WEBPACK_IMPORTED_MODULE_6__.relativePositionToAbsolutePosition)(\n      binding.doc,\n      binding.type,\n      relSel.head,\n      binding.mapping\n    )\n    if (anchor !== null && head !== null) {\n      tr = tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_3__.TextSelection.create(tr.doc, anchor, head))\n    }\n  }\n}\n\nconst getRelativeSelection = (pmbinding, state) => ({\n  anchor: (0,_lib_js__WEBPACK_IMPORTED_MODULE_6__.absolutePositionToRelativePosition)(\n    state.selection.anchor,\n    pmbinding.type,\n    pmbinding.mapping\n  ),\n  head: (0,_lib_js__WEBPACK_IMPORTED_MODULE_6__.absolutePositionToRelativePosition)(\n    state.selection.head,\n    pmbinding.type,\n    pmbinding.mapping\n  )\n})\n\n/**\n * Binding for prosemirror.\n *\n * @protected\n */\nclass ProsemirrorBinding {\n  /**\n   * @param {Y.XmlFragment} yXmlFragment The bind source\n   * @param {ProsemirrorMapping} mapping\n   */\n  constructor (yXmlFragment, mapping = new Map()) {\n    this.type = yXmlFragment\n    /**\n     * this will be set once the view is created\n     * @type {any}\n     */\n    this.prosemirrorView = null\n    this.mux = (0,lib0_mutex__WEBPACK_IMPORTED_MODULE_7__.createMutex)()\n    this.mapping = mapping\n    this._observeFunction = this._typeChanged.bind(this)\n    /**\n     * @type {Y.Doc}\n     */\n    // @ts-ignore\n    this.doc = yXmlFragment.doc\n    /**\n     * current selection as relative positions in the Yjs model\n     */\n    this.beforeTransactionSelection = null\n    this.beforeAllTransactions = () => {\n      if (this.beforeTransactionSelection === null && this.prosemirrorView != null) {\n        this.beforeTransactionSelection = getRelativeSelection(\n          this,\n          this.prosemirrorView.state\n        )\n      }\n    }\n    this.afterAllTransactions = () => {\n      this.beforeTransactionSelection = null\n    }\n    this._domSelectionInView = null\n  }\n\n  /**\n   * Create a transaction for changing the prosemirror state.\n   *\n   * @returns\n   */\n  get _tr () {\n    return this.prosemirrorView.state.tr.setMeta('addToHistory', false)\n  }\n\n  _isLocalCursorInView () {\n    if (!this.prosemirrorView.hasFocus()) return false\n    if (lib0_environment__WEBPACK_IMPORTED_MODULE_8__.isBrowser && this._domSelectionInView === null) {\n      // Calculate the domSelectionInView and clear by next tick after all events are finished\n      lib0_eventloop__WEBPACK_IMPORTED_MODULE_5__.timeout(0, () => {\n        this._domSelectionInView = null\n      })\n      this._domSelectionInView = this._isDomSelectionInView()\n    }\n    return this._domSelectionInView\n  }\n\n  _isDomSelectionInView () {\n    const selection = this.prosemirrorView._root.getSelection()\n\n    const range = this.prosemirrorView._root.createRange()\n    range.setStart(selection.anchorNode, selection.anchorOffset)\n    range.setEnd(selection.focusNode, selection.focusOffset)\n\n    // This is a workaround for an edgecase where getBoundingClientRect will\n    // return zero values if the selection is collapsed at the start of a newline\n    // see reference here: https://stackoverflow.com/a/59780954\n    const rects = range.getClientRects()\n    if (rects.length === 0) {\n      // probably buggy newline behavior, explicitly select the node contents\n      if (range.startContainer && range.collapsed) {\n        range.selectNodeContents(range.startContainer)\n      }\n    }\n\n    const bounding = range.getBoundingClientRect()\n    const documentElement = lib0_dom__WEBPACK_IMPORTED_MODULE_9__.doc.documentElement\n\n    return bounding.bottom >= 0 && bounding.right >= 0 &&\n      bounding.left <=\n        (window.innerWidth || documentElement.clientWidth || 0) &&\n      bounding.top <= (window.innerHeight || documentElement.clientHeight || 0)\n  }\n\n  /**\n   * @param {Y.Snapshot} snapshot\n   * @param {Y.Snapshot} prevSnapshot\n   */\n  renderSnapshot (snapshot, prevSnapshot) {\n    if (!prevSnapshot) {\n      prevSnapshot = yjs__WEBPACK_IMPORTED_MODULE_0__.createSnapshot(yjs__WEBPACK_IMPORTED_MODULE_0__.createDeleteSet(), new Map())\n    }\n    this.prosemirrorView.dispatch(\n      this._tr.setMeta(_keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey, { snapshot, prevSnapshot })\n    )\n  }\n\n  unrenderSnapshot () {\n    this.mapping.clear()\n    this.mux(() => {\n      const fragmentContent = this.type.toArray().map((t) =>\n        createNodeFromYElement(\n          /** @type {Y.XmlElement} */ (t),\n          this.prosemirrorView.state.schema,\n          this.mapping\n        )\n      ).filter((n) => n !== null)\n      // @ts-ignore\n      const tr = this._tr.replace(\n        0,\n        this.prosemirrorView.state.doc.content.size,\n        new prosemirror_model__WEBPACK_IMPORTED_MODULE_10__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_10__.Fragment.from(fragmentContent), 0, 0)\n      )\n      tr.setMeta(_keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey, { snapshot: null, prevSnapshot: null })\n      this.prosemirrorView.dispatch(tr)\n    })\n  }\n\n  _forceRerender () {\n    this.mapping.clear()\n    this.mux(() => {\n      // If this is a forced rerender, this might neither happen as a pm change nor within a Yjs\n      // transaction. Then the \"before selection\" doesn't exist. In this case, we need to create a\n      // relative position before replacing content. Fixes #126\n      const sel = this.beforeTransactionSelection !== null ? null : this.prosemirrorView.state.selection\n      const fragmentContent = this.type.toArray().map((t) =>\n        createNodeFromYElement(\n          /** @type {Y.XmlElement} */ (t),\n          this.prosemirrorView.state.schema,\n          this.mapping\n        )\n      ).filter((n) => n !== null)\n      // @ts-ignore\n      const tr = this._tr.replace(\n        0,\n        this.prosemirrorView.state.doc.content.size,\n        new prosemirror_model__WEBPACK_IMPORTED_MODULE_10__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_10__.Fragment.from(fragmentContent), 0, 0)\n      )\n      if (sel) {\n        tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_3__.TextSelection.create(tr.doc, sel.anchor, sel.head))\n      }\n      this.prosemirrorView.dispatch(\n        tr.setMeta(_keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey, { isChangeOrigin: true, binding: this })\n      )\n    })\n  }\n\n  /**\n   * @param {Y.Snapshot|Uint8Array} snapshot\n   * @param {Y.Snapshot|Uint8Array} prevSnapshot\n   * @param {Object} pluginState\n   */\n  _renderSnapshot (snapshot, prevSnapshot, pluginState) {\n    /**\n     * The document that contains the full history of this document.\n     * @type {Y.Doc}\n     */\n    let historyDoc = this.doc\n    if (!snapshot) {\n      snapshot = yjs__WEBPACK_IMPORTED_MODULE_0__.snapshot(this.doc)\n    }\n    if (snapshot instanceof Uint8Array || prevSnapshot instanceof Uint8Array) {\n      if (!(snapshot instanceof Uint8Array) || !(prevSnapshot instanceof Uint8Array)) {\n        // expected both snapshots to be v2 updates\n        lib0_error__WEBPACK_IMPORTED_MODULE_11__.unexpectedCase()\n      }\n      historyDoc = new yjs__WEBPACK_IMPORTED_MODULE_0__.Doc({ gc: false })\n      yjs__WEBPACK_IMPORTED_MODULE_0__.applyUpdateV2(historyDoc, prevSnapshot)\n      prevSnapshot = yjs__WEBPACK_IMPORTED_MODULE_0__.snapshot(historyDoc)\n      yjs__WEBPACK_IMPORTED_MODULE_0__.applyUpdateV2(historyDoc, snapshot)\n      snapshot = yjs__WEBPACK_IMPORTED_MODULE_0__.snapshot(historyDoc)\n    }\n    // clear mapping because we are going to rerender\n    this.mapping.clear()\n    this.mux(() => {\n      historyDoc.transact((transaction) => {\n        // before rendering, we are going to sanitize ops and split deleted ops\n        // if they were deleted by seperate users.\n        const pud = pluginState.permanentUserData\n        if (pud) {\n          pud.dss.forEach((ds) => {\n            yjs__WEBPACK_IMPORTED_MODULE_0__.iterateDeletedStructs(transaction, ds, (_item) => {})\n          })\n        }\n        /**\n         * @param {'removed'|'added'} type\n         * @param {Y.ID} id\n         */\n        const computeYChange = (type, id) => {\n          const user = type === 'added'\n            ? pud.getUserByClientId(id.client)\n            : pud.getUserByDeletedId(id)\n          return {\n            user,\n            type,\n            color: getUserColor(\n              pluginState.colorMapping,\n              pluginState.colors,\n              user\n            )\n          }\n        }\n        // Create document fragment and render\n        const fragmentContent = yjs__WEBPACK_IMPORTED_MODULE_0__.typeListToArraySnapshot(\n          this.type,\n          new yjs__WEBPACK_IMPORTED_MODULE_0__.Snapshot(prevSnapshot.ds, snapshot.sv)\n        ).map((t) => {\n          if (\n            !t._item.deleted || isVisible(t._item, snapshot) ||\n            isVisible(t._item, prevSnapshot)\n          ) {\n            return createNodeFromYElement(\n              t,\n              this.prosemirrorView.state.schema,\n              new Map(),\n              snapshot,\n              prevSnapshot,\n              computeYChange\n            )\n          } else {\n            // No need to render elements that are not visible by either snapshot.\n            // If a client adds and deletes content in the same snapshot the element is not visible by either snapshot.\n            return null\n          }\n        }).filter((n) => n !== null)\n        // @ts-ignore\n        const tr = this._tr.replace(\n          0,\n          this.prosemirrorView.state.doc.content.size,\n          new prosemirror_model__WEBPACK_IMPORTED_MODULE_10__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_10__.Fragment.from(fragmentContent), 0, 0)\n        )\n        this.prosemirrorView.dispatch(\n          tr.setMeta(_keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey, { isChangeOrigin: true })\n        )\n      }, _keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey)\n    })\n  }\n\n  /**\n   * @param {Array<Y.YEvent<any>>} events\n   * @param {Y.Transaction} transaction\n   */\n  _typeChanged (events, transaction) {\n    if (this.prosemirrorView == null) return\n    const syncState = _keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey.getState(this.prosemirrorView.state)\n    if (\n      events.length === 0 || syncState.snapshot != null ||\n      syncState.prevSnapshot != null\n    ) {\n      // drop out if snapshot is active\n      this.renderSnapshot(syncState.snapshot, syncState.prevSnapshot)\n      return\n    }\n    this.mux(() => {\n      /**\n       * @param {any} _\n       * @param {Y.AbstractType<any>} type\n       */\n      const delType = (_, type) => this.mapping.delete(type)\n      yjs__WEBPACK_IMPORTED_MODULE_0__.iterateDeletedStructs(\n        transaction,\n        transaction.deleteSet,\n        (struct) => {\n          if (struct.constructor === yjs__WEBPACK_IMPORTED_MODULE_0__.Item) {\n            const type = /** @type {Y.ContentType} */ (/** @type {Y.Item} */ (struct).content).type\n            type && this.mapping.delete(type)\n          }\n        }\n      )\n      transaction.changed.forEach(delType)\n      transaction.changedParentTypes.forEach(delType)\n      const fragmentContent = this.type.toArray().map((t) =>\n        createNodeIfNotExists(\n          /** @type {Y.XmlElement | Y.XmlHook} */ (t),\n          this.prosemirrorView.state.schema,\n          this.mapping\n        )\n      ).filter((n) => n !== null)\n      // @ts-ignore\n      let tr = this._tr.replace(\n        0,\n        this.prosemirrorView.state.doc.content.size,\n        new prosemirror_model__WEBPACK_IMPORTED_MODULE_10__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_10__.Fragment.from(fragmentContent), 0, 0)\n      )\n      restoreRelativeSelection(tr, this.beforeTransactionSelection, this)\n      tr = tr.setMeta(_keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey, { isChangeOrigin: true, isUndoRedoOperation: transaction.origin instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.UndoManager })\n      if (\n        this.beforeTransactionSelection !== null && this._isLocalCursorInView()\n      ) {\n        tr.scrollIntoView()\n      }\n      this.prosemirrorView.dispatch(tr)\n    })\n  }\n\n  _prosemirrorChanged (doc) {\n    this.doc.transact(() => {\n      updateYFragment(this.doc, this.type, doc, this.mapping)\n      this.beforeTransactionSelection = getRelativeSelection(\n        this,\n        this.prosemirrorView.state\n      )\n    }, _keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey)\n  }\n\n  /**\n   * View is ready to listen to changes. Register observers.\n   * @param {any} prosemirrorView\n   */\n  initView (prosemirrorView) {\n    if (this.prosemirrorView != null) this.destroy()\n    this.prosemirrorView = prosemirrorView\n    this.doc.on('beforeAllTransactions', this.beforeAllTransactions)\n    this.doc.on('afterAllTransactions', this.afterAllTransactions)\n    this.type.observeDeep(this._observeFunction)\n  }\n\n  destroy () {\n    if (this.prosemirrorView == null) return\n    this.prosemirrorView = null\n    this.type.unobserveDeep(this._observeFunction)\n    this.doc.off('beforeAllTransactions', this.beforeAllTransactions)\n    this.doc.off('afterAllTransactions', this.afterAllTransactions)\n  }\n}\n\n/**\n * @private\n * @param {Y.XmlElement | Y.XmlHook} el\n * @param {PModel.Schema} schema\n * @param {ProsemirrorMapping} mapping\n * @param {Y.Snapshot} [snapshot]\n * @param {Y.Snapshot} [prevSnapshot]\n * @param {function('removed' | 'added', Y.ID):any} [computeYChange]\n * @return {PModel.Node | null}\n */\nconst createNodeIfNotExists = (\n  el,\n  schema,\n  mapping,\n  snapshot,\n  prevSnapshot,\n  computeYChange\n) => {\n  const node = /** @type {PModel.Node} */ (mapping.get(el))\n  if (node === undefined) {\n    if (el instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.XmlElement) {\n      return createNodeFromYElement(\n        el,\n        schema,\n        mapping,\n        snapshot,\n        prevSnapshot,\n        computeYChange\n      )\n    } else {\n      throw lib0_error__WEBPACK_IMPORTED_MODULE_11__.methodUnimplemented() // we are currently not handling hooks\n    }\n  }\n  return node\n}\n\n/**\n * @private\n * @param {Y.XmlElement} el\n * @param {any} schema\n * @param {ProsemirrorMapping} mapping\n * @param {Y.Snapshot} [snapshot]\n * @param {Y.Snapshot} [prevSnapshot]\n * @param {function('removed' | 'added', Y.ID):any} [computeYChange]\n * @return {PModel.Node | null} Returns node if node could be created. Otherwise it deletes the yjs type and returns null\n */\nconst createNodeFromYElement = (\n  el,\n  schema,\n  mapping,\n  snapshot,\n  prevSnapshot,\n  computeYChange\n) => {\n  const children = []\n  const createChildren = (type) => {\n    if (type.constructor === yjs__WEBPACK_IMPORTED_MODULE_0__.XmlElement) {\n      const n = createNodeIfNotExists(\n        type,\n        schema,\n        mapping,\n        snapshot,\n        prevSnapshot,\n        computeYChange\n      )\n      if (n !== null) {\n        children.push(n)\n      }\n    } else {\n      // If the next ytext exists and was created by us, move the content to the current ytext.\n      // This is a fix for #160 -- duplication of characters when two Y.Text exist next to each\n      // other.\n      const nextytext = type._item.right?.content.type\n      if (nextytext instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.Text && !nextytext._item.deleted && nextytext._item.id.client === nextytext.doc.clientID) {\n        type.applyDelta([\n          { retain: type.length },\n          ...nextytext.toDelta()\n        ])\n        nextytext.doc.transact(tr => {\n          nextytext._item.delete(tr)\n        })\n      }\n      // now create the prosemirror text nodes\n      const ns = createTextNodesFromYText(\n        type,\n        schema,\n        mapping,\n        snapshot,\n        prevSnapshot,\n        computeYChange\n      )\n      if (ns !== null) {\n        ns.forEach((textchild) => {\n          if (textchild !== null) {\n            children.push(textchild)\n          }\n        })\n      }\n    }\n  }\n  if (snapshot === undefined || prevSnapshot === undefined) {\n    el.toArray().forEach(createChildren)\n  } else {\n    yjs__WEBPACK_IMPORTED_MODULE_0__.typeListToArraySnapshot(el, new yjs__WEBPACK_IMPORTED_MODULE_0__.Snapshot(prevSnapshot.ds, snapshot.sv))\n      .forEach(createChildren)\n  }\n  try {\n    const attrs = el.getAttributes(snapshot)\n    if (snapshot !== undefined) {\n      if (!isVisible(/** @type {Y.Item} */ (el._item), snapshot)) {\n        attrs.ychange = computeYChange\n          ? computeYChange('removed', /** @type {Y.Item} */ (el._item).id)\n          : { type: 'removed' }\n      } else if (!isVisible(/** @type {Y.Item} */ (el._item), prevSnapshot)) {\n        attrs.ychange = computeYChange\n          ? computeYChange('added', /** @type {Y.Item} */ (el._item).id)\n          : { type: 'added' }\n      }\n    }\n    const node = schema.node(el.nodeName, attrs, children)\n    mapping.set(el, node)\n    return node\n  } catch (e) {\n    // an error occured while creating the node. This is probably a result of a concurrent action.\n    /** @type {Y.Doc} */ (el.doc).transact((transaction) => {\n      /** @type {Y.Item} */ (el._item).delete(transaction)\n    }, _keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey)\n    mapping.delete(el)\n    return null\n  }\n}\n\n/**\n * @private\n * @param {Y.XmlText} text\n * @param {any} schema\n * @param {ProsemirrorMapping} _mapping\n * @param {Y.Snapshot} [snapshot]\n * @param {Y.Snapshot} [prevSnapshot]\n * @param {function('removed' | 'added', Y.ID):any} [computeYChange]\n * @return {Array<PModel.Node>|null}\n */\nconst createTextNodesFromYText = (\n  text,\n  schema,\n  _mapping,\n  snapshot,\n  prevSnapshot,\n  computeYChange\n) => {\n  const nodes = []\n  const deltas = text.toDelta(snapshot, prevSnapshot, computeYChange)\n  try {\n    for (let i = 0; i < deltas.length; i++) {\n      const delta = deltas[i]\n      const marks = []\n      for (const markName in delta.attributes) {\n        marks.push(schema.mark(markName, delta.attributes[markName]))\n      }\n      nodes.push(schema.text(delta.insert, marks))\n    }\n  } catch (e) {\n    // an error occured while creating the node. This is probably a result of a concurrent action.\n    /** @type {Y.Doc} */ (text.doc).transact((transaction) => {\n      /** @type {Y.Item} */ (text._item).delete(transaction)\n    }, _keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey)\n    return null\n  }\n  // @ts-ignore\n  return nodes\n}\n\n/**\n * @private\n * @param {Array<any>} nodes prosemirror node\n * @param {ProsemirrorMapping} mapping\n * @return {Y.XmlText}\n */\nconst createTypeFromTextNodes = (nodes, mapping) => {\n  const type = new yjs__WEBPACK_IMPORTED_MODULE_0__.XmlText()\n  const delta = nodes.map((node) => ({\n    // @ts-ignore\n    insert: node.text,\n    attributes: marksToAttributes(node.marks)\n  }))\n  type.applyDelta(delta)\n  mapping.set(type, nodes)\n  return type\n}\n\n/**\n * @private\n * @param {any} node prosemirror node\n * @param {ProsemirrorMapping} mapping\n * @return {Y.XmlElement}\n */\nconst createTypeFromElementNode = (node, mapping) => {\n  const type = new yjs__WEBPACK_IMPORTED_MODULE_0__.XmlElement(node.type.name)\n  for (const key in node.attrs) {\n    const val = node.attrs[key]\n    if (val !== null && key !== 'ychange') {\n      type.setAttribute(key, val)\n    }\n  }\n  type.insert(\n    0,\n    normalizePNodeContent(node).map((n) =>\n      createTypeFromTextOrElementNode(n, mapping)\n    )\n  )\n  mapping.set(type, node)\n  return type\n}\n\n/**\n * @private\n * @param {PModel.Node|Array<PModel.Node>} node prosemirror text node\n * @param {ProsemirrorMapping} mapping\n * @return {Y.XmlElement|Y.XmlText}\n */\nconst createTypeFromTextOrElementNode = (node, mapping) =>\n  node instanceof Array\n    ? createTypeFromTextNodes(node, mapping)\n    : createTypeFromElementNode(node, mapping)\n\nconst isObject = (val) => typeof val === 'object' && val !== null\n\nconst equalAttrs = (pattrs, yattrs) => {\n  const keys = Object.keys(pattrs).filter((key) => pattrs[key] !== null)\n  let eq =\n    keys.length ===\n      Object.keys(yattrs).filter((key) => yattrs[key] !== null).length\n  for (let i = 0; i < keys.length && eq; i++) {\n    const key = keys[i]\n    const l = pattrs[key]\n    const r = yattrs[key]\n    eq = key === 'ychange' || l === r ||\n      (isObject(l) && isObject(r) && equalAttrs(l, r))\n  }\n  return eq\n}\n\n/**\n * @typedef {Array<Array<PModel.Node>|PModel.Node>} NormalizedPNodeContent\n */\n\n/**\n * @param {any} pnode\n * @return {NormalizedPNodeContent}\n */\nconst normalizePNodeContent = (pnode) => {\n  const c = pnode.content.content\n  const res = []\n  for (let i = 0; i < c.length; i++) {\n    const n = c[i]\n    if (n.isText) {\n      const textNodes = []\n      for (let tnode = c[i]; i < c.length && tnode.isText; tnode = c[++i]) {\n        textNodes.push(tnode)\n      }\n      i--\n      res.push(textNodes)\n    } else {\n      res.push(n)\n    }\n  }\n  return res\n}\n\n/**\n * @param {Y.XmlText} ytext\n * @param {Array<any>} ptexts\n */\nconst equalYTextPText = (ytext, ptexts) => {\n  const delta = ytext.toDelta()\n  return delta.length === ptexts.length &&\n    delta.every((d, i) =>\n      d.insert === /** @type {any} */ (ptexts[i]).text &&\n      lib0_object__WEBPACK_IMPORTED_MODULE_12__.keys(d.attributes || {}).length === ptexts[i].marks.length &&\n      ptexts[i].marks.every((mark) =>\n        equalAttrs(d.attributes[mark.type.name] || {}, mark.attrs)\n      )\n    )\n}\n\n/**\n * @param {Y.XmlElement|Y.XmlText|Y.XmlHook} ytype\n * @param {any|Array<any>} pnode\n */\nconst equalYTypePNode = (ytype, pnode) => {\n  if (\n    ytype instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.XmlElement && !(pnode instanceof Array) &&\n    matchNodeName(ytype, pnode)\n  ) {\n    const normalizedContent = normalizePNodeContent(pnode)\n    return ytype._length === normalizedContent.length &&\n      equalAttrs(ytype.getAttributes(), pnode.attrs) &&\n      ytype.toArray().every((ychild, i) =>\n        equalYTypePNode(ychild, normalizedContent[i])\n      )\n  }\n  return ytype instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.XmlText && pnode instanceof Array &&\n    equalYTextPText(ytype, pnode)\n}\n\n/**\n * @param {PModel.Node | Array<PModel.Node> | undefined} mapped\n * @param {PModel.Node | Array<PModel.Node>} pcontent\n */\nconst mappedIdentity = (mapped, pcontent) =>\n  mapped === pcontent ||\n  (mapped instanceof Array && pcontent instanceof Array &&\n    mapped.length === pcontent.length && mapped.every((a, i) =>\n    pcontent[i] === a\n  ))\n\n/**\n * @param {Y.XmlElement} ytype\n * @param {PModel.Node} pnode\n * @param {ProsemirrorMapping} mapping\n * @return {{ foundMappedChild: boolean, equalityFactor: number }}\n */\nconst computeChildEqualityFactor = (ytype, pnode, mapping) => {\n  const yChildren = ytype.toArray()\n  const pChildren = normalizePNodeContent(pnode)\n  const pChildCnt = pChildren.length\n  const yChildCnt = yChildren.length\n  const minCnt = lib0_math__WEBPACK_IMPORTED_MODULE_13__.min(yChildCnt, pChildCnt)\n  let left = 0\n  let right = 0\n  let foundMappedChild = false\n  for (; left < minCnt; left++) {\n    const leftY = yChildren[left]\n    const leftP = pChildren[left]\n    if (mappedIdentity(mapping.get(leftY), leftP)) {\n      foundMappedChild = true // definite (good) match!\n    } else if (!equalYTypePNode(leftY, leftP)) {\n      break\n    }\n  }\n  for (; left + right < minCnt; right++) {\n    const rightY = yChildren[yChildCnt - right - 1]\n    const rightP = pChildren[pChildCnt - right - 1]\n    if (mappedIdentity(mapping.get(rightY), rightP)) {\n      foundMappedChild = true\n    } else if (!equalYTypePNode(rightY, rightP)) {\n      break\n    }\n  }\n  return {\n    equalityFactor: left + right,\n    foundMappedChild\n  }\n}\n\nconst ytextTrans = (ytext) => {\n  let str = ''\n  /**\n   * @type {Y.Item|null}\n   */\n  let n = ytext._start\n  const nAttrs = {}\n  while (n !== null) {\n    if (!n.deleted) {\n      if (n.countable && n.content instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.ContentString) {\n        str += n.content.str\n      } else if (n.content instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.ContentFormat) {\n        nAttrs[n.content.key] = null\n      }\n    }\n    n = n.right\n  }\n  return {\n    str,\n    nAttrs\n  }\n}\n\n/**\n * @todo test this more\n *\n * @param {Y.Text} ytext\n * @param {Array<any>} ptexts\n * @param {ProsemirrorMapping} mapping\n */\nconst updateYText = (ytext, ptexts, mapping) => {\n  mapping.set(ytext, ptexts)\n  const { nAttrs, str } = ytextTrans(ytext)\n  const content = ptexts.map((p) => ({\n    insert: /** @type {any} */ (p).text,\n    attributes: Object.assign({}, nAttrs, marksToAttributes(p.marks))\n  }))\n  const { insert, remove, index } = (0,lib0_diff__WEBPACK_IMPORTED_MODULE_14__.simpleDiff)(\n    str,\n    content.map((c) => c.insert).join('')\n  )\n  ytext.delete(index, remove)\n  ytext.insert(index, insert)\n  ytext.applyDelta(\n    content.map((c) => ({ retain: c.insert.length, attributes: c.attributes }))\n  )\n}\n\nconst marksToAttributes = (marks) => {\n  const pattrs = {}\n  marks.forEach((mark) => {\n    if (mark.type.name !== 'ychange') {\n      pattrs[mark.type.name] = mark.attrs\n    }\n  })\n  return pattrs\n}\n\n/**\n * Update a yDom node by syncing the current content of the prosemirror node.\n *\n * This is a y-prosemirror internal feature that you can use at your own risk.\n *\n * @private\n * @unstable\n *\n * @param {{transact: Function}} y\n * @param {Y.XmlFragment} yDomFragment\n * @param {any} pNode\n * @param {ProsemirrorMapping} mapping\n */\nconst updateYFragment = (y, yDomFragment, pNode, mapping) => {\n  if (\n    yDomFragment instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.XmlElement &&\n    yDomFragment.nodeName !== pNode.type.name\n  ) {\n    throw new Error('node name mismatch!')\n  }\n  mapping.set(yDomFragment, pNode)\n  // update attributes\n  if (yDomFragment instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.XmlElement) {\n    const yDomAttrs = yDomFragment.getAttributes()\n    const pAttrs = pNode.attrs\n    for (const key in pAttrs) {\n      if (pAttrs[key] !== null) {\n        if (yDomAttrs[key] !== pAttrs[key] && key !== 'ychange') {\n          yDomFragment.setAttribute(key, pAttrs[key])\n        }\n      } else {\n        yDomFragment.removeAttribute(key)\n      }\n    }\n    // remove all keys that are no longer in pAttrs\n    for (const key in yDomAttrs) {\n      if (pAttrs[key] === undefined) {\n        yDomFragment.removeAttribute(key)\n      }\n    }\n  }\n  // update children\n  const pChildren = normalizePNodeContent(pNode)\n  const pChildCnt = pChildren.length\n  const yChildren = yDomFragment.toArray()\n  const yChildCnt = yChildren.length\n  const minCnt = lib0_math__WEBPACK_IMPORTED_MODULE_13__.min(pChildCnt, yChildCnt)\n  let left = 0\n  let right = 0\n  // find number of matching elements from left\n  for (; left < minCnt; left++) {\n    const leftY = yChildren[left]\n    const leftP = pChildren[left]\n    if (!mappedIdentity(mapping.get(leftY), leftP)) {\n      if (equalYTypePNode(leftY, leftP)) {\n        // update mapping\n        mapping.set(leftY, leftP)\n      } else {\n        break\n      }\n    }\n  }\n  // find number of matching elements from right\n  for (; right + left + 1 < minCnt; right++) {\n    const rightY = yChildren[yChildCnt - right - 1]\n    const rightP = pChildren[pChildCnt - right - 1]\n    if (!mappedIdentity(mapping.get(rightY), rightP)) {\n      if (equalYTypePNode(rightY, rightP)) {\n        // update mapping\n        mapping.set(rightY, rightP)\n      } else {\n        break\n      }\n    }\n  }\n  y.transact(() => {\n    // try to compare and update\n    while (yChildCnt - left - right > 0 && pChildCnt - left - right > 0) {\n      const leftY = yChildren[left]\n      const leftP = pChildren[left]\n      const rightY = yChildren[yChildCnt - right - 1]\n      const rightP = pChildren[pChildCnt - right - 1]\n      if (leftY instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.XmlText && leftP instanceof Array) {\n        if (!equalYTextPText(leftY, leftP)) {\n          updateYText(leftY, leftP, mapping)\n        }\n        left += 1\n      } else {\n        let updateLeft = leftY instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.XmlElement &&\n          matchNodeName(leftY, leftP)\n        let updateRight = rightY instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.XmlElement &&\n          matchNodeName(rightY, rightP)\n        if (updateLeft && updateRight) {\n          // decide which which element to update\n          const equalityLeft = computeChildEqualityFactor(\n            /** @type {Y.XmlElement} */ (leftY),\n            /** @type {PModel.Node} */ (leftP),\n            mapping\n          )\n          const equalityRight = computeChildEqualityFactor(\n            /** @type {Y.XmlElement} */ (rightY),\n            /** @type {PModel.Node} */ (rightP),\n            mapping\n          )\n          if (\n            equalityLeft.foundMappedChild && !equalityRight.foundMappedChild\n          ) {\n            updateRight = false\n          } else if (\n            !equalityLeft.foundMappedChild && equalityRight.foundMappedChild\n          ) {\n            updateLeft = false\n          } else if (\n            equalityLeft.equalityFactor < equalityRight.equalityFactor\n          ) {\n            updateLeft = false\n          } else {\n            updateRight = false\n          }\n        }\n        if (updateLeft) {\n          updateYFragment(\n            y,\n            /** @type {Y.XmlFragment} */ (leftY),\n            /** @type {PModel.Node} */ (leftP),\n            mapping\n          )\n          left += 1\n        } else if (updateRight) {\n          updateYFragment(\n            y,\n            /** @type {Y.XmlFragment} */ (rightY),\n            /** @type {PModel.Node} */ (rightP),\n            mapping\n          )\n          right += 1\n        } else {\n          mapping.delete(yDomFragment.get(left))\n          yDomFragment.delete(left, 1)\n          yDomFragment.insert(left, [\n            createTypeFromTextOrElementNode(leftP, mapping)\n          ])\n          left += 1\n        }\n      }\n    }\n    const yDelLen = yChildCnt - left - right\n    if (\n      yChildCnt === 1 && pChildCnt === 0 && yChildren[0] instanceof yjs__WEBPACK_IMPORTED_MODULE_0__.XmlText\n    ) {\n      mapping.delete(yChildren[0])\n      // Edge case handling https://github.com/yjs/y-prosemirror/issues/108\n      // Only delete the content of the Y.Text to retain remote changes on the same Y.Text object\n      yChildren[0].delete(0, yChildren[0].length)\n    } else if (yDelLen > 0) {\n      yDomFragment.slice(left, left + yDelLen).forEach(type => mapping.delete(type))\n      yDomFragment.delete(left, yDelLen)\n    }\n    if (left + right < pChildCnt) {\n      const ins = []\n      for (let i = left; i < pChildCnt - right; i++) {\n        ins.push(createTypeFromTextOrElementNode(pChildren[i], mapping))\n      }\n      yDomFragment.insert(left, ins)\n    }\n  }, _keys_js__WEBPACK_IMPORTED_MODULE_4__.ySyncPluginKey)\n}\n\n/**\n * @function\n * @param {Y.XmlElement} yElement\n * @param {any} pNode Prosemirror Node\n */\nconst matchNodeName = (yElement, pNode) =>\n  !(pNode instanceof Array) && yElement.nodeName === pNode.type.name\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/y-prosemirror/src/plugins/sync-plugin.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/y-prosemirror/src/plugins/undo-plugin.js":
/*!***************************************************************!*\
  !*** ./node_modules/y-prosemirror/src/plugins/undo-plugin.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultDeleteFilter: () => (/* binding */ defaultDeleteFilter),\n/* harmony export */   defaultProtectedNodes: () => (/* binding */ defaultProtectedNodes),\n/* harmony export */   redo: () => (/* binding */ redo),\n/* harmony export */   undo: () => (/* binding */ undo),\n/* harmony export */   yUndoPlugin: () => (/* binding */ yUndoPlugin)\n/* harmony export */ });\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/./node_modules/prosemirror-state/dist/index.js\");\n/* harmony import */ var _sync_plugin_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./sync-plugin.js */ \"(ssr)/./node_modules/y-prosemirror/src/plugins/sync-plugin.js\");\n/* harmony import */ var yjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! yjs */ \"(ssr)/./node_modules/yjs/dist/yjs.mjs\");\n/* harmony import */ var _keys_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./keys.js */ \"(ssr)/./node_modules/y-prosemirror/src/plugins/keys.js\");\n // eslint-disable-line\n\n\n\n\n\nconst undo = state => {\n  const undoManager = _keys_js__WEBPACK_IMPORTED_MODULE_0__.yUndoPluginKey.getState(state).undoManager\n  if (undoManager != null) {\n    undoManager.undo()\n    return true\n  }\n}\n\nconst redo = state => {\n  const undoManager = _keys_js__WEBPACK_IMPORTED_MODULE_0__.yUndoPluginKey.getState(state).undoManager\n  if (undoManager != null) {\n    undoManager.redo()\n    return true\n  }\n}\n\nconst defaultProtectedNodes = new Set(['paragraph'])\n\nconst defaultDeleteFilter = (item, protectedNodes) => !(item instanceof yjs__WEBPACK_IMPORTED_MODULE_1__.Item) ||\n!(item.content instanceof yjs__WEBPACK_IMPORTED_MODULE_1__.ContentType) ||\n!(item.content.type instanceof yjs__WEBPACK_IMPORTED_MODULE_1__.Text ||\n  (item.content.type instanceof yjs__WEBPACK_IMPORTED_MODULE_1__.XmlElement && protectedNodes.has(item.content.type.nodeName))) ||\nitem.content.type._length === 0\n\nconst yUndoPlugin = ({ protectedNodes = defaultProtectedNodes, trackedOrigins = [], undoManager = null } = {}) => new prosemirror_state__WEBPACK_IMPORTED_MODULE_2__.Plugin({\n  key: _keys_js__WEBPACK_IMPORTED_MODULE_0__.yUndoPluginKey,\n  state: {\n    init: (initargs, state) => {\n      // TODO: check if plugin order matches and fix\n      const ystate = _keys_js__WEBPACK_IMPORTED_MODULE_0__.ySyncPluginKey.getState(state)\n      const _undoManager = undoManager || new yjs__WEBPACK_IMPORTED_MODULE_1__.UndoManager(ystate.type, {\n        trackedOrigins: new Set([_keys_js__WEBPACK_IMPORTED_MODULE_0__.ySyncPluginKey].concat(trackedOrigins)),\n        deleteFilter: (item) => defaultDeleteFilter(item, protectedNodes),\n        captureTransaction: tr => tr.meta.get('addToHistory') !== false\n      })\n      return {\n        undoManager: _undoManager,\n        prevSel: null,\n        hasUndoOps: _undoManager.undoStack.length > 0,\n        hasRedoOps: _undoManager.redoStack.length > 0\n      }\n    },\n    /**\n     * @returns {any}\n     */\n    apply: (tr, val, oldState, state) => {\n      const binding = _keys_js__WEBPACK_IMPORTED_MODULE_0__.ySyncPluginKey.getState(state).binding\n      const undoManager = val.undoManager\n      const hasUndoOps = undoManager.undoStack.length > 0\n      const hasRedoOps = undoManager.redoStack.length > 0\n      if (binding) {\n        return {\n          undoManager,\n          prevSel: (0,_sync_plugin_js__WEBPACK_IMPORTED_MODULE_3__.getRelativeSelection)(binding, oldState),\n          hasUndoOps,\n          hasRedoOps\n        }\n      } else {\n        if (hasUndoOps !== val.hasUndoOps || hasRedoOps !== val.hasRedoOps) {\n          return Object.assign({}, val, {\n            hasUndoOps: undoManager.undoStack.length > 0,\n            hasRedoOps: undoManager.redoStack.length > 0\n          })\n        } else { // nothing changed\n          return val\n        }\n      }\n    }\n  },\n  view: view => {\n    const ystate = _keys_js__WEBPACK_IMPORTED_MODULE_0__.ySyncPluginKey.getState(view.state)\n    const undoManager = _keys_js__WEBPACK_IMPORTED_MODULE_0__.yUndoPluginKey.getState(view.state).undoManager\n    undoManager.on('stack-item-added', ({ stackItem }) => {\n      const binding = ystate.binding\n      if (binding) {\n        stackItem.meta.set(binding, _keys_js__WEBPACK_IMPORTED_MODULE_0__.yUndoPluginKey.getState(view.state).prevSel)\n      }\n    })\n    undoManager.on('stack-item-popped', ({ stackItem }) => {\n      const binding = ystate.binding\n      if (binding) {\n        binding.beforeTransactionSelection = stackItem.meta.get(binding) || binding.beforeTransactionSelection\n      }\n    })\n    return {\n      destroy: () => {\n        undoManager.destroy()\n      }\n    }\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/y-prosemirror/src/plugins/undo-plugin.js\n");

/***/ })

};
;