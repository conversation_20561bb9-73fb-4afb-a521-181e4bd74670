/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_custom-ui_tagInput_tsx"],{

/***/ "(app-pages-browser)/./node_modules/tributejs/dist/tribute.min.js":
/*!****************************************************!*\
  !*** ./node_modules/tributejs/dist/tribute.min.js ***!
  \****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,t){ true?module.exports=t():0}(this,(function(){\"use strict\";function e(e,t){if(!(e instanceof t))throw new TypeError(\"Cannot call a class as a function\")}function t(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,\"value\"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function n(e,n,i){return n&&t(e.prototype,n),i&&t(e,i),e}function i(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if(!(Symbol.iterator in Object(e)||\"[object Arguments]\"===Object.prototype.toString.call(e)))return;var n=[],i=!0,r=!1,o=void 0;try{for(var u,l=e[Symbol.iterator]();!(i=(u=l.next()).done)&&(n.push(u.value),!t||n.length!==t);i=!0);}catch(e){r=!0,o=e}finally{try{i||null==l.return||l.return()}finally{if(r)throw o}}return n}(e,t)||function(){throw new TypeError(\"Invalid attempt to destructure non-iterable instance\")}()}if(Array.prototype.find||(Array.prototype.find=function(e){if(null===this)throw new TypeError(\"Array.prototype.find called on null or undefined\");if(\"function\"!=typeof e)throw new TypeError(\"predicate must be a function\");for(var t,n=Object(this),i=n.length>>>0,r=arguments[1],o=0;o<i;o++)if(t=n[o],e.call(r,t,o,n))return t}),window&&\"function\"!=typeof window.CustomEvent){var r=function(e,t){t=t||{bubbles:!1,cancelable:!1,detail:void 0};var n=document.createEvent(\"CustomEvent\");return n.initCustomEvent(e,t.bubbles,t.cancelable,t.detail),n};void 0!==window.Event&&(r.prototype=window.Event.prototype),window.CustomEvent=r}var o=function(){function t(n){e(this,t),this.tribute=n,this.tribute.events=this}return n(t,[{key:\"bind\",value:function(e){e.boundKeydown=this.keydown.bind(e,this),e.boundKeyup=this.keyup.bind(e,this),e.boundInput=this.input.bind(e,this),e.addEventListener(\"keydown\",e.boundKeydown,!1),e.addEventListener(\"keyup\",e.boundKeyup,!1),e.addEventListener(\"input\",e.boundInput,!1)}},{key:\"unbind\",value:function(e){e.removeEventListener(\"keydown\",e.boundKeydown,!1),e.removeEventListener(\"keyup\",e.boundKeyup,!1),e.removeEventListener(\"input\",e.boundInput,!1),delete e.boundKeydown,delete e.boundKeyup,delete e.boundInput}},{key:\"keydown\",value:function(e,n){e.shouldDeactivate(n)&&(e.tribute.isActive=!1,e.tribute.hideMenu());var i=this;e.commandEvent=!1,t.keys().forEach((function(t){t.key===n.keyCode&&(e.commandEvent=!0,e.callbacks()[t.value.toLowerCase()](n,i))}))}},{key:\"input\",value:function(e,t){e.inputEvent=!0,e.keyup.call(this,e,t)}},{key:\"click\",value:function(e,t){var n=e.tribute;if(n.menu&&n.menu.contains(t.target)){var i=t.target;for(t.preventDefault(),t.stopPropagation();\"li\"!==i.nodeName.toLowerCase();)if(!(i=i.parentNode)||i===n.menu)throw new Error(\"cannot find the <li> container for the click\");n.selectItemAtIndex(i.getAttribute(\"data-index\"),t),n.hideMenu()}else n.current.element&&!n.current.externalTrigger&&(n.current.externalTrigger=!1,setTimeout((function(){return n.hideMenu()})))}},{key:\"keyup\",value:function(e,t){if(e.inputEvent&&(e.inputEvent=!1),e.updateSelection(this),27!==t.keyCode){if(!e.tribute.allowSpaces&&e.tribute.hasTrailingSpace)return e.tribute.hasTrailingSpace=!1,e.commandEvent=!0,void e.callbacks().space(t,this);if(!e.tribute.isActive)if(e.tribute.autocompleteMode)e.callbacks().triggerChar(t,this,\"\");else{var n=e.getKeyCode(e,this,t);if(isNaN(n)||!n)return;var i=e.tribute.triggers().find((function(e){return e.charCodeAt(0)===n}));void 0!==i&&e.callbacks().triggerChar(t,this,i)}e.tribute.current.mentionText.length<e.tribute.current.collection.menuShowMinLength||((e.tribute.current.trigger||e.tribute.autocompleteMode)&&!1===e.commandEvent||e.tribute.isActive&&8===t.keyCode)&&e.tribute.showMenuFor(this,!0)}}},{key:\"shouldDeactivate\",value:function(e){if(!this.tribute.isActive)return!1;if(0===this.tribute.current.mentionText.length){var n=!1;return t.keys().forEach((function(t){e.keyCode===t.key&&(n=!0)})),!n}return!1}},{key:\"getKeyCode\",value:function(e,t,n){var i=e.tribute,r=i.range.getTriggerInfo(!1,i.hasTrailingSpace,!0,i.allowSpaces,i.autocompleteMode);return!!r&&r.mentionTriggerChar.charCodeAt(0)}},{key:\"updateSelection\",value:function(e){this.tribute.current.element=e;var t=this.tribute.range.getTriggerInfo(!1,this.tribute.hasTrailingSpace,!0,this.tribute.allowSpaces,this.tribute.autocompleteMode);t&&(this.tribute.current.selectedPath=t.mentionSelectedPath,this.tribute.current.mentionText=t.mentionText,this.tribute.current.selectedOffset=t.mentionSelectedOffset)}},{key:\"callbacks\",value:function(){var e=this;return{triggerChar:function(t,n,i){var r=e.tribute;r.current.trigger=i;var o=r.collection.find((function(e){return e.trigger===i}));r.current.collection=o,r.current.mentionText.length>=r.current.collection.menuShowMinLength&&r.inputEvent&&r.showMenuFor(n,!0)},enter:function(t,n){e.tribute.isActive&&e.tribute.current.filteredItems&&(t.preventDefault(),t.stopPropagation(),setTimeout((function(){e.tribute.selectItemAtIndex(e.tribute.menuSelected,t),e.tribute.hideMenu()}),0))},escape:function(t,n){e.tribute.isActive&&(t.preventDefault(),t.stopPropagation(),e.tribute.isActive=!1,e.tribute.hideMenu())},tab:function(t,n){e.callbacks().enter(t,n)},space:function(t,n){e.tribute.isActive&&(e.tribute.spaceSelectsMatch?e.callbacks().enter(t,n):e.tribute.allowSpaces||(t.stopPropagation(),setTimeout((function(){e.tribute.hideMenu(),e.tribute.isActive=!1}),0)))},up:function(t,n){if(e.tribute.isActive&&e.tribute.current.filteredItems){t.preventDefault(),t.stopPropagation();var i=e.tribute.current.filteredItems.length,r=e.tribute.menuSelected;i>r&&r>0?(e.tribute.menuSelected--,e.setActiveLi()):0===r&&(e.tribute.menuSelected=i-1,e.setActiveLi(),e.tribute.menu.scrollTop=e.tribute.menu.scrollHeight)}},down:function(t,n){if(e.tribute.isActive&&e.tribute.current.filteredItems){t.preventDefault(),t.stopPropagation();var i=e.tribute.current.filteredItems.length-1,r=e.tribute.menuSelected;i>r?(e.tribute.menuSelected++,e.setActiveLi()):i===r&&(e.tribute.menuSelected=0,e.setActiveLi(),e.tribute.menu.scrollTop=0)}},delete:function(t,n){e.tribute.isActive&&e.tribute.current.mentionText.length<1?e.tribute.hideMenu():e.tribute.isActive&&e.tribute.showMenuFor(n)}}}},{key:\"setActiveLi\",value:function(e){var t=this.tribute.menu.querySelectorAll(\"li\"),n=t.length>>>0;e&&(this.tribute.menuSelected=parseInt(e));for(var i=0;i<n;i++){var r=t[i];if(i===this.tribute.menuSelected){r.classList.add(this.tribute.current.collection.selectClass);var o=r.getBoundingClientRect(),u=this.tribute.menu.getBoundingClientRect();if(o.bottom>u.bottom){var l=o.bottom-u.bottom;this.tribute.menu.scrollTop+=l}else if(o.top<u.top){var s=u.top-o.top;this.tribute.menu.scrollTop-=s}}else r.classList.remove(this.tribute.current.collection.selectClass)}}},{key:\"getFullHeight\",value:function(e,t){var n=e.getBoundingClientRect().height;if(t){var i=e.currentStyle||window.getComputedStyle(e);return n+parseFloat(i.marginTop)+parseFloat(i.marginBottom)}return n}}],[{key:\"keys\",value:function(){return[{key:9,value:\"TAB\"},{key:8,value:\"DELETE\"},{key:13,value:\"ENTER\"},{key:27,value:\"ESCAPE\"},{key:32,value:\"SPACE\"},{key:38,value:\"UP\"},{key:40,value:\"DOWN\"}]}}]),t}(),u=function(){function t(n){e(this,t),this.tribute=n,this.tribute.menuEvents=this,this.menu=this.tribute.menu}return n(t,[{key:\"bind\",value:function(e){var t=this;this.menuClickEvent=this.tribute.events.click.bind(null,this),this.menuContainerScrollEvent=this.debounce((function(){t.tribute.isActive&&t.tribute.showMenuFor(t.tribute.current.element,!1)}),300,!1),this.windowResizeEvent=this.debounce((function(){t.tribute.isActive&&t.tribute.range.positionMenuAtCaret(!0)}),300,!1),this.tribute.range.getDocument().addEventListener(\"MSPointerDown\",this.menuClickEvent,!1),this.tribute.range.getDocument().addEventListener(\"mousedown\",this.menuClickEvent,!1),window.addEventListener(\"resize\",this.windowResizeEvent),this.menuContainer?this.menuContainer.addEventListener(\"scroll\",this.menuContainerScrollEvent,!1):window.addEventListener(\"scroll\",this.menuContainerScrollEvent)}},{key:\"unbind\",value:function(e){this.tribute.range.getDocument().removeEventListener(\"mousedown\",this.menuClickEvent,!1),this.tribute.range.getDocument().removeEventListener(\"MSPointerDown\",this.menuClickEvent,!1),window.removeEventListener(\"resize\",this.windowResizeEvent),this.menuContainer?this.menuContainer.removeEventListener(\"scroll\",this.menuContainerScrollEvent,!1):window.removeEventListener(\"scroll\",this.menuContainerScrollEvent)}},{key:\"debounce\",value:function(e,t,n){var i,r=arguments,o=this;return function(){var u=o,l=r,s=n&&!i;clearTimeout(i),i=setTimeout((function(){i=null,n||e.apply(u,l)}),t),s&&e.apply(u,l)}}}]),t}(),l=function(){function t(n){e(this,t),this.tribute=n,this.tribute.range=this}return n(t,[{key:\"getDocument\",value:function(){var e;return this.tribute.current.collection&&(e=this.tribute.current.collection.iframe),e?e.contentWindow.document:document}},{key:\"positionMenuAtCaret\",value:function(e){var t,n=this,i=this.tribute.current,r=this.getTriggerInfo(!1,this.tribute.hasTrailingSpace,!0,this.tribute.allowSpaces,this.tribute.autocompleteMode);if(void 0!==r){if(!this.tribute.positionMenu)return void(this.tribute.menu.style.cssText=\"display: block;\");t=this.isContentEditable(i.element)?this.getContentEditableCaretPosition(r.mentionPosition):this.getTextAreaOrInputUnderlinePosition(this.tribute.current.element,r.mentionPosition),this.tribute.menu.style.cssText=\"top: \".concat(t.top,\"px;\\n                                     left: \").concat(t.left,\"px;\\n                                     right: \").concat(t.right,\"px;\\n                                     bottom: \").concat(t.bottom,\"px;\\n                                     position: absolute;\\n                                     display: block;\"),\"auto\"===t.left&&(this.tribute.menu.style.left=\"auto\"),\"auto\"===t.top&&(this.tribute.menu.style.top=\"auto\"),e&&this.scrollIntoView(),window.setTimeout((function(){var i={width:n.tribute.menu.offsetWidth,height:n.tribute.menu.offsetHeight},r=n.isMenuOffScreen(t,i),o=window.innerWidth>i.width&&(r.left||r.right),u=window.innerHeight>i.height&&(r.top||r.bottom);(o||u)&&(n.tribute.menu.style.cssText=\"display: none\",n.positionMenuAtCaret(e))}),0)}else this.tribute.menu.style.cssText=\"display: none\"}},{key:\"selectElement\",value:function(e,t,n){var i,r=e;if(t)for(var o=0;o<t.length;o++){if(void 0===(r=r.childNodes[t[o]]))return;for(;r.length<n;)n-=r.length,r=r.nextSibling;0!==r.childNodes.length||r.length||(r=r.previousSibling)}var u=this.getWindowSelection();(i=this.getDocument().createRange()).setStart(r,n),i.setEnd(r,n),i.collapse(!0);try{u.removeAllRanges()}catch(e){}u.addRange(i),e.focus()}},{key:\"replaceTriggerText\",value:function(e,t,n,i,r){var o=this.getTriggerInfo(!0,n,t,this.tribute.allowSpaces,this.tribute.autocompleteMode);if(void 0!==o){var u=this.tribute.current,l=new CustomEvent(\"tribute-replaced\",{detail:{item:r,instance:u,context:o,event:i}});if(this.isContentEditable(u.element)){e+=\"string\"==typeof this.tribute.replaceTextSuffix?this.tribute.replaceTextSuffix:\" \";var s=o.mentionPosition+o.mentionText.length;this.tribute.autocompleteMode||(s+=o.mentionTriggerChar.length),this.pasteHtml(e,o.mentionPosition,s)}else{var a=this.tribute.current.element,c=\"string\"==typeof this.tribute.replaceTextSuffix?this.tribute.replaceTextSuffix:\" \";e+=c;var h=o.mentionPosition,d=o.mentionPosition+o.mentionText.length+c.length;this.tribute.autocompleteMode||(d+=o.mentionTriggerChar.length-1),a.value=a.value.substring(0,h)+e+a.value.substring(d,a.value.length),a.selectionStart=h+e.length,a.selectionEnd=h+e.length}u.element.dispatchEvent(new CustomEvent(\"input\",{bubbles:!0})),u.element.dispatchEvent(l)}}},{key:\"pasteHtml\",value:function(e,t,n){var i,r;r=this.getWindowSelection(),(i=this.getDocument().createRange()).setStart(r.anchorNode,t),i.setEnd(r.anchorNode,n),i.deleteContents();var o=this.getDocument().createElement(\"div\");o.innerHTML=e;for(var u,l,s=this.getDocument().createDocumentFragment();u=o.firstChild;)l=s.appendChild(u);i.insertNode(s),l&&((i=i.cloneRange()).setStartAfter(l),i.collapse(!0),r.removeAllRanges(),r.addRange(i))}},{key:\"getWindowSelection\",value:function(){return this.tribute.collection.iframe?this.tribute.collection.iframe.contentWindow.getSelection():window.getSelection()}},{key:\"getNodePositionInParent\",value:function(e){if(null===e.parentNode)return 0;for(var t=0;t<e.parentNode.childNodes.length;t++){if(e.parentNode.childNodes[t]===e)return t}}},{key:\"getContentEditableSelectedPath\",value:function(e){var t=this.getWindowSelection(),n=t.anchorNode,i=[];if(null!=n){for(var r,o=n.contentEditable;null!==n&&\"true\"!==o;)r=this.getNodePositionInParent(n),i.push(r),null!==(n=n.parentNode)&&(o=n.contentEditable);return i.reverse(),{selected:n,path:i,offset:t.getRangeAt(0).startOffset}}}},{key:\"getTextPrecedingCurrentSelection\",value:function(){var e=this.tribute.current,t=\"\";if(this.isContentEditable(e.element)){var n=this.getWindowSelection().anchorNode;if(null!=n){var i=n.textContent,r=this.getWindowSelection().getRangeAt(0).startOffset;i&&r>=0&&(t=i.substring(0,r))}}else{var o=this.tribute.current.element;if(o){var u=o.selectionStart;o.value&&u>=0&&(t=o.value.substring(0,u))}}return t}},{key:\"getLastWordInText\",value:function(e){var t=(e=e.replace(/\\u00A0/g,\" \")).split(/\\s+/);return t[t.length-1].trim()}},{key:\"getTriggerInfo\",value:function(e,t,n,i,r){var o,u,l,s=this,a=this.tribute.current;if(this.isContentEditable(a.element)){var c=this.getContentEditableSelectedPath(a);c&&(o=c.selected,u=c.path,l=c.offset)}else o=this.tribute.current.element;var h=this.getTextPrecedingCurrentSelection(),d=this.getLastWordInText(h);if(r)return{mentionPosition:h.length-d.length,mentionText:d,mentionSelectedElement:o,mentionSelectedPath:u,mentionSelectedOffset:l};if(null!=h){var f,m=-1;if(this.tribute.collection.forEach((function(e){var t=e.trigger,i=e.requireLeadingSpace?s.lastIndexWithLeadingSpace(h,t):h.lastIndexOf(t);i>m&&(m=i,f=t,n=e.requireLeadingSpace)})),m>=0&&(0===m||!n||/[\\xA0\\s]/g.test(h.substring(m-1,m)))){var p=h.substring(m+f.length,h.length);f=h.substring(m,m+f.length);var v=p.substring(0,1),g=p.length>0&&(\" \"===v||\" \"===v);t&&(p=p.trim());var b=i?/[^\\S ]/g:/[\\xA0\\s]/g;if(this.tribute.hasTrailingSpace=b.test(p),!g&&(e||!b.test(p)))return{mentionPosition:m,mentionText:p,mentionSelectedElement:o,mentionSelectedPath:u,mentionSelectedOffset:l,mentionTriggerChar:f}}}}},{key:\"lastIndexWithLeadingSpace\",value:function(e,t){for(var n=e.split(\"\").reverse().join(\"\"),i=-1,r=0,o=e.length;r<o;r++){for(var u=r===e.length-1,l=/\\s/.test(n[r+1]),s=!0,a=t.length-1;a>=0;a--)if(t[a]!==n[r-a]){s=!1;break}if(s&&(u||l)){i=e.length-1-r;break}}return i}},{key:\"isContentEditable\",value:function(e){return\"INPUT\"!==e.nodeName&&\"TEXTAREA\"!==e.nodeName}},{key:\"isMenuOffScreen\",value:function(e,t){var n=window.innerWidth,i=window.innerHeight,r=document.documentElement,o=(window.pageXOffset||r.scrollLeft)-(r.clientLeft||0),u=(window.pageYOffset||r.scrollTop)-(r.clientTop||0),l=\"number\"==typeof e.top?e.top:u+i-e.bottom-t.height,s=\"number\"==typeof e.right?e.right:e.left+t.width,a=\"number\"==typeof e.bottom?e.bottom:e.top+t.height,c=\"number\"==typeof e.left?e.left:o+n-e.right-t.width;return{top:l<Math.floor(u),right:s>Math.ceil(o+n),bottom:a>Math.ceil(u+i),left:c<Math.floor(o)}}},{key:\"getMenuDimensions\",value:function(){var e={width:null,height:null};return this.tribute.menu.style.cssText=\"top: 0px;\\n                                 left: 0px;\\n                                 position: fixed;\\n                                 display: block;\\n                                 visibility; hidden;\",e.width=this.tribute.menu.offsetWidth,e.height=this.tribute.menu.offsetHeight,this.tribute.menu.style.cssText=\"display: none;\",e}},{key:\"getTextAreaOrInputUnderlinePosition\",value:function(e,t,n){var i=null!==window.mozInnerScreenX,r=this.getDocument().createElement(\"div\");r.id=\"input-textarea-caret-position-mirror-div\",this.getDocument().body.appendChild(r);var o=r.style,u=window.getComputedStyle?getComputedStyle(e):e.currentStyle;o.whiteSpace=\"pre-wrap\",\"INPUT\"!==e.nodeName&&(o.wordWrap=\"break-word\"),o.position=\"absolute\",o.visibility=\"hidden\",[\"direction\",\"boxSizing\",\"width\",\"height\",\"overflowX\",\"overflowY\",\"borderTopWidth\",\"borderRightWidth\",\"borderBottomWidth\",\"borderLeftWidth\",\"paddingTop\",\"paddingRight\",\"paddingBottom\",\"paddingLeft\",\"fontStyle\",\"fontVariant\",\"fontWeight\",\"fontStretch\",\"fontSize\",\"fontSizeAdjust\",\"lineHeight\",\"fontFamily\",\"textAlign\",\"textTransform\",\"textIndent\",\"textDecoration\",\"letterSpacing\",\"wordSpacing\"].forEach((function(e){o[e]=u[e]})),i?(o.width=\"\".concat(parseInt(u.width)-2,\"px\"),e.scrollHeight>parseInt(u.height)&&(o.overflowY=\"scroll\")):o.overflow=\"hidden\",r.textContent=e.value.substring(0,t),\"INPUT\"===e.nodeName&&(r.textContent=r.textContent.replace(/\\s/g,\" \"));var l=this.getDocument().createElement(\"span\");l.textContent=e.value.substring(t)||\".\",r.appendChild(l);var s=e.getBoundingClientRect(),a=document.documentElement,c=(window.pageXOffset||a.scrollLeft)-(a.clientLeft||0),h=(window.pageYOffset||a.scrollTop)-(a.clientTop||0),d=0,f=0;this.menuContainerIsBody&&(d=s.top,f=s.left);var m={top:d+h+l.offsetTop+parseInt(u.borderTopWidth)+parseInt(u.fontSize)-e.scrollTop,left:f+c+l.offsetLeft+parseInt(u.borderLeftWidth)},p=window.innerWidth,v=window.innerHeight,g=this.getMenuDimensions(),b=this.isMenuOffScreen(m,g);b.right&&(m.right=p-m.left,m.left=\"auto\");var y=this.tribute.menuContainer?this.tribute.menuContainer.offsetHeight:this.getDocument().body.offsetHeight;if(b.bottom){var w=y-(v-(this.tribute.menuContainer?this.tribute.menuContainer.getBoundingClientRect():this.getDocument().body.getBoundingClientRect()).top);m.bottom=w+(v-s.top-l.offsetTop),m.top=\"auto\"}return(b=this.isMenuOffScreen(m,g)).left&&(m.left=p>g.width?c+p-g.width:c,delete m.right),b.top&&(m.top=v>g.height?h+v-g.height:h,delete m.bottom),this.getDocument().body.removeChild(r),m}},{key:\"getContentEditableCaretPosition\",value:function(e){var t,n=this.getWindowSelection();(t=this.getDocument().createRange()).setStart(n.anchorNode,e),t.setEnd(n.anchorNode,e),t.collapse(!1);var i=t.getBoundingClientRect(),r=document.documentElement,o=(window.pageXOffset||r.scrollLeft)-(r.clientLeft||0),u=(window.pageYOffset||r.scrollTop)-(r.clientTop||0),l={left:i.left+o,top:i.top+i.height+u},s=window.innerWidth,a=window.innerHeight,c=this.getMenuDimensions(),h=this.isMenuOffScreen(l,c);h.right&&(l.left=\"auto\",l.right=s-i.left-o);var d=this.tribute.menuContainer?this.tribute.menuContainer.offsetHeight:this.getDocument().body.offsetHeight;if(h.bottom){var f=d-(a-(this.tribute.menuContainer?this.tribute.menuContainer.getBoundingClientRect():this.getDocument().body.getBoundingClientRect()).top);l.top=\"auto\",l.bottom=f+(a-i.top)}return(h=this.isMenuOffScreen(l,c)).left&&(l.left=s>c.width?o+s-c.width:o,delete l.right),h.top&&(l.top=a>c.height?u+a-c.height:u,delete l.bottom),this.menuContainerIsBody||(l.left=l.left?l.left-this.tribute.menuContainer.offsetLeft:l.left,l.top=l.top?l.top-this.tribute.menuContainer.offsetTop:l.top),l}},{key:\"scrollIntoView\",value:function(e){var t,n=this.menu;if(void 0!==n){for(;void 0===t||0===t.height;)if(0===(t=n.getBoundingClientRect()).height&&(void 0===(n=n.childNodes[0])||!n.getBoundingClientRect))return;var i=t.top,r=i+t.height;if(i<0)window.scrollTo(0,window.pageYOffset+t.top-20);else if(r>window.innerHeight){var o=window.pageYOffset+t.top-20;o-window.pageYOffset>100&&(o=window.pageYOffset+100);var u=window.pageYOffset-(window.innerHeight-r);u>o&&(u=o),window.scrollTo(0,u)}}}},{key:\"menuContainerIsBody\",get:function(){return this.tribute.menuContainer===document.body||!this.tribute.menuContainer}}]),t}(),s=function(){function t(n){e(this,t),this.tribute=n,this.tribute.search=this}return n(t,[{key:\"simpleFilter\",value:function(e,t){var n=this;return t.filter((function(t){return n.test(e,t)}))}},{key:\"test\",value:function(e,t){return null!==this.match(e,t)}},{key:\"match\",value:function(e,t,n){n=n||{};t.length;var i=n.pre||\"\",r=n.post||\"\",o=n.caseSensitive&&t||t.toLowerCase();if(n.skip)return{rendered:t,score:0};e=n.caseSensitive&&e||e.toLowerCase();var u=this.traverse(o,e,0,0,[]);return u?{rendered:this.render(t,u.cache,i,r),score:u.score}:null}},{key:\"traverse\",value:function(e,t,n,i,r){if(t.length===i)return{score:this.calculateScore(r),cache:r.slice()};if(!(e.length===n||t.length-i>e.length-n)){for(var o,u,l=t[i],s=e.indexOf(l,n);s>-1;){if(r.push(s),u=this.traverse(e,t,s+1,i+1,r),r.pop(),!u)return o;(!o||o.score<u.score)&&(o=u),s=e.indexOf(l,s+1)}return o}}},{key:\"calculateScore\",value:function(e){var t=0,n=1;return e.forEach((function(i,r){r>0&&(e[r-1]+1===i?n+=n+1:n=1),t+=n})),t}},{key:\"render\",value:function(e,t,n,i){var r=e.substring(0,t[0]);return t.forEach((function(o,u){r+=n+e[o]+i+e.substring(o+1,t[u+1]?t[u+1]:e.length)})),r}},{key:\"filter\",value:function(e,t,n){var i=this;return n=n||{},t.reduce((function(t,r,o,u){var l=r;n.extract&&((l=n.extract(r))||(l=\"\"));var s=i.match(e,l,n);return null!=s&&(t[t.length]={string:s.rendered,score:s.score,index:o,original:r}),t}),[]).sort((function(e,t){var n=t.score-e.score;return n||e.index-t.index}))}}]),t}();return function(){function t(n){var i,r=this,a=n.values,c=void 0===a?null:a,h=n.iframe,d=void 0===h?null:h,f=n.selectClass,m=void 0===f?\"highlight\":f,p=n.containerClass,v=void 0===p?\"tribute-container\":p,g=n.itemClass,b=void 0===g?\"\":g,y=n.trigger,w=void 0===y?\"@\":y,T=n.autocompleteMode,C=void 0!==T&&T,S=n.selectTemplate,E=void 0===S?null:S,k=n.menuItemTemplate,x=void 0===k?null:k,M=n.lookup,A=void 0===M?\"key\":M,L=n.fillAttr,I=void 0===L?\"value\":L,N=n.collection,O=void 0===N?null:N,D=n.menuContainer,P=void 0===D?null:D,R=n.noMatchTemplate,W=void 0===R?null:R,H=n.requireLeadingSpace,B=void 0===H||H,F=n.allowSpaces,_=void 0!==F&&F,j=n.replaceTextSuffix,Y=void 0===j?null:j,z=n.positionMenu,K=void 0===z||z,q=n.spaceSelectsMatch,U=void 0!==q&&q,X=n.searchOpts,Q=void 0===X?{}:X,V=n.menuItemLimit,G=void 0===V?null:V,J=n.menuShowMinLength,Z=void 0===J?0:J;if(e(this,t),this.autocompleteMode=C,this.menuSelected=0,this.current={},this.inputEvent=!1,this.isActive=!1,this.menuContainer=P,this.allowSpaces=_,this.replaceTextSuffix=Y,this.positionMenu=K,this.hasTrailingSpace=!1,this.spaceSelectsMatch=U,this.autocompleteMode&&(w=\"\",_=!1),c)this.collection=[{trigger:w,iframe:d,selectClass:m,containerClass:v,itemClass:b,selectTemplate:(E||t.defaultSelectTemplate).bind(this),menuItemTemplate:(x||t.defaultMenuItemTemplate).bind(this),noMatchTemplate:(i=W,\"string\"==typeof i?\"\"===i.trim()?null:i:\"function\"==typeof i?i.bind(r):W||function(){return\"<li>No Match Found!</li>\"}.bind(r)),lookup:A,fillAttr:I,values:c,requireLeadingSpace:B,searchOpts:Q,menuItemLimit:G,menuShowMinLength:Z}];else{if(!O)throw new Error(\"[Tribute] No collection specified.\");this.autocompleteMode&&console.warn(\"Tribute in autocomplete mode does not work for collections\"),this.collection=O.map((function(e){return{trigger:e.trigger||w,iframe:e.iframe||d,selectClass:e.selectClass||m,containerClass:e.containerClass||v,itemClass:e.itemClass||b,selectTemplate:(e.selectTemplate||t.defaultSelectTemplate).bind(r),menuItemTemplate:(e.menuItemTemplate||t.defaultMenuItemTemplate).bind(r),noMatchTemplate:function(e){return\"string\"==typeof e?\"\"===e.trim()?null:e:\"function\"==typeof e?e.bind(r):W||function(){return\"<li>No Match Found!</li>\"}.bind(r)}(W),lookup:e.lookup||A,fillAttr:e.fillAttr||I,values:e.values,requireLeadingSpace:e.requireLeadingSpace,searchOpts:e.searchOpts||Q,menuItemLimit:e.menuItemLimit||G,menuShowMinLength:e.menuShowMinLength||Z}}))}new l(this),new o(this),new u(this),new s(this)}return n(t,[{key:\"triggers\",value:function(){return this.collection.map((function(e){return e.trigger}))}},{key:\"attach\",value:function(e){if(!e)throw new Error(\"[Tribute] Must pass in a DOM node or NodeList.\");if(\"undefined\"!=typeof jQuery&&e instanceof jQuery&&(e=e.get()),e.constructor===NodeList||e.constructor===HTMLCollection||e.constructor===Array)for(var t=e.length,n=0;n<t;++n)this._attach(e[n]);else this._attach(e)}},{key:\"_attach\",value:function(e){e.hasAttribute(\"data-tribute\")&&console.warn(\"Tribute was already bound to \"+e.nodeName),this.ensureEditable(e),this.events.bind(e),e.setAttribute(\"data-tribute\",!0)}},{key:\"ensureEditable\",value:function(e){if(-1===t.inputTypes().indexOf(e.nodeName)){if(!e.contentEditable)throw new Error(\"[Tribute] Cannot bind to \"+e.nodeName);e.contentEditable=!0}}},{key:\"createMenu\",value:function(e){var t=this.range.getDocument().createElement(\"div\"),n=this.range.getDocument().createElement(\"ul\");return t.className=e,t.appendChild(n),this.menuContainer?this.menuContainer.appendChild(t):this.range.getDocument().body.appendChild(t)}},{key:\"showMenuFor\",value:function(e,t){var n=this;if(!this.isActive||this.current.element!==e||this.current.mentionText!==this.currentMentionTextSnapshot){this.currentMentionTextSnapshot=this.current.mentionText,this.menu||(this.menu=this.createMenu(this.current.collection.containerClass),e.tributeMenu=this.menu,this.menuEvents.bind(this.menu)),this.isActive=!0,this.menuSelected=0,this.current.mentionText||(this.current.mentionText=\"\");var r=function(e){if(n.isActive){var r=n.search.filter(n.current.mentionText,e,{pre:n.current.collection.searchOpts.pre||\"<span>\",post:n.current.collection.searchOpts.post||\"</span>\",skip:n.current.collection.searchOpts.skip,extract:function(e){if(\"string\"==typeof n.current.collection.lookup)return e[n.current.collection.lookup];if(\"function\"==typeof n.current.collection.lookup)return n.current.collection.lookup(e,n.current.mentionText);throw new Error(\"Invalid lookup attribute, lookup must be string or function.\")}});n.current.collection.menuItemLimit&&(r=r.slice(0,n.current.collection.menuItemLimit)),n.current.filteredItems=r;var o=n.menu.querySelector(\"ul\");if(n.range.positionMenuAtCaret(t),!r.length){var u=new CustomEvent(\"tribute-no-match\",{detail:n.menu});return n.current.element.dispatchEvent(u),void(\"function\"==typeof n.current.collection.noMatchTemplate&&!n.current.collection.noMatchTemplate()||!n.current.collection.noMatchTemplate?n.hideMenu():\"function\"==typeof n.current.collection.noMatchTemplate?o.innerHTML=n.current.collection.noMatchTemplate():o.innerHTML=n.current.collection.noMatchTemplate)}o.innerHTML=\"\";var l=n.range.getDocument().createDocumentFragment();r.forEach((function(e,t){var r=n.range.getDocument().createElement(\"li\");r.setAttribute(\"data-index\",t),r.className=n.current.collection.itemClass,r.addEventListener(\"mousemove\",(function(e){var t=i(n._findLiTarget(e.target),2),r=(t[0],t[1]);0!==e.movementY&&n.events.setActiveLi(r)})),n.menuSelected===t&&r.classList.add(n.current.collection.selectClass),r.innerHTML=n.current.collection.menuItemTemplate(e),l.appendChild(r)})),o.appendChild(l)}};\"function\"==typeof this.current.collection.values?this.current.collection.values(this.current.mentionText,r):r(this.current.collection.values)}}},{key:\"_findLiTarget\",value:function(e){if(!e)return[];var t=e.getAttribute(\"data-index\");return t?[e,t]:this._findLiTarget(e.parentNode)}},{key:\"showMenuForCollection\",value:function(e,t){e!==document.activeElement&&this.placeCaretAtEnd(e),this.current.collection=this.collection[t||0],this.current.externalTrigger=!0,this.current.element=e,e.isContentEditable?this.insertTextAtCursor(this.current.collection.trigger):this.insertAtCaret(e,this.current.collection.trigger),this.showMenuFor(e)}},{key:\"placeCaretAtEnd\",value:function(e){if(e.focus(),void 0!==window.getSelection&&void 0!==document.createRange){var t=document.createRange();t.selectNodeContents(e),t.collapse(!1);var n=window.getSelection();n.removeAllRanges(),n.addRange(t)}else if(void 0!==document.body.createTextRange){var i=document.body.createTextRange();i.moveToElementText(e),i.collapse(!1),i.select()}}},{key:\"insertTextAtCursor\",value:function(e){var t,n;(n=(t=window.getSelection()).getRangeAt(0)).deleteContents();var i=document.createTextNode(e);n.insertNode(i),n.selectNodeContents(i),n.collapse(!1),t.removeAllRanges(),t.addRange(n)}},{key:\"insertAtCaret\",value:function(e,t){var n=e.scrollTop,i=e.selectionStart,r=e.value.substring(0,i),o=e.value.substring(e.selectionEnd,e.value.length);e.value=r+t+o,i+=t.length,e.selectionStart=i,e.selectionEnd=i,e.focus(),e.scrollTop=n}},{key:\"hideMenu\",value:function(){this.menu&&(this.menu.style.cssText=\"display: none;\",this.isActive=!1,this.menuSelected=0,this.current={})}},{key:\"selectItemAtIndex\",value:function(e,t){if(\"number\"==typeof(e=parseInt(e))&&!isNaN(e)){var n=this.current.filteredItems[e],i=this.current.collection.selectTemplate(n);null!==i&&this.replaceText(i,t,n)}}},{key:\"replaceText\",value:function(e,t,n){this.range.replaceTriggerText(e,!0,!0,t,n)}},{key:\"_append\",value:function(e,t,n){if(\"function\"==typeof e.values)throw new Error(\"Unable to append to values, as it is a function.\");e.values=n?t:e.values.concat(t)}},{key:\"append\",value:function(e,t,n){var i=parseInt(e);if(\"number\"!=typeof i)throw new Error(\"please provide an index for the collection to update.\");var r=this.collection[i];this._append(r,t,n)}},{key:\"appendCurrent\",value:function(e,t){if(!this.isActive)throw new Error(\"No active state. Please use append instead and pass an index.\");this._append(this.current.collection,e,t)}},{key:\"detach\",value:function(e){if(!e)throw new Error(\"[Tribute] Must pass in a DOM node or NodeList.\");if(\"undefined\"!=typeof jQuery&&e instanceof jQuery&&(e=e.get()),e.constructor===NodeList||e.constructor===HTMLCollection||e.constructor===Array)for(var t=e.length,n=0;n<t;++n)this._detach(e[n]);else this._detach(e)}},{key:\"_detach\",value:function(e){var t=this;this.events.unbind(e),e.tributeMenu&&this.menuEvents.unbind(e.tributeMenu),setTimeout((function(){e.removeAttribute(\"data-tribute\"),t.isActive=!1,e.tributeMenu&&e.tributeMenu.remove()}))}},{key:\"isActive\",get:function(){return this._isActive},set:function(e){if(this._isActive!=e&&(this._isActive=e,this.current.element)){var t=new CustomEvent(\"tribute-active-\".concat(e));this.current.element.dispatchEvent(t)}}}],[{key:\"defaultSelectTemplate\",value:function(e){return void 0===e?\"\".concat(this.current.collection.trigger).concat(this.current.mentionText):this.range.isContentEditable(this.current.element)?'<span class=\"tribute-mention\">'+(this.current.collection.trigger+e.original[this.current.collection.fillAttr])+\"</span>\":this.current.collection.trigger+e.original[this.current.collection.fillAttr]}},{key:\"defaultMenuItemTemplate\",value:function(e){return e.string}},{key:\"inputTypes\",value:function(){return[\"TEXTAREA\",\"INPUT\"]}}]),t}()}));\n//# sourceMappingURL=tribute.min.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/tributejs/dist/tribute.min.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/custom-ui/tagInput.tsx":
/*!***********************************************!*\
  !*** ./src/components/custom-ui/tagInput.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TagInput: function() { return /* binding */ TagInput; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var opendb_app_db_utils_lib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! opendb-app-db-utils/lib */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/index.js\");\n/* harmony import */ var opendb_app_db_utils_lib__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(opendb_app_db_utils_lib__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_clipboard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/clipboard */ \"(app-pages-browser)/./src/utils/clipboard.ts\");\n/* harmony import */ var _taggableInput_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./taggableInput.css */ \"(app-pages-browser)/./src/components/custom-ui/taggableInput.css\");\n/* harmony import */ var _providers_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/alert */ \"(app-pages-browser)/./src/providers/alert.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var tributejs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! tributejs */ \"(app-pages-browser)/./node_modules/tributejs/dist/tribute.min.js\");\n/* harmony import */ var tributejs__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(tributejs__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _components_custom_ui_tagInputHelpers__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/custom-ui/tagInputHelpers */ \"(app-pages-browser)/./src/components/custom-ui/tagInputHelpers.ts\");\n/* __next_internal_client_entry_do_not_use__ TagInput auto */ \nvar _s = $RefreshSig$();\n\n\n\n// import Tribute, {TributeItem, TributeOptions} from \"tributejs\";\n\n\n\n\n\n\n\n\n// import dynamic from \"next/dynamic\";\n// @ts-ignore\n// const Tribute: any = dynamic(() => import('tributejs').then((module) => module.default), {ssr: false});\nconst TagInput = (props)=>{\n    _s();\n    const idRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(props.id || \"tag-input-\".concat(props.key || \"\", \"-\").concat(new Date().getTime()));\n    const value = (props.value || \"\").toString();\n    const onChangeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(props.onChange);\n    onChangeRef.current = props.onChange;\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const options = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const options = [];\n        const titleMap = {};\n        for (let value of Object.values(props.tagOptionsMap)){\n            options.push(value);\n            titleMap[value.tag] = value.label;\n        }\n        return {\n            options,\n            titleMap\n        };\n    }, [\n        props.tagOptionsMap\n    ]);\n    const valueRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)((0,opendb_app_db_utils_lib__WEBPACK_IMPORTED_MODULE_2__.taggedTextToTaggedHtml)(value, true, options.titleMap, \"curly\"));\n    const alert = (0,_providers_alert__WEBPACK_IMPORTED_MODULE_5__.useAlert)();\n    const tributeProps = {\n        values: options.options,\n        selectTemplate: function(item) {\n            const id = \"tag-\".concat(new Date().getTime());\n            return '<span data-tag=\"'.concat(item.original.tag, '\" data-tag-label=\"').concat(item.original.label, '\" id=\"').concat(id, '\" contenteditable=\"false\">').concat(item.original.label, \"</span>\");\n        },\n        autocompleteMode: props.autocompleteMode,\n        requireLeadingSpace: false,\n        noMatchTemplate: ()=>\"\",\n        menuItemTemplate: function(item) {\n            return '<div class=\"bg-white text-xs px-2 py-1 font-medium cursor-pointer hover:bg-neutral-200\">'.concat(item.original.label, \"</div>\");\n        },\n        trigger: \"@\",\n        lookup: \"label\",\n        // @ts-ignore\n        menuItemLimit: props.menuLimit || 30,\n        menuShowMinLength: props.minShowLength,\n        containerClass: \"shadow-md bg-white p-1.5 border mt-2 tribute-highlight h-fit max-h-64 overflow-y-auto overflow-x-hidden\"\n    };\n    const tributeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new (tributejs__WEBPACK_IMPORTED_MODULE_10___default())(tributeProps));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const tribute = tributeRef.current;\n        const element = inputRef.current;\n        if (!element) return;\n        const handleClick = (e)=>{\n            e.stopPropagation();\n        };\n        element.addEventListener(\"click\", handleClick);\n        return ()=>{\n            element.removeEventListener(\"click\", handleClick);\n        };\n    }, []);\n    const savedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(value);\n    const toSaveRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(value);\n    const editorHasChangesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const debounceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(lodash_debounce__WEBPACK_IMPORTED_MODULE_9___default()(async ()=>{\n        if (savedRef.current === toSaveRef.current) return;\n        if (toSaveRef.current !== undefined && editorHasChangesRef.current) {\n            var _onChangeRef_current;\n            const toSave = toSaveRef.current;\n            (_onChangeRef_current = onChangeRef.current) === null || _onChangeRef_current === void 0 ? void 0 : _onChangeRef_current.call(onChangeRef, toSave);\n            savedRef.current = toSave;\n            editorHasChangesRef.current = false;\n        // currentUserMadeChangesRef.current = false\n        // console.log(\"Saved:\", {toSave})\n        }\n    }, props.debounceTimeoutMS || 2500));\n    function saveSelection(containerEl) {\n        const selection = window.getSelection();\n        if (!selection || selection.rangeCount === 0) return null;\n        const range = selection.getRangeAt(0);\n        return range;\n    }\n    function restoreSelection(containerEl, savedRange) {\n        if (!savedRange) return;\n        const range = document.createRange();\n        let sel = window.getSelection(); // Declare here, so it's accessible everywhere\n        try {\n            range.setStart(savedRange.startContainer, savedRange.startOffset);\n            range.setEnd(savedRange.endContainer, savedRange.endOffset);\n            if (sel) {\n                sel.removeAllRanges();\n                sel.addRange(range);\n            }\n        } catch (e) {\n            console.error(\"Failed to restore selection:\", e);\n            // Fallback: Place cursor at the end if restoration fails\n            range.selectNodeContents(containerEl);\n            range.collapse(false);\n            sel = window.getSelection(); // Ensure sel is defined here\n            if (sel) {\n                sel.removeAllRanges();\n                sel.addRange(range);\n            }\n        }\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const element = inputRef.current;\n        if (!element) return;\n        // Only update if the element is not focused.\n        if (document.activeElement === element) return;\n        const newHtml = (0,opendb_app_db_utils_lib__WEBPACK_IMPORTED_MODULE_2__.taggedTextToTaggedHtml)(value, true, options.titleMap, \"curly\");\n        const currentTaggedText = (0,opendb_app_db_utils_lib__WEBPACK_IMPORTED_MODULE_2__.taggedHtmlToTaggedText)(valueRef.current, \"curly\");\n        if (value !== currentTaggedText) {\n            valueRef.current = newHtml;\n            const savedSelection = saveSelection(element);\n            element.innerHTML = newHtml;\n            restoreSelection(element, savedSelection);\n        }\n    }, [\n        value,\n        options.titleMap\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!inputRef.current) return;\n        (0,_utils_clipboard__WEBPACK_IMPORTED_MODULE_3__.TextPasteElement)(inputRef.current);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const element = document.getElementById(idRef.current);\n        if (!element) return;\n        const onBlur = ()=>{\n            let taggedText = (0,opendb_app_db_utils_lib__WEBPACK_IMPORTED_MODULE_2__.taggedHtmlToTaggedText)(valueRef.current, \"curly\");\n            if (props.stripTagsInOutput) {\n                taggedText = (0,opendb_app_db_utils_lib__WEBPACK_IMPORTED_MODULE_2__.strReplaceAll)(\"<br>\", taggedText, \"\\n\");\n                taggedText = (0,opendb_app_db_utils_lib__WEBPACK_IMPORTED_MODULE_2__.stripHtmlTags)(taggedText, props.preserveNewLinesInOutput);\n            }\n            editorHasChangesRef.current = true;\n            toSaveRef.current = taggedText.trim();\n            // console.log({valueRef: valueRef.current, taggedText})\n            debounceRef.current();\n        // console.log(\"onBlur\", taggedText.trim())\n        };\n        const handleChange = (e)=>{\n            if (e && (e === null || e === void 0 ? void 0 : e.stopPropagation)) e.stopPropagation();\n            valueRef.current = element.value || element.innerHTML;\n            onBlur();\n        };\n        const onClick = (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            if (!e.target) return;\n            const targetElement = e.target;\n            const tag = targetElement.getAttribute(\"data-tag\");\n            const tagLabel = targetElement.getAttribute(\"data-tag-label\");\n            if (!tag || !tagLabel) return;\n            const tagSplit = tag.slice(2, -2).split(\"/\");\n            const [tagString, defaultVal] = tagSplit;\n            let defaultValue = defaultVal || \"\";\n            alert.confirm(\"Customize\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            className: \"text-xs mb-2 block\",\n                            children: [\n                                \"If \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: tagLabel\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\tagInput.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 62\n                                }, undefined),\n                                \" is not available, use\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\tagInput.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                className: \"form-control text-xs rounded-none\",\n                                type: \"text\",\n                                defaultValue: defaultValue,\n                                onChange: (e)=>{\n                                    defaultValue = e.target.value.trim();\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\tagInput.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\tagInput.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\tagInput.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false), ()=>{\n                targetElement.setAttribute(\"data-tag\", \"{{\".concat(tagString).concat(defaultValue ? \"/\" + defaultValue : \"\", \"}}\"));\n                targetElement.innerText = \"\".concat(tagLabel).concat(defaultValue ? \" / \" + defaultValue : \"\");\n                element.focus();\n                setTimeout(()=>{\n                    handleChange();\n                    (0,_components_custom_ui_tagInputHelpers__WEBPACK_IMPORTED_MODULE_11__.placeCaretAfterNode)(targetElement);\n                }, 250);\n            });\n        };\n        element.addEventListener(\"click\", onClick);\n        const tribute = tributeRef.current;\n        tribute.attach(element);\n        const config = {\n            attributes: false,\n            childList: true,\n            subtree: true\n        };\n        // Callback function to execute when mutations are observed\n        const callback = (mutationList, observer)=>{\n            if (!element) return;\n            // console.log({mutationList})\n            for (const mutation of mutationList){\n                if (mutation.type === \"childList\" || mutation.type === \"subtree\") {\n                // console.log('A child node has been added or removed.');\n                } else if (mutation.type === \"attributes\") {\n                // console.log(`The ${mutation.attributeName} attribute was modified.`);\n                }\n                handleChange();\n            }\n        };\n        const observer = new MutationObserver(callback);\n        observer.observe(element, config);\n        element.addEventListener(\"input\", handleChange);\n        element.addEventListener(\"blur\", handleChange);\n        element.addEventListener(\"change\", handleChange);\n        // const keyDown = (e: KeyboardEvent) => {\n        //     if (e.key === 'Enter') {\n        //         e.preventDefault()\n        //         document.execCommand('insertHTML', false, '<div><br></div>');\n        //     }\n        // }\n        // element.addEventListener('keydown', keyDown)\n        (0,_utils_clipboard__WEBPACK_IMPORTED_MODULE_3__.TextPasteElement)(element);\n        console.log(\"Taggable Input attached to Id: \", props.id);\n        element.innerHTML = valueRef.current;\n        // dangerouslySetInnerHTML={{__html: valueRef.current}}\n        return ()=>{\n            tribute.detach(element);\n            observer.disconnect();\n            element.removeEventListener(\"click\", onClick);\n            element.removeEventListener(\"input\", handleChange);\n            element.removeEventListener(\"blur\", handleChange);\n            element.removeEventListener(\"change\", handleChange);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"border border-neutral-300 rounded-none text-xs font-medium focus:border-black focus-within:border-black group cE\", props.wrapperClassName),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"p-3 !outline-0 !ring-0 !border-0 !rounded-none text-xs leading-7 min-h-32\", props.className),\n                    ref: inputRef,\n                    // onBlur={onBlur}\n                    id: idRef.current,\n                    spellCheck: \"false\",\n                    contentEditable: !(props.readonly || props.disabled),\n                    \"data-placeholder\": props.placeholder\n                }, idRef.current, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\tagInput.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 13\n                }, undefined),\n                props.showVariables && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t p-3 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                className: \"font-semibold text-muted-foreground text-xs mb-2 block\",\n                                children: \"Available variables\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\tagInput.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: Object.keys(props.tagOptionsMap).map((k)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-2 mb-2 font-semibold text-xs text-neutral-700 whitespace-nowrap inline-block select-none rounded-full outline outline-neutral-300 leading-relaxed px-1\",\n                                        children: props.tagOptionsMap[k].label\n                                    }, k, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\tagInput.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 68\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\tagInput.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                className: \"font-medium text-muted-foreground text-xs text-[11px]\",\n                                children: \"Mention variables with @, and double-click the inserted variable to add a default value when the variable is missing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\tagInput.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\tagInput.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\tagInput.tsx\",\n            lineNumber: 280,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false);\n} // export const TaggableInput = TagInput\n // const Tribute: any = dynamic(() => import('tributejs').then((module) => module), {ssr: false});\n;\n_s(TagInput, \"/PiohoyevO0d4znBzDNTKMvPwTc=\", false, function() {\n    return [\n        _providers_alert__WEBPACK_IMPORTED_MODULE_5__.useAlert\n    ];\n});\n_c = TagInput;\nvar _c;\n$RefreshReg$(_c, \"TagInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/custom-ui/tagInput.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/custom-ui/tagInputHelpers.ts":
/*!*****************************************************!*\
  !*** ./src/components/custom-ui/tagInputHelpers.ts ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   placeCaretAfterNode: function() { return /* binding */ placeCaretAfterNode; }\n/* harmony export */ });\nconst placeCaretAfterNode = (node)=>{\n    if (false) {}\n    const selection = window.getSelection();\n    if (selection) {\n        const range = document.createRange();\n        range.setStartAfter(node);\n        range.collapse(true);\n        selection.removeAllRanges();\n        selection.addRange(range);\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/custom-ui/tagInputHelpers.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/clipboard.ts":
/*!********************************!*\
  !*** ./src/utils/clipboard.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TextPasteElement: function() { return /* binding */ TextPasteElement; },\n/* harmony export */   clearWindowSelection: function() { return /* binding */ clearWindowSelection; },\n/* harmony export */   copyToClipboard: function() { return /* binding */ copyToClipboard; }\n/* harmony export */ });\nfunction copyToClipboard(text) {\n    const input = document.createElement(\"textarea\");\n    input.value = text;\n    input.style.position = \"fixed\";\n    input.style.left = \"-9999px\";\n    document.body.append(input);\n    input.select();\n    document.execCommand(\"Copy\");\n    document.body.removeChild(input);\n    if (window.navigator) navigator.clipboard.writeText(text).then();\n}\nconst TextPasteElement = (htmlElement)=>{\n    htmlElement.addEventListener(\"paste\", (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        e.stopImmediatePropagation();\n        console.log(\"Paste event\", e);\n        let text = (e.originalEvent || e).clipboardData.getData(\"text/plain\");\n        if (htmlElement.dataset.log) {\n            console.log(\"text before\", text);\n        }\n        text = text.replace(/(\\r\\n|\\n\\r|\\r|\\n)/g, \"<br>\");\n        if (htmlElement.dataset.log) {\n            console.log(\"text after\", text);\n        }\n        document.execCommand(\"insertHTML\", false, text);\n    });\n};\nconst clearWindowSelection = ()=>{\n    if (window && window.getSelection) {\n        var _window_getSelection, _window_getSelection1;\n        if ((_window_getSelection = window.getSelection()) === null || _window_getSelection === void 0 ? void 0 : _window_getSelection.empty) {\n            var // Chrome\n            _window_getSelection2;\n            (_window_getSelection2 = window.getSelection()) === null || _window_getSelection2 === void 0 ? void 0 : _window_getSelection2.empty();\n        } else if ((_window_getSelection1 = window.getSelection()) === null || _window_getSelection1 === void 0 ? void 0 : _window_getSelection1.removeAllRanges) {\n            var // Firefox\n            _window_getSelection3;\n            (_window_getSelection3 = window.getSelection()) === null || _window_getSelection3 === void 0 ? void 0 : _window_getSelection3.removeAllRanges();\n        }\n    // @ts-ignore\n    } else if (document.selection && document.selection.empty) {\n        // IE\n        // @ts-ignore\n        document.selection.empty();\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/clipboard.ts\n"));

/***/ })

}]);