"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx":
/*!****************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/index.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarView: function() { return /* binding */ CalendarView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _providers_template__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/template */ \"(app-pages-browser)/./src/providers/template.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/workspace/main/views/table */ \"(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/providers/stackedPeek */ \"(app-pages-browser)/./src/providers/stackedPeek.tsx\");\n/* harmony import */ var _components_DayView__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./components/DayView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx\");\n/* harmony import */ var _components_WeekView__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./components/WeekView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\");\n/* harmony import */ var _components_MonthView__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./components/MonthView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\");\n/* harmony import */ var _components_CalendarEventItem__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./components/CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/custom-ui/customSelect */ \"(app-pages-browser)/./src/components/custom-ui/customSelect.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./components/CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInMilliseconds/index.js\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInDays/index.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Custom modifier to restrict dragging to the calendar main content area only\nconst restrictToCalendarContainer = (param)=>{\n    let { transform, draggingNodeRect, windowRect } = param;\n    if (!draggingNodeRect || !windowRect) {\n        return transform;\n    }\n    // Find the calendar main content container (the div that contains the views)\n    const calendarContainer = document.querySelector('[data-calendar-content=\"true\"]');\n    if (!calendarContainer) {\n        return transform;\n    }\n    const containerRect = calendarContainer.getBoundingClientRect();\n    // For month view, we need to account for the side card\n    // The side card is positioned on the right side of the calendar\n    const sideCard = document.querySelector('[data-side-card=\"true\"]');\n    let maxX = containerRect.right - draggingNodeRect.width;\n    if (sideCard) {\n        const sideCardRect = sideCard.getBoundingClientRect();\n        // Restrict dragging to not go past the side card\n        maxX = Math.min(maxX, sideCardRect.left - draggingNodeRect.width);\n    }\n    // Find header areas to prevent dragging over them\n    const timeLabels = document.querySelector('[data-time-labels=\"true\"]');\n    const dayHeaders = document.querySelector('[data-day-headers=\"true\"]');\n    const allDayRow = document.querySelector('[data-all-day-row=\"true\"]');\n    // Calculate the boundaries relative to the window\n    let minX = containerRect.left;\n    let minY = containerRect.top;\n    const maxY = containerRect.bottom - draggingNodeRect.height;\n    // Prevent dragging over time labels (left side in day/week view)\n    if (timeLabels) {\n        const timeLabelsRect = timeLabels.getBoundingClientRect();\n        minX = Math.max(minX, timeLabelsRect.right);\n    }\n    // Prevent dragging over day headers (top of calendar)\n    if (dayHeaders) {\n        const dayHeadersRect = dayHeaders.getBoundingClientRect();\n        minY = Math.max(minY, dayHeadersRect.bottom);\n    }\n    // Prevent dragging over all-day row (if present)\n    if (allDayRow) {\n        const allDayRowRect = allDayRow.getBoundingClientRect();\n        minY = Math.max(minY, allDayRowRect.bottom);\n    }\n    // Get current pointer position\n    const currentX = transform.x + draggingNodeRect.left;\n    const currentY = transform.y + draggingNodeRect.top;\n    // Constrain the position\n    const constrainedX = Math.min(Math.max(currentX, minX), maxX);\n    const constrainedY = Math.min(Math.max(currentY, minY), maxY);\n    return {\n        ...transform,\n        x: constrainedX - draggingNodeRect.left,\n        y: constrainedY - draggingNodeRect.top\n    };\n};\n// Custom hook to track previous value\nconst usePrevious = (value)=>{\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        ref.current = value;\n    });\n    return ref.current;\n};\n_s(usePrevious, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nconst CalendarView = (props)=>{\n    _s1();\n    const { databaseStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace)();\n    const { definition } = props;\n    const { accessLevel } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage)();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_16__.useScreenSize)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_17__.useMaybeRecord)();\n    const maybeShared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared)();\n    const maybeTemplate = (0,_providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate)();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [viewType, setViewType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"week\");\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSideCalendar, setShowSideCalendar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!isMobile);\n    const [activeDragData, setActiveDragData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const savedScrollTop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const pointerCoordinates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const isInRecordTab = !!maybeRecord;\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const databaseId = definition.databaseId;\n    const database = databaseStore[definition.databaseId];\n    const isPublishedView = !!maybeShared;\n    const editable = !definition.lockContent && !isPublishedView && !!accessLevel;\n    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    const { createRecords, updateRecordValues, setPeekRecordId, peekRecordId, refreshDatabase, deleteRecords } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews)();\n    const { sorts, filter, search } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering)();\n    const { selectedIds, setSelectedIds } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewSelection)();\n    const prevPeekRecordId = usePrevious(peekRecordId);\n    const { openRecord } = (0,_providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__.useStackedPeek)();\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.PointerSensor, {\n        activationConstraint: {\n            distance: 8\n        }\n    }), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.TouchSensor, {\n        activationConstraint: {\n            delay: 150,\n            tolerance: 5\n        }\n    }));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n        const container = document.getElementById(containerId);\n        if (container) {\n            requestAnimationFrame(()=>{\n                container.scrollTop = savedScrollTop.current;\n            });\n        }\n    }, [\n        selectedEvent,\n        viewType\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowSideCalendar(!isMobile);\n    }, [\n        isMobile\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only refresh if the peek view was open and is now closed.\n        if (prevPeekRecordId && !peekRecordId) {\n            console.log(\"Peek view was closed, refreshing calendar data\");\n            refreshDatabase(definition.databaseId);\n        }\n    }, [\n        peekRecordId,\n        prevPeekRecordId,\n        definition.databaseId,\n        refreshDatabase\n    ]);\n    if (!database) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__.PageLoader, {\n        size: \"full\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 211,\n        columnNumber: 25\n    }, undefined);\n    const getEvents = ()=>{\n        var _workspace_workspaceMember, _database_database;\n        if (!database) return [];\n        const { rows } = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.filterAndSortRecords)(database, members, databaseStore, definition.filter || {\n            match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All,\n            conditions: []\n        }, filter, sorts.length ? sorts : definition.sorts || [], (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspaceMember = workspace.workspaceMember) === null || _workspace_workspaceMember === void 0 ? void 0 : _workspace_workspaceMember.userId) || \"\", (database === null || database === void 0 ? void 0 : (_database_database = database.database) === null || _database_database === void 0 ? void 0 : _database_database.id) || \"\");\n        const filteredRows = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.searchFilteredRecords)(search || \"\", rows);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        return filteredRows.map((row)=>{\n            const startValue = row.processedRecord.processedRecordValues[definition.eventStartColumnId];\n            let startDate;\n            if (startValue && typeof startValue === \"string\") {\n                startDate = new Date(startValue);\n            } else {\n                startDate = new Date();\n            }\n            let endDate;\n            if (definition.eventEndColumnId) {\n                const endValue = row.processedRecord.processedRecordValues[definition.eventEndColumnId];\n                if (endValue && typeof endValue === \"string\") {\n                    endDate = new Date(endValue);\n                } else {\n                    endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n                }\n            } else {\n                endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n            }\n            const title = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getRecordTitle)(row.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts);\n            return {\n                id: row.id,\n                title,\n                start: startDate,\n                end: endDate,\n                record: row.record,\n                processedRecord: row.processedRecord\n            };\n        });\n    };\n    const getFilteredEvents = ()=>{\n        const baseEvents = getEvents();\n        if (!searchTerm.trim()) {\n            return baseEvents;\n        }\n        return baseEvents.filter((event)=>{\n            const searchLower = searchTerm.toLowerCase();\n            return event.title.toLowerCase().includes(searchLower);\n        });\n    };\n    const onDragStart = (event)=>{\n        if (event.active.data.current) {\n            var _event_active_rect_current_translated, _event_active_rect_current, _event_active_rect_current_translated1, _event_active_rect_current_translated2;\n            const initialPointerY = pointerCoordinates.current.y;\n            var _event_active_rect_current_translated_top;\n            const initialEventTop = (_event_active_rect_current_translated_top = (_event_active_rect_current = event.active.rect.current) === null || _event_active_rect_current === void 0 ? void 0 : (_event_active_rect_current_translated = _event_active_rect_current.translated) === null || _event_active_rect_current_translated === void 0 ? void 0 : _event_active_rect_current_translated.top) !== null && _event_active_rect_current_translated_top !== void 0 ? _event_active_rect_current_translated_top : 0;\n            const grabOffsetY = initialPointerY - initialEventTop;\n            // Get the exact dimensions from the DOM element\n            // Handle both event and segment types\n            const { payload, type } = event.active.data.current;\n            let draggedElement;\n            // Find the element based on the drag type\n            if (type === \"segment\") {\n                // For segments in day/week views\n                draggedElement = document.getElementById(\"segment-\".concat(payload.id));\n            } else {\n                // For regular events in month view\n                draggedElement = document.getElementById(\"event-\".concat(payload.id));\n            }\n            // If element not found by ID, try to find it by the active ID\n            if (!draggedElement) {\n                draggedElement = document.querySelector('[data-dnd-id=\"'.concat(event.active.id, '\"]'));\n            }\n            // Get dimensions from the element or fallback to DnD rect\n            const width = draggedElement ? draggedElement.offsetWidth : (_event_active_rect_current_translated1 = event.active.rect.current.translated) === null || _event_active_rect_current_translated1 === void 0 ? void 0 : _event_active_rect_current_translated1.width;\n            const height = draggedElement ? draggedElement.offsetHeight : (_event_active_rect_current_translated2 = event.active.rect.current.translated) === null || _event_active_rect_current_translated2 === void 0 ? void 0 : _event_active_rect_current_translated2.height;\n            // Store all necessary data for the drag operation\n            setActiveDragData({\n                ...event.active.data.current,\n                grabOffsetY,\n                width,\n                height\n            });\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"touchmove\", handleTouchMove);\n        }\n    };\n    const onDragEnd = async (param)=>{\n        let { active, over } = param;\n        document.removeEventListener(\"mousemove\", handleMouseMove);\n        document.removeEventListener(\"touchmove\", handleTouchMove);\n        // Always clear drag data at the start to prevent stuck states\n        setActiveDragData(null);\n        if (!over || !active || !canEditData || active.id === over.id) {\n            return;\n        }\n        const activeData = active.data.current;\n        const overData = over.data.current;\n        if (!activeData || !overData) {\n            return;\n        }\n        // Additional check: ensure we're only dropping on valid drop zones\n        const validDropTypes = [\n            \"timeslot-day\",\n            \"timeslot-week\",\n            \"allday-day\",\n            \"allday-week\",\n            \"daycell\"\n        ];\n        if (!validDropTypes.includes(overData.type)) {\n            return;\n        }\n        const { payload, type } = activeData;\n        const eventToUpdate = type === \"segment\" ? payload.originalEvent : payload;\n        // Ensure we're updating the correct event\n        if (!eventToUpdate || !eventToUpdate.id) {\n            return;\n        }\n        const originalStart = new Date(eventToUpdate.start);\n        const originalEnd = new Date(eventToUpdate.end);\n        const duration = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_27__[\"default\"])(originalEnd, originalStart);\n        let newStart;\n        if (overData.type.startsWith(\"allday\")) {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(originalStart, dayDifference);\n            // For all-day drops, normalize the time to the start of the day\n            newStart.setHours(0, 0, 0, 0);\n        } else if (overData.type.startsWith(\"timeslot\")) {\n            newStart = new Date(overData.date);\n            // Enhanced Minute-level precision calculation\n            const grabOffsetY = activeData.grabOffsetY || 0;\n            const slotTop = over.rect.top;\n            const pointerY = pointerCoordinates.current.y;\n            // Adjust pointer position based on initial grab point\n            const adjustedPointerY = pointerY - grabOffsetY;\n            const offsetY = adjustedPointerY - slotTop;\n            const slotHeight = over.rect.height; // Should be 60px\n            const minutesFromTop = offsetY / slotHeight * 60;\n            // Snap to nearest 5-minute interval\n            const snappedMinutes = Math.round(minutesFromTop / 5) * 5;\n            const newMinutes = Math.max(0, Math.min(59, snappedMinutes));\n            newStart.setHours(overData.hour, newMinutes, originalStart.getSeconds(), originalStart.getMilliseconds());\n        } else if (overData.type === \"daycell\") {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(originalStart, dayDifference);\n        } else {\n            return; // Invalid drop target\n        }\n        const newEnd = new Date(newStart.getTime() + duration);\n        const recordId = eventToUpdate.record.id;\n        const newValues = {\n            [definition.eventStartColumnId]: newStart.toISOString(),\n            ...definition.eventEndColumnId && {\n                [definition.eventEndColumnId]: newEnd.toISOString()\n            }\n        };\n        try {\n            await updateRecordValues(definition.databaseId, [\n                recordId\n            ], newValues);\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.success('Event \"'.concat(eventToUpdate.title, '\" updated.'));\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to update event.\");\n            console.error(\"Error updating event:\", error);\n        }\n    };\n    const handleMouseMove = (event)=>{\n        pointerCoordinates.current = {\n            x: event.clientX,\n            y: event.clientY\n        };\n    };\n    const handleTouchMove = (event)=>{\n        const touch = event.touches[0];\n        pointerCoordinates.current = {\n            x: touch.clientX,\n            y: touch.clientY\n        };\n    };\n    // Custom collision detection that prioritizes drop zones over events\n    const customCollisionDetection = (args)=>{\n        const { droppableContainers, active } = args;\n        // Filter out event and segment droppables, only keep valid drop zones\n        const validDropZones = Array.from(droppableContainers.values()).filter((container)=>{\n            const data = container.data.current;\n            return data && [\n                \"timeslot-day\",\n                \"timeslot-week\",\n                \"allday-day\",\n                \"allday-week\",\n                \"daycell\"\n            ].includes(data.type);\n        });\n        // If no valid drop zones found, fall back to default collision detection\n        if (validDropZones.length === 0) {\n            return (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.pointerWithin)(args);\n        }\n        // Use pointer-based collision detection on filtered containers\n        const collisions = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.pointerWithin)({\n            ...args,\n            droppableContainers: new Map(validDropZones.map((container)=>[\n                    container.id,\n                    container\n                ]))\n        });\n        // If no collisions found with pointerWithin, try rectIntersection for better accuracy\n        if (collisions.length === 0) {\n            const rectCollisions = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.rectIntersection)({\n                ...args,\n                droppableContainers: new Map(validDropZones.map((container)=>[\n                        container.id,\n                        container\n                    ]))\n            });\n            return rectCollisions;\n        }\n        return collisions;\n    };\n    const events = getFilteredEvents();\n    const goToToday = ()=>setSelectedDate(new Date());\n    const goToPrevious = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(prevDate, -1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const goToNext = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(prevDate, 1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const handleRequestCreateEvent = function(date) {\n        let useSystemTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (useSystemTime) {\n            const now = new Date();\n            const newDate = new Date(date);\n            newDate.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());\n            handleCreateEvent(newDate);\n        } else {\n            handleCreateEvent(date);\n        }\n    };\n    const handleCreateEvent = async function() {\n        let date = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : new Date();\n        if (!canEditData) return;\n        const startTime = new Date(date);\n        const endTime = new Date(startTime.getTime() + (definition.defaultDuration || 30) * 60000);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        try {\n            const recordValues = {\n                [definition.eventStartColumnId]: startTime.toISOString(),\n                ...definition.eventEndColumnId && {\n                    [definition.eventEndColumnId]: endTime.toISOString()\n                }\n            };\n            if (titleColOpts.titleColId) {\n                recordValues[titleColOpts.titleColId] = \"New Event\";\n            }\n            const result = await createRecords(definition.databaseId, [\n                recordValues\n            ]);\n            if (result && result.records && result.records.length > 0) {\n                const newRecordId = result.records[0].id;\n                if (newRecordId) {\n                    await refreshDatabase(definition.databaseId);\n                    setPeekRecordId(newRecordId);\n                    sonner__WEBPACK_IMPORTED_MODULE_14__.toast.success(\"New event created\");\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Error accessing the new event\");\n                }\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to create event properly\");\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to create event\");\n        }\n    };\n    const handleEventClick = (event)=>{\n        if (event && event.id) {\n            const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n            const container = document.getElementById(containerId);\n            if (container) {\n                savedScrollTop.current = container.scrollTop;\n            }\n            openRecord(event.id, event.record.databaseId);\n            setSelectedEvent(event.id);\n        }\n    };\n    const getHeaderDateDisplay = ()=>{\n        switch(viewType){\n            case \"day\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n            case \"week\":\n                return \"\".concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(selectedDate, -selectedDate.getDay()), \"MMM d\"), \" - \").concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(selectedDate, 6 - selectedDate.getDay()), \"MMM d, yyyy\"));\n            case \"month\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM yyyy\");\n            default:\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n        }\n    };\n    const handleEventDelete = async (event)=>{\n        if (!canEditData) return;\n        try {\n            await deleteRecords(database.database.id, [\n                event.record.id\n            ]);\n        } catch (error) {\n            console.error(\"Error deleting event:\", error);\n        }\n    };\n    const viewTypeOptions = [\n        {\n            id: \"day\",\n            value: \"day\",\n            title: \"Day\",\n            data: \"day\"\n        },\n        {\n            id: \"week\",\n            value: \"week\",\n            title: \"Week\",\n            data: \"week\"\n        },\n        {\n            id: \"month\",\n            value: \"month\",\n            title: \"Month\",\n            data: \"month\"\n        }\n    ];\n    const selectedViewOption = viewType === \"day\" ? [\n        \"day\"\n    ] : viewType === \"week\" ? [\n        \"week\"\n    ] : [\n        \"month\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"border-b border-neutral-300 bg-white\", isInRecordTab && \"py-1\"),\n                children: [\n                    isMobile ? /* Mobile Header Layout */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"p-2\", isInRecordTab && \"py-1\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black truncate flex-1 mr-2\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(!showSideCalendar),\n                                        className: \"rounded-full h-8 px-3 text-xs text-black hover:bg-gray-50\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 588,\n                                columnNumber: 12\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToToday,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: \"Today\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToPrevious,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleLeftIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 622,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 18\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToNext,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleRightIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 18\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                                options: viewTypeOptions,\n                                                selectedIds: selectedViewOption,\n                                                onChange: (selected)=>{\n                                                    if (selected.length > 0) {\n                                                        setViewType(selected[0]);\n                                                    }\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black w-20\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                placeholder: \"View\",\n                                                hideSearch: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 20\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 656,\n                                                columnNumber: 18\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 637,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 601,\n                                columnNumber: 12\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 587,\n                        columnNumber: 10\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"flex items-center justify-between px-4\", isInRecordTab ? \"py-1\" : \"py-2\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: goToToday,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 675,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToPrevious,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleLeftIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 686,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToNext,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleRightIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 14\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 708,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 674,\n                                columnNumber: 10\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                placeholder: \"Search events...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-48 pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                                className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 722,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 715,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                        options: viewTypeOptions,\n                                        selectedIds: selectedViewOption,\n                                        onChange: (selected)=>{\n                                            if (selected.length > 0) {\n                                                setViewType(selected[0]);\n                                            }\n                                        },\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6 w-20\" : \"h-8 w-28\"),\n                                        placeholder: \"View\",\n                                        hideSearch: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 726,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black gap-1\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Add Event\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 751,\n                                                columnNumber: 35\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 743,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 713,\n                                columnNumber: 10\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 670,\n                        columnNumber: 8\n                    }, undefined),\n                    isMobile && !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                    placeholder: \"Search events...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"w-full pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 761,\n                                    columnNumber: 12\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                    className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 767,\n                                    columnNumber: 12\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                            lineNumber: 760,\n                            columnNumber: 10\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 759,\n                        columnNumber: 8\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 581,\n                columnNumber: 6\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex min-h-0\",\n                children: [\n                    showSideCalendar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"flex-none bg-white\", isMobile ? \"w-full absolute z-50 backdrop-blur-sm h-full shadow-lg\" : \"w-fit border-r border-neutral-300\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 border-b border-neutral-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 781,\n                                        columnNumber: 10\n                                    }, undefined),\n                                    isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(false),\n                                        className: \"rounded-full h-8 w-8 p-1 text-black hover:bg-neutral-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Close\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 788,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            \"\\xd7\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 783,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 780,\n                                columnNumber: 8\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                mode: \"single\",\n                                selected: selectedDate,\n                                onSelect: (date)=>{\n                                    if (date) {\n                                        setSelectedDate(date);\n                                        if (isMobile) {\n                                            setShowSideCalendar(false);\n                                        }\n                                    }\n                                },\n                                className: \"rounded-md border-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 793,\n                                columnNumber: 8\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 776,\n                        columnNumber: 6\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DndContext, {\n                        sensors: sensors,\n                        onDragStart: onDragStart,\n                        onDragEnd: onDragEnd,\n                        collisionDetection: customCollisionDetection,\n                        modifiers: [\n                            restrictToCalendarContainer\n                        ],\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                \"data-calendar-content\": \"true\",\n                                children: [\n                                    viewType === \"day\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DayView__WEBPACK_IMPORTED_MODULE_19__.DayView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 818,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"week\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WeekView__WEBPACK_IMPORTED_MODULE_20__.WeekView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 831,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"month\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MonthView__WEBPACK_IMPORTED_MODULE_21__.MonthView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: (date)=>handleRequestCreateEvent(date, true),\n                                        canEditData: canEditData,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 845,\n                                        columnNumber: 7\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 816,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DragOverlay, {\n                                dropAnimation: null,\n                                children: activeDragData && activeDragData.type === \"segment\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_26__.CalendarEventSegment, {\n                                    segment: activeDragData.payload,\n                                    view: viewType === \"day\" ? \"day\" : \"week\",\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height,\n                                        position: \"relative\",\n                                        transform: \"none\"\n                                    },\n                                    isDragging: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 861,\n                                    columnNumber: 11\n                                }, undefined) : activeDragData && activeDragData.type === \"event\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CalendarEventItem__WEBPACK_IMPORTED_MODULE_22__.CalendarEventItem, {\n                                    event: activeDragData.payload,\n                                    view: viewType,\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height,\n                                        position: \"relative\",\n                                        transform: \"none\"\n                                    },\n                                    isDragging: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 874,\n                                    columnNumber: 11\n                                }, undefined) : null\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 859,\n                                columnNumber: 6\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 809,\n                        columnNumber: 6\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 774,\n                columnNumber: 4\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 580,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CalendarView, \"gC+3ORgbOSOWj17lR2zBDE1vWrs=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace,\n        _providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage,\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_16__.useScreenSize,\n        _providers_record__WEBPACK_IMPORTED_MODULE_17__.useMaybeRecord,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared,\n        _providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewSelection,\n        usePrevious,\n        _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__.useStackedPeek,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors\n    ];\n});\n_c = CalendarView;\nvar _c;\n$RefreshReg$(_c, \"CalendarView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx\n"));

/***/ })

});