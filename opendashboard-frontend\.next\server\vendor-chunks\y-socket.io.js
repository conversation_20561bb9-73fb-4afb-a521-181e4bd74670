"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/y-socket.io";
exports.ids = ["vendor-chunks/y-socket.io"];
exports.modules = {

/***/ "(ssr)/./node_modules/y-socket.io/dist/index.mjs":
/*!*************************************************!*\
  !*** ./node_modules/y-socket.io/dist/index.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SocketIOProvider: () => (/* binding */ h)\n/* harmony export */ });\n/* harmony import */ var yjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! yjs */ \"(ssr)/./node_modules/yjs/dist/yjs.mjs\");\n/* harmony import */ var lib0_broadcastchannel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lib0/broadcastchannel */ \"(ssr)/./node_modules/lib0/broadcastchannel.js\");\n/* harmony import */ var y_protocols_awareness__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! y-protocols/awareness */ \"(ssr)/./node_modules/y-protocols/awareness.js\");\n/* harmony import */ var lib0_observable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lib0/observable */ \"(ssr)/./node_modules/lib0/observable.js\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n// src/client/provider.ts\n\n\n\n\n\nvar h = class extends lib0_observable__WEBPACK_IMPORTED_MODULE_2__.Observable {\n  constructor(e, t, o = new yjs__WEBPACK_IMPORTED_MODULE_3__.Doc(), {\n    autoConnect: c = !0,\n    awareness: i = new y_protocols_awareness__WEBPACK_IMPORTED_MODULE_0__.Awareness(o),\n    resyncInterval: l = -1,\n    disableBc: p = !1,\n    auth: y = {}\n  }, d = void 0) {\n    super();\n    this.bcconnected = !1;\n    this._synced = !1;\n    this.resyncInterval = null;\n    this.initSyncListeners = () => {\n      this.socket.on(\"sync-step-1\", (e, t) => {\n        t(yjs__WEBPACK_IMPORTED_MODULE_3__.encodeStateAsUpdate(this.doc, new Uint8Array(e))), this.synced = !0;\n      }), this.socket.on(\"sync-update\", this.onSocketSyncUpdate);\n    };\n    this.initAwarenessListeners = () => {\n      this.socket.on(\"awareness-update\", (e) => {\n        y_protocols_awareness__WEBPACK_IMPORTED_MODULE_0__.applyAwarenessUpdate(this.awareness, new Uint8Array(e), this);\n      });\n    };\n    this.initSystemListeners = () => {\n      typeof window != \"undefined\" ? window.addEventListener(\"beforeunload\", this.beforeUnloadHandler) : typeof process != \"undefined\" && process.on(\"exit\", this.beforeUnloadHandler);\n    };\n    this.onSocketConnection = (e = -1) => {\n      this.emit(\"status\", [{ status: \"connected\" }]), this.socket.emit(\"sync-step-1\", yjs__WEBPACK_IMPORTED_MODULE_3__.encodeStateVector(this.doc), (t) => {\n        yjs__WEBPACK_IMPORTED_MODULE_3__.applyUpdate(this.doc, new Uint8Array(t), this);\n      }), this.awareness.getLocalState() !== null && this.socket.emit(\"awareness-update\", y_protocols_awareness__WEBPACK_IMPORTED_MODULE_0__.encodeAwarenessUpdate(this.awareness, [this.doc.clientID])), e > 0 && (this.resyncInterval = setInterval(() => {\n        this.socket.disconnected || this.socket.emit(\"sync-step-1\", yjs__WEBPACK_IMPORTED_MODULE_3__.encodeStateVector(this.doc), (t) => {\n          yjs__WEBPACK_IMPORTED_MODULE_3__.applyUpdate(this.doc, new Uint8Array(t), this);\n        });\n      }, e));\n    };\n    this.onSocketDisconnection = (e) => {\n      this.emit(\"connection-close\", [e, this]), this.synced = !1, y_protocols_awareness__WEBPACK_IMPORTED_MODULE_0__.removeAwarenessStates(this.awareness, Array.from(this.awareness.getStates().keys()).filter((t) => t !== this.doc.clientID), this), this.emit(\"status\", [{ status: \"disconnected\" }]);\n    };\n    this.onSocketConnectionError = (e) => {\n      this.emit(\"connection-error\", [e, this]);\n    };\n    this.onUpdateDoc = (e, t) => {\n      t !== this && (this.socket.emit(\"sync-update\", e), this.bcconnected && lib0_broadcastchannel__WEBPACK_IMPORTED_MODULE_4__.publish(this._broadcastChannel, {\n        type: \"sync-update\",\n        data: e\n      }, this));\n    };\n    this.onSocketSyncUpdate = (e) => {\n      yjs__WEBPACK_IMPORTED_MODULE_3__.applyUpdate(this.doc, new Uint8Array(e), this);\n    };\n    this.awarenessUpdate = ({ added: e, updated: t, removed: o }, c) => {\n      let i = e.concat(t).concat(o);\n      this.socket.emit(\"awareness-update\", y_protocols_awareness__WEBPACK_IMPORTED_MODULE_0__.encodeAwarenessUpdate(this.awareness, i)), this.bcconnected && lib0_broadcastchannel__WEBPACK_IMPORTED_MODULE_4__.publish(this._broadcastChannel, {\n        type: \"awareness-update\",\n        data: y_protocols_awareness__WEBPACK_IMPORTED_MODULE_0__.encodeAwarenessUpdate(this.awareness, i)\n      }, this);\n    };\n    this.beforeUnloadHandler = () => {\n      y_protocols_awareness__WEBPACK_IMPORTED_MODULE_0__.removeAwarenessStates(this.awareness, [this.doc.clientID], \"window unload\");\n    };\n    this.connectBc = () => {\n      this.bcconnected || (lib0_broadcastchannel__WEBPACK_IMPORTED_MODULE_4__.subscribe(this._broadcastChannel, this.onBroadcastChannelMessage), this.bcconnected = !0), lib0_broadcastchannel__WEBPACK_IMPORTED_MODULE_4__.publish(this._broadcastChannel, { type: \"sync-step-1\", data: yjs__WEBPACK_IMPORTED_MODULE_3__.encodeStateVector(this.doc) }, this), lib0_broadcastchannel__WEBPACK_IMPORTED_MODULE_4__.publish(this._broadcastChannel, { type: \"sync-step-2\", data: yjs__WEBPACK_IMPORTED_MODULE_3__.encodeStateAsUpdate(this.doc) }, this), lib0_broadcastchannel__WEBPACK_IMPORTED_MODULE_4__.publish(this._broadcastChannel, { type: \"query-awareness\", data: null }, this), lib0_broadcastchannel__WEBPACK_IMPORTED_MODULE_4__.publish(this._broadcastChannel, { type: \"awareness-update\", data: y_protocols_awareness__WEBPACK_IMPORTED_MODULE_0__.encodeAwarenessUpdate(this.awareness, [this.doc.clientID]) }, this);\n    };\n    this.disconnectBc = () => {\n      lib0_broadcastchannel__WEBPACK_IMPORTED_MODULE_4__.publish(this._broadcastChannel, {\n        type: \"awareness-update\",\n        data: y_protocols_awareness__WEBPACK_IMPORTED_MODULE_0__.encodeAwarenessUpdate(this.awareness, [this.doc.clientID], /* @__PURE__ */ new Map())\n      }, this), this.bcconnected && (lib0_broadcastchannel__WEBPACK_IMPORTED_MODULE_4__.unsubscribe(this._broadcastChannel, this.onBroadcastChannelMessage), this.bcconnected = !1);\n    };\n    this.onBroadcastChannelMessage = (e, t) => {\n      if (t !== this && e.type.length > 0)\n        switch (e.type) {\n          case \"sync-step-1\":\n            lib0_broadcastchannel__WEBPACK_IMPORTED_MODULE_4__.publish(this._broadcastChannel, {\n              type: \"sync-step-2\",\n              data: yjs__WEBPACK_IMPORTED_MODULE_3__.encodeStateAsUpdate(this.doc, e.data)\n            }, this);\n            break;\n          case \"sync-step-2\":\n            yjs__WEBPACK_IMPORTED_MODULE_3__.applyUpdate(this.doc, new Uint8Array(e.data), this);\n            break;\n          case \"sync-update\":\n            yjs__WEBPACK_IMPORTED_MODULE_3__.applyUpdate(this.doc, new Uint8Array(e.data), this);\n            break;\n          case \"query-awareness\":\n            lib0_broadcastchannel__WEBPACK_IMPORTED_MODULE_4__.publish(this._broadcastChannel, {\n              type: \"awareness-update\",\n              data: y_protocols_awareness__WEBPACK_IMPORTED_MODULE_0__.encodeAwarenessUpdate(this.awareness, Array.from(this.awareness.getStates().keys()))\n            }, this);\n            break;\n          case \"awareness-update\":\n            y_protocols_awareness__WEBPACK_IMPORTED_MODULE_0__.applyAwarenessUpdate(this.awareness, new Uint8Array(e.data), this);\n            break;\n          default:\n            break;\n        }\n    };\n    for (; e[e.length - 1] === \"/\"; )\n      e = e.slice(0, e.length - 1);\n    this._url = e, this.roomName = t, this.doc = o, this.awareness = i, this._broadcastChannel = `${e}/${t}`, this.disableBc = p, this._socketIoOptions = d, this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_1__.io)(`${this.url}/yjs|${t}`, {\n      autoConnect: !1,\n      transports: [\"websocket\"],\n      forceNew: !0,\n      auth: y,\n      ...d\n    }), this.doc.on(\"update\", this.onUpdateDoc), this.socket.on(\"connect\", () => this.onSocketConnection(l)), this.socket.on(\"disconnect\", (r) => this.onSocketDisconnection(r)), this.socket.on(\"connect_error\", (r) => this.onSocketConnectionError(r)), this.initSyncListeners(), this.initAwarenessListeners(), this.initSystemListeners(), i.on(\"update\", this.awarenessUpdate), c && this.connect();\n  }\n  get broadcastChannel() {\n    return this._broadcastChannel;\n  }\n  get url() {\n    return this._url;\n  }\n  get synced() {\n    return this._synced;\n  }\n  set synced(e) {\n    this._synced !== e && (this._synced = e, this.emit(\"synced\", [e]), this.emit(\"sync\", [e]));\n  }\n  connect() {\n    this.socket.connected || (this.emit(\"status\", [{ status: \"connecting\" }]), this.socket.connect(), this.disableBc || this.connectBc(), this.synced = !1);\n  }\n  disconnect() {\n    this.socket.connected && (this.disconnectBc(), this.socket.disconnect());\n  }\n  destroy() {\n    this.resyncInterval != null && clearInterval(this.resyncInterval), this.disconnect(), typeof window != \"undefined\" ? window.removeEventListener(\"beforeunload\", this.beforeUnloadHandler) : typeof process != \"undefined\" && process.off(\"exit\", this.beforeUnloadHandler), this.awareness.off(\"update\", this.awarenessUpdate), this.awareness.destroy(), this.doc.off(\"update\", this.onUpdateDoc), super.destroy();\n  }\n};\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/y-socket.io/dist/index.mjs\n");

/***/ })

};
;