"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/html-whitespace-sensitive-tag-names";
exports.ids = ["vendor-chunks/html-whitespace-sensitive-tag-names"];
exports.modules = {

/***/ "(ssr)/./node_modules/html-whitespace-sensitive-tag-names/lib/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/html-whitespace-sensitive-tag-names/lib/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   whitespaceSensitiveTagNames: () => (/* binding */ whitespaceSensitiveTagNames)\n/* harmony export */ });\n/**\n * List of HTML tag names that are whitespace sensitive.\n */\nconst whitespaceSensitiveTagNames = [\n  'pre',\n  'script',\n  'style',\n  'textarea'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaHRtbC13aGl0ZXNwYWNlLXNlbnNpdGl2ZS10YWctbmFtZXMvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2h0bWwtd2hpdGVzcGFjZS1zZW5zaXRpdmUtdGFnLW5hbWVzL2xpYi9pbmRleC5qcz83Nzk0Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogTGlzdCBvZiBIVE1MIHRhZyBuYW1lcyB0aGF0IGFyZSB3aGl0ZXNwYWNlIHNlbnNpdGl2ZS5cbiAqL1xuZXhwb3J0IGNvbnN0IHdoaXRlc3BhY2VTZW5zaXRpdmVUYWdOYW1lcyA9IFtcbiAgJ3ByZScsXG4gICdzY3JpcHQnLFxuICAnc3R5bGUnLFxuICAndGV4dGFyZWEnXG5dXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/html-whitespace-sensitive-tag-names/lib/index.js\n");

/***/ })

};
;