"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx":
/*!***************************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx ***!
  \***************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarEventItem: function() { return /* binding */ CalendarEventItem; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _utils_color__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/color */ \"(app-pages-browser)/./src/utils/color.ts\");\n/* harmony import */ var _utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/dateUtils */ \"(app-pages-browser)/./src/utils/dateUtils.ts\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst CalendarEventItem = (param)=>{\n    let { event, style, onClick, onContextMenu, view = \"month\", isDragging, showTitle = true, isDraggable = true } = param;\n    _s();\n    const dragRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { attributes, listeners, setNodeRef, isDragging: dndIsDragging } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDraggable)({\n        id: \"event-\".concat(event.id),\n        data: {\n            type: \"event\",\n            payload: event\n        },\n        disabled: !isDraggable\n    });\n    // Combine external isDragging with internal dndIsDragging\n    const combinedIsDragging = isDragging || dndIsDragging;\n    // Memoize event calculations\n    const eventDetails = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const start = new Date(event.start);\n        const eventHeight = (style === null || style === void 0 ? void 0 : style.height) ? parseInt(style.height.toString().replace(\"px\", \"\")) : null;\n        return {\n            start,\n            eventSize: (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.getEventSize)(eventHeight),\n            isInstant: (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.isInstantEvent)(event),\n            formattedTime: (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.formatEventTime)(start, view, {\n                shortFormat: true\n            })\n        };\n    }, [\n        event,\n        style,\n        view\n    ]);\n    // Memoize styling\n    const eventStyles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const denimColorInfo = (0,_utils_color__WEBPACK_IMPORTED_MODULE_3__.ColorInfo)(\"Denim\");\n        // Extract RGB values from the rgba string and make it fully opaque\n        const rgbMatch = denimColorInfo.bg.match(/rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)/);\n        const opaqueBackground = rgbMatch ? \"rgb(\".concat(rgbMatch[1], \", \").concat(rgbMatch[2], \", \").concat(rgbMatch[3], \")\") : denimColorInfo.bg;\n        return {\n            ...style,\n            backgroundColor: opaqueBackground,\n            minHeight: view === \"month\" ? \"24px\" : \"30px\",\n            marginBottom: view === \"month\" ? \"4px\" : \"0px\",\n            // Add subtle shadow for better visual depth\n            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)\",\n            opacity: combinedIsDragging ? 0.5 : isDraggable ? 1 : 0.7\n        };\n    }, [\n        style,\n        view,\n        combinedIsDragging,\n        isDraggable\n    ]);\n    // Memoize classes\n    const eventClasses = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        // Month view or small events\n        if (view === \"month\" || eventDetails.eventSize === \"small\") {\n            return {\n                baseClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-md select-none text-black text-xs overflow-hidden\", !combinedIsDragging && isDraggable && \"cursor-pointer\", !combinedIsDragging && !isDraggable && \"cursor-default\", \"p-1\"),\n                containerClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-1\", \"flex-nowrap\"),\n                titleClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-medium truncate leading-tight text-xs overflow-hidden\", \"max-w-[70%]\"),\n                timeClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"opacity-75 text-xs flex-shrink-0\", \"text-[0.65rem]\")\n            };\n        }\n        // Day and Week views for medium/large events\n        return {\n            baseClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-md select-none text-black text-xs overflow-hidden\", !combinedIsDragging && isDraggable && \"cursor-pointer\", !combinedIsDragging && !isDraggable && \"cursor-default\", \"p-2\"),\n            containerClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col\", \"space-y-0.5\"),\n            titleClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-medium truncate leading-tight text-xs overflow-hidden\"),\n            timeClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"opacity-75 text-xs\")\n        };\n    }, [\n        eventDetails,\n        view,\n        combinedIsDragging,\n        isDraggable\n    ]);\n    // Render event content based on view and size\n    const renderEventContent = ()=>{\n        // Month view or small events\n        if (view === \"month\" || eventDetails.eventSize === \"small\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: eventClasses.containerClasses,\n                children: [\n                    showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: eventClasses.titleClasses,\n                        children: event.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 13\n                    }, undefined),\n                    showTitle && eventDetails.formattedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: eventClasses.timeClasses,\n                        children: eventDetails.formattedTime\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, undefined);\n        }\n        // Day and Week views for medium/large events\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: eventClasses.containerClasses,\n            children: [\n                showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: eventClasses.titleClasses,\n                    children: event.title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 11\n                }, undefined),\n                showTitle && eventDetails.formattedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: eventClasses.timeClasses,\n                    children: eventDetails.formattedTime\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n            lineNumber: 145,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"event-\".concat(event.id),\n        \"data-dnd-id\": \"event-\".concat(event.id),\n        ref: (node)=>{\n            setNodeRef(node);\n            dragRef.current = node;\n        },\n        style: {\n            ...eventStyles,\n            zIndex: combinedIsDragging ? 1000 : \"auto\" // Ensure dragged item is on top\n        },\n        className: eventClasses.baseClasses,\n        onClick: onClick,\n        onContextMenu: onContextMenu,\n        ...isDraggable ? {\n            ...listeners,\n            ...attributes\n        } : {},\n        children: renderEventContent()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, undefined);\n}; // import React, { useMemo } from 'react';\n // import { cn } from '@/lib/utils';\n // import { CalendarEvent } from '@/typings/page';\n // import { ColorInfo } from '@/utils/color';\n // import { \n //   formatEventTime, \n //   isInstantEvent, \n //   getEventSize \n // } from '@/utils/dateUtils';\n // export const CalendarEventItem = ({\n //   event,\n //   style,\n //   onClick,\n //   onContextMenu,\n //   view = 'month' // Default to month view if not specified\n // }: {\n //   event: CalendarEvent;\n //   style?: React.CSSProperties;\n //   onClick: (e: React.MouseEvent) => void;\n //   onContextMenu?: (e: React.MouseEvent) => void;\n //   view?: 'day' | 'week' | 'month';\n // }) => {\n //   // Memoize event calculations\n //   const eventDetails = useMemo(() => {\n //     const start = new Date(event.start);\n //     const eventHeight = style?.height ? parseInt(style.height.toString().replace('px', '')) : null;\n //     return { \n //       start, \n //       eventSize: getEventSize(eventHeight),\n //       isInstant: isInstantEvent(event),\n //       formattedTime: formatEventTime(start, view, { shortFormat: true })\n //     };\n //   }, [event, style, view]);\n //   // Memoize styling\n //   const eventStyles = useMemo(() => {\n //     const denimColorInfo = ColorInfo('Denim');\n //     return {\n //       ...style,\n //       backgroundColor: denimColorInfo.bg,\n //       minHeight: '30px',\n //     };\n //   }, [style]);\n //   // Memoize classes\n //   const eventClasses = useMemo(() => {\n //     // Month view or small events\n //     if (view === 'month' || eventDetails.eventSize === 'small') {\n //       return {\n //         baseClasses: cn(\n //           \"rounded-md cursor-pointer select-none text-black text-xs overflow-hidden\",\n //           eventDetails.eventSize === 'small' ? \"p-1\" : \"p-2\"\n //         ),\n //         containerClasses: cn(\n //           \"flex items-center space-x-1\",\n //           \"flex-nowrap\"\n //         ),\n //         titleClasses: cn(\n //           \"font-medium truncate leading-tight text-xs overflow-hidden flex-grow\",\n //           \"max-w-[70%]\"\n //         ),\n //         timeClasses: cn(\n //           \"opacity-75 text-xs flex-shrink-0\",\n //           \"text-[0.65rem]\"\n //         )\n //       };\n //     }\n //     // Day and Week views for medium/large events\n //     return {\n //       baseClasses: cn(\n //         \"rounded-md cursor-pointer select-none text-black text-xs overflow-hidden\",\n //         eventDetails.eventSize === 'small' ? \"p-1\" : \"p-2\"\n //       ),\n //       containerClasses: cn(\n //         \"flex flex-col\",\n //         eventDetails.eventSize === 'small' ? \"space-y-0\" : \"space-y-0.5\"\n //       ),\n //       titleClasses: cn(\n //         \"font-medium truncate leading-tight text-xs overflow-hidden\",\n //         eventDetails.eventSize === 'small' ? \"mb-0\" : \"\"\n //       ),\n //       timeClasses: cn(\n //         \"opacity-75 text-xs\",\n //         eventDetails.eventSize === 'small' ? \"text-[0.65rem] leading-none\" : \"\"\n //       )\n //     };\n //   }, [eventDetails, view]);\n //   // Render event content based on view and size\n //   const renderEventContent = () => {\n //     // Month view or small events\n //     if (view === 'month' || eventDetails.eventSize === 'small') {\n //       return (\n //         <div className={eventClasses.containerClasses}>\n //           <span className={eventClasses.titleClasses}>{event.title}</span>\n //           <span className={eventClasses.timeClasses}>\n //             {eventDetails.formattedTime}\n //           </span>\n //         </div>\n //       );\n //     }\n //     // Day and Week views for medium/large events\n //     return (\n //       <div className={eventClasses.containerClasses}>\n //         <div className={eventClasses.titleClasses}>{event.title}</div>\n //         <div className={eventClasses.timeClasses}>\n //           {eventDetails.formattedTime}\n //         </div>\n //       </div>\n //     );\n //   };\n //   return (\n //     <div\n //       style={eventStyles}\n //       className={eventClasses.baseClasses}\n //       onClick={onClick}\n //       onContextMenu={onContextMenu}\n //     >\n //       {renderEventContent()}\n //     </div>\n //   );\n // }; \n_s(CalendarEventItem, \"kiz8nz2pAp5RwnBQHmiQwwayVv8=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDraggable\n    ];\n});\n_c = CalendarEventItem;\nvar _c;\n$RefreshReg$(_c, \"CalendarEventItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\n"));

/***/ })

});