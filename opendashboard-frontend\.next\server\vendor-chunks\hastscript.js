"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hastscript";
exports.ids = ["vendor-chunks/hastscript"];
exports.modules = {

/***/ "(ssr)/./node_modules/hastscript/lib/core.js":
/*!*********************************************!*\
  !*** ./node_modules/hastscript/lib/core.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   core: () => (/* binding */ core)\n/* harmony export */ });\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/normalize.js\");\n/* harmony import */ var hast_util_parse_selector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-parse-selector */ \"(ssr)/./node_modules/hast-util-parse-selector/lib/index.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/space-separated-tokens/index.js\");\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/comma-separated-tokens/index.js\");\n/**\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Content} Content\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('property-information').Info} Info\n * @typedef {import('property-information').Schema} Schema\n */\n\n/**\n * @typedef {Content | Root} Node\n *   Any concrete `hast` node.\n * @typedef {Root | Element} HResult\n *   Result from a `h` (or `s`) call.\n *\n * @typedef {string | number} HStyleValue\n *   Value for a CSS style field.\n * @typedef {Record<string, HStyleValue>} HStyle\n *   Supported value of a `style` prop.\n * @typedef {string | number | boolean | null | undefined} HPrimitiveValue\n *   Primitive property value.\n * @typedef {Array<string | number>} HArrayValue\n *   List of property values for space- or comma separated values (such as `className`).\n * @typedef {HPrimitiveValue | HArrayValue} HPropertyValue\n *   Primitive value or list value.\n * @typedef {{[property: string]: HPropertyValue | HStyle}} HProperties\n *   Acceptable value for element properties.\n *\n * @typedef {string | number | null | undefined} HPrimitiveChild\n *   Primitive children, either ignored (nullish), or turned into text nodes.\n * @typedef {Array<Node | HPrimitiveChild>} HArrayChild\n *   List of children.\n * @typedef {Node | HPrimitiveChild | HArrayChild} HChild\n *   Acceptable child value.\n */\n\n\n\n\n\n\nconst buttonTypes = new Set(['menu', 'submit', 'reset', 'button'])\n\nconst own = {}.hasOwnProperty\n\n/**\n * @param {Schema} schema\n * @param {string} defaultTagName\n * @param {Array<string>} [caseSensitive]\n */\nfunction core(schema, defaultTagName, caseSensitive) {\n  const adjust = caseSensitive && createAdjustMap(caseSensitive)\n\n  const h =\n    /**\n     * @type {{\n     *   (): Root\n     *   (selector: null | undefined, ...children: Array<HChild>): Root\n     *   (selector: string, properties?: HProperties, ...children: Array<HChild>): Element\n     *   (selector: string, ...children: Array<HChild>): Element\n     * }}\n     */\n    (\n      /**\n       * Hyperscript compatible DSL for creating virtual hast trees.\n       *\n       * @param {string | null} [selector]\n       * @param {HProperties | HChild} [properties]\n       * @param {Array<HChild>} children\n       * @returns {HResult}\n       */\n      function (selector, properties, ...children) {\n        let index = -1\n        /** @type {HResult} */\n        let node\n\n        if (selector === undefined || selector === null) {\n          node = {type: 'root', children: []}\n          // @ts-expect-error Properties are not supported for roots.\n          children.unshift(properties)\n        } else {\n          node = (0,hast_util_parse_selector__WEBPACK_IMPORTED_MODULE_0__.parseSelector)(selector, defaultTagName)\n          // Normalize the name.\n          node.tagName = node.tagName.toLowerCase()\n          if (adjust && own.call(adjust, node.tagName)) {\n            node.tagName = adjust[node.tagName]\n          }\n\n          // Handle props.\n          if (isProperties(properties, node.tagName)) {\n            /** @type {string} */\n            let key\n\n            for (key in properties) {\n              if (own.call(properties, key)) {\n                // @ts-expect-error `node.properties` is set.\n                addProperty(schema, node.properties, key, properties[key])\n              }\n            }\n          } else {\n            children.unshift(properties)\n          }\n        }\n\n        // Handle children.\n        while (++index < children.length) {\n          addChild(node.children, children[index])\n        }\n\n        if (node.type === 'element' && node.tagName === 'template') {\n          node.content = {type: 'root', children: node.children}\n          node.children = []\n        }\n\n        return node\n      }\n    )\n\n  return h\n}\n\n/**\n * @param {HProperties | HChild} value\n * @param {string} name\n * @returns {value is HProperties}\n */\nfunction isProperties(value, name) {\n  if (\n    value === null ||\n    value === undefined ||\n    typeof value !== 'object' ||\n    Array.isArray(value)\n  ) {\n    return false\n  }\n\n  if (name === 'input' || !value.type || typeof value.type !== 'string') {\n    return true\n  }\n\n  if ('children' in value && Array.isArray(value.children)) {\n    return false\n  }\n\n  if (name === 'button') {\n    return buttonTypes.has(value.type.toLowerCase())\n  }\n\n  return !('value' in value)\n}\n\n/**\n * @param {Schema} schema\n * @param {Properties} properties\n * @param {string} key\n * @param {HStyle | HPropertyValue} value\n * @returns {void}\n */\nfunction addProperty(schema, properties, key, value) {\n  const info = (0,property_information__WEBPACK_IMPORTED_MODULE_1__.find)(schema, key)\n  let index = -1\n  /** @type {HPropertyValue} */\n  let result\n\n  // Ignore nullish and NaN values.\n  if (value === undefined || value === null) return\n\n  if (typeof value === 'number') {\n    // Ignore NaN.\n    if (Number.isNaN(value)) return\n\n    result = value\n  }\n  // Booleans.\n  else if (typeof value === 'boolean') {\n    result = value\n  }\n  // Handle list values.\n  else if (typeof value === 'string') {\n    if (info.spaceSeparated) {\n      result = (0,space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__.parse)(value)\n    } else if (info.commaSeparated) {\n      result = (0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__.parse)(value)\n    } else if (info.commaOrSpaceSeparated) {\n      result = (0,space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__.parse)((0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__.parse)(value).join(' '))\n    } else {\n      result = parsePrimitive(info, info.property, value)\n    }\n  } else if (Array.isArray(value)) {\n    result = value.concat()\n  } else {\n    result = info.property === 'style' ? style(value) : String(value)\n  }\n\n  if (Array.isArray(result)) {\n    /** @type {Array<string | number>} */\n    const finalResult = []\n\n    while (++index < result.length) {\n      // @ts-expect-error Assume no booleans in array.\n      finalResult[index] = parsePrimitive(info, info.property, result[index])\n    }\n\n    result = finalResult\n  }\n\n  // Class names (which can be added both on the `selector` and here).\n  if (info.property === 'className' && Array.isArray(properties.className)) {\n    // @ts-expect-error Assume no booleans in `className`.\n    result = properties.className.concat(result)\n  }\n\n  properties[info.property] = result\n}\n\n/**\n * @param {Array<Content>} nodes\n * @param {HChild} value\n * @returns {void}\n */\nfunction addChild(nodes, value) {\n  let index = -1\n\n  if (value === undefined || value === null) {\n    // Empty.\n  } else if (typeof value === 'string' || typeof value === 'number') {\n    nodes.push({type: 'text', value: String(value)})\n  } else if (Array.isArray(value)) {\n    while (++index < value.length) {\n      addChild(nodes, value[index])\n    }\n  } else if (typeof value === 'object' && 'type' in value) {\n    if (value.type === 'root') {\n      addChild(nodes, value.children)\n    } else {\n      nodes.push(value)\n    }\n  } else {\n    throw new Error('Expected node, nodes, or string, got `' + value + '`')\n  }\n}\n\n/**\n * Parse a single primitives.\n *\n * @param {Info} info\n * @param {string} name\n * @param {HPrimitiveValue} value\n * @returns {HPrimitiveValue}\n */\nfunction parsePrimitive(info, name, value) {\n  if (typeof value === 'string') {\n    if (info.number && value && !Number.isNaN(Number(value))) {\n      return Number(value)\n    }\n\n    if (\n      (info.boolean || info.overloadedBoolean) &&\n      (value === '' || (0,property_information__WEBPACK_IMPORTED_MODULE_4__.normalize)(value) === (0,property_information__WEBPACK_IMPORTED_MODULE_4__.normalize)(name))\n    ) {\n      return true\n    }\n  }\n\n  return value\n}\n\n/**\n * Serialize a `style` object as a string.\n *\n * @param {HStyle} value\n *   Style object.\n * @returns {string}\n *   CSS string.\n */\nfunction style(value) {\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {string} */\n  let key\n\n  for (key in value) {\n    if (own.call(value, key)) {\n      result.push([key, value[key]].join(': '))\n    }\n  }\n\n  return result.join('; ')\n}\n\n/**\n * Create a map to adjust casing.\n *\n * @param {Array<string>} values\n *   List of properly cased keys.\n * @returns {Record<string, string>}\n *   Map of lowercase keys to uppercase keys.\n */\nfunction createAdjustMap(values) {\n  /** @type {Record<string, string>} */\n  const result = {}\n  let index = -1\n\n  while (++index < values.length) {\n    result[values[index].toLowerCase()] = values[index]\n  }\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/lib/core.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/lib/html.js":
/*!*********************************************!*\
  !*** ./node_modules/hastscript/lib/html.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   h: () => (/* binding */ h)\n/* harmony export */ });\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var _core_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core.js */ \"(ssr)/./node_modules/hastscript/lib/core.js\");\n/**\n * @typedef {import('./core.js').HChild} Child\n *   Acceptable child value.\n * @typedef {import('./core.js').HProperties} Properties\n *   Acceptable value for element properties.\n * @typedef {import('./core.js').HResult} Result\n *   Result from a `h` (or `s`) call.\n *\n * @typedef {import('./jsx-classic.js').Element} h.JSX.Element\n * @typedef {import('./jsx-classic.js').IntrinsicAttributes} h.JSX.IntrinsicAttributes\n * @typedef {import('./jsx-classic.js').IntrinsicElements} h.JSX.IntrinsicElements\n * @typedef {import('./jsx-classic.js').ElementChildrenAttribute} h.JSX.ElementChildrenAttribute\n */\n\n\n\n\nconst h = (0,_core_js__WEBPACK_IMPORTED_MODULE_0__.core)(property_information__WEBPACK_IMPORTED_MODULE_1__.html, 'div')\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9saWIvaHRtbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBLGFBQWEsNEJBQTRCO0FBQ3pDO0FBQ0EsYUFBYSxpQ0FBaUM7QUFDOUM7QUFDQSxhQUFhLDZCQUE2QjtBQUMxQztBQUNBO0FBQ0EsYUFBYSxvQ0FBb0M7QUFDakQsYUFBYSxnREFBZ0Q7QUFDN0QsYUFBYSw4Q0FBOEM7QUFDM0QsYUFBYSxxREFBcUQ7QUFDbEU7O0FBRXlDO0FBQ1g7O0FBRXZCLFVBQVUsOENBQUksQ0FBQyxzREFBSSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9saWIvaHRtbC5qcz80YTM1Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi9jb3JlLmpzJykuSENoaWxkfSBDaGlsZFxuICogICBBY2NlcHRhYmxlIGNoaWxkIHZhbHVlLlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi9jb3JlLmpzJykuSFByb3BlcnRpZXN9IFByb3BlcnRpZXNcbiAqICAgQWNjZXB0YWJsZSB2YWx1ZSBmb3IgZWxlbWVudCBwcm9wZXJ0aWVzLlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi9jb3JlLmpzJykuSFJlc3VsdH0gUmVzdWx0XG4gKiAgIFJlc3VsdCBmcm9tIGEgYGhgIChvciBgc2ApIGNhbGwuXG4gKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi9qc3gtY2xhc3NpYy5qcycpLkVsZW1lbnR9IGguSlNYLkVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4vanN4LWNsYXNzaWMuanMnKS5JbnRyaW5zaWNBdHRyaWJ1dGVzfSBoLkpTWC5JbnRyaW5zaWNBdHRyaWJ1dGVzXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2pzeC1jbGFzc2ljLmpzJykuSW50cmluc2ljRWxlbWVudHN9IGguSlNYLkludHJpbnNpY0VsZW1lbnRzXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2pzeC1jbGFzc2ljLmpzJykuRWxlbWVudENoaWxkcmVuQXR0cmlidXRlfSBoLkpTWC5FbGVtZW50Q2hpbGRyZW5BdHRyaWJ1dGVcbiAqL1xuXG5pbXBvcnQge2h0bWx9IGZyb20gJ3Byb3BlcnR5LWluZm9ybWF0aW9uJ1xuaW1wb3J0IHtjb3JlfSBmcm9tICcuL2NvcmUuanMnXG5cbmV4cG9ydCBjb25zdCBoID0gY29yZShodG1sLCAnZGl2JylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/lib/svg-case-sensitive-tag-names.js":
/*!*********************************************************************!*\
  !*** ./node_modules/hastscript/lib/svg-case-sensitive-tag-names.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   svgCaseSensitiveTagNames: () => (/* binding */ svgCaseSensitiveTagNames)\n/* harmony export */ });\nconst svgCaseSensitiveTagNames = [\n  'altGlyph',\n  'altGlyphDef',\n  'altGlyphItem',\n  'animateColor',\n  'animateMotion',\n  'animateTransform',\n  'clipPath',\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feDropShadow',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n  'foreignObject',\n  'glyphRef',\n  'linearGradient',\n  'radialGradient',\n  'solidColor',\n  'textArea',\n  'textPath'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9saWIvc3ZnLWNhc2Utc2Vuc2l0aXZlLXRhZy1uYW1lcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9saWIvc3ZnLWNhc2Utc2Vuc2l0aXZlLXRhZy1uYW1lcy5qcz85MDA0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBzdmdDYXNlU2Vuc2l0aXZlVGFnTmFtZXMgPSBbXG4gICdhbHRHbHlwaCcsXG4gICdhbHRHbHlwaERlZicsXG4gICdhbHRHbHlwaEl0ZW0nLFxuICAnYW5pbWF0ZUNvbG9yJyxcbiAgJ2FuaW1hdGVNb3Rpb24nLFxuICAnYW5pbWF0ZVRyYW5zZm9ybScsXG4gICdjbGlwUGF0aCcsXG4gICdmZUJsZW5kJyxcbiAgJ2ZlQ29sb3JNYXRyaXgnLFxuICAnZmVDb21wb25lbnRUcmFuc2ZlcicsXG4gICdmZUNvbXBvc2l0ZScsXG4gICdmZUNvbnZvbHZlTWF0cml4JyxcbiAgJ2ZlRGlmZnVzZUxpZ2h0aW5nJyxcbiAgJ2ZlRGlzcGxhY2VtZW50TWFwJyxcbiAgJ2ZlRGlzdGFudExpZ2h0JyxcbiAgJ2ZlRHJvcFNoYWRvdycsXG4gICdmZUZsb29kJyxcbiAgJ2ZlRnVuY0EnLFxuICAnZmVGdW5jQicsXG4gICdmZUZ1bmNHJyxcbiAgJ2ZlRnVuY1InLFxuICAnZmVHYXVzc2lhbkJsdXInLFxuICAnZmVJbWFnZScsXG4gICdmZU1lcmdlJyxcbiAgJ2ZlTWVyZ2VOb2RlJyxcbiAgJ2ZlTW9ycGhvbG9neScsXG4gICdmZU9mZnNldCcsXG4gICdmZVBvaW50TGlnaHQnLFxuICAnZmVTcGVjdWxhckxpZ2h0aW5nJyxcbiAgJ2ZlU3BvdExpZ2h0JyxcbiAgJ2ZlVGlsZScsXG4gICdmZVR1cmJ1bGVuY2UnLFxuICAnZm9yZWlnbk9iamVjdCcsXG4gICdnbHlwaFJlZicsXG4gICdsaW5lYXJHcmFkaWVudCcsXG4gICdyYWRpYWxHcmFkaWVudCcsXG4gICdzb2xpZENvbG9yJyxcbiAgJ3RleHRBcmVhJyxcbiAgJ3RleHRQYXRoJ1xuXVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/lib/svg-case-sensitive-tag-names.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/lib/svg.js":
/*!********************************************!*\
  !*** ./node_modules/hastscript/lib/svg.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   s: () => (/* binding */ s)\n/* harmony export */ });\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var _core_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core.js */ \"(ssr)/./node_modules/hastscript/lib/core.js\");\n/* harmony import */ var _svg_case_sensitive_tag_names_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./svg-case-sensitive-tag-names.js */ \"(ssr)/./node_modules/hastscript/lib/svg-case-sensitive-tag-names.js\");\n/**\n * @typedef {import('./core.js').HChild} Child\n *   Acceptable child value.\n * @typedef {import('./core.js').HProperties} Properties\n *   Acceptable value for element properties.\n * @typedef {import('./core.js').HResult} Result\n *   Result from a `h` (or `s`) call.\n *\n * @typedef {import('./jsx-classic.js').Element} s.JSX.Element\n * @typedef {import('./jsx-classic.js').IntrinsicAttributes} s.JSX.IntrinsicAttributes\n * @typedef {import('./jsx-classic.js').IntrinsicElements} s.JSX.IntrinsicElements\n * @typedef {import('./jsx-classic.js').ElementChildrenAttribute} s.JSX.ElementChildrenAttribute\n */\n\n\n\n\n\nconst s = (0,_core_js__WEBPACK_IMPORTED_MODULE_0__.core)(property_information__WEBPACK_IMPORTED_MODULE_1__.svg, 'g', _svg_case_sensitive_tag_names_js__WEBPACK_IMPORTED_MODULE_2__.svgCaseSensitiveTagNames)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9saWIvc3ZnLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUNBLGFBQWEsNEJBQTRCO0FBQ3pDO0FBQ0EsYUFBYSxpQ0FBaUM7QUFDOUM7QUFDQSxhQUFhLDZCQUE2QjtBQUMxQztBQUNBO0FBQ0EsYUFBYSxvQ0FBb0M7QUFDakQsYUFBYSxnREFBZ0Q7QUFDN0QsYUFBYSw4Q0FBOEM7QUFDM0QsYUFBYSxxREFBcUQ7QUFDbEU7O0FBRXdDO0FBQ1Y7QUFDNEM7O0FBRW5FLFVBQVUsOENBQUksQ0FBQyxxREFBRyxPQUFPLHNGQUF3QiIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9saWIvc3ZnLmpzP2NlZmIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2NvcmUuanMnKS5IQ2hpbGR9IENoaWxkXG4gKiAgIEFjY2VwdGFibGUgY2hpbGQgdmFsdWUuXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2NvcmUuanMnKS5IUHJvcGVydGllc30gUHJvcGVydGllc1xuICogICBBY2NlcHRhYmxlIHZhbHVlIGZvciBlbGVtZW50IHByb3BlcnRpZXMuXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2NvcmUuanMnKS5IUmVzdWx0fSBSZXN1bHRcbiAqICAgUmVzdWx0IGZyb20gYSBgaGAgKG9yIGBzYCkgY2FsbC5cbiAqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2pzeC1jbGFzc2ljLmpzJykuRWxlbWVudH0gcy5KU1guRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi9qc3gtY2xhc3NpYy5qcycpLkludHJpbnNpY0F0dHJpYnV0ZXN9IHMuSlNYLkludHJpbnNpY0F0dHJpYnV0ZXNcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4vanN4LWNsYXNzaWMuanMnKS5JbnRyaW5zaWNFbGVtZW50c30gcy5KU1guSW50cmluc2ljRWxlbWVudHNcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4vanN4LWNsYXNzaWMuanMnKS5FbGVtZW50Q2hpbGRyZW5BdHRyaWJ1dGV9IHMuSlNYLkVsZW1lbnRDaGlsZHJlbkF0dHJpYnV0ZVxuICovXG5cbmltcG9ydCB7c3ZnfSBmcm9tICdwcm9wZXJ0eS1pbmZvcm1hdGlvbidcbmltcG9ydCB7Y29yZX0gZnJvbSAnLi9jb3JlLmpzJ1xuaW1wb3J0IHtzdmdDYXNlU2Vuc2l0aXZlVGFnTmFtZXN9IGZyb20gJy4vc3ZnLWNhc2Utc2Vuc2l0aXZlLXRhZy1uYW1lcy5qcydcblxuZXhwb3J0IGNvbnN0IHMgPSBjb3JlKHN2ZywgJ2cnLCBzdmdDYXNlU2Vuc2l0aXZlVGFnTmFtZXMpXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/lib/svg.js\n");

/***/ })

};
;