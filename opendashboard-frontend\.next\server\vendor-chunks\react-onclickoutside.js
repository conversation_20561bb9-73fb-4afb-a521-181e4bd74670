"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-onclickoutside";
exports.ids = ["vendor-chunks/react-onclickoutside"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-onclickoutside/dist/react-onclickoutside.es.js":
/*!***************************************************************************!*\
  !*** ./node_modules/react-onclickoutside/dist/react-onclickoutside.es.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IGNORE_CLASS_NAME: () => (/* binding */ IGNORE_CLASS_NAME),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_1__);\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n\n  _setPrototypeOf(subClass, superClass);\n}\n\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}/**\n * Check whether some DOM node is our Component's node.\n */\nfunction isNodeFound(current, componentNode, ignoreClass) {\n  if (current === componentNode) {\n    return true;\n  } // SVG <use/> elements do not technically reside in the rendered DOM, so\n  // they do not have classList directly, but they offer a link to their\n  // corresponding element, which can have classList. This extra check is for\n  // that case.\n  // See: http://www.w3.org/TR/SVG11/struct.html#InterfaceSVGUseElement\n  // Discussion: https://github.com/Pomax/react-onclickoutside/pull/17\n\n\n  if (current.correspondingElement) {\n    return current.correspondingElement.classList.contains(ignoreClass);\n  }\n\n  return current.classList.contains(ignoreClass);\n}\n/**\n * Try to find our node in a hierarchy of nodes, returning the document\n * node as highest node if our node is not found in the path up.\n */\n\nfunction findHighest(current, componentNode, ignoreClass) {\n  if (current === componentNode) {\n    return true;\n  } // If source=local then this event came from 'somewhere'\n  // inside and should be ignored. We could handle this with\n  // a layered approach, too, but that requires going back to\n  // thinking in terms of Dom node nesting, running counter\n  // to React's 'you shouldn't care about the DOM' philosophy.\n  // Also cover shadowRoot node by checking current.host\n\n\n  while (current.parentNode || current.host) {\n    // Only check normal node without shadowRoot\n    if (current.parentNode && isNodeFound(current, componentNode, ignoreClass)) {\n      return true;\n    }\n\n    current = current.parentNode || current.host;\n  }\n\n  return current;\n}\n/**\n * Check if the browser scrollbar was clicked\n */\n\nfunction clickedScrollbar(evt) {\n  return document.documentElement.clientWidth <= evt.clientX || document.documentElement.clientHeight <= evt.clientY;\n}// ideally will get replaced with external dep\n// when rafrex/detect-passive-events#4 and rafrex/detect-passive-events#5 get merged in\nvar testPassiveEventSupport = function testPassiveEventSupport() {\n  if (typeof window === 'undefined' || typeof window.addEventListener !== 'function') {\n    return;\n  }\n\n  var passive = false;\n  var options = Object.defineProperty({}, 'passive', {\n    get: function get() {\n      passive = true;\n    }\n  });\n\n  var noop = function noop() {};\n\n  window.addEventListener('testPassiveEventSupport', noop, options);\n  window.removeEventListener('testPassiveEventSupport', noop, options);\n  return passive;\n};function autoInc(seed) {\n  if (seed === void 0) {\n    seed = 0;\n  }\n\n  return function () {\n    return ++seed;\n  };\n}\n\nvar uid = autoInc();var passiveEventSupport;\nvar handlersMap = {};\nvar enabledInstances = {};\nvar touchEvents = ['touchstart', 'touchmove'];\nvar IGNORE_CLASS_NAME = 'ignore-react-onclickoutside';\n/**\n * Options for addEventHandler and removeEventHandler\n */\n\nfunction getEventHandlerOptions(instance, eventName) {\n  var handlerOptions = {};\n  var isTouchEvent = touchEvents.indexOf(eventName) !== -1;\n\n  if (isTouchEvent && passiveEventSupport) {\n    handlerOptions.passive = !instance.props.preventDefault;\n  }\n\n  return handlerOptions;\n}\n/**\n * This function generates the HOC function that you'll use\n * in order to impart onOutsideClick listening to an\n * arbitrary component. It gets called at the end of the\n * bootstrapping code to yield an instance of the\n * onClickOutsideHOC function defined inside setupHOC().\n */\n\n\nfunction onClickOutsideHOC(WrappedComponent, config) {\n  var _class, _temp;\n\n  var componentName = WrappedComponent.displayName || WrappedComponent.name || 'Component';\n  return _temp = _class = /*#__PURE__*/function (_Component) {\n    _inheritsLoose(onClickOutside, _Component);\n\n    function onClickOutside(props) {\n      var _this;\n\n      _this = _Component.call(this, props) || this;\n\n      _this.__outsideClickHandler = function (event) {\n        if (typeof _this.__clickOutsideHandlerProp === 'function') {\n          _this.__clickOutsideHandlerProp(event);\n\n          return;\n        }\n\n        var instance = _this.getInstance();\n\n        if (typeof instance.props.handleClickOutside === 'function') {\n          instance.props.handleClickOutside(event);\n          return;\n        }\n\n        if (typeof instance.handleClickOutside === 'function') {\n          instance.handleClickOutside(event);\n          return;\n        }\n\n        throw new Error(\"WrappedComponent: \" + componentName + \" lacks a handleClickOutside(event) function for processing outside click events.\");\n      };\n\n      _this.__getComponentNode = function () {\n        var instance = _this.getInstance();\n\n        if (config && typeof config.setClickOutsideRef === 'function') {\n          return config.setClickOutsideRef()(instance);\n        }\n\n        if (typeof instance.setClickOutsideRef === 'function') {\n          return instance.setClickOutsideRef();\n        }\n\n        return (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.findDOMNode)(instance);\n      };\n\n      _this.enableOnClickOutside = function () {\n        if (typeof document === 'undefined' || enabledInstances[_this._uid]) {\n          return;\n        }\n\n        if (typeof passiveEventSupport === 'undefined') {\n          passiveEventSupport = testPassiveEventSupport();\n        }\n\n        enabledInstances[_this._uid] = true;\n        var events = _this.props.eventTypes;\n\n        if (!events.forEach) {\n          events = [events];\n        }\n\n        handlersMap[_this._uid] = function (event) {\n          if (_this.componentNode === null) return;\n          if (_this.initTimeStamp > event.timeStamp) return;\n\n          if (_this.props.preventDefault) {\n            event.preventDefault();\n          }\n\n          if (_this.props.stopPropagation) {\n            event.stopPropagation();\n          }\n\n          if (_this.props.excludeScrollbar && clickedScrollbar(event)) return;\n          var current = event.composed && event.composedPath && event.composedPath().shift() || event.target;\n\n          if (findHighest(current, _this.componentNode, _this.props.outsideClickIgnoreClass) !== document) {\n            return;\n          }\n\n          _this.__outsideClickHandler(event);\n        };\n\n        events.forEach(function (eventName) {\n          document.addEventListener(eventName, handlersMap[_this._uid], getEventHandlerOptions(_assertThisInitialized(_this), eventName));\n        });\n      };\n\n      _this.disableOnClickOutside = function () {\n        delete enabledInstances[_this._uid];\n        var fn = handlersMap[_this._uid];\n\n        if (fn && typeof document !== 'undefined') {\n          var events = _this.props.eventTypes;\n\n          if (!events.forEach) {\n            events = [events];\n          }\n\n          events.forEach(function (eventName) {\n            return document.removeEventListener(eventName, fn, getEventHandlerOptions(_assertThisInitialized(_this), eventName));\n          });\n          delete handlersMap[_this._uid];\n        }\n      };\n\n      _this.getRef = function (ref) {\n        return _this.instanceRef = ref;\n      };\n\n      _this._uid = uid();\n      _this.initTimeStamp = performance.now();\n      return _this;\n    }\n    /**\n     * Access the WrappedComponent's instance.\n     */\n\n\n    var _proto = onClickOutside.prototype;\n\n    _proto.getInstance = function getInstance() {\n      if (WrappedComponent.prototype && !WrappedComponent.prototype.isReactComponent) {\n        return this;\n      }\n\n      var ref = this.instanceRef;\n      return ref.getInstance ? ref.getInstance() : ref;\n    };\n\n    /**\n     * Add click listeners to the current document,\n     * linked to this component's state.\n     */\n    _proto.componentDidMount = function componentDidMount() {\n      // If we are in an environment without a DOM such\n      // as shallow rendering or snapshots then we exit\n      // early to prevent any unhandled errors being thrown.\n      if (typeof document === 'undefined' || !document.createElement) {\n        return;\n      }\n\n      var instance = this.getInstance();\n\n      if (config && typeof config.handleClickOutside === 'function') {\n        this.__clickOutsideHandlerProp = config.handleClickOutside(instance);\n\n        if (typeof this.__clickOutsideHandlerProp !== 'function') {\n          throw new Error(\"WrappedComponent: \" + componentName + \" lacks a function for processing outside click events specified by the handleClickOutside config option.\");\n        }\n      }\n\n      this.componentNode = this.__getComponentNode(); // return early so we dont initiate onClickOutside\n\n      if (this.props.disableOnClickOutside) return;\n      this.enableOnClickOutside();\n    };\n\n    _proto.componentDidUpdate = function componentDidUpdate() {\n      this.componentNode = this.__getComponentNode();\n    }\n    /**\n     * Remove all document's event listeners for this component\n     */\n    ;\n\n    _proto.componentWillUnmount = function componentWillUnmount() {\n      this.disableOnClickOutside();\n    }\n    /**\n     * Can be called to explicitly enable event listening\n     * for clicks and touches outside of this element.\n     */\n    ;\n\n    /**\n     * Pass-through render\n     */\n    _proto.render = function render() {\n      // eslint-disable-next-line no-unused-vars\n      var _this$props = this.props;\n          _this$props.excludeScrollbar;\n          var props = _objectWithoutPropertiesLoose(_this$props, [\"excludeScrollbar\"]);\n\n      if (WrappedComponent.prototype && WrappedComponent.prototype.isReactComponent) {\n        props.ref = this.getRef;\n      } else {\n        props.wrappedRef = this.getRef;\n      }\n\n      props.disableOnClickOutside = this.disableOnClickOutside;\n      props.enableOnClickOutside = this.enableOnClickOutside;\n      return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(WrappedComponent, props);\n    };\n\n    return onClickOutside;\n  }(react__WEBPACK_IMPORTED_MODULE_0__.Component), _class.displayName = \"OnClickOutside(\" + componentName + \")\", _class.defaultProps = {\n    eventTypes: ['mousedown', 'touchstart'],\n    excludeScrollbar: config && config.excludeScrollbar || false,\n    outsideClickIgnoreClass: IGNORE_CLASS_NAME,\n    preventDefault: false,\n    stopPropagation: false\n  }, _class.getClass = function () {\n    return WrappedComponent.getClass ? WrappedComponent.getClass() : WrappedComponent;\n  }, _temp;\n}/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (onClickOutsideHOC);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-onclickoutside/dist/react-onclickoutside.es.js\n");

/***/ })

};
;