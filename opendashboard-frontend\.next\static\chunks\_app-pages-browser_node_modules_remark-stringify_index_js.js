"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_remark-stringify_index_js"],{

/***/ "(app-pages-browser)/./node_modules/decode-named-character-reference/index.dom.js":
/*!********************************************************************!*\
  !*** ./node_modules/decode-named-character-reference/index.dom.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decodeNamedCharacterReference: function() { return /* binding */ decodeNamedCharacterReference; }\n/* harmony export */ });\n/// <reference lib=\"dom\" />\n\n/* eslint-env browser */\n\nconst element = document.createElement('i')\n\n/**\n * @param {string} value\n * @returns {string | false}\n */\nfunction decodeNamedCharacterReference(value) {\n  const characterReference = '&' + value + ';'\n  element.innerHTML = characterReference\n  const character = element.textContent\n\n  // Some named character references do not require the closing semicolon\n  // (`&not`, for instance), which leads to situations where parsing the assumed\n  // named reference of `&notit;` will result in the string `¬it;`.\n  // When we encounter a trailing semicolon after parsing, and the character\n  // reference to decode was not a semicolon (`&semi;`), we can assume that the\n  // matching was not complete.\n  if (\n    // @ts-expect-error: TypeScript is wrong that `textContent` on elements can\n    // yield `null`.\n    character.charCodeAt(character.length - 1) === 59 /* `;` */ &&\n    value !== 'semi'\n  ) {\n    return false\n  }\n\n  // If the decoded string is equal to the input, the character reference was\n  // not valid.\n  // @ts-expect-error: TypeScript is wrong that `textContent` on elements can\n  // yield `null`.\n  return character === characterReference ? false : character\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/decode-named-character-reference/index.dom.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/longest-streak/index.js":
/*!**********************************************!*\
  !*** ./node_modules/longest-streak/index.js ***!
  \**********************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   longestStreak: function() { return /* binding */ longestStreak; }\n/* harmony export */ });\n/**\n * Get the count of the longest repeating streak of `substring` in `value`.\n *\n * @param {string} value\n *   Content to search in.\n * @param {string} substring\n *   Substring to look for, typically one character.\n * @returns {number}\n *   Count of most frequent adjacent `substring`s in `value`.\n */\nfunction longestStreak(value, substring) {\n  const source = String(value)\n  let index = source.indexOf(substring)\n  let expected = index\n  let count = 0\n  let max = 0\n\n  if (typeof substring !== 'string') {\n    throw new TypeError('Expected substring')\n  }\n\n  while (index !== -1) {\n    if (index === expected) {\n      if (++count > max) {\n        max = count\n      }\n    } else {\n      count = 1\n    }\n\n    expected = index + substring.length\n    index = source.indexOf(substring, expected)\n  }\n\n  return max\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sb25nZXN0LXN0cmVhay9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbG9uZ2VzdC1zdHJlYWsvaW5kZXguanM/ZThlZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEdldCB0aGUgY291bnQgb2YgdGhlIGxvbmdlc3QgcmVwZWF0aW5nIHN0cmVhayBvZiBgc3Vic3RyaW5nYCBpbiBgdmFsdWVgLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSB2YWx1ZVxuICogICBDb250ZW50IHRvIHNlYXJjaCBpbi5cbiAqIEBwYXJhbSB7c3RyaW5nfSBzdWJzdHJpbmdcbiAqICAgU3Vic3RyaW5nIHRvIGxvb2sgZm9yLCB0eXBpY2FsbHkgb25lIGNoYXJhY3Rlci5cbiAqIEByZXR1cm5zIHtudW1iZXJ9XG4gKiAgIENvdW50IG9mIG1vc3QgZnJlcXVlbnQgYWRqYWNlbnQgYHN1YnN0cmluZ2BzIGluIGB2YWx1ZWAuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBsb25nZXN0U3RyZWFrKHZhbHVlLCBzdWJzdHJpbmcpIHtcbiAgY29uc3Qgc291cmNlID0gU3RyaW5nKHZhbHVlKVxuICBsZXQgaW5kZXggPSBzb3VyY2UuaW5kZXhPZihzdWJzdHJpbmcpXG4gIGxldCBleHBlY3RlZCA9IGluZGV4XG4gIGxldCBjb3VudCA9IDBcbiAgbGV0IG1heCA9IDBcblxuICBpZiAodHlwZW9mIHN1YnN0cmluZyAhPT0gJ3N0cmluZycpIHtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdFeHBlY3RlZCBzdWJzdHJpbmcnKVxuICB9XG5cbiAgd2hpbGUgKGluZGV4ICE9PSAtMSkge1xuICAgIGlmIChpbmRleCA9PT0gZXhwZWN0ZWQpIHtcbiAgICAgIGlmICgrK2NvdW50ID4gbWF4KSB7XG4gICAgICAgIG1heCA9IGNvdW50XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIGNvdW50ID0gMVxuICAgIH1cblxuICAgIGV4cGVjdGVkID0gaW5kZXggKyBzdWJzdHJpbmcubGVuZ3RoXG4gICAgaW5kZXggPSBzb3VyY2UuaW5kZXhPZihzdWJzdHJpbmcsIGV4cGVjdGVkKVxuICB9XG5cbiAgcmV0dXJuIG1heFxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/longest-streak/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-phrasing/lib/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/mdast-util-phrasing/lib/index.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   phrasing: function() { return /* binding */ phrasing; }\n/* harmony export */ });\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-is */ \"(app-pages-browser)/./node_modules/mdast-util-phrasing/node_modules/unist-util-is/lib/index.js\");\n/**\n * @typedef {import('mdast').PhrasingContent} PhrasingContent\n * @typedef {import('unist-util-is').AssertPredicate<PhrasingContent>} AssertPredicatePhrasing\n */\n\n\n\n/**\n * Check if the given value is *phrasing content*.\n *\n * @param\n *   Thing to check, typically `Node`.\n * @returns\n *   Whether `value` is phrasing content.\n */\nconst phrasing = /** @type {AssertPredicatePhrasing} */ (\n  (0,unist_util_is__WEBPACK_IMPORTED_MODULE_0__.convert)([\n    'break',\n    'delete',\n    'emphasis',\n    'footnote',\n    'footnoteReference',\n    'image',\n    'imageReference',\n    'inlineCode',\n    'link',\n    'linkReference',\n    'strong',\n    'text'\n  ])\n)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXBocmFzaW5nL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSxpQ0FBaUM7QUFDOUMsYUFBYSwwREFBMEQ7QUFDdkU7O0FBRXFDOztBQUVyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ08sNEJBQTRCLHlCQUF5QjtBQUM1RCxFQUFFLHNEQUFPO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC1waHJhc2luZy9saWIvaW5kZXguanM/MmM3YiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuUGhyYXNpbmdDb250ZW50fSBQaHJhc2luZ0NvbnRlbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ3VuaXN0LXV0aWwtaXMnKS5Bc3NlcnRQcmVkaWNhdGU8UGhyYXNpbmdDb250ZW50Pn0gQXNzZXJ0UHJlZGljYXRlUGhyYXNpbmdcbiAqL1xuXG5pbXBvcnQge2NvbnZlcnR9IGZyb20gJ3VuaXN0LXV0aWwtaXMnXG5cbi8qKlxuICogQ2hlY2sgaWYgdGhlIGdpdmVuIHZhbHVlIGlzICpwaHJhc2luZyBjb250ZW50Ki5cbiAqXG4gKiBAcGFyYW1cbiAqICAgVGhpbmcgdG8gY2hlY2ssIHR5cGljYWxseSBgTm9kZWAuXG4gKiBAcmV0dXJuc1xuICogICBXaGV0aGVyIGB2YWx1ZWAgaXMgcGhyYXNpbmcgY29udGVudC5cbiAqL1xuZXhwb3J0IGNvbnN0IHBocmFzaW5nID0gLyoqIEB0eXBlIHtBc3NlcnRQcmVkaWNhdGVQaHJhc2luZ30gKi8gKFxuICBjb252ZXJ0KFtcbiAgICAnYnJlYWsnLFxuICAgICdkZWxldGUnLFxuICAgICdlbXBoYXNpcycsXG4gICAgJ2Zvb3Rub3RlJyxcbiAgICAnZm9vdG5vdGVSZWZlcmVuY2UnLFxuICAgICdpbWFnZScsXG4gICAgJ2ltYWdlUmVmZXJlbmNlJyxcbiAgICAnaW5saW5lQ29kZScsXG4gICAgJ2xpbmsnLFxuICAgICdsaW5rUmVmZXJlbmNlJyxcbiAgICAnc3Ryb25nJyxcbiAgICAndGV4dCdcbiAgXSlcbilcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-phrasing/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-phrasing/node_modules/unist-util-is/lib/index.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/mdast-util-phrasing/node_modules/unist-util-is/lib/index.js ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convert: function() { return /* binding */ convert; },\n/* harmony export */   is: function() { return /* binding */ is; }\n/* harmony export */ });\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n */\n\n/**\n * @typedef {Record<string, unknown>} Props\n * @typedef {null | undefined | string | Props | TestFunctionAnything | Array<string | Props | TestFunctionAnything>} Test\n *   Check for an arbitrary node, unaware of TypeScript inferral.\n *\n * @callback TestFunctionAnything\n *   Check if a node passes a test, unaware of TypeScript inferral.\n * @param {unknown} this\n *   The given context.\n * @param {Node} node\n *   A node.\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean | void}\n *   Whether this node passes the test.\n */\n\n/**\n * @template {Node} Kind\n *   Node type.\n * @typedef {Kind['type'] | Partial<Kind> | TestFunctionPredicate<Kind> | Array<Kind['type'] | Partial<Kind> | TestFunctionPredicate<Kind>>} PredicateTest\n *   Check for a node that can be inferred by TypeScript.\n */\n\n/**\n * Check if a node passes a certain test.\n *\n * @template {Node} Kind\n *   Node type.\n * @callback TestFunctionPredicate\n *   Complex test function for a node that can be inferred by TypeScript.\n * @param {Node} node\n *   A node.\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {node is Kind}\n *   Whether this node passes the test.\n */\n\n/**\n * @callback AssertAnything\n *   Check that an arbitrary value is a node, unaware of TypeScript inferral.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is a node and passes a test.\n */\n\n/**\n * Check if a node is a node and passes a certain node test.\n *\n * @template {Node} Kind\n *   Node type.\n * @callback AssertPredicate\n *   Check that an arbitrary value is a specific node, aware of TypeScript.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {node is Kind}\n *   Whether this is a node and passes a test.\n */\n\n/**\n * Check if `node` is a `Node` and whether it passes the given test.\n *\n * @param node\n *   Thing to check, typically `Node`.\n * @param test\n *   A check for a specific node.\n * @param index\n *   The node’s position in its parent.\n * @param parent\n *   The node’s parent.\n * @returns\n *   Whether `node` is a node and passes a test.\n */\nconst is =\n  /**\n   * @type {(\n   *   (() => false) &\n   *   (<Kind extends Node = Node>(node: unknown, test: PredicateTest<Kind>, index: number, parent: Parent, context?: unknown) => node is Kind) &\n   *   (<Kind extends Node = Node>(node: unknown, test: PredicateTest<Kind>, index?: null | undefined, parent?: null | undefined, context?: unknown) => node is Kind) &\n   *   ((node: unknown, test: Test, index: number, parent: Parent, context?: unknown) => boolean) &\n   *   ((node: unknown, test?: Test, index?: null | undefined, parent?: null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [node]\n     * @param {Test} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parent | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function is(node, test, index, parent, context) {\n      const check = convert(test)\n\n      if (\n        index !== undefined &&\n        index !== null &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite index')\n      }\n\n      if (\n        parent !== undefined &&\n        parent !== null &&\n        (!is(parent) || !parent.children)\n      ) {\n        throw new Error('Expected parent node')\n      }\n\n      if (\n        (parent === undefined || parent === null) !==\n        (index === undefined || index === null)\n      ) {\n        throw new Error('Expected both parent and index')\n      }\n\n      // @ts-expect-error Looks like a node.\n      return node && node.type && typeof node.type === 'string'\n        ? Boolean(check.call(context, node, index, parent))\n        : false\n    }\n  )\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param test\n *   *   when nullish, checks if `node` is a `Node`.\n *   *   when `string`, works like passing `(node) => node.type === test`.\n *   *   when `function` checks if function passed the node is true.\n *   *   when `object`, checks that all keys in test are in node, and that they have (strictly) equal values.\n *   *   when `array`, checks if any one of the subtests pass.\n * @returns\n *   An assertion.\n */\nconst convert =\n  /**\n   * @type {(\n   *   (<Kind extends Node>(test: PredicateTest<Kind>) => AssertPredicate<Kind>) &\n   *   ((test?: Test) => AssertAnything)\n   * )}\n   */\n  (\n    /**\n     * @param {Test} [test]\n     * @returns {AssertAnything}\n     */\n    function (test) {\n      if (test === undefined || test === null) {\n        return ok\n      }\n\n      if (typeof test === 'string') {\n        return typeFactory(test)\n      }\n\n      if (typeof test === 'object') {\n        return Array.isArray(test) ? anyFactory(test) : propsFactory(test)\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      throw new Error('Expected function, string, or object as test')\n    }\n  )\n\n/**\n * @param {Array<string | Props | TestFunctionAnything>} tests\n * @returns {AssertAnything}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<AssertAnything>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convert(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].call(this, ...parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn an object into a test for a node with a certain fields.\n *\n * @param {Props} check\n * @returns {AssertAnything}\n */\nfunction propsFactory(check) {\n  return castFactory(all)\n\n  /**\n   * @param {Node} node\n   * @returns {boolean}\n   */\n  function all(node) {\n    /** @type {string} */\n    let key\n\n    for (key in check) {\n      // @ts-expect-error: hush, it sure works as an index.\n      if (node[key] !== check[key]) return false\n    }\n\n    return true\n  }\n}\n\n/**\n * Turn a string into a test for a node with a certain type.\n *\n * @param {string} check\n * @returns {AssertAnything}\n */\nfunction typeFactory(check) {\n  return castFactory(type)\n\n  /**\n   * @param {Node} node\n   */\n  function type(node) {\n    return node && node.type === check\n  }\n}\n\n/**\n * Turn a custom test into a test for a node that passes that test.\n *\n * @param {TestFunctionAnything} check\n * @returns {AssertAnything}\n */\nfunction castFactory(check) {\n  return assertion\n\n  /**\n   * @this {unknown}\n   * @param {unknown} node\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function assertion(node, ...parameters) {\n    return Boolean(\n      node &&\n        typeof node === 'object' &&\n        'type' in node &&\n        // @ts-expect-error: fine.\n        Boolean(check.call(this, node, ...parameters))\n    )\n  }\n}\n\nfunction ok() {\n  return true\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-phrasing/node_modules/unist-util-is/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/configure.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/configure.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   configure: function() { return /* binding */ configure; }\n/* harmony export */ });\n/**\n * @typedef {import('./types.js').Options} Options\n * @typedef {import('./types.js').State} State\n */\n\n/**\n * @param {State} base\n * @param {Options} extension\n * @returns {State}\n */\nfunction configure(base, extension) {\n  let index = -1\n  /** @type {keyof Options} */\n  let key\n\n  // First do subextensions.\n  if (extension.extensions) {\n    while (++index < extension.extensions.length) {\n      configure(base, extension.extensions[index])\n    }\n  }\n\n  for (key in extension) {\n    if (key === 'extensions') {\n      // Empty.\n    } else if (key === 'unsafe' || key === 'join') {\n      /* c8 ignore next 2 */\n      // @ts-expect-error: hush.\n      base[key] = [...(base[key] || []), ...(extension[key] || [])]\n    } else if (key === 'handlers') {\n      base[key] = Object.assign(base[key], extension[key] || {})\n    } else {\n      // @ts-expect-error: hush.\n      base.options[key] = extension[key]\n    }\n  }\n\n  return base\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9jb25maWd1cmUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSw0QkFBNEI7QUFDekM7O0FBRUE7QUFDQSxXQUFXLE9BQU87QUFDbEIsV0FBVyxTQUFTO0FBQ3BCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQSxhQUFhLGVBQWU7QUFDNUI7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ04sK0RBQStEO0FBQy9ELE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9jb25maWd1cmUuanM/MTA4MSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4vdHlwZXMuanMnKS5PcHRpb25zfSBPcHRpb25zXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL3R5cGVzLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBiYXNlXG4gKiBAcGFyYW0ge09wdGlvbnN9IGV4dGVuc2lvblxuICogQHJldHVybnMge1N0YXRlfVxuICovXG5leHBvcnQgZnVuY3Rpb24gY29uZmlndXJlKGJhc2UsIGV4dGVuc2lvbikge1xuICBsZXQgaW5kZXggPSAtMVxuICAvKiogQHR5cGUge2tleW9mIE9wdGlvbnN9ICovXG4gIGxldCBrZXlcblxuICAvLyBGaXJzdCBkbyBzdWJleHRlbnNpb25zLlxuICBpZiAoZXh0ZW5zaW9uLmV4dGVuc2lvbnMpIHtcbiAgICB3aGlsZSAoKytpbmRleCA8IGV4dGVuc2lvbi5leHRlbnNpb25zLmxlbmd0aCkge1xuICAgICAgY29uZmlndXJlKGJhc2UsIGV4dGVuc2lvbi5leHRlbnNpb25zW2luZGV4XSlcbiAgICB9XG4gIH1cblxuICBmb3IgKGtleSBpbiBleHRlbnNpb24pIHtcbiAgICBpZiAoa2V5ID09PSAnZXh0ZW5zaW9ucycpIHtcbiAgICAgIC8vIEVtcHR5LlxuICAgIH0gZWxzZSBpZiAoa2V5ID09PSAndW5zYWZlJyB8fCBrZXkgPT09ICdqb2luJykge1xuICAgICAgLyogYzggaWdub3JlIG5leHQgMiAqL1xuICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvcjogaHVzaC5cbiAgICAgIGJhc2Vba2V5XSA9IFsuLi4oYmFzZVtrZXldIHx8IFtdKSwgLi4uKGV4dGVuc2lvbltrZXldIHx8IFtdKV1cbiAgICB9IGVsc2UgaWYgKGtleSA9PT0gJ2hhbmRsZXJzJykge1xuICAgICAgYmFzZVtrZXldID0gT2JqZWN0LmFzc2lnbihiYXNlW2tleV0sIGV4dGVuc2lvbltrZXldIHx8IHt9KVxuICAgIH0gZWxzZSB7XG4gICAgICAvLyBAdHMtZXhwZWN0LWVycm9yOiBodXNoLlxuICAgICAgYmFzZS5vcHRpb25zW2tleV0gPSBleHRlbnNpb25ba2V5XVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiBiYXNlXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/configure.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blockquote: function() { return /* binding */ blockquote; }\n/* harmony export */ });\n/**\n * @typedef {import('mdast').Blockquote} Blockquote\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n * @typedef {import('../types.js').Map} Map\n */\n\n/**\n * @param {Blockquote} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction blockquote(node, _, state, info) {\n  const exit = state.enter('blockquote')\n  const tracker = state.createTracker(info)\n  tracker.move('> ')\n  tracker.shift(2)\n  const value = state.indentLines(\n    state.containerFlow(node, tracker.current()),\n    map\n  )\n  exit()\n  return value\n}\n\n/** @type {Map} */\nfunction map(line, _, blank) {\n  return '>' + (blank ? '' : ' ') + line\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvYmxvY2txdW90ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLDRCQUE0QjtBQUN6QyxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLDZCQUE2QjtBQUMxQyxhQUFhLDRCQUE0QjtBQUN6QyxhQUFhLDJCQUEyQjtBQUN4Qzs7QUFFQTtBQUNBLFdBQVcsWUFBWTtBQUN2QixXQUFXLG9CQUFvQjtBQUMvQixXQUFXLE9BQU87QUFDbEIsV0FBVyxNQUFNO0FBQ2pCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxXQUFXLEtBQUs7QUFDaEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvYmxvY2txdW90ZS5qcz8xOTBmIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5CbG9ja3F1b3RlfSBCbG9ja3F1b3RlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlBhcmVudH0gUGFyZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlN0YXRlfSBTdGF0ZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5JbmZvfSBJbmZvXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLk1hcH0gTWFwXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge0Jsb2NrcXVvdGV9IG5vZGVcbiAqIEBwYXJhbSB7UGFyZW50IHwgdW5kZWZpbmVkfSBfXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHBhcmFtIHtJbmZvfSBpbmZvXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gYmxvY2txdW90ZShub2RlLCBfLCBzdGF0ZSwgaW5mbykge1xuICBjb25zdCBleGl0ID0gc3RhdGUuZW50ZXIoJ2Jsb2NrcXVvdGUnKVxuICBjb25zdCB0cmFja2VyID0gc3RhdGUuY3JlYXRlVHJhY2tlcihpbmZvKVxuICB0cmFja2VyLm1vdmUoJz4gJylcbiAgdHJhY2tlci5zaGlmdCgyKVxuICBjb25zdCB2YWx1ZSA9IHN0YXRlLmluZGVudExpbmVzKFxuICAgIHN0YXRlLmNvbnRhaW5lckZsb3cobm9kZSwgdHJhY2tlci5jdXJyZW50KCkpLFxuICAgIG1hcFxuICApXG4gIGV4aXQoKVxuICByZXR1cm4gdmFsdWVcbn1cblxuLyoqIEB0eXBlIHtNYXB9ICovXG5mdW5jdGlvbiBtYXAobGluZSwgXywgYmxhbmspIHtcbiAgcmV0dXJuICc+JyArIChibGFuayA/ICcnIDogJyAnKSArIGxpbmVcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/break.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/break.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hardBreak: function() { return /* binding */ hardBreak; }\n/* harmony export */ });\n/* harmony import */ var _util_pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/pattern-in-scope.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js\");\n/**\n * @typedef {import('mdast').Break} Break\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n\n\n/**\n * @param {Break} _\n * @param {Parent | undefined} _1\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction hardBreak(_, _1, state, info) {\n  let index = -1\n\n  while (++index < state.unsafe.length) {\n    // If we can’t put eols in this construct (setext headings, tables), use a\n    // space instead.\n    if (\n      state.unsafe[index].character === '\\n' &&\n      (0,_util_pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__.patternInScope)(state.stack, state.unsafe[index])\n    ) {\n      return /[ \\t]/.test(info.before) ? '' : ' '\n    }\n  }\n\n  return '\\\\\\n'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvYnJlYWsuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEsdUJBQXVCO0FBQ3BDLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsNkJBQTZCO0FBQzFDLGFBQWEsNEJBQTRCO0FBQ3pDOztBQUUwRDs7QUFFMUQ7QUFDQSxXQUFXLE9BQU87QUFDbEIsV0FBVyxvQkFBb0I7QUFDL0IsV0FBVyxPQUFPO0FBQ2xCLFdBQVcsTUFBTTtBQUNqQixhQUFhO0FBQ2I7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLHlFQUFjO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL2hhbmRsZS9icmVhay5qcz8zNTg0Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5CcmVha30gQnJlYWtcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuUGFyZW50fSBQYXJlbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuU3RhdGV9IFN0YXRlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkluZm99IEluZm9cbiAqL1xuXG5pbXBvcnQge3BhdHRlcm5JblNjb3BlfSBmcm9tICcuLi91dGlsL3BhdHRlcm4taW4tc2NvcGUuanMnXG5cbi8qKlxuICogQHBhcmFtIHtCcmVha30gX1xuICogQHBhcmFtIHtQYXJlbnQgfCB1bmRlZmluZWR9IF8xXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHBhcmFtIHtJbmZvfSBpbmZvXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gaGFyZEJyZWFrKF8sIF8xLCBzdGF0ZSwgaW5mbykge1xuICBsZXQgaW5kZXggPSAtMVxuXG4gIHdoaWxlICgrK2luZGV4IDwgc3RhdGUudW5zYWZlLmxlbmd0aCkge1xuICAgIC8vIElmIHdlIGNhbuKAmXQgcHV0IGVvbHMgaW4gdGhpcyBjb25zdHJ1Y3QgKHNldGV4dCBoZWFkaW5ncywgdGFibGVzKSwgdXNlIGFcbiAgICAvLyBzcGFjZSBpbnN0ZWFkLlxuICAgIGlmIChcbiAgICAgIHN0YXRlLnVuc2FmZVtpbmRleF0uY2hhcmFjdGVyID09PSAnXFxuJyAmJlxuICAgICAgcGF0dGVybkluU2NvcGUoc3RhdGUuc3RhY2ssIHN0YXRlLnVuc2FmZVtpbmRleF0pXG4gICAgKSB7XG4gICAgICByZXR1cm4gL1sgXFx0XS8udGVzdChpbmZvLmJlZm9yZSkgPyAnJyA6ICcgJ1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiAnXFxcXFxcbidcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/break.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/code.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/code.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   code: function() { return /* binding */ code; }\n/* harmony export */ });\n/* harmony import */ var longest_streak__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! longest-streak */ \"(app-pages-browser)/./node_modules/longest-streak/index.js\");\n/* harmony import */ var _util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/format-code-as-indented.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js\");\n/* harmony import */ var _util_check_fence_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-fence.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js\");\n/**\n * @typedef {import('mdast').Code} Code\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n * @typedef {import('../types.js').Map} Map\n */\n\n\n\n\n\n/**\n * @param {Code} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction code(node, _, state, info) {\n  const marker = (0,_util_check_fence_js__WEBPACK_IMPORTED_MODULE_0__.checkFence)(state)\n  const raw = node.value || ''\n  const suffix = marker === '`' ? 'GraveAccent' : 'Tilde'\n\n  if ((0,_util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_1__.formatCodeAsIndented)(node, state)) {\n    const exit = state.enter('codeIndented')\n    const value = state.indentLines(raw, map)\n    exit()\n    return value\n  }\n\n  const tracker = state.createTracker(info)\n  const sequence = marker.repeat(Math.max((0,longest_streak__WEBPACK_IMPORTED_MODULE_2__.longestStreak)(raw, marker) + 1, 3))\n  const exit = state.enter('codeFenced')\n  let value = tracker.move(sequence)\n\n  if (node.lang) {\n    const subexit = state.enter(`codeFencedLang${suffix}`)\n    value += tracker.move(\n      state.safe(node.lang, {\n        before: value,\n        after: ' ',\n        encode: ['`'],\n        ...tracker.current()\n      })\n    )\n    subexit()\n  }\n\n  if (node.lang && node.meta) {\n    const subexit = state.enter(`codeFencedMeta${suffix}`)\n    value += tracker.move(' ')\n    value += tracker.move(\n      state.safe(node.meta, {\n        before: value,\n        after: '\\n',\n        encode: ['`'],\n        ...tracker.current()\n      })\n    )\n    subexit()\n  }\n\n  value += tracker.move('\\n')\n\n  if (raw) {\n    value += tracker.move(raw + '\\n')\n  }\n\n  value += tracker.move(sequence)\n  exit()\n  return value\n}\n\n/** @type {Map} */\nfunction map(line, _, blank) {\n  return (blank ? '' : '    ') + line\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/code.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/definition.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   definition: function() { return /* binding */ definition; }\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/**\n * @typedef {import('mdast').Definition} Definition\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n\n\n/**\n * @param {Definition} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction definition(node, _, state, info) {\n  const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const exit = state.enter('definition')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('[')\n  value += tracker.move(\n    state.safe(state.associationId(node), {\n      before: value,\n      after: ']',\n      ...tracker.current()\n    })\n  )\n  value += tracker.move(']: ')\n\n  subexit()\n\n  if (\n    // If there’s no url, or…\n    !node.url ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : '\\n',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  exit()\n\n  return value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   emphasis: function() { return /* binding */ emphasis; }\n/* harmony export */ });\n/* harmony import */ var _util_check_emphasis_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-emphasis.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js\");\n/**\n * @typedef {import('mdast').Emphasis} Emphasis\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n\n\nemphasis.peek = emphasisPeek\n\n// To do: there are cases where emphasis cannot “form” depending on the\n// previous or next character of sequences.\n// There’s no way around that though, except for injecting zero-width stuff.\n// Do we need to safeguard against that?\n/**\n * @param {Emphasis} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction emphasis(node, _, state, info) {\n  const marker = (0,_util_check_emphasis_js__WEBPACK_IMPORTED_MODULE_0__.checkEmphasis)(state)\n  const exit = state.enter('emphasis')\n  const tracker = state.createTracker(info)\n  let value = tracker.move(marker)\n  value += tracker.move(\n    state.containerPhrasing(node, {\n      before: value,\n      after: marker,\n      ...tracker.current()\n    })\n  )\n  value += tracker.move(marker)\n  exit()\n  return value\n}\n\n/**\n * @param {Emphasis} _\n * @param {Parent | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nfunction emphasisPeek(_, _1, state) {\n  return state.options.emphasis || '*'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/heading.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   heading: function() { return /* binding */ heading; }\n/* harmony export */ });\n/* harmony import */ var _util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/format-heading-as-setext.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js\");\n/**\n * @typedef {import('mdast').Heading} Heading\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n\n\n/**\n * @param {Heading} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction heading(node, _, state, info) {\n  const rank = Math.max(Math.min(6, node.depth || 1), 1)\n  const tracker = state.createTracker(info)\n\n  if ((0,_util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_0__.formatHeadingAsSetext)(node, state)) {\n    const exit = state.enter('headingSetext')\n    const subexit = state.enter('phrasing')\n    const value = state.containerPhrasing(node, {\n      ...tracker.current(),\n      before: '\\n',\n      after: '\\n'\n    })\n    subexit()\n    exit()\n\n    return (\n      value +\n      '\\n' +\n      (rank === 1 ? '=' : '-').repeat(\n        // The whole size…\n        value.length -\n          // Minus the position of the character after the last EOL (or\n          // 0 if there is none)…\n          (Math.max(value.lastIndexOf('\\r'), value.lastIndexOf('\\n')) + 1)\n      )\n    )\n  }\n\n  const sequence = '#'.repeat(rank)\n  const exit = state.enter('headingAtx')\n  const subexit = state.enter('phrasing')\n\n  // Note: for proper tracking, we should reset the output positions when there\n  // is no content returned, because then the space is not output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  tracker.move(sequence + ' ')\n\n  let value = state.containerPhrasing(node, {\n    before: '# ',\n    after: '\\n',\n    ...tracker.current()\n  })\n\n  if (/^[\\t ]/.test(value)) {\n    // To do: what effect has the character reference on tracking?\n    value =\n      '&#x' +\n      value.charCodeAt(0).toString(16).toUpperCase() +\n      ';' +\n      value.slice(1)\n  }\n\n  value = value ? sequence + ' ' + value : sequence\n\n  if (state.options.closeAtx) {\n    value += ' ' + sequence\n  }\n\n  subexit()\n  exit()\n\n  return value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/html.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/html.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   html: function() { return /* binding */ html; }\n/* harmony export */ });\n/**\n * @typedef {import('mdast').HTML} HTML\n */\n\nhtml.peek = htmlPeek\n\n/**\n * @param {HTML} node\n * @returns {string}\n */\nfunction html(node) {\n  return node.value || ''\n}\n\n/**\n * @returns {string}\n */\nfunction htmlPeek() {\n  return '<'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvaHRtbC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLHNCQUFzQjtBQUNuQzs7QUFFQTs7QUFFQTtBQUNBLFdBQVcsTUFBTTtBQUNqQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0E7O0FBRUE7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL2hhbmRsZS9odG1sLmpzP2E3YmYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkhUTUx9IEhUTUxcbiAqL1xuXG5odG1sLnBlZWsgPSBodG1sUGVla1xuXG4vKipcbiAqIEBwYXJhbSB7SFRNTH0gbm9kZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGh0bWwobm9kZSkge1xuICByZXR1cm4gbm9kZS52YWx1ZSB8fCAnJ1xufVxuXG4vKipcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmZ1bmN0aW9uIGh0bWxQZWVrKCkge1xuICByZXR1cm4gJzwnXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/html.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js":
/*!***************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   imageReference: function() { return /* binding */ imageReference; }\n/* harmony export */ });\n/**\n * @typedef {import('mdast').ImageReference} ImageReference\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\nimageReference.peek = imageReferencePeek\n\n/**\n * @param {ImageReference} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction imageReference(node, _, state, info) {\n  const type = node.referenceType\n  const exit = state.enter('imageReference')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('![')\n  const alt = state.safe(node.alt, {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  value += tracker.move(alt + '][')\n\n  subexit()\n  // Hide the fact that we’re in phrasing, because escapes don’t work.\n  const stack = state.stack\n  state.stack = []\n  subexit = state.enter('reference')\n  // Note: for proper tracking, we should reset the output positions when we end\n  // up making a `shortcut` reference, because then there is no brace output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  const reference = state.safe(state.associationId(node), {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  subexit()\n  state.stack = stack\n  exit()\n\n  if (type === 'full' || !alt || alt !== reference) {\n    value += tracker.move(reference + ']')\n  } else if (type === 'shortcut') {\n    // Remove the unwanted `[`.\n    value = value.slice(0, -1)\n  } else {\n    value += tracker.move(']')\n  }\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction imageReferencePeek() {\n  return '!'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/image.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/image.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   image: function() { return /* binding */ image; }\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/**\n * @typedef {import('mdast').Image} Image\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n\n\nimage.peek = imagePeek\n\n/**\n * @param {Image} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction image(node, _, state, info) {\n  const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const exit = state.enter('image')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('![')\n  value += tracker.move(\n    state.safe(node.alt, {before: value, after: ']', ...tracker.current()})\n  )\n  value += tracker.move('](')\n\n  subexit()\n\n  if (\n    // If there’s no url but there is a title…\n    (!node.url && node.title) ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : ')',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  value += tracker.move(')')\n  exit()\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction imagePeek() {\n  return '!'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/image.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/index.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handle: function() { return /* binding */ handle; }\n/* harmony export */ });\n/* harmony import */ var _blockquote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./blockquote.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js\");\n/* harmony import */ var _break_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./break.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/break.js\");\n/* harmony import */ var _code_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./code.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/code.js\");\n/* harmony import */ var _definition_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./definition.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js\");\n/* harmony import */ var _emphasis_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./emphasis.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js\");\n/* harmony import */ var _heading_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./heading.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js\");\n/* harmony import */ var _html_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./html.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/html.js\");\n/* harmony import */ var _image_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./image.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/image.js\");\n/* harmony import */ var _image_reference_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./image-reference.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js\");\n/* harmony import */ var _inline_code_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./inline-code.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js\");\n/* harmony import */ var _link_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./link.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/link.js\");\n/* harmony import */ var _link_reference_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./link-reference.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js\");\n/* harmony import */ var _list_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./list.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/list.js\");\n/* harmony import */ var _list_item_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./list-item.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js\");\n/* harmony import */ var _paragraph_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./paragraph.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./root.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/root.js\");\n/* harmony import */ var _strong_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./strong.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./text.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/text.js\");\n/* harmony import */ var _thematic_break_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./thematic-break.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Default (CommonMark) handlers.\n */\nconst handle = {\n  blockquote: _blockquote_js__WEBPACK_IMPORTED_MODULE_0__.blockquote,\n  break: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,\n  code: _code_js__WEBPACK_IMPORTED_MODULE_2__.code,\n  definition: _definition_js__WEBPACK_IMPORTED_MODULE_3__.definition,\n  emphasis: _emphasis_js__WEBPACK_IMPORTED_MODULE_4__.emphasis,\n  hardBreak: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,\n  heading: _heading_js__WEBPACK_IMPORTED_MODULE_5__.heading,\n  html: _html_js__WEBPACK_IMPORTED_MODULE_6__.html,\n  image: _image_js__WEBPACK_IMPORTED_MODULE_7__.image,\n  imageReference: _image_reference_js__WEBPACK_IMPORTED_MODULE_8__.imageReference,\n  inlineCode: _inline_code_js__WEBPACK_IMPORTED_MODULE_9__.inlineCode,\n  link: _link_js__WEBPACK_IMPORTED_MODULE_10__.link,\n  linkReference: _link_reference_js__WEBPACK_IMPORTED_MODULE_11__.linkReference,\n  list: _list_js__WEBPACK_IMPORTED_MODULE_12__.list,\n  listItem: _list_item_js__WEBPACK_IMPORTED_MODULE_13__.listItem,\n  paragraph: _paragraph_js__WEBPACK_IMPORTED_MODULE_14__.paragraph,\n  root: _root_js__WEBPACK_IMPORTED_MODULE_15__.root,\n  strong: _strong_js__WEBPACK_IMPORTED_MODULE_16__.strong,\n  text: _text_js__WEBPACK_IMPORTED_MODULE_17__.text,\n  thematicBreak: _thematic_break_js__WEBPACK_IMPORTED_MODULE_18__.thematicBreak\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js":
/*!***********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inlineCode: function() { return /* binding */ inlineCode; }\n/* harmony export */ });\n/* harmony import */ var _util_pattern_compile_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/pattern-compile.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/pattern-compile.js\");\n/**\n * @typedef {import('mdast').InlineCode} InlineCode\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n */\n\n\n\ninlineCode.peek = inlineCodePeek\n\n/**\n * @param {InlineCode} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @returns {string}\n */\nfunction inlineCode(node, _, state) {\n  let value = node.value || ''\n  let sequence = '`'\n  let index = -1\n\n  // If there is a single grave accent on its own in the code, use a fence of\n  // two.\n  // If there are two in a row, use one.\n  while (new RegExp('(^|[^`])' + sequence + '([^`]|$)').test(value)) {\n    sequence += '`'\n  }\n\n  // If this is not just spaces or eols (tabs don’t count), and either the\n  // first or last character are a space, eol, or tick, then pad with spaces.\n  if (\n    /[^ \\r\\n]/.test(value) &&\n    ((/^[ \\r\\n]/.test(value) && /[ \\r\\n]$/.test(value)) || /^`|`$/.test(value))\n  ) {\n    value = ' ' + value + ' '\n  }\n\n  // We have a potential problem: certain characters after eols could result in\n  // blocks being seen.\n  // For example, if someone injected the string `'\\n# b'`, then that would\n  // result in an ATX heading.\n  // We can’t escape characters in `inlineCode`, but because eols are\n  // transformed to spaces when going from markdown to HTML anyway, we can swap\n  // them out.\n  while (++index < state.unsafe.length) {\n    const pattern = state.unsafe[index]\n    const expression = (0,_util_pattern_compile_js__WEBPACK_IMPORTED_MODULE_0__.patternCompile)(pattern)\n    /** @type {RegExpExecArray | null} */\n    let match\n\n    // Only look for `atBreak`s.\n    // Btw: note that `atBreak` patterns will always start the regex at LF or\n    // CR.\n    if (!pattern.atBreak) continue\n\n    while ((match = expression.exec(value))) {\n      let position = match.index\n\n      // Support CRLF (patterns only look for one of the characters).\n      if (\n        value.charCodeAt(position) === 10 /* `\\n` */ &&\n        value.charCodeAt(position - 1) === 13 /* `\\r` */\n      ) {\n        position--\n      }\n\n      value = value.slice(0, position) + ' ' + value.slice(match.index + 1)\n    }\n  }\n\n  return sequence + value + sequence\n}\n\n/**\n * @returns {string}\n */\nfunction inlineCodePeek() {\n  return '`'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   linkReference: function() { return /* binding */ linkReference; }\n/* harmony export */ });\n/**\n * @typedef {import('mdast').LinkReference} LinkReference\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\nlinkReference.peek = linkReferencePeek\n\n/**\n * @param {LinkReference} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction linkReference(node, _, state, info) {\n  const type = node.referenceType\n  const exit = state.enter('linkReference')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('[')\n  const text = state.containerPhrasing(node, {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  value += tracker.move(text + '][')\n\n  subexit()\n  // Hide the fact that we’re in phrasing, because escapes don’t work.\n  const stack = state.stack\n  state.stack = []\n  subexit = state.enter('reference')\n  // Note: for proper tracking, we should reset the output positions when we end\n  // up making a `shortcut` reference, because then there is no brace output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  const reference = state.safe(state.associationId(node), {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  subexit()\n  state.stack = stack\n  exit()\n\n  if (type === 'full' || !text || text !== reference) {\n    value += tracker.move(reference + ']')\n  } else if (type === 'shortcut') {\n    // Remove the unwanted `[`.\n    value = value.slice(0, -1)\n  } else {\n    value += tracker.move(']')\n  }\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction linkReferencePeek() {\n  return '['\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvbGluay1yZWZlcmVuY2UuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSwrQkFBK0I7QUFDNUMsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSw2QkFBNkI7QUFDMUMsYUFBYSw0QkFBNEI7QUFDekM7O0FBRUE7O0FBRUE7QUFDQSxXQUFXLGVBQWU7QUFDMUIsV0FBVyxvQkFBb0I7QUFDL0IsV0FBVyxPQUFPO0FBQ2xCLFdBQVcsTUFBTTtBQUNqQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2xpbmstcmVmZXJlbmNlLmpzPzAxNjUiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkxpbmtSZWZlcmVuY2V9IExpbmtSZWZlcmVuY2VcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuUGFyZW50fSBQYXJlbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuU3RhdGV9IFN0YXRlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkluZm99IEluZm9cbiAqL1xuXG5saW5rUmVmZXJlbmNlLnBlZWsgPSBsaW5rUmVmZXJlbmNlUGVla1xuXG4vKipcbiAqIEBwYXJhbSB7TGlua1JlZmVyZW5jZX0gbm9kZVxuICogQHBhcmFtIHtQYXJlbnQgfCB1bmRlZmluZWR9IF9cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcGFyYW0ge0luZm99IGluZm9cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBsaW5rUmVmZXJlbmNlKG5vZGUsIF8sIHN0YXRlLCBpbmZvKSB7XG4gIGNvbnN0IHR5cGUgPSBub2RlLnJlZmVyZW5jZVR5cGVcbiAgY29uc3QgZXhpdCA9IHN0YXRlLmVudGVyKCdsaW5rUmVmZXJlbmNlJylcbiAgbGV0IHN1YmV4aXQgPSBzdGF0ZS5lbnRlcignbGFiZWwnKVxuICBjb25zdCB0cmFja2VyID0gc3RhdGUuY3JlYXRlVHJhY2tlcihpbmZvKVxuICBsZXQgdmFsdWUgPSB0cmFja2VyLm1vdmUoJ1snKVxuICBjb25zdCB0ZXh0ID0gc3RhdGUuY29udGFpbmVyUGhyYXNpbmcobm9kZSwge1xuICAgIGJlZm9yZTogdmFsdWUsXG4gICAgYWZ0ZXI6ICddJyxcbiAgICAuLi50cmFja2VyLmN1cnJlbnQoKVxuICB9KVxuICB2YWx1ZSArPSB0cmFja2VyLm1vdmUodGV4dCArICddWycpXG5cbiAgc3ViZXhpdCgpXG4gIC8vIEhpZGUgdGhlIGZhY3QgdGhhdCB3ZeKAmXJlIGluIHBocmFzaW5nLCBiZWNhdXNlIGVzY2FwZXMgZG9u4oCZdCB3b3JrLlxuICBjb25zdCBzdGFjayA9IHN0YXRlLnN0YWNrXG4gIHN0YXRlLnN0YWNrID0gW11cbiAgc3ViZXhpdCA9IHN0YXRlLmVudGVyKCdyZWZlcmVuY2UnKVxuICAvLyBOb3RlOiBmb3IgcHJvcGVyIHRyYWNraW5nLCB3ZSBzaG91bGQgcmVzZXQgdGhlIG91dHB1dCBwb3NpdGlvbnMgd2hlbiB3ZSBlbmRcbiAgLy8gdXAgbWFraW5nIGEgYHNob3J0Y3V0YCByZWZlcmVuY2UsIGJlY2F1c2UgdGhlbiB0aGVyZSBpcyBubyBicmFjZSBvdXRwdXQuXG4gIC8vIFByYWN0aWNhbGx5LCBpbiB0aGF0IGNhc2UsIHRoZXJlIGlzIG5vIGNvbnRlbnQsIHNvIGl0IGRvZXNu4oCZdCBtYXR0ZXIgdGhhdFxuICAvLyB3ZeKAmXZlIHRyYWNrZWQgb25lIHRvbyBtYW55IGNoYXJhY3RlcnMuXG4gIGNvbnN0IHJlZmVyZW5jZSA9IHN0YXRlLnNhZmUoc3RhdGUuYXNzb2NpYXRpb25JZChub2RlKSwge1xuICAgIGJlZm9yZTogdmFsdWUsXG4gICAgYWZ0ZXI6ICddJyxcbiAgICAuLi50cmFja2VyLmN1cnJlbnQoKVxuICB9KVxuICBzdWJleGl0KClcbiAgc3RhdGUuc3RhY2sgPSBzdGFja1xuICBleGl0KClcblxuICBpZiAodHlwZSA9PT0gJ2Z1bGwnIHx8ICF0ZXh0IHx8IHRleHQgIT09IHJlZmVyZW5jZSkge1xuICAgIHZhbHVlICs9IHRyYWNrZXIubW92ZShyZWZlcmVuY2UgKyAnXScpXG4gIH0gZWxzZSBpZiAodHlwZSA9PT0gJ3Nob3J0Y3V0Jykge1xuICAgIC8vIFJlbW92ZSB0aGUgdW53YW50ZWQgYFtgLlxuICAgIHZhbHVlID0gdmFsdWUuc2xpY2UoMCwgLTEpXG4gIH0gZWxzZSB7XG4gICAgdmFsdWUgKz0gdHJhY2tlci5tb3ZlKCddJylcbiAgfVxuXG4gIHJldHVybiB2YWx1ZVxufVxuXG4vKipcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmZ1bmN0aW9uIGxpbmtSZWZlcmVuY2VQZWVrKCkge1xuICByZXR1cm4gJ1snXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/link.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/link.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   link: function() { return /* binding */ link; }\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/* harmony import */ var _util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/format-link-as-autolink.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js\");\n/**\n * @typedef {import('mdast').Link} Link\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n * @typedef {import('../types.js').Exit} Exit\n */\n\n\n\n\nlink.peek = linkPeek\n\n/**\n * @param {Link} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction link(node, _, state, info) {\n  const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const tracker = state.createTracker(info)\n  /** @type {Exit} */\n  let exit\n  /** @type {Exit} */\n  let subexit\n\n  if ((0,_util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__.formatLinkAsAutolink)(node, state)) {\n    // Hide the fact that we’re in phrasing, because escapes don’t work.\n    const stack = state.stack\n    state.stack = []\n    exit = state.enter('autolink')\n    let value = tracker.move('<')\n    value += tracker.move(\n      state.containerPhrasing(node, {\n        before: value,\n        after: '>',\n        ...tracker.current()\n      })\n    )\n    value += tracker.move('>')\n    exit()\n    state.stack = stack\n    return value\n  }\n\n  exit = state.enter('link')\n  subexit = state.enter('label')\n  let value = tracker.move('[')\n  value += tracker.move(\n    state.containerPhrasing(node, {\n      before: value,\n      after: '](',\n      ...tracker.current()\n    })\n  )\n  value += tracker.move('](')\n  subexit()\n\n  if (\n    // If there’s no url but there is a title…\n    (!node.url && node.title) ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : ')',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  value += tracker.move(')')\n\n  exit()\n  return value\n}\n\n/**\n * @param {Link} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @returns {string}\n */\nfunction linkPeek(node, _, state) {\n  return (0,_util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__.formatLinkAsAutolink)(node, state) ? '<' : '['\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/list-item.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   listItem: function() { return /* binding */ listItem; }\n/* harmony export */ });\n/* harmony import */ var _util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-bullet.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/* harmony import */ var _util_check_list_item_indent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-list-item-indent.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js\");\n/**\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('../types.js').Map} Map\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n\n\n\n/**\n * @param {ListItem} node\n * @param {Parent | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction listItem(node, parent, state, info) {\n  const listItemIndent = (0,_util_check_list_item_indent_js__WEBPACK_IMPORTED_MODULE_0__.checkListItemIndent)(state)\n  let bullet = state.bulletCurrent || (0,_util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__.checkBullet)(state)\n\n  // Add the marker value for ordered lists.\n  if (parent && parent.type === 'list' && parent.ordered) {\n    bullet =\n      (typeof parent.start === 'number' && parent.start > -1\n        ? parent.start\n        : 1) +\n      (state.options.incrementListMarker === false\n        ? 0\n        : parent.children.indexOf(node)) +\n      bullet\n  }\n\n  let size = bullet.length + 1\n\n  if (\n    listItemIndent === 'tab' ||\n    (listItemIndent === 'mixed' &&\n      ((parent && parent.type === 'list' && parent.spread) || node.spread))\n  ) {\n    size = Math.ceil(size / 4) * 4\n  }\n\n  const tracker = state.createTracker(info)\n  tracker.move(bullet + ' '.repeat(size - bullet.length))\n  tracker.shift(size)\n  const exit = state.enter('listItem')\n  const value = state.indentLines(\n    state.containerFlow(node, tracker.current()),\n    map\n  )\n  exit()\n\n  return value\n\n  /** @type {Map} */\n  function map(line, index, blank) {\n    if (index) {\n      return (blank ? '' : ' '.repeat(size)) + line\n    }\n\n    return (blank ? bullet : bullet + ' '.repeat(size - bullet.length)) + line\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/list.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/list.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   list: function() { return /* binding */ list; }\n/* harmony export */ });\n/* harmony import */ var _util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-bullet.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/* harmony import */ var _util_check_bullet_other_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/check-bullet-other.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js\");\n/* harmony import */ var _util_check_bullet_ordered_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-bullet-ordered.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js\");\n/* harmony import */ var _util_check_bullet_ordered_other_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/check-bullet-ordered-other.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered-other.js\");\n/* harmony import */ var _util_check_rule_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/check-rule.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\");\n/**\n * @typedef {import('mdast').List} List\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n\n\n\n\n\n\n/**\n * @param {List} node\n * @param {Parent | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction list(node, parent, state, info) {\n  const exit = state.enter('list')\n  const bulletCurrent = state.bulletCurrent\n  /** @type {string} */\n  let bullet = node.ordered ? (0,_util_check_bullet_ordered_js__WEBPACK_IMPORTED_MODULE_0__.checkBulletOrdered)(state) : (0,_util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__.checkBullet)(state)\n  /** @type {string} */\n  const bulletOther = node.ordered\n    ? (0,_util_check_bullet_ordered_other_js__WEBPACK_IMPORTED_MODULE_2__.checkBulletOrderedOther)(state)\n    : (0,_util_check_bullet_other_js__WEBPACK_IMPORTED_MODULE_3__.checkBulletOther)(state)\n  const bulletLastUsed = state.bulletLastUsed\n  let useDifferentMarker = false\n\n  if (\n    parent &&\n    // Explicit `other` set.\n    (node.ordered\n      ? state.options.bulletOrderedOther\n      : state.options.bulletOther) &&\n    bulletLastUsed &&\n    bullet === bulletLastUsed\n  ) {\n    useDifferentMarker = true\n  }\n\n  if (!node.ordered) {\n    const firstListItem = node.children ? node.children[0] : undefined\n\n    // If there’s an empty first list item directly in two list items,\n    // we have to use a different bullet:\n    //\n    // ```markdown\n    // * - *\n    // ```\n    //\n    // …because otherwise it would become one big thematic break.\n    if (\n      // Bullet could be used as a thematic break marker:\n      (bullet === '*' || bullet === '-') &&\n      // Empty first list item:\n      firstListItem &&\n      (!firstListItem.children || !firstListItem.children[0]) &&\n      // Directly in two other list items:\n      state.stack[state.stack.length - 1] === 'list' &&\n      state.stack[state.stack.length - 2] === 'listItem' &&\n      state.stack[state.stack.length - 3] === 'list' &&\n      state.stack[state.stack.length - 4] === 'listItem' &&\n      // That are each the first child.\n      state.indexStack[state.indexStack.length - 1] === 0 &&\n      state.indexStack[state.indexStack.length - 2] === 0 &&\n      state.indexStack[state.indexStack.length - 3] === 0\n    ) {\n      useDifferentMarker = true\n    }\n\n    // If there’s a thematic break at the start of the first list item,\n    // we have to use a different bullet:\n    //\n    // ```markdown\n    // * ---\n    // ```\n    //\n    // …because otherwise it would become one big thematic break.\n    if ((0,_util_check_rule_js__WEBPACK_IMPORTED_MODULE_4__.checkRule)(state) === bullet && firstListItem) {\n      let index = -1\n\n      while (++index < node.children.length) {\n        const item = node.children[index]\n\n        if (\n          item &&\n          item.type === 'listItem' &&\n          item.children &&\n          item.children[0] &&\n          item.children[0].type === 'thematicBreak'\n        ) {\n          useDifferentMarker = true\n          break\n        }\n      }\n    }\n  }\n\n  if (useDifferentMarker) {\n    bullet = bulletOther\n  }\n\n  state.bulletCurrent = bullet\n  const value = state.containerFlow(node, info)\n  state.bulletLastUsed = bullet\n  state.bulletCurrent = bulletCurrent\n  exit()\n  return value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/list.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   paragraph: function() { return /* binding */ paragraph; }\n/* harmony export */ });\n/**\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n/**\n * @param {Paragraph} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction paragraph(node, _, state, info) {\n  const exit = state.enter('paragraph')\n  const subexit = state.enter('phrasing')\n  const value = state.containerPhrasing(node, info)\n  subexit()\n  exit()\n  return value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvcGFyYWdyYXBoLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsMkJBQTJCO0FBQ3hDLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsNkJBQTZCO0FBQzFDLGFBQWEsNEJBQTRCO0FBQ3pDOztBQUVBO0FBQ0EsV0FBVyxXQUFXO0FBQ3RCLFdBQVcsb0JBQW9CO0FBQy9CLFdBQVcsT0FBTztBQUNsQixXQUFXLE1BQU07QUFDakIsYUFBYTtBQUNiO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3BhcmFncmFwaC5qcz9iNjAyIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5QYXJhZ3JhcGh9IFBhcmFncmFwaFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5QYXJlbnR9IFBhcmVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5TdGF0ZX0gU3RhdGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSW5mb30gSW5mb1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtQYXJhZ3JhcGh9IG5vZGVcbiAqIEBwYXJhbSB7UGFyZW50IHwgdW5kZWZpbmVkfSBfXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHBhcmFtIHtJbmZvfSBpbmZvXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gcGFyYWdyYXBoKG5vZGUsIF8sIHN0YXRlLCBpbmZvKSB7XG4gIGNvbnN0IGV4aXQgPSBzdGF0ZS5lbnRlcigncGFyYWdyYXBoJylcbiAgY29uc3Qgc3ViZXhpdCA9IHN0YXRlLmVudGVyKCdwaHJhc2luZycpXG4gIGNvbnN0IHZhbHVlID0gc3RhdGUuY29udGFpbmVyUGhyYXNpbmcobm9kZSwgaW5mbylcbiAgc3ViZXhpdCgpXG4gIGV4aXQoKVxuICByZXR1cm4gdmFsdWVcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/root.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/root.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: function() { return /* binding */ root; }\n/* harmony export */ });\n/* harmony import */ var mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-phrasing */ \"(app-pages-browser)/./node_modules/mdast-util-phrasing/lib/index.js\");\n/**\n * @typedef {import('mdast').Root} Root\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n\n\n/**\n * @param {Root} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction root(node, _, state, info) {\n  // Note: `html` nodes are ambiguous.\n  const hasPhrasing = node.children.some((d) => (0,mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_0__.phrasing)(d))\n  const fn = hasPhrasing ? state.containerPhrasing : state.containerFlow\n  // @ts-expect-error: `root`s are supposed to have one type of content\n  return fn.call(state, node, info)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvcm9vdC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSxzQkFBc0I7QUFDbkMsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSw2QkFBNkI7QUFDMUMsYUFBYSw0QkFBNEI7QUFDekM7O0FBRTRDOztBQUU1QztBQUNBLFdBQVcsTUFBTTtBQUNqQixXQUFXLG9CQUFvQjtBQUMvQixXQUFXLE9BQU87QUFDbEIsV0FBVyxNQUFNO0FBQ2pCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQSxnREFBZ0QsNkRBQVE7QUFDeEQ7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL2hhbmRsZS9yb290LmpzP2Q3ZDAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLlJvb3R9IFJvb3RcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuUGFyZW50fSBQYXJlbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuU3RhdGV9IFN0YXRlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkluZm99IEluZm9cbiAqL1xuXG5pbXBvcnQge3BocmFzaW5nfSBmcm9tICdtZGFzdC11dGlsLXBocmFzaW5nJ1xuXG4vKipcbiAqIEBwYXJhbSB7Um9vdH0gbm9kZVxuICogQHBhcmFtIHtQYXJlbnQgfCB1bmRlZmluZWR9IF9cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcGFyYW0ge0luZm99IGluZm9cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiByb290KG5vZGUsIF8sIHN0YXRlLCBpbmZvKSB7XG4gIC8vIE5vdGU6IGBodG1sYCBub2RlcyBhcmUgYW1iaWd1b3VzLlxuICBjb25zdCBoYXNQaHJhc2luZyA9IG5vZGUuY2hpbGRyZW4uc29tZSgoZCkgPT4gcGhyYXNpbmcoZCkpXG4gIGNvbnN0IGZuID0gaGFzUGhyYXNpbmcgPyBzdGF0ZS5jb250YWluZXJQaHJhc2luZyA6IHN0YXRlLmNvbnRhaW5lckZsb3dcbiAgLy8gQHRzLWV4cGVjdC1lcnJvcjogYHJvb3RgcyBhcmUgc3VwcG9zZWQgdG8gaGF2ZSBvbmUgdHlwZSBvZiBjb250ZW50XG4gIHJldHVybiBmbi5jYWxsKHN0YXRlLCBub2RlLCBpbmZvKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/root.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js":
/*!******************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/strong.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strong: function() { return /* binding */ strong; }\n/* harmony export */ });\n/* harmony import */ var _util_check_strong_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-strong.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js\");\n/**\n * @typedef {import('mdast').Strong} Strong\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n\n\nstrong.peek = strongPeek\n\n// To do: there are cases where emphasis cannot “form” depending on the\n// previous or next character of sequences.\n// There’s no way around that though, except for injecting zero-width stuff.\n// Do we need to safeguard against that?\n/**\n * @param {Strong} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction strong(node, _, state, info) {\n  const marker = (0,_util_check_strong_js__WEBPACK_IMPORTED_MODULE_0__.checkStrong)(state)\n  const exit = state.enter('strong')\n  const tracker = state.createTracker(info)\n  let value = tracker.move(marker + marker)\n  value += tracker.move(\n    state.containerPhrasing(node, {\n      before: value,\n      after: marker,\n      ...tracker.current()\n    })\n  )\n  value += tracker.move(marker + marker)\n  exit()\n  return value\n}\n\n/**\n * @param {Strong} _\n * @param {Parent | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nfunction strongPeek(_, _1, state) {\n  return state.options.strong || '*'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/text.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/text.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: function() { return /* binding */ text; }\n/* harmony export */ });\n/**\n * @typedef {import('mdast').Text} Text\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n/**\n * @param {Text} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction text(node, _, state, info) {\n  return state.safe(node.value, info)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvdGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLHNCQUFzQjtBQUNuQyxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLDZCQUE2QjtBQUMxQyxhQUFhLDRCQUE0QjtBQUN6Qzs7QUFFQTtBQUNBLFdBQVcsTUFBTTtBQUNqQixXQUFXLG9CQUFvQjtBQUMvQixXQUFXLE9BQU87QUFDbEIsV0FBVyxNQUFNO0FBQ2pCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3RleHQuanM/YjJhNiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuVGV4dH0gVGV4dFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5QYXJlbnR9IFBhcmVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5TdGF0ZX0gU3RhdGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuSW5mb30gSW5mb1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtUZXh0fSBub2RlXG4gKiBAcGFyYW0ge1BhcmVudCB8IHVuZGVmaW5lZH0gX1xuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEBwYXJhbSB7SW5mb30gaW5mb1xuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHRleHQobm9kZSwgXywgc3RhdGUsIGluZm8pIHtcbiAgcmV0dXJuIHN0YXRlLnNhZmUobm9kZS52YWx1ZSwgaW5mbylcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/text.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   thematicBreak: function() { return /* binding */ thematicBreak; }\n/* harmony export */ });\n/* harmony import */ var _util_check_rule_repetition_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-rule-repetition.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js\");\n/* harmony import */ var _util_check_rule_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-rule.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\");\n/**\n * @typedef {import('mdast').ThematicBreak} ThematicBreak\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n */\n\n\n\n\n/**\n * @param {ThematicBreak} _\n * @param {Parent | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nfunction thematicBreak(_, _1, state) {\n  const value = (\n    (0,_util_check_rule_js__WEBPACK_IMPORTED_MODULE_0__.checkRule)(state) + (state.options.ruleSpaces ? ' ' : '')\n  ).repeat((0,_util_check_rule_repetition_js__WEBPACK_IMPORTED_MODULE_1__.checkRuleRepetition)(state))\n\n  return state.options.ruleSpaces ? value.slice(0, -1) : value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvdGhlbWF0aWMtYnJlYWsuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQSxhQUFhLCtCQUErQjtBQUM1QyxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLDZCQUE2QjtBQUMxQzs7QUFFb0U7QUFDckI7O0FBRS9DO0FBQ0EsV0FBVyxlQUFlO0FBQzFCLFdBQVcsb0JBQW9CO0FBQy9CLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0EsSUFBSSw4REFBUztBQUNiLFdBQVcsbUZBQW1COztBQUU5QjtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvdGhlbWF0aWMtYnJlYWsuanM/NzdlMyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuVGhlbWF0aWNCcmVha30gVGhlbWF0aWNCcmVha1xuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5QYXJlbnR9IFBhcmVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5TdGF0ZX0gU3RhdGVcbiAqL1xuXG5pbXBvcnQge2NoZWNrUnVsZVJlcGV0aXRpb259IGZyb20gJy4uL3V0aWwvY2hlY2stcnVsZS1yZXBldGl0aW9uLmpzJ1xuaW1wb3J0IHtjaGVja1J1bGV9IGZyb20gJy4uL3V0aWwvY2hlY2stcnVsZS5qcydcblxuLyoqXG4gKiBAcGFyYW0ge1RoZW1hdGljQnJlYWt9IF9cbiAqIEBwYXJhbSB7UGFyZW50IHwgdW5kZWZpbmVkfSBfMVxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0aGVtYXRpY0JyZWFrKF8sIF8xLCBzdGF0ZSkge1xuICBjb25zdCB2YWx1ZSA9IChcbiAgICBjaGVja1J1bGUoc3RhdGUpICsgKHN0YXRlLm9wdGlvbnMucnVsZVNwYWNlcyA/ICcgJyA6ICcnKVxuICApLnJlcGVhdChjaGVja1J1bGVSZXBldGl0aW9uKHN0YXRlKSlcblxuICByZXR1cm4gc3RhdGUub3B0aW9ucy5ydWxlU3BhY2VzID8gdmFsdWUuc2xpY2UoMCwgLTEpIDogdmFsdWVcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/index.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toMarkdown: function() { return /* binding */ toMarkdown; }\n/* harmony export */ });\n/* harmony import */ var zwitch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! zwitch */ \"(app-pages-browser)/./node_modules/zwitch/index.js\");\n/* harmony import */ var _configure_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./configure.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/configure.js\");\n/* harmony import */ var _handle_index_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./handle/index.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/index.js\");\n/* harmony import */ var _join_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./join.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/join.js\");\n/* harmony import */ var _unsafe_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./unsafe.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/unsafe.js\");\n/* harmony import */ var _util_association_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/association.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/association.js\");\n/* harmony import */ var _util_container_phrasing_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./util/container-phrasing.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js\");\n/* harmony import */ var _util_container_flow_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./util/container-flow.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/container-flow.js\");\n/* harmony import */ var _util_indent_lines_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/indent-lines.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/indent-lines.js\");\n/* harmony import */ var _util_safe_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./util/safe.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/safe.js\");\n/* harmony import */ var _util_track_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/track.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/track.js\");\n/**\n * @typedef {import('./types.js').Enter} Enter\n * @typedef {import('./types.js').Info} Info\n * @typedef {import('./types.js').Join} Join\n * @typedef {import('./types.js').FlowContent} FlowContent\n * @typedef {import('./types.js').Node} Node\n * @typedef {import('./types.js').Options} Options\n * @typedef {import('./types.js').Parent} Parent\n * @typedef {import('./types.js').PhrasingContent} PhrasingContent\n * @typedef {import('./types.js').SafeConfig} SafeConfig\n * @typedef {import('./types.js').State} State\n * @typedef {import('./types.js').TrackFields} TrackFields\n */\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Turn an mdast syntax tree into markdown.\n *\n * @param {Node} tree\n *   Tree to serialize.\n * @param {Options} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized markdown representing `tree`.\n */\nfunction toMarkdown(tree, options = {}) {\n  /** @type {State} */\n  const state = {\n    enter,\n    indentLines: _util_indent_lines_js__WEBPACK_IMPORTED_MODULE_0__.indentLines,\n    associationId: _util_association_js__WEBPACK_IMPORTED_MODULE_1__.association,\n    containerPhrasing: containerPhrasingBound,\n    containerFlow: containerFlowBound,\n    createTracker: _util_track_js__WEBPACK_IMPORTED_MODULE_2__.track,\n    safe: safeBound,\n    stack: [],\n    unsafe: [],\n    join: [],\n    // @ts-expect-error: we’ll fill it next.\n    handlers: {},\n    options: {},\n    indexStack: [],\n    // @ts-expect-error: we’ll add `handle` later.\n    handle: undefined\n  }\n\n  ;(0,_configure_js__WEBPACK_IMPORTED_MODULE_3__.configure)(state, {unsafe: _unsafe_js__WEBPACK_IMPORTED_MODULE_4__.unsafe, join: _join_js__WEBPACK_IMPORTED_MODULE_5__.join, handlers: _handle_index_js__WEBPACK_IMPORTED_MODULE_6__.handle})\n  ;(0,_configure_js__WEBPACK_IMPORTED_MODULE_3__.configure)(state, options)\n\n  if (state.options.tightDefinitions) {\n    (0,_configure_js__WEBPACK_IMPORTED_MODULE_3__.configure)(state, {join: [joinDefinition]})\n  }\n\n  state.handle = (0,zwitch__WEBPACK_IMPORTED_MODULE_7__.zwitch)('type', {\n    invalid,\n    unknown,\n    handlers: state.handlers\n  })\n\n  let result = state.handle(tree, undefined, state, {\n    before: '\\n',\n    after: '\\n',\n    now: {line: 1, column: 1},\n    lineShift: 0\n  })\n\n  if (\n    result &&\n    result.charCodeAt(result.length - 1) !== 10 &&\n    result.charCodeAt(result.length - 1) !== 13\n  ) {\n    result += '\\n'\n  }\n\n  return result\n\n  /** @type {Enter} */\n  function enter(name) {\n    state.stack.push(name)\n    return exit\n\n    function exit() {\n      state.stack.pop()\n    }\n  }\n}\n\n/**\n * @param {unknown} value\n * @returns {never}\n */\nfunction invalid(value) {\n  throw new Error('Cannot handle value `' + value + '`, expected node')\n}\n\n/**\n * @param {unknown} node\n * @returns {never}\n */\nfunction unknown(node) {\n  // @ts-expect-error: fine.\n  throw new Error('Cannot handle unknown node `' + node.type + '`')\n}\n\n/** @type {Join} */\nfunction joinDefinition(left, right) {\n  // No blank line between adjacent definitions.\n  if (left.type === 'definition' && left.type === right.type) {\n    return 0\n  }\n}\n\n/**\n * Serialize the children of a parent that contains phrasing children.\n *\n * These children will be joined flush together.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Parent & {children: Array<PhrasingContent>}} parent\n *   Parent of flow nodes.\n * @param {Info} info\n *   Info on where we are in the document we are generating.\n * @returns {string}\n *   Serialized children, joined together.\n */\nfunction containerPhrasingBound(parent, info) {\n  return (0,_util_container_phrasing_js__WEBPACK_IMPORTED_MODULE_8__.containerPhrasing)(parent, this, info)\n}\n\n/**\n * Serialize the children of a parent that contains flow children.\n *\n * These children will typically be joined by blank lines.\n * What they are joined by exactly is defined by `Join` functions.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Parent & {children: Array<FlowContent>}} parent\n *   Parent of flow nodes.\n * @param {TrackFields} info\n *   Info on where we are in the document we are generating.\n * @returns {string}\n *   Serialized children, joined by (blank) lines.\n */\nfunction containerFlowBound(parent, info) {\n  return (0,_util_container_flow_js__WEBPACK_IMPORTED_MODULE_9__.containerFlow)(parent, this, info)\n}\n\n/**\n * Make a string safe for embedding in markdown constructs.\n *\n * In markdown, almost all punctuation characters can, in certain cases,\n * result in something.\n * Whether they do is highly subjective to where they happen and in what\n * they happen.\n *\n * To solve this, `mdast-util-to-markdown` tracks:\n *\n * * Characters before and after something;\n * * What “constructs” we are in.\n *\n * This information is then used by this function to escape or encode\n * special characters.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {string | null | undefined} value\n *   Raw value to make safe.\n * @param {SafeConfig} config\n *   Configuration.\n * @returns {string}\n *   Serialized markdown safe for embedding.\n */\nfunction safeBound(value, config) {\n  return (0,_util_safe_js__WEBPACK_IMPORTED_MODULE_10__.safe)(this, value, config)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/join.js":
/*!*********************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/join.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   join: function() { return /* binding */ join; }\n/* harmony export */ });\n/* harmony import */ var _util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/format-code-as-indented.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js\");\n/* harmony import */ var _util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/format-heading-as-setext.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js\");\n/**\n * @typedef {import('./types.js').Join} Join\n */\n\n\n\n\n/** @type {Array<Join>} */\nconst join = [joinDefaults]\n\n/** @type {Join} */\nfunction joinDefaults(left, right, parent, state) {\n  // Indented code after list or another indented code.\n  if (\n    right.type === 'code' &&\n    (0,_util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_0__.formatCodeAsIndented)(right, state) &&\n    (left.type === 'list' ||\n      (left.type === right.type && (0,_util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_0__.formatCodeAsIndented)(left, state)))\n  ) {\n    return false\n  }\n\n  // Two lists with the same marker.\n  if (\n    left.type === 'list' &&\n    left.type === right.type &&\n    Boolean(left.ordered) === Boolean(right.ordered) &&\n    !(left.ordered\n      ? state.options.bulletOrderedOther\n      : state.options.bulletOther)\n  ) {\n    return false\n  }\n\n  // Join children of a list or an item.\n  // In which case, `parent` has a `spread` field.\n  if ('spread' in parent && typeof parent.spread === 'boolean') {\n    if (\n      left.type === 'paragraph' &&\n      // Two paragraphs.\n      (left.type === right.type ||\n        right.type === 'definition' ||\n        // Paragraph followed by a setext heading.\n        (right.type === 'heading' && (0,_util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_1__.formatHeadingAsSetext)(right, state)))\n    ) {\n      return\n    }\n\n    return parent.spread ? 1 : 0\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/join.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/unsafe.js":
/*!***********************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/unsafe.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unsafe: function() { return /* binding */ unsafe; }\n/* harmony export */ });\n/**\n * @typedef {import('./types.js').Unsafe} Unsafe\n * @typedef {import('./types.js').ConstructName} ConstructName\n */\n\n/**\n * List of constructs that occur in phrasing (paragraphs, headings), but cannot\n * contain things like attention (emphasis, strong), images, or links.\n * So they sort of cancel each other out.\n * Note: could use a better name.\n *\n * @type {Array<ConstructName>}\n */\nconst fullPhrasingSpans = [\n  'autolink',\n  'destinationLiteral',\n  'destinationRaw',\n  'reference',\n  'titleQuote',\n  'titleApostrophe'\n]\n\n/** @type {Array<Unsafe>} */\nconst unsafe = [\n  {character: '\\t', after: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  {character: '\\t', before: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  {\n    character: '\\t',\n    inConstruct: ['codeFencedLangGraveAccent', 'codeFencedLangTilde']\n  },\n  {\n    character: '\\r',\n    inConstruct: [\n      'codeFencedLangGraveAccent',\n      'codeFencedLangTilde',\n      'codeFencedMetaGraveAccent',\n      'codeFencedMetaTilde',\n      'destinationLiteral',\n      'headingAtx'\n    ]\n  },\n  {\n    character: '\\n',\n    inConstruct: [\n      'codeFencedLangGraveAccent',\n      'codeFencedLangTilde',\n      'codeFencedMetaGraveAccent',\n      'codeFencedMetaTilde',\n      'destinationLiteral',\n      'headingAtx'\n    ]\n  },\n  {character: ' ', after: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  {character: ' ', before: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  {\n    character: ' ',\n    inConstruct: ['codeFencedLangGraveAccent', 'codeFencedLangTilde']\n  },\n  // An exclamation mark can start an image, if it is followed by a link or\n  // a link reference.\n  {\n    character: '!',\n    after: '\\\\[',\n    inConstruct: 'phrasing',\n    notInConstruct: fullPhrasingSpans\n  },\n  // A quote can break out of a title.\n  {character: '\"', inConstruct: 'titleQuote'},\n  // A number sign could start an ATX heading if it starts a line.\n  {atBreak: true, character: '#'},\n  {character: '#', inConstruct: 'headingAtx', after: '(?:[\\r\\n]|$)'},\n  // Dollar sign and percentage are not used in markdown.\n  // An ampersand could start a character reference.\n  {character: '&', after: '[#A-Za-z]', inConstruct: 'phrasing'},\n  // An apostrophe can break out of a title.\n  {character: \"'\", inConstruct: 'titleApostrophe'},\n  // A left paren could break out of a destination raw.\n  {character: '(', inConstruct: 'destinationRaw'},\n  // A left paren followed by `]` could make something into a link or image.\n  {\n    before: '\\\\]',\n    character: '(',\n    inConstruct: 'phrasing',\n    notInConstruct: fullPhrasingSpans\n  },\n  // A right paren could start a list item or break out of a destination\n  // raw.\n  {atBreak: true, before: '\\\\d+', character: ')'},\n  {character: ')', inConstruct: 'destinationRaw'},\n  // An asterisk can start thematic breaks, list items, emphasis, strong.\n  {atBreak: true, character: '*', after: '(?:[ \\t\\r\\n*])'},\n  {character: '*', inConstruct: 'phrasing', notInConstruct: fullPhrasingSpans},\n  // A plus sign could start a list item.\n  {atBreak: true, character: '+', after: '(?:[ \\t\\r\\n])'},\n  // A dash can start thematic breaks, list items, and setext heading\n  // underlines.\n  {atBreak: true, character: '-', after: '(?:[ \\t\\r\\n-])'},\n  // A dot could start a list item.\n  {atBreak: true, before: '\\\\d+', character: '.', after: '(?:[ \\t\\r\\n]|$)'},\n  // Slash, colon, and semicolon are not used in markdown for constructs.\n  // A less than can start html (flow or text) or an autolink.\n  // HTML could start with an exclamation mark (declaration, cdata, comment),\n  // slash (closing tag), question mark (instruction), or a letter (tag).\n  // An autolink also starts with a letter.\n  // Finally, it could break out of a destination literal.\n  {atBreak: true, character: '<', after: '[!/?A-Za-z]'},\n  {\n    character: '<',\n    after: '[!/?A-Za-z]',\n    inConstruct: 'phrasing',\n    notInConstruct: fullPhrasingSpans\n  },\n  {character: '<', inConstruct: 'destinationLiteral'},\n  // An equals to can start setext heading underlines.\n  {atBreak: true, character: '='},\n  // A greater than can start block quotes and it can break out of a\n  // destination literal.\n  {atBreak: true, character: '>'},\n  {character: '>', inConstruct: 'destinationLiteral'},\n  // Question mark and at sign are not used in markdown for constructs.\n  // A left bracket can start definitions, references, labels,\n  {atBreak: true, character: '['},\n  {character: '[', inConstruct: 'phrasing', notInConstruct: fullPhrasingSpans},\n  {character: '[', inConstruct: ['label', 'reference']},\n  // A backslash can start an escape (when followed by punctuation) or a\n  // hard break (when followed by an eol).\n  // Note: typical escapes are handled in `safe`!\n  {character: '\\\\', after: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  // A right bracket can exit labels.\n  {character: ']', inConstruct: ['label', 'reference']},\n  // Caret is not used in markdown for constructs.\n  // An underscore can start emphasis, strong, or a thematic break.\n  {atBreak: true, character: '_'},\n  {character: '_', inConstruct: 'phrasing', notInConstruct: fullPhrasingSpans},\n  // A grave accent can start code (fenced or text), or it can break out of\n  // a grave accent code fence.\n  {atBreak: true, character: '`'},\n  {\n    character: '`',\n    inConstruct: ['codeFencedLangGraveAccent', 'codeFencedMetaGraveAccent']\n  },\n  {character: '`', inConstruct: 'phrasing', notInConstruct: fullPhrasingSpans},\n  // Left brace, vertical bar, right brace are not used in markdown for\n  // constructs.\n  // A tilde can start code (fenced).\n  {atBreak: true, character: '~'}\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/unsafe.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/association.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/association.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   association: function() { return /* binding */ association; }\n/* harmony export */ });\n/* harmony import */ var micromark_util_decode_string__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-decode-string */ \"(app-pages-browser)/./node_modules/micromark-util-decode-string/dev/index.js\");\n/**\n * @typedef {import('../types.js').AssociationId} AssociationId\n */\n\n\n\n/**\n * Get an identifier from an association to match it to others.\n *\n * Associations are nodes that match to something else through an ID:\n * <https://github.com/syntax-tree/mdast#association>.\n *\n * The `label` of an association is the string value: character escapes and\n * references work, and casing is intact.\n * The `identifier` is used to match one association to another:\n * controversially, character escapes and references don’t work in this\n * matching: `&copy;` does not match `©`, and `\\+` does not match `+`.\n *\n * But casing is ignored (and whitespace) is trimmed and collapsed: ` A\\nb`\n * matches `a b`.\n * So, we do prefer the label when figuring out how we’re going to serialize:\n * it has whitespace, casing, and we can ignore most useless character\n * escapes and all character references.\n *\n * @type {AssociationId}\n */\nfunction association(node) {\n  if (node.label || !node.identifier) {\n    return node.label || ''\n  }\n\n  return (0,micromark_util_decode_string__WEBPACK_IMPORTED_MODULE_0__.decodeString)(node.identifier)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/association.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered-other.js":
/*!************************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered-other.js ***!
  \************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBulletOrderedOther: function() { return /* binding */ checkBulletOrderedOther; }\n/* harmony export */ });\n/* harmony import */ var _check_bullet_ordered_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./check-bullet-ordered.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js\");\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bulletOrdered'], null | undefined>}\n */\nfunction checkBulletOrderedOther(state) {\n  const bulletOrdered = (0,_check_bullet_ordered_js__WEBPACK_IMPORTED_MODULE_0__.checkBulletOrdered)(state)\n  const bulletOrderedOther = state.options.bulletOrderedOther\n\n  if (!bulletOrderedOther) {\n    return bulletOrdered === '.' ? ')' : '.'\n  }\n\n  if (bulletOrderedOther !== '.' && bulletOrderedOther !== ')') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        bulletOrderedOther +\n        '` for `options.bulletOrderedOther`, expected `*`, `+`, or `-`'\n    )\n  }\n\n  if (bulletOrderedOther === bulletOrdered) {\n    throw new Error(\n      'Expected `bulletOrdered` (`' +\n        bulletOrdered +\n        '`) and `bulletOrderedOther` (`' +\n        bulletOrderedOther +\n        '`) to be different'\n    )\n  }\n\n  return bulletOrderedOther\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered-other.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js":
/*!******************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js ***!
  \******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBulletOrdered: function() { return /* binding */ checkBulletOrdered; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bulletOrdered'], null | undefined>}\n */\nfunction checkBulletOrdered(state) {\n  const marker = state.options.bulletOrdered || '.'\n\n  if (marker !== '.' && marker !== ')') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        marker +\n        '` for `options.bulletOrdered`, expected `.` or `)`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLWJ1bGxldC1vcmRlcmVkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsNkJBQTZCO0FBQzFDLGFBQWEsK0JBQStCO0FBQzVDOztBQUVBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQtb3JkZXJlZC5qcz9jM2RlIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5TdGF0ZX0gU3RhdGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuT3B0aW9uc30gT3B0aW9uc1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ2J1bGxldE9yZGVyZWQnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja0J1bGxldE9yZGVyZWQoc3RhdGUpIHtcbiAgY29uc3QgbWFya2VyID0gc3RhdGUub3B0aW9ucy5idWxsZXRPcmRlcmVkIHx8ICcuJ1xuXG4gIGlmIChtYXJrZXIgIT09ICcuJyAmJiBtYXJrZXIgIT09ICcpJykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIGl0ZW1zIHdpdGggYCcgK1xuICAgICAgICBtYXJrZXIgK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMuYnVsbGV0T3JkZXJlZGAsIGV4cGVjdGVkIGAuYCBvciBgKWAnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIG1hcmtlclxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js":
/*!****************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBulletOther: function() { return /* binding */ checkBulletOther; }\n/* harmony export */ });\n/* harmony import */ var _check_bullet_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./check-bullet.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */\nfunction checkBulletOther(state) {\n  const bullet = (0,_check_bullet_js__WEBPACK_IMPORTED_MODULE_0__.checkBullet)(state)\n  const bulletOther = state.options.bulletOther\n\n  if (!bulletOther) {\n    return bullet === '*' ? '-' : '*'\n  }\n\n  if (bulletOther !== '*' && bulletOther !== '+' && bulletOther !== '-') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        bulletOther +\n        '` for `options.bulletOther`, expected `*`, `+`, or `-`'\n    )\n  }\n\n  if (bulletOther === bullet) {\n    throw new Error(\n      'Expected `bullet` (`' +\n        bullet +\n        '`) and `bulletOther` (`' +\n        bulletOther +\n        '`) to be different'\n    )\n  }\n\n  return bulletOther\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLWJ1bGxldC1vdGhlci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSw2QkFBNkI7QUFDMUMsYUFBYSwrQkFBK0I7QUFDNUM7O0FBRTZDOztBQUU3QztBQUNBLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQLGlCQUFpQiw2REFBVztBQUM1Qjs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stYnVsbGV0LW90aGVyLmpzP2NiMDAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlN0YXRlfSBTdGF0ZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5PcHRpb25zfSBPcHRpb25zXG4gKi9cblxuaW1wb3J0IHtjaGVja0J1bGxldH0gZnJvbSAnLi9jaGVjay1idWxsZXQuanMnXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ2J1bGxldCddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrQnVsbGV0T3RoZXIoc3RhdGUpIHtcbiAgY29uc3QgYnVsbGV0ID0gY2hlY2tCdWxsZXQoc3RhdGUpXG4gIGNvbnN0IGJ1bGxldE90aGVyID0gc3RhdGUub3B0aW9ucy5idWxsZXRPdGhlclxuXG4gIGlmICghYnVsbGV0T3RoZXIpIHtcbiAgICByZXR1cm4gYnVsbGV0ID09PSAnKicgPyAnLScgOiAnKidcbiAgfVxuXG4gIGlmIChidWxsZXRPdGhlciAhPT0gJyonICYmIGJ1bGxldE90aGVyICE9PSAnKycgJiYgYnVsbGV0T3RoZXIgIT09ICctJykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIGl0ZW1zIHdpdGggYCcgK1xuICAgICAgICBidWxsZXRPdGhlciArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5idWxsZXRPdGhlcmAsIGV4cGVjdGVkIGAqYCwgYCtgLCBvciBgLWAnXG4gICAgKVxuICB9XG5cbiAgaWYgKGJ1bGxldE90aGVyID09PSBidWxsZXQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnRXhwZWN0ZWQgYGJ1bGxldGAgKGAnICtcbiAgICAgICAgYnVsbGV0ICtcbiAgICAgICAgJ2ApIGFuZCBgYnVsbGV0T3RoZXJgIChgJyArXG4gICAgICAgIGJ1bGxldE90aGVyICtcbiAgICAgICAgJ2ApIHRvIGJlIGRpZmZlcmVudCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gYnVsbGV0T3RoZXJcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBullet: function() { return /* binding */ checkBullet; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */\nfunction checkBullet(state) {\n  const marker = state.options.bullet || '*'\n\n  if (marker !== '*' && marker !== '+' && marker !== '-') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        marker +\n        '` for `options.bullet`, expected `*`, `+`, or `-`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLWJ1bGxldC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLDZCQUE2QjtBQUMxQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stYnVsbGV0LmpzP2RlMDQiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlN0YXRlfSBTdGF0ZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5PcHRpb25zfSBPcHRpb25zXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1snYnVsbGV0J10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tCdWxsZXQoc3RhdGUpIHtcbiAgY29uc3QgbWFya2VyID0gc3RhdGUub3B0aW9ucy5idWxsZXQgfHwgJyonXG5cbiAgaWYgKG1hcmtlciAhPT0gJyonICYmIG1hcmtlciAhPT0gJysnICYmIG1hcmtlciAhPT0gJy0nKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgaXRlbXMgd2l0aCBgJyArXG4gICAgICAgIG1hcmtlciArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5idWxsZXRgLCBleHBlY3RlZCBgKmAsIGArYCwgb3IgYC1gJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBtYXJrZXJcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkEmphasis: function() { return /* binding */ checkEmphasis; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['emphasis'], null | undefined>}\n */\nfunction checkEmphasis(state) {\n  const marker = state.options.emphasis || '*'\n\n  if (marker !== '*' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize emphasis with `' +\n        marker +\n        '` for `options.emphasis`, expected `*`, or `_`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLWVtcGhhc2lzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsNkJBQTZCO0FBQzFDLGFBQWEsK0JBQStCO0FBQzVDOztBQUVBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1lbXBoYXNpcy5qcz9iMzk0Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5TdGF0ZX0gU3RhdGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuT3B0aW9uc30gT3B0aW9uc1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ2VtcGhhc2lzJ10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tFbXBoYXNpcyhzdGF0ZSkge1xuICBjb25zdCBtYXJrZXIgPSBzdGF0ZS5vcHRpb25zLmVtcGhhc2lzIHx8ICcqJ1xuXG4gIGlmIChtYXJrZXIgIT09ICcqJyAmJiBtYXJrZXIgIT09ICdfJykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIGVtcGhhc2lzIHdpdGggYCcgK1xuICAgICAgICBtYXJrZXIgK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMuZW1waGFzaXNgLCBleHBlY3RlZCBgKmAsIG9yIGBfYCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gbWFya2VyXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-fence.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkFence: function() { return /* binding */ checkFence; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['fence'], null | undefined>}\n */\nfunction checkFence(state) {\n  const marker = state.options.fence || '`'\n\n  if (marker !== '`' && marker !== '~') {\n    throw new Error(\n      'Cannot serialize code with `' +\n        marker +\n        '` for `options.fence`, expected `` ` `` or `~`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLWZlbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsNkJBQTZCO0FBQzFDLGFBQWEsK0JBQStCO0FBQzVDOztBQUVBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1mZW5jZS5qcz80M2VmIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5TdGF0ZX0gU3RhdGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuT3B0aW9uc30gT3B0aW9uc1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ2ZlbmNlJ10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tGZW5jZShzdGF0ZSkge1xuICBjb25zdCBtYXJrZXIgPSBzdGF0ZS5vcHRpb25zLmZlbmNlIHx8ICdgJ1xuXG4gIGlmIChtYXJrZXIgIT09ICdgJyAmJiBtYXJrZXIgIT09ICd+Jykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIGNvZGUgd2l0aCBgJyArXG4gICAgICAgIG1hcmtlciArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5mZW5jZWAsIGV4cGVjdGVkIGBgIGAgYGAgb3IgYH5gJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBtYXJrZXJcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js":
/*!********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkListItemIndent: function() { return /* binding */ checkListItemIndent; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['listItemIndent'], null | undefined>}\n */\nfunction checkListItemIndent(state) {\n  const style = state.options.listItemIndent || 'tab'\n\n  // To do: remove in a major.\n  // @ts-expect-error: deprecated.\n  if (style === 1 || style === '1') {\n    return 'one'\n  }\n\n  if (style !== 'tab' && style !== 'one' && style !== 'mixed') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        style +\n        '` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`'\n    )\n  }\n\n  return style\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLWxpc3QtaXRlbS1pbmRlbnQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSw2QkFBNkI7QUFDMUMsYUFBYSwrQkFBK0I7QUFDNUM7O0FBRUE7QUFDQSxXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ087QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stbGlzdC1pdGVtLWluZGVudC5qcz83YzY1Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5TdGF0ZX0gU3RhdGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuT3B0aW9uc30gT3B0aW9uc1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ2xpc3RJdGVtSW5kZW50J10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tMaXN0SXRlbUluZGVudChzdGF0ZSkge1xuICBjb25zdCBzdHlsZSA9IHN0YXRlLm9wdGlvbnMubGlzdEl0ZW1JbmRlbnQgfHwgJ3RhYidcblxuICAvLyBUbyBkbzogcmVtb3ZlIGluIGEgbWFqb3IuXG4gIC8vIEB0cy1leHBlY3QtZXJyb3I6IGRlcHJlY2F0ZWQuXG4gIGlmIChzdHlsZSA9PT0gMSB8fCBzdHlsZSA9PT0gJzEnKSB7XG4gICAgcmV0dXJuICdvbmUnXG4gIH1cblxuICBpZiAoc3R5bGUgIT09ICd0YWInICYmIHN0eWxlICE9PSAnb25lJyAmJiBzdHlsZSAhPT0gJ21peGVkJykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIGl0ZW1zIHdpdGggYCcgK1xuICAgICAgICBzdHlsZSArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5saXN0SXRlbUluZGVudGAsIGV4cGVjdGVkIGB0YWJgLCBgb25lYCwgb3IgYG1peGVkYCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gc3R5bGVcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-quote.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkQuote: function() { return /* binding */ checkQuote; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['quote'], null | undefined>}\n */\nfunction checkQuote(state) {\n  const marker = state.options.quote || '\"'\n\n  if (marker !== '\"' && marker !== \"'\") {\n    throw new Error(\n      'Cannot serialize title with `' +\n        marker +\n        '` for `options.quote`, expected `\"`, or `\\'`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLXF1b3RlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsNkJBQTZCO0FBQzFDLGFBQWEsK0JBQStCO0FBQzVDOztBQUVBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1xdW90ZS5qcz80MThmIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5TdGF0ZX0gU3RhdGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuT3B0aW9uc30gT3B0aW9uc1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ3F1b3RlJ10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tRdW90ZShzdGF0ZSkge1xuICBjb25zdCBtYXJrZXIgPSBzdGF0ZS5vcHRpb25zLnF1b3RlIHx8ICdcIidcblxuICBpZiAobWFya2VyICE9PSAnXCInICYmIG1hcmtlciAhPT0gXCInXCIpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSB0aXRsZSB3aXRoIGAnICtcbiAgICAgICAgbWFya2VyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLnF1b3RlYCwgZXhwZWN0ZWQgYFwiYCwgb3IgYFxcJ2AnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIG1hcmtlclxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkRuleRepetition: function() { return /* binding */ checkRuleRepetition; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['ruleRepetition'], null | undefined>}\n */\nfunction checkRuleRepetition(state) {\n  const repetition = state.options.ruleRepetition || 3\n\n  if (repetition < 3) {\n    throw new Error(\n      'Cannot serialize rules with repetition `' +\n        repetition +\n        '` for `options.ruleRepetition`, expected `3` or more'\n    )\n  }\n\n  return repetition\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLXJ1bGUtcmVwZXRpdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLDZCQUE2QjtBQUMxQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stcnVsZS1yZXBldGl0aW9uLmpzP2I2ZGIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlN0YXRlfSBTdGF0ZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5PcHRpb25zfSBPcHRpb25zXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1sncnVsZVJlcGV0aXRpb24nXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja1J1bGVSZXBldGl0aW9uKHN0YXRlKSB7XG4gIGNvbnN0IHJlcGV0aXRpb24gPSBzdGF0ZS5vcHRpb25zLnJ1bGVSZXBldGl0aW9uIHx8IDNcblxuICBpZiAocmVwZXRpdGlvbiA8IDMpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBydWxlcyB3aXRoIHJlcGV0aXRpb24gYCcgK1xuICAgICAgICByZXBldGl0aW9uICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLnJ1bGVSZXBldGl0aW9uYCwgZXhwZWN0ZWQgYDNgIG9yIG1vcmUnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIHJlcGV0aXRpb25cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-rule.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkRule: function() { return /* binding */ checkRule; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['rule'], null | undefined>}\n */\nfunction checkRule(state) {\n  const marker = state.options.rule || '*'\n\n  if (marker !== '*' && marker !== '-' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize rules with `' +\n        marker +\n        '` for `options.rule`, expected `*`, `-`, or `_`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLXJ1bGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSw2QkFBNkI7QUFDMUMsYUFBYSwrQkFBK0I7QUFDNUM7O0FBRUE7QUFDQSxXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ087QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLXJ1bGUuanM/ZjU2ZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuU3RhdGV9IFN0YXRlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLk9wdGlvbnN9IE9wdGlvbnNcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydydWxlJ10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tSdWxlKHN0YXRlKSB7XG4gIGNvbnN0IG1hcmtlciA9IHN0YXRlLm9wdGlvbnMucnVsZSB8fCAnKidcblxuICBpZiAobWFya2VyICE9PSAnKicgJiYgbWFya2VyICE9PSAnLScgJiYgbWFya2VyICE9PSAnXycpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBydWxlcyB3aXRoIGAnICtcbiAgICAgICAgbWFya2VyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLnJ1bGVgLCBleHBlY3RlZCBgKmAsIGAtYCwgb3IgYF9gJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBtYXJrZXJcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-strong.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkStrong: function() { return /* binding */ checkStrong; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['strong'], null | undefined>}\n */\nfunction checkStrong(state) {\n  const marker = state.options.strong || '*'\n\n  if (marker !== '*' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize strong with `' +\n        marker +\n        '` for `options.strong`, expected `*`, or `_`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLXN0cm9uZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLDZCQUE2QjtBQUMxQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stc3Ryb25nLmpzPzllZmEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlN0YXRlfSBTdGF0ZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5PcHRpb25zfSBPcHRpb25zXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1snc3Ryb25nJ10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tTdHJvbmcoc3RhdGUpIHtcbiAgY29uc3QgbWFya2VyID0gc3RhdGUub3B0aW9ucy5zdHJvbmcgfHwgJyonXG5cbiAgaWYgKG1hcmtlciAhPT0gJyonICYmIG1hcmtlciAhPT0gJ18nKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgc3Ryb25nIHdpdGggYCcgK1xuICAgICAgICBtYXJrZXIgK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMuc3Ryb25nYCwgZXhwZWN0ZWQgYCpgLCBvciBgX2AnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIG1hcmtlclxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/container-flow.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/container-flow.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   containerFlow: function() { return /* binding */ containerFlow; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').FlowContent} FlowContent\n * @typedef {import('../types.js').Node} Node\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').TrackFields} TrackFields\n */\n\n/**\n * @param {Parent & {children: Array<FlowContent>}} parent\n *   Parent of flow nodes.\n * @param {State} state\n *   Info passed around about the current state.\n * @param {TrackFields} info\n *   Info on where we are in the document we are generating.\n * @returns {string}\n *   Serialized children, joined by (blank) lines.\n */\nfunction containerFlow(parent, state, info) {\n  const indexStack = state.indexStack\n  const children = parent.children || []\n  const tracker = state.createTracker(info)\n  /** @type {Array<string>} */\n  const results = []\n  let index = -1\n\n  indexStack.push(-1)\n\n  while (++index < children.length) {\n    const child = children[index]\n\n    indexStack[indexStack.length - 1] = index\n\n    results.push(\n      tracker.move(\n        state.handle(child, parent, state, {\n          before: '\\n',\n          after: '\\n',\n          ...tracker.current()\n        })\n      )\n    )\n\n    if (child.type !== 'list') {\n      state.bulletLastUsed = undefined\n    }\n\n    if (index < children.length - 1) {\n      results.push(\n        tracker.move(between(child, children[index + 1], parent, state))\n      )\n    }\n  }\n\n  indexStack.pop()\n\n  return results.join('')\n}\n\n/**\n * @param {Node} left\n * @param {Node} right\n * @param {Parent} parent\n * @param {State} state\n * @returns {string}\n */\nfunction between(left, right, parent, state) {\n  let index = state.join.length\n\n  while (index--) {\n    const result = state.join[index](left, right, parent, state)\n\n    if (result === true || result === 1) {\n      break\n    }\n\n    if (typeof result === 'number') {\n      return '\\n'.repeat(1 + result)\n    }\n\n    if (result === false) {\n      return '\\n\\n<!---->\\n\\n'\n    }\n  }\n\n  return '\\n\\n'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/container-flow.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js":
/*!****************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   containerPhrasing: function() { return /* binding */ containerPhrasing; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Info} Info\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').PhrasingContent} PhrasingContent\n * @typedef {import('../types.js').State} State\n */\n\n/**\n * Serialize the children of a parent that contains phrasing children.\n *\n * These children will be joined flush together.\n *\n * @param {Parent & {children: Array<PhrasingContent>}} parent\n *   Parent of flow nodes.\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Info} info\n *   Info on where we are in the document we are generating.\n * @returns {string}\n *   Serialized children, joined together.\n */\nfunction containerPhrasing(parent, state, info) {\n  const indexStack = state.indexStack\n  const children = parent.children || []\n  /** @type {Array<string>} */\n  const results = []\n  let index = -1\n  let before = info.before\n\n  indexStack.push(-1)\n  let tracker = state.createTracker(info)\n\n  while (++index < children.length) {\n    const child = children[index]\n    /** @type {string} */\n    let after\n\n    indexStack[indexStack.length - 1] = index\n\n    if (index + 1 < children.length) {\n      /** @type {Handle} */\n      // @ts-expect-error: hush, it’s actually a `zwitch`.\n      let handle = state.handle.handlers[children[index + 1].type]\n      /** @type {Handle} */\n      // @ts-expect-error: hush, it’s actually a `zwitch`.\n      if (handle && handle.peek) handle = handle.peek\n      after = handle\n        ? handle(children[index + 1], parent, state, {\n            before: '',\n            after: '',\n            ...tracker.current()\n          }).charAt(0)\n        : ''\n    } else {\n      after = info.after\n    }\n\n    // In some cases, html (text) can be found in phrasing right after an eol.\n    // When we’d serialize that, in most cases that would be seen as html\n    // (flow).\n    // As we can’t escape or so to prevent it from happening, we take a somewhat\n    // reasonable approach: replace that eol with a space.\n    // See: <https://github.com/syntax-tree/mdast-util-to-markdown/issues/15>\n    if (\n      results.length > 0 &&\n      (before === '\\r' || before === '\\n') &&\n      child.type === 'html'\n    ) {\n      results[results.length - 1] = results[results.length - 1].replace(\n        /(\\r?\\n|\\r)$/,\n        ' '\n      )\n      before = ' '\n\n      // To do: does this work to reset tracker?\n      tracker = state.createTracker(info)\n      tracker.move(results.join(''))\n    }\n\n    results.push(\n      tracker.move(\n        state.handle(child, parent, state, {\n          ...tracker.current(),\n          before,\n          after\n        })\n      )\n    )\n\n    before = results[results.length - 1].slice(-1)\n  }\n\n  indexStack.pop()\n\n  return results.join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatCodeAsIndented: function() { return /* binding */ formatCodeAsIndented; }\n/* harmony export */ });\n/**\n * @typedef {import('mdast').Code} Code\n * @typedef {import('../types.js').State} State\n */\n\n/**\n * @param {Code} node\n * @param {State} state\n * @returns {boolean}\n */\nfunction formatCodeAsIndented(node, state) {\n  return Boolean(\n    !state.options.fences &&\n      node.value &&\n      // If there’s no info…\n      !node.lang &&\n      // And there’s a non-whitespace character…\n      /[^ \\r\\n]/.test(node.value) &&\n      // And the value doesn’t start or end in a blank…\n      !/^[\\t ]*(?:[\\r\\n]|$)|(?:^|[\\r\\n])[\\t ]*$/.test(node.value)\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2Zvcm1hdC1jb2RlLWFzLWluZGVudGVkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsc0JBQXNCO0FBQ25DLGFBQWEsNkJBQTZCO0FBQzFDOztBQUVBO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvZm9ybWF0LWNvZGUtYXMtaW5kZW50ZWQuanM/MDQwOCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuQ29kZX0gQ29kZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5TdGF0ZX0gU3RhdGVcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7Q29kZX0gbm9kZVxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtib29sZWFufVxuICovXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0Q29kZUFzSW5kZW50ZWQobm9kZSwgc3RhdGUpIHtcbiAgcmV0dXJuIEJvb2xlYW4oXG4gICAgIXN0YXRlLm9wdGlvbnMuZmVuY2VzICYmXG4gICAgICBub2RlLnZhbHVlICYmXG4gICAgICAvLyBJZiB0aGVyZeKAmXMgbm8gaW5mb+KAplxuICAgICAgIW5vZGUubGFuZyAmJlxuICAgICAgLy8gQW5kIHRoZXJl4oCZcyBhIG5vbi13aGl0ZXNwYWNlIGNoYXJhY3RlcuKAplxuICAgICAgL1teIFxcclxcbl0vLnRlc3Qobm9kZS52YWx1ZSkgJiZcbiAgICAgIC8vIEFuZCB0aGUgdmFsdWUgZG9lc27igJl0IHN0YXJ0IG9yIGVuZCBpbiBhIGJsYW5r4oCmXG4gICAgICAhL15bXFx0IF0qKD86W1xcclxcbl18JCl8KD86XnxbXFxyXFxuXSlbXFx0IF0qJC8udGVzdChub2RlLnZhbHVlKVxuICApXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatHeadingAsSetext: function() { return /* binding */ formatHeadingAsSetext; }\n/* harmony export */ });\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit */ \"(app-pages-browser)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(app-pages-browser)/./node_modules/unist-util-visit/node_modules/unist-util-visit-parents/lib/index.js\");\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mdast-util-to-string */ \"(app-pages-browser)/./node_modules/mdast-util-to-string/lib/index.js\");\n/**\n * @typedef {import('mdast').Heading} Heading\n * @typedef {import('../types.js').State} State\n */\n\n\n\n\n/**\n * @param {Heading} node\n * @param {State} state\n * @returns {boolean}\n */\nfunction formatHeadingAsSetext(node, state) {\n  let literalWithBreak = false\n\n  // Look for literals with a line break.\n  // Note that this also\n  ;(0,unist_util_visit__WEBPACK_IMPORTED_MODULE_0__.visit)(node, (node) => {\n    if (\n      ('value' in node && /\\r?\\n|\\r/.test(node.value)) ||\n      node.type === 'break'\n    ) {\n      literalWithBreak = true\n      return unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.EXIT\n    }\n  })\n\n  return Boolean(\n    (!node.depth || node.depth < 3) &&\n      (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_2__.toString)(node) &&\n      (state.options.setext || literalWithBreak)\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2Zvcm1hdC1oZWFkaW5nLWFzLXNldGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7QUFDQSxhQUFhLHlCQUF5QjtBQUN0QyxhQUFhLDZCQUE2QjtBQUMxQzs7QUFFNEM7QUFDQzs7QUFFN0M7QUFDQSxXQUFXLFNBQVM7QUFDcEIsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBLEVBQUUsd0RBQUs7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSxrREFBSTtBQUNqQjtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBLE1BQU0sOERBQVE7QUFDZDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvZm9ybWF0LWhlYWRpbmctYXMtc2V0ZXh0LmpzP2UyOTAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkhlYWRpbmd9IEhlYWRpbmdcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuaW1wb3J0IHt2aXNpdCwgRVhJVH0gZnJvbSAndW5pc3QtdXRpbC12aXNpdCdcbmltcG9ydCB7dG9TdHJpbmd9IGZyb20gJ21kYXN0LXV0aWwtdG8tc3RyaW5nJ1xuXG4vKipcbiAqIEBwYXJhbSB7SGVhZGluZ30gbm9kZVxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtib29sZWFufVxuICovXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0SGVhZGluZ0FzU2V0ZXh0KG5vZGUsIHN0YXRlKSB7XG4gIGxldCBsaXRlcmFsV2l0aEJyZWFrID0gZmFsc2VcblxuICAvLyBMb29rIGZvciBsaXRlcmFscyB3aXRoIGEgbGluZSBicmVhay5cbiAgLy8gTm90ZSB0aGF0IHRoaXMgYWxzb1xuICB2aXNpdChub2RlLCAobm9kZSkgPT4ge1xuICAgIGlmIChcbiAgICAgICgndmFsdWUnIGluIG5vZGUgJiYgL1xccj9cXG58XFxyLy50ZXN0KG5vZGUudmFsdWUpKSB8fFxuICAgICAgbm9kZS50eXBlID09PSAnYnJlYWsnXG4gICAgKSB7XG4gICAgICBsaXRlcmFsV2l0aEJyZWFrID0gdHJ1ZVxuICAgICAgcmV0dXJuIEVYSVRcbiAgICB9XG4gIH0pXG5cbiAgcmV0dXJuIEJvb2xlYW4oXG4gICAgKCFub2RlLmRlcHRoIHx8IG5vZGUuZGVwdGggPCAzKSAmJlxuICAgICAgdG9TdHJpbmcobm9kZSkgJiZcbiAgICAgIChzdGF0ZS5vcHRpb25zLnNldGV4dCB8fCBsaXRlcmFsV2l0aEJyZWFrKVxuICApXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLinkAsAutolink: function() { return /* binding */ formatLinkAsAutolink; }\n/* harmony export */ });\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-to-string */ \"(app-pages-browser)/./node_modules/mdast-util-to-string/lib/index.js\");\n/**\n * @typedef {import('mdast').Link} Link\n * @typedef {import('../types.js').State} State\n */\n\n\n\n/**\n * @param {Link} node\n * @param {State} state\n * @returns {boolean}\n */\nfunction formatLinkAsAutolink(node, state) {\n  const raw = (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_0__.toString)(node)\n\n  return Boolean(\n    !state.options.resourceLink &&\n      // If there’s a url…\n      node.url &&\n      // And there’s a no title…\n      !node.title &&\n      // And the content of `node` is a single text node…\n      node.children &&\n      node.children.length === 1 &&\n      node.children[0].type === 'text' &&\n      // And if the url is the same as the content…\n      (raw === node.url || 'mailto:' + raw === node.url) &&\n      // And that starts w/ a protocol…\n      /^[a-z][a-z+.-]+:/i.test(node.url) &&\n      // And that doesn’t contain ASCII control codes (character escapes and\n      // references don’t work), space, or angle brackets…\n      !/[\\0- <>\\u007F]/.test(node.url)\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/indent-lines.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/indent-lines.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   indentLines: function() { return /* binding */ indentLines; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').IndentLines} IndentLines\n */\n\nconst eol = /\\r?\\n|\\r/g\n\n/**\n * @type {IndentLines}\n */\nfunction indentLines(value, map) {\n  /** @type {Array<string>} */\n  const result = []\n  let start = 0\n  let line = 0\n  /** @type {RegExpExecArray | null} */\n  let match\n\n  while ((match = eol.exec(value))) {\n    one(value.slice(start, match.index))\n    result.push(match[0])\n    start = match.index + match[0].length\n    line++\n  }\n\n  one(value.slice(start))\n\n  return result.join('')\n\n  /**\n   * @param {string} value\n   */\n  function one(value) {\n    result.push(map(value, line, !value))\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2luZGVudC1saW5lcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLG1DQUFtQztBQUNoRDs7QUFFQTs7QUFFQTtBQUNBLFVBQVU7QUFDVjtBQUNPO0FBQ1AsYUFBYSxlQUFlO0FBQzVCO0FBQ0E7QUFDQTtBQUNBLGFBQWEsd0JBQXdCO0FBQ3JDOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBLGFBQWEsUUFBUTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2luZGVudC1saW5lcy5qcz80OTA0Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5JbmRlbnRMaW5lc30gSW5kZW50TGluZXNcbiAqL1xuXG5jb25zdCBlb2wgPSAvXFxyP1xcbnxcXHIvZ1xuXG4vKipcbiAqIEB0eXBlIHtJbmRlbnRMaW5lc31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGluZGVudExpbmVzKHZhbHVlLCBtYXApIHtcbiAgLyoqIEB0eXBlIHtBcnJheTxzdHJpbmc+fSAqL1xuICBjb25zdCByZXN1bHQgPSBbXVxuICBsZXQgc3RhcnQgPSAwXG4gIGxldCBsaW5lID0gMFxuICAvKiogQHR5cGUge1JlZ0V4cEV4ZWNBcnJheSB8IG51bGx9ICovXG4gIGxldCBtYXRjaFxuXG4gIHdoaWxlICgobWF0Y2ggPSBlb2wuZXhlYyh2YWx1ZSkpKSB7XG4gICAgb25lKHZhbHVlLnNsaWNlKHN0YXJ0LCBtYXRjaC5pbmRleCkpXG4gICAgcmVzdWx0LnB1c2gobWF0Y2hbMF0pXG4gICAgc3RhcnQgPSBtYXRjaC5pbmRleCArIG1hdGNoWzBdLmxlbmd0aFxuICAgIGxpbmUrK1xuICB9XG5cbiAgb25lKHZhbHVlLnNsaWNlKHN0YXJ0KSlcblxuICByZXR1cm4gcmVzdWx0LmpvaW4oJycpXG5cbiAgLyoqXG4gICAqIEBwYXJhbSB7c3RyaW5nfSB2YWx1ZVxuICAgKi9cbiAgZnVuY3Rpb24gb25lKHZhbHVlKSB7XG4gICAgcmVzdWx0LnB1c2gobWFwKHZhbHVlLCBsaW5lLCAhdmFsdWUpKVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/indent-lines.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/pattern-compile.js":
/*!*************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/pattern-compile.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patternCompile: function() { return /* binding */ patternCompile; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Unsafe} Unsafe\n */\n\n/**\n * @param {Unsafe} pattern\n * @returns {RegExp}\n */\nfunction patternCompile(pattern) {\n  if (!pattern._compiled) {\n    const before =\n      (pattern.atBreak ? '[\\\\r\\\\n][\\\\t ]*' : '') +\n      (pattern.before ? '(?:' + pattern.before + ')' : '')\n\n    pattern._compiled = new RegExp(\n      (before ? '(' + before + ')' : '') +\n        (/[|\\\\{}()[\\]^$+*?.-]/.test(pattern.character) ? '\\\\' : '') +\n        pattern.character +\n        (pattern.after ? '(?:' + pattern.after + ')' : ''),\n      'g'\n    )\n  }\n\n  return pattern._compiled\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL3BhdHRlcm4tY29tcGlsZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQzs7QUFFQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL3BhdHRlcm4tY29tcGlsZS5qcz9iYTE4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5VbnNhZmV9IFVuc2FmZVxuICovXG5cbi8qKlxuICogQHBhcmFtIHtVbnNhZmV9IHBhdHRlcm5cbiAqIEByZXR1cm5zIHtSZWdFeHB9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwYXR0ZXJuQ29tcGlsZShwYXR0ZXJuKSB7XG4gIGlmICghcGF0dGVybi5fY29tcGlsZWQpIHtcbiAgICBjb25zdCBiZWZvcmUgPVxuICAgICAgKHBhdHRlcm4uYXRCcmVhayA/ICdbXFxcXHJcXFxcbl1bXFxcXHQgXSonIDogJycpICtcbiAgICAgIChwYXR0ZXJuLmJlZm9yZSA/ICcoPzonICsgcGF0dGVybi5iZWZvcmUgKyAnKScgOiAnJylcblxuICAgIHBhdHRlcm4uX2NvbXBpbGVkID0gbmV3IFJlZ0V4cChcbiAgICAgIChiZWZvcmUgPyAnKCcgKyBiZWZvcmUgKyAnKScgOiAnJykgK1xuICAgICAgICAoL1t8XFxcXHt9KClbXFxdXiQrKj8uLV0vLnRlc3QocGF0dGVybi5jaGFyYWN0ZXIpID8gJ1xcXFwnIDogJycpICtcbiAgICAgICAgcGF0dGVybi5jaGFyYWN0ZXIgK1xuICAgICAgICAocGF0dGVybi5hZnRlciA/ICcoPzonICsgcGF0dGVybi5hZnRlciArICcpJyA6ICcnKSxcbiAgICAgICdnJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBwYXR0ZXJuLl9jb21waWxlZFxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/pattern-compile.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patternInScope: function() { return /* binding */ patternInScope; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Unsafe} Unsafe\n * @typedef {import('../types.js').ConstructName} ConstructName\n */\n\n/**\n * @param {Array<ConstructName>} stack\n * @param {Unsafe} pattern\n * @returns {boolean}\n */\nfunction patternInScope(stack, pattern) {\n  return (\n    listInScope(stack, pattern.inConstruct, true) &&\n    !listInScope(stack, pattern.notInConstruct, false)\n  )\n}\n\n/**\n * @param {Array<ConstructName>} stack\n * @param {Unsafe['inConstruct']} list\n * @param {boolean} none\n * @returns {boolean}\n */\nfunction listInScope(stack, list, none) {\n  if (typeof list === 'string') {\n    list = [list]\n  }\n\n  if (!list || list.length === 0) {\n    return none\n  }\n\n  let index = -1\n\n  while (++index < list.length) {\n    if (stack.includes(list[index])) {\n      return true\n    }\n  }\n\n  return false\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL3BhdHRlcm4taW4tc2NvcGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSxxQ0FBcUM7QUFDbEQ7O0FBRUE7QUFDQSxXQUFXLHNCQUFzQjtBQUNqQyxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsV0FBVyxzQkFBc0I7QUFDakMsV0FBVyx1QkFBdUI7QUFDbEMsV0FBVyxTQUFTO0FBQ3BCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvcGF0dGVybi1pbi1zY29wZS5qcz8wZThkIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5VbnNhZmV9IFVuc2FmZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5Db25zdHJ1Y3ROYW1lfSBDb25zdHJ1Y3ROYW1lXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge0FycmF5PENvbnN0cnVjdE5hbWU+fSBzdGFja1xuICogQHBhcmFtIHtVbnNhZmV9IHBhdHRlcm5cbiAqIEByZXR1cm5zIHtib29sZWFufVxuICovXG5leHBvcnQgZnVuY3Rpb24gcGF0dGVybkluU2NvcGUoc3RhY2ssIHBhdHRlcm4pIHtcbiAgcmV0dXJuIChcbiAgICBsaXN0SW5TY29wZShzdGFjaywgcGF0dGVybi5pbkNvbnN0cnVjdCwgdHJ1ZSkgJiZcbiAgICAhbGlzdEluU2NvcGUoc3RhY2ssIHBhdHRlcm4ubm90SW5Db25zdHJ1Y3QsIGZhbHNlKVxuICApXG59XG5cbi8qKlxuICogQHBhcmFtIHtBcnJheTxDb25zdHJ1Y3ROYW1lPn0gc3RhY2tcbiAqIEBwYXJhbSB7VW5zYWZlWydpbkNvbnN0cnVjdCddfSBsaXN0XG4gKiBAcGFyYW0ge2Jvb2xlYW59IG5vbmVcbiAqIEByZXR1cm5zIHtib29sZWFufVxuICovXG5mdW5jdGlvbiBsaXN0SW5TY29wZShzdGFjaywgbGlzdCwgbm9uZSkge1xuICBpZiAodHlwZW9mIGxpc3QgPT09ICdzdHJpbmcnKSB7XG4gICAgbGlzdCA9IFtsaXN0XVxuICB9XG5cbiAgaWYgKCFsaXN0IHx8IGxpc3QubGVuZ3RoID09PSAwKSB7XG4gICAgcmV0dXJuIG5vbmVcbiAgfVxuXG4gIGxldCBpbmRleCA9IC0xXG5cbiAgd2hpbGUgKCsraW5kZXggPCBsaXN0Lmxlbmd0aCkge1xuICAgIGlmIChzdGFjay5pbmNsdWRlcyhsaXN0W2luZGV4XSkpIHtcbiAgICAgIHJldHVybiB0cnVlXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIGZhbHNlXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/safe.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/safe.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   safe: function() { return /* binding */ safe; }\n/* harmony export */ });\n/* harmony import */ var _pattern_compile_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./pattern-compile.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/pattern-compile.js\");\n/* harmony import */ var _pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./pattern-in-scope.js */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js\");\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').SafeConfig} SafeConfig\n */\n\n\n\n\n/**\n * Make a string safe for embedding in markdown constructs.\n *\n * In markdown, almost all punctuation characters can, in certain cases,\n * result in something.\n * Whether they do is highly subjective to where they happen and in what\n * they happen.\n *\n * To solve this, `mdast-util-to-markdown` tracks:\n *\n * * Characters before and after something;\n * * What “constructs” we are in.\n *\n * This information is then used by this function to escape or encode\n * special characters.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {string | null | undefined} input\n *   Raw value to make safe.\n * @param {SafeConfig} config\n *   Configuration.\n * @returns {string}\n *   Serialized markdown safe for embedding.\n */\nfunction safe(state, input, config) {\n  const value = (config.before || '') + (input || '') + (config.after || '')\n  /** @type {Array<number>} */\n  const positions = []\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {Record<number, {before: boolean, after: boolean}>} */\n  const infos = {}\n  let index = -1\n\n  while (++index < state.unsafe.length) {\n    const pattern = state.unsafe[index]\n\n    if (!(0,_pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__.patternInScope)(state.stack, pattern)) {\n      continue\n    }\n\n    const expression = (0,_pattern_compile_js__WEBPACK_IMPORTED_MODULE_1__.patternCompile)(pattern)\n    /** @type {RegExpExecArray | null} */\n    let match\n\n    while ((match = expression.exec(value))) {\n      const before = 'before' in pattern || Boolean(pattern.atBreak)\n      const after = 'after' in pattern\n      const position = match.index + (before ? match[1].length : 0)\n\n      if (positions.includes(position)) {\n        if (infos[position].before && !before) {\n          infos[position].before = false\n        }\n\n        if (infos[position].after && !after) {\n          infos[position].after = false\n        }\n      } else {\n        positions.push(position)\n        infos[position] = {before, after}\n      }\n    }\n  }\n\n  positions.sort(numerical)\n\n  let start = config.before ? config.before.length : 0\n  const end = value.length - (config.after ? config.after.length : 0)\n  index = -1\n\n  while (++index < positions.length) {\n    const position = positions[index]\n\n    // Character before or after matched:\n    if (position < start || position >= end) {\n      continue\n    }\n\n    // If this character is supposed to be escaped because it has a condition on\n    // the next character, and the next character is definitly being escaped,\n    // then skip this escape.\n    if (\n      (position + 1 < end &&\n        positions[index + 1] === position + 1 &&\n        infos[position].after &&\n        !infos[position + 1].before &&\n        !infos[position + 1].after) ||\n      (positions[index - 1] === position - 1 &&\n        infos[position].before &&\n        !infos[position - 1].before &&\n        !infos[position - 1].after)\n    ) {\n      continue\n    }\n\n    if (start !== position) {\n      // If we have to use a character reference, an ampersand would be more\n      // correct, but as backslashes only care about punctuation, either will\n      // do the trick\n      result.push(escapeBackslashes(value.slice(start, position), '\\\\'))\n    }\n\n    start = position\n\n    if (\n      /[!-/:-@[-`{-~]/.test(value.charAt(position)) &&\n      (!config.encode || !config.encode.includes(value.charAt(position)))\n    ) {\n      // Character escape.\n      result.push('\\\\')\n    } else {\n      // Character reference.\n      result.push(\n        '&#x' + value.charCodeAt(position).toString(16).toUpperCase() + ';'\n      )\n      start++\n    }\n  }\n\n  result.push(escapeBackslashes(value.slice(start, end), config.after))\n\n  return result.join('')\n}\n\n/**\n * @param {number} a\n * @param {number} b\n * @returns {number}\n */\nfunction numerical(a, b) {\n  return a - b\n}\n\n/**\n * @param {string} value\n * @param {string} after\n * @returns {string}\n */\nfunction escapeBackslashes(value, after) {\n  const expression = /\\\\(?=[!-/:-@[-`{-~])/g\n  /** @type {Array<number>} */\n  const positions = []\n  /** @type {Array<string>} */\n  const results = []\n  const whole = value + after\n  let index = -1\n  let start = 0\n  /** @type {RegExpExecArray | null} */\n  let match\n\n  while ((match = expression.exec(whole))) {\n    positions.push(match.index)\n  }\n\n  while (++index < positions.length) {\n    if (start !== positions[index]) {\n      results.push(value.slice(start, positions[index]))\n    }\n\n    results.push('\\\\')\n    start = positions[index]\n  }\n\n  results.push(value.slice(start))\n\n  return results.join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/safe.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/track.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/track.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   track: function() { return /* binding */ track; }\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').CreateTracker} CreateTracker\n * @typedef {import('../types.js').TrackCurrent} TrackCurrent\n * @typedef {import('../types.js').TrackMove} TrackMove\n * @typedef {import('../types.js').TrackShift} TrackShift\n */\n\n/**\n * Track positional info in the output.\n *\n * @type {CreateTracker}\n */\nfunction track(config) {\n  // Defaults are used to prevent crashes when older utilities somehow activate\n  // this code.\n  /* c8 ignore next 5 */\n  const options = config || {}\n  const now = options.now || {}\n  let lineShift = options.lineShift || 0\n  let line = now.line || 1\n  let column = now.column || 1\n\n  return {move, current, shift}\n\n  /**\n   * Get the current tracked info.\n   *\n   * @type {TrackCurrent}\n   */\n  function current() {\n    return {now: {line, column}, lineShift}\n  }\n\n  /**\n   * Define an increased line shift (the typical indent for lines).\n   *\n   * @type {TrackShift}\n   */\n  function shift(value) {\n    lineShift += value\n  }\n\n  /**\n   * Move past some generated markdown.\n   *\n   * @type {TrackMove}\n   */\n  function move(input) {\n    // eslint-disable-next-line unicorn/prefer-default-parameters\n    const value = input || ''\n    const chunks = value.split(/\\r?\\n|\\r/g)\n    const tail = chunks[chunks.length - 1]\n    line += chunks.length - 1\n    column =\n      chunks.length === 1 ? column + tail.length : 1 + tail.length + lineShift\n    return value\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/track.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mdast-util-to-string/lib/index.js":
/*!********************************************************!*\
  !*** ./node_modules/mdast-util-to-string/lib/index.js ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toString: function() { return /* binding */ toString; }\n/* harmony export */ });\n/**\n * @typedef {import('mdast').Root|import('mdast').Content} Node\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [includeImageAlt=true]\n *   Whether to use `alt` for `image`s.\n * @property {boolean | null | undefined} [includeHtml=true]\n *   Whether to use `value` of HTML.\n */\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Get the text content of a node or list of nodes.\n *\n * Prefers the node’s plain-text fields, otherwise serializes its children,\n * and if the given value is an array, serialize the nodes in it.\n *\n * @param {unknown} value\n *   Thing to serialize, typically `Node`.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized `value`.\n */\nfunction toString(value, options) {\n  const settings = options || emptyOptions\n  const includeImageAlt =\n    typeof settings.includeImageAlt === 'boolean'\n      ? settings.includeImageAlt\n      : true\n  const includeHtml =\n    typeof settings.includeHtml === 'boolean' ? settings.includeHtml : true\n\n  return one(value, includeImageAlt, includeHtml)\n}\n\n/**\n * One node or several nodes.\n *\n * @param {unknown} value\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized node.\n */\nfunction one(value, includeImageAlt, includeHtml) {\n  if (node(value)) {\n    if ('value' in value) {\n      return value.type === 'html' && !includeHtml ? '' : value.value\n    }\n\n    if (includeImageAlt && 'alt' in value && value.alt) {\n      return value.alt\n    }\n\n    if ('children' in value) {\n      return all(value.children, includeImageAlt, includeHtml)\n    }\n  }\n\n  if (Array.isArray(value)) {\n    return all(value, includeImageAlt, includeHtml)\n  }\n\n  return ''\n}\n\n/**\n * Serialize a list of nodes.\n *\n * @param {Array<unknown>} values\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized nodes.\n */\nfunction all(values, includeImageAlt, includeHtml) {\n  /** @type {Array<string>} */\n  const result = []\n  let index = -1\n\n  while (++index < values.length) {\n    result[index] = one(values[index], includeImageAlt, includeHtml)\n  }\n\n  return result.join('')\n}\n\n/**\n * Check if `value` looks like a node.\n *\n * @param {unknown} value\n *   Thing.\n * @returns {value is Node}\n *   Whether `value` is a node.\n */\nfunction node(value) {\n  return Boolean(value && typeof value === 'object')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mdast-util-to-string/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/micromark-util-decode-numeric-character-reference/dev/index.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/micromark-util-decode-numeric-character-reference/dev/index.js ***!
  \*************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decodeNumericCharacterReference: function() { return /* binding */ decodeNumericCharacterReference; }\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(app-pages-browser)/./node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_values_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol/values.js */ \"(app-pages-browser)/./node_modules/micromark-util-symbol/values.js\");\n\n\n\n/**\n * Turn the number (in string form as either hexa- or plain decimal) coming from\n * a numeric character reference into a character.\n *\n * Sort of like `String.fromCharCode(Number.parseInt(value, base))`, but makes\n * non-characters and control characters safe.\n *\n * @param {string} value\n *   Value to decode.\n * @param {number} base\n *   Numeric base.\n * @returns {string}\n *   Character.\n */\nfunction decodeNumericCharacterReference(value, base) {\n  const code = Number.parseInt(value, base)\n\n  if (\n    // C0 except for HT, LF, FF, CR, space.\n    code < micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.ht ||\n    code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.vt ||\n    (code > micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.cr && code < micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.space) ||\n    // Control character (DEL) of C0, and C1 controls.\n    (code > micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.tilde && code < 160) ||\n    // Lone high surrogates and low surrogates.\n    (code > 55295 && code < 57344) ||\n    // Noncharacters.\n    (code > 64975 && code < 65008) ||\n    /* eslint-disable no-bitwise */\n    (code & 65535) === 65535 ||\n    (code & 65535) === 65534 ||\n    /* eslint-enable no-bitwise */\n    // Out of range\n    code > 1114111\n  ) {\n    return micromark_util_symbol_values_js__WEBPACK_IMPORTED_MODULE_1__.values.replacementCharacter\n  }\n\n  return String.fromCharCode(code)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/micromark-util-decode-numeric-character-reference/dev/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/micromark-util-decode-string/dev/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/micromark-util-decode-string/dev/index.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decodeString: function() { return /* binding */ decodeString; }\n/* harmony export */ });\n/* harmony import */ var decode_named_character_reference__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! decode-named-character-reference */ \"(app-pages-browser)/./node_modules/decode-named-character-reference/index.dom.js\");\n/* harmony import */ var micromark_util_decode_numeric_character_reference__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-decode-numeric-character-reference */ \"(app-pages-browser)/./node_modules/micromark-util-decode-numeric-character-reference/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(app-pages-browser)/./node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/constants.js */ \"(app-pages-browser)/./node_modules/micromark-util-symbol/constants.js\");\n\n\n\n\n\nconst characterEscapeOrReference =\n  /\\\\([!-/:-@[-`{-~])|&(#(?:\\d{1,7}|x[\\da-f]{1,6})|[\\da-z]{1,31});/gi\n\n/**\n * Decode markdown strings (which occur in places such as fenced code info\n * strings, destinations, labels, and titles).\n *\n * The “string” content type allows character escapes and -references.\n * This decodes those.\n *\n * @param {string} value\n *   Value to decode.\n * @returns {string}\n *   Decoded value.\n */\nfunction decodeString(value) {\n  return value.replace(characterEscapeOrReference, decode)\n}\n\n/**\n * @param {string} $0\n * @param {string} $1\n * @param {string} $2\n * @returns {string}\n */\nfunction decode($0, $1, $2) {\n  if ($1) {\n    // Escape.\n    return $1\n  }\n\n  // Reference.\n  const head = $2.charCodeAt(0)\n\n  if (head === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.numberSign) {\n    const head = $2.charCodeAt(1)\n    const hex = head === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseX || head === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseX\n    return (0,micromark_util_decode_numeric_character_reference__WEBPACK_IMPORTED_MODULE_1__.decodeNumericCharacterReference)(\n      $2.slice(hex ? 2 : 1),\n      hex ? micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_2__.constants.numericBaseHexadecimal : micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_2__.constants.numericBaseDecimal\n    )\n  }\n\n  return (0,decode_named_character_reference__WEBPACK_IMPORTED_MODULE_3__.decodeNamedCharacterReference)($2) || $0\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/micromark-util-decode-string/dev/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/micromark-util-symbol/codes.js":
/*!*****************************************************!*\
  !*** ./node_modules/micromark-util-symbol/codes.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   codes: function() { return /* binding */ codes; }\n/* harmony export */ });\n/**\n * Character codes.\n *\n * This module is compiled away!\n *\n * micromark works based on character codes.\n * This module contains constants for the ASCII block and the replacement\n * character.\n * A couple of them are handled in a special way, such as the line endings\n * (CR, LF, and CR+LF, commonly known as end-of-line: EOLs), the tab (horizontal\n * tab) and its expansion based on what column it’s at (virtual space),\n * and the end-of-file (eof) character.\n * As values are preprocessed before handling them, the actual characters LF,\n * CR, HT, and NUL (which is present as the replacement character), are\n * guaranteed to not exist.\n *\n * Unicode basic latin block.\n */\nconst codes = /** @type {const} */ ({\n  carriageReturn: -5,\n  lineFeed: -4,\n  carriageReturnLineFeed: -3,\n  horizontalTab: -2,\n  virtualSpace: -1,\n  eof: null,\n  nul: 0,\n  soh: 1,\n  stx: 2,\n  etx: 3,\n  eot: 4,\n  enq: 5,\n  ack: 6,\n  bel: 7,\n  bs: 8,\n  ht: 9, // `\\t`\n  lf: 10, // `\\n`\n  vt: 11, // `\\v`\n  ff: 12, // `\\f`\n  cr: 13, // `\\r`\n  so: 14,\n  si: 15,\n  dle: 16,\n  dc1: 17,\n  dc2: 18,\n  dc3: 19,\n  dc4: 20,\n  nak: 21,\n  syn: 22,\n  etb: 23,\n  can: 24,\n  em: 25,\n  sub: 26,\n  esc: 27,\n  fs: 28,\n  gs: 29,\n  rs: 30,\n  us: 31,\n  space: 32,\n  exclamationMark: 33, // `!`\n  quotationMark: 34, // `\"`\n  numberSign: 35, // `#`\n  dollarSign: 36, // `$`\n  percentSign: 37, // `%`\n  ampersand: 38, // `&`\n  apostrophe: 39, // `'`\n  leftParenthesis: 40, // `(`\n  rightParenthesis: 41, // `)`\n  asterisk: 42, // `*`\n  plusSign: 43, // `+`\n  comma: 44, // `,`\n  dash: 45, // `-`\n  dot: 46, // `.`\n  slash: 47, // `/`\n  digit0: 48, // `0`\n  digit1: 49, // `1`\n  digit2: 50, // `2`\n  digit3: 51, // `3`\n  digit4: 52, // `4`\n  digit5: 53, // `5`\n  digit6: 54, // `6`\n  digit7: 55, // `7`\n  digit8: 56, // `8`\n  digit9: 57, // `9`\n  colon: 58, // `:`\n  semicolon: 59, // `;`\n  lessThan: 60, // `<`\n  equalsTo: 61, // `=`\n  greaterThan: 62, // `>`\n  questionMark: 63, // `?`\n  atSign: 64, // `@`\n  uppercaseA: 65, // `A`\n  uppercaseB: 66, // `B`\n  uppercaseC: 67, // `C`\n  uppercaseD: 68, // `D`\n  uppercaseE: 69, // `E`\n  uppercaseF: 70, // `F`\n  uppercaseG: 71, // `G`\n  uppercaseH: 72, // `H`\n  uppercaseI: 73, // `I`\n  uppercaseJ: 74, // `J`\n  uppercaseK: 75, // `K`\n  uppercaseL: 76, // `L`\n  uppercaseM: 77, // `M`\n  uppercaseN: 78, // `N`\n  uppercaseO: 79, // `O`\n  uppercaseP: 80, // `P`\n  uppercaseQ: 81, // `Q`\n  uppercaseR: 82, // `R`\n  uppercaseS: 83, // `S`\n  uppercaseT: 84, // `T`\n  uppercaseU: 85, // `U`\n  uppercaseV: 86, // `V`\n  uppercaseW: 87, // `W`\n  uppercaseX: 88, // `X`\n  uppercaseY: 89, // `Y`\n  uppercaseZ: 90, // `Z`\n  leftSquareBracket: 91, // `[`\n  backslash: 92, // `\\`\n  rightSquareBracket: 93, // `]`\n  caret: 94, // `^`\n  underscore: 95, // `_`\n  graveAccent: 96, // `` ` ``\n  lowercaseA: 97, // `a`\n  lowercaseB: 98, // `b`\n  lowercaseC: 99, // `c`\n  lowercaseD: 100, // `d`\n  lowercaseE: 101, // `e`\n  lowercaseF: 102, // `f`\n  lowercaseG: 103, // `g`\n  lowercaseH: 104, // `h`\n  lowercaseI: 105, // `i`\n  lowercaseJ: 106, // `j`\n  lowercaseK: 107, // `k`\n  lowercaseL: 108, // `l`\n  lowercaseM: 109, // `m`\n  lowercaseN: 110, // `n`\n  lowercaseO: 111, // `o`\n  lowercaseP: 112, // `p`\n  lowercaseQ: 113, // `q`\n  lowercaseR: 114, // `r`\n  lowercaseS: 115, // `s`\n  lowercaseT: 116, // `t`\n  lowercaseU: 117, // `u`\n  lowercaseV: 118, // `v`\n  lowercaseW: 119, // `w`\n  lowercaseX: 120, // `x`\n  lowercaseY: 121, // `y`\n  lowercaseZ: 122, // `z`\n  leftCurlyBrace: 123, // `{`\n  verticalBar: 124, // `|`\n  rightCurlyBrace: 125, // `}`\n  tilde: 126, // `~`\n  del: 127,\n  // Unicode Specials block.\n  byteOrderMarker: 65279,\n  // Unicode Specials block.\n  replacementCharacter: 65533 // `�`\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/micromark-util-symbol/codes.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/micromark-util-symbol/constants.js":
/*!*********************************************************!*\
  !*** ./node_modules/micromark-util-symbol/constants.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   constants: function() { return /* binding */ constants; }\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * Parsing markdown comes with a couple of constants, such as minimum or maximum\n * sizes of certain sequences.\n * Additionally, there are a couple symbols used inside micromark.\n * These are all defined here, but compiled away by scripts.\n */\nconst constants = /** @type {const} */ ({\n  attentionSideBefore: 1, // Symbol to mark an attention sequence as before content: `*a`\n  attentionSideAfter: 2, // Symbol to mark an attention sequence as after content: `a*`\n  atxHeadingOpeningFenceSizeMax: 6, // 6 number signs is fine, 7 isn’t.\n  autolinkDomainSizeMax: 63, // 63 characters is fine, 64 is too many.\n  autolinkSchemeSizeMax: 32, // 32 characters is fine, 33 is too many.\n  cdataOpeningString: 'CDATA[', // And preceded by `<![`.\n  characterGroupWhitespace: 1, // Symbol used to indicate a character is whitespace\n  characterGroupPunctuation: 2, // Symbol used to indicate a character is punctuation\n  characterReferenceDecimalSizeMax: 7, // `&#9999999;`.\n  characterReferenceHexadecimalSizeMax: 6, // `&#xff9999;`.\n  characterReferenceNamedSizeMax: 31, // `&CounterClockwiseContourIntegral;`.\n  codeFencedSequenceSizeMin: 3, // At least 3 ticks or tildes are needed.\n  contentTypeDocument: 'document',\n  contentTypeFlow: 'flow',\n  contentTypeContent: 'content',\n  contentTypeString: 'string',\n  contentTypeText: 'text',\n  hardBreakPrefixSizeMin: 2, // At least 2 trailing spaces are needed.\n  htmlRaw: 1, // Symbol for `<script>`\n  htmlComment: 2, // Symbol for `<!---->`\n  htmlInstruction: 3, // Symbol for `<?php?>`\n  htmlDeclaration: 4, // Symbol for `<!doctype>`\n  htmlCdata: 5, // Symbol for `<![CDATA[]]>`\n  htmlBasic: 6, // Symbol for `<div`\n  htmlComplete: 7, // Symbol for `<x>`\n  htmlRawSizeMax: 8, // Length of `textarea`.\n  linkResourceDestinationBalanceMax: 32, // See: <https://spec.commonmark.org/0.30/#link-destination>, <https://github.com/remarkjs/react-markdown/issues/658#issuecomment-984345577>\n  linkReferenceSizeMax: 999, // See: <https://spec.commonmark.org/0.30/#link-label>\n  listItemValueSizeMax: 10, // See: <https://spec.commonmark.org/0.30/#ordered-list-marker>\n  numericBaseDecimal: 10,\n  numericBaseHexadecimal: 0x10,\n  tabSize: 4, // Tabs have a hard-coded size of 4, per CommonMark.\n  thematicBreakMarkerCountMin: 3, // At least 3 asterisks, dashes, or underscores are needed.\n  v8MaxSafeChunkSize: 10000 // V8 (and potentially others) have problems injecting giant arrays into other arrays, hence we operate in chunks.\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/micromark-util-symbol/constants.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/micromark-util-symbol/values.js":
/*!******************************************************!*\
  !*** ./node_modules/micromark-util-symbol/values.js ***!
  \******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   values: function() { return /* binding */ values; }\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * While micromark works based on character codes, this module includes the\n * string versions of ’em.\n * The C0 block, except for LF, CR, HT, and w/ the replacement character added,\n * are available here.\n */\nconst values = /** @type {const} */ ({\n  ht: '\\t',\n  lf: '\\n',\n  cr: '\\r',\n  space: ' ',\n  exclamationMark: '!',\n  quotationMark: '\"',\n  numberSign: '#',\n  dollarSign: '$',\n  percentSign: '%',\n  ampersand: '&',\n  apostrophe: \"'\",\n  leftParenthesis: '(',\n  rightParenthesis: ')',\n  asterisk: '*',\n  plusSign: '+',\n  comma: ',',\n  dash: '-',\n  dot: '.',\n  slash: '/',\n  digit0: '0',\n  digit1: '1',\n  digit2: '2',\n  digit3: '3',\n  digit4: '4',\n  digit5: '5',\n  digit6: '6',\n  digit7: '7',\n  digit8: '8',\n  digit9: '9',\n  colon: ':',\n  semicolon: ';',\n  lessThan: '<',\n  equalsTo: '=',\n  greaterThan: '>',\n  questionMark: '?',\n  atSign: '@',\n  uppercaseA: 'A',\n  uppercaseB: 'B',\n  uppercaseC: 'C',\n  uppercaseD: 'D',\n  uppercaseE: 'E',\n  uppercaseF: 'F',\n  uppercaseG: 'G',\n  uppercaseH: 'H',\n  uppercaseI: 'I',\n  uppercaseJ: 'J',\n  uppercaseK: 'K',\n  uppercaseL: 'L',\n  uppercaseM: 'M',\n  uppercaseN: 'N',\n  uppercaseO: 'O',\n  uppercaseP: 'P',\n  uppercaseQ: 'Q',\n  uppercaseR: 'R',\n  uppercaseS: 'S',\n  uppercaseT: 'T',\n  uppercaseU: 'U',\n  uppercaseV: 'V',\n  uppercaseW: 'W',\n  uppercaseX: 'X',\n  uppercaseY: 'Y',\n  uppercaseZ: 'Z',\n  leftSquareBracket: '[',\n  backslash: '\\\\',\n  rightSquareBracket: ']',\n  caret: '^',\n  underscore: '_',\n  graveAccent: '`',\n  lowercaseA: 'a',\n  lowercaseB: 'b',\n  lowercaseC: 'c',\n  lowercaseD: 'd',\n  lowercaseE: 'e',\n  lowercaseF: 'f',\n  lowercaseG: 'g',\n  lowercaseH: 'h',\n  lowercaseI: 'i',\n  lowercaseJ: 'j',\n  lowercaseK: 'k',\n  lowercaseL: 'l',\n  lowercaseM: 'm',\n  lowercaseN: 'n',\n  lowercaseO: 'o',\n  lowercaseP: 'p',\n  lowercaseQ: 'q',\n  lowercaseR: 'r',\n  lowercaseS: 's',\n  lowercaseT: 't',\n  lowercaseU: 'u',\n  lowercaseV: 'v',\n  lowercaseW: 'w',\n  lowercaseX: 'x',\n  lowercaseY: 'y',\n  lowercaseZ: 'z',\n  leftCurlyBrace: '{',\n  verticalBar: '|',\n  rightCurlyBrace: '}',\n  tilde: '~',\n  replacementCharacter: '�'\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/micromark-util-symbol/values.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/remark-stringify/index.js":
/*!************************************************!*\
  !*** ./node_modules/remark-stringify/index.js ***!
  \************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* reexport safe */ _lib_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _lib_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/index.js */ \"(app-pages-browser)/./node_modules/remark-stringify/lib/index.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZW1hcmstc3RyaW5naWZ5L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9yZW1hcmstc3RyaW5naWZ5L2luZGV4LmpzP2VhNTkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHtkZWZhdWx0fSBmcm9tICcuL2xpYi9pbmRleC5qcydcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/remark-stringify/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/remark-stringify/lib/index.js":
/*!****************************************************!*\
  !*** ./node_modules/remark-stringify/lib/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ remarkStringify; }\n/* harmony export */ });\n/* harmony import */ var mdast_util_to_markdown__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-to-markdown */ \"(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/index.js\");\n/**\n * @typedef {import('mdast').Root|import('mdast').Content} Node\n * @typedef {import('mdast-util-to-markdown').Options} ToMarkdownOptions\n * @typedef {Omit<ToMarkdownOptions, 'extensions'>} Options\n */\n\n\n\n/**\n * @this {import('unified').Processor}\n * @type {import('unified').Plugin<[Options?]|void[], Node, string>}\n */\nfunction remarkStringify(options) {\n  /** @type {import('unified').CompilerFunction<Node, string>} */\n  const compiler = (tree) => {\n    // Assume options.\n    const settings = /** @type {Options} */ (this.data('settings'))\n\n    return (0,mdast_util_to_markdown__WEBPACK_IMPORTED_MODULE_0__.toMarkdown)(\n      tree,\n      Object.assign({}, settings, options, {\n        // Note: this option is not in the readme.\n        // The goal is for it to be set by plugins on `data` instead of being\n        // passed by users.\n        extensions:\n          /** @type {ToMarkdownOptions['extensions']} */ (\n            this.data('toMarkdownExtensions')\n          ) || []\n      })\n    )\n  }\n\n  Object.assign(this, {Compiler: compiler})\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZW1hcmstc3RyaW5naWZ5L2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSw4Q0FBOEM7QUFDM0QsYUFBYSwwQ0FBMEM7QUFDdkQsYUFBYSx1Q0FBdUM7QUFDcEQ7O0FBRWlEOztBQUVqRDtBQUNBLFVBQVU7QUFDVixVQUFVO0FBQ1Y7QUFDZTtBQUNmLGFBQWEsa0RBQWtEO0FBQy9EO0FBQ0E7QUFDQSxnQ0FBZ0MsU0FBUzs7QUFFekMsV0FBVyxrRUFBVTtBQUNyQjtBQUNBLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQixpQ0FBaUM7QUFDdEQ7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBOztBQUVBLHVCQUF1QixtQkFBbUI7QUFDMUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL3JlbWFyay1zdHJpbmdpZnkvbGliL2luZGV4LmpzPzBjY2YiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLlJvb3R8aW1wb3J0KCdtZGFzdCcpLkNvbnRlbnR9IE5vZGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nKS5PcHRpb25zfSBUb01hcmtkb3duT3B0aW9uc1xuICogQHR5cGVkZWYge09taXQ8VG9NYXJrZG93bk9wdGlvbnMsICdleHRlbnNpb25zJz59IE9wdGlvbnNcbiAqL1xuXG5pbXBvcnQge3RvTWFya2Rvd259IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG5cbi8qKlxuICogQHRoaXMge2ltcG9ydCgndW5pZmllZCcpLlByb2Nlc3Nvcn1cbiAqIEB0eXBlIHtpbXBvcnQoJ3VuaWZpZWQnKS5QbHVnaW48W09wdGlvbnM/XXx2b2lkW10sIE5vZGUsIHN0cmluZz59XG4gKi9cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHJlbWFya1N0cmluZ2lmeShvcHRpb25zKSB7XG4gIC8qKiBAdHlwZSB7aW1wb3J0KCd1bmlmaWVkJykuQ29tcGlsZXJGdW5jdGlvbjxOb2RlLCBzdHJpbmc+fSAqL1xuICBjb25zdCBjb21waWxlciA9ICh0cmVlKSA9PiB7XG4gICAgLy8gQXNzdW1lIG9wdGlvbnMuXG4gICAgY29uc3Qgc2V0dGluZ3MgPSAvKiogQHR5cGUge09wdGlvbnN9ICovICh0aGlzLmRhdGEoJ3NldHRpbmdzJykpXG5cbiAgICByZXR1cm4gdG9NYXJrZG93bihcbiAgICAgIHRyZWUsXG4gICAgICBPYmplY3QuYXNzaWduKHt9LCBzZXR0aW5ncywgb3B0aW9ucywge1xuICAgICAgICAvLyBOb3RlOiB0aGlzIG9wdGlvbiBpcyBub3QgaW4gdGhlIHJlYWRtZS5cbiAgICAgICAgLy8gVGhlIGdvYWwgaXMgZm9yIGl0IHRvIGJlIHNldCBieSBwbHVnaW5zIG9uIGBkYXRhYCBpbnN0ZWFkIG9mIGJlaW5nXG4gICAgICAgIC8vIHBhc3NlZCBieSB1c2Vycy5cbiAgICAgICAgZXh0ZW5zaW9uczpcbiAgICAgICAgICAvKiogQHR5cGUge1RvTWFya2Rvd25PcHRpb25zWydleHRlbnNpb25zJ119ICovIChcbiAgICAgICAgICAgIHRoaXMuZGF0YSgndG9NYXJrZG93bkV4dGVuc2lvbnMnKVxuICAgICAgICAgICkgfHwgW11cbiAgICAgIH0pXG4gICAgKVxuICB9XG5cbiAgT2JqZWN0LmFzc2lnbih0aGlzLCB7Q29tcGlsZXI6IGNvbXBpbGVyfSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/remark-stringify/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/unist-util-visit/lib/index.js":
/*!****************************************************!*\
  !*** ./node_modules/unist-util-visit/lib/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CONTINUE: function() { return /* reexport safe */ unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_0__.CONTINUE; },\n/* harmony export */   EXIT: function() { return /* reexport safe */ unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_0__.EXIT; },\n/* harmony export */   SKIP: function() { return /* reexport safe */ unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_0__.SKIP; },\n/* harmony export */   visit: function() { return /* binding */ visit; }\n/* harmony export */ });\n/* harmony import */ var unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit-parents */ \"(app-pages-browser)/./node_modules/unist-util-visit/node_modules/unist-util-visit-parents/lib/index.js\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n * @typedef {import('unist-util-is').Test} Test\n * @typedef {import('unist-util-visit-parents').VisitorResult} VisitorResult\n */\n\n/**\n * Check if `Child` can be a child of `Ancestor`.\n *\n * Returns the ancestor when `Child` can be a child of `Ancestor`, or returns\n * `never`.\n *\n * @template {Node} Ancestor\n *   Node type.\n * @template {Node} Child\n *   Node type.\n * @typedef {(\n *   Ancestor extends Parent\n *     ? Child extends Ancestor['children'][number]\n *       ? Ancestor\n *       : never\n *     : never\n * )} ParentsOf\n */\n\n/**\n * @template {Node} [Visited=Node]\n *   Visited node type.\n * @template {Parent} [Ancestor=Parent]\n *   Ancestor type.\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform `parent`.\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of `parent` still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Visited extends Node ? number | null : never} index\n *   Index of `node` in `parent`.\n * @param {Ancestor extends Node ? Ancestor | null : never} parent\n *   Parent of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n */\n\n/**\n * Build a typed `Visitor` function from a node and all possible parents.\n *\n * It will infer which values are passed as `node` and which as `parent`.\n *\n * @template {Node} Visited\n *   Node type.\n * @template {Parent} Ancestor\n *   Parent type.\n * @typedef {Visitor<Visited, ParentsOf<Ancestor, Visited>>} BuildVisitorFromMatch\n */\n\n/**\n * Build a typed `Visitor` function from a list of descendants and a test.\n *\n * It will infer which values are passed as `node` and which as `parent`.\n *\n * @template {Node} Descendant\n *   Node type.\n * @template {Test} Check\n *   Test type.\n * @typedef {(\n *   BuildVisitorFromMatch<\n *     import('unist-util-visit-parents/complex-types.js').Matches<Descendant, Check>,\n *     Extract<Descendant, Parent>\n *   >\n * )} BuildVisitorFromDescendants\n */\n\n/**\n * Build a typed `Visitor` function from a tree and a test.\n *\n * It will infer which values are passed as `node` and which as `parent`.\n *\n * @template {Node} [Tree=Node]\n *   Node type.\n * @template {Test} [Check=string]\n *   Test type.\n * @typedef {(\n *   BuildVisitorFromDescendants<\n *     import('unist-util-visit-parents/complex-types.js').InclusiveDescendant<Tree>,\n *     Check\n *   >\n * )} BuildVisitor\n */\n\n\n\n/**\n * Visit nodes.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @param tree\n *   Tree to traverse.\n * @param test\n *   `unist-util-is`-compatible test\n * @param visitor\n *   Handle each node.\n * @param reverse\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns\n *   Nothing.\n */\nconst visit =\n  /**\n   * @type {(\n   *   (<Tree extends Node, Check extends Test>(tree: Tree, test: Check, visitor: BuildVisitor<Tree, Check>, reverse?: boolean | null | undefined) => void) &\n   *   (<Tree extends Node>(tree: Tree, visitor: BuildVisitor<Tree>, reverse?: boolean | null | undefined) => void)\n   * )}\n   */\n  (\n    /**\n     * @param {Node} tree\n     * @param {Test} test\n     * @param {Visitor} visitor\n     * @param {boolean | null | undefined} [reverse]\n     * @returns {void}\n     */\n    function (tree, test, visitor, reverse) {\n      if (typeof test === 'function' && typeof visitor !== 'function') {\n        reverse = visitor\n        visitor = test\n        test = null\n      }\n\n      (0,unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_0__.visitParents)(tree, test, overload, reverse)\n\n      /**\n       * @param {Node} node\n       * @param {Array<Parent>} parents\n       */\n      function overload(node, parents) {\n        const parent = parents[parents.length - 1]\n        return visitor(\n          node,\n          parent ? parent.children.indexOf(node) : null,\n          parent\n        )\n      }\n    }\n  )\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/unist-util-visit/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/unist-util-visit/node_modules/unist-util-is/lib/index.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/unist-util-visit/node_modules/unist-util-is/lib/index.js ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convert: function() { return /* binding */ convert; },\n/* harmony export */   is: function() { return /* binding */ is; }\n/* harmony export */ });\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n */\n\n/**\n * @typedef {Record<string, unknown>} Props\n * @typedef {null | undefined | string | Props | TestFunctionAnything | Array<string | Props | TestFunctionAnything>} Test\n *   Check for an arbitrary node, unaware of TypeScript inferral.\n *\n * @callback TestFunctionAnything\n *   Check if a node passes a test, unaware of TypeScript inferral.\n * @param {unknown} this\n *   The given context.\n * @param {Node} node\n *   A node.\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean | void}\n *   Whether this node passes the test.\n */\n\n/**\n * @template {Node} Kind\n *   Node type.\n * @typedef {Kind['type'] | Partial<Kind> | TestFunctionPredicate<Kind> | Array<Kind['type'] | Partial<Kind> | TestFunctionPredicate<Kind>>} PredicateTest\n *   Check for a node that can be inferred by TypeScript.\n */\n\n/**\n * Check if a node passes a certain test.\n *\n * @template {Node} Kind\n *   Node type.\n * @callback TestFunctionPredicate\n *   Complex test function for a node that can be inferred by TypeScript.\n * @param {Node} node\n *   A node.\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {node is Kind}\n *   Whether this node passes the test.\n */\n\n/**\n * @callback AssertAnything\n *   Check that an arbitrary value is a node, unaware of TypeScript inferral.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is a node and passes a test.\n */\n\n/**\n * Check if a node is a node and passes a certain node test.\n *\n * @template {Node} Kind\n *   Node type.\n * @callback AssertPredicate\n *   Check that an arbitrary value is a specific node, aware of TypeScript.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {node is Kind}\n *   Whether this is a node and passes a test.\n */\n\n/**\n * Check if `node` is a `Node` and whether it passes the given test.\n *\n * @param node\n *   Thing to check, typically `Node`.\n * @param test\n *   A check for a specific node.\n * @param index\n *   The node’s position in its parent.\n * @param parent\n *   The node’s parent.\n * @returns\n *   Whether `node` is a node and passes a test.\n */\nconst is =\n  /**\n   * @type {(\n   *   (() => false) &\n   *   (<Kind extends Node = Node>(node: unknown, test: PredicateTest<Kind>, index: number, parent: Parent, context?: unknown) => node is Kind) &\n   *   (<Kind extends Node = Node>(node: unknown, test: PredicateTest<Kind>, index?: null | undefined, parent?: null | undefined, context?: unknown) => node is Kind) &\n   *   ((node: unknown, test: Test, index: number, parent: Parent, context?: unknown) => boolean) &\n   *   ((node: unknown, test?: Test, index?: null | undefined, parent?: null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [node]\n     * @param {Test} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parent | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function is(node, test, index, parent, context) {\n      const check = convert(test)\n\n      if (\n        index !== undefined &&\n        index !== null &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite index')\n      }\n\n      if (\n        parent !== undefined &&\n        parent !== null &&\n        (!is(parent) || !parent.children)\n      ) {\n        throw new Error('Expected parent node')\n      }\n\n      if (\n        (parent === undefined || parent === null) !==\n        (index === undefined || index === null)\n      ) {\n        throw new Error('Expected both parent and index')\n      }\n\n      // @ts-expect-error Looks like a node.\n      return node && node.type && typeof node.type === 'string'\n        ? Boolean(check.call(context, node, index, parent))\n        : false\n    }\n  )\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param test\n *   *   when nullish, checks if `node` is a `Node`.\n *   *   when `string`, works like passing `(node) => node.type === test`.\n *   *   when `function` checks if function passed the node is true.\n *   *   when `object`, checks that all keys in test are in node, and that they have (strictly) equal values.\n *   *   when `array`, checks if any one of the subtests pass.\n * @returns\n *   An assertion.\n */\nconst convert =\n  /**\n   * @type {(\n   *   (<Kind extends Node>(test: PredicateTest<Kind>) => AssertPredicate<Kind>) &\n   *   ((test?: Test) => AssertAnything)\n   * )}\n   */\n  (\n    /**\n     * @param {Test} [test]\n     * @returns {AssertAnything}\n     */\n    function (test) {\n      if (test === undefined || test === null) {\n        return ok\n      }\n\n      if (typeof test === 'string') {\n        return typeFactory(test)\n      }\n\n      if (typeof test === 'object') {\n        return Array.isArray(test) ? anyFactory(test) : propsFactory(test)\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      throw new Error('Expected function, string, or object as test')\n    }\n  )\n\n/**\n * @param {Array<string | Props | TestFunctionAnything>} tests\n * @returns {AssertAnything}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<AssertAnything>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convert(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].call(this, ...parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn an object into a test for a node with a certain fields.\n *\n * @param {Props} check\n * @returns {AssertAnything}\n */\nfunction propsFactory(check) {\n  return castFactory(all)\n\n  /**\n   * @param {Node} node\n   * @returns {boolean}\n   */\n  function all(node) {\n    /** @type {string} */\n    let key\n\n    for (key in check) {\n      // @ts-expect-error: hush, it sure works as an index.\n      if (node[key] !== check[key]) return false\n    }\n\n    return true\n  }\n}\n\n/**\n * Turn a string into a test for a node with a certain type.\n *\n * @param {string} check\n * @returns {AssertAnything}\n */\nfunction typeFactory(check) {\n  return castFactory(type)\n\n  /**\n   * @param {Node} node\n   */\n  function type(node) {\n    return node && node.type === check\n  }\n}\n\n/**\n * Turn a custom test into a test for a node that passes that test.\n *\n * @param {TestFunctionAnything} check\n * @returns {AssertAnything}\n */\nfunction castFactory(check) {\n  return assertion\n\n  /**\n   * @this {unknown}\n   * @param {unknown} node\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function assertion(node, ...parameters) {\n    return Boolean(\n      node &&\n        typeof node === 'object' &&\n        'type' in node &&\n        // @ts-expect-error: fine.\n        Boolean(check.call(this, node, ...parameters))\n    )\n  }\n}\n\nfunction ok() {\n  return true\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/unist-util-visit/node_modules/unist-util-is/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/unist-util-visit/node_modules/unist-util-visit-parents/lib/color.browser.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/unist-util-visit/node_modules/unist-util-visit-parents/lib/color.browser.js ***!
  \**************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   color: function() { return /* binding */ color; }\n/* harmony export */ });\n/**\n * @param {string} d\n * @returns {string}\n */\nfunction color(d) {\n  return d\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91bmlzdC11dGlsLXZpc2l0L25vZGVfbW9kdWxlcy91bmlzdC11dGlsLXZpc2l0LXBhcmVudHMvbGliL2NvbG9yLmJyb3dzZXIuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvdW5pc3QtdXRpbC12aXNpdC9ub2RlX21vZHVsZXMvdW5pc3QtdXRpbC12aXNpdC1wYXJlbnRzL2xpYi9jb2xvci5icm93c2VyLmpzPzIzYmUiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAcGFyYW0ge3N0cmluZ30gZFxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNvbG9yKGQpIHtcbiAgcmV0dXJuIGRcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/unist-util-visit/node_modules/unist-util-visit-parents/lib/color.browser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/unist-util-visit/node_modules/unist-util-visit-parents/lib/index.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/unist-util-visit/node_modules/unist-util-visit-parents/lib/index.js ***!
  \******************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CONTINUE: function() { return /* binding */ CONTINUE; },\n/* harmony export */   EXIT: function() { return /* binding */ EXIT; },\n/* harmony export */   SKIP: function() { return /* binding */ SKIP; },\n/* harmony export */   visitParents: function() { return /* binding */ visitParents; }\n/* harmony export */ });\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-is */ \"(app-pages-browser)/./node_modules/unist-util-visit/node_modules/unist-util-is/lib/index.js\");\n/* harmony import */ var _color_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./color.js */ \"(app-pages-browser)/./node_modules/unist-util-visit/node_modules/unist-util-visit-parents/lib/color.browser.js\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n * @typedef {import('unist-util-is').Test} Test\n */\n\n/**\n * @typedef {boolean | 'skip'} Action\n *   Union of the action types.\n *\n * @typedef {number} Index\n *   Move to the sibling at `index` next (after node itself is completely\n *   traversed).\n *\n *   Useful if mutating the tree, such as removing the node the visitor is\n *   currently on, or any of its previous siblings.\n *   Results less than 0 or greater than or equal to `children.length` stop\n *   traversing the parent.\n *\n * @typedef {[(Action | null | undefined | void)?, (Index | null | undefined)?]} ActionTuple\n *   List with one or two values, the first an action, the second an index.\n *\n * @typedef {Action | ActionTuple | Index | null | undefined | void} VisitorResult\n *   Any value that can be returned from a visitor.\n */\n\n/**\n * @template {Node} [Visited=Node]\n *   Visited node type.\n * @template {Parent} [Ancestor=Parent]\n *   Ancestor type.\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform the parent of node (the last of `ancestors`).\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of an ancestor still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Array<Ancestor>} ancestors\n *   Ancestors of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   Tree type.\n * @template {Test} [Check=string]\n *   Test type.\n * @typedef {Visitor<import('./complex-types.js').Matches<import('./complex-types.js').InclusiveDescendant<Tree>, Check>, Extract<import('./complex-types.js').InclusiveDescendant<Tree>, Parent>>} BuildVisitor\n *   Build a typed `Visitor` function from a tree and a test.\n *\n *   It will infer which values are passed as `node` and which as `parents`.\n */\n\n\n\n\n/**\n * Continue traversing as normal.\n */\nconst CONTINUE = true\n\n/**\n * Stop traversing immediately.\n */\nconst EXIT = false\n\n/**\n * Do not traverse this node’s children.\n */\nconst SKIP = 'skip'\n\n/**\n * Visit nodes, with ancestral information.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @param tree\n *   Tree to traverse.\n * @param test\n *   `unist-util-is`-compatible test\n * @param visitor\n *   Handle each node.\n * @param reverse\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns\n *   Nothing.\n */\nconst visitParents =\n  /**\n   * @type {(\n   *   (<Tree extends Node, Check extends Test>(tree: Tree, test: Check, visitor: BuildVisitor<Tree, Check>, reverse?: boolean | null | undefined) => void) &\n   *   (<Tree extends Node>(tree: Tree, visitor: BuildVisitor<Tree>, reverse?: boolean | null | undefined) => void)\n   * )}\n   */\n  (\n    /**\n     * @param {Node} tree\n     * @param {Test} test\n     * @param {Visitor<Node>} visitor\n     * @param {boolean | null | undefined} [reverse]\n     * @returns {void}\n     */\n    function (tree, test, visitor, reverse) {\n      if (typeof test === 'function' && typeof visitor !== 'function') {\n        reverse = visitor\n        // @ts-expect-error no visitor given, so `visitor` is test.\n        visitor = test\n        test = null\n      }\n\n      const is = (0,unist_util_is__WEBPACK_IMPORTED_MODULE_0__.convert)(test)\n      const step = reverse ? -1 : 1\n\n      factory(tree, undefined, [])()\n\n      /**\n       * @param {Node} node\n       * @param {number | undefined} index\n       * @param {Array<Parent>} parents\n       */\n      function factory(node, index, parents) {\n        /** @type {Record<string, unknown>} */\n        // @ts-expect-error: hush\n        const value = node && typeof node === 'object' ? node : {}\n\n        if (typeof value.type === 'string') {\n          const name =\n            // `hast`\n            typeof value.tagName === 'string'\n              ? value.tagName\n              : // `xast`\n              typeof value.name === 'string'\n              ? value.name\n              : undefined\n\n          Object.defineProperty(visit, 'name', {\n            value:\n              'node (' + (0,_color_js__WEBPACK_IMPORTED_MODULE_1__.color)(node.type + (name ? '<' + name + '>' : '')) + ')'\n          })\n        }\n\n        return visit\n\n        function visit() {\n          /** @type {ActionTuple} */\n          let result = []\n          /** @type {ActionTuple} */\n          let subresult\n          /** @type {number} */\n          let offset\n          /** @type {Array<Parent>} */\n          let grandparents\n\n          if (!test || is(node, index, parents[parents.length - 1] || null)) {\n            result = toResult(visitor(node, parents))\n\n            if (result[0] === EXIT) {\n              return result\n            }\n          }\n\n          // @ts-expect-error looks like a parent.\n          if (node.children && result[0] !== SKIP) {\n            // @ts-expect-error looks like a parent.\n            offset = (reverse ? node.children.length : -1) + step\n            // @ts-expect-error looks like a parent.\n            grandparents = parents.concat(node)\n\n            // @ts-expect-error looks like a parent.\n            while (offset > -1 && offset < node.children.length) {\n              // @ts-expect-error looks like a parent.\n              subresult = factory(node.children[offset], offset, grandparents)()\n\n              if (subresult[0] === EXIT) {\n                return subresult\n              }\n\n              offset =\n                typeof subresult[1] === 'number' ? subresult[1] : offset + step\n            }\n          }\n\n          return result\n        }\n      }\n    }\n  )\n\n/**\n * Turn a return value into a clean result.\n *\n * @param {VisitorResult} value\n *   Valid return values from visitors.\n * @returns {ActionTuple}\n *   Clean result.\n */\nfunction toResult(value) {\n  if (Array.isArray(value)) {\n    return value\n  }\n\n  if (typeof value === 'number') {\n    return [CONTINUE, value]\n  }\n\n  return [value]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/unist-util-visit/node_modules/unist-util-visit-parents/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/zwitch/index.js":
/*!**************************************!*\
  !*** ./node_modules/zwitch/index.js ***!
  \**************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   zwitch: function() { return /* binding */ zwitch; }\n/* harmony export */ });\n/**\n * @callback Handler\n *   Handle a value, with a certain ID field set to a certain value.\n *   The ID field is passed to `zwitch`, and it’s value is this function’s\n *   place on the `handlers` record.\n * @param {...any} parameters\n *   Arbitrary parameters passed to the zwitch.\n *   The first will be an object with a certain ID field set to a certain value.\n * @returns {any}\n *   Anything!\n */\n\n/**\n * @callback UnknownHandler\n *   Handle values that do have a certain ID field, but it’s set to a value\n *   that is not listed in the `handlers` record.\n * @param {unknown} value\n *   An object with a certain ID field set to an unknown value.\n * @param {...any} rest\n *   Arbitrary parameters passed to the zwitch.\n * @returns {any}\n *   Anything!\n */\n\n/**\n * @callback InvalidHandler\n *   Handle values that do not have a certain ID field.\n * @param {unknown} value\n *   Any unknown value.\n * @param {...any} rest\n *   Arbitrary parameters passed to the zwitch.\n * @returns {void|null|undefined|never}\n *   This should crash or return nothing.\n */\n\n/**\n * @template {InvalidHandler} [Invalid=InvalidHandler]\n * @template {UnknownHandler} [Unknown=UnknownHandler]\n * @template {Record<string, Handler>} [Handlers=Record<string, Handler>]\n * @typedef Options\n *   Configuration (required).\n * @property {Invalid} [invalid]\n *   Handler to use for invalid values.\n * @property {Unknown} [unknown]\n *   Handler to use for unknown values.\n * @property {Handlers} [handlers]\n *   Handlers to use.\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * Handle values based on a field.\n *\n * @template {InvalidHandler} [Invalid=InvalidHandler]\n * @template {UnknownHandler} [Unknown=UnknownHandler]\n * @template {Record<string, Handler>} [Handlers=Record<string, Handler>]\n * @param {string} key\n *   Field to switch on.\n * @param {Options<Invalid, Unknown, Handlers>} [options]\n *   Configuration (required).\n * @returns {{unknown: Unknown, invalid: Invalid, handlers: Handlers, (...parameters: Parameters<Handlers[keyof Handlers]>): ReturnType<Handlers[keyof Handlers]>, (...parameters: Parameters<Unknown>): ReturnType<Unknown>}}\n */\nfunction zwitch(key, options) {\n  const settings = options || {}\n\n  /**\n   * Handle one value.\n   *\n   * Based on the bound `key`, a respective handler will be called.\n   * If `value` is not an object, or doesn’t have a `key` property, the special\n   * “invalid” handler will be called.\n   * If `value` has an unknown `key`, the special “unknown” handler will be\n   * called.\n   *\n   * All arguments, and the context object, are passed through to the handler,\n   * and it’s result is returned.\n   *\n   * @this {unknown}\n   *   Any context object.\n   * @param {unknown} [value]\n   *   Any value.\n   * @param {...unknown} parameters\n   *   Arbitrary parameters passed to the zwitch.\n   * @property {Handler} invalid\n   *   Handle for values that do not have a certain ID field.\n   * @property {Handler} unknown\n   *   Handle values that do have a certain ID field, but it’s set to a value\n   *   that is not listed in the `handlers` record.\n   * @property {Handlers} handlers\n   *   Record of handlers.\n   * @returns {unknown}\n   *   Anything.\n   */\n  function one(value, ...parameters) {\n    /** @type {Handler|undefined} */\n    let fn = one.invalid\n    const handlers = one.handlers\n\n    if (value && own.call(value, key)) {\n      // @ts-expect-error Indexable.\n      const id = String(value[key])\n      // @ts-expect-error Indexable.\n      fn = own.call(handlers, id) ? handlers[id] : one.unknown\n    }\n\n    if (fn) {\n      return fn.call(this, value, ...parameters)\n    }\n  }\n\n  one.handlers = settings.handlers || {}\n  one.invalid = settings.invalid\n  one.unknown = settings.unknown\n\n  // @ts-expect-error: matches!\n  return one\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/zwitch/index.js\n"));

/***/ })

}]);