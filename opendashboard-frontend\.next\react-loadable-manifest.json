{"..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> @emoji-mart/data": {"id": "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> @emoji-mart/data", "files": ["static/chunks/_app-pages-browser_node_modules_emoji-mart_data_sets_15_native_json.js"]}, "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> emoji-mart": {"id": "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> emoji-mart", "files": ["static/chunks/_app-pages-browser_node_modules_emoji-mart_dist_module_js.js"]}, "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> hast-util-from-dom": {"id": "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> hast-util-from-dom", "files": ["static/chunks/_app-pages-browser_node_modules_hast-util-from-dom_index_js.js"]}, "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> rehype-format": {"id": "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> rehype-format", "files": ["static/chunks/_app-pages-browser_node_modules_rehype-format_index_js.js"]}, "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> rehype-parse": {"id": "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> rehype-parse", "files": ["static/chunks/_app-pages-browser_node_modules_rehype-parse_index_js.js"]}, "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> rehype-remark": {"id": "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> rehype-remark", "files": ["static/chunks/_app-pages-browser_node_modules_rehype-remark_index_js.js"]}, "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> rehype-stringify": {"id": "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> rehype-stringify", "files": ["static/chunks/_app-pages-browser_node_modules_rehype-stringify_index_js.js"]}, "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> remark-gfm": {"id": "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> remark-gfm", "files": ["static/chunks/_app-pages-browser_node_modules_remark-gfm_index_js.js"]}, "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> remark-parse": {"id": "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> remark-parse", "files": ["static/chunks/_app-pages-browser_node_modules_remark-parse_index_js.js"]}, "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> remark-rehype": {"id": "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> remark-rehype", "files": ["static/chunks/_app-pages-browser_node_modules_remark-rehype_index_js.js"]}, "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> remark-stringify": {"id": "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> remark-stringify", "files": ["static/chunks/_app-pages-browser_node_modules_remark-stringify_index_js.js"]}, "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> unified": {"id": "..\\node_modules\\@blocknote\\core\\dist\\blocknote.js -> unified", "files": ["static/chunks/_app-pages-browser_node_modules_unified_index_js.js"]}, "components\\custom-ui\\iconPicker.tsx -> emoji-picker-react": {"id": "components\\custom-ui\\iconPicker.tsx -> emoji-picker-react", "files": ["static/chunks/_app-pages-browser_node_modules_emoji-picker-react_dist_emoji-picker-react_esm_js.js"]}, "components\\custom-ui\\taggableInput.tsx -> ./tagInput": {"id": "components\\custom-ui\\taggableInput.tsx -> ./tagInput", "files": ["static/chunks/_app-pages-browser_src_components_custom-ui_tagInput_tsx.js"]}, "components\\workspace\\main\\record\\components\\recordExtras.tsx -> @/components/workspace/main/record/components/recordOverview": {"id": "components\\workspace\\main\\record\\components\\recordExtras.tsx -> @/components/workspace/main/record/components/recordOverview", "files": ["static/chunks/_app-pages-browser_src_components_workspace_main_record_components_recordOverview_tsx.js"]}, "components\\workspace\\main\\views\\viewRender.tsx -> @/components/workspace/main/views/board/board": {"id": "components\\workspace\\main\\views\\viewRender.tsx -> @/components/workspace/main/views/board/board", "files": ["static/css/_app-pages-browser_src_components_workspace_main_views_board_board_tsx.css", "static/chunks/_app-pages-browser_src_components_workspace_main_views_board_board_tsx.js"]}, "components\\workspace\\main\\views\\viewRender.tsx -> @/components/workspace/main/views/calendar/index": {"id": "components\\workspace\\main\\views\\viewRender.tsx -> @/components/workspace/main/views/calendar/index", "files": ["static/css/_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx.css", "static/chunks/_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx.js"]}, "components\\workspace\\main\\views\\viewRender.tsx -> @/components/workspace/main/views/dashboard/dashboard": {"id": "components\\workspace\\main\\views\\viewRender.tsx -> @/components/workspace/main/views/dashboard/dashboard", "files": ["static/css/_app-pages-browser_src_components_workspace_main_views_dashboard_dashboard_tsx.css", "static/chunks/_app-pages-browser_src_components_workspace_main_views_dashboard_dashboard_tsx.js"]}, "components\\workspace\\main\\views\\viewRender.tsx -> @/components/workspace/main/views/document/document": {"id": "components\\workspace\\main\\views\\viewRender.tsx -> @/components/workspace/main/views/document/document", "files": ["static/css/_app-pages-browser_src_components_workspace_main_views_document_document_tsx.css", "static/chunks/_app-pages-browser_src_components_workspace_main_views_document_document_tsx.js"]}, "components\\workspace\\main\\views\\viewRender.tsx -> @/components/workspace/main/views/form/form": {"id": "components\\workspace\\main\\views\\viewRender.tsx -> @/components/workspace/main/views/form/form", "files": ["static/css/_app-pages-browser_src_components_workspace_main_views_form_form_tsx.css", "static/chunks/_app-pages-browser_src_components_workspace_main_views_form_form_tsx.js"]}, "components\\workspace\\main\\views\\viewRender.tsx -> @/components/workspace/main/views/list": {"id": "components\\workspace\\main\\views\\viewRender.tsx -> @/components/workspace/main/views/list", "files": ["static/css/_app-pages-browser_src_components_workspace_main_views_list_index_tsx.css", "static/chunks/_app-pages-browser_src_components_workspace_main_views_list_index_tsx.js"]}, "components\\workspace\\main\\views\\viewRender.tsx -> @/components/workspace/main/views/summaryTable/summaryTableView": {"id": "components\\workspace\\main\\views\\viewRender.tsx -> @/components/workspace/main/views/summaryTable/summaryTableView", "files": ["static/css/_app-pages-browser_src_components_workspace_main_views_summaryTable_summaryTableView_tsx.css", "static/chunks/_app-pages-browser_src_components_workspace_main_views_summaryTable_summaryTableView_tsx.js"]}, "components\\workspace\\main\\views\\viewRender.tsx -> @/components/workspace/main/views/table/index": {"id": "components\\workspace\\main\\views\\viewRender.tsx -> @/components/workspace/main/views/table/index", "files": ["static/css/_app-pages-browser_src_components_workspace_main_views_form_components_element_linked_tsx.css", "static/chunks/_app-pages-browser_src_components_workspace_main_views_form_components_element_linked_tsx.js"]}, "utils\\buttonAction.ts -> @/api/workflow": {"id": "utils\\buttonAction.ts -> @/api/workflow", "files": []}, "utils\\buttonAction.ts -> @/api/workspace": {"id": "utils\\buttonAction.ts -> @/api/workspace", "files": []}}