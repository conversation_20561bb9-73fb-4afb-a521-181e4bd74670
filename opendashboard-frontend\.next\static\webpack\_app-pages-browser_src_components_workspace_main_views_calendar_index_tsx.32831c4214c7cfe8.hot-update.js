"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonthView: function() { return /* binding */ MonthView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CalendarSideCard */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarSideCard.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst DayCell = (param)=>{\n    let { date, children, onClick, isCurrentMonth } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable)({\n        id: \"daycell-\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(date, \"yyyy-MM-dd\")),\n        data: {\n            date: date,\n            type: \"daycell\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        onClick: onClick,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[120px] lg:p-3 lg:min-h-[140px]\", isCurrentMonth ? \"bg-white hover:bg-neutral-50\" : \"bg-neutral-100 hover:bg-neutral-200\", isOver && \"bg-blue-50 border-blue-200\"),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DayCell, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable\n    ];\n});\n_c = DayCell;\n// My own calculation function for processing events\nconst useMonthEvents = (weeks, events)=>{\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const eventsByDay = new Map();\n        const eventsByWeek = new Map();\n        // Process each week\n        weeks.forEach((week, weekIndex)=>{\n            const weekStart = week[0];\n            const weekEnd = week[6];\n            // Find all events that overlap with this week\n            const weekEvents = events.filter((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                return eventStart <= weekEnd && eventEnd >= weekStart;\n            });\n            // Process each day in the week\n            week.forEach((day, dayIndex)=>{\n                const dayKey = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"yyyy-MM-dd\");\n                const dayEvents = [];\n                weekEvents.forEach((event)=>{\n                    const eventStart = new Date(event.start);\n                    const eventEnd = new Date(event.end);\n                    // Check if this event appears on this day\n                    const isEventOnThisDay = eventStart <= day && eventEnd >= day;\n                    if (isEventOnThisDay) {\n                        // Calculate event properties for this day\n                        const isStartDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(eventStart, day);\n                        const isEndDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(eventEnd, day);\n                        const isMultiDay = !(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(eventStart, eventEnd);\n                        // Calculate span within this week\n                        const weekStartIndex = week.findIndex((d)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(d, eventStart));\n                        const weekEndIndex = week.findIndex((d)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(eventEnd, d));\n                        const startInWeek = weekStartIndex !== -1 ? weekStartIndex : 0;\n                        const endInWeek = weekEndIndex !== -1 ? weekEndIndex : 6;\n                        const spanInWeek = endInWeek - startInWeek + 1;\n                        dayEvents.push({\n                            event,\n                            dayIndex,\n                            isStartDay,\n                            isEndDay,\n                            isMultiDay,\n                            spanInWeek,\n                            showTitle: isStartDay || dayIndex === 0 && !isStartDay,\n                            isDraggable: isStartDay,\n                            row: 0,\n                            colSpan: spanInWeek\n                        });\n                    }\n                });\n                // Sort events by start time and assign rows\n                const sortedDayEvents = dayEvents.sort((a, b)=>new Date(a.event.start).getTime() - new Date(b.event.start).getTime());\n                // Assign row positions (simple stacking for now)\n                sortedDayEvents.forEach((event, index)=>{\n                    event.row = index;\n                });\n                eventsByDay.set(dayKey, sortedDayEvents);\n            });\n            // Store week events for spanning calculations\n            eventsByWeek.set(weekIndex, weekEvents);\n        });\n        return {\n            eventsByDay,\n            eventsByWeek\n        };\n    }, [\n        weeks,\n        events\n    ]);\n};\n_s1(useMonthEvents, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\nconst MonthView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, handleEventClick, activeDragData } = param;\n    _s2();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord)();\n    const isInRecordTab = !!maybeRecord;\n    // Memoize month calculations\n    const monthCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const monthStart = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(selectedDate);\n        const monthEnd = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(selectedDate);\n        const startDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(monthStart, {\n            weekStartsOn: 0\n        });\n        const endDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(monthEnd, {\n            weekStartsOn: 0\n        });\n        const days = [];\n        let day = startDay;\n        while(day <= endDay){\n            days.push(day);\n            day = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(day, 1);\n        }\n        const weeks = [];\n        for(let i = 0; i < days.length; i += 7){\n            weeks.push(days.slice(i, i + 7));\n        }\n        return {\n            monthStart,\n            monthEnd,\n            startDay,\n            endDay,\n            days,\n            weeks\n        };\n    }, [\n        selectedDate\n    ]);\n    // Memoize month events\n    const monthEvents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>events.filter((event)=>{\n            const eventStart = new Date(event.start);\n            return eventStart >= monthCalculations.startDay && eventStart <= monthCalculations.endDay;\n        }), [\n        events,\n        monthCalculations.startDay,\n        monthCalculations.endDay\n    ]);\n    const positionedEventsByWeek = useMonthEvents(monthCalculations.weeks, events);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 border-b border-neutral-300 bg-white\",\n                    children: [\n                        \"Sunday\",\n                        \"Monday\",\n                        \"Tuesday\",\n                        \"Wednesday\",\n                        \"Thursday\",\n                        \"Friday\",\n                        \"Saturday\"\n                    ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                            children: dayName.substring(0, 3)\n                        }, dayName, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_7__.NoEvents, {\n                    title: \"No events this month\",\n                    message: \"\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, \"MMMM yyyy\"), \" is completely free. Start planning your month!\"),\n                    showCreateButton: canEditData,\n                    onCreate: ()=>openAddEventForm(selectedDate)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n            lineNumber: 194,\n            columnNumber: 5\n        }, undefined);\n    // Render day cell content\n    const renderDayCellContent = (day, dayEvents)=>{\n        const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n        const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(day);\n        const MAX_VISIBLE_EVENTS = 4;\n        const ROW_HEIGHT = 28;\n        const sortedEvents = dayEvents.sort((a, b)=>a.row - b.row);\n        const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);\n        const hasMore = sortedEvents.length > MAX_VISIBLE_EVENTS;\n        // To fix the gap after the \"+more\" link, we must calculate the container's height\n        // instead of letting it expand. The link is placed at the start of the 5th row,\n        // and we'll give it 20px of height.\n        const maxRow = visibleEvents.reduce((max, event)=>Math.max(max, event.row), -1);\n        const containerHeight = hasMore ? MAX_VISIBLE_EVENTS * ROW_HEIGHT + 20 : (maxRow + 1) * ROW_HEIGHT;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\", isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"),\n                            children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, \"d\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, undefined),\n                        canEditData && isCurrentMonth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            className: \"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\",\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setTimeout(()=>openAddEventForm(day), 150);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-3 w-3 text-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    style: {\n                        height: \"\".concat(containerHeight, \"px\")\n                    },\n                    children: [\n                        visibleEvents.map((pe)=>{\n                            var _activeDragData_payload;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute\",\n                                style: {\n                                    top: \"\".concat(pe.row * ROW_HEIGHT, \"px\"),\n                                    left: \"2px\",\n                                    width: pe.colSpan > 1 ? \"calc(\".concat(pe.colSpan * 100, \"% + \").concat((pe.colSpan - 1) * 19, \"px)\") : \"calc(100% - 4px)\",\n                                    // zIndex: pe.colSpan > 1 ? 10 : 1,\n                                    zIndex: 10 + pe.row\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__.CalendarEventItem, {\n                                    event: pe.event,\n                                    view: \"month\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        setSelectedEvent(pe.event.id);\n                                        handleEventClick(pe.event);\n                                    },\n                                    isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === pe.event.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, pe.event.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 13\n                            }, undefined);\n                        }),\n                        hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-black hover:text-black font-medium text-xs cursor-pointer\",\n                            style: {\n                                position: \"absolute\",\n                                top: \"\".concat(MAX_VISIBLE_EVENTS * ROW_HEIGHT, \"px\"),\n                                left: \"2px\"\n                            },\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setSelectedDate(day);\n                            },\n                            children: [\n                                \"+ \",\n                                sortedEvents.length - MAX_VISIBLE_EVENTS,\n                                \" more\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true);\n    };\n    // Render main view\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-background flex flex-col lg:flex-row\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\",\n                        children: [\n                            \"Sunday\",\n                            \"Monday\",\n                            \"Tuesday\",\n                            \"Wednesday\",\n                            \"Thursday\",\n                            \"Friday\",\n                            \"Saturday\"\n                        ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                                children: dayName.substring(0, 3)\n                            }, dayName, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-7 border-neutral-300 border-b\",\n                            children: monthCalculations.weeks.map((week, weekIndex)=>week.map((day, dayIndex)=>{\n                                    const weekEvents = positionedEventsByWeek.get(weekIndex) || [];\n                                    const dayEvents = weekEvents.filter((pe)=>pe.startDayIndex === dayIndex);\n                                    const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DayCell, {\n                                        date: day,\n                                        isCurrentMonth: isCurrentMonth,\n                                        onClick: ()=>setSelectedDate(day),\n                                        children: renderDayCellContent(day, dayEvents)\n                                    }, \"\".concat(weekIndex, \"-\").concat(dayIndex), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__.CalendarSideCard, {\n                selectedDate: selectedDate,\n                events: events,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 347,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 308,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(MonthView, \"LfpiC9GNlfiXFS91dl5Hp92L/ME=\", false, function() {\n    return [\n        _providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord,\n        useMonthEvents\n    ];\n});\n_c1 = MonthView;\nfunction isMultiDay(event) {\n    return !(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(new Date(event.start), new Date(event.end));\n}\nvar _c, _c1;\n$RefreshReg$(_c, \"DayCell\");\n$RefreshReg$(_c1, \"MonthView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ })

});