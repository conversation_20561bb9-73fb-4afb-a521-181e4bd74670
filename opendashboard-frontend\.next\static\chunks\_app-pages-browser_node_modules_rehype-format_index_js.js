"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_rehype-format_index_js"],{

/***/ "(app-pages-browser)/./node_modules/hast-util-embedded/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/hast-util-embedded/lib/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   embedded: function() { return /* binding */ embedded; }\n/* harmony export */ });\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(app-pages-browser)/./node_modules/hast-util-is-element/lib/index.js\");\n\n\n/**\n * Check if a node is a *embedded content*.\n *\n * @param value\n *   Thing to check (typically `Node`).\n * @returns\n *   Whether `value` is an element considered embedded content.\n *\n *   The elements `audio`, `canvas`, `embed`, `iframe`, `img`, `math`,\n *   `object`, `picture`, `svg`, and `video` are embedded content.\n */\nconst embedded = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)(\n  /**\n   * @param element\n   * @returns {element is {tagName: 'audio' | 'canvas' | 'embed' | 'iframe' | 'img' | 'math' | 'object' | 'picture' | 'svg' | 'video'}}\n   */\n  function (element) {\n    return (\n      element.tagName === 'audio' ||\n      element.tagName === 'canvas' ||\n      element.tagName === 'embed' ||\n      element.tagName === 'iframe' ||\n      element.tagName === 'img' ||\n      element.tagName === 'math' ||\n      element.tagName === 'object' ||\n      element.tagName === 'picture' ||\n      element.tagName === 'svg' ||\n      element.tagName === 'video'\n    )\n  }\n)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtZW1iZWRkZWQvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW1EOztBQUVuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ08saUJBQWlCLG9FQUFjO0FBQ3RDO0FBQ0E7QUFDQSxlQUFlLFlBQVk7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC1lbWJlZGRlZC9saWIvaW5kZXguanM/MTU2NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2NvbnZlcnRFbGVtZW50fSBmcm9tICdoYXN0LXV0aWwtaXMtZWxlbWVudCdcblxuLyoqXG4gKiBDaGVjayBpZiBhIG5vZGUgaXMgYSAqZW1iZWRkZWQgY29udGVudCouXG4gKlxuICogQHBhcmFtIHZhbHVlXG4gKiAgIFRoaW5nIHRvIGNoZWNrICh0eXBpY2FsbHkgYE5vZGVgKS5cbiAqIEByZXR1cm5zXG4gKiAgIFdoZXRoZXIgYHZhbHVlYCBpcyBhbiBlbGVtZW50IGNvbnNpZGVyZWQgZW1iZWRkZWQgY29udGVudC5cbiAqXG4gKiAgIFRoZSBlbGVtZW50cyBgYXVkaW9gLCBgY2FudmFzYCwgYGVtYmVkYCwgYGlmcmFtZWAsIGBpbWdgLCBgbWF0aGAsXG4gKiAgIGBvYmplY3RgLCBgcGljdHVyZWAsIGBzdmdgLCBhbmQgYHZpZGVvYCBhcmUgZW1iZWRkZWQgY29udGVudC5cbiAqL1xuZXhwb3J0IGNvbnN0IGVtYmVkZGVkID0gY29udmVydEVsZW1lbnQoXG4gIC8qKlxuICAgKiBAcGFyYW0gZWxlbWVudFxuICAgKiBAcmV0dXJucyB7ZWxlbWVudCBpcyB7dGFnTmFtZTogJ2F1ZGlvJyB8ICdjYW52YXMnIHwgJ2VtYmVkJyB8ICdpZnJhbWUnIHwgJ2ltZycgfCAnbWF0aCcgfCAnb2JqZWN0JyB8ICdwaWN0dXJlJyB8ICdzdmcnIHwgJ3ZpZGVvJ319XG4gICAqL1xuICBmdW5jdGlvbiAoZWxlbWVudCkge1xuICAgIHJldHVybiAoXG4gICAgICBlbGVtZW50LnRhZ05hbWUgPT09ICdhdWRpbycgfHxcbiAgICAgIGVsZW1lbnQudGFnTmFtZSA9PT0gJ2NhbnZhcycgfHxcbiAgICAgIGVsZW1lbnQudGFnTmFtZSA9PT0gJ2VtYmVkJyB8fFxuICAgICAgZWxlbWVudC50YWdOYW1lID09PSAnaWZyYW1lJyB8fFxuICAgICAgZWxlbWVudC50YWdOYW1lID09PSAnaW1nJyB8fFxuICAgICAgZWxlbWVudC50YWdOYW1lID09PSAnbWF0aCcgfHxcbiAgICAgIGVsZW1lbnQudGFnTmFtZSA9PT0gJ29iamVjdCcgfHxcbiAgICAgIGVsZW1lbnQudGFnTmFtZSA9PT0gJ3BpY3R1cmUnIHx8XG4gICAgICBlbGVtZW50LnRhZ05hbWUgPT09ICdzdmcnIHx8XG4gICAgICBlbGVtZW50LnRhZ05hbWUgPT09ICd2aWRlbydcbiAgICApXG4gIH1cbilcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-embedded/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-format/lib/index.js":
/*!****************************************************!*\
  !*** ./node_modules/hast-util-format/lib/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   format: function() { return /* binding */ format; }\n/* harmony export */ });\n/* harmony import */ var hast_util_embedded__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! hast-util-embedded */ \"(app-pages-browser)/./node_modules/hast-util-embedded/lib/index.js\");\n/* harmony import */ var hast_util_minify_whitespace__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-minify-whitespace */ \"(app-pages-browser)/./node_modules/hast-util-minify-whitespace/lib/index.js\");\n/* harmony import */ var hast_util_phrasing__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! hast-util-phrasing */ \"(app-pages-browser)/./node_modules/hast-util-phrasing/lib/index.js\");\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-whitespace */ \"(app-pages-browser)/./node_modules/hast-util-whitespace/lib/index.js\");\n/* harmony import */ var html_whitespace_sensitive_tag_names__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! html-whitespace-sensitive-tag-names */ \"(app-pages-browser)/./node_modules/html-whitespace-sensitive-tag-names/lib/index.js\");\n/* harmony import */ var unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit-parents */ \"(app-pages-browser)/./node_modules/unist-util-visit-parents/lib/index.js\");\n/**\n * @import {Nodes, RootContent, Root} from 'hast'\n * @import {BuildVisitor} from 'unist-util-visit-parents'\n * @import {Options, State} from './types.js'\n */\n\n\n\n\n\n\n\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Format whitespace in HTML.\n *\n * @param {Root} tree\n *   Tree.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nfunction format(tree, options) {\n  const settings = options || emptyOptions\n\n  /** @type {State} */\n  const state = {\n    blanks: settings.blanks || [],\n    head: false,\n    indentInitial: settings.indentInitial !== false,\n    indent:\n      typeof settings.indent === 'number'\n        ? ' '.repeat(settings.indent)\n        : typeof settings.indent === 'string'\n          ? settings.indent\n          : '  '\n  }\n\n  ;(0,hast_util_minify_whitespace__WEBPACK_IMPORTED_MODULE_0__.minifyWhitespace)(tree, {newlines: true})\n\n  ;(0,unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_1__.visitParents)(tree, visitor)\n\n  /**\n   * @type {BuildVisitor<Root>}\n   */\n  function visitor(node, parents) {\n    if (!('children' in node)) {\n      return\n    }\n\n    if (node.type === 'element' && node.tagName === 'head') {\n      state.head = true\n    }\n\n    if (state.head && node.type === 'element' && node.tagName === 'body') {\n      state.head = false\n    }\n\n    if (\n      node.type === 'element' &&\n      html_whitespace_sensitive_tag_names__WEBPACK_IMPORTED_MODULE_2__.whitespaceSensitiveTagNames.includes(node.tagName)\n    ) {\n      return unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_1__.SKIP\n    }\n\n    // Don’t indent content of whitespace-sensitive nodes / inlines.\n    if (node.children.length === 0 || !padding(state, node)) {\n      return\n    }\n\n    let level = parents.length\n\n    if (!state.indentInitial) {\n      level--\n    }\n\n    let eol = false\n\n    // Indent newlines in `text`.\n    for (const child of node.children) {\n      if (child.type === 'comment' || child.type === 'text') {\n        if (child.value.includes('\\n')) {\n          eol = true\n        }\n\n        child.value = child.value.replace(\n          / *\\n/g,\n          '$&' + state.indent.repeat(level)\n        )\n      }\n    }\n\n    /** @type {Array<RootContent>} */\n    const result = []\n    /** @type {RootContent | undefined} */\n    let previous\n\n    for (const child of node.children) {\n      if (padding(state, child) || (eol && !previous)) {\n        addBreak(result, level, child)\n        eol = true\n      }\n\n      previous = child\n      result.push(child)\n    }\n\n    if (previous && (eol || padding(state, previous))) {\n      // Ignore trailing whitespace (if that already existed), as we’ll add\n      // properly indented whitespace.\n      if ((0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_3__.whitespace)(previous)) {\n        result.pop()\n        previous = result[result.length - 1]\n      }\n\n      addBreak(result, level - 1)\n    }\n\n    node.children = result\n  }\n\n  /**\n   * @param {Array<RootContent>} list\n   *   Nodes.\n   * @param {number} level\n   *   Indentation level.\n   * @param {RootContent | undefined} [next]\n   *   Next node.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function addBreak(list, level, next) {\n    const tail = list[list.length - 1]\n    const previous = tail && (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_3__.whitespace)(tail) ? list[list.length - 2] : tail\n    const replace =\n      (blank(state, previous) && blank(state, next) ? '\\n\\n' : '\\n') +\n      state.indent.repeat(Math.max(level, 0))\n\n    if (tail && tail.type === 'text') {\n      tail.value = (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_3__.whitespace)(tail) ? replace : tail.value + replace\n    } else {\n      list.push({type: 'text', value: replace})\n    }\n  }\n}\n\n/**\n * @param {State} state\n *   Info passed around.\n * @param {Nodes | undefined} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is a blank.\n */\nfunction blank(state, node) {\n  return Boolean(\n    node &&\n      node.type === 'element' &&\n      state.blanks.length > 0 &&\n      state.blanks.includes(node.tagName)\n  )\n}\n\n/**\n * @param {State} state\n *   Info passed around.\n * @param {Nodes} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` should be padded.\n */\nfunction padding(state, node) {\n  return (\n    node.type === 'root' ||\n    (node.type === 'element'\n      ? state.head ||\n        node.tagName === 'script' ||\n        (0,hast_util_embedded__WEBPACK_IMPORTED_MODULE_4__.embedded)(node) ||\n        !(0,hast_util_phrasing__WEBPACK_IMPORTED_MODULE_5__.phrasing)(node)\n      : false)\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-format/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-has-property/lib/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/hast-util-has-property/lib/index.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasProperty: function() { return /* binding */ hasProperty; }\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Nodes} Nodes\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * Check if `node` is an element and has a `name` property.\n *\n * @template {string} Key\n *   Type of key.\n * @param {Nodes} node\n *   Node to check (typically `Element`).\n * @param {Key} name\n *   Property name to check.\n * @returns {node is Element & {properties: Record<Key, Array<number | string> | number | string | true>}}}\n *   Whether `node` is an element that has a `name` property.\n *\n *   Note: see <https://github.com/DefinitelyTyped/DefinitelyTyped/blob/27c9274/types/hast/index.d.ts#L37C29-L37C98>.\n */\nfunction hasProperty(node, name) {\n  const value =\n    node.type === 'element' &&\n    own.call(node.properties, name) &&\n    node.properties[name]\n\n  return value !== null && value !== undefined && value !== false\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtaGFzLXByb3BlcnR5L2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLHdCQUF3QjtBQUNyQyxhQUFhLHNCQUFzQjtBQUNuQzs7QUFFQSxjQUFjOztBQUVkO0FBQ0E7QUFDQTtBQUNBLGNBQWMsUUFBUTtBQUN0QjtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsS0FBSztBQUNoQjtBQUNBLGFBQWEsbUJBQW1CO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtaGFzLXByb3BlcnR5L2xpYi9pbmRleC5qcz8wMDE4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5Ob2Rlc30gTm9kZXNcbiAqL1xuXG5jb25zdCBvd24gPSB7fS5oYXNPd25Qcm9wZXJ0eVxuXG4vKipcbiAqIENoZWNrIGlmIGBub2RlYCBpcyBhbiBlbGVtZW50IGFuZCBoYXMgYSBgbmFtZWAgcHJvcGVydHkuXG4gKlxuICogQHRlbXBsYXRlIHtzdHJpbmd9IEtleVxuICogICBUeXBlIG9mIGtleS5cbiAqIEBwYXJhbSB7Tm9kZXN9IG5vZGVcbiAqICAgTm9kZSB0byBjaGVjayAodHlwaWNhbGx5IGBFbGVtZW50YCkuXG4gKiBAcGFyYW0ge0tleX0gbmFtZVxuICogICBQcm9wZXJ0eSBuYW1lIHRvIGNoZWNrLlxuICogQHJldHVybnMge25vZGUgaXMgRWxlbWVudCAmIHtwcm9wZXJ0aWVzOiBSZWNvcmQ8S2V5LCBBcnJheTxudW1iZXIgfCBzdHJpbmc+IHwgbnVtYmVyIHwgc3RyaW5nIHwgdHJ1ZT59fX1cbiAqICAgV2hldGhlciBgbm9kZWAgaXMgYW4gZWxlbWVudCB0aGF0IGhhcyBhIGBuYW1lYCBwcm9wZXJ0eS5cbiAqXG4gKiAgIE5vdGU6IHNlZSA8aHR0cHM6Ly9naXRodWIuY29tL0RlZmluaXRlbHlUeXBlZC9EZWZpbml0ZWx5VHlwZWQvYmxvYi8yN2M5Mjc0L3R5cGVzL2hhc3QvaW5kZXguZC50cyNMMzdDMjktTDM3Qzk4Pi5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGhhc1Byb3BlcnR5KG5vZGUsIG5hbWUpIHtcbiAgY29uc3QgdmFsdWUgPVxuICAgIG5vZGUudHlwZSA9PT0gJ2VsZW1lbnQnICYmXG4gICAgb3duLmNhbGwobm9kZS5wcm9wZXJ0aWVzLCBuYW1lKSAmJlxuICAgIG5vZGUucHJvcGVydGllc1tuYW1lXVxuXG4gIHJldHVybiB2YWx1ZSAhPT0gbnVsbCAmJiB2YWx1ZSAhPT0gdW5kZWZpbmVkICYmIHZhbHVlICE9PSBmYWxzZVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-has-property/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-is-body-ok-link/lib/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/hast-util-is-body-ok-link/lib/index.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBodyOkLink: function() { return /* binding */ isBodyOkLink; }\n/* harmony export */ });\n/**\n * @import {Nodes} from 'hast'\n */\n\nconst list = new Set(['pingback', 'prefetch', 'stylesheet'])\n\n/**\n * Checks whether a node is a “body OK” link.\n *\n * @param {Nodes} node\n *   Node to check.\n * @returns {boolean}\n *   Whether `node` is a “body OK” link.\n */\nfunction isBodyOkLink(node) {\n  if (node.type !== 'element' || node.tagName !== 'link') {\n    return false\n  }\n\n  if (node.properties.itemProp) {\n    return true\n  }\n\n  const value = node.properties.rel\n  let index = -1\n\n  if (!Array.isArray(value) || value.length === 0) {\n    return false\n  }\n\n  while (++index < value.length) {\n    if (!list.has(String(value[index]))) {\n      return false\n    }\n  }\n\n  return true\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtaXMtYm9keS1vay1saW5rL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLE9BQU87QUFDbkI7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtaXMtYm9keS1vay1saW5rL2xpYi9pbmRleC5qcz8yM2ZiIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7Tm9kZXN9IGZyb20gJ2hhc3QnXG4gKi9cblxuY29uc3QgbGlzdCA9IG5ldyBTZXQoWydwaW5nYmFjaycsICdwcmVmZXRjaCcsICdzdHlsZXNoZWV0J10pXG5cbi8qKlxuICogQ2hlY2tzIHdoZXRoZXIgYSBub2RlIGlzIGEg4oCcYm9keSBPS+KAnSBsaW5rLlxuICpcbiAqIEBwYXJhbSB7Tm9kZXN9IG5vZGVcbiAqICAgTm9kZSB0byBjaGVjay5cbiAqIEByZXR1cm5zIHtib29sZWFufVxuICogICBXaGV0aGVyIGBub2RlYCBpcyBhIOKAnGJvZHkgT0vigJ0gbGluay5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGlzQm9keU9rTGluayhub2RlKSB7XG4gIGlmIChub2RlLnR5cGUgIT09ICdlbGVtZW50JyB8fCBub2RlLnRhZ05hbWUgIT09ICdsaW5rJykge1xuICAgIHJldHVybiBmYWxzZVxuICB9XG5cbiAgaWYgKG5vZGUucHJvcGVydGllcy5pdGVtUHJvcCkge1xuICAgIHJldHVybiB0cnVlXG4gIH1cblxuICBjb25zdCB2YWx1ZSA9IG5vZGUucHJvcGVydGllcy5yZWxcbiAgbGV0IGluZGV4ID0gLTFcblxuICBpZiAoIUFycmF5LmlzQXJyYXkodmFsdWUpIHx8IHZhbHVlLmxlbmd0aCA9PT0gMCkge1xuICAgIHJldHVybiBmYWxzZVxuICB9XG5cbiAgd2hpbGUgKCsraW5kZXggPCB2YWx1ZS5sZW5ndGgpIHtcbiAgICBpZiAoIWxpc3QuaGFzKFN0cmluZyh2YWx1ZVtpbmRleF0pKSkge1xuICAgICAgcmV0dXJuIGZhbHNlXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIHRydWVcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-is-body-ok-link/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-is-element/lib/index.js":
/*!********************************************************!*\
  !*** ./node_modules/hast-util-is-element/lib/index.js ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertElement: function() { return /* binding */ convertElement; },\n/* harmony export */   isElement: function() { return /* binding */ isElement; }\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Parents} Parents\n */\n\n/**\n * @template Fn\n * @template Fallback\n * @typedef {Fn extends (value: any) => value is infer Thing ? Thing : Fallback} Predicate\n */\n\n/**\n * @callback Check\n *   Check that an arbitrary value is an element.\n * @param {unknown} this\n *   Context object (`this`) to call `test` with\n * @param {unknown} [element]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   Position of `element` in its parent.\n * @param {Parents | null | undefined} [parent]\n *   Parent of `element`.\n * @returns {boolean}\n *   Whether this is an element and passes a test.\n *\n * @typedef {Array<TestFunction | string> | TestFunction | string | null | undefined} Test\n *   Check for an arbitrary element.\n *\n *   * when `string`, checks that the element has that tag name\n *   * when `function`, see `TestFunction`\n *   * when `Array`, checks if one of the subtests pass\n *\n * @callback TestFunction\n *   Check if an element passes a test.\n * @param {unknown} this\n *   The given context.\n * @param {Element} element\n *   An element.\n * @param {number | undefined} [index]\n *   Position of `element` in its parent.\n * @param {Parents | undefined} [parent]\n *   Parent of `element`.\n * @returns {boolean | undefined | void}\n *   Whether this element passes the test.\n *\n *   Note: `void` is included until TS sees no return as `undefined`.\n */\n\n/**\n * Check if `element` is an `Element` and whether it passes the given test.\n *\n * @param element\n *   Thing to check, typically `element`.\n * @param test\n *   Check for a specific element.\n * @param index\n *   Position of `element` in its parent.\n * @param parent\n *   Parent of `element`.\n * @param context\n *   Context object (`this`) to call `test` with.\n * @returns\n *   Whether `element` is an `Element` and passes a test.\n * @throws\n *   When an incorrect `test`, `index`, or `parent` is given; there is no error\n *   thrown when `element` is not a node or not an element.\n */\nconst isElement =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends TestFunction>(element: unknown, test: Condition, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & Predicate<Condition, Element>) &\n   *   (<Condition extends string>(element: unknown, test: Condition, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & {tagName: Condition}) &\n   *   ((element?: null | undefined) => false) &\n   *   ((element: unknown, test?: null | undefined, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element) &\n   *   ((element: unknown, test?: Test, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [element]\n     * @param {Test | undefined} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parents | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function (element, test, index, parent, context) {\n      const check = convertElement(test)\n\n      if (\n        index !== null &&\n        index !== undefined &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite `index`')\n      }\n\n      if (\n        parent !== null &&\n        parent !== undefined &&\n        (!parent.type || !parent.children)\n      ) {\n        throw new Error('Expected valid `parent`')\n      }\n\n      if (\n        (index === null || index === undefined) !==\n        (parent === null || parent === undefined)\n      ) {\n        throw new Error('Expected both `index` and `parent`')\n      }\n\n      return looksLikeAnElement(element)\n        ? check.call(context, element, index, parent)\n        : false\n    }\n  )\n\n/**\n * Generate a check from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * an `element`, `index`, and `parent`.\n *\n * @param test\n *   A test for a specific element.\n * @returns\n *   A check.\n */\nconst convertElement =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends TestFunction>(test: Condition) => (element: unknown, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & Predicate<Condition, Element>) &\n   *   (<Condition extends string>(test: Condition) => (element: unknown, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & {tagName: Condition}) &\n   *   ((test?: null | undefined) => (element?: unknown, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element) &\n   *   ((test?: Test) => Check)\n   * )}\n   */\n  (\n    /**\n     * @param {Test | null | undefined} [test]\n     * @returns {Check}\n     */\n    function (test) {\n      if (test === null || test === undefined) {\n        return element\n      }\n\n      if (typeof test === 'string') {\n        return tagNameFactory(test)\n      }\n\n      // Assume array.\n      if (typeof test === 'object') {\n        return anyFactory(test)\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      throw new Error('Expected function, string, or array as `test`')\n    }\n  )\n\n/**\n * Handle multiple tests.\n *\n * @param {Array<TestFunction | string>} tests\n * @returns {Check}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<Check>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convertElement(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @type {TestFunction}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].apply(this, parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn a string into a test for an element with a certain type.\n *\n * @param {string} check\n * @returns {Check}\n */\nfunction tagNameFactory(check) {\n  return castFactory(tagName)\n\n  /**\n   * @param {Element} element\n   * @returns {boolean}\n   */\n  function tagName(element) {\n    return element.tagName === check\n  }\n}\n\n/**\n * Turn a custom test into a test for an element that passes that test.\n *\n * @param {TestFunction} testFunction\n * @returns {Check}\n */\nfunction castFactory(testFunction) {\n  return check\n\n  /**\n   * @this {unknown}\n   * @type {Check}\n   */\n  function check(value, index, parent) {\n    return Boolean(\n      looksLikeAnElement(value) &&\n        testFunction.call(\n          this,\n          value,\n          typeof index === 'number' ? index : undefined,\n          parent || undefined\n        )\n    )\n  }\n}\n\n/**\n * Make sure something is an element.\n *\n * @param {unknown} element\n * @returns {element is Element}\n */\nfunction element(element) {\n  return Boolean(\n    element &&\n      typeof element === 'object' &&\n      'type' in element &&\n      element.type === 'element' &&\n      'tagName' in element &&\n      typeof element.tagName === 'string'\n  )\n}\n\n/**\n * @param {unknown} value\n * @returns {value is Element}\n */\nfunction looksLikeAnElement(value) {\n  return (\n    value !== null &&\n    typeof value === 'object' &&\n    'type' in value &&\n    'tagName' in value\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtaXMtZWxlbWVudC9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEsd0JBQXdCO0FBQ3JDLGFBQWEsd0JBQXdCO0FBQ3JDOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsb0VBQW9FO0FBQ2pGOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLFdBQVcsMkJBQTJCO0FBQ3RDO0FBQ0EsV0FBVyw0QkFBNEI7QUFDdkM7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBLGFBQWEseUVBQXlFO0FBQ3RGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLFdBQVcscUJBQXFCO0FBQ2hDO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOERBQThEO0FBQzlEO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQSwwTEFBMEwsbUJBQW1CO0FBQzdNO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxTQUFTO0FBQ3hCLGVBQWUsa0JBQWtCO0FBQ2pDLGVBQWUsMkJBQTJCO0FBQzFDLGVBQWUsNEJBQTRCO0FBQzNDLGVBQWUsU0FBUztBQUN4QixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQSw4TEFBOEwsbUJBQW1CO0FBQ2pOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUseUJBQXlCO0FBQ3hDLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsOEJBQThCO0FBQ3pDLGFBQWE7QUFDYjtBQUNBO0FBQ0EsYUFBYSxjQUFjO0FBQzNCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0EsWUFBWTtBQUNaLFlBQVk7QUFDWjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGFBQWEsU0FBUztBQUN0QixlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGNBQWM7QUFDekIsYUFBYTtBQUNiO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWixZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC1pcy1lbGVtZW50L2xpYi9pbmRleC5qcz80ODMwIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5QYXJlbnRzfSBQYXJlbnRzXG4gKi9cblxuLyoqXG4gKiBAdGVtcGxhdGUgRm5cbiAqIEB0ZW1wbGF0ZSBGYWxsYmFja1xuICogQHR5cGVkZWYge0ZuIGV4dGVuZHMgKHZhbHVlOiBhbnkpID0+IHZhbHVlIGlzIGluZmVyIFRoaW5nID8gVGhpbmcgOiBGYWxsYmFja30gUHJlZGljYXRlXG4gKi9cblxuLyoqXG4gKiBAY2FsbGJhY2sgQ2hlY2tcbiAqICAgQ2hlY2sgdGhhdCBhbiBhcmJpdHJhcnkgdmFsdWUgaXMgYW4gZWxlbWVudC5cbiAqIEBwYXJhbSB7dW5rbm93bn0gdGhpc1xuICogICBDb250ZXh0IG9iamVjdCAoYHRoaXNgKSB0byBjYWxsIGB0ZXN0YCB3aXRoXG4gKiBAcGFyYW0ge3Vua25vd259IFtlbGVtZW50XVxuICogICBBbnl0aGluZyAodHlwaWNhbGx5IGEgbm9kZSkuXG4gKiBAcGFyYW0ge251bWJlciB8IG51bGwgfCB1bmRlZmluZWR9IFtpbmRleF1cbiAqICAgUG9zaXRpb24gb2YgYGVsZW1lbnRgIGluIGl0cyBwYXJlbnQuXG4gKiBAcGFyYW0ge1BhcmVudHMgfCBudWxsIHwgdW5kZWZpbmVkfSBbcGFyZW50XVxuICogICBQYXJlbnQgb2YgYGVsZW1lbnRgLlxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKiAgIFdoZXRoZXIgdGhpcyBpcyBhbiBlbGVtZW50IGFuZCBwYXNzZXMgYSB0ZXN0LlxuICpcbiAqIEB0eXBlZGVmIHtBcnJheTxUZXN0RnVuY3Rpb24gfCBzdHJpbmc+IHwgVGVzdEZ1bmN0aW9uIHwgc3RyaW5nIHwgbnVsbCB8IHVuZGVmaW5lZH0gVGVzdFxuICogICBDaGVjayBmb3IgYW4gYXJiaXRyYXJ5IGVsZW1lbnQuXG4gKlxuICogICAqIHdoZW4gYHN0cmluZ2AsIGNoZWNrcyB0aGF0IHRoZSBlbGVtZW50IGhhcyB0aGF0IHRhZyBuYW1lXG4gKiAgICogd2hlbiBgZnVuY3Rpb25gLCBzZWUgYFRlc3RGdW5jdGlvbmBcbiAqICAgKiB3aGVuIGBBcnJheWAsIGNoZWNrcyBpZiBvbmUgb2YgdGhlIHN1YnRlc3RzIHBhc3NcbiAqXG4gKiBAY2FsbGJhY2sgVGVzdEZ1bmN0aW9uXG4gKiAgIENoZWNrIGlmIGFuIGVsZW1lbnQgcGFzc2VzIGEgdGVzdC5cbiAqIEBwYXJhbSB7dW5rbm93bn0gdGhpc1xuICogICBUaGUgZ2l2ZW4gY29udGV4dC5cbiAqIEBwYXJhbSB7RWxlbWVudH0gZWxlbWVudFxuICogICBBbiBlbGVtZW50LlxuICogQHBhcmFtIHtudW1iZXIgfCB1bmRlZmluZWR9IFtpbmRleF1cbiAqICAgUG9zaXRpb24gb2YgYGVsZW1lbnRgIGluIGl0cyBwYXJlbnQuXG4gKiBAcGFyYW0ge1BhcmVudHMgfCB1bmRlZmluZWR9IFtwYXJlbnRdXG4gKiAgIFBhcmVudCBvZiBgZWxlbWVudGAuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbiB8IHVuZGVmaW5lZCB8IHZvaWR9XG4gKiAgIFdoZXRoZXIgdGhpcyBlbGVtZW50IHBhc3NlcyB0aGUgdGVzdC5cbiAqXG4gKiAgIE5vdGU6IGB2b2lkYCBpcyBpbmNsdWRlZCB1bnRpbCBUUyBzZWVzIG5vIHJldHVybiBhcyBgdW5kZWZpbmVkYC5cbiAqL1xuXG4vKipcbiAqIENoZWNrIGlmIGBlbGVtZW50YCBpcyBhbiBgRWxlbWVudGAgYW5kIHdoZXRoZXIgaXQgcGFzc2VzIHRoZSBnaXZlbiB0ZXN0LlxuICpcbiAqIEBwYXJhbSBlbGVtZW50XG4gKiAgIFRoaW5nIHRvIGNoZWNrLCB0eXBpY2FsbHkgYGVsZW1lbnRgLlxuICogQHBhcmFtIHRlc3RcbiAqICAgQ2hlY2sgZm9yIGEgc3BlY2lmaWMgZWxlbWVudC5cbiAqIEBwYXJhbSBpbmRleFxuICogICBQb3NpdGlvbiBvZiBgZWxlbWVudGAgaW4gaXRzIHBhcmVudC5cbiAqIEBwYXJhbSBwYXJlbnRcbiAqICAgUGFyZW50IG9mIGBlbGVtZW50YC5cbiAqIEBwYXJhbSBjb250ZXh0XG4gKiAgIENvbnRleHQgb2JqZWN0IChgdGhpc2ApIHRvIGNhbGwgYHRlc3RgIHdpdGguXG4gKiBAcmV0dXJuc1xuICogICBXaGV0aGVyIGBlbGVtZW50YCBpcyBhbiBgRWxlbWVudGAgYW5kIHBhc3NlcyBhIHRlc3QuXG4gKiBAdGhyb3dzXG4gKiAgIFdoZW4gYW4gaW5jb3JyZWN0IGB0ZXN0YCwgYGluZGV4YCwgb3IgYHBhcmVudGAgaXMgZ2l2ZW47IHRoZXJlIGlzIG5vIGVycm9yXG4gKiAgIHRocm93biB3aGVuIGBlbGVtZW50YCBpcyBub3QgYSBub2RlIG9yIG5vdCBhbiBlbGVtZW50LlxuICovXG5leHBvcnQgY29uc3QgaXNFbGVtZW50ID1cbiAgLy8gTm90ZTogb3ZlcmxvYWRzIGluIEpTRG9jIGNhbuKAmXQgeWV0IHVzZSBkaWZmZXJlbnQgYEB0ZW1wbGF0ZWBzLlxuICAvKipcbiAgICogQHR5cGUgeyhcbiAgICogICAoPENvbmRpdGlvbiBleHRlbmRzIFRlc3RGdW5jdGlvbj4oZWxlbWVudDogdW5rbm93biwgdGVzdDogQ29uZGl0aW9uLCBpbmRleD86IG51bWJlciB8IG51bGwgfCB1bmRlZmluZWQsIHBhcmVudD86IFBhcmVudHMgfCBudWxsIHwgdW5kZWZpbmVkLCBjb250ZXh0PzogdW5rbm93bikgPT4gZWxlbWVudCBpcyBFbGVtZW50ICYgUHJlZGljYXRlPENvbmRpdGlvbiwgRWxlbWVudD4pICZcbiAgICogICAoPENvbmRpdGlvbiBleHRlbmRzIHN0cmluZz4oZWxlbWVudDogdW5rbm93biwgdGVzdDogQ29uZGl0aW9uLCBpbmRleD86IG51bWJlciB8IG51bGwgfCB1bmRlZmluZWQsIHBhcmVudD86IFBhcmVudHMgfCBudWxsIHwgdW5kZWZpbmVkLCBjb250ZXh0PzogdW5rbm93bikgPT4gZWxlbWVudCBpcyBFbGVtZW50ICYge3RhZ05hbWU6IENvbmRpdGlvbn0pICZcbiAgICogICAoKGVsZW1lbnQ/OiBudWxsIHwgdW5kZWZpbmVkKSA9PiBmYWxzZSkgJlxuICAgKiAgICgoZWxlbWVudDogdW5rbm93biwgdGVzdD86IG51bGwgfCB1bmRlZmluZWQsIGluZGV4PzogbnVtYmVyIHwgbnVsbCB8IHVuZGVmaW5lZCwgcGFyZW50PzogUGFyZW50cyB8IG51bGwgfCB1bmRlZmluZWQsIGNvbnRleHQ/OiB1bmtub3duKSA9PiBlbGVtZW50IGlzIEVsZW1lbnQpICZcbiAgICogICAoKGVsZW1lbnQ6IHVua25vd24sIHRlc3Q/OiBUZXN0LCBpbmRleD86IG51bWJlciB8IG51bGwgfCB1bmRlZmluZWQsIHBhcmVudD86IFBhcmVudHMgfCBudWxsIHwgdW5kZWZpbmVkLCBjb250ZXh0PzogdW5rbm93bikgPT4gYm9vbGVhbilcbiAgICogKX1cbiAgICovXG4gIChcbiAgICAvKipcbiAgICAgKiBAcGFyYW0ge3Vua25vd259IFtlbGVtZW50XVxuICAgICAqIEBwYXJhbSB7VGVzdCB8IHVuZGVmaW5lZH0gW3Rlc3RdXG4gICAgICogQHBhcmFtIHtudW1iZXIgfCBudWxsIHwgdW5kZWZpbmVkfSBbaW5kZXhdXG4gICAgICogQHBhcmFtIHtQYXJlbnRzIHwgbnVsbCB8IHVuZGVmaW5lZH0gW3BhcmVudF1cbiAgICAgKiBAcGFyYW0ge3Vua25vd259IFtjb250ZXh0XVxuICAgICAqIEByZXR1cm5zIHtib29sZWFufVxuICAgICAqL1xuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBtYXgtcGFyYW1zXG4gICAgZnVuY3Rpb24gKGVsZW1lbnQsIHRlc3QsIGluZGV4LCBwYXJlbnQsIGNvbnRleHQpIHtcbiAgICAgIGNvbnN0IGNoZWNrID0gY29udmVydEVsZW1lbnQodGVzdClcblxuICAgICAgaWYgKFxuICAgICAgICBpbmRleCAhPT0gbnVsbCAmJlxuICAgICAgICBpbmRleCAhPT0gdW5kZWZpbmVkICYmXG4gICAgICAgICh0eXBlb2YgaW5kZXggIT09ICdudW1iZXInIHx8XG4gICAgICAgICAgaW5kZXggPCAwIHx8XG4gICAgICAgICAgaW5kZXggPT09IE51bWJlci5QT1NJVElWRV9JTkZJTklUWSlcbiAgICAgICkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0V4cGVjdGVkIHBvc2l0aXZlIGZpbml0ZSBgaW5kZXhgJylcbiAgICAgIH1cblxuICAgICAgaWYgKFxuICAgICAgICBwYXJlbnQgIT09IG51bGwgJiZcbiAgICAgICAgcGFyZW50ICE9PSB1bmRlZmluZWQgJiZcbiAgICAgICAgKCFwYXJlbnQudHlwZSB8fCAhcGFyZW50LmNoaWxkcmVuKVxuICAgICAgKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRXhwZWN0ZWQgdmFsaWQgYHBhcmVudGAnKVxuICAgICAgfVxuXG4gICAgICBpZiAoXG4gICAgICAgIChpbmRleCA9PT0gbnVsbCB8fCBpbmRleCA9PT0gdW5kZWZpbmVkKSAhPT1cbiAgICAgICAgKHBhcmVudCA9PT0gbnVsbCB8fCBwYXJlbnQgPT09IHVuZGVmaW5lZClcbiAgICAgICkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0V4cGVjdGVkIGJvdGggYGluZGV4YCBhbmQgYHBhcmVudGAnKVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gbG9va3NMaWtlQW5FbGVtZW50KGVsZW1lbnQpXG4gICAgICAgID8gY2hlY2suY2FsbChjb250ZXh0LCBlbGVtZW50LCBpbmRleCwgcGFyZW50KVxuICAgICAgICA6IGZhbHNlXG4gICAgfVxuICApXG5cbi8qKlxuICogR2VuZXJhdGUgYSBjaGVjayBmcm9tIGEgdGVzdC5cbiAqXG4gKiBVc2VmdWwgaWYgeW914oCZcmUgZ29pbmcgdG8gdGVzdCBtYW55IG5vZGVzLCBmb3IgZXhhbXBsZSB3aGVuIGNyZWF0aW5nIGFcbiAqIHV0aWxpdHkgd2hlcmUgc29tZXRoaW5nIGVsc2UgcGFzc2VzIGEgY29tcGF0aWJsZSB0ZXN0LlxuICpcbiAqIFRoZSBjcmVhdGVkIGZ1bmN0aW9uIGlzIGEgYml0IGZhc3RlciBiZWNhdXNlIGl0IGV4cGVjdHMgdmFsaWQgaW5wdXQgb25seTpcbiAqIGFuIGBlbGVtZW50YCwgYGluZGV4YCwgYW5kIGBwYXJlbnRgLlxuICpcbiAqIEBwYXJhbSB0ZXN0XG4gKiAgIEEgdGVzdCBmb3IgYSBzcGVjaWZpYyBlbGVtZW50LlxuICogQHJldHVybnNcbiAqICAgQSBjaGVjay5cbiAqL1xuZXhwb3J0IGNvbnN0IGNvbnZlcnRFbGVtZW50ID1cbiAgLy8gTm90ZTogb3ZlcmxvYWRzIGluIEpTRG9jIGNhbuKAmXQgeWV0IHVzZSBkaWZmZXJlbnQgYEB0ZW1wbGF0ZWBzLlxuICAvKipcbiAgICogQHR5cGUgeyhcbiAgICogICAoPENvbmRpdGlvbiBleHRlbmRzIFRlc3RGdW5jdGlvbj4odGVzdDogQ29uZGl0aW9uKSA9PiAoZWxlbWVudDogdW5rbm93biwgaW5kZXg/OiBudW1iZXIgfCBudWxsIHwgdW5kZWZpbmVkLCBwYXJlbnQ/OiBQYXJlbnRzIHwgbnVsbCB8IHVuZGVmaW5lZCwgY29udGV4dD86IHVua25vd24pID0+IGVsZW1lbnQgaXMgRWxlbWVudCAmIFByZWRpY2F0ZTxDb25kaXRpb24sIEVsZW1lbnQ+KSAmXG4gICAqICAgKDxDb25kaXRpb24gZXh0ZW5kcyBzdHJpbmc+KHRlc3Q6IENvbmRpdGlvbikgPT4gKGVsZW1lbnQ6IHVua25vd24sIGluZGV4PzogbnVtYmVyIHwgbnVsbCB8IHVuZGVmaW5lZCwgcGFyZW50PzogUGFyZW50cyB8IG51bGwgfCB1bmRlZmluZWQsIGNvbnRleHQ/OiB1bmtub3duKSA9PiBlbGVtZW50IGlzIEVsZW1lbnQgJiB7dGFnTmFtZTogQ29uZGl0aW9ufSkgJlxuICAgKiAgICgodGVzdD86IG51bGwgfCB1bmRlZmluZWQpID0+IChlbGVtZW50PzogdW5rbm93biwgaW5kZXg/OiBudW1iZXIgfCBudWxsIHwgdW5kZWZpbmVkLCBwYXJlbnQ/OiBQYXJlbnRzIHwgbnVsbCB8IHVuZGVmaW5lZCwgY29udGV4dD86IHVua25vd24pID0+IGVsZW1lbnQgaXMgRWxlbWVudCkgJlxuICAgKiAgICgodGVzdD86IFRlc3QpID0+IENoZWNrKVxuICAgKiApfVxuICAgKi9cbiAgKFxuICAgIC8qKlxuICAgICAqIEBwYXJhbSB7VGVzdCB8IG51bGwgfCB1bmRlZmluZWR9IFt0ZXN0XVxuICAgICAqIEByZXR1cm5zIHtDaGVja31cbiAgICAgKi9cbiAgICBmdW5jdGlvbiAodGVzdCkge1xuICAgICAgaWYgKHRlc3QgPT09IG51bGwgfHwgdGVzdCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHJldHVybiBlbGVtZW50XG4gICAgICB9XG5cbiAgICAgIGlmICh0eXBlb2YgdGVzdCA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgcmV0dXJuIHRhZ05hbWVGYWN0b3J5KHRlc3QpXG4gICAgICB9XG5cbiAgICAgIC8vIEFzc3VtZSBhcnJheS5cbiAgICAgIGlmICh0eXBlb2YgdGVzdCA9PT0gJ29iamVjdCcpIHtcbiAgICAgICAgcmV0dXJuIGFueUZhY3RvcnkodGVzdClcbiAgICAgIH1cblxuICAgICAgaWYgKHR5cGVvZiB0ZXN0ID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgIHJldHVybiBjYXN0RmFjdG9yeSh0ZXN0KVxuICAgICAgfVxuXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0V4cGVjdGVkIGZ1bmN0aW9uLCBzdHJpbmcsIG9yIGFycmF5IGFzIGB0ZXN0YCcpXG4gICAgfVxuICApXG5cbi8qKlxuICogSGFuZGxlIG11bHRpcGxlIHRlc3RzLlxuICpcbiAqIEBwYXJhbSB7QXJyYXk8VGVzdEZ1bmN0aW9uIHwgc3RyaW5nPn0gdGVzdHNcbiAqIEByZXR1cm5zIHtDaGVja31cbiAqL1xuZnVuY3Rpb24gYW55RmFjdG9yeSh0ZXN0cykge1xuICAvKiogQHR5cGUge0FycmF5PENoZWNrPn0gKi9cbiAgY29uc3QgY2hlY2tzID0gW11cbiAgbGV0IGluZGV4ID0gLTFcblxuICB3aGlsZSAoKytpbmRleCA8IHRlc3RzLmxlbmd0aCkge1xuICAgIGNoZWNrc1tpbmRleF0gPSBjb252ZXJ0RWxlbWVudCh0ZXN0c1tpbmRleF0pXG4gIH1cblxuICByZXR1cm4gY2FzdEZhY3RvcnkoYW55KVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7dW5rbm93bn1cbiAgICogQHR5cGUge1Rlc3RGdW5jdGlvbn1cbiAgICovXG4gIGZ1bmN0aW9uIGFueSguLi5wYXJhbWV0ZXJzKSB7XG4gICAgbGV0IGluZGV4ID0gLTFcblxuICAgIHdoaWxlICgrK2luZGV4IDwgY2hlY2tzLmxlbmd0aCkge1xuICAgICAgaWYgKGNoZWNrc1tpbmRleF0uYXBwbHkodGhpcywgcGFyYW1ldGVycykpIHJldHVybiB0cnVlXG4gICAgfVxuXG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cbn1cblxuLyoqXG4gKiBUdXJuIGEgc3RyaW5nIGludG8gYSB0ZXN0IGZvciBhbiBlbGVtZW50IHdpdGggYSBjZXJ0YWluIHR5cGUuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IGNoZWNrXG4gKiBAcmV0dXJucyB7Q2hlY2t9XG4gKi9cbmZ1bmN0aW9uIHRhZ05hbWVGYWN0b3J5KGNoZWNrKSB7XG4gIHJldHVybiBjYXN0RmFjdG9yeSh0YWdOYW1lKVxuXG4gIC8qKlxuICAgKiBAcGFyYW0ge0VsZW1lbnR9IGVsZW1lbnRcbiAgICogQHJldHVybnMge2Jvb2xlYW59XG4gICAqL1xuICBmdW5jdGlvbiB0YWdOYW1lKGVsZW1lbnQpIHtcbiAgICByZXR1cm4gZWxlbWVudC50YWdOYW1lID09PSBjaGVja1xuICB9XG59XG5cbi8qKlxuICogVHVybiBhIGN1c3RvbSB0ZXN0IGludG8gYSB0ZXN0IGZvciBhbiBlbGVtZW50IHRoYXQgcGFzc2VzIHRoYXQgdGVzdC5cbiAqXG4gKiBAcGFyYW0ge1Rlc3RGdW5jdGlvbn0gdGVzdEZ1bmN0aW9uXG4gKiBAcmV0dXJucyB7Q2hlY2t9XG4gKi9cbmZ1bmN0aW9uIGNhc3RGYWN0b3J5KHRlc3RGdW5jdGlvbikge1xuICByZXR1cm4gY2hlY2tcblxuICAvKipcbiAgICogQHRoaXMge3Vua25vd259XG4gICAqIEB0eXBlIHtDaGVja31cbiAgICovXG4gIGZ1bmN0aW9uIGNoZWNrKHZhbHVlLCBpbmRleCwgcGFyZW50KSB7XG4gICAgcmV0dXJuIEJvb2xlYW4oXG4gICAgICBsb29rc0xpa2VBbkVsZW1lbnQodmFsdWUpICYmXG4gICAgICAgIHRlc3RGdW5jdGlvbi5jYWxsKFxuICAgICAgICAgIHRoaXMsXG4gICAgICAgICAgdmFsdWUsXG4gICAgICAgICAgdHlwZW9mIGluZGV4ID09PSAnbnVtYmVyJyA/IGluZGV4IDogdW5kZWZpbmVkLFxuICAgICAgICAgIHBhcmVudCB8fCB1bmRlZmluZWRcbiAgICAgICAgKVxuICAgIClcbiAgfVxufVxuXG4vKipcbiAqIE1ha2Ugc3VyZSBzb21ldGhpbmcgaXMgYW4gZWxlbWVudC5cbiAqXG4gKiBAcGFyYW0ge3Vua25vd259IGVsZW1lbnRcbiAqIEByZXR1cm5zIHtlbGVtZW50IGlzIEVsZW1lbnR9XG4gKi9cbmZ1bmN0aW9uIGVsZW1lbnQoZWxlbWVudCkge1xuICByZXR1cm4gQm9vbGVhbihcbiAgICBlbGVtZW50ICYmXG4gICAgICB0eXBlb2YgZWxlbWVudCA9PT0gJ29iamVjdCcgJiZcbiAgICAgICd0eXBlJyBpbiBlbGVtZW50ICYmXG4gICAgICBlbGVtZW50LnR5cGUgPT09ICdlbGVtZW50JyAmJlxuICAgICAgJ3RhZ05hbWUnIGluIGVsZW1lbnQgJiZcbiAgICAgIHR5cGVvZiBlbGVtZW50LnRhZ05hbWUgPT09ICdzdHJpbmcnXG4gIClcbn1cblxuLyoqXG4gKiBAcGFyYW0ge3Vua25vd259IHZhbHVlXG4gKiBAcmV0dXJucyB7dmFsdWUgaXMgRWxlbWVudH1cbiAqL1xuZnVuY3Rpb24gbG9va3NMaWtlQW5FbGVtZW50KHZhbHVlKSB7XG4gIHJldHVybiAoXG4gICAgdmFsdWUgIT09IG51bGwgJiZcbiAgICB0eXBlb2YgdmFsdWUgPT09ICdvYmplY3QnICYmXG4gICAgJ3R5cGUnIGluIHZhbHVlICYmXG4gICAgJ3RhZ05hbWUnIGluIHZhbHVlXG4gIClcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-is-element/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-minify-whitespace/lib/block.js":
/*!***************************************************************!*\
  !*** ./node_modules/hast-util-minify-whitespace/lib/block.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blocks: function() { return /* binding */ blocks; }\n/* harmony export */ });\n// See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\nconst blocks = [\n  'address', // Flow content.\n  'article', // Sections and headings.\n  'aside', // Sections and headings.\n  'blockquote', // Flow content.\n  'body', // Page.\n  'br', // Contribute whitespace intrinsically.\n  'caption', // Similar to block.\n  'center', // Flow content, legacy.\n  'col', // Similar to block.\n  'colgroup', // Similar to block.\n  'dd', // Lists.\n  'dialog', // Flow content.\n  'dir', // Lists, legacy.\n  'div', // Flow content.\n  'dl', // Lists.\n  'dt', // Lists.\n  'figcaption', // Flow content.\n  'figure', // Flow content.\n  'footer', // Flow content.\n  'form', // Flow content.\n  'h1', // Sections and headings.\n  'h2', // Sections and headings.\n  'h3', // Sections and headings.\n  'h4', // Sections and headings.\n  'h5', // Sections and headings.\n  'h6', // Sections and headings.\n  'head', // Page.\n  'header', // Flow content.\n  'hgroup', // Sections and headings.\n  'hr', // Flow content.\n  'html', // Page.\n  'legend', // Flow content.\n  'li', // Block-like.\n  'li', // Similar to block.\n  'listing', // Flow content, legacy\n  'main', // Flow content.\n  'menu', // Lists.\n  'nav', // Sections and headings.\n  'ol', // Lists.\n  'optgroup', // Similar to block.\n  'option', // Similar to block.\n  'p', // Flow content.\n  'plaintext', // Flow content, legacy\n  'pre', // Flow content.\n  'section', // Sections and headings.\n  'summary', // Similar to block.\n  'table', // Similar to block.\n  'tbody', // Similar to block.\n  'td', // Block-like.\n  'td', // Similar to block.\n  'tfoot', // Similar to block.\n  'th', // Block-like.\n  'th', // Similar to block.\n  'thead', // Similar to block.\n  'tr', // Similar to block.\n  'ul', // Lists.\n  'wbr', // Contribute whitespace intrinsically.\n  'xmp' // Flow content, legacy\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-minify-whitespace/lib/block.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-minify-whitespace/lib/content.js":
/*!*****************************************************************!*\
  !*** ./node_modules/hast-util-minify-whitespace/lib/content.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   content: function() { return /* binding */ content; }\n/* harmony export */ });\nconst content = [\n  // Form.\n  'button',\n  'input',\n  'select',\n  'textarea'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtbWluaWZ5LXdoaXRlc3BhY2UvbGliL2NvbnRlbnQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtbWluaWZ5LXdoaXRlc3BhY2UvbGliL2NvbnRlbnQuanM/YTFjNiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgY29udGVudCA9IFtcbiAgLy8gRm9ybS5cbiAgJ2J1dHRvbicsXG4gICdpbnB1dCcsXG4gICdzZWxlY3QnLFxuICAndGV4dGFyZWEnXG5dXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-minify-whitespace/lib/content.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-minify-whitespace/lib/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/hast-util-minify-whitespace/lib/index.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   minifyWhitespace: function() { return /* binding */ minifyWhitespace; }\n/* harmony export */ });\n/* harmony import */ var hast_util_embedded__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-embedded */ \"(app-pages-browser)/./node_modules/hast-util-embedded/lib/index.js\");\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-is-element */ \"(app-pages-browser)/./node_modules/hast-util-is-element/lib/index.js\");\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-whitespace */ \"(app-pages-browser)/./node_modules/hast-util-whitespace/lib/index.js\");\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-is */ \"(app-pages-browser)/./node_modules/unist-util-is/lib/index.js\");\n/* harmony import */ var _block_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./block.js */ \"(app-pages-browser)/./node_modules/hast-util-minify-whitespace/lib/block.js\");\n/* harmony import */ var _content_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./content.js */ \"(app-pages-browser)/./node_modules/hast-util-minify-whitespace/lib/content.js\");\n/* harmony import */ var _skippable_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./skippable.js */ \"(app-pages-browser)/./node_modules/hast-util-minify-whitespace/lib/skippable.js\");\n/**\n * @import {Nodes, Parents, Text} from 'hast'\n */\n\n/**\n * @callback Collapse\n *   Collapse a string.\n * @param {string} value\n *   Value to collapse.\n * @returns {string}\n *   Collapsed value.\n *\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [newlines=false]\n *   Collapse whitespace containing newlines to `'\\n'` instead of `' '`\n *   (default: `false`); the default is to collapse to a single space.\n *\n * @typedef Result\n *   Result.\n * @property {boolean} remove\n *   Whether to remove.\n * @property {boolean} ignore\n *   Whether to ignore.\n * @property {boolean} stripAtStart\n *   Whether to strip at the start.\n *\n * @typedef State\n *   Info passed around.\n * @property {Collapse} collapse\n *   Collapse.\n * @property {Whitespace} whitespace\n *   Current whitespace.\n * @property {boolean | undefined} [before]\n *   Whether there is a break before (default: `false`).\n * @property {boolean | undefined} [after]\n *   Whether there is a break after (default: `false`).\n *\n * @typedef {'normal' | 'nowrap' | 'pre' | 'pre-wrap'} Whitespace\n *   Whitespace setting.\n */\n\n\n\n\n\n\n\n\n\n/** @type {Options} */\nconst emptyOptions = {}\nconst ignorableNode = (0,unist_util_is__WEBPACK_IMPORTED_MODULE_0__.convert)(['comment', 'doctype'])\n\n/**\n * Minify whitespace.\n *\n * @param {Nodes} tree\n *   Tree.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nfunction minifyWhitespace(tree, options) {\n  const settings = options || emptyOptions\n\n  minify(tree, {\n    collapse: collapseFactory(\n      settings.newlines ? replaceNewlines : replaceWhitespace\n    ),\n    whitespace: 'normal'\n  })\n}\n\n/**\n * @param {Nodes} node\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Result}\n *   Result.\n */\nfunction minify(node, state) {\n  if ('children' in node) {\n    const settings = {...state}\n\n    if (node.type === 'root' || blocklike(node)) {\n      settings.before = true\n      settings.after = true\n    }\n\n    settings.whitespace = inferWhiteSpace(node, state)\n\n    return all(node, settings)\n  }\n\n  if (node.type === 'text') {\n    if (state.whitespace === 'normal') {\n      return minifyText(node, state)\n    }\n\n    // Naïve collapse, but no trimming:\n    if (state.whitespace === 'nowrap') {\n      node.value = state.collapse(node.value)\n    }\n\n    // The `pre-wrap` or `pre` whitespace settings are neither collapsed nor\n    // trimmed.\n  }\n\n  return {ignore: ignorableNode(node), stripAtStart: false, remove: false}\n}\n\n/**\n * @param {Text} node\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Result}\n *   Result.\n */\nfunction minifyText(node, state) {\n  const value = state.collapse(node.value)\n  const result = {ignore: false, stripAtStart: false, remove: false}\n  let start = 0\n  let end = value.length\n\n  if (state.before && removable(value.charAt(0))) {\n    start++\n  }\n\n  if (start !== end && removable(value.charAt(end - 1))) {\n    if (state.after) {\n      end--\n    } else {\n      result.stripAtStart = true\n    }\n  }\n\n  if (start === end) {\n    result.remove = true\n  } else {\n    node.value = value.slice(start, end)\n  }\n\n  return result\n}\n\n/**\n * @param {Parents} parent\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Result}\n *   Result.\n */\nfunction all(parent, state) {\n  let before = state.before\n  const after = state.after\n  const children = parent.children\n  let length = children.length\n  let index = -1\n\n  while (++index < length) {\n    const result = minify(children[index], {\n      ...state,\n      after: collapsableAfter(children, index, after),\n      before\n    })\n\n    if (result.remove) {\n      children.splice(index, 1)\n      index--\n      length--\n    } else if (!result.ignore) {\n      before = result.stripAtStart\n    }\n\n    // If this element, such as a `<select>` or `<img>`, contributes content\n    // somehow, allow whitespace again.\n    if (content(children[index])) {\n      before = false\n    }\n  }\n\n  return {ignore: false, stripAtStart: Boolean(before || after), remove: false}\n}\n\n/**\n * @param {Array<Nodes>} nodes\n *   Nodes.\n * @param {number} index\n *   Index.\n * @param {boolean | undefined} [after]\n *   Whether there is a break after `nodes` (default: `false`).\n * @returns {boolean | undefined}\n *   Whether there is a break after the node at `index`.\n */\nfunction collapsableAfter(nodes, index, after) {\n  while (++index < nodes.length) {\n    const node = nodes[index]\n    let result = inferBoundary(node)\n\n    if (result === undefined && 'children' in node && !skippable(node)) {\n      result = collapsableAfter(node.children, -1)\n    }\n\n    if (typeof result === 'boolean') {\n      return result\n    }\n  }\n\n  return after\n}\n\n/**\n * Infer two types of boundaries:\n *\n * 1. `true` — boundary for which whitespace around it does not contribute\n *    anything\n * 2. `false` — boundary for which whitespace around it *does* contribute\n *\n * No result (`undefined`) is returned if it is unknown.\n *\n * @param {Nodes} node\n *   Node.\n * @returns {boolean | undefined}\n *   Boundary.\n */\nfunction inferBoundary(node) {\n  if (node.type === 'element') {\n    if (content(node)) {\n      return false\n    }\n\n    if (blocklike(node)) {\n      return true\n    }\n\n    // Unknown: either depends on siblings if embedded or metadata, or on\n    // children.\n  } else if (node.type === 'text') {\n    if (!(0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_1__.whitespace)(node)) {\n      return false\n    }\n  } else if (!ignorableNode(node)) {\n    return false\n  }\n}\n\n/**\n * Infer whether a node is skippable.\n *\n * @param {Nodes} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is skippable.\n */\nfunction content(node) {\n  return (0,hast_util_embedded__WEBPACK_IMPORTED_MODULE_2__.embedded)(node) || (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__.isElement)(node, _content_js__WEBPACK_IMPORTED_MODULE_4__.content)\n}\n\n/**\n * See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\n *\n * @param {Nodes} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is block-like.\n */\nfunction blocklike(node) {\n  return (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__.isElement)(node, _block_js__WEBPACK_IMPORTED_MODULE_5__.blocks)\n}\n\n/**\n * @param {Parents} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is skippable.\n */\nfunction skippable(node) {\n  return (\n    Boolean(node.type === 'element' && node.properties.hidden) ||\n    ignorableNode(node) ||\n    (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__.isElement)(node, _skippable_js__WEBPACK_IMPORTED_MODULE_6__.skippable)\n  )\n}\n\n/**\n * @param {string} character\n *   Character.\n * @returns {boolean}\n *   Whether `character` is removable.\n */\nfunction removable(character) {\n  return character === ' ' || character === '\\n'\n}\n\n/**\n * @type {Collapse}\n */\nfunction replaceNewlines(value) {\n  const match = /\\r?\\n|\\r/.exec(value)\n  return match ? match[0] : ' '\n}\n\n/**\n * @type {Collapse}\n */\nfunction replaceWhitespace() {\n  return ' '\n}\n\n/**\n * @param {Collapse} replace\n * @returns {Collapse}\n *   Collapse.\n */\nfunction collapseFactory(replace) {\n  return collapse\n\n  /**\n   * @type {Collapse}\n   */\n  function collapse(value) {\n    return String(value).replace(/[\\t\\n\\v\\f\\r ]+/g, replace)\n  }\n}\n\n/**\n * We don’t need to support void elements here (so `nobr wbr` -> `normal` is\n * ignored).\n *\n * @param {Parents} node\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Whitespace}\n *   Whitespace.\n */\nfunction inferWhiteSpace(node, state) {\n  if ('tagName' in node && node.properties) {\n    switch (node.tagName) {\n      // Whitespace in script/style, while not displayed by CSS as significant,\n      // could have some meaning in JS/CSS, so we can’t touch them.\n      case 'listing':\n      case 'plaintext':\n      case 'script':\n      case 'style':\n      case 'xmp': {\n        return 'pre'\n      }\n\n      case 'nobr': {\n        return 'nowrap'\n      }\n\n      case 'pre': {\n        return node.properties.wrap ? 'pre-wrap' : 'pre'\n      }\n\n      case 'td':\n      case 'th': {\n        return node.properties.noWrap ? 'nowrap' : state.whitespace\n      }\n\n      case 'textarea': {\n        return 'pre-wrap'\n      }\n\n      default:\n    }\n  }\n\n  return state.whitespace\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-minify-whitespace/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-minify-whitespace/lib/skippable.js":
/*!*******************************************************************!*\
  !*** ./node_modules/hast-util-minify-whitespace/lib/skippable.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   skippable: function() { return /* binding */ skippable; }\n/* harmony export */ });\nconst skippable = [\n  'area',\n  'base',\n  'basefont',\n  'dialog',\n  'datalist',\n  'head',\n  'link',\n  'meta',\n  'noembed',\n  'noframes',\n  'param',\n  'rp',\n  'script',\n  'source',\n  'style',\n  'template',\n  'track',\n  'title'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtbWluaWZ5LXdoaXRlc3BhY2UvbGliL3NraXBwYWJsZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLW1pbmlmeS13aGl0ZXNwYWNlL2xpYi9za2lwcGFibGUuanM/ZjYzYiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3Qgc2tpcHBhYmxlID0gW1xuICAnYXJlYScsXG4gICdiYXNlJyxcbiAgJ2Jhc2Vmb250JyxcbiAgJ2RpYWxvZycsXG4gICdkYXRhbGlzdCcsXG4gICdoZWFkJyxcbiAgJ2xpbmsnLFxuICAnbWV0YScsXG4gICdub2VtYmVkJyxcbiAgJ25vZnJhbWVzJyxcbiAgJ3BhcmFtJyxcbiAgJ3JwJyxcbiAgJ3NjcmlwdCcsXG4gICdzb3VyY2UnLFxuICAnc3R5bGUnLFxuICAndGVtcGxhdGUnLFxuICAndHJhY2snLFxuICAndGl0bGUnXG5dXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-minify-whitespace/lib/skippable.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-phrasing/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/hast-util-phrasing/lib/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   phrasing: function() { return /* binding */ phrasing; }\n/* harmony export */ });\n/* harmony import */ var hast_util_embedded__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-embedded */ \"(app-pages-browser)/./node_modules/hast-util-embedded/lib/index.js\");\n/* harmony import */ var hast_util_has_property__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-has-property */ \"(app-pages-browser)/./node_modules/hast-util-has-property/lib/index.js\");\n/* harmony import */ var hast_util_is_body_ok_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-is-body-ok-link */ \"(app-pages-browser)/./node_modules/hast-util-is-body-ok-link/lib/index.js\");\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(app-pages-browser)/./node_modules/hast-util-is-element/lib/index.js\");\n/**\n * @typedef {import('hast').Nodes} Nodes\n */\n\n\n\n\n\n\nconst basic = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  'a',\n  'abbr',\n  // `area` is in fact only phrasing if it is inside a `map` element.\n  // However, since `area`s are required to be inside a `map` element, and it’s\n  // a rather involved check, it’s ignored here for now.\n  'area',\n  'b',\n  'bdi',\n  'bdo',\n  'br',\n  'button',\n  'cite',\n  'code',\n  'data',\n  'datalist',\n  'del',\n  'dfn',\n  'em',\n  'i',\n  'input',\n  'ins',\n  'kbd',\n  'keygen',\n  'label',\n  'map',\n  'mark',\n  'meter',\n  'noscript',\n  'output',\n  'progress',\n  'q',\n  'ruby',\n  's',\n  'samp',\n  'script',\n  'select',\n  'small',\n  'span',\n  'strong',\n  'sub',\n  'sup',\n  'template',\n  'textarea',\n  'time',\n  'u',\n  'var',\n  'wbr'\n])\n\nconst meta = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('meta')\n\n/**\n * Check if the given value is *phrasing* content.\n *\n * @param {Nodes} value\n *   Node to check.\n * @returns {boolean}\n *   Whether `value` is phrasing content.\n */\nfunction phrasing(value) {\n  return Boolean(\n    value.type === 'text' ||\n      basic(value) ||\n      (0,hast_util_embedded__WEBPACK_IMPORTED_MODULE_1__.embedded)(value) ||\n      (0,hast_util_is_body_ok_link__WEBPACK_IMPORTED_MODULE_2__.isBodyOkLink)(value) ||\n      (meta(value) && (0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_3__.hasProperty)(value, 'itemProp'))\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-phrasing/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/hast-util-whitespace/lib/index.js":
/*!********************************************************!*\
  !*** ./node_modules/hast-util-whitespace/lib/index.js ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   whitespace: function() { return /* binding */ whitespace; }\n/* harmony export */ });\n/**\n * @typedef {import('hast').Nodes} Nodes\n */\n\n// HTML whitespace expression.\n// See <https://infra.spec.whatwg.org/#ascii-whitespace>.\nconst re = /[ \\t\\n\\f\\r]/g\n\n/**\n * Check if the given value is *inter-element whitespace*.\n *\n * @param {Nodes | string} thing\n *   Thing to check (`Node` or `string`).\n * @returns {boolean}\n *   Whether the `value` is inter-element whitespace (`boolean`): consisting of\n *   zero or more of space, tab (`\\t`), line feed (`\\n`), carriage return\n *   (`\\r`), or form feed (`\\f`); if a node is passed it must be a `Text` node,\n *   whose `value` field is checked.\n */\nfunction whitespace(thing) {\n  return typeof thing === 'object'\n    ? thing.type === 'text'\n      ? empty(thing.value)\n      : false\n    : empty(thing)\n}\n\n/**\n * @param {string} value\n * @returns {boolean}\n */\nfunction empty(value) {\n  return value.replace(re, '') === ''\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtd2hpdGVzcGFjZS9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSxzQkFBc0I7QUFDbkM7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsZ0JBQWdCO0FBQzNCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxrQ0FBa0M7QUFDbEM7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXdoaXRlc3BhY2UvbGliL2luZGV4LmpzPzU0Y2IiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuTm9kZXN9IE5vZGVzXG4gKi9cblxuLy8gSFRNTCB3aGl0ZXNwYWNlIGV4cHJlc3Npb24uXG4vLyBTZWUgPGh0dHBzOi8vaW5mcmEuc3BlYy53aGF0d2cub3JnLyNhc2NpaS13aGl0ZXNwYWNlPi5cbmNvbnN0IHJlID0gL1sgXFx0XFxuXFxmXFxyXS9nXG5cbi8qKlxuICogQ2hlY2sgaWYgdGhlIGdpdmVuIHZhbHVlIGlzICppbnRlci1lbGVtZW50IHdoaXRlc3BhY2UqLlxuICpcbiAqIEBwYXJhbSB7Tm9kZXMgfCBzdHJpbmd9IHRoaW5nXG4gKiAgIFRoaW5nIHRvIGNoZWNrIChgTm9kZWAgb3IgYHN0cmluZ2ApLlxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKiAgIFdoZXRoZXIgdGhlIGB2YWx1ZWAgaXMgaW50ZXItZWxlbWVudCB3aGl0ZXNwYWNlIChgYm9vbGVhbmApOiBjb25zaXN0aW5nIG9mXG4gKiAgIHplcm8gb3IgbW9yZSBvZiBzcGFjZSwgdGFiIChgXFx0YCksIGxpbmUgZmVlZCAoYFxcbmApLCBjYXJyaWFnZSByZXR1cm5cbiAqICAgKGBcXHJgKSwgb3IgZm9ybSBmZWVkIChgXFxmYCk7IGlmIGEgbm9kZSBpcyBwYXNzZWQgaXQgbXVzdCBiZSBhIGBUZXh0YCBub2RlLFxuICogICB3aG9zZSBgdmFsdWVgIGZpZWxkIGlzIGNoZWNrZWQuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB3aGl0ZXNwYWNlKHRoaW5nKSB7XG4gIHJldHVybiB0eXBlb2YgdGhpbmcgPT09ICdvYmplY3QnXG4gICAgPyB0aGluZy50eXBlID09PSAndGV4dCdcbiAgICAgID8gZW1wdHkodGhpbmcudmFsdWUpXG4gICAgICA6IGZhbHNlXG4gICAgOiBlbXB0eSh0aGluZylcbn1cblxuLyoqXG4gKiBAcGFyYW0ge3N0cmluZ30gdmFsdWVcbiAqIEByZXR1cm5zIHtib29sZWFufVxuICovXG5mdW5jdGlvbiBlbXB0eSh2YWx1ZSkge1xuICByZXR1cm4gdmFsdWUucmVwbGFjZShyZSwgJycpID09PSAnJ1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/hast-util-whitespace/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/html-whitespace-sensitive-tag-names/lib/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/html-whitespace-sensitive-tag-names/lib/index.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   whitespaceSensitiveTagNames: function() { return /* binding */ whitespaceSensitiveTagNames; }\n/* harmony export */ });\n/**\n * List of HTML tag names that are whitespace sensitive.\n */\nconst whitespaceSensitiveTagNames = [\n  'pre',\n  'script',\n  'style',\n  'textarea'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9odG1sLXdoaXRlc3BhY2Utc2Vuc2l0aXZlLXRhZy1uYW1lcy9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvaHRtbC13aGl0ZXNwYWNlLXNlbnNpdGl2ZS10YWctbmFtZXMvbGliL2luZGV4LmpzPzVjYTAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBMaXN0IG9mIEhUTUwgdGFnIG5hbWVzIHRoYXQgYXJlIHdoaXRlc3BhY2Ugc2Vuc2l0aXZlLlxuICovXG5leHBvcnQgY29uc3Qgd2hpdGVzcGFjZVNlbnNpdGl2ZVRhZ05hbWVzID0gW1xuICAncHJlJyxcbiAgJ3NjcmlwdCcsXG4gICdzdHlsZScsXG4gICd0ZXh0YXJlYSdcbl1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/html-whitespace-sensitive-tag-names/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/rehype-format/index.js":
/*!*********************************************!*\
  !*** ./node_modules/rehype-format/index.js ***!
  \*********************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* reexport safe */ _lib_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _lib_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/index.js */ \"(app-pages-browser)/./node_modules/rehype-format/lib/index.js\");\n/**\n * @typedef {import('hast-util-format').Options} Options\n */\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWh5cGUtZm9ybWF0L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLG9DQUFvQztBQUNqRDs7QUFFc0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL3JlaHlwZS1mb3JtYXQvaW5kZXguanM/YzBhZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QtdXRpbC1mb3JtYXQnKS5PcHRpb25zfSBPcHRpb25zXG4gKi9cblxuZXhwb3J0IHtkZWZhdWx0fSBmcm9tICcuL2xpYi9pbmRleC5qcydcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/rehype-format/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/rehype-format/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/rehype-format/lib/index.js ***!
  \*************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ rehypeFormat; }\n/* harmony export */ });\n/* harmony import */ var hast_util_format__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-format */ \"(app-pages-browser)/./node_modules/hast-util-format/lib/index.js\");\n/**\n * @import {Options} from 'hast-util-format'\n * @import {Root} from 'hast'\n */\n\n\n\n/**\n * Format whitespace in HTML.\n *\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns\n *   Transform.\n */\nfunction rehypeFormat(options) {\n  /**\n   * Transform.\n   *\n   * @param {Root} tree\n   *   Tree.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  return function (tree) {\n    (0,hast_util_format__WEBPACK_IMPORTED_MODULE_0__.format)(tree, options)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWh5cGUtZm9ybWF0L2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsWUFBWSxTQUFTO0FBQ3JCLFlBQVksTUFBTTtBQUNsQjs7QUFFdUM7O0FBRXZDO0FBQ0E7QUFDQTtBQUNBLFdBQVcsNEJBQTRCO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ2U7QUFDZjtBQUNBO0FBQ0E7QUFDQSxhQUFhLE1BQU07QUFDbkI7QUFDQSxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0EsSUFBSSx3REFBTTtBQUNWO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL3JlaHlwZS1mb3JtYXQvbGliL2luZGV4LmpzPzE2YWMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zfSBmcm9tICdoYXN0LXV0aWwtZm9ybWF0J1xuICogQGltcG9ydCB7Um9vdH0gZnJvbSAnaGFzdCdcbiAqL1xuXG5pbXBvcnQge2Zvcm1hdH0gZnJvbSAnaGFzdC11dGlsLWZvcm1hdCdcblxuLyoqXG4gKiBGb3JtYXQgd2hpdGVzcGFjZSBpbiBIVE1MLlxuICpcbiAqIEBwYXJhbSB7T3B0aW9ucyB8IG51bGwgfCB1bmRlZmluZWR9IFtvcHRpb25zXVxuICogICBDb25maWd1cmF0aW9uIChvcHRpb25hbCkuXG4gKiBAcmV0dXJuc1xuICogICBUcmFuc2Zvcm0uXG4gKi9cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHJlaHlwZUZvcm1hdChvcHRpb25zKSB7XG4gIC8qKlxuICAgKiBUcmFuc2Zvcm0uXG4gICAqXG4gICAqIEBwYXJhbSB7Um9vdH0gdHJlZVxuICAgKiAgIFRyZWUuXG4gICAqIEByZXR1cm5zIHt1bmRlZmluZWR9XG4gICAqICAgTm90aGluZy5cbiAgICovXG4gIHJldHVybiBmdW5jdGlvbiAodHJlZSkge1xuICAgIGZvcm1hdCh0cmVlLCBvcHRpb25zKVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/rehype-format/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/unist-util-is/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/unist-util-is/lib/index.js ***!
  \*************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convert: function() { return /* binding */ convert; },\n/* harmony export */   is: function() { return /* binding */ is; }\n/* harmony export */ });\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n */\n\n/**\n * @template Fn\n * @template Fallback\n * @typedef {Fn extends (value: any) => value is infer Thing ? Thing : Fallback} Predicate\n */\n\n/**\n * @callback Check\n *   Check that an arbitrary value is a node.\n * @param {unknown} this\n *   The given context.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is a node and passes a test.\n *\n * @typedef {Record<string, unknown> | Node} Props\n *   Object to check for equivalence.\n *\n *   Note: `Node` is included as it is common but is not indexable.\n *\n * @typedef {Array<Props | TestFunction | string> | Props | TestFunction | string | null | undefined} Test\n *   Check for an arbitrary node.\n *\n * @callback TestFunction\n *   Check if a node passes a test.\n * @param {unknown} this\n *   The given context.\n * @param {Node} node\n *   A node.\n * @param {number | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean | undefined | void}\n *   Whether this node passes the test.\n *\n *   Note: `void` is included until TS sees no return as `undefined`.\n */\n\n/**\n * Check if `node` is a `Node` and whether it passes the given test.\n *\n * @param {unknown} node\n *   Thing to check, typically `Node`.\n * @param {Test} test\n *   A check for a specific node.\n * @param {number | null | undefined} index\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} parent\n *   The node’s parent.\n * @param {unknown} context\n *   Context object (`this`) to pass to `test` functions.\n * @returns {boolean}\n *   Whether `node` is a node and passes a test.\n */\nconst is =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends string>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &\n   *   (<Condition extends Props>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &\n   *   (<Condition extends TestFunction>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &\n   *   ((node?: null | undefined) => false) &\n   *   ((node: unknown, test?: null | undefined, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &\n   *   ((node: unknown, test?: Test, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [node]\n     * @param {Test} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parent | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function (node, test, index, parent, context) {\n      const check = convert(test)\n\n      if (\n        index !== undefined &&\n        index !== null &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite index')\n      }\n\n      if (\n        parent !== undefined &&\n        parent !== null &&\n        (!is(parent) || !parent.children)\n      ) {\n        throw new Error('Expected parent node')\n      }\n\n      if (\n        (parent === undefined || parent === null) !==\n        (index === undefined || index === null)\n      ) {\n        throw new Error('Expected both parent and index')\n      }\n\n      return looksLikeANode(node)\n        ? check.call(context, node, index, parent)\n        : false\n    }\n  )\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param {Test} test\n *   *   when nullish, checks if `node` is a `Node`.\n *   *   when `string`, works like passing `(node) => node.type === test`.\n *   *   when `function` checks if function passed the node is true.\n *   *   when `object`, checks that all keys in test are in node, and that they have (strictly) equal values.\n *   *   when `array`, checks if any one of the subtests pass.\n * @returns {Check}\n *   An assertion.\n */\nconst convert =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends string>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &\n   *   (<Condition extends Props>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &\n   *   (<Condition extends TestFunction>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &\n   *   ((test?: null | undefined) => (node?: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &\n   *   ((test?: Test) => Check)\n   * )}\n   */\n  (\n    /**\n     * @param {Test} [test]\n     * @returns {Check}\n     */\n    function (test) {\n      if (test === null || test === undefined) {\n        return ok\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      if (typeof test === 'object') {\n        return Array.isArray(test) ? anyFactory(test) : propsFactory(test)\n      }\n\n      if (typeof test === 'string') {\n        return typeFactory(test)\n      }\n\n      throw new Error('Expected function, string, or object as test')\n    }\n  )\n\n/**\n * @param {Array<Props | TestFunction | string>} tests\n * @returns {Check}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<Check>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convert(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @type {TestFunction}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].apply(this, parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn an object into a test for a node with a certain fields.\n *\n * @param {Props} check\n * @returns {Check}\n */\nfunction propsFactory(check) {\n  const checkAsRecord = /** @type {Record<string, unknown>} */ (check)\n\n  return castFactory(all)\n\n  /**\n   * @param {Node} node\n   * @returns {boolean}\n   */\n  function all(node) {\n    const nodeAsRecord = /** @type {Record<string, unknown>} */ (\n      /** @type {unknown} */ (node)\n    )\n\n    /** @type {string} */\n    let key\n\n    for (key in check) {\n      if (nodeAsRecord[key] !== checkAsRecord[key]) return false\n    }\n\n    return true\n  }\n}\n\n/**\n * Turn a string into a test for a node with a certain type.\n *\n * @param {string} check\n * @returns {Check}\n */\nfunction typeFactory(check) {\n  return castFactory(type)\n\n  /**\n   * @param {Node} node\n   */\n  function type(node) {\n    return node && node.type === check\n  }\n}\n\n/**\n * Turn a custom test into a test for a node that passes that test.\n *\n * @param {TestFunction} testFunction\n * @returns {Check}\n */\nfunction castFactory(testFunction) {\n  return check\n\n  /**\n   * @this {unknown}\n   * @type {Check}\n   */\n  function check(value, index, parent) {\n    return Boolean(\n      looksLikeANode(value) &&\n        testFunction.call(\n          this,\n          value,\n          typeof index === 'number' ? index : undefined,\n          parent || undefined\n        )\n    )\n  }\n}\n\nfunction ok() {\n  return true\n}\n\n/**\n * @param {unknown} value\n * @returns {value is Node}\n */\nfunction looksLikeANode(value) {\n  return value !== null && typeof value === 'object' && 'type' in value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/unist-util-is/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/unist-util-visit-parents/lib/color.js":
/*!************************************************************!*\
  !*** ./node_modules/unist-util-visit-parents/lib/color.js ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   color: function() { return /* binding */ color; }\n/* harmony export */ });\n/**\n * @param {string} d\n * @returns {string}\n */\nfunction color(d) {\n  return d\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91bmlzdC11dGlsLXZpc2l0LXBhcmVudHMvbGliL2NvbG9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL3VuaXN0LXV0aWwtdmlzaXQtcGFyZW50cy9saWIvY29sb3IuanM/MGFjNCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBwYXJhbSB7c3RyaW5nfSBkXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gY29sb3IoZCkge1xuICByZXR1cm4gZFxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/unist-util-visit-parents/lib/color.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/unist-util-visit-parents/lib/index.js":
/*!************************************************************!*\
  !*** ./node_modules/unist-util-visit-parents/lib/index.js ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CONTINUE: function() { return /* binding */ CONTINUE; },\n/* harmony export */   EXIT: function() { return /* binding */ EXIT; },\n/* harmony export */   SKIP: function() { return /* binding */ SKIP; },\n/* harmony export */   visitParents: function() { return /* binding */ visitParents; }\n/* harmony export */ });\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-is */ \"(app-pages-browser)/./node_modules/unist-util-is/lib/index.js\");\n/* harmony import */ var unist_util_visit_parents_do_not_use_color__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit-parents/do-not-use-color */ \"(app-pages-browser)/./node_modules/unist-util-visit-parents/lib/color.js\");\n/**\n * @typedef {import('unist').Node} UnistNode\n * @typedef {import('unist').Parent} UnistParent\n */\n\n/**\n * @typedef {Exclude<import('unist-util-is').Test, undefined> | undefined} Test\n *   Test from `unist-util-is`.\n *\n *   Note: we have remove and add `undefined`, because otherwise when generating\n *   automatic `.d.ts` files, TS tries to flatten paths from a local perspective,\n *   which doesn’t work when publishing on npm.\n */\n\n/**\n * @typedef {(\n *   Fn extends (value: any) => value is infer Thing\n *   ? Thing\n *   : Fallback\n * )} Predicate\n *   Get the value of a type guard `Fn`.\n * @template Fn\n *   Value; typically function that is a type guard (such as `(x): x is Y`).\n * @template Fallback\n *   Value to yield if `Fn` is not a type guard.\n */\n\n/**\n * @typedef {(\n *   Check extends null | undefined // No test.\n *   ? Value\n *   : Value extends {type: Check} // String (type) test.\n *   ? Value\n *   : Value extends Check // Partial test.\n *   ? Value\n *   : Check extends Function // Function test.\n *   ? Predicate<Check, Value> extends Value\n *     ? Predicate<Check, Value>\n *     : never\n *   : never // Some other test?\n * )} MatchesOne\n *   Check whether a node matches a primitive check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test, but not arrays.\n */\n\n/**\n * @typedef {(\n *   Check extends Array<any>\n *   ? MatchesOne<Value, Check[keyof Check]>\n *   : MatchesOne<Value, Check>\n * )} Matches\n *   Check whether a node matches a check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test.\n */\n\n/**\n * @typedef {0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10} Uint\n *   Number; capped reasonably.\n */\n\n/**\n * @typedef {I extends 0 ? 1 : I extends 1 ? 2 : I extends 2 ? 3 : I extends 3 ? 4 : I extends 4 ? 5 : I extends 5 ? 6 : I extends 6 ? 7 : I extends 7 ? 8 : I extends 8 ? 9 : 10} Increment\n *   Increment a number in the type system.\n * @template {Uint} [I=0]\n *   Index.\n */\n\n/**\n * @typedef {(\n *   Node extends UnistParent\n *   ? Node extends {children: Array<infer Children>}\n *     ? Child extends Children ? Node : never\n *     : never\n *   : never\n * )} InternalParent\n *   Collect nodes that can be parents of `Child`.\n * @template {UnistNode} Node\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {InternalParent<InclusiveDescendant<Tree>, Child>} Parent\n *   Collect nodes in `Tree` that can be parents of `Child`.\n * @template {UnistNode} Tree\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {(\n *   Depth extends Max\n *   ? never\n *   :\n *     | InternalParent<Node, Child>\n *     | InternalAncestor<Node, InternalParent<Node, Child>, Max, Increment<Depth>>\n * )} InternalAncestor\n *   Collect nodes in `Tree` that can be ancestors of `Child`.\n * @template {UnistNode} Node\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n * @template {Uint} [Max=10]\n *   Max; searches up to this depth.\n * @template {Uint} [Depth=0]\n *   Current depth.\n */\n\n/**\n * @typedef {InternalAncestor<InclusiveDescendant<Tree>, Child>} Ancestor\n *   Collect nodes in `Tree` that can be ancestors of `Child`.\n * @template {UnistNode} Tree\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {(\n *   Tree extends UnistParent\n *     ? Depth extends Max\n *       ? Tree\n *       : Tree | InclusiveDescendant<Tree['children'][number], Max, Increment<Depth>>\n *     : Tree\n * )} InclusiveDescendant\n *   Collect all (inclusive) descendants of `Tree`.\n *\n *   > 👉 **Note**: for performance reasons, this seems to be the fastest way to\n *   > recurse without actually running into an infinite loop, which the\n *   > previous version did.\n *   >\n *   > Practically, a max of `2` is typically enough assuming a `Root` is\n *   > passed, but it doesn’t improve performance.\n *   > It gets higher with `List > ListItem > Table > TableRow > TableCell`.\n *   > Using up to `10` doesn’t hurt or help either.\n * @template {UnistNode} Tree\n *   Tree type.\n * @template {Uint} [Max=10]\n *   Max; searches up to this depth.\n * @template {Uint} [Depth=0]\n *   Current depth.\n */\n\n/**\n * @typedef {'skip' | boolean} Action\n *   Union of the action types.\n *\n * @typedef {number} Index\n *   Move to the sibling at `index` next (after node itself is completely\n *   traversed).\n *\n *   Useful if mutating the tree, such as removing the node the visitor is\n *   currently on, or any of its previous siblings.\n *   Results less than 0 or greater than or equal to `children.length` stop\n *   traversing the parent.\n *\n * @typedef {[(Action | null | undefined | void)?, (Index | null | undefined)?]} ActionTuple\n *   List with one or two values, the first an action, the second an index.\n *\n * @typedef {Action | ActionTuple | Index | null | undefined | void} VisitorResult\n *   Any value that can be returned from a visitor.\n */\n\n/**\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform the parent of node (the last of `ancestors`).\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of an ancestor still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Array<VisitedParents>} ancestors\n *   Ancestors of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n * @template {UnistNode} [Visited=UnistNode]\n *   Visited node type.\n * @template {UnistParent} [VisitedParents=UnistParent]\n *   Ancestor type.\n */\n\n/**\n * @typedef {Visitor<Matches<InclusiveDescendant<Tree>, Check>, Ancestor<Tree, Matches<InclusiveDescendant<Tree>, Check>>>} BuildVisitor\n *   Build a typed `Visitor` function from a tree and a test.\n *\n *   It will infer which values are passed as `node` and which as `parents`.\n * @template {UnistNode} [Tree=UnistNode]\n *   Tree type.\n * @template {Test} [Check=Test]\n *   Test type.\n */\n\n\n\n\n/** @type {Readonly<ActionTuple>} */\nconst empty = []\n\n/**\n * Continue traversing as normal.\n */\nconst CONTINUE = true\n\n/**\n * Stop traversing immediately.\n */\nconst EXIT = false\n\n/**\n * Do not traverse this node’s children.\n */\nconst SKIP = 'skip'\n\n/**\n * Visit nodes, with ancestral information.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @overload\n * @param {Tree} tree\n * @param {Check} check\n * @param {BuildVisitor<Tree, Check>} visitor\n * @param {boolean | null | undefined} [reverse]\n * @returns {undefined}\n *\n * @overload\n * @param {Tree} tree\n * @param {BuildVisitor<Tree>} visitor\n * @param {boolean | null | undefined} [reverse]\n * @returns {undefined}\n *\n * @param {UnistNode} tree\n *   Tree to traverse.\n * @param {Visitor | Test} test\n *   `unist-util-is`-compatible test\n * @param {Visitor | boolean | null | undefined} [visitor]\n *   Handle each node.\n * @param {boolean | null | undefined} [reverse]\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns {undefined}\n *   Nothing.\n *\n * @template {UnistNode} Tree\n *   Node type.\n * @template {Test} Check\n *   `unist-util-is`-compatible test.\n */\nfunction visitParents(tree, test, visitor, reverse) {\n  /** @type {Test} */\n  let check\n\n  if (typeof test === 'function' && typeof visitor !== 'function') {\n    reverse = visitor\n    // @ts-expect-error no visitor given, so `visitor` is test.\n    visitor = test\n  } else {\n    // @ts-expect-error visitor given, so `test` isn’t a visitor.\n    check = test\n  }\n\n  const is = (0,unist_util_is__WEBPACK_IMPORTED_MODULE_0__.convert)(check)\n  const step = reverse ? -1 : 1\n\n  factory(tree, undefined, [])()\n\n  /**\n   * @param {UnistNode} node\n   * @param {number | undefined} index\n   * @param {Array<UnistParent>} parents\n   */\n  function factory(node, index, parents) {\n    const value = /** @type {Record<string, unknown>} */ (\n      node && typeof node === 'object' ? node : {}\n    )\n\n    if (typeof value.type === 'string') {\n      const name =\n        // `hast`\n        typeof value.tagName === 'string'\n          ? value.tagName\n          : // `xast`\n          typeof value.name === 'string'\n          ? value.name\n          : undefined\n\n      Object.defineProperty(visit, 'name', {\n        value:\n          'node (' + (0,unist_util_visit_parents_do_not_use_color__WEBPACK_IMPORTED_MODULE_1__.color)(node.type + (name ? '<' + name + '>' : '')) + ')'\n      })\n    }\n\n    return visit\n\n    function visit() {\n      /** @type {Readonly<ActionTuple>} */\n      let result = empty\n      /** @type {Readonly<ActionTuple>} */\n      let subresult\n      /** @type {number} */\n      let offset\n      /** @type {Array<UnistParent>} */\n      let grandparents\n\n      if (!test || is(node, index, parents[parents.length - 1] || undefined)) {\n        // @ts-expect-error: `visitor` is now a visitor.\n        result = toResult(visitor(node, parents))\n\n        if (result[0] === EXIT) {\n          return result\n        }\n      }\n\n      if ('children' in node && node.children) {\n        const nodeAsParent = /** @type {UnistParent} */ (node)\n\n        if (nodeAsParent.children && result[0] !== SKIP) {\n          offset = (reverse ? nodeAsParent.children.length : -1) + step\n          grandparents = parents.concat(nodeAsParent)\n\n          while (offset > -1 && offset < nodeAsParent.children.length) {\n            const child = nodeAsParent.children[offset]\n\n            subresult = factory(child, offset, grandparents)()\n\n            if (subresult[0] === EXIT) {\n              return subresult\n            }\n\n            offset =\n              typeof subresult[1] === 'number' ? subresult[1] : offset + step\n          }\n        }\n      }\n\n      return result\n    }\n  }\n}\n\n/**\n * Turn a return value into a clean result.\n *\n * @param {VisitorResult} value\n *   Valid return values from visitors.\n * @returns {Readonly<ActionTuple>}\n *   Clean result.\n */\nfunction toResult(value) {\n  if (Array.isArray(value)) {\n    return value\n  }\n\n  if (typeof value === 'number') {\n    return [CONTINUE, value]\n  }\n\n  return value === null || value === undefined ? empty : [value]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/unist-util-visit-parents/lib/index.js\n"));

/***/ })

}]);