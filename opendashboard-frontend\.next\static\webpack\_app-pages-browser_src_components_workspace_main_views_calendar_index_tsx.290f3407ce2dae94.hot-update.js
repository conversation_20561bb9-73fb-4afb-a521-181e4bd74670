"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx":
/*!******************************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx ***!
  \******************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarEventSegment: function() { return /* binding */ CalendarEventSegment; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _utils_color__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/color */ \"(app-pages-browser)/./src/utils/color.ts\");\n/* harmony import */ var _utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/dateUtils */ \"(app-pages-browser)/./src/utils/dateUtils.ts\");\n/* harmony import */ var _utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/multiDayEventUtils */ \"(app-pages-browser)/./src/utils/multiDayEventUtils.ts\");\n/* harmony import */ var _MultiDayEventBadge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./MultiDayEventBadge */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MultiDayEventBadge.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CalendarEventSegment = (param)=>{\n    let { segment, style, onClick, onContextMenu, view = \"month\", isEndOfEvent, isDragging } = param;\n    _s();\n    const dragRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { attributes, listeners, setNodeRef, isDragging: dndIsDragging } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_7__.useDraggable)({\n        id: \"segment-\".concat(segment.id),\n        data: {\n            type: \"segment\",\n            payload: segment\n        }\n    });\n    const combinedIsDragging = isDragging || dndIsDragging;\n    // Memoize event calculations\n    const eventDetails = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const eventHeight = (style === null || style === void 0 ? void 0 : style.height) ? parseInt(style.height.toString().replace(\"px\", \"\")) : null;\n        const showTime = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.shouldShowTimeInSegment)(segment, view);\n        const continuationText = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentContinuationText)(segment);\n        // let formattedTime = null;\n        // if (segment.isAllDay) {\n        //   formattedTime = 'All day';\n        // } else if (showTime) {\n        //   formattedTime = formatEventTime(segment.startTime, view, { shortFormat: true });\n        // }\n        return {\n            eventSize: (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.getEventSize)(eventHeight),\n            showTime,\n            continuationText,\n            formattedTime: showTime ? (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.formatEventTime)(segment.startTime, view, {\n                shortFormat: true\n            }) : null\n        };\n    }, [\n        segment,\n        style,\n        view\n    ]);\n    // Memoize styling\n    const eventStyles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const denimColorInfo = (0,_utils_color__WEBPACK_IMPORTED_MODULE_3__.ColorInfo)(\"Denim\");\n        const stylingClasses = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentStylingClasses)(segment);\n        // Extract RGB values from the rgba string and make it fully opaque\n        const rgbMatch = denimColorInfo.bg.match(/rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)/);\n        const opaqueBackground = rgbMatch ? \"rgb(\".concat(rgbMatch[1], \", \").concat(rgbMatch[2], \", \").concat(rgbMatch[3], \")\") : denimColorInfo.bg;\n        return {\n            style: {\n                ...style,\n                backgroundColor: opaqueBackground,\n                minHeight: \"24px\",\n                // Add subtle shadow for better visual depth\n                boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)\",\n                opacity: combinedIsDragging ? 0.5 : 1\n            },\n            classes: stylingClasses\n        };\n    }, [\n        style,\n        segment,\n        combinedIsDragging\n    ]);\n    // Memoize classes\n    const eventClasses = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const baseClasses = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"select-none text-black text-xs overflow-hidden relative\", !combinedIsDragging && \"cursor-pointer\", eventStyles.classes.roundedCorners, eventStyles.classes.continuationIndicator, eventStyles.classes.opacity, \"p-1\");\n        // Month view or small events\n        if (view === \"month\" || eventDetails.eventSize === \"small\") {\n            return {\n                baseClasses,\n                containerClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-1 flex-nowrap\"),\n                titleClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-medium truncate leading-tight text-xs overflow-hidden\", segment.isMultiDay ? \"max-w-[60%]\" : \"max-w-[70%]\"),\n                timeClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"opacity-75 text-xs flex-shrink-0 text-[0.65rem]\"),\n                continuationClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-xs opacity-60 flex-shrink-0 text-[0.6rem]\")\n            };\n        }\n        // Day and Week views for medium/large events\n        return {\n            baseClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, \"p-2\"),\n            containerClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col\", \"space-y-0.5\"),\n            titleClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-medium truncate leading-tight text-xs overflow-hidden\"),\n            timeClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"opacity-75 text-xs\"),\n            continuationClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-xs opacity-60\")\n        };\n    }, [\n        eventDetails,\n        view,\n        segment.isMultiDay,\n        eventStyles.classes,\n        combinedIsDragging\n    ]);\n    // Render event content based on view and size\n    const renderEventContent = ()=>{\n        const event = segment.originalEvent;\n        // Month view or small events - horizontal layout\n        if (view === \"month\" || eventDetails.eventSize === \"small\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: eventClasses.containerClasses,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: eventClasses.titleClasses,\n                        children: event.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, undefined),\n                    eventDetails.showTime && eventDetails.formattedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: eventClasses.timeClasses,\n                        children: eventDetails.formattedTime\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 13\n                    }, undefined),\n                    segment.isMultiDay && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultiDayEventBadge__WEBPACK_IMPORTED_MODULE_6__.MultiDayEventBadge, {\n                        segment: segment,\n                        view: view,\n                        size: \"small\",\n                        className: eventClasses.continuationClasses,\n                        isEndOfEvent: isEndOfEvent\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, undefined);\n        }\n        // Day and Week views - vertical layout\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: eventClasses.containerClasses,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: eventClasses.titleClasses,\n                    children: [\n                        event.title,\n                        segment.isMultiDay && !eventDetails.showTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultiDayEventBadge__WEBPACK_IMPORTED_MODULE_6__.ContinuationArrow, {\n                            direction: segment.isFirstSegment ? \"right\" : segment.isLastSegment ? \"left\" : \"both\",\n                            className: \"ml-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, undefined),\n                (eventDetails.showTime || segment.isAllDay) && eventDetails.formattedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: eventClasses.timeClasses,\n                    children: [\n                        eventDetails.formattedTime,\n                        segment.isMultiDay && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultiDayEventBadge__WEBPACK_IMPORTED_MODULE_6__.ContinuationArrow, {\n                            direction: segment.isFirstSegment ? \"right\" : segment.isLastSegment ? \"left\" : \"both\",\n                            className: \"ml-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 11\n                }, undefined),\n                segment.isMultiDay && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: eventClasses.continuationClasses,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultiDayEventBadge__WEBPACK_IMPORTED_MODULE_6__.MultiDayEventBadge, {\n                        segment: segment,\n                        view: view,\n                        size: eventDetails.eventSize === \"large\" ? \"medium\" : \"small\",\n                        isEndOfEvent: isEndOfEvent\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n            lineNumber: 170,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"segment-\".concat(segment.id),\n        \"data-dnd-id\": \"segment-\".concat(segment.id),\n        ref: (node)=>{\n            setNodeRef(node);\n            dragRef.current = node;\n        },\n        style: {\n            ...eventStyles.style,\n            zIndex: combinedIsDragging ? 1000 : \"auto\" // Ensure dragged item is on top\n        },\n        className: eventClasses.baseClasses,\n        onClick: onClick,\n        onContextMenu: onContextMenu,\n        ...listeners,\n        ...attributes,\n        children: renderEventContent()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventSegment.tsx\",\n        lineNumber: 206,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CalendarEventSegment, \"kiz8nz2pAp5RwnBQHmiQwwayVv8=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_7__.useDraggable\n    ];\n});\n_c = CalendarEventSegment;\nvar _c;\n$RefreshReg$(_c, \"CalendarEventSegment\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\n"));

/***/ })

});