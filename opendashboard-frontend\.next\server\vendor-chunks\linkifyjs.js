"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/linkifyjs";
exports.ids = ["vendor-chunks/linkifyjs"];
exports.modules = {

/***/ "(ssr)/./node_modules/linkifyjs/dist/linkify.mjs":
/*!*************************************************!*\
  !*** ./node_modules/linkifyjs/dist/linkify.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MultiToken: () => (/* binding */ MultiToken),\n/* harmony export */   Options: () => (/* binding */ Options),\n/* harmony export */   State: () => (/* binding */ State),\n/* harmony export */   createTokenClass: () => (/* binding */ createTokenClass),\n/* harmony export */   find: () => (/* binding */ find),\n/* harmony export */   init: () => (/* binding */ init),\n/* harmony export */   multi: () => (/* binding */ multi),\n/* harmony export */   options: () => (/* binding */ options),\n/* harmony export */   regexp: () => (/* binding */ regexp),\n/* harmony export */   registerCustomProtocol: () => (/* binding */ registerCustomProtocol),\n/* harmony export */   registerPlugin: () => (/* binding */ registerPlugin),\n/* harmony export */   registerTokenPlugin: () => (/* binding */ registerTokenPlugin),\n/* harmony export */   reset: () => (/* binding */ reset),\n/* harmony export */   stringToArray: () => (/* binding */ stringToArray),\n/* harmony export */   test: () => (/* binding */ test),\n/* harmony export */   text: () => (/* binding */ multi),\n/* harmony export */   tokenize: () => (/* binding */ tokenize)\n/* harmony export */ });\n// THIS FILE IS AUTOMATICALLY GENERATED DO NOT EDIT DIRECTLY\n// See update-tlds.js for encoding/decoding format\n// https://data.iana.org/TLD/tlds-alpha-by-domain.txt\nconst encodedTlds = 'aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5mögensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2';\n// Internationalized domain names containing non-ASCII\nconst encodedUtlds = 'ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2';\n\n/**\n * @template A\n * @template B\n * @param {A} target\n * @param {B} properties\n * @return {A & B}\n */\nconst assign = (target, properties) => {\n  for (const key in properties) {\n    target[key] = properties[key];\n  }\n  return target;\n};\n\n/**\n * Finite State Machine generation utilities\n */\n\n/**\n * @template T\n * @typedef {{ [group: string]: T[] }} Collections\n */\n\n/**\n * @typedef {{ [group: string]: true }} Flags\n */\n\n// Keys in scanner Collections instances\nconst numeric = 'numeric';\nconst ascii = 'ascii';\nconst alpha = 'alpha';\nconst asciinumeric = 'asciinumeric';\nconst alphanumeric = 'alphanumeric';\nconst domain = 'domain';\nconst emoji = 'emoji';\nconst scheme = 'scheme';\nconst slashscheme = 'slashscheme';\nconst whitespace = 'whitespace';\n\n/**\n * @template T\n * @param {string} name\n * @param {Collections<T>} groups to register in\n * @returns {T[]} Current list of tokens in the given collection\n */\nfunction registerGroup(name, groups) {\n  if (!(name in groups)) {\n    groups[name] = [];\n  }\n  return groups[name];\n}\n\n/**\n * @template T\n * @param {T} t token to add\n * @param {Collections<T>} groups\n * @param {Flags} flags\n */\nfunction addToGroups(t, flags, groups) {\n  if (flags[numeric]) {\n    flags[asciinumeric] = true;\n    flags[alphanumeric] = true;\n  }\n  if (flags[ascii]) {\n    flags[asciinumeric] = true;\n    flags[alpha] = true;\n  }\n  if (flags[asciinumeric]) {\n    flags[alphanumeric] = true;\n  }\n  if (flags[alpha]) {\n    flags[alphanumeric] = true;\n  }\n  if (flags[alphanumeric]) {\n    flags[domain] = true;\n  }\n  if (flags[emoji]) {\n    flags[domain] = true;\n  }\n  for (const k in flags) {\n    const group = registerGroup(k, groups);\n    if (group.indexOf(t) < 0) {\n      group.push(t);\n    }\n  }\n}\n\n/**\n * @template T\n * @param {T} t token to check\n * @param {Collections<T>} groups\n * @returns {Flags} group flags that contain this token\n */\nfunction flagsForToken(t, groups) {\n  const result = {};\n  for (const c in groups) {\n    if (groups[c].indexOf(t) >= 0) {\n      result[c] = true;\n    }\n  }\n  return result;\n}\n\n/**\n * @template T\n * @typedef {null | T } Transition\n */\n\n/**\n * Define a basic state machine state. j is the list of character transitions,\n * jr is the list of regex-match transitions, jd is the default state to\n * transition to t is the accepting token type, if any. If this is the terminal\n * state, then it does not emit a token.\n *\n * The template type T represents the type of the token this state accepts. This\n * should be a string (such as of the token exports in `text.js`) or a\n * MultiToken subclass (from `multi.js`)\n *\n * @template T\n * @param {T} [token] Token that this state emits\n */\nfunction State(token = null) {\n  // this.n = null; // DEBUG: State name\n  /** @type {{ [input: string]: State<T> }} j */\n  this.j = {}; // IMPLEMENTATION 1\n  // this.j = []; // IMPLEMENTATION 2\n  /** @type {[RegExp, State<T>][]} jr */\n  this.jr = [];\n  /** @type {?State<T>} jd */\n  this.jd = null;\n  /** @type {?T} t */\n  this.t = token;\n}\n\n/**\n * Scanner token groups\n * @type Collections<string>\n */\nState.groups = {};\nState.prototype = {\n  accepts() {\n    return !!this.t;\n  },\n  /**\n   * Follow an existing transition from the given input to the next state.\n   * Does not mutate.\n   * @param {string} input character or token type to transition on\n   * @returns {?State<T>} the next state, if any\n   */\n  go(input) {\n    const state = this;\n    const nextState = state.j[input];\n    if (nextState) {\n      return nextState;\n    }\n    for (let i = 0; i < state.jr.length; i++) {\n      const regex = state.jr[i][0];\n      const nextState = state.jr[i][1]; // note: might be empty to prevent default jump\n      if (nextState && regex.test(input)) {\n        return nextState;\n      }\n    }\n    // Nowhere left to jump! Return default, if any\n    return state.jd;\n  },\n  /**\n   * Whether the state has a transition for the given input. Set the second\n   * argument to true to only look for an exact match (and not a default or\n   * regular-expression-based transition)\n   * @param {string} input\n   * @param {boolean} exactOnly\n   */\n  has(input, exactOnly = false) {\n    return exactOnly ? input in this.j : !!this.go(input);\n  },\n  /**\n   * Short for \"transition all\"; create a transition from the array of items\n   * in the given list to the same final resulting state.\n   * @param {string | string[]} inputs Group of inputs to transition on\n   * @param {Transition<T> | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of token groups\n   */\n  ta(inputs, next, flags, groups) {\n    for (let i = 0; i < inputs.length; i++) {\n      this.tt(inputs[i], next, flags, groups);\n    }\n  },\n  /**\n   * Short for \"take regexp transition\"; defines a transition for this state\n   * when it encounters a token which matches the given regular expression\n   * @param {RegExp} regexp Regular expression transition (populate first)\n   * @param {T | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of token groups\n   * @returns {State<T>} taken after the given input\n   */\n  tr(regexp, next, flags, groups) {\n    groups = groups || State.groups;\n    let nextState;\n    if (next && next.j) {\n      nextState = next;\n    } else {\n      // Token with maybe token groups\n      nextState = new State(next);\n      if (flags && groups) {\n        addToGroups(next, flags, groups);\n      }\n    }\n    this.jr.push([regexp, nextState]);\n    return nextState;\n  },\n  /**\n   * Short for \"take transitions\", will take as many sequential transitions as\n   * the length of the given input and returns the\n   * resulting final state.\n   * @param {string | string[]} input\n   * @param {T | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of token groups\n   * @returns {State<T>} taken after the given input\n   */\n  ts(input, next, flags, groups) {\n    let state = this;\n    const len = input.length;\n    if (!len) {\n      return state;\n    }\n    for (let i = 0; i < len - 1; i++) {\n      state = state.tt(input[i]);\n    }\n    return state.tt(input[len - 1], next, flags, groups);\n  },\n  /**\n   * Short for \"take transition\", this is a method for building/working with\n   * state machines.\n   *\n   * If a state already exists for the given input, returns it.\n   *\n   * If a token is specified, that state will emit that token when reached by\n   * the linkify engine.\n   *\n   * If no state exists, it will be initialized with some default transitions\n   * that resemble existing default transitions.\n   *\n   * If a state is given for the second argument, that state will be\n   * transitioned to on the given input regardless of what that input\n   * previously did.\n   *\n   * Specify a token group flags to define groups that this token belongs to.\n   * The token will be added to corresponding entires in the given groups\n   * object.\n   *\n   * @param {string} input character, token type to transition on\n   * @param {T | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of groups\n   * @returns {State<T>} taken after the given input\n   */\n  tt(input, next, flags, groups) {\n    groups = groups || State.groups;\n    const state = this;\n\n    // Check if existing state given, just a basic transition\n    if (next && next.j) {\n      state.j[input] = next;\n      return next;\n    }\n    const t = next;\n\n    // Take the transition with the usual default mechanisms and use that as\n    // a template for creating the next state\n    let nextState,\n      templateState = state.go(input);\n    if (templateState) {\n      nextState = new State();\n      assign(nextState.j, templateState.j);\n      nextState.jr.push.apply(nextState.jr, templateState.jr);\n      nextState.jd = templateState.jd;\n      nextState.t = templateState.t;\n    } else {\n      nextState = new State();\n    }\n    if (t) {\n      // Ensure newly token is in the same groups as the old token\n      if (groups) {\n        if (nextState.t && typeof nextState.t === 'string') {\n          const allFlags = assign(flagsForToken(nextState.t, groups), flags);\n          addToGroups(t, allFlags, groups);\n        } else if (flags) {\n          addToGroups(t, flags, groups);\n        }\n      }\n      nextState.t = t; // overwrite anything that was previously there\n    }\n    state.j[input] = nextState;\n    return nextState;\n  }\n};\n\n// Helper functions to improve minification (not exported outside linkifyjs module)\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {string | string[]} input\n * @param {Flags} [flags]\n * @param {Collections<T>} [groups]\n */\nconst ta = (state, input, next, flags, groups) => state.ta(input, next, flags, groups);\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {RegExp} regexp\n * @param {T | State<T>} [next]\n * @param {Flags} [flags]\n * @param {Collections<T>} [groups]\n */\nconst tr = (state, regexp, next, flags, groups) => state.tr(regexp, next, flags, groups);\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {string | string[]} input\n * @param {T | State<T>} [next]\n * @param {Flags} [flags]\n * @param {Collections<T>} [groups]\n */\nconst ts = (state, input, next, flags, groups) => state.ts(input, next, flags, groups);\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {string} input\n * @param {T | State<T>} [next]\n * @param {Collections<T>} [groups]\n * @param {Flags} [flags]\n */\nconst tt = (state, input, next, flags, groups) => state.tt(input, next, flags, groups);\n\n/******************************************************************************\nText Tokens\nIdentifiers for token outputs from the regexp scanner\n******************************************************************************/\n\n// A valid web domain token\nconst WORD = 'WORD'; // only contains a-z\nconst UWORD = 'UWORD'; // contains letters other than a-z, used for IDN\nconst ASCIINUMERICAL = 'ASCIINUMERICAL'; // contains a-z, 0-9\nconst ALPHANUMERICAL = 'ALPHANUMERICAL'; // contains numbers and letters other than a-z, used for IDN\n\n// Special case of word\nconst LOCALHOST = 'LOCALHOST';\n\n// Valid top-level domain, special case of WORD (see tlds.js)\nconst TLD = 'TLD';\n\n// Valid IDN TLD, special case of UWORD (see tlds.js)\nconst UTLD = 'UTLD';\n\n// The scheme portion of a web URI protocol. Supported types include: `mailto`,\n// `file`, and user-defined custom protocols. Limited to schemes that contain\n// only letters\nconst SCHEME = 'SCHEME';\n\n// Similar to SCHEME, except makes distinction for schemes that must always be\n// followed by `://`, not just `:`. Supported types include `http`, `https`,\n// `ftp`, `ftps`\nconst SLASH_SCHEME = 'SLASH_SCHEME';\n\n// Any sequence of digits 0-9\nconst NUM = 'NUM';\n\n// Any number of consecutive whitespace characters that are not newline\nconst WS = 'WS';\n\n// New line (unix style)\nconst NL = 'NL'; // \\n\n\n// Opening/closing bracket classes\n// TODO: Rename OPEN -> LEFT and CLOSE -> RIGHT in v5 to fit with Unicode names\n// Also rename angle brackes to LESSTHAN and GREATER THAN\nconst OPENBRACE = 'OPENBRACE'; // {\nconst CLOSEBRACE = 'CLOSEBRACE'; // }\nconst OPENBRACKET = 'OPENBRACKET'; // [\nconst CLOSEBRACKET = 'CLOSEBRACKET'; // ]\nconst OPENPAREN = 'OPENPAREN'; // (\nconst CLOSEPAREN = 'CLOSEPAREN'; // )\nconst OPENANGLEBRACKET = 'OPENANGLEBRACKET'; // <\nconst CLOSEANGLEBRACKET = 'CLOSEANGLEBRACKET'; // >\nconst FULLWIDTHLEFTPAREN = 'FULLWIDTHLEFTPAREN'; // （\nconst FULLWIDTHRIGHTPAREN = 'FULLWIDTHRIGHTPAREN'; // ）\nconst LEFTCORNERBRACKET = 'LEFTCORNERBRACKET'; // 「\nconst RIGHTCORNERBRACKET = 'RIGHTCORNERBRACKET'; // 」\nconst LEFTWHITECORNERBRACKET = 'LEFTWHITECORNERBRACKET'; // 『\nconst RIGHTWHITECORNERBRACKET = 'RIGHTWHITECORNERBRACKET'; // 』\nconst FULLWIDTHLESSTHAN = 'FULLWIDTHLESSTHAN'; // ＜\nconst FULLWIDTHGREATERTHAN = 'FULLWIDTHGREATERTHAN'; // ＞\n\n// Various symbols\nconst AMPERSAND = 'AMPERSAND'; // &\nconst APOSTROPHE = 'APOSTROPHE'; // '\nconst ASTERISK = 'ASTERISK'; // *\nconst AT = 'AT'; // @\nconst BACKSLASH = 'BACKSLASH'; // \\\nconst BACKTICK = 'BACKTICK'; // `\nconst CARET = 'CARET'; // ^\nconst COLON = 'COLON'; // :\nconst COMMA = 'COMMA'; // ,\nconst DOLLAR = 'DOLLAR'; // $\nconst DOT = 'DOT'; // .\nconst EQUALS = 'EQUALS'; // =\nconst EXCLAMATION = 'EXCLAMATION'; // !\nconst HYPHEN = 'HYPHEN'; // -\nconst PERCENT = 'PERCENT'; // %\nconst PIPE = 'PIPE'; // |\nconst PLUS = 'PLUS'; // +\nconst POUND = 'POUND'; // #\nconst QUERY = 'QUERY'; // ?\nconst QUOTE = 'QUOTE'; // \"\nconst FULLWIDTHMIDDLEDOT = 'FULLWIDTHMIDDLEDOT'; // ・\n\nconst SEMI = 'SEMI'; // ;\nconst SLASH = 'SLASH'; // /\nconst TILDE = 'TILDE'; // ~\nconst UNDERSCORE = 'UNDERSCORE'; // _\n\n// Emoji symbol\nconst EMOJI$1 = 'EMOJI';\n\n// Default token - anything that is not one of the above\nconst SYM = 'SYM';\n\nvar tk = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tALPHANUMERICAL: ALPHANUMERICAL,\n\tAMPERSAND: AMPERSAND,\n\tAPOSTROPHE: APOSTROPHE,\n\tASCIINUMERICAL: ASCIINUMERICAL,\n\tASTERISK: ASTERISK,\n\tAT: AT,\n\tBACKSLASH: BACKSLASH,\n\tBACKTICK: BACKTICK,\n\tCARET: CARET,\n\tCLOSEANGLEBRACKET: CLOSEANGLEBRACKET,\n\tCLOSEBRACE: CLOSEBRACE,\n\tCLOSEBRACKET: CLOSEBRACKET,\n\tCLOSEPAREN: CLOSEPAREN,\n\tCOLON: COLON,\n\tCOMMA: COMMA,\n\tDOLLAR: DOLLAR,\n\tDOT: DOT,\n\tEMOJI: EMOJI$1,\n\tEQUALS: EQUALS,\n\tEXCLAMATION: EXCLAMATION,\n\tFULLWIDTHGREATERTHAN: FULLWIDTHGREATERTHAN,\n\tFULLWIDTHLEFTPAREN: FULLWIDTHLEFTPAREN,\n\tFULLWIDTHLESSTHAN: FULLWIDTHLESSTHAN,\n\tFULLWIDTHMIDDLEDOT: FULLWIDTHMIDDLEDOT,\n\tFULLWIDTHRIGHTPAREN: FULLWIDTHRIGHTPAREN,\n\tHYPHEN: HYPHEN,\n\tLEFTCORNERBRACKET: LEFTCORNERBRACKET,\n\tLEFTWHITECORNERBRACKET: LEFTWHITECORNERBRACKET,\n\tLOCALHOST: LOCALHOST,\n\tNL: NL,\n\tNUM: NUM,\n\tOPENANGLEBRACKET: OPENANGLEBRACKET,\n\tOPENBRACE: OPENBRACE,\n\tOPENBRACKET: OPENBRACKET,\n\tOPENPAREN: OPENPAREN,\n\tPERCENT: PERCENT,\n\tPIPE: PIPE,\n\tPLUS: PLUS,\n\tPOUND: POUND,\n\tQUERY: QUERY,\n\tQUOTE: QUOTE,\n\tRIGHTCORNERBRACKET: RIGHTCORNERBRACKET,\n\tRIGHTWHITECORNERBRACKET: RIGHTWHITECORNERBRACKET,\n\tSCHEME: SCHEME,\n\tSEMI: SEMI,\n\tSLASH: SLASH,\n\tSLASH_SCHEME: SLASH_SCHEME,\n\tSYM: SYM,\n\tTILDE: TILDE,\n\tTLD: TLD,\n\tUNDERSCORE: UNDERSCORE,\n\tUTLD: UTLD,\n\tUWORD: UWORD,\n\tWORD: WORD,\n\tWS: WS\n});\n\n// Note that these two Unicode ones expand into a really big one with Babel\nconst ASCII_LETTER = /[a-z]/;\nconst LETTER = /\\p{L}/u; // Any Unicode character with letter data type\nconst EMOJI = /\\p{Emoji}/u; // Any Unicode emoji character\nconst EMOJI_VARIATION$1 = /\\ufe0f/;\nconst DIGIT = /\\d/;\nconst SPACE = /\\s/;\n\nvar regexp = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tASCII_LETTER: ASCII_LETTER,\n\tDIGIT: DIGIT,\n\tEMOJI: EMOJI,\n\tEMOJI_VARIATION: EMOJI_VARIATION$1,\n\tLETTER: LETTER,\n\tSPACE: SPACE\n});\n\n/**\n\tThe scanner provides an interface that takes a string of text as input, and\n\toutputs an array of tokens instances that can be used for easy URL parsing.\n*/\n\nconst CR = '\\r'; // carriage-return character\nconst LF = '\\n'; // line-feed character\nconst EMOJI_VARIATION = '\\ufe0f'; // Variation selector, follows heart and others\nconst EMOJI_JOINER = '\\u200d'; // zero-width joiner\nconst OBJECT_REPLACEMENT = '\\ufffc'; // whitespace placeholder that sometimes appears in rich text editors\n\nlet tlds = null,\n  utlds = null; // don't change so only have to be computed once\n\n/**\n * Scanner output token:\n * - `t` is the token name (e.g., 'NUM', 'EMOJI', 'TLD')\n * - `v` is the value of the token (e.g., '123', '❤️', 'com')\n * - `s` is the start index of the token in the original string\n * - `e` is the end index of the token in the original string\n * @typedef {{t: string, v: string, s: number, e: number}} Token\n */\n\n/**\n * @template T\n * @typedef {{ [collection: string]: T[] }} Collections\n */\n\n/**\n * Initialize the scanner character-based state machine for the given start\n * state\n * @param {[string, boolean][]} customSchemes List of custom schemes, where each\n * item is a length-2 tuple with the first element set to the string scheme, and\n * the second element set to `true` if the `://` after the scheme is optional\n */\nfunction init$2(customSchemes = []) {\n  // Frequently used states (name argument removed during minification)\n  /** @type Collections<string> */\n  const groups = {}; // of tokens\n  State.groups = groups;\n  /** @type State<string> */\n  const Start = new State();\n  if (tlds == null) {\n    tlds = decodeTlds(encodedTlds);\n  }\n  if (utlds == null) {\n    utlds = decodeTlds(encodedUtlds);\n  }\n\n  // States for special URL symbols that accept immediately after start\n  tt(Start, \"'\", APOSTROPHE);\n  tt(Start, '{', OPENBRACE);\n  tt(Start, '}', CLOSEBRACE);\n  tt(Start, '[', OPENBRACKET);\n  tt(Start, ']', CLOSEBRACKET);\n  tt(Start, '(', OPENPAREN);\n  tt(Start, ')', CLOSEPAREN);\n  tt(Start, '<', OPENANGLEBRACKET);\n  tt(Start, '>', CLOSEANGLEBRACKET);\n  tt(Start, '（', FULLWIDTHLEFTPAREN);\n  tt(Start, '）', FULLWIDTHRIGHTPAREN);\n  tt(Start, '「', LEFTCORNERBRACKET);\n  tt(Start, '」', RIGHTCORNERBRACKET);\n  tt(Start, '『', LEFTWHITECORNERBRACKET);\n  tt(Start, '』', RIGHTWHITECORNERBRACKET);\n  tt(Start, '＜', FULLWIDTHLESSTHAN);\n  tt(Start, '＞', FULLWIDTHGREATERTHAN);\n  tt(Start, '&', AMPERSAND);\n  tt(Start, '*', ASTERISK);\n  tt(Start, '@', AT);\n  tt(Start, '`', BACKTICK);\n  tt(Start, '^', CARET);\n  tt(Start, ':', COLON);\n  tt(Start, ',', COMMA);\n  tt(Start, '$', DOLLAR);\n  tt(Start, '.', DOT);\n  tt(Start, '=', EQUALS);\n  tt(Start, '!', EXCLAMATION);\n  tt(Start, '-', HYPHEN);\n  tt(Start, '%', PERCENT);\n  tt(Start, '|', PIPE);\n  tt(Start, '+', PLUS);\n  tt(Start, '#', POUND);\n  tt(Start, '?', QUERY);\n  tt(Start, '\"', QUOTE);\n  tt(Start, '/', SLASH);\n  tt(Start, ';', SEMI);\n  tt(Start, '~', TILDE);\n  tt(Start, '_', UNDERSCORE);\n  tt(Start, '\\\\', BACKSLASH);\n  tt(Start, '・', FULLWIDTHMIDDLEDOT);\n  const Num = tr(Start, DIGIT, NUM, {\n    [numeric]: true\n  });\n  tr(Num, DIGIT, Num);\n  const Asciinumeric = tr(Num, ASCII_LETTER, ASCIINUMERICAL, {\n    [asciinumeric]: true\n  });\n  const Alphanumeric = tr(Num, LETTER, ALPHANUMERICAL, {\n    [alphanumeric]: true\n  });\n\n  // State which emits a word token\n  const Word = tr(Start, ASCII_LETTER, WORD, {\n    [ascii]: true\n  });\n  tr(Word, DIGIT, Asciinumeric);\n  tr(Word, ASCII_LETTER, Word);\n  tr(Asciinumeric, DIGIT, Asciinumeric);\n  tr(Asciinumeric, ASCII_LETTER, Asciinumeric);\n\n  // Same as previous, but specific to non-fsm.ascii alphabet words\n  const UWord = tr(Start, LETTER, UWORD, {\n    [alpha]: true\n  });\n  tr(UWord, ASCII_LETTER); // Non-accepting\n  tr(UWord, DIGIT, Alphanumeric);\n  tr(UWord, LETTER, UWord);\n  tr(Alphanumeric, DIGIT, Alphanumeric);\n  tr(Alphanumeric, ASCII_LETTER); // Non-accepting\n  tr(Alphanumeric, LETTER, Alphanumeric); // Non-accepting\n\n  // Whitespace jumps\n  // Tokens of only non-newline whitespace are arbitrarily long\n  // If any whitespace except newline, more whitespace!\n  const Nl = tt(Start, LF, NL, {\n    [whitespace]: true\n  });\n  const Cr = tt(Start, CR, WS, {\n    [whitespace]: true\n  });\n  const Ws = tr(Start, SPACE, WS, {\n    [whitespace]: true\n  });\n  tt(Start, OBJECT_REPLACEMENT, Ws);\n  tt(Cr, LF, Nl); // \\r\\n\n  tt(Cr, OBJECT_REPLACEMENT, Ws);\n  tr(Cr, SPACE, Ws);\n  tt(Ws, CR); // non-accepting state to avoid mixing whitespaces\n  tt(Ws, LF); // non-accepting state to avoid mixing whitespaces\n  tr(Ws, SPACE, Ws);\n  tt(Ws, OBJECT_REPLACEMENT, Ws);\n\n  // Emoji tokens. They are not grouped by the scanner except in cases where a\n  // zero-width joiner is present\n  const Emoji = tr(Start, EMOJI, EMOJI$1, {\n    [emoji]: true\n  });\n  tt(Emoji, '#'); // no transition, emoji regex seems to match #\n  tr(Emoji, EMOJI, Emoji);\n  tt(Emoji, EMOJI_VARIATION, Emoji);\n  // tt(Start, EMOJI_VARIATION, Emoji); // This one is sketchy\n\n  const EmojiJoiner = tt(Emoji, EMOJI_JOINER);\n  tt(EmojiJoiner, '#');\n  tr(EmojiJoiner, EMOJI, Emoji);\n  // tt(EmojiJoiner, EMOJI_VARIATION, Emoji); // also sketchy\n\n  // Generates states for top-level domains\n  // Note that this is most accurate when tlds are in alphabetical order\n  const wordjr = [[ASCII_LETTER, Word], [DIGIT, Asciinumeric]];\n  const uwordjr = [[ASCII_LETTER, null], [LETTER, UWord], [DIGIT, Alphanumeric]];\n  for (let i = 0; i < tlds.length; i++) {\n    fastts(Start, tlds[i], TLD, WORD, wordjr);\n  }\n  for (let i = 0; i < utlds.length; i++) {\n    fastts(Start, utlds[i], UTLD, UWORD, uwordjr);\n  }\n  addToGroups(TLD, {\n    tld: true,\n    ascii: true\n  }, groups);\n  addToGroups(UTLD, {\n    utld: true,\n    alpha: true\n  }, groups);\n\n  // Collect the states generated by different protocols. NOTE: If any new TLDs\n  // get added that are also protocols, set the token to be the same as the\n  // protocol to ensure parsing works as expected.\n  fastts(Start, 'file', SCHEME, WORD, wordjr);\n  fastts(Start, 'mailto', SCHEME, WORD, wordjr);\n  fastts(Start, 'http', SLASH_SCHEME, WORD, wordjr);\n  fastts(Start, 'https', SLASH_SCHEME, WORD, wordjr);\n  fastts(Start, 'ftp', SLASH_SCHEME, WORD, wordjr);\n  fastts(Start, 'ftps', SLASH_SCHEME, WORD, wordjr);\n  addToGroups(SCHEME, {\n    scheme: true,\n    ascii: true\n  }, groups);\n  addToGroups(SLASH_SCHEME, {\n    slashscheme: true,\n    ascii: true\n  }, groups);\n\n  // Register custom schemes. Assumes each scheme is asciinumeric with hyphens\n  customSchemes = customSchemes.sort((a, b) => a[0] > b[0] ? 1 : -1);\n  for (let i = 0; i < customSchemes.length; i++) {\n    const sch = customSchemes[i][0];\n    const optionalSlashSlash = customSchemes[i][1];\n    const flags = optionalSlashSlash ? {\n      [scheme]: true\n    } : {\n      [slashscheme]: true\n    };\n    if (sch.indexOf('-') >= 0) {\n      flags[domain] = true;\n    } else if (!ASCII_LETTER.test(sch)) {\n      flags[numeric] = true; // numbers only\n    } else if (DIGIT.test(sch)) {\n      flags[asciinumeric] = true;\n    } else {\n      flags[ascii] = true;\n    }\n    ts(Start, sch, sch, flags);\n  }\n\n  // Localhost token\n  ts(Start, 'localhost', LOCALHOST, {\n    ascii: true\n  });\n\n  // Set default transition for start state (some symbol)\n  Start.jd = new State(SYM);\n  return {\n    start: Start,\n    tokens: assign({\n      groups\n    }, tk)\n  };\n}\n\n/**\n\tGiven a string, returns an array of TOKEN instances representing the\n\tcomposition of that string.\n\n\t@method run\n\t@param {State<string>} start scanner starting state\n\t@param {string} str input string to scan\n\t@return {Token[]} list of tokens, each with a type and value\n*/\nfunction run$1(start, str) {\n  // State machine is not case sensitive, so input is tokenized in lowercased\n  // form (still returns regular case). Uses selective `toLowerCase` because\n  // lowercasing the entire string causes the length and character position to\n  // vary in some non-English strings with V8-based runtimes.\n  const iterable = stringToArray(str.replace(/[A-Z]/g, c => c.toLowerCase()));\n  const charCount = iterable.length; // <= len if there are emojis, etc\n  const tokens = []; // return value\n\n  // cursor through the string itself, accounting for characters that have\n  // width with length 2 such as emojis\n  let cursor = 0;\n\n  // Cursor through the array-representation of the string\n  let charCursor = 0;\n\n  // Tokenize the string\n  while (charCursor < charCount) {\n    let state = start;\n    let nextState = null;\n    let tokenLength = 0;\n    let latestAccepting = null;\n    let sinceAccepts = -1;\n    let charsSinceAccepts = -1;\n    while (charCursor < charCount && (nextState = state.go(iterable[charCursor]))) {\n      state = nextState;\n\n      // Keep track of the latest accepting state\n      if (state.accepts()) {\n        sinceAccepts = 0;\n        charsSinceAccepts = 0;\n        latestAccepting = state;\n      } else if (sinceAccepts >= 0) {\n        sinceAccepts += iterable[charCursor].length;\n        charsSinceAccepts++;\n      }\n      tokenLength += iterable[charCursor].length;\n      cursor += iterable[charCursor].length;\n      charCursor++;\n    }\n\n    // Roll back to the latest accepting state\n    cursor -= sinceAccepts;\n    charCursor -= charsSinceAccepts;\n    tokenLength -= sinceAccepts;\n\n    // No more jumps, just make a new token from the last accepting one\n    tokens.push({\n      t: latestAccepting.t,\n      // token type/name\n      v: str.slice(cursor - tokenLength, cursor),\n      // string value\n      s: cursor - tokenLength,\n      // start index\n      e: cursor // end index (excluding)\n    });\n  }\n  return tokens;\n}\n\n/**\n * Convert a String to an Array of characters, taking into account that some\n * characters like emojis take up two string indexes.\n *\n * Adapted from core-js (MIT license)\n * https://github.com/zloirock/core-js/blob/2d69cf5f99ab3ea3463c395df81e5a15b68f49d9/packages/core-js/internals/string-multibyte.js\n *\n * @function stringToArray\n * @param {string} str\n * @returns {string[]}\n */\nfunction stringToArray(str) {\n  const result = [];\n  const len = str.length;\n  let index = 0;\n  while (index < len) {\n    let first = str.charCodeAt(index);\n    let second;\n    let char = first < 0xd800 || first > 0xdbff || index + 1 === len || (second = str.charCodeAt(index + 1)) < 0xdc00 || second > 0xdfff ? str[index] // single character\n    : str.slice(index, index + 2); // two-index characters\n    result.push(char);\n    index += char.length;\n  }\n  return result;\n}\n\n/**\n * Fast version of ts function for when transition defaults are well known\n * @param {State<string>} state\n * @param {string} input\n * @param {string} t\n * @param {string} defaultt\n * @param {[RegExp, State<string>][]} jr\n * @returns {State<string>}\n */\nfunction fastts(state, input, t, defaultt, jr) {\n  let next;\n  const len = input.length;\n  for (let i = 0; i < len - 1; i++) {\n    const char = input[i];\n    if (state.j[char]) {\n      next = state.j[char];\n    } else {\n      next = new State(defaultt);\n      next.jr = jr.slice();\n      state.j[char] = next;\n    }\n    state = next;\n  }\n  next = new State(t);\n  next.jr = jr.slice();\n  state.j[input[len - 1]] = next;\n  return next;\n}\n\n/**\n * Converts a string of Top-Level Domain names encoded in update-tlds.js back\n * into a list of strings.\n * @param {str} encoded encoded TLDs string\n * @returns {str[]} original TLDs list\n */\nfunction decodeTlds(encoded) {\n  const words = [];\n  const stack = [];\n  let i = 0;\n  let digits = '0123456789';\n  while (i < encoded.length) {\n    let popDigitCount = 0;\n    while (digits.indexOf(encoded[i + popDigitCount]) >= 0) {\n      popDigitCount++; // encountered some digits, have to pop to go one level up trie\n    }\n    if (popDigitCount > 0) {\n      words.push(stack.join('')); // whatever preceded the pop digits must be a word\n      for (let popCount = parseInt(encoded.substring(i, i + popDigitCount), 10); popCount > 0; popCount--) {\n        stack.pop();\n      }\n      i += popDigitCount;\n    } else {\n      stack.push(encoded[i]); // drop down a level into the trie\n      i++;\n    }\n  }\n  return words;\n}\n\n/**\n * An object where each key is a valid DOM Event Name such as `click` or `focus`\n * and each value is an event handler function.\n *\n * https://developer.mozilla.org/en-US/docs/Web/API/Element#events\n * @typedef {?{ [event: string]: Function }} EventListeners\n */\n\n/**\n * All formatted properties required to render a link, including `tagName`,\n * `attributes`, `content` and `eventListeners`.\n * @typedef {{ tagName: any, attributes: {[attr: string]: any}, content: string,\n * eventListeners: EventListeners }} IntermediateRepresentation\n */\n\n/**\n * Specify either an object described by the template type `O` or a function.\n *\n * The function takes a string value (usually the link's href attribute), the\n * link type (`'url'`, `'hashtag`', etc.) and an internal token representation\n * of the link. It should return an object of the template type `O`\n * @template O\n * @typedef {O | ((value: string, type: string, token: MultiToken) => O)} OptObj\n */\n\n/**\n * Specify either a function described by template type `F` or an object.\n *\n * Each key in the object should be a link type (`'url'`, `'hashtag`', etc.). Each\n * value should be a function with template type `F` that is called when the\n * corresponding link type is encountered.\n * @template F\n * @typedef {F | { [type: string]: F}} OptFn\n */\n\n/**\n * Specify either a value with template type `V`, a function that returns `V` or\n * an object where each value resolves to `V`.\n *\n * The function takes a string value (usually the link's href attribute), the\n * link type (`'url'`, `'hashtag`', etc.) and an internal token representation\n * of the link. It should return an object of the template type `V`\n *\n * For the object, each key should be a link type (`'url'`, `'hashtag`', etc.).\n * Each value should either have type `V` or a function that returns V. This\n * function similarly takes a string value and a token.\n *\n * Example valid types for `Opt<string>`:\n *\n * ```js\n * 'hello'\n * (value, type, token) => 'world'\n * { url: 'hello', email: (value, token) => 'world'}\n * ```\n * @template V\n * @typedef {V | ((value: string, type: string, token: MultiToken) => V) | { [type: string]: V | ((value: string, token: MultiToken) => V) }} Opt\n */\n\n/**\n * See available options: https://linkify.js.org/docs/options.html\n * @typedef {{\n * \tdefaultProtocol?: string,\n *  events?: OptObj<EventListeners>,\n * \tformat?: Opt<string>,\n * \tformatHref?: Opt<string>,\n * \tnl2br?: boolean,\n * \ttagName?: Opt<any>,\n * \ttarget?: Opt<string>,\n * \trel?: Opt<string>,\n * \tvalidate?: Opt<boolean>,\n * \ttruncate?: Opt<number>,\n * \tclassName?: Opt<string>,\n * \tattributes?: OptObj<({ [attr: string]: any })>,\n *  ignoreTags?: string[],\n * \trender?: OptFn<((ir: IntermediateRepresentation) => any)>\n * }} Opts\n */\n\n/**\n * @type Required<Opts>\n */\nconst defaults = {\n  defaultProtocol: 'http',\n  events: null,\n  format: noop,\n  formatHref: noop,\n  nl2br: false,\n  tagName: 'a',\n  target: null,\n  rel: null,\n  validate: true,\n  truncate: Infinity,\n  className: null,\n  attributes: null,\n  ignoreTags: [],\n  render: null\n};\n\n/**\n * Utility class for linkify interfaces to apply specified\n * {@link Opts formatting and rendering options}.\n *\n * @param {Opts | Options} [opts] Option value overrides.\n * @param {(ir: IntermediateRepresentation) => any} [defaultRender] (For\n *   internal use) default render function that determines how to generate an\n *   HTML element based on a link token's derived tagName, attributes and HTML.\n *   Similar to render option\n */\nfunction Options(opts, defaultRender = null) {\n  let o = assign({}, defaults);\n  if (opts) {\n    o = assign(o, opts instanceof Options ? opts.o : opts);\n  }\n\n  // Ensure all ignored tags are uppercase\n  const ignoredTags = o.ignoreTags;\n  const uppercaseIgnoredTags = [];\n  for (let i = 0; i < ignoredTags.length; i++) {\n    uppercaseIgnoredTags.push(ignoredTags[i].toUpperCase());\n  }\n  /** @protected */\n  this.o = o;\n  if (defaultRender) {\n    this.defaultRender = defaultRender;\n  }\n  this.ignoreTags = uppercaseIgnoredTags;\n}\nOptions.prototype = {\n  o: defaults,\n  /**\n   * @type string[]\n   */\n  ignoreTags: [],\n  /**\n   * @param {IntermediateRepresentation} ir\n   * @returns {any}\n   */\n  defaultRender(ir) {\n    return ir;\n  },\n  /**\n   * Returns true or false based on whether a token should be displayed as a\n   * link based on the user options.\n   * @param {MultiToken} token\n   * @returns {boolean}\n   */\n  check(token) {\n    return this.get('validate', token.toString(), token);\n  },\n  // Private methods\n\n  /**\n   * Resolve an option's value based on the value of the option and the given\n   * params. If operator and token are specified and the target option is\n   * callable, automatically calls the function with the given argument.\n   * @template {keyof Opts} K\n   * @param {K} key Name of option to use\n   * @param {string} [operator] will be passed to the target option if it's a\n   * function. If not specified, RAW function value gets returned\n   * @param {MultiToken} [token] The token from linkify.tokenize\n   * @returns {Opts[K] | any}\n   */\n  get(key, operator, token) {\n    const isCallable = operator != null;\n    let option = this.o[key];\n    if (!option) {\n      return option;\n    }\n    if (typeof option === 'object') {\n      option = token.t in option ? option[token.t] : defaults[key];\n      if (typeof option === 'function' && isCallable) {\n        option = option(operator, token);\n      }\n    } else if (typeof option === 'function' && isCallable) {\n      option = option(operator, token.t, token);\n    }\n    return option;\n  },\n  /**\n   * @template {keyof Opts} L\n   * @param {L} key Name of options object to use\n   * @param {string} [operator]\n   * @param {MultiToken} [token]\n   * @returns {Opts[L] | any}\n   */\n  getObj(key, operator, token) {\n    let obj = this.o[key];\n    if (typeof obj === 'function' && operator != null) {\n      obj = obj(operator, token.t, token);\n    }\n    return obj;\n  },\n  /**\n   * Convert the given token to a rendered element that may be added to the\n   * calling-interface's DOM\n   * @param {MultiToken} token Token to render to an HTML element\n   * @returns {any} Render result; e.g., HTML string, DOM element, React\n   *   Component, etc.\n   */\n  render(token) {\n    const ir = token.render(this); // intermediate representation\n    const renderFn = this.get('render', null, token) || this.defaultRender;\n    return renderFn(ir, token.t, token);\n  }\n};\nfunction noop(val) {\n  return val;\n}\n\nvar options = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tOptions: Options,\n\tassign: assign,\n\tdefaults: defaults\n});\n\n/******************************************************************************\n\tMulti-Tokens\n\tTokens composed of arrays of TextTokens\n******************************************************************************/\n\n/**\n * @param {string} value\n * @param {Token[]} tokens\n */\nfunction MultiToken(value, tokens) {\n  this.t = 'token';\n  this.v = value;\n  this.tk = tokens;\n}\n\n/**\n * Abstract class used for manufacturing tokens of text tokens. That is rather\n * than the value for a token being a small string of text, it's value an array\n * of text tokens.\n *\n * Used for grouping together URLs, emails, hashtags, and other potential\n * creations.\n * @class MultiToken\n * @property {string} t\n * @property {string} v\n * @property {Token[]} tk\n * @abstract\n */\nMultiToken.prototype = {\n  isLink: false,\n  /**\n   * Return the string this token represents.\n   * @return {string}\n   */\n  toString() {\n    return this.v;\n  },\n  /**\n   * What should the value for this token be in the `href` HTML attribute?\n   * Returns the `.toString` value by default.\n   * @param {string} [scheme]\n   * @return {string}\n   */\n  toHref(scheme) {\n    return this.toString();\n  },\n  /**\n   * @param {Options} options Formatting options\n   * @returns {string}\n   */\n  toFormattedString(options) {\n    const val = this.toString();\n    const truncate = options.get('truncate', val, this);\n    const formatted = options.get('format', val, this);\n    return truncate && formatted.length > truncate ? formatted.substring(0, truncate) + '…' : formatted;\n  },\n  /**\n   *\n   * @param {Options} options\n   * @returns {string}\n   */\n  toFormattedHref(options) {\n    return options.get('formatHref', this.toHref(options.get('defaultProtocol')), this);\n  },\n  /**\n   * The start index of this token in the original input string\n   * @returns {number}\n   */\n  startIndex() {\n    return this.tk[0].s;\n  },\n  /**\n   * The end index of this token in the original input string (up to this\n   * index but not including it)\n   * @returns {number}\n   */\n  endIndex() {\n    return this.tk[this.tk.length - 1].e;\n  },\n  /**\n  \tReturns an object  of relevant values for this token, which includes keys\n  \t* type - Kind of token ('url', 'email', etc.)\n  \t* value - Original text\n  \t* href - The value that should be added to the anchor tag's href\n  \t\tattribute\n  \t\t@method toObject\n  \t@param {string} [protocol] `'http'` by default\n  */\n  toObject(protocol = defaults.defaultProtocol) {\n    return {\n      type: this.t,\n      value: this.toString(),\n      isLink: this.isLink,\n      href: this.toHref(protocol),\n      start: this.startIndex(),\n      end: this.endIndex()\n    };\n  },\n  /**\n   *\n   * @param {Options} options Formatting option\n   */\n  toFormattedObject(options) {\n    return {\n      type: this.t,\n      value: this.toFormattedString(options),\n      isLink: this.isLink,\n      href: this.toFormattedHref(options),\n      start: this.startIndex(),\n      end: this.endIndex()\n    };\n  },\n  /**\n   * Whether this token should be rendered as a link according to the given options\n   * @param {Options} options\n   * @returns {boolean}\n   */\n  validate(options) {\n    return options.get('validate', this.toString(), this);\n  },\n  /**\n   * Return an object that represents how this link should be rendered.\n   * @param {Options} options Formattinng options\n   */\n  render(options) {\n    const token = this;\n    const href = this.toHref(options.get('defaultProtocol'));\n    const formattedHref = options.get('formatHref', href, this);\n    const tagName = options.get('tagName', href, token);\n    const content = this.toFormattedString(options);\n    const attributes = {};\n    const className = options.get('className', href, token);\n    const target = options.get('target', href, token);\n    const rel = options.get('rel', href, token);\n    const attrs = options.getObj('attributes', href, token);\n    const eventListeners = options.getObj('events', href, token);\n    attributes.href = formattedHref;\n    if (className) {\n      attributes.class = className;\n    }\n    if (target) {\n      attributes.target = target;\n    }\n    if (rel) {\n      attributes.rel = rel;\n    }\n    if (attrs) {\n      assign(attributes, attrs);\n    }\n    return {\n      tagName,\n      attributes,\n      content,\n      eventListeners\n    };\n  }\n};\n\n/**\n * Create a new token that can be emitted by the parser state machine\n * @param {string} type readable type of the token\n * @param {object} props properties to assign or override, including isLink = true or false\n * @returns {new (value: string, tokens: Token[]) => MultiToken} new token class\n */\nfunction createTokenClass(type, props) {\n  class Token extends MultiToken {\n    constructor(value, tokens) {\n      super(value, tokens);\n      this.t = type;\n    }\n  }\n  for (const p in props) {\n    Token.prototype[p] = props[p];\n  }\n  Token.t = type;\n  return Token;\n}\n\n/**\n\tRepresents a list of tokens making up a valid email address\n*/\nconst Email = createTokenClass('email', {\n  isLink: true,\n  toHref() {\n    return 'mailto:' + this.toString();\n  }\n});\n\n/**\n\tRepresents some plain text\n*/\nconst Text = createTokenClass('text');\n\n/**\n\tMulti-linebreak token - represents a line break\n\t@class Nl\n*/\nconst Nl = createTokenClass('nl');\n\n/**\n\tRepresents a list of text tokens making up a valid URL\n\t@class Url\n*/\nconst Url = createTokenClass('url', {\n  isLink: true,\n  /**\n  \tLowercases relevant parts of the domain and adds the protocol if\n  \trequired. Note that this will not escape unsafe HTML characters in the\n  \tURL.\n  \t\t@param {string} [scheme] default scheme (e.g., 'https')\n  \t@return {string} the full href\n  */\n  toHref(scheme = defaults.defaultProtocol) {\n    // Check if already has a prefix scheme\n    return this.hasProtocol() ? this.v : `${scheme}://${this.v}`;\n  },\n  /**\n   * Check whether this URL token has a protocol\n   * @return {boolean}\n   */\n  hasProtocol() {\n    const tokens = this.tk;\n    return tokens.length >= 2 && tokens[0].t !== LOCALHOST && tokens[1].t === COLON;\n  }\n});\n\nvar multi = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tBase: MultiToken,\n\tEmail: Email,\n\tMultiToken: MultiToken,\n\tNl: Nl,\n\tText: Text,\n\tUrl: Url,\n\tcreateTokenClass: createTokenClass\n});\n\n/**\n\tNot exactly parser, more like the second-stage scanner (although we can\n\ttheoretically hotswap the code here with a real parser in the future... but\n\tfor a little URL-finding utility abstract syntax trees may be a little\n\toverkill).\n\n\tURL format: http://en.wikipedia.org/wiki/URI_scheme\n\tEmail format: http://en.wikipedia.org/wiki/EmailAddress (links to RFC in\n\treference)\n\n\t@module linkify\n\t@submodule parser\n\t@main run\n*/\n\nconst makeState = arg => new State(arg);\n\n/**\n * Generate the parser multi token-based state machine\n * @param {{ groups: Collections<string> }} tokens\n */\nfunction init$1({\n  groups\n}) {\n  // Types of characters the URL can definitely end in\n  const qsAccepting = groups.domain.concat([AMPERSAND, ASTERISK, AT, BACKSLASH, BACKTICK, CARET, DOLLAR, EQUALS, HYPHEN, NUM, PERCENT, PIPE, PLUS, POUND, SLASH, SYM, TILDE, UNDERSCORE]);\n\n  // Types of tokens that can follow a URL and be part of the query string\n  // but cannot be the very last characters\n  // Characters that cannot appear in the URL at all should be excluded\n  const qsNonAccepting = [APOSTROPHE, COLON, COMMA, DOT, EXCLAMATION, PERCENT, QUERY, QUOTE, SEMI, OPENANGLEBRACKET, CLOSEANGLEBRACKET, OPENBRACE, CLOSEBRACE, CLOSEBRACKET, OPENBRACKET, OPENPAREN, CLOSEPAREN, FULLWIDTHLEFTPAREN, FULLWIDTHRIGHTPAREN, LEFTCORNERBRACKET, RIGHTCORNERBRACKET, LEFTWHITECORNERBRACKET, RIGHTWHITECORNERBRACKET, FULLWIDTHLESSTHAN, FULLWIDTHGREATERTHAN];\n\n  // For addresses without the mailto prefix\n  // Tokens allowed in the localpart of the email\n  const localpartAccepting = [AMPERSAND, APOSTROPHE, ASTERISK, BACKSLASH, BACKTICK, CARET, DOLLAR, EQUALS, HYPHEN, OPENBRACE, CLOSEBRACE, PERCENT, PIPE, PLUS, POUND, QUERY, SLASH, SYM, TILDE, UNDERSCORE];\n\n  // The universal starting state.\n  /**\n   * @type State<Token>\n   */\n  const Start = makeState();\n  const Localpart = tt(Start, TILDE); // Local part of the email address\n  ta(Localpart, localpartAccepting, Localpart);\n  ta(Localpart, groups.domain, Localpart);\n  const Domain = makeState(),\n    Scheme = makeState(),\n    SlashScheme = makeState();\n  ta(Start, groups.domain, Domain); // parsed string ends with a potential domain name (A)\n  ta(Start, groups.scheme, Scheme); // e.g., 'mailto'\n  ta(Start, groups.slashscheme, SlashScheme); // e.g., 'http'\n\n  ta(Domain, localpartAccepting, Localpart);\n  ta(Domain, groups.domain, Domain);\n  const LocalpartAt = tt(Domain, AT); // Local part of the email address plus @\n\n  tt(Localpart, AT, LocalpartAt); // close to an email address now\n\n  // Local part of an email address can be e.g. 'http' or 'mailto'\n  tt(Scheme, AT, LocalpartAt);\n  tt(SlashScheme, AT, LocalpartAt);\n  const LocalpartDot = tt(Localpart, DOT); // Local part of the email address plus '.' (localpart cannot end in .)\n  ta(LocalpartDot, localpartAccepting, Localpart);\n  ta(LocalpartDot, groups.domain, Localpart);\n  const EmailDomain = makeState();\n  ta(LocalpartAt, groups.domain, EmailDomain); // parsed string starts with local email info + @ with a potential domain name\n  ta(EmailDomain, groups.domain, EmailDomain);\n  const EmailDomainDot = tt(EmailDomain, DOT); // domain followed by DOT\n  ta(EmailDomainDot, groups.domain, EmailDomain);\n  const Email$1 = makeState(Email); // Possible email address (could have more tlds)\n  ta(EmailDomainDot, groups.tld, Email$1);\n  ta(EmailDomainDot, groups.utld, Email$1);\n  tt(LocalpartAt, LOCALHOST, Email$1);\n\n  // Hyphen can jump back to a domain name\n  const EmailDomainHyphen = tt(EmailDomain, HYPHEN); // parsed string starts with local email info + @ with a potential domain name\n  tt(EmailDomainHyphen, HYPHEN, EmailDomainHyphen);\n  ta(EmailDomainHyphen, groups.domain, EmailDomain);\n  ta(Email$1, groups.domain, EmailDomain);\n  tt(Email$1, DOT, EmailDomainDot);\n  tt(Email$1, HYPHEN, EmailDomainHyphen);\n\n  // Final possible email states\n  const EmailColon = tt(Email$1, COLON); // URL followed by colon (potential port number here)\n  /*const EmailColonPort = */\n  ta(EmailColon, groups.numeric, Email); // URL followed by colon and port number\n\n  // Account for dots and hyphens. Hyphens are usually parts of domain names\n  // (but not TLDs)\n  const DomainHyphen = tt(Domain, HYPHEN); // domain followed by hyphen\n  const DomainDot = tt(Domain, DOT); // domain followed by DOT\n  tt(DomainHyphen, HYPHEN, DomainHyphen);\n  ta(DomainHyphen, groups.domain, Domain);\n  ta(DomainDot, localpartAccepting, Localpart);\n  ta(DomainDot, groups.domain, Domain);\n  const DomainDotTld = makeState(Url); // Simplest possible URL with no query string\n  ta(DomainDot, groups.tld, DomainDotTld);\n  ta(DomainDot, groups.utld, DomainDotTld);\n  ta(DomainDotTld, groups.domain, Domain);\n  ta(DomainDotTld, localpartAccepting, Localpart);\n  tt(DomainDotTld, DOT, DomainDot);\n  tt(DomainDotTld, HYPHEN, DomainHyphen);\n  tt(DomainDotTld, AT, LocalpartAt);\n  const DomainDotTldColon = tt(DomainDotTld, COLON); // URL followed by colon (potential port number here)\n  const DomainDotTldColonPort = makeState(Url); // TLD followed by a port number\n  ta(DomainDotTldColon, groups.numeric, DomainDotTldColonPort);\n\n  // Long URL with optional port and maybe query string\n  const Url$1 = makeState(Url);\n\n  // URL with extra symbols at the end, followed by an opening bracket\n  const UrlNonaccept = makeState(); // URL followed by some symbols (will not be part of the final URL)\n\n  // Query strings\n  ta(Url$1, qsAccepting, Url$1);\n  ta(Url$1, qsNonAccepting, UrlNonaccept);\n  ta(UrlNonaccept, qsAccepting, Url$1);\n  ta(UrlNonaccept, qsNonAccepting, UrlNonaccept);\n\n  // Become real URLs after `SLASH` or `COLON NUM SLASH`\n  // Here works with or without scheme:// prefix\n  tt(DomainDotTld, SLASH, Url$1);\n  tt(DomainDotTldColonPort, SLASH, Url$1);\n\n  // Note that domains that begin with schemes are treated slighly differently\n  const SchemeColon = tt(Scheme, COLON); // e.g., 'mailto:'\n  const SlashSchemeColon = tt(SlashScheme, COLON); // e.g., 'http:'\n  const SlashSchemeColonSlash = tt(SlashSchemeColon, SLASH); // e.g., 'http:/'\n\n  const UriPrefix = tt(SlashSchemeColonSlash, SLASH); // e.g., 'http://'\n\n  // Scheme states can transition to domain states\n  ta(Scheme, groups.domain, Domain);\n  tt(Scheme, DOT, DomainDot);\n  tt(Scheme, HYPHEN, DomainHyphen);\n  ta(SlashScheme, groups.domain, Domain);\n  tt(SlashScheme, DOT, DomainDot);\n  tt(SlashScheme, HYPHEN, DomainHyphen);\n\n  // Force URL with scheme prefix followed by anything sane\n  ta(SchemeColon, groups.domain, Url$1);\n  tt(SchemeColon, SLASH, Url$1);\n  tt(SchemeColon, QUERY, Url$1);\n  ta(UriPrefix, groups.domain, Url$1);\n  ta(UriPrefix, qsAccepting, Url$1);\n  tt(UriPrefix, SLASH, Url$1);\n  const bracketPairs = [[OPENBRACE, CLOSEBRACE],\n  // {}\n  [OPENBRACKET, CLOSEBRACKET],\n  // []\n  [OPENPAREN, CLOSEPAREN],\n  // ()\n  [OPENANGLEBRACKET, CLOSEANGLEBRACKET],\n  // <>\n  [FULLWIDTHLEFTPAREN, FULLWIDTHRIGHTPAREN],\n  // （）\n  [LEFTCORNERBRACKET, RIGHTCORNERBRACKET],\n  // 「」\n  [LEFTWHITECORNERBRACKET, RIGHTWHITECORNERBRACKET],\n  // 『』\n  [FULLWIDTHLESSTHAN, FULLWIDTHGREATERTHAN] // ＜＞\n  ];\n  for (let i = 0; i < bracketPairs.length; i++) {\n    const [OPEN, CLOSE] = bracketPairs[i];\n    const UrlOpen = tt(Url$1, OPEN); // URL followed by open bracket\n\n    // Continue not accepting for open brackets\n    tt(UrlNonaccept, OPEN, UrlOpen);\n\n    // Closing bracket component. This character WILL be included in the URL\n    tt(UrlOpen, CLOSE, Url$1);\n\n    // URL that beings with an opening bracket, followed by a symbols.\n    // Note that the final state can still be `UrlOpen` (if the URL has a\n    // single opening bracket for some reason).\n    const UrlOpenQ = makeState(Url);\n    ta(UrlOpen, qsAccepting, UrlOpenQ);\n    const UrlOpenSyms = makeState(); // UrlOpen followed by some symbols it cannot end it\n    ta(UrlOpen, qsNonAccepting);\n\n    // URL that begins with an opening bracket, followed by some symbols\n    ta(UrlOpenQ, qsAccepting, UrlOpenQ);\n    ta(UrlOpenQ, qsNonAccepting, UrlOpenSyms);\n    ta(UrlOpenSyms, qsAccepting, UrlOpenQ);\n    ta(UrlOpenSyms, qsNonAccepting, UrlOpenSyms);\n\n    // Close brace/bracket to become regular URL\n    tt(UrlOpenQ, CLOSE, Url$1);\n    tt(UrlOpenSyms, CLOSE, Url$1);\n  }\n  tt(Start, LOCALHOST, DomainDotTld); // localhost is a valid URL state\n  tt(Start, NL, Nl); // single new line\n\n  return {\n    start: Start,\n    tokens: tk\n  };\n}\n\n/**\n * Run the parser state machine on a list of scanned string-based tokens to\n * create a list of multi tokens, each of which represents a URL, email address,\n * plain text, etc.\n *\n * @param {State<MultiToken>} start parser start state\n * @param {string} input the original input used to generate the given tokens\n * @param {Token[]} tokens list of scanned tokens\n * @returns {MultiToken[]}\n */\nfunction run(start, input, tokens) {\n  let len = tokens.length;\n  let cursor = 0;\n  let multis = [];\n  let textTokens = [];\n  while (cursor < len) {\n    let state = start;\n    let secondState = null;\n    let nextState = null;\n    let multiLength = 0;\n    let latestAccepting = null;\n    let sinceAccepts = -1;\n    while (cursor < len && !(secondState = state.go(tokens[cursor].t))) {\n      // Starting tokens with nowhere to jump to.\n      // Consider these to be just plain text\n      textTokens.push(tokens[cursor++]);\n    }\n    while (cursor < len && (nextState = secondState || state.go(tokens[cursor].t))) {\n      // Get the next state\n      secondState = null;\n      state = nextState;\n\n      // Keep track of the latest accepting state\n      if (state.accepts()) {\n        sinceAccepts = 0;\n        latestAccepting = state;\n      } else if (sinceAccepts >= 0) {\n        sinceAccepts++;\n      }\n      cursor++;\n      multiLength++;\n    }\n    if (sinceAccepts < 0) {\n      // No accepting state was found, part of a regular text token add\n      // the first text token to the text tokens array and try again from\n      // the next\n      cursor -= multiLength;\n      if (cursor < len) {\n        textTokens.push(tokens[cursor]);\n        cursor++;\n      }\n    } else {\n      // Accepting state!\n      // First close off the textTokens (if available)\n      if (textTokens.length > 0) {\n        multis.push(initMultiToken(Text, input, textTokens));\n        textTokens = [];\n      }\n\n      // Roll back to the latest accepting state\n      cursor -= sinceAccepts;\n      multiLength -= sinceAccepts;\n\n      // Create a new multitoken\n      const Multi = latestAccepting.t;\n      const subtokens = tokens.slice(cursor - multiLength, cursor);\n      multis.push(initMultiToken(Multi, input, subtokens));\n    }\n  }\n\n  // Finally close off the textTokens (if available)\n  if (textTokens.length > 0) {\n    multis.push(initMultiToken(Text, input, textTokens));\n  }\n  return multis;\n}\n\n/**\n * Utility function for instantiating a new multitoken with all the relevant\n * fields during parsing.\n * @param {new (value: string, tokens: Token[]) => MultiToken} Multi class to instantiate\n * @param {string} input original input string\n * @param {Token[]} tokens consecutive tokens scanned from input string\n * @returns {MultiToken}\n */\nfunction initMultiToken(Multi, input, tokens) {\n  const startIdx = tokens[0].s;\n  const endIdx = tokens[tokens.length - 1].e;\n  const value = input.slice(startIdx, endIdx);\n  return new Multi(value, tokens);\n}\n\nconst warn = typeof console !== 'undefined' && console && console.warn || (() => {});\nconst warnAdvice = 'until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.';\n\n// Side-effect initialization state\nconst INIT = {\n  scanner: null,\n  parser: null,\n  tokenQueue: [],\n  pluginQueue: [],\n  customSchemes: [],\n  initialized: false\n};\n\n/**\n * @typedef {{\n * \tstart: State<string>,\n * \ttokens: { groups: Collections<string> } & typeof tk\n * }} ScannerInit\n */\n\n/**\n * @typedef {{\n * \tstart: State<MultiToken>,\n * \ttokens: typeof multi\n * }} ParserInit\n */\n\n/**\n * @typedef {(arg: { scanner: ScannerInit }) => void} TokenPlugin\n */\n\n/**\n * @typedef {(arg: { scanner: ScannerInit, parser: ParserInit }) => void} Plugin\n */\n\n/**\n * De-register all plugins and reset the internal state-machine. Used for\n * testing; not required in practice.\n * @private\n */\nfunction reset() {\n  State.groups = {};\n  INIT.scanner = null;\n  INIT.parser = null;\n  INIT.tokenQueue = [];\n  INIT.pluginQueue = [];\n  INIT.customSchemes = [];\n  INIT.initialized = false;\n  return INIT;\n}\n\n/**\n * Register a token plugin to allow the scanner to recognize additional token\n * types before the parser state machine is constructed from the results.\n * @param {string} name of plugin to register\n * @param {TokenPlugin} plugin function that accepts the scanner state machine\n * and available scanner tokens and collections and extends the state machine to\n * recognize additional tokens or groups.\n */\nfunction registerTokenPlugin(name, plugin) {\n  if (typeof plugin !== 'function') {\n    throw new Error(`linkifyjs: Invalid token plugin ${plugin} (expects function)`);\n  }\n  for (let i = 0; i < INIT.tokenQueue.length; i++) {\n    if (name === INIT.tokenQueue[i][0]) {\n      warn(`linkifyjs: token plugin \"${name}\" already registered - will be overwritten`);\n      INIT.tokenQueue[i] = [name, plugin];\n      return;\n    }\n  }\n  INIT.tokenQueue.push([name, plugin]);\n  if (INIT.initialized) {\n    warn(`linkifyjs: already initialized - will not register token plugin \"${name}\" ${warnAdvice}`);\n  }\n}\n\n/**\n * Register a linkify plugin\n * @param {string} name of plugin to register\n * @param {Plugin} plugin function that accepts the parser state machine and\n * extends the parser to recognize additional link types\n */\nfunction registerPlugin(name, plugin) {\n  if (typeof plugin !== 'function') {\n    throw new Error(`linkifyjs: Invalid plugin ${plugin} (expects function)`);\n  }\n  for (let i = 0; i < INIT.pluginQueue.length; i++) {\n    if (name === INIT.pluginQueue[i][0]) {\n      warn(`linkifyjs: plugin \"${name}\" already registered - will be overwritten`);\n      INIT.pluginQueue[i] = [name, plugin];\n      return;\n    }\n  }\n  INIT.pluginQueue.push([name, plugin]);\n  if (INIT.initialized) {\n    warn(`linkifyjs: already initialized - will not register plugin \"${name}\" ${warnAdvice}`);\n  }\n}\n\n/**\n * Detect URLs with the following additional protocol. Anything with format\n * \"protocol://...\" will be considered a link. If `optionalSlashSlash` is set to\n * `true`, anything with format \"protocol:...\" will be considered a link.\n * @param {string} scheme\n * @param {boolean} [optionalSlashSlash]\n */\nfunction registerCustomProtocol(scheme, optionalSlashSlash = false) {\n  if (INIT.initialized) {\n    warn(`linkifyjs: already initialized - will not register custom scheme \"${scheme}\" ${warnAdvice}`);\n  }\n  if (!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(scheme)) {\n    throw new Error(`linkifyjs: incorrect scheme format.\n1. Must only contain digits, lowercase ASCII letters or \"-\"\n2. Cannot start or end with \"-\"\n3. \"-\" cannot repeat`);\n  }\n  INIT.customSchemes.push([scheme, optionalSlashSlash]);\n}\n\n/**\n * Initialize the linkify state machine. Called automatically the first time\n * linkify is called on a string, but may be called manually as well.\n */\nfunction init() {\n  // Initialize scanner state machine and plugins\n  INIT.scanner = init$2(INIT.customSchemes);\n  for (let i = 0; i < INIT.tokenQueue.length; i++) {\n    INIT.tokenQueue[i][1]({\n      scanner: INIT.scanner\n    });\n  }\n\n  // Initialize parser state machine and plugins\n  INIT.parser = init$1(INIT.scanner.tokens);\n  for (let i = 0; i < INIT.pluginQueue.length; i++) {\n    INIT.pluginQueue[i][1]({\n      scanner: INIT.scanner,\n      parser: INIT.parser\n    });\n  }\n  INIT.initialized = true;\n  return INIT;\n}\n\n/**\n * Parse a string into tokens that represent linkable and non-linkable sub-components\n * @param {string} str\n * @return {MultiToken[]} tokens\n */\nfunction tokenize(str) {\n  if (!INIT.initialized) {\n    init();\n  }\n  return run(INIT.parser.start, str, run$1(INIT.scanner.start, str));\n}\ntokenize.scan = run$1; // for testing\n\n/**\n * Find a list of linkable items in the given string.\n * @param {string} str string to find links in\n * @param {string | Opts} [type] either formatting options or specific type of\n * links to find, e.g., 'url' or 'email'\n * @param {Opts} [opts] formatting options for final output. Cannot be specified\n * if opts already provided in `type` argument\n */\nfunction find(str, type = null, opts = null) {\n  if (type && typeof type === 'object') {\n    if (opts) {\n      throw Error(`linkifyjs: Invalid link type ${type}; must be a string`);\n    }\n    opts = type;\n    type = null;\n  }\n  const options = new Options(opts);\n  const tokens = tokenize(str);\n  const filtered = [];\n  for (let i = 0; i < tokens.length; i++) {\n    const token = tokens[i];\n    if (token.isLink && (!type || token.t === type) && options.check(token)) {\n      filtered.push(token.toFormattedObject(options));\n    }\n  }\n  return filtered;\n}\n\n/**\n * Is the given string valid linkable text of some sort. Note that this does not\n * trim the text for you.\n *\n * Optionally pass in a second `type` param, which is the type of link to test\n * for.\n *\n * For example,\n *\n *     linkify.test(str, 'email');\n *\n * Returns `true` if str is a valid email.\n * @param {string} str string to test for links\n * @param {string} [type] optional specific link type to look for\n * @returns boolean true/false\n */\nfunction test(str, type = null) {\n  const tokens = tokenize(str);\n  return tokens.length === 1 && tokens[0].isLink && (!type || tokens[0].t === type);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/linkifyjs/dist/linkify.mjs\n");

/***/ })

};
;