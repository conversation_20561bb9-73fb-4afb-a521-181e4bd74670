/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sanitize-html";
exports.ids = ["vendor-chunks/sanitize-html"];
exports.modules = {

/***/ "(ssr)/./node_modules/sanitize-html/index.js":
/*!*********************************************!*\
  !*** ./node_modules/sanitize-html/index.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const htmlparser = __webpack_require__(/*! htmlparser2 */ \"(ssr)/./node_modules/htmlparser2/lib/index.js\");\nconst escapeStringRegexp = __webpack_require__(/*! escape-string-regexp */ \"(ssr)/./node_modules/escape-string-regexp/index.js\");\nconst { isPlainObject } = __webpack_require__(/*! is-plain-object */ \"(ssr)/./node_modules/is-plain-object/dist/is-plain-object.js\");\nconst deepmerge = __webpack_require__(/*! deepmerge */ \"(ssr)/./node_modules/deepmerge/dist/cjs.js\");\nconst parseSrcset = __webpack_require__(/*! parse-srcset */ \"(ssr)/./node_modules/parse-srcset/src/parse-srcset.js\");\nconst { parse: postcssParse } = __webpack_require__(/*! postcss */ \"postcss\");\n// Tags that can conceivably represent stand-alone media.\nconst mediaTags = [\n  'img', 'audio', 'video', 'picture', 'svg',\n  'object', 'map', 'iframe', 'embed'\n];\n// Tags that are inherently vulnerable to being used in XSS attacks.\nconst vulnerableTags = [ 'script', 'style' ];\n\nfunction each(obj, cb) {\n  if (obj) {\n    Object.keys(obj).forEach(function (key) {\n      cb(obj[key], key);\n    });\n  }\n}\n\n// Avoid false positives with .__proto__, .hasOwnProperty, etc.\nfunction has(obj, key) {\n  return ({}).hasOwnProperty.call(obj, key);\n}\n\n// Returns those elements of `a` for which `cb(a)` returns truthy\nfunction filter(a, cb) {\n  const n = [];\n  each(a, function(v) {\n    if (cb(v)) {\n      n.push(v);\n    }\n  });\n  return n;\n}\n\nfunction isEmptyObject(obj) {\n  for (const key in obj) {\n    if (has(obj, key)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction stringifySrcset(parsedSrcset) {\n  return parsedSrcset.map(function(part) {\n    if (!part.url) {\n      throw new Error('URL missing');\n    }\n\n    return (\n      part.url +\n      (part.w ? ` ${part.w}w` : '') +\n      (part.h ? ` ${part.h}h` : '') +\n      (part.d ? ` ${part.d}x` : '')\n    );\n  }).join(', ');\n}\n\nmodule.exports = sanitizeHtml;\n\n// A valid attribute name.\n// We use a tolerant definition based on the set of strings defined by\n// html.spec.whatwg.org/multipage/parsing.html#before-attribute-name-state\n// and html.spec.whatwg.org/multipage/parsing.html#attribute-name-state .\n// The characters accepted are ones which can be appended to the attribute\n// name buffer without triggering a parse error:\n//   * unexpected-equals-sign-before-attribute-name\n//   * unexpected-null-character\n//   * unexpected-character-in-attribute-name\n// We exclude the empty string because it's impossible to get to the after\n// attribute name state with an empty attribute name buffer.\nconst VALID_HTML_ATTRIBUTE_NAME = /^[^\\0\\t\\n\\f\\r /<=>]+$/;\n\n// Ignore the _recursing flag; it's there for recursive\n// invocation as a guard against this exploit:\n// https://github.com/fb55/htmlparser2/issues/105\n\nfunction sanitizeHtml(html, options, _recursing) {\n  if (html == null) {\n    return '';\n  }\n  if (typeof html === 'number') {\n    html = html.toString();\n  }\n\n  let result = '';\n  // Used for hot swapping the result variable with an empty string in order to \"capture\" the text written to it.\n  let tempResult = '';\n\n  function Frame(tag, attribs) {\n    const that = this;\n    this.tag = tag;\n    this.attribs = attribs || {};\n    this.tagPosition = result.length;\n    this.text = ''; // Node inner text\n    this.openingTagLength = 0;\n    this.mediaChildren = [];\n\n    this.updateParentNodeText = function() {\n      if (stack.length) {\n        const parentFrame = stack[stack.length - 1];\n        parentFrame.text += that.text;\n      }\n    };\n\n    this.updateParentNodeMediaChildren = function() {\n      if (stack.length && mediaTags.includes(this.tag)) {\n        const parentFrame = stack[stack.length - 1];\n        parentFrame.mediaChildren.push(this.tag);\n      }\n    };\n  }\n\n  options = Object.assign({}, sanitizeHtml.defaults, options);\n  options.parser = Object.assign({}, htmlParserDefaults, options.parser);\n\n  const tagAllowed = function (name) {\n    return options.allowedTags === false || (options.allowedTags || []).indexOf(name) > -1;\n  };\n\n  // vulnerableTags\n  vulnerableTags.forEach(function (tag) {\n    if (tagAllowed(tag) && !options.allowVulnerableTags) {\n      console.warn(`\\n\\n⚠️ Your \\`allowedTags\\` option includes, \\`${tag}\\`, which is inherently\\nvulnerable to XSS attacks. Please remove it from \\`allowedTags\\`.\\nOr, to disable this warning, add the \\`allowVulnerableTags\\` option\\nand ensure you are accounting for this risk.\\n\\n`);\n    }\n  });\n\n  // Tags that contain something other than HTML, or where discarding\n  // the text when the tag is disallowed makes sense for other reasons.\n  // If we are not allowing these tags, we should drop their content too.\n  // For other tags you would drop the tag but keep its content.\n  const nonTextTagsArray = options.nonTextTags || [\n    'script',\n    'style',\n    'textarea',\n    'option'\n  ];\n  let allowedAttributesMap;\n  let allowedAttributesGlobMap;\n  if (options.allowedAttributes) {\n    allowedAttributesMap = {};\n    allowedAttributesGlobMap = {};\n    each(options.allowedAttributes, function(attributes, tag) {\n      allowedAttributesMap[tag] = [];\n      const globRegex = [];\n      attributes.forEach(function(obj) {\n        if (typeof obj === 'string' && obj.indexOf('*') >= 0) {\n          globRegex.push(escapeStringRegexp(obj).replace(/\\\\\\*/g, '.*'));\n        } else {\n          allowedAttributesMap[tag].push(obj);\n        }\n      });\n      if (globRegex.length) {\n        allowedAttributesGlobMap[tag] = new RegExp('^(' + globRegex.join('|') + ')$');\n      }\n    });\n  }\n  const allowedClassesMap = {};\n  const allowedClassesGlobMap = {};\n  const allowedClassesRegexMap = {};\n  each(options.allowedClasses, function(classes, tag) {\n    // Implicitly allows the class attribute\n    if (allowedAttributesMap) {\n      if (!has(allowedAttributesMap, tag)) {\n        allowedAttributesMap[tag] = [];\n      }\n      allowedAttributesMap[tag].push('class');\n    }\n\n    allowedClassesMap[tag] = classes;\n\n    if (Array.isArray(classes)) {\n      const globRegex = [];\n      allowedClassesMap[tag] = [];\n      allowedClassesRegexMap[tag] = [];\n      classes.forEach(function(obj) {\n        if (typeof obj === 'string' && obj.indexOf('*') >= 0) {\n          globRegex.push(escapeStringRegexp(obj).replace(/\\\\\\*/g, '.*'));\n        } else if (obj instanceof RegExp) {\n          allowedClassesRegexMap[tag].push(obj);\n        } else {\n          allowedClassesMap[tag].push(obj);\n        }\n      });\n      if (globRegex.length) {\n        allowedClassesGlobMap[tag] = new RegExp('^(' + globRegex.join('|') + ')$');\n      }\n    }\n  });\n\n  const transformTagsMap = {};\n  let transformTagsAll;\n  each(options.transformTags, function(transform, tag) {\n    let transFun;\n    if (typeof transform === 'function') {\n      transFun = transform;\n    } else if (typeof transform === 'string') {\n      transFun = sanitizeHtml.simpleTransform(transform);\n    }\n    if (tag === '*') {\n      transformTagsAll = transFun;\n    } else {\n      transformTagsMap[tag] = transFun;\n    }\n  });\n\n  let depth;\n  let stack;\n  let skipMap;\n  let transformMap;\n  let skipText;\n  let skipTextDepth;\n  let addedText = false;\n\n  initializeState();\n\n  const parser = new htmlparser.Parser({\n    onopentag: function(name, attribs) {\n      if (options.onOpenTag) {\n        options.onOpenTag(name, attribs);\n      }\n\n      // If `enforceHtmlBoundary` is `true` and this has found the opening\n      // `html` tag, reset the state.\n      if (options.enforceHtmlBoundary && name === 'html') {\n        initializeState();\n      }\n\n      if (skipText) {\n        skipTextDepth++;\n        return;\n      }\n      const frame = new Frame(name, attribs);\n      stack.push(frame);\n\n      let skip = false;\n      const hasText = !!frame.text;\n      let transformedTag;\n      if (has(transformTagsMap, name)) {\n        transformedTag = transformTagsMap[name](name, attribs);\n\n        frame.attribs = attribs = transformedTag.attribs;\n\n        if (transformedTag.text !== undefined) {\n          frame.innerText = transformedTag.text;\n        }\n\n        if (name !== transformedTag.tagName) {\n          frame.name = name = transformedTag.tagName;\n          transformMap[depth] = transformedTag.tagName;\n        }\n      }\n      if (transformTagsAll) {\n        transformedTag = transformTagsAll(name, attribs);\n\n        frame.attribs = attribs = transformedTag.attribs;\n        if (name !== transformedTag.tagName) {\n          frame.name = name = transformedTag.tagName;\n          transformMap[depth] = transformedTag.tagName;\n        }\n      }\n\n      if (!tagAllowed(name) || (options.disallowedTagsMode === 'recursiveEscape' && !isEmptyObject(skipMap)) || (options.nestingLimit != null && depth >= options.nestingLimit)) {\n        skip = true;\n        skipMap[depth] = true;\n        if (options.disallowedTagsMode === 'discard' || options.disallowedTagsMode === 'completelyDiscard') {\n          if (nonTextTagsArray.indexOf(name) !== -1) {\n            skipText = true;\n            skipTextDepth = 1;\n          }\n        }\n      }\n      depth++;\n      if (skip) {\n        if (options.disallowedTagsMode === 'discard' || options.disallowedTagsMode === 'completelyDiscard') {\n          // We want the contents but not this tag\n          if (frame.innerText && !hasText) {\n            const escaped = escapeHtml(frame.innerText);\n            if (options.textFilter) {\n              result += options.textFilter(escaped, name);\n            } else {\n              result += escaped;\n            }\n            addedText = true;\n          }\n          return;\n        }\n        tempResult = result;\n        result = '';\n      }\n      result += '<' + name;\n\n      if (name === 'script') {\n        if (options.allowedScriptHostnames || options.allowedScriptDomains) {\n          frame.innerText = '';\n        }\n      }\n\n      const isBeingEscaped = skip && (options.disallowedTagsMode === 'escape' || options.disallowedTagsMode === 'recursiveEscape');\n      const shouldPreserveEscapedAttributes = isBeingEscaped && options.preserveEscapedAttributes;\n\n      if (shouldPreserveEscapedAttributes) {\n        each(attribs, function(value, a) {\n          result += ' ' + a + '=\"' + escapeHtml((value || ''), true) + '\"';\n        });\n      } else if (!allowedAttributesMap || has(allowedAttributesMap, name) || allowedAttributesMap['*']) {\n        each(attribs, function(value, a) {\n          if (!VALID_HTML_ATTRIBUTE_NAME.test(a)) {\n            // This prevents part of an attribute name in the output from being\n            // interpreted as the end of an attribute, or end of a tag.\n            delete frame.attribs[a];\n            return;\n          }\n          // If the value is empty, check if the attribute is in the allowedEmptyAttributes array.\n          // If it is not in the allowedEmptyAttributes array, and it is a known non-boolean attribute, delete it\n          // List taken from https://html.spec.whatwg.org/multipage/indices.html#attributes-3\n          if (value === '' && (!options.allowedEmptyAttributes.includes(a)) &&\n            (options.nonBooleanAttributes.includes(a) || options.nonBooleanAttributes.includes('*'))) {\n            delete frame.attribs[a];\n            return;\n          }\n          // check allowedAttributesMap for the element and attribute and modify the value\n          // as necessary if there are specific values defined.\n          let passedAllowedAttributesMapCheck = false;\n          if (!allowedAttributesMap ||\n            (has(allowedAttributesMap, name) && allowedAttributesMap[name].indexOf(a) !== -1) ||\n            (allowedAttributesMap['*'] && allowedAttributesMap['*'].indexOf(a) !== -1) ||\n            (has(allowedAttributesGlobMap, name) && allowedAttributesGlobMap[name].test(a)) ||\n            (allowedAttributesGlobMap['*'] && allowedAttributesGlobMap['*'].test(a))) {\n            passedAllowedAttributesMapCheck = true;\n          } else if (allowedAttributesMap && allowedAttributesMap[name]) {\n            for (const o of allowedAttributesMap[name]) {\n              if (isPlainObject(o) && o.name && (o.name === a)) {\n                passedAllowedAttributesMapCheck = true;\n                let newValue = '';\n                if (o.multiple === true) {\n                  // verify the values that are allowed\n                  const splitStrArray = value.split(' ');\n                  for (const s of splitStrArray) {\n                    if (o.values.indexOf(s) !== -1) {\n                      if (newValue === '') {\n                        newValue = s;\n                      } else {\n                        newValue += ' ' + s;\n                      }\n                    }\n                  }\n                } else if (o.values.indexOf(value) >= 0) {\n                  // verified an allowed value matches the entire attribute value\n                  newValue = value;\n                }\n                value = newValue;\n              }\n            }\n          }\n          if (passedAllowedAttributesMapCheck) {\n            if (options.allowedSchemesAppliedToAttributes.indexOf(a) !== -1) {\n              if (naughtyHref(name, value)) {\n                delete frame.attribs[a];\n                return;\n              }\n            }\n\n            if (name === 'script' && a === 'src') {\n\n              let allowed = true;\n\n              try {\n                const parsed = parseUrl(value);\n\n                if (options.allowedScriptHostnames || options.allowedScriptDomains) {\n                  const allowedHostname = (options.allowedScriptHostnames || []).find(function (hostname) {\n                    return hostname === parsed.url.hostname;\n                  });\n                  const allowedDomain = (options.allowedScriptDomains || []).find(function(domain) {\n                    return parsed.url.hostname === domain || parsed.url.hostname.endsWith(`.${domain}`);\n                  });\n                  allowed = allowedHostname || allowedDomain;\n                }\n              } catch (e) {\n                allowed = false;\n              }\n\n              if (!allowed) {\n                delete frame.attribs[a];\n                return;\n              }\n            }\n\n            if (name === 'iframe' && a === 'src') {\n              let allowed = true;\n              try {\n                const parsed = parseUrl(value);\n\n                if (parsed.isRelativeUrl) {\n                  // default value of allowIframeRelativeUrls is true\n                  // unless allowedIframeHostnames or allowedIframeDomains specified\n                  allowed = has(options, 'allowIframeRelativeUrls')\n                    ? options.allowIframeRelativeUrls\n                    : (!options.allowedIframeHostnames && !options.allowedIframeDomains);\n                } else if (options.allowedIframeHostnames || options.allowedIframeDomains) {\n                  const allowedHostname = (options.allowedIframeHostnames || []).find(function (hostname) {\n                    return hostname === parsed.url.hostname;\n                  });\n                  const allowedDomain = (options.allowedIframeDomains || []).find(function(domain) {\n                    return parsed.url.hostname === domain || parsed.url.hostname.endsWith(`.${domain}`);\n                  });\n                  allowed = allowedHostname || allowedDomain;\n                }\n              } catch (e) {\n                // Unparseable iframe src\n                allowed = false;\n              }\n              if (!allowed) {\n                delete frame.attribs[a];\n                return;\n              }\n            }\n            if (a === 'srcset') {\n              try {\n                let parsed = parseSrcset(value);\n                parsed.forEach(function(value) {\n                  if (naughtyHref('srcset', value.url)) {\n                    value.evil = true;\n                  }\n                });\n                parsed = filter(parsed, function(v) {\n                  return !v.evil;\n                });\n                if (!parsed.length) {\n                  delete frame.attribs[a];\n                  return;\n                } else {\n                  value = stringifySrcset(filter(parsed, function(v) {\n                    return !v.evil;\n                  }));\n                  frame.attribs[a] = value;\n                }\n              } catch (e) {\n                // Unparseable srcset\n                delete frame.attribs[a];\n                return;\n              }\n            }\n            if (a === 'class') {\n              const allowedSpecificClasses = allowedClassesMap[name];\n              const allowedWildcardClasses = allowedClassesMap['*'];\n              const allowedSpecificClassesGlob = allowedClassesGlobMap[name];\n              const allowedSpecificClassesRegex = allowedClassesRegexMap[name];\n              const allowedWildcardClassesRegex = allowedClassesRegexMap['*'];\n              const allowedWildcardClassesGlob = allowedClassesGlobMap['*'];\n              const allowedClassesGlobs = [\n                allowedSpecificClassesGlob,\n                allowedWildcardClassesGlob\n              ]\n                .concat(allowedSpecificClassesRegex, allowedWildcardClassesRegex)\n                .filter(function (t) {\n                  return t;\n                });\n              if (allowedSpecificClasses && allowedWildcardClasses) {\n                value = filterClasses(value, deepmerge(allowedSpecificClasses, allowedWildcardClasses), allowedClassesGlobs);\n              } else {\n                value = filterClasses(value, allowedSpecificClasses || allowedWildcardClasses, allowedClassesGlobs);\n              }\n              if (!value.length) {\n                delete frame.attribs[a];\n                return;\n              }\n            }\n            if (a === 'style') {\n              if (options.parseStyleAttributes) {\n                try {\n                  const abstractSyntaxTree = postcssParse(name + ' {' + value + '}', { map: false });\n                  const filteredAST = filterCss(abstractSyntaxTree, options.allowedStyles);\n\n                  value = stringifyStyleAttributes(filteredAST);\n\n                  if (value.length === 0) {\n                    delete frame.attribs[a];\n                    return;\n                  }\n                } catch (e) {\n                  if (typeof window !== 'undefined') {\n                    console.warn('Failed to parse \"' + name + ' {' + value + '}' + '\", If you\\'re running this in a browser, we recommend to disable style parsing: options.parseStyleAttributes: false, since this only works in a node environment due to a postcss dependency, More info: https://github.com/apostrophecms/sanitize-html/issues/547');\n                  }\n                  delete frame.attribs[a];\n                  return;\n                }\n              } else if (options.allowedStyles) {\n                throw new Error('allowedStyles option cannot be used together with parseStyleAttributes: false.');\n              }\n            }\n            result += ' ' + a;\n            if (value && value.length) {\n              result += '=\"' + escapeHtml(value, true) + '\"';\n            } else if (options.allowedEmptyAttributes.includes(a)) {\n              result += '=\"\"';\n            }\n          } else {\n            delete frame.attribs[a];\n          }\n        });\n      }\n      if (options.selfClosing.indexOf(name) !== -1) {\n        result += ' />';\n      } else {\n        result += '>';\n        if (frame.innerText && !hasText && !options.textFilter) {\n          result += escapeHtml(frame.innerText);\n          addedText = true;\n        }\n      }\n      if (skip) {\n        result = tempResult + escapeHtml(result);\n        tempResult = '';\n      }\n      frame.openingTagLength = result.length - frame.tagPosition;\n    },\n    ontext: function(text) {\n      if (skipText) {\n        return;\n      }\n      const lastFrame = stack[stack.length - 1];\n      let tag;\n\n      if (lastFrame) {\n        tag = lastFrame.tag;\n        // If inner text was set by transform function then let's use it\n        text = lastFrame.innerText !== undefined ? lastFrame.innerText : text;\n      }\n\n      if (options.disallowedTagsMode === 'completelyDiscard' && !tagAllowed(tag)) {\n        text = '';\n      } else if ((options.disallowedTagsMode === 'discard' || options.disallowedTagsMode === 'completelyDiscard') && ((tag === 'script') || (tag === 'style'))) {\n        // htmlparser2 gives us these as-is. Escaping them ruins the content. Allowing\n        // script tags is, by definition, game over for XSS protection, so if that's\n        // your concern, don't allow them. The same is essentially true for style tags\n        // which have their own collection of XSS vectors.\n        result += text;\n      } else if (!addedText) {\n        const escaped = escapeHtml(text, false);\n        if (options.textFilter) {\n          result += options.textFilter(escaped, tag);\n        } else {\n          result += escaped;\n        }\n      }\n      if (stack.length) {\n        const frame = stack[stack.length - 1];\n        frame.text += text;\n      }\n    },\n    onclosetag: function(name, isImplied) {\n      if (options.onCloseTag) {\n        options.onCloseTag(name, isImplied);\n      }\n\n      if (skipText) {\n        skipTextDepth--;\n        if (!skipTextDepth) {\n          skipText = false;\n        } else {\n          return;\n        }\n      }\n\n      const frame = stack.pop();\n      if (!frame) {\n        // Do not crash on bad markup\n        return;\n      }\n\n      if (frame.tag !== name) {\n        // Another case of bad markup.\n        // Push to stack, so that it will be used in future closing tags.\n        stack.push(frame);\n        return;\n      }\n\n      skipText = options.enforceHtmlBoundary ? name === 'html' : false;\n      depth--;\n      const skip = skipMap[depth];\n      if (skip) {\n        delete skipMap[depth];\n        if (options.disallowedTagsMode === 'discard' || options.disallowedTagsMode === 'completelyDiscard') {\n          frame.updateParentNodeText();\n          return;\n        }\n        tempResult = result;\n        result = '';\n      }\n\n      if (transformMap[depth]) {\n        name = transformMap[depth];\n        delete transformMap[depth];\n      }\n\n      if (options.exclusiveFilter) {\n        const filterResult = options.exclusiveFilter(frame);\n        if (filterResult === 'excludeTag') {\n          if (skip) {\n            // no longer escaping the tag since it's not added at all\n            result = tempResult;\n            tempResult = '';\n          }\n          // remove the opening tag from the result\n          result = result.substring(0, frame.tagPosition) + result.substring(frame.tagPosition + frame.openingTagLength);\n          return;\n        } else if (filterResult) {\n          result = result.substring(0, frame.tagPosition);\n          return;\n        }\n      }\n\n      frame.updateParentNodeMediaChildren();\n      frame.updateParentNodeText();\n\n      if (\n        // Already output />\n        options.selfClosing.indexOf(name) !== -1 ||\n        // Escaped tag, closing tag is implied\n        (isImplied && !tagAllowed(name) && [ 'escape', 'recursiveEscape' ].indexOf(options.disallowedTagsMode) >= 0)\n      ) {\n        if (skip) {\n          result = tempResult;\n          tempResult = '';\n        }\n        return;\n      }\n\n      result += '</' + name + '>';\n      if (skip) {\n        result = tempResult + escapeHtml(result);\n        tempResult = '';\n      }\n      addedText = false;\n    }\n  }, options.parser);\n  parser.write(html);\n  parser.end();\n\n  return result;\n\n  function initializeState() {\n    result = '';\n    depth = 0;\n    stack = [];\n    skipMap = {};\n    transformMap = {};\n    skipText = false;\n    skipTextDepth = 0;\n  }\n\n  function escapeHtml(s, quote) {\n    if (typeof (s) !== 'string') {\n      s = s + '';\n    }\n    if (options.parser.decodeEntities) {\n      s = s.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');\n      if (quote) {\n        s = s.replace(/\"/g, '&quot;');\n      }\n    }\n    // TODO: this is inadequate because it will pass `&0;`. This approach\n    // will not work, each & must be considered with regard to whether it\n    // is followed by a 100% syntactically valid entity or not, and escaped\n    // if it is not. If this bothers you, don't set parser.decodeEntities\n    // to false. (The default is true.)\n    s = s.replace(/&(?![a-zA-Z0-9#]{1,20};)/g, '&amp;') // Match ampersands not part of existing HTML entity\n      .replace(/</g, '&lt;')\n      .replace(/>/g, '&gt;');\n    if (quote) {\n      s = s.replace(/\"/g, '&quot;');\n    }\n    return s;\n  }\n\n  function naughtyHref(name, href) {\n    // Browsers ignore character codes of 32 (space) and below in a surprising\n    // number of situations. Start reading here:\n    // https://www.owasp.org/index.php/XSS_Filter_Evasion_Cheat_Sheet#Embedded_tab\n    // eslint-disable-next-line no-control-regex\n    href = href.replace(/[\\x00-\\x20]+/g, '');\n    // Clobber any comments in URLs, which the browser might\n    // interpret inside an XML data island, allowing\n    // a javascript: URL to be snuck through\n    while (true) {\n      const firstIndex = href.indexOf('<!--');\n      if (firstIndex === -1) {\n        break;\n      }\n      const lastIndex = href.indexOf('-->', firstIndex + 4);\n      if (lastIndex === -1) {\n        break;\n      }\n      href = href.substring(0, firstIndex) + href.substring(lastIndex + 3);\n    }\n    // Case insensitive so we don't get faked out by JAVASCRIPT #1\n    // Allow more characters after the first so we don't get faked\n    // out by certain schemes browsers accept\n    const matches = href.match(/^([a-zA-Z][a-zA-Z0-9.\\-+]*):/);\n    if (!matches) {\n      // Protocol-relative URL starting with any combination of '/' and '\\'\n      if (href.match(/^[/\\\\]{2}/)) {\n        return !options.allowProtocolRelative;\n      }\n\n      // No scheme\n      return false;\n    }\n    const scheme = matches[1].toLowerCase();\n\n    if (has(options.allowedSchemesByTag, name)) {\n      return options.allowedSchemesByTag[name].indexOf(scheme) === -1;\n    }\n\n    return !options.allowedSchemes || options.allowedSchemes.indexOf(scheme) === -1;\n  }\n\n  function parseUrl(value) {\n    value = value.replace(/^(\\w+:)?\\s*[\\\\/]\\s*[\\\\/]/, '$1//');\n    if (value.startsWith('relative:')) {\n      // An attempt to exploit our workaround for base URLs being\n      // mandatory for relative URL validation in the WHATWG\n      // URL parser, reject it\n      throw new Error('relative: exploit attempt');\n    }\n    // naughtyHref is in charge of whether protocol relative URLs\n    // are cool. Here we are concerned just with allowed hostnames and\n    // whether to allow relative URLs.\n    //\n    // Build a placeholder \"base URL\" against which any reasonable\n    // relative URL may be parsed successfully\n    let base = 'relative://relative-site';\n    for (let i = 0; (i < 100); i++) {\n      base += `/${i}`;\n    }\n\n    const parsed = new URL(value, base);\n\n    const isRelativeUrl = parsed && parsed.hostname === 'relative-site' && parsed.protocol === 'relative:';\n    return {\n      isRelativeUrl,\n      url: parsed\n    };\n  }\n  /**\n   * Filters user input css properties by allowlisted regex attributes.\n   * Modifies the abstractSyntaxTree object.\n   *\n   * @param {object} abstractSyntaxTree  - Object representation of CSS attributes.\n   * @property {array[Declaration]} abstractSyntaxTree.nodes[0] - Each object cointains prop and value key, i.e { prop: 'color', value: 'red' }.\n   * @param {object} allowedStyles       - Keys are properties (i.e color), value is list of permitted regex rules (i.e /green/i).\n   * @return {object}                    - The modified tree.\n   */\n  function filterCss(abstractSyntaxTree, allowedStyles) {\n    if (!allowedStyles) {\n      return abstractSyntaxTree;\n    }\n\n    const astRules = abstractSyntaxTree.nodes[0];\n    let selectedRule;\n\n    // Merge global and tag-specific styles into new AST.\n    if (allowedStyles[astRules.selector] && allowedStyles['*']) {\n      selectedRule = deepmerge(\n        allowedStyles[astRules.selector],\n        allowedStyles['*']\n      );\n    } else {\n      selectedRule = allowedStyles[astRules.selector] || allowedStyles['*'];\n    }\n\n    if (selectedRule) {\n      abstractSyntaxTree.nodes[0].nodes = astRules.nodes.reduce(filterDeclarations(selectedRule), []);\n    }\n\n    return abstractSyntaxTree;\n  }\n\n  /**\n   * Extracts the style attributes from an AbstractSyntaxTree and formats those\n   * values in the inline style attribute format.\n   *\n   * @param  {AbstractSyntaxTree} filteredAST\n   * @return {string}             - Example: \"color:yellow;text-align:center !important;font-family:helvetica;\"\n   */\n  function stringifyStyleAttributes(filteredAST) {\n    return filteredAST.nodes[0].nodes\n      .reduce(function(extractedAttributes, attrObject) {\n        extractedAttributes.push(\n          `${attrObject.prop}:${attrObject.value}${attrObject.important ? ' !important' : ''}`\n        );\n        return extractedAttributes;\n      }, [])\n      .join(';');\n  }\n\n  /**\n    * Filters the existing attributes for the given property. Discards any attributes\n    * which don't match the allowlist.\n    *\n    * @param  {object} selectedRule             - Example: { color: red, font-family: helvetica }\n    * @param  {array} allowedDeclarationsList   - List of declarations which pass the allowlist.\n    * @param  {object} attributeObject          - Object representing the current css property.\n    * @property {string} attributeObject.type   - Typically 'declaration'.\n    * @property {string} attributeObject.prop   - The CSS property, i.e 'color'.\n    * @property {string} attributeObject.value  - The corresponding value to the css property, i.e 'red'.\n    * @return {function}                        - When used in Array.reduce, will return an array of Declaration objects\n    */\n  function filterDeclarations(selectedRule) {\n    return function (allowedDeclarationsList, attributeObject) {\n      // If this property is allowlisted...\n      if (has(selectedRule, attributeObject.prop)) {\n        const matchesRegex = selectedRule[attributeObject.prop].some(function(regularExpression) {\n          return regularExpression.test(attributeObject.value);\n        });\n\n        if (matchesRegex) {\n          allowedDeclarationsList.push(attributeObject);\n        }\n      }\n      return allowedDeclarationsList;\n    };\n  }\n\n  function filterClasses(classes, allowed, allowedGlobs) {\n    if (!allowed) {\n      // The class attribute is allowed without filtering on this tag\n      return classes;\n    }\n    classes = classes.split(/\\s+/);\n    return classes.filter(function(clss) {\n      return allowed.indexOf(clss) !== -1 || allowedGlobs.some(function(glob) {\n        return glob.test(clss);\n      });\n    }).join(' ');\n  }\n}\n\n// Defaults are accessible to you so that you can use them as a starting point\n// programmatically if you wish\n\nconst htmlParserDefaults = {\n  decodeEntities: true\n};\nsanitizeHtml.defaults = {\n  allowedTags: [\n    // Sections derived from MDN element categories and limited to the more\n    // benign categories.\n    // https://developer.mozilla.org/en-US/docs/Web/HTML/Element\n    // Content sectioning\n    'address', 'article', 'aside', 'footer', 'header',\n    'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'hgroup',\n    'main', 'nav', 'section',\n    // Text content\n    'blockquote', 'dd', 'div', 'dl', 'dt', 'figcaption', 'figure',\n    'hr', 'li', 'menu', 'ol', 'p', 'pre', 'ul',\n    // Inline text semantics\n    'a', 'abbr', 'b', 'bdi', 'bdo', 'br', 'cite', 'code', 'data', 'dfn',\n    'em', 'i', 'kbd', 'mark', 'q',\n    'rb', 'rp', 'rt', 'rtc', 'ruby',\n    's', 'samp', 'small', 'span', 'strong', 'sub', 'sup', 'time', 'u', 'var', 'wbr',\n    // Table content\n    'caption', 'col', 'colgroup', 'table', 'tbody', 'td', 'tfoot', 'th',\n    'thead', 'tr'\n  ],\n  // Tags that cannot be boolean\n  nonBooleanAttributes: [\n    'abbr', 'accept', 'accept-charset', 'accesskey', 'action',\n    'allow', 'alt', 'as', 'autocapitalize', 'autocomplete',\n    'blocking', 'charset', 'cite', 'class', 'color', 'cols',\n    'colspan', 'content', 'contenteditable', 'coords', 'crossorigin',\n    'data', 'datetime', 'decoding', 'dir', 'dirname', 'download',\n    'draggable', 'enctype', 'enterkeyhint', 'fetchpriority', 'for',\n    'form', 'formaction', 'formenctype', 'formmethod', 'formtarget',\n    'headers', 'height', 'hidden', 'high', 'href', 'hreflang',\n    'http-equiv', 'id', 'imagesizes', 'imagesrcset', 'inputmode',\n    'integrity', 'is', 'itemid', 'itemprop', 'itemref', 'itemtype',\n    'kind', 'label', 'lang', 'list', 'loading', 'low', 'max',\n    'maxlength', 'media', 'method', 'min', 'minlength', 'name',\n    'nonce', 'optimum', 'pattern', 'ping', 'placeholder', 'popover',\n    'popovertarget', 'popovertargetaction', 'poster', 'preload',\n    'referrerpolicy', 'rel', 'rows', 'rowspan', 'sandbox', 'scope',\n    'shape', 'size', 'sizes', 'slot', 'span', 'spellcheck', 'src',\n    'srcdoc', 'srclang', 'srcset', 'start', 'step', 'style',\n    'tabindex', 'target', 'title', 'translate', 'type', 'usemap',\n    'value', 'width', 'wrap',\n    // Event handlers\n    'onauxclick', 'onafterprint', 'onbeforematch', 'onbeforeprint',\n    'onbeforeunload', 'onbeforetoggle', 'onblur', 'oncancel',\n    'oncanplay', 'oncanplaythrough', 'onchange', 'onclick', 'onclose',\n    'oncontextlost', 'oncontextmenu', 'oncontextrestored', 'oncopy',\n    'oncuechange', 'oncut', 'ondblclick', 'ondrag', 'ondragend',\n    'ondragenter', 'ondragleave', 'ondragover', 'ondragstart',\n    'ondrop', 'ondurationchange', 'onemptied', 'onended',\n    'onerror', 'onfocus', 'onformdata', 'onhashchange', 'oninput',\n    'oninvalid', 'onkeydown', 'onkeypress', 'onkeyup',\n    'onlanguagechange', 'onload', 'onloadeddata', 'onloadedmetadata',\n    'onloadstart', 'onmessage', 'onmessageerror', 'onmousedown',\n    'onmouseenter', 'onmouseleave', 'onmousemove', 'onmouseout',\n    'onmouseover', 'onmouseup', 'onoffline', 'ononline', 'onpagehide',\n    'onpageshow', 'onpaste', 'onpause', 'onplay', 'onplaying',\n    'onpopstate', 'onprogress', 'onratechange', 'onreset', 'onresize',\n    'onrejectionhandled', 'onscroll', 'onscrollend',\n    'onsecuritypolicyviolation', 'onseeked', 'onseeking', 'onselect',\n    'onslotchange', 'onstalled', 'onstorage', 'onsubmit', 'onsuspend',\n    'ontimeupdate', 'ontoggle', 'onunhandledrejection', 'onunload',\n    'onvolumechange', 'onwaiting', 'onwheel'\n  ],\n  disallowedTagsMode: 'discard',\n  allowedAttributes: {\n    a: [ 'href', 'name', 'target' ],\n    // We don't currently allow img itself by default, but\n    // these attributes would make sense if we did.\n    img: [ 'src', 'srcset', 'alt', 'title', 'width', 'height', 'loading' ]\n  },\n  allowedEmptyAttributes: [\n    'alt'\n  ],\n  // Lots of these won't come up by default because we don't allow them\n  selfClosing: [ 'img', 'br', 'hr', 'area', 'base', 'basefont', 'input', 'link', 'meta' ],\n  // URL schemes we permit\n  allowedSchemes: [ 'http', 'https', 'ftp', 'mailto', 'tel' ],\n  allowedSchemesByTag: {},\n  allowedSchemesAppliedToAttributes: [ 'href', 'src', 'cite' ],\n  allowProtocolRelative: true,\n  enforceHtmlBoundary: false,\n  parseStyleAttributes: true,\n  preserveEscapedAttributes: false\n};\n\nsanitizeHtml.simpleTransform = function(newTagName, newAttribs, merge) {\n  merge = (merge === undefined) ? true : merge;\n  newAttribs = newAttribs || {};\n\n  return function(tagName, attribs) {\n    let attrib;\n    if (merge) {\n      for (attrib in newAttribs) {\n        attribs[attrib] = newAttribs[attrib];\n      }\n    } else {\n      attribs = newAttribs;\n    }\n\n    return {\n      tagName: newTagName,\n      attribs: attribs\n    };\n  };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/sanitize-html/index.js\n");

/***/ })

};
;