"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/htmlparser2";
exports.ids = ["vendor-chunks/htmlparser2"];
exports.modules = {

/***/ "(ssr)/./node_modules/htmlparser2/lib/Parser.js":
/*!************************************************!*\
  !*** ./node_modules/htmlparser2/lib/Parser.js ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Parser = void 0;\nvar Tokenizer_js_1 = __importStar(__webpack_require__(/*! ./Tokenizer.js */ \"(ssr)/./node_modules/htmlparser2/lib/Tokenizer.js\"));\nvar decode_js_1 = __webpack_require__(/*! entities/lib/decode.js */ \"(ssr)/./node_modules/entities/lib/decode.js\");\nvar formTags = new Set([\n    \"input\",\n    \"option\",\n    \"optgroup\",\n    \"select\",\n    \"button\",\n    \"datalist\",\n    \"textarea\",\n]);\nvar pTag = new Set([\"p\"]);\nvar tableSectionTags = new Set([\"thead\", \"tbody\"]);\nvar ddtTags = new Set([\"dd\", \"dt\"]);\nvar rtpTags = new Set([\"rt\", \"rp\"]);\nvar openImpliesClose = new Map([\n    [\"tr\", new Set([\"tr\", \"th\", \"td\"])],\n    [\"th\", new Set([\"th\"])],\n    [\"td\", new Set([\"thead\", \"th\", \"td\"])],\n    [\"body\", new Set([\"head\", \"link\", \"script\"])],\n    [\"li\", new Set([\"li\"])],\n    [\"p\", pTag],\n    [\"h1\", pTag],\n    [\"h2\", pTag],\n    [\"h3\", pTag],\n    [\"h4\", pTag],\n    [\"h5\", pTag],\n    [\"h6\", pTag],\n    [\"select\", formTags],\n    [\"input\", formTags],\n    [\"output\", formTags],\n    [\"button\", formTags],\n    [\"datalist\", formTags],\n    [\"textarea\", formTags],\n    [\"option\", new Set([\"option\"])],\n    [\"optgroup\", new Set([\"optgroup\", \"option\"])],\n    [\"dd\", ddtTags],\n    [\"dt\", ddtTags],\n    [\"address\", pTag],\n    [\"article\", pTag],\n    [\"aside\", pTag],\n    [\"blockquote\", pTag],\n    [\"details\", pTag],\n    [\"div\", pTag],\n    [\"dl\", pTag],\n    [\"fieldset\", pTag],\n    [\"figcaption\", pTag],\n    [\"figure\", pTag],\n    [\"footer\", pTag],\n    [\"form\", pTag],\n    [\"header\", pTag],\n    [\"hr\", pTag],\n    [\"main\", pTag],\n    [\"nav\", pTag],\n    [\"ol\", pTag],\n    [\"pre\", pTag],\n    [\"section\", pTag],\n    [\"table\", pTag],\n    [\"ul\", pTag],\n    [\"rt\", rtpTags],\n    [\"rp\", rtpTags],\n    [\"tbody\", tableSectionTags],\n    [\"tfoot\", tableSectionTags],\n]);\nvar voidElements = new Set([\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"command\",\n    \"embed\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"keygen\",\n    \"link\",\n    \"meta\",\n    \"param\",\n    \"source\",\n    \"track\",\n    \"wbr\",\n]);\nvar foreignContextElements = new Set([\"math\", \"svg\"]);\nvar htmlIntegrationElements = new Set([\n    \"mi\",\n    \"mo\",\n    \"mn\",\n    \"ms\",\n    \"mtext\",\n    \"annotation-xml\",\n    \"foreignobject\",\n    \"desc\",\n    \"title\",\n]);\nvar reNameEnd = /\\s|\\//;\nvar Parser = /** @class */ (function () {\n    function Parser(cbs, options) {\n        if (options === void 0) { options = {}; }\n        var _a, _b, _c, _d, _e;\n        this.options = options;\n        /** The start index of the last event. */\n        this.startIndex = 0;\n        /** The end index of the last event. */\n        this.endIndex = 0;\n        /**\n         * Store the start index of the current open tag,\n         * so we can update the start index for attributes.\n         */\n        this.openTagStart = 0;\n        this.tagname = \"\";\n        this.attribname = \"\";\n        this.attribvalue = \"\";\n        this.attribs = null;\n        this.stack = [];\n        this.foreignContext = [];\n        this.buffers = [];\n        this.bufferOffset = 0;\n        /** The index of the last written buffer. Used when resuming after a `pause()`. */\n        this.writeIndex = 0;\n        /** Indicates whether the parser has finished running / `.end` has been called. */\n        this.ended = false;\n        this.cbs = cbs !== null && cbs !== void 0 ? cbs : {};\n        this.lowerCaseTagNames = (_a = options.lowerCaseTags) !== null && _a !== void 0 ? _a : !options.xmlMode;\n        this.lowerCaseAttributeNames =\n            (_b = options.lowerCaseAttributeNames) !== null && _b !== void 0 ? _b : !options.xmlMode;\n        this.tokenizer = new ((_c = options.Tokenizer) !== null && _c !== void 0 ? _c : Tokenizer_js_1.default)(this.options, this);\n        (_e = (_d = this.cbs).onparserinit) === null || _e === void 0 ? void 0 : _e.call(_d, this);\n    }\n    // Tokenizer event handlers\n    /** @internal */\n    Parser.prototype.ontext = function (start, endIndex) {\n        var _a, _b;\n        var data = this.getSlice(start, endIndex);\n        this.endIndex = endIndex - 1;\n        (_b = (_a = this.cbs).ontext) === null || _b === void 0 ? void 0 : _b.call(_a, data);\n        this.startIndex = endIndex;\n    };\n    /** @internal */\n    Parser.prototype.ontextentity = function (cp) {\n        var _a, _b;\n        /*\n         * Entities can be emitted on the character, or directly after.\n         * We use the section start here to get accurate indices.\n         */\n        var index = this.tokenizer.getSectionStart();\n        this.endIndex = index - 1;\n        (_b = (_a = this.cbs).ontext) === null || _b === void 0 ? void 0 : _b.call(_a, (0, decode_js_1.fromCodePoint)(cp));\n        this.startIndex = index;\n    };\n    Parser.prototype.isVoidElement = function (name) {\n        return !this.options.xmlMode && voidElements.has(name);\n    };\n    /** @internal */\n    Parser.prototype.onopentagname = function (start, endIndex) {\n        this.endIndex = endIndex;\n        var name = this.getSlice(start, endIndex);\n        if (this.lowerCaseTagNames) {\n            name = name.toLowerCase();\n        }\n        this.emitOpenTag(name);\n    };\n    Parser.prototype.emitOpenTag = function (name) {\n        var _a, _b, _c, _d;\n        this.openTagStart = this.startIndex;\n        this.tagname = name;\n        var impliesClose = !this.options.xmlMode && openImpliesClose.get(name);\n        if (impliesClose) {\n            while (this.stack.length > 0 &&\n                impliesClose.has(this.stack[this.stack.length - 1])) {\n                var element = this.stack.pop();\n                (_b = (_a = this.cbs).onclosetag) === null || _b === void 0 ? void 0 : _b.call(_a, element, true);\n            }\n        }\n        if (!this.isVoidElement(name)) {\n            this.stack.push(name);\n            if (foreignContextElements.has(name)) {\n                this.foreignContext.push(true);\n            }\n            else if (htmlIntegrationElements.has(name)) {\n                this.foreignContext.push(false);\n            }\n        }\n        (_d = (_c = this.cbs).onopentagname) === null || _d === void 0 ? void 0 : _d.call(_c, name);\n        if (this.cbs.onopentag)\n            this.attribs = {};\n    };\n    Parser.prototype.endOpenTag = function (isImplied) {\n        var _a, _b;\n        this.startIndex = this.openTagStart;\n        if (this.attribs) {\n            (_b = (_a = this.cbs).onopentag) === null || _b === void 0 ? void 0 : _b.call(_a, this.tagname, this.attribs, isImplied);\n            this.attribs = null;\n        }\n        if (this.cbs.onclosetag && this.isVoidElement(this.tagname)) {\n            this.cbs.onclosetag(this.tagname, true);\n        }\n        this.tagname = \"\";\n    };\n    /** @internal */\n    Parser.prototype.onopentagend = function (endIndex) {\n        this.endIndex = endIndex;\n        this.endOpenTag(false);\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    };\n    /** @internal */\n    Parser.prototype.onclosetag = function (start, endIndex) {\n        var _a, _b, _c, _d, _e, _f;\n        this.endIndex = endIndex;\n        var name = this.getSlice(start, endIndex);\n        if (this.lowerCaseTagNames) {\n            name = name.toLowerCase();\n        }\n        if (foreignContextElements.has(name) ||\n            htmlIntegrationElements.has(name)) {\n            this.foreignContext.pop();\n        }\n        if (!this.isVoidElement(name)) {\n            var pos = this.stack.lastIndexOf(name);\n            if (pos !== -1) {\n                if (this.cbs.onclosetag) {\n                    var count = this.stack.length - pos;\n                    while (count--) {\n                        // We know the stack has sufficient elements.\n                        this.cbs.onclosetag(this.stack.pop(), count !== 0);\n                    }\n                }\n                else\n                    this.stack.length = pos;\n            }\n            else if (!this.options.xmlMode && name === \"p\") {\n                // Implicit open before close\n                this.emitOpenTag(\"p\");\n                this.closeCurrentTag(true);\n            }\n        }\n        else if (!this.options.xmlMode && name === \"br\") {\n            // We can't use `emitOpenTag` for implicit open, as `br` would be implicitly closed.\n            (_b = (_a = this.cbs).onopentagname) === null || _b === void 0 ? void 0 : _b.call(_a, \"br\");\n            (_d = (_c = this.cbs).onopentag) === null || _d === void 0 ? void 0 : _d.call(_c, \"br\", {}, true);\n            (_f = (_e = this.cbs).onclosetag) === null || _f === void 0 ? void 0 : _f.call(_e, \"br\", false);\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    };\n    /** @internal */\n    Parser.prototype.onselfclosingtag = function (endIndex) {\n        this.endIndex = endIndex;\n        if (this.options.xmlMode ||\n            this.options.recognizeSelfClosing ||\n            this.foreignContext[this.foreignContext.length - 1]) {\n            this.closeCurrentTag(false);\n            // Set `startIndex` for next node\n            this.startIndex = endIndex + 1;\n        }\n        else {\n            // Ignore the fact that the tag is self-closing.\n            this.onopentagend(endIndex);\n        }\n    };\n    Parser.prototype.closeCurrentTag = function (isOpenImplied) {\n        var _a, _b;\n        var name = this.tagname;\n        this.endOpenTag(isOpenImplied);\n        // Self-closing tags will be on the top of the stack\n        if (this.stack[this.stack.length - 1] === name) {\n            // If the opening tag isn't implied, the closing tag has to be implied.\n            (_b = (_a = this.cbs).onclosetag) === null || _b === void 0 ? void 0 : _b.call(_a, name, !isOpenImplied);\n            this.stack.pop();\n        }\n    };\n    /** @internal */\n    Parser.prototype.onattribname = function (start, endIndex) {\n        this.startIndex = start;\n        var name = this.getSlice(start, endIndex);\n        this.attribname = this.lowerCaseAttributeNames\n            ? name.toLowerCase()\n            : name;\n    };\n    /** @internal */\n    Parser.prototype.onattribdata = function (start, endIndex) {\n        this.attribvalue += this.getSlice(start, endIndex);\n    };\n    /** @internal */\n    Parser.prototype.onattribentity = function (cp) {\n        this.attribvalue += (0, decode_js_1.fromCodePoint)(cp);\n    };\n    /** @internal */\n    Parser.prototype.onattribend = function (quote, endIndex) {\n        var _a, _b;\n        this.endIndex = endIndex;\n        (_b = (_a = this.cbs).onattribute) === null || _b === void 0 ? void 0 : _b.call(_a, this.attribname, this.attribvalue, quote === Tokenizer_js_1.QuoteType.Double\n            ? '\"'\n            : quote === Tokenizer_js_1.QuoteType.Single\n                ? \"'\"\n                : quote === Tokenizer_js_1.QuoteType.NoValue\n                    ? undefined\n                    : null);\n        if (this.attribs &&\n            !Object.prototype.hasOwnProperty.call(this.attribs, this.attribname)) {\n            this.attribs[this.attribname] = this.attribvalue;\n        }\n        this.attribvalue = \"\";\n    };\n    Parser.prototype.getInstructionName = function (value) {\n        var index = value.search(reNameEnd);\n        var name = index < 0 ? value : value.substr(0, index);\n        if (this.lowerCaseTagNames) {\n            name = name.toLowerCase();\n        }\n        return name;\n    };\n    /** @internal */\n    Parser.prototype.ondeclaration = function (start, endIndex) {\n        this.endIndex = endIndex;\n        var value = this.getSlice(start, endIndex);\n        if (this.cbs.onprocessinginstruction) {\n            var name = this.getInstructionName(value);\n            this.cbs.onprocessinginstruction(\"!\".concat(name), \"!\".concat(value));\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    };\n    /** @internal */\n    Parser.prototype.onprocessinginstruction = function (start, endIndex) {\n        this.endIndex = endIndex;\n        var value = this.getSlice(start, endIndex);\n        if (this.cbs.onprocessinginstruction) {\n            var name = this.getInstructionName(value);\n            this.cbs.onprocessinginstruction(\"?\".concat(name), \"?\".concat(value));\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    };\n    /** @internal */\n    Parser.prototype.oncomment = function (start, endIndex, offset) {\n        var _a, _b, _c, _d;\n        this.endIndex = endIndex;\n        (_b = (_a = this.cbs).oncomment) === null || _b === void 0 ? void 0 : _b.call(_a, this.getSlice(start, endIndex - offset));\n        (_d = (_c = this.cbs).oncommentend) === null || _d === void 0 ? void 0 : _d.call(_c);\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    };\n    /** @internal */\n    Parser.prototype.oncdata = function (start, endIndex, offset) {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;\n        this.endIndex = endIndex;\n        var value = this.getSlice(start, endIndex - offset);\n        if (this.options.xmlMode || this.options.recognizeCDATA) {\n            (_b = (_a = this.cbs).oncdatastart) === null || _b === void 0 ? void 0 : _b.call(_a);\n            (_d = (_c = this.cbs).ontext) === null || _d === void 0 ? void 0 : _d.call(_c, value);\n            (_f = (_e = this.cbs).oncdataend) === null || _f === void 0 ? void 0 : _f.call(_e);\n        }\n        else {\n            (_h = (_g = this.cbs).oncomment) === null || _h === void 0 ? void 0 : _h.call(_g, \"[CDATA[\".concat(value, \"]]\"));\n            (_k = (_j = this.cbs).oncommentend) === null || _k === void 0 ? void 0 : _k.call(_j);\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    };\n    /** @internal */\n    Parser.prototype.onend = function () {\n        var _a, _b;\n        if (this.cbs.onclosetag) {\n            // Set the end index for all remaining tags\n            this.endIndex = this.startIndex;\n            for (var index = this.stack.length; index > 0; this.cbs.onclosetag(this.stack[--index], true))\n                ;\n        }\n        (_b = (_a = this.cbs).onend) === null || _b === void 0 ? void 0 : _b.call(_a);\n    };\n    /**\n     * Resets the parser to a blank state, ready to parse a new HTML document\n     */\n    Parser.prototype.reset = function () {\n        var _a, _b, _c, _d;\n        (_b = (_a = this.cbs).onreset) === null || _b === void 0 ? void 0 : _b.call(_a);\n        this.tokenizer.reset();\n        this.tagname = \"\";\n        this.attribname = \"\";\n        this.attribs = null;\n        this.stack.length = 0;\n        this.startIndex = 0;\n        this.endIndex = 0;\n        (_d = (_c = this.cbs).onparserinit) === null || _d === void 0 ? void 0 : _d.call(_c, this);\n        this.buffers.length = 0;\n        this.bufferOffset = 0;\n        this.writeIndex = 0;\n        this.ended = false;\n    };\n    /**\n     * Resets the parser, then parses a complete document and\n     * pushes it to the handler.\n     *\n     * @param data Document to parse.\n     */\n    Parser.prototype.parseComplete = function (data) {\n        this.reset();\n        this.end(data);\n    };\n    Parser.prototype.getSlice = function (start, end) {\n        while (start - this.bufferOffset >= this.buffers[0].length) {\n            this.shiftBuffer();\n        }\n        var slice = this.buffers[0].slice(start - this.bufferOffset, end - this.bufferOffset);\n        while (end - this.bufferOffset > this.buffers[0].length) {\n            this.shiftBuffer();\n            slice += this.buffers[0].slice(0, end - this.bufferOffset);\n        }\n        return slice;\n    };\n    Parser.prototype.shiftBuffer = function () {\n        this.bufferOffset += this.buffers[0].length;\n        this.writeIndex--;\n        this.buffers.shift();\n    };\n    /**\n     * Parses a chunk of data and calls the corresponding callbacks.\n     *\n     * @param chunk Chunk to parse.\n     */\n    Parser.prototype.write = function (chunk) {\n        var _a, _b;\n        if (this.ended) {\n            (_b = (_a = this.cbs).onerror) === null || _b === void 0 ? void 0 : _b.call(_a, new Error(\".write() after done!\"));\n            return;\n        }\n        this.buffers.push(chunk);\n        if (this.tokenizer.running) {\n            this.tokenizer.write(chunk);\n            this.writeIndex++;\n        }\n    };\n    /**\n     * Parses the end of the buffer and clears the stack, calls onend.\n     *\n     * @param chunk Optional final chunk to parse.\n     */\n    Parser.prototype.end = function (chunk) {\n        var _a, _b;\n        if (this.ended) {\n            (_b = (_a = this.cbs).onerror) === null || _b === void 0 ? void 0 : _b.call(_a, new Error(\".end() after done!\"));\n            return;\n        }\n        if (chunk)\n            this.write(chunk);\n        this.ended = true;\n        this.tokenizer.end();\n    };\n    /**\n     * Pauses parsing. The parser won't emit events until `resume` is called.\n     */\n    Parser.prototype.pause = function () {\n        this.tokenizer.pause();\n    };\n    /**\n     * Resumes parsing after `pause` was called.\n     */\n    Parser.prototype.resume = function () {\n        this.tokenizer.resume();\n        while (this.tokenizer.running &&\n            this.writeIndex < this.buffers.length) {\n            this.tokenizer.write(this.buffers[this.writeIndex++]);\n        }\n        if (this.ended)\n            this.tokenizer.end();\n    };\n    /**\n     * Alias of `write`, for backwards compatibility.\n     *\n     * @param chunk Chunk to parse.\n     * @deprecated\n     */\n    Parser.prototype.parseChunk = function (chunk) {\n        this.write(chunk);\n    };\n    /**\n     * Alias of `end`, for backwards compatibility.\n     *\n     * @param chunk Optional final chunk to parse.\n     * @deprecated\n     */\n    Parser.prototype.done = function (chunk) {\n        this.end(chunk);\n    };\n    return Parser;\n}());\nexports.Parser = Parser;\n//# sourceMappingURL=Parser.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/htmlparser2/lib/Parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/htmlparser2/lib/Tokenizer.js":
/*!***************************************************!*\
  !*** ./node_modules/htmlparser2/lib/Tokenizer.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.QuoteType = void 0;\nvar decode_js_1 = __webpack_require__(/*! entities/lib/decode.js */ \"(ssr)/./node_modules/entities/lib/decode.js\");\nvar CharCodes;\n(function (CharCodes) {\n    CharCodes[CharCodes[\"Tab\"] = 9] = \"Tab\";\n    CharCodes[CharCodes[\"NewLine\"] = 10] = \"NewLine\";\n    CharCodes[CharCodes[\"FormFeed\"] = 12] = \"FormFeed\";\n    CharCodes[CharCodes[\"CarriageReturn\"] = 13] = \"CarriageReturn\";\n    CharCodes[CharCodes[\"Space\"] = 32] = \"Space\";\n    CharCodes[CharCodes[\"ExclamationMark\"] = 33] = \"ExclamationMark\";\n    CharCodes[CharCodes[\"Number\"] = 35] = \"Number\";\n    CharCodes[CharCodes[\"Amp\"] = 38] = \"Amp\";\n    CharCodes[CharCodes[\"SingleQuote\"] = 39] = \"SingleQuote\";\n    CharCodes[CharCodes[\"DoubleQuote\"] = 34] = \"DoubleQuote\";\n    CharCodes[CharCodes[\"Dash\"] = 45] = \"Dash\";\n    CharCodes[CharCodes[\"Slash\"] = 47] = \"Slash\";\n    CharCodes[CharCodes[\"Zero\"] = 48] = \"Zero\";\n    CharCodes[CharCodes[\"Nine\"] = 57] = \"Nine\";\n    CharCodes[CharCodes[\"Semi\"] = 59] = \"Semi\";\n    CharCodes[CharCodes[\"Lt\"] = 60] = \"Lt\";\n    CharCodes[CharCodes[\"Eq\"] = 61] = \"Eq\";\n    CharCodes[CharCodes[\"Gt\"] = 62] = \"Gt\";\n    CharCodes[CharCodes[\"Questionmark\"] = 63] = \"Questionmark\";\n    CharCodes[CharCodes[\"UpperA\"] = 65] = \"UpperA\";\n    CharCodes[CharCodes[\"LowerA\"] = 97] = \"LowerA\";\n    CharCodes[CharCodes[\"UpperF\"] = 70] = \"UpperF\";\n    CharCodes[CharCodes[\"LowerF\"] = 102] = \"LowerF\";\n    CharCodes[CharCodes[\"UpperZ\"] = 90] = \"UpperZ\";\n    CharCodes[CharCodes[\"LowerZ\"] = 122] = \"LowerZ\";\n    CharCodes[CharCodes[\"LowerX\"] = 120] = \"LowerX\";\n    CharCodes[CharCodes[\"OpeningSquareBracket\"] = 91] = \"OpeningSquareBracket\";\n})(CharCodes || (CharCodes = {}));\n/** All the states the tokenizer can be in. */\nvar State;\n(function (State) {\n    State[State[\"Text\"] = 1] = \"Text\";\n    State[State[\"BeforeTagName\"] = 2] = \"BeforeTagName\";\n    State[State[\"InTagName\"] = 3] = \"InTagName\";\n    State[State[\"InSelfClosingTag\"] = 4] = \"InSelfClosingTag\";\n    State[State[\"BeforeClosingTagName\"] = 5] = \"BeforeClosingTagName\";\n    State[State[\"InClosingTagName\"] = 6] = \"InClosingTagName\";\n    State[State[\"AfterClosingTagName\"] = 7] = \"AfterClosingTagName\";\n    // Attributes\n    State[State[\"BeforeAttributeName\"] = 8] = \"BeforeAttributeName\";\n    State[State[\"InAttributeName\"] = 9] = \"InAttributeName\";\n    State[State[\"AfterAttributeName\"] = 10] = \"AfterAttributeName\";\n    State[State[\"BeforeAttributeValue\"] = 11] = \"BeforeAttributeValue\";\n    State[State[\"InAttributeValueDq\"] = 12] = \"InAttributeValueDq\";\n    State[State[\"InAttributeValueSq\"] = 13] = \"InAttributeValueSq\";\n    State[State[\"InAttributeValueNq\"] = 14] = \"InAttributeValueNq\";\n    // Declarations\n    State[State[\"BeforeDeclaration\"] = 15] = \"BeforeDeclaration\";\n    State[State[\"InDeclaration\"] = 16] = \"InDeclaration\";\n    // Processing instructions\n    State[State[\"InProcessingInstruction\"] = 17] = \"InProcessingInstruction\";\n    // Comments & CDATA\n    State[State[\"BeforeComment\"] = 18] = \"BeforeComment\";\n    State[State[\"CDATASequence\"] = 19] = \"CDATASequence\";\n    State[State[\"InSpecialComment\"] = 20] = \"InSpecialComment\";\n    State[State[\"InCommentLike\"] = 21] = \"InCommentLike\";\n    // Special tags\n    State[State[\"BeforeSpecialS\"] = 22] = \"BeforeSpecialS\";\n    State[State[\"SpecialStartSequence\"] = 23] = \"SpecialStartSequence\";\n    State[State[\"InSpecialTag\"] = 24] = \"InSpecialTag\";\n    State[State[\"BeforeEntity\"] = 25] = \"BeforeEntity\";\n    State[State[\"BeforeNumericEntity\"] = 26] = \"BeforeNumericEntity\";\n    State[State[\"InNamedEntity\"] = 27] = \"InNamedEntity\";\n    State[State[\"InNumericEntity\"] = 28] = \"InNumericEntity\";\n    State[State[\"InHexEntity\"] = 29] = \"InHexEntity\";\n})(State || (State = {}));\nfunction isWhitespace(c) {\n    return (c === CharCodes.Space ||\n        c === CharCodes.NewLine ||\n        c === CharCodes.Tab ||\n        c === CharCodes.FormFeed ||\n        c === CharCodes.CarriageReturn);\n}\nfunction isEndOfTagSection(c) {\n    return c === CharCodes.Slash || c === CharCodes.Gt || isWhitespace(c);\n}\nfunction isNumber(c) {\n    return c >= CharCodes.Zero && c <= CharCodes.Nine;\n}\nfunction isASCIIAlpha(c) {\n    return ((c >= CharCodes.LowerA && c <= CharCodes.LowerZ) ||\n        (c >= CharCodes.UpperA && c <= CharCodes.UpperZ));\n}\nfunction isHexDigit(c) {\n    return ((c >= CharCodes.UpperA && c <= CharCodes.UpperF) ||\n        (c >= CharCodes.LowerA && c <= CharCodes.LowerF));\n}\nvar QuoteType;\n(function (QuoteType) {\n    QuoteType[QuoteType[\"NoValue\"] = 0] = \"NoValue\";\n    QuoteType[QuoteType[\"Unquoted\"] = 1] = \"Unquoted\";\n    QuoteType[QuoteType[\"Single\"] = 2] = \"Single\";\n    QuoteType[QuoteType[\"Double\"] = 3] = \"Double\";\n})(QuoteType = exports.QuoteType || (exports.QuoteType = {}));\n/**\n * Sequences used to match longer strings.\n *\n * We don't have `Script`, `Style`, or `Title` here. Instead, we re-use the *End\n * sequences with an increased offset.\n */\nvar Sequences = {\n    Cdata: new Uint8Array([0x43, 0x44, 0x41, 0x54, 0x41, 0x5b]),\n    CdataEnd: new Uint8Array([0x5d, 0x5d, 0x3e]),\n    CommentEnd: new Uint8Array([0x2d, 0x2d, 0x3e]),\n    ScriptEnd: new Uint8Array([0x3c, 0x2f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74]),\n    StyleEnd: new Uint8Array([0x3c, 0x2f, 0x73, 0x74, 0x79, 0x6c, 0x65]),\n    TitleEnd: new Uint8Array([0x3c, 0x2f, 0x74, 0x69, 0x74, 0x6c, 0x65]), // `</title`\n};\nvar Tokenizer = /** @class */ (function () {\n    function Tokenizer(_a, cbs) {\n        var _b = _a.xmlMode, xmlMode = _b === void 0 ? false : _b, _c = _a.decodeEntities, decodeEntities = _c === void 0 ? true : _c;\n        this.cbs = cbs;\n        /** The current state the tokenizer is in. */\n        this.state = State.Text;\n        /** The read buffer. */\n        this.buffer = \"\";\n        /** The beginning of the section that is currently being read. */\n        this.sectionStart = 0;\n        /** The index within the buffer that we are currently looking at. */\n        this.index = 0;\n        /** Some behavior, eg. when decoding entities, is done while we are in another state. This keeps track of the other state type. */\n        this.baseState = State.Text;\n        /** For special parsing behavior inside of script and style tags. */\n        this.isSpecial = false;\n        /** Indicates whether the tokenizer has been paused. */\n        this.running = true;\n        /** The offset of the current buffer. */\n        this.offset = 0;\n        this.currentSequence = undefined;\n        this.sequenceIndex = 0;\n        this.trieIndex = 0;\n        this.trieCurrent = 0;\n        /** For named entities, the index of the value. For numeric entities, the code point. */\n        this.entityResult = 0;\n        this.entityExcess = 0;\n        this.xmlMode = xmlMode;\n        this.decodeEntities = decodeEntities;\n        this.entityTrie = xmlMode ? decode_js_1.xmlDecodeTree : decode_js_1.htmlDecodeTree;\n    }\n    Tokenizer.prototype.reset = function () {\n        this.state = State.Text;\n        this.buffer = \"\";\n        this.sectionStart = 0;\n        this.index = 0;\n        this.baseState = State.Text;\n        this.currentSequence = undefined;\n        this.running = true;\n        this.offset = 0;\n    };\n    Tokenizer.prototype.write = function (chunk) {\n        this.offset += this.buffer.length;\n        this.buffer = chunk;\n        this.parse();\n    };\n    Tokenizer.prototype.end = function () {\n        if (this.running)\n            this.finish();\n    };\n    Tokenizer.prototype.pause = function () {\n        this.running = false;\n    };\n    Tokenizer.prototype.resume = function () {\n        this.running = true;\n        if (this.index < this.buffer.length + this.offset) {\n            this.parse();\n        }\n    };\n    /**\n     * The current index within all of the written data.\n     */\n    Tokenizer.prototype.getIndex = function () {\n        return this.index;\n    };\n    /**\n     * The start of the current section.\n     */\n    Tokenizer.prototype.getSectionStart = function () {\n        return this.sectionStart;\n    };\n    Tokenizer.prototype.stateText = function (c) {\n        if (c === CharCodes.Lt ||\n            (!this.decodeEntities && this.fastForwardTo(CharCodes.Lt))) {\n            if (this.index > this.sectionStart) {\n                this.cbs.ontext(this.sectionStart, this.index);\n            }\n            this.state = State.BeforeTagName;\n            this.sectionStart = this.index;\n        }\n        else if (this.decodeEntities && c === CharCodes.Amp) {\n            this.state = State.BeforeEntity;\n        }\n    };\n    Tokenizer.prototype.stateSpecialStartSequence = function (c) {\n        var isEnd = this.sequenceIndex === this.currentSequence.length;\n        var isMatch = isEnd\n            ? // If we are at the end of the sequence, make sure the tag name has ended\n                isEndOfTagSection(c)\n            : // Otherwise, do a case-insensitive comparison\n                (c | 0x20) === this.currentSequence[this.sequenceIndex];\n        if (!isMatch) {\n            this.isSpecial = false;\n        }\n        else if (!isEnd) {\n            this.sequenceIndex++;\n            return;\n        }\n        this.sequenceIndex = 0;\n        this.state = State.InTagName;\n        this.stateInTagName(c);\n    };\n    /** Look for an end tag. For <title> tags, also decode entities. */\n    Tokenizer.prototype.stateInSpecialTag = function (c) {\n        if (this.sequenceIndex === this.currentSequence.length) {\n            if (c === CharCodes.Gt || isWhitespace(c)) {\n                var endOfText = this.index - this.currentSequence.length;\n                if (this.sectionStart < endOfText) {\n                    // Spoof the index so that reported locations match up.\n                    var actualIndex = this.index;\n                    this.index = endOfText;\n                    this.cbs.ontext(this.sectionStart, endOfText);\n                    this.index = actualIndex;\n                }\n                this.isSpecial = false;\n                this.sectionStart = endOfText + 2; // Skip over the `</`\n                this.stateInClosingTagName(c);\n                return; // We are done; skip the rest of the function.\n            }\n            this.sequenceIndex = 0;\n        }\n        if ((c | 0x20) === this.currentSequence[this.sequenceIndex]) {\n            this.sequenceIndex += 1;\n        }\n        else if (this.sequenceIndex === 0) {\n            if (this.currentSequence === Sequences.TitleEnd) {\n                // We have to parse entities in <title> tags.\n                if (this.decodeEntities && c === CharCodes.Amp) {\n                    this.state = State.BeforeEntity;\n                }\n            }\n            else if (this.fastForwardTo(CharCodes.Lt)) {\n                // Outside of <title> tags, we can fast-forward.\n                this.sequenceIndex = 1;\n            }\n        }\n        else {\n            // If we see a `<`, set the sequence index to 1; useful for eg. `<</script>`.\n            this.sequenceIndex = Number(c === CharCodes.Lt);\n        }\n    };\n    Tokenizer.prototype.stateCDATASequence = function (c) {\n        if (c === Sequences.Cdata[this.sequenceIndex]) {\n            if (++this.sequenceIndex === Sequences.Cdata.length) {\n                this.state = State.InCommentLike;\n                this.currentSequence = Sequences.CdataEnd;\n                this.sequenceIndex = 0;\n                this.sectionStart = this.index + 1;\n            }\n        }\n        else {\n            this.sequenceIndex = 0;\n            this.state = State.InDeclaration;\n            this.stateInDeclaration(c); // Reconsume the character\n        }\n    };\n    /**\n     * When we wait for one specific character, we can speed things up\n     * by skipping through the buffer until we find it.\n     *\n     * @returns Whether the character was found.\n     */\n    Tokenizer.prototype.fastForwardTo = function (c) {\n        while (++this.index < this.buffer.length + this.offset) {\n            if (this.buffer.charCodeAt(this.index - this.offset) === c) {\n                return true;\n            }\n        }\n        /*\n         * We increment the index at the end of the `parse` loop,\n         * so set it to `buffer.length - 1` here.\n         *\n         * TODO: Refactor `parse` to increment index before calling states.\n         */\n        this.index = this.buffer.length + this.offset - 1;\n        return false;\n    };\n    /**\n     * Comments and CDATA end with `-->` and `]]>`.\n     *\n     * Their common qualities are:\n     * - Their end sequences have a distinct character they start with.\n     * - That character is then repeated, so we have to check multiple repeats.\n     * - All characters but the start character of the sequence can be skipped.\n     */\n    Tokenizer.prototype.stateInCommentLike = function (c) {\n        if (c === this.currentSequence[this.sequenceIndex]) {\n            if (++this.sequenceIndex === this.currentSequence.length) {\n                if (this.currentSequence === Sequences.CdataEnd) {\n                    this.cbs.oncdata(this.sectionStart, this.index, 2);\n                }\n                else {\n                    this.cbs.oncomment(this.sectionStart, this.index, 2);\n                }\n                this.sequenceIndex = 0;\n                this.sectionStart = this.index + 1;\n                this.state = State.Text;\n            }\n        }\n        else if (this.sequenceIndex === 0) {\n            // Fast-forward to the first character of the sequence\n            if (this.fastForwardTo(this.currentSequence[0])) {\n                this.sequenceIndex = 1;\n            }\n        }\n        else if (c !== this.currentSequence[this.sequenceIndex - 1]) {\n            // Allow long sequences, eg. --->, ]]]>\n            this.sequenceIndex = 0;\n        }\n    };\n    /**\n     * HTML only allows ASCII alpha characters (a-z and A-Z) at the beginning of a tag name.\n     *\n     * XML allows a lot more characters here (@see https://www.w3.org/TR/REC-xml/#NT-NameStartChar).\n     * We allow anything that wouldn't end the tag.\n     */\n    Tokenizer.prototype.isTagStartChar = function (c) {\n        return this.xmlMode ? !isEndOfTagSection(c) : isASCIIAlpha(c);\n    };\n    Tokenizer.prototype.startSpecial = function (sequence, offset) {\n        this.isSpecial = true;\n        this.currentSequence = sequence;\n        this.sequenceIndex = offset;\n        this.state = State.SpecialStartSequence;\n    };\n    Tokenizer.prototype.stateBeforeTagName = function (c) {\n        if (c === CharCodes.ExclamationMark) {\n            this.state = State.BeforeDeclaration;\n            this.sectionStart = this.index + 1;\n        }\n        else if (c === CharCodes.Questionmark) {\n            this.state = State.InProcessingInstruction;\n            this.sectionStart = this.index + 1;\n        }\n        else if (this.isTagStartChar(c)) {\n            var lower = c | 0x20;\n            this.sectionStart = this.index;\n            if (!this.xmlMode && lower === Sequences.TitleEnd[2]) {\n                this.startSpecial(Sequences.TitleEnd, 3);\n            }\n            else {\n                this.state =\n                    !this.xmlMode && lower === Sequences.ScriptEnd[2]\n                        ? State.BeforeSpecialS\n                        : State.InTagName;\n            }\n        }\n        else if (c === CharCodes.Slash) {\n            this.state = State.BeforeClosingTagName;\n        }\n        else {\n            this.state = State.Text;\n            this.stateText(c);\n        }\n    };\n    Tokenizer.prototype.stateInTagName = function (c) {\n        if (isEndOfTagSection(c)) {\n            this.cbs.onopentagname(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n    };\n    Tokenizer.prototype.stateBeforeClosingTagName = function (c) {\n        if (isWhitespace(c)) {\n            // Ignore\n        }\n        else if (c === CharCodes.Gt) {\n            this.state = State.Text;\n        }\n        else {\n            this.state = this.isTagStartChar(c)\n                ? State.InClosingTagName\n                : State.InSpecialComment;\n            this.sectionStart = this.index;\n        }\n    };\n    Tokenizer.prototype.stateInClosingTagName = function (c) {\n        if (c === CharCodes.Gt || isWhitespace(c)) {\n            this.cbs.onclosetag(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.state = State.AfterClosingTagName;\n            this.stateAfterClosingTagName(c);\n        }\n    };\n    Tokenizer.prototype.stateAfterClosingTagName = function (c) {\n        // Skip everything until \">\"\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.state = State.Text;\n            this.baseState = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    };\n    Tokenizer.prototype.stateBeforeAttributeName = function (c) {\n        if (c === CharCodes.Gt) {\n            this.cbs.onopentagend(this.index);\n            if (this.isSpecial) {\n                this.state = State.InSpecialTag;\n                this.sequenceIndex = 0;\n            }\n            else {\n                this.state = State.Text;\n            }\n            this.baseState = this.state;\n            this.sectionStart = this.index + 1;\n        }\n        else if (c === CharCodes.Slash) {\n            this.state = State.InSelfClosingTag;\n        }\n        else if (!isWhitespace(c)) {\n            this.state = State.InAttributeName;\n            this.sectionStart = this.index;\n        }\n    };\n    Tokenizer.prototype.stateInSelfClosingTag = function (c) {\n        if (c === CharCodes.Gt) {\n            this.cbs.onselfclosingtag(this.index);\n            this.state = State.Text;\n            this.baseState = State.Text;\n            this.sectionStart = this.index + 1;\n            this.isSpecial = false; // Reset special state, in case of self-closing special tags\n        }\n        else if (!isWhitespace(c)) {\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n    };\n    Tokenizer.prototype.stateInAttributeName = function (c) {\n        if (c === CharCodes.Eq || isEndOfTagSection(c)) {\n            this.cbs.onattribname(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.state = State.AfterAttributeName;\n            this.stateAfterAttributeName(c);\n        }\n    };\n    Tokenizer.prototype.stateAfterAttributeName = function (c) {\n        if (c === CharCodes.Eq) {\n            this.state = State.BeforeAttributeValue;\n        }\n        else if (c === CharCodes.Slash || c === CharCodes.Gt) {\n            this.cbs.onattribend(QuoteType.NoValue, this.index);\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n        else if (!isWhitespace(c)) {\n            this.cbs.onattribend(QuoteType.NoValue, this.index);\n            this.state = State.InAttributeName;\n            this.sectionStart = this.index;\n        }\n    };\n    Tokenizer.prototype.stateBeforeAttributeValue = function (c) {\n        if (c === CharCodes.DoubleQuote) {\n            this.state = State.InAttributeValueDq;\n            this.sectionStart = this.index + 1;\n        }\n        else if (c === CharCodes.SingleQuote) {\n            this.state = State.InAttributeValueSq;\n            this.sectionStart = this.index + 1;\n        }\n        else if (!isWhitespace(c)) {\n            this.sectionStart = this.index;\n            this.state = State.InAttributeValueNq;\n            this.stateInAttributeValueNoQuotes(c); // Reconsume token\n        }\n    };\n    Tokenizer.prototype.handleInAttributeValue = function (c, quote) {\n        if (c === quote ||\n            (!this.decodeEntities && this.fastForwardTo(quote))) {\n            this.cbs.onattribdata(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.cbs.onattribend(quote === CharCodes.DoubleQuote\n                ? QuoteType.Double\n                : QuoteType.Single, this.index);\n            this.state = State.BeforeAttributeName;\n        }\n        else if (this.decodeEntities && c === CharCodes.Amp) {\n            this.baseState = this.state;\n            this.state = State.BeforeEntity;\n        }\n    };\n    Tokenizer.prototype.stateInAttributeValueDoubleQuotes = function (c) {\n        this.handleInAttributeValue(c, CharCodes.DoubleQuote);\n    };\n    Tokenizer.prototype.stateInAttributeValueSingleQuotes = function (c) {\n        this.handleInAttributeValue(c, CharCodes.SingleQuote);\n    };\n    Tokenizer.prototype.stateInAttributeValueNoQuotes = function (c) {\n        if (isWhitespace(c) || c === CharCodes.Gt) {\n            this.cbs.onattribdata(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.cbs.onattribend(QuoteType.Unquoted, this.index);\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n        else if (this.decodeEntities && c === CharCodes.Amp) {\n            this.baseState = this.state;\n            this.state = State.BeforeEntity;\n        }\n    };\n    Tokenizer.prototype.stateBeforeDeclaration = function (c) {\n        if (c === CharCodes.OpeningSquareBracket) {\n            this.state = State.CDATASequence;\n            this.sequenceIndex = 0;\n        }\n        else {\n            this.state =\n                c === CharCodes.Dash\n                    ? State.BeforeComment\n                    : State.InDeclaration;\n        }\n    };\n    Tokenizer.prototype.stateInDeclaration = function (c) {\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.cbs.ondeclaration(this.sectionStart, this.index);\n            this.state = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    };\n    Tokenizer.prototype.stateInProcessingInstruction = function (c) {\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.cbs.onprocessinginstruction(this.sectionStart, this.index);\n            this.state = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    };\n    Tokenizer.prototype.stateBeforeComment = function (c) {\n        if (c === CharCodes.Dash) {\n            this.state = State.InCommentLike;\n            this.currentSequence = Sequences.CommentEnd;\n            // Allow short comments (eg. <!-->)\n            this.sequenceIndex = 2;\n            this.sectionStart = this.index + 1;\n        }\n        else {\n            this.state = State.InDeclaration;\n        }\n    };\n    Tokenizer.prototype.stateInSpecialComment = function (c) {\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.cbs.oncomment(this.sectionStart, this.index, 0);\n            this.state = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    };\n    Tokenizer.prototype.stateBeforeSpecialS = function (c) {\n        var lower = c | 0x20;\n        if (lower === Sequences.ScriptEnd[3]) {\n            this.startSpecial(Sequences.ScriptEnd, 4);\n        }\n        else if (lower === Sequences.StyleEnd[3]) {\n            this.startSpecial(Sequences.StyleEnd, 4);\n        }\n        else {\n            this.state = State.InTagName;\n            this.stateInTagName(c); // Consume the token again\n        }\n    };\n    Tokenizer.prototype.stateBeforeEntity = function (c) {\n        // Start excess with 1 to include the '&'\n        this.entityExcess = 1;\n        this.entityResult = 0;\n        if (c === CharCodes.Number) {\n            this.state = State.BeforeNumericEntity;\n        }\n        else if (c === CharCodes.Amp) {\n            // We have two `&` characters in a row. Stay in the current state.\n        }\n        else {\n            this.trieIndex = 0;\n            this.trieCurrent = this.entityTrie[0];\n            this.state = State.InNamedEntity;\n            this.stateInNamedEntity(c);\n        }\n    };\n    Tokenizer.prototype.stateInNamedEntity = function (c) {\n        this.entityExcess += 1;\n        this.trieIndex = (0, decode_js_1.determineBranch)(this.entityTrie, this.trieCurrent, this.trieIndex + 1, c);\n        if (this.trieIndex < 0) {\n            this.emitNamedEntity();\n            this.index--;\n            return;\n        }\n        this.trieCurrent = this.entityTrie[this.trieIndex];\n        var masked = this.trieCurrent & decode_js_1.BinTrieFlags.VALUE_LENGTH;\n        // If the branch is a value, store it and continue\n        if (masked) {\n            // The mask is the number of bytes of the value, including the current byte.\n            var valueLength = (masked >> 14) - 1;\n            // If we have a legacy entity while parsing strictly, just skip the number of bytes\n            if (!this.allowLegacyEntity() && c !== CharCodes.Semi) {\n                this.trieIndex += valueLength;\n            }\n            else {\n                // Add 1 as we have already incremented the excess\n                var entityStart = this.index - this.entityExcess + 1;\n                if (entityStart > this.sectionStart) {\n                    this.emitPartial(this.sectionStart, entityStart);\n                }\n                // If this is a surrogate pair, consume the next two bytes\n                this.entityResult = this.trieIndex;\n                this.trieIndex += valueLength;\n                this.entityExcess = 0;\n                this.sectionStart = this.index + 1;\n                if (valueLength === 0) {\n                    this.emitNamedEntity();\n                }\n            }\n        }\n    };\n    Tokenizer.prototype.emitNamedEntity = function () {\n        this.state = this.baseState;\n        if (this.entityResult === 0) {\n            return;\n        }\n        var valueLength = (this.entityTrie[this.entityResult] & decode_js_1.BinTrieFlags.VALUE_LENGTH) >>\n            14;\n        switch (valueLength) {\n            case 1: {\n                this.emitCodePoint(this.entityTrie[this.entityResult] &\n                    ~decode_js_1.BinTrieFlags.VALUE_LENGTH);\n                break;\n            }\n            case 2: {\n                this.emitCodePoint(this.entityTrie[this.entityResult + 1]);\n                break;\n            }\n            case 3: {\n                this.emitCodePoint(this.entityTrie[this.entityResult + 1]);\n                this.emitCodePoint(this.entityTrie[this.entityResult + 2]);\n            }\n        }\n    };\n    Tokenizer.prototype.stateBeforeNumericEntity = function (c) {\n        if ((c | 0x20) === CharCodes.LowerX) {\n            this.entityExcess++;\n            this.state = State.InHexEntity;\n        }\n        else {\n            this.state = State.InNumericEntity;\n            this.stateInNumericEntity(c);\n        }\n    };\n    Tokenizer.prototype.emitNumericEntity = function (strict) {\n        var entityStart = this.index - this.entityExcess - 1;\n        var numberStart = entityStart + 2 + Number(this.state === State.InHexEntity);\n        if (numberStart !== this.index) {\n            // Emit leading data if any\n            if (entityStart > this.sectionStart) {\n                this.emitPartial(this.sectionStart, entityStart);\n            }\n            this.sectionStart = this.index + Number(strict);\n            this.emitCodePoint((0, decode_js_1.replaceCodePoint)(this.entityResult));\n        }\n        this.state = this.baseState;\n    };\n    Tokenizer.prototype.stateInNumericEntity = function (c) {\n        if (c === CharCodes.Semi) {\n            this.emitNumericEntity(true);\n        }\n        else if (isNumber(c)) {\n            this.entityResult = this.entityResult * 10 + (c - CharCodes.Zero);\n            this.entityExcess++;\n        }\n        else {\n            if (this.allowLegacyEntity()) {\n                this.emitNumericEntity(false);\n            }\n            else {\n                this.state = this.baseState;\n            }\n            this.index--;\n        }\n    };\n    Tokenizer.prototype.stateInHexEntity = function (c) {\n        if (c === CharCodes.Semi) {\n            this.emitNumericEntity(true);\n        }\n        else if (isNumber(c)) {\n            this.entityResult = this.entityResult * 16 + (c - CharCodes.Zero);\n            this.entityExcess++;\n        }\n        else if (isHexDigit(c)) {\n            this.entityResult =\n                this.entityResult * 16 + ((c | 0x20) - CharCodes.LowerA + 10);\n            this.entityExcess++;\n        }\n        else {\n            if (this.allowLegacyEntity()) {\n                this.emitNumericEntity(false);\n            }\n            else {\n                this.state = this.baseState;\n            }\n            this.index--;\n        }\n    };\n    Tokenizer.prototype.allowLegacyEntity = function () {\n        return (!this.xmlMode &&\n            (this.baseState === State.Text ||\n                this.baseState === State.InSpecialTag));\n    };\n    /**\n     * Remove data that has already been consumed from the buffer.\n     */\n    Tokenizer.prototype.cleanup = function () {\n        // If we are inside of text or attributes, emit what we already have.\n        if (this.running && this.sectionStart !== this.index) {\n            if (this.state === State.Text ||\n                (this.state === State.InSpecialTag && this.sequenceIndex === 0)) {\n                this.cbs.ontext(this.sectionStart, this.index);\n                this.sectionStart = this.index;\n            }\n            else if (this.state === State.InAttributeValueDq ||\n                this.state === State.InAttributeValueSq ||\n                this.state === State.InAttributeValueNq) {\n                this.cbs.onattribdata(this.sectionStart, this.index);\n                this.sectionStart = this.index;\n            }\n        }\n    };\n    Tokenizer.prototype.shouldContinue = function () {\n        return this.index < this.buffer.length + this.offset && this.running;\n    };\n    /**\n     * Iterates through the buffer, calling the function corresponding to the current state.\n     *\n     * States that are more likely to be hit are higher up, as a performance improvement.\n     */\n    Tokenizer.prototype.parse = function () {\n        while (this.shouldContinue()) {\n            var c = this.buffer.charCodeAt(this.index - this.offset);\n            switch (this.state) {\n                case State.Text: {\n                    this.stateText(c);\n                    break;\n                }\n                case State.SpecialStartSequence: {\n                    this.stateSpecialStartSequence(c);\n                    break;\n                }\n                case State.InSpecialTag: {\n                    this.stateInSpecialTag(c);\n                    break;\n                }\n                case State.CDATASequence: {\n                    this.stateCDATASequence(c);\n                    break;\n                }\n                case State.InAttributeValueDq: {\n                    this.stateInAttributeValueDoubleQuotes(c);\n                    break;\n                }\n                case State.InAttributeName: {\n                    this.stateInAttributeName(c);\n                    break;\n                }\n                case State.InCommentLike: {\n                    this.stateInCommentLike(c);\n                    break;\n                }\n                case State.InSpecialComment: {\n                    this.stateInSpecialComment(c);\n                    break;\n                }\n                case State.BeforeAttributeName: {\n                    this.stateBeforeAttributeName(c);\n                    break;\n                }\n                case State.InTagName: {\n                    this.stateInTagName(c);\n                    break;\n                }\n                case State.InClosingTagName: {\n                    this.stateInClosingTagName(c);\n                    break;\n                }\n                case State.BeforeTagName: {\n                    this.stateBeforeTagName(c);\n                    break;\n                }\n                case State.AfterAttributeName: {\n                    this.stateAfterAttributeName(c);\n                    break;\n                }\n                case State.InAttributeValueSq: {\n                    this.stateInAttributeValueSingleQuotes(c);\n                    break;\n                }\n                case State.BeforeAttributeValue: {\n                    this.stateBeforeAttributeValue(c);\n                    break;\n                }\n                case State.BeforeClosingTagName: {\n                    this.stateBeforeClosingTagName(c);\n                    break;\n                }\n                case State.AfterClosingTagName: {\n                    this.stateAfterClosingTagName(c);\n                    break;\n                }\n                case State.BeforeSpecialS: {\n                    this.stateBeforeSpecialS(c);\n                    break;\n                }\n                case State.InAttributeValueNq: {\n                    this.stateInAttributeValueNoQuotes(c);\n                    break;\n                }\n                case State.InSelfClosingTag: {\n                    this.stateInSelfClosingTag(c);\n                    break;\n                }\n                case State.InDeclaration: {\n                    this.stateInDeclaration(c);\n                    break;\n                }\n                case State.BeforeDeclaration: {\n                    this.stateBeforeDeclaration(c);\n                    break;\n                }\n                case State.BeforeComment: {\n                    this.stateBeforeComment(c);\n                    break;\n                }\n                case State.InProcessingInstruction: {\n                    this.stateInProcessingInstruction(c);\n                    break;\n                }\n                case State.InNamedEntity: {\n                    this.stateInNamedEntity(c);\n                    break;\n                }\n                case State.BeforeEntity: {\n                    this.stateBeforeEntity(c);\n                    break;\n                }\n                case State.InHexEntity: {\n                    this.stateInHexEntity(c);\n                    break;\n                }\n                case State.InNumericEntity: {\n                    this.stateInNumericEntity(c);\n                    break;\n                }\n                default: {\n                    // `this._state === State.BeforeNumericEntity`\n                    this.stateBeforeNumericEntity(c);\n                }\n            }\n            this.index++;\n        }\n        this.cleanup();\n    };\n    Tokenizer.prototype.finish = function () {\n        if (this.state === State.InNamedEntity) {\n            this.emitNamedEntity();\n        }\n        // If there is remaining data, emit it in a reasonable way\n        if (this.sectionStart < this.index) {\n            this.handleTrailingData();\n        }\n        this.cbs.onend();\n    };\n    /** Handle any trailing data. */\n    Tokenizer.prototype.handleTrailingData = function () {\n        var endIndex = this.buffer.length + this.offset;\n        if (this.state === State.InCommentLike) {\n            if (this.currentSequence === Sequences.CdataEnd) {\n                this.cbs.oncdata(this.sectionStart, endIndex, 0);\n            }\n            else {\n                this.cbs.oncomment(this.sectionStart, endIndex, 0);\n            }\n        }\n        else if (this.state === State.InNumericEntity &&\n            this.allowLegacyEntity()) {\n            this.emitNumericEntity(false);\n            // All trailing data will have been consumed\n        }\n        else if (this.state === State.InHexEntity &&\n            this.allowLegacyEntity()) {\n            this.emitNumericEntity(false);\n            // All trailing data will have been consumed\n        }\n        else if (this.state === State.InTagName ||\n            this.state === State.BeforeAttributeName ||\n            this.state === State.BeforeAttributeValue ||\n            this.state === State.AfterAttributeName ||\n            this.state === State.InAttributeName ||\n            this.state === State.InAttributeValueSq ||\n            this.state === State.InAttributeValueDq ||\n            this.state === State.InAttributeValueNq ||\n            this.state === State.InClosingTagName) {\n            /*\n             * If we are currently in an opening or closing tag, us not calling the\n             * respective callback signals that the tag should be ignored.\n             */\n        }\n        else {\n            this.cbs.ontext(this.sectionStart, endIndex);\n        }\n    };\n    Tokenizer.prototype.emitPartial = function (start, endIndex) {\n        if (this.baseState !== State.Text &&\n            this.baseState !== State.InSpecialTag) {\n            this.cbs.onattribdata(start, endIndex);\n        }\n        else {\n            this.cbs.ontext(start, endIndex);\n        }\n    };\n    Tokenizer.prototype.emitCodePoint = function (cp) {\n        if (this.baseState !== State.Text &&\n            this.baseState !== State.InSpecialTag) {\n            this.cbs.onattribentity(cp);\n        }\n        else {\n            this.cbs.ontextentity(cp);\n        }\n    };\n    return Tokenizer;\n}());\nexports[\"default\"] = Tokenizer;\n//# sourceMappingURL=Tokenizer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/htmlparser2/lib/Tokenizer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/htmlparser2/lib/index.js":
/*!***********************************************!*\
  !*** ./node_modules/htmlparser2/lib/index.js ***!
  \***********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DomUtils = exports.parseFeed = exports.getFeed = exports.ElementType = exports.Tokenizer = exports.createDomStream = exports.parseDOM = exports.parseDocument = exports.DefaultHandler = exports.DomHandler = exports.Parser = void 0;\nvar Parser_js_1 = __webpack_require__(/*! ./Parser.js */ \"(ssr)/./node_modules/htmlparser2/lib/Parser.js\");\nvar Parser_js_2 = __webpack_require__(/*! ./Parser.js */ \"(ssr)/./node_modules/htmlparser2/lib/Parser.js\");\nObject.defineProperty(exports, \"Parser\", ({ enumerable: true, get: function () { return Parser_js_2.Parser; } }));\nvar domhandler_1 = __webpack_require__(/*! domhandler */ \"(ssr)/./node_modules/domhandler/lib/index.js\");\nvar domhandler_2 = __webpack_require__(/*! domhandler */ \"(ssr)/./node_modules/domhandler/lib/index.js\");\nObject.defineProperty(exports, \"DomHandler\", ({ enumerable: true, get: function () { return domhandler_2.DomHandler; } }));\n// Old name for DomHandler\nObject.defineProperty(exports, \"DefaultHandler\", ({ enumerable: true, get: function () { return domhandler_2.DomHandler; } }));\n// Helper methods\n/**\n * Parses the data, returns the resulting document.\n *\n * @param data The data that should be parsed.\n * @param options Optional options for the parser and DOM builder.\n */\nfunction parseDocument(data, options) {\n    var handler = new domhandler_1.DomHandler(undefined, options);\n    new Parser_js_1.Parser(handler, options).end(data);\n    return handler.root;\n}\nexports.parseDocument = parseDocument;\n/**\n * Parses data, returns an array of the root nodes.\n *\n * Note that the root nodes still have a `Document` node as their parent.\n * Use `parseDocument` to get the `Document` node instead.\n *\n * @param data The data that should be parsed.\n * @param options Optional options for the parser and DOM builder.\n * @deprecated Use `parseDocument` instead.\n */\nfunction parseDOM(data, options) {\n    return parseDocument(data, options).children;\n}\nexports.parseDOM = parseDOM;\n/**\n * Creates a parser instance, with an attached DOM handler.\n *\n * @param callback A callback that will be called once parsing has been completed.\n * @param options Optional options for the parser and DOM builder.\n * @param elementCallback An optional callback that will be called every time a tag has been completed inside of the DOM.\n */\nfunction createDomStream(callback, options, elementCallback) {\n    var handler = new domhandler_1.DomHandler(callback, options, elementCallback);\n    return new Parser_js_1.Parser(handler, options);\n}\nexports.createDomStream = createDomStream;\nvar Tokenizer_js_1 = __webpack_require__(/*! ./Tokenizer.js */ \"(ssr)/./node_modules/htmlparser2/lib/Tokenizer.js\");\nObject.defineProperty(exports, \"Tokenizer\", ({ enumerable: true, get: function () { return __importDefault(Tokenizer_js_1).default; } }));\n/*\n * All of the following exports exist for backwards-compatibility.\n * They should probably be removed eventually.\n */\nexports.ElementType = __importStar(__webpack_require__(/*! domelementtype */ \"(ssr)/./node_modules/domelementtype/lib/index.js\"));\nvar domutils_1 = __webpack_require__(/*! domutils */ \"(ssr)/./node_modules/domutils/lib/index.js\");\nvar domutils_2 = __webpack_require__(/*! domutils */ \"(ssr)/./node_modules/domutils/lib/index.js\");\nObject.defineProperty(exports, \"getFeed\", ({ enumerable: true, get: function () { return domutils_2.getFeed; } }));\nvar parseFeedDefaultOptions = { xmlMode: true };\n/**\n * Parse a feed.\n *\n * @param feed The feed that should be parsed, as a string.\n * @param options Optionally, options for parsing. When using this, you should set `xmlMode` to `true`.\n */\nfunction parseFeed(feed, options) {\n    if (options === void 0) { options = parseFeedDefaultOptions; }\n    return (0, domutils_1.getFeed)(parseDOM(feed, options));\n}\nexports.parseFeed = parseFeed;\nexports.DomUtils = __importStar(__webpack_require__(/*! domutils */ \"(ssr)/./node_modules/domutils/lib/index.js\"));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/htmlparser2/lib/index.js\n");

/***/ })

};
;