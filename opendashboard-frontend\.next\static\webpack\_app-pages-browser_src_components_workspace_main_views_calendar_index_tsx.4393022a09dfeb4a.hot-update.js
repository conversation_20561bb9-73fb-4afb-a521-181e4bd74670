/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// import React, { useMemo } from 'react';\n// import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameDay, isToday } from 'date-fns';\n// import { cn } from '@/lib/utils';\n// import { Button } from '@/components/ui/button';\n// import { PlusIcon } from '@heroicons/react/24/outline';\n// import { ScrollArea } from '@/components/ui/scroll-area';\n// import { CalendarEvent } from '@/typings/page';\n// import { useMaybeRecord } from '@/providers/record';\n// import { CalendarEventItem } from './CalendarEventItem';\n// import { NoEvents } from './NoEvents';\n// import { CalendarSideCard } from './CalendarSideCard';\n// import { useDroppable } from '@dnd-kit/core';\n// interface MonthViewProps {\n//   selectedDate: Date;\n//   events: CalendarEvent[];\n//   selectedEvent: string | null;\n//   setSelectedEvent: (id: string) => void;\n//   setSelectedDate: (date: Date) => void;\n//   openAddEventForm: (date: Date) => void;\n//   canEditData: boolean;\n//   handleEventClick: (event: CalendarEvent) => void;\n//   activeDragData: any;\n// }\n// const DayCell = ({\n//   date,\n//   children,\n//   onClick,\n//   isCurrentMonth\n// }: {\n//   date: Date;\n//   children: React.ReactNode;\n//   onClick: () => void;\n//   isCurrentMonth: boolean;\n// }) => {\n//   const { setNodeRef, isOver } = useDroppable({\n//     id: `daycell-${format(date, 'yyyy-MM-dd')}`,\n//     data: {\n//       date: date,\n//       type: 'daycell'\n//     }\n//   });\n//   return (\n//     <div\n//       ref={setNodeRef}\n//       onClick={onClick}\n//       className={cn(\n//         \"border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[120px] lg:p-3 lg:min-h-[140px]\",\n//         isCurrentMonth\n//           ? \"bg-white hover:bg-neutral-50\"\n//           : \"bg-neutral-100 hover:bg-neutral-200\",\n//         isOver && \"bg-blue-50 border-blue-200\"\n//       )}\n//     >\n//       {children}\n//     </div>\n//   );\n// };\n// // New helper function to process events for the entire month\n// const useMonthEvents = (weeks: Date[][], events: CalendarEvent[]) => {\n//   return useMemo(() => {\n//     const positionedEventsByWeek = new Map<number, any[]>();\n//     const allEventsByDay = new Map<string, any[]>();\n//     weeks.forEach((week, weekIndex) => {\n//       const weekStart = week[0];\n//       const weekEnd = week[6];\n//       const weekEvents = events.filter(event => {\n//         const eventStart = new Date(event.start);\n//         const eventEnd = new Date(event.end);\n//         return eventStart <= weekEnd && eventEnd >= weekStart;\n//       });\n//       const spanningEvents: any[] = [];\n//       weekEvents.forEach(event => {\n//         const eventStart = new Date(event.start);\n//         const eventEnd = new Date(event.end);\n//         const startDayIndex = week.findIndex(day => isSameDay(day, eventStart));\n//         const endDayIndex = week.findIndex(day => isSameDay(day, eventEnd));\n//         const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || (eventStart < weekStart && eventEnd > weekEnd);\n//         if (eventSpansWeek) {\n//           const start = startDayIndex !== -1 ? startDayIndex : 0;\n//           const end = endDayIndex !== -1 ? endDayIndex : 6;\n//           spanningEvents.push({\n//             event,\n//             startDayIndex: start,\n//             endDayIndex: end,\n//             colSpan: end - start + 1,\n//           });\n//         }\n//       });\n//       const positioned: any[] = [];\n//       const rows: any[][] = [];\n//       const sortedEvents = spanningEvents.sort((a, b) => a.startDayIndex - b.startDayIndex || b.colSpan - a.colSpan);\n//       sortedEvents.forEach(event => {\n//         let assigned = false;\n//         for (let i = 0; i < rows.length; i++) {\n//           const row = rows[i];\n//           if (!row.some(e => event.startDayIndex <= e.endDayIndex && event.endDayIndex >= e.startDayIndex)) {\n//             row.push(event);\n//             positioned.push({ ...event, row: i });\n//             assigned = true;\n//             break;\n//           }\n//         }\n//         if (!assigned) {\n//           rows.push([event]);\n//           positioned.push({ ...event, row: rows.length - 1 });\n//         }\n//       });\n//       positionedEventsByWeek.set(weekIndex, positioned);\n//       // Track all events that appear in each day cell\n//       week.forEach((day, dayIndex) => {\n//         const dayKey = format(day, 'yyyy-MM-dd');\n//         const dayEvents: any[] = [];\n//         positioned.forEach(pe => {\n//           // Include events that start on this day\n//           if (pe.startDayIndex === dayIndex) {\n//             dayEvents.push({\n//               ...pe,\n//               showTitle: true, // Show title on start day\n//               isDraggable: true // Draggable on start day\n//             });\n//           }\n//           // Include events that continue on this day (multi-day events)\n//           else if (pe.startDayIndex < dayIndex && pe.endDayIndex >= dayIndex) {\n//             // Check if this is the first day of a new week AND the event started in a previous week\n//             const isFirstDayOfWeek = dayIndex === 0;\n//             const eventStartedInPreviousWeek = pe.startDayIndex < 0; // Event started before this week\n//             dayEvents.push({\n//               ...pe,\n//               showTitle: isFirstDayOfWeek, // Show title only on first day of week\n//               isDraggable: false // Never draggable on continuation days\n//             });\n//           }\n//         });\n//         allEventsByDay.set(dayKey, dayEvents);\n//       });\n//     });\n//     return { positionedEventsByWeek, allEventsByDay };\n//   }, [weeks, events]);\n// };\n// export const MonthView: React.FC<MonthViewProps> = ({\n//   selectedDate,\n//   events,\n//   selectedEvent,\n//   setSelectedEvent,\n//   setSelectedDate,\n//   openAddEventForm,\n//   canEditData,\n//   handleEventClick,\n//   activeDragData,\n// }) => {\n//   const maybeRecord = useMaybeRecord();\n//   const isInRecordTab = !!maybeRecord;\n//   // Memoize month calculations\n//   const monthCalculations = useMemo(() => {\n//     const monthStart = startOfMonth(selectedDate);\n//     const monthEnd = endOfMonth(selectedDate);\n//     const startDay = startOfWeek(monthStart, { weekStartsOn: 0 });\n//     const endDay = endOfWeek(monthEnd, { weekStartsOn: 0 });\n//     const days = [];\n//     let day = startDay;\n//     while (day <= endDay) {\n//       days.push(day);\n//       day = addDays(day, 1);\n//     }\n//     const weeks = [];\n//     for (let i = 0; i < days.length; i += 7) {\n//       weeks.push(days.slice(i, i + 7));\n//     }\n//     return { monthStart, monthEnd, startDay, endDay, days, weeks };\n//   }, [selectedDate]);\n//   // Memoize month events\n//   const monthEvents = useMemo(() => \n//     events.filter(event => {\n//       const eventStart = new Date(event.start);\n//       return eventStart >= monthCalculations.startDay && \n//              eventStart <= monthCalculations.endDay;\n//     }), \n//     [events, monthCalculations.startDay, monthCalculations.endDay]\n//   );\n//   const { positionedEventsByWeek, allEventsByDay } = useMonthEvents(monthCalculations.weeks, events);\n//   // Render empty state when no events\n//   const renderEmptyState = () => (\n//     <div className=\"flex flex-col h-full bg-background\">\n//       <div className=\"grid grid-cols-7 border-b border-neutral-300 bg-white\">\n//         {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(dayName => (\n//           <div key={dayName} className={cn(\n//             \"text-center font-semibold text-black\",\n//             \"py-2 text-xs\"\n//           )}>\n//             {dayName.substring(0, 3)}\n//           </div>\n//         ))}\n//       </div>\n//       <NoEvents\n//         title=\"No events this month\"\n//         message={`${format(selectedDate, 'MMMM yyyy')} is completely free. Start planning your month!`}\n//         showCreateButton={canEditData}\n//         onCreate={() => openAddEventForm(selectedDate)}\n//       />\n//     </div>\n//   );\n//   // Render day cell content\n//   const renderDayCellContent = (day: Date, dayEvents: any[]) => {\n//     const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n//     const isCurrentDay = isToday(day);\n//     const MAX_VISIBLE_EVENTS = 4;\n//     const ROW_HEIGHT = 28;\n//     // Get all events for this specific day (including multi-day events that continue here)\n//     const dayKey = format(day, 'yyyy-MM-dd');\n//     const allDayEvents = allEventsByDay.get(dayKey) || [];\n//     // Sort events by row and limit to visible events\n//     const sortedEvents = allDayEvents.sort((a, b) => a.row - b.row);\n//     const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);\n//     const hasMore = sortedEvents.length > MAX_VISIBLE_EVENTS;\n//     // FIXED HEIGHT CALCULATION: Always use 5 rows (4 events + 1 \"+ more\" row)\n//     // This prevents cell expansion regardless of how many events there are\n//     const containerHeight = (MAX_VISIBLE_EVENTS + 1) * ROW_HEIGHT; // 5 * 28 = 140px\n//     return (\n//       <>\n//         <div className=\"flex items-center justify-between mb-2\">\n//           <span className={cn(\n//             \"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\",\n//             isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"\n//           )}>\n//             {format(day, 'd')}\n//           </span>\n//           {canEditData && isCurrentMonth && (\n//             <Button\n//               variant=\"ghost\"\n//               className=\"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\"\n//               onClick={(e) => {\n//                 e.stopPropagation();\n//                 setTimeout(() => openAddEventForm(day), 150);\n//               }}\n//             >\n//               <PlusIcon className=\"h-3 w-3 text-black\" />\n//             </Button>\n//           )}\n//         </div>\n//         <div className=\"relative\" style={{ height: `${containerHeight}px` }}>\n//           {visibleEvents.map(pe => (\n//             <div\n//               key={pe.event.id}\n//               className=\"absolute\"\n//               style={{\n//                 top: `${pe.row * ROW_HEIGHT}px`,\n//                 left: '2px',\n//                 width: pe.colSpan > 1 \n//                   ? `calc(${pe.colSpan * 100}% + ${(pe.colSpan - 1) * 19}px)`\n//                   : 'calc(100% - 4px)',\n//                 zIndex: 10 + pe.row,\n//               }}\n//             >\n//               <CalendarEventItem\n//                 event={pe.event}\n//                 view=\"month\"\n//                 onClick={(e) => {\n//                   e.stopPropagation();\n//                   setSelectedEvent(pe.event.id);\n//                   handleEventClick(pe.event);\n//                 }}\n//                 isDragging={activeDragData?.payload?.id === pe.event.id}\n//                 showTitle={pe.showTitle}\n//                 isDraggable={pe.isDraggable}\n//               />\n//             </div>\n//           ))}\n//           {hasMore && (\n//             <div \n//               className=\"text-black hover:text-black font-medium text-xs cursor-pointer\"\n//               style={{\n//                 position: 'absolute',\n//                 top: `${MAX_VISIBLE_EVENTS * ROW_HEIGHT}px`,\n//                 left: '2px',\n//               }}\n//               onClick={(e) => {\n//                 e.stopPropagation();\n//                 setSelectedDate(day);\n//               }}\n//             >\n//               + {sortedEvents.length - MAX_VISIBLE_EVENTS} more\n//             </div>\n//           )}\n//         </div>\n//       </>\n//     );\n//   };\n//   // Render main view\n//   return (\n//     <div className=\"h-full bg-background flex flex-col lg:flex-row\">\n//       <div className=\"flex-1 flex flex-col min-h-0\">\n//         {/* Day Headers */}\n//         <div className=\"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\">\n//           {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(dayName => (\n//             <div key={dayName} className={cn(\n//               \"text-center font-semibold text-black\",\n//               \"py-2 text-xs\"\n//             )}>\n//               {dayName.substring(0, 3)}\n//             </div>\n//           ))}\n//         </div>\n//         {/* Month Grid */}\n//         <ScrollArea className=\"flex-1\">\n//           <div className=\"grid grid-cols-7 border-neutral-300 border-b\">\n//             {monthCalculations.weeks.map((week, weekIndex) =>\n//               week.map((day, dayIndex) => {\n//                 const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n//                 return (\n//                   <DayCell\n//                     key={`${weekIndex}-${dayIndex}`}\n//                     date={day}\n//                     isCurrentMonth={isCurrentMonth}\n//                     onClick={() => setSelectedDate(day)}\n//                   >\n//                     {renderDayCellContent(day, [])}\n//                   </DayCell>\n//                 );\n//               }),\n//             )}\n//           </div>\n//         </ScrollArea>\n//       </div>\n//       <CalendarSideCard\n//         selectedDate={selectedDate}\n//         events={events}\n//         selectedEvent={selectedEvent}\n//         setSelectedEvent={setSelectedEvent}\n//         handleEventClick={handleEventClick}\n//       />\n//     </div>\n//   );\n// };\n// function isMultiDay(event: CalendarEvent): boolean {\n//   return !isSameDay(new Date(event.start), new Date(event.end));\n// }\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ })

});