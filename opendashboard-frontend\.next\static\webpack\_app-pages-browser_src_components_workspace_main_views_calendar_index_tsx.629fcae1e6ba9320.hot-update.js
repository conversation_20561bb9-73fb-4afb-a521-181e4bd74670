"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx":
/*!******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/WeekView.tsx ***!
  \******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WeekView: function() { return /* binding */ WeekView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/setHours/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _AllDayRow__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AllDayRow */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/AllDayRow.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/multiDayEventUtils */ \"(app-pages-browser)/./src/utils/multiDayEventUtils.ts\");\n/* harmony import */ var _utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/eventCollisionUtils */ \"(app-pages-browser)/./src/utils/eventCollisionUtils.ts\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst TimeSlot = (param)=>{\n    let { day, hour, children, onDoubleClick } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable)({\n        id: \"timeslot-week-\".concat((0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"yyyy-MM-dd\"), \"-\").concat(hour),\n        data: {\n            date: day,\n            hour: hour,\n            type: \"timeslot-week\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        className: \"flex-1 border-r border-neutral-300 last:border-r-0 relative min-h-[60px] cursor-pointer\",\n        onDoubleClick: onDoubleClick,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TimeSlot, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable\n    ];\n});\n_c = TimeSlot;\nconst WeekView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, savedScrollTop, handleEventClick, activeDragData } = param;\n    _s1();\n    // Memoize week-related calculations\n    const weekCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const weekStart = (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        });\n        const weekEnd = (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        });\n        const days = Array.from({\n            length: 7\n        }, (_, i)=>(0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(weekStart, i));\n        const todayIndex = days.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(day));\n        return {\n            weekStart,\n            weekEnd,\n            days,\n            todayIndex\n        };\n    }, [\n        selectedDate\n    ]);\n    const { days, todayIndex } = weekCalculations;\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>i);\n    // Memoize week segments\n    const weekSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const allSegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.eventsToSegments)(events);\n        return (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentsForWeek)(allSegments, weekCalculations.weekStart, weekCalculations.weekEnd);\n    }, [\n        events,\n        weekCalculations.weekStart,\n        weekCalculations.weekEnd\n    ]);\n    // Separate all-day and time-slot segments\n    const allDaySegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getAllDaySegments)(weekSegments), [\n        weekSegments\n    ]);\n    const timeSlotSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getTimeSlotSegments)(weekSegments), [\n        weekSegments\n    ]);\n    // Memoize current time position\n    const currentTimePosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>todayIndex !== -1 ? {\n            dayIndex: todayIndex,\n            hour: new Date().getHours(),\n            minutes: new Date().getMinutes()\n        } : null, [\n        todayIndex\n    ]);\n    // Helper to get event duration in minutes\n    const getEventDurationInMinutes = (event)=>{\n        const start = new Date(event.start);\n        const end = new Date(event.end);\n        return Math.max(20, (end.getTime() - start.getTime()) / (1000 * 60));\n    };\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_5__.NoEvents, {\n            title: \"No events this week\",\n            message: \"Your week is completely free. Add some events to get organized!\",\n            showCreateButton: canEditData,\n            onCreate: ()=>openAddEventForm(selectedDate)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n            lineNumber: 129,\n            columnNumber: 5\n        }, undefined);\n    // Render time slots with events\n    const renderTimeSlots = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 relative bg-white border-b border-neutral-300 overflow-y-auto lg:overflow-auto\",\n            id: \"week-view-container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-x-auto lg:overflow-x-visible\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col min-w-[700px] lg:min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: hours.map((hour, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors\", i === hours.length - 1 && \"border-b-neutral-300\"),\n                                    style: {\n                                        height: \"60px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            \"data-time-labels\": \"true\",\n                                            className: \"sticky left-0 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-200 bg-white z-20 w-14 lg:w-20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs font-semibold\",\n                                                    children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(new Date(), hour), \"h a\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        days.map((day)=>{\n                                            const daySegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentsForDay)(timeSlotSegments, day);\n                                            const { segmentLayouts } = (0,_utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_7__.calculateLayout)(daySegments);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeSlot, {\n                                                day: day,\n                                                hour: hour,\n                                                onDoubleClick: ()=>{\n                                                    if (canEditData) {\n                                                        const newDate = (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(day, hour);\n                                                        openAddEventForm(newDate);\n                                                    }\n                                                },\n                                                children: segmentLayouts.map((layout)=>{\n                                                    var _activeDragData_payload, _activeDragData_payload1;\n                                                    const segmentStart = layout.segment.startTime;\n                                                    const isFirstHour = segmentStart.getHours() === hour;\n                                                    if (!isFirstHour) return null;\n                                                    const segmentHeight = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentHeight)(layout.segment);\n                                                    const topOffset = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentTopOffset)(layout.segment);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                                        segment: layout.segment,\n                                                        style: {\n                                                            height: \"\".concat(segmentHeight, \"px\"),\n                                                            position: \"absolute\",\n                                                            top: \"\".concat(topOffset, \"px\"),\n                                                            left: \"\".concat(layout.left, \"%\"),\n                                                            width: \"\".concat(layout.width, \"%\"),\n                                                            zIndex: selectedEvent === layout.segment.originalEventId ? 20 : layout.zIndex,\n                                                            paddingRight: \"2px\",\n                                                            border: layout.hasOverlap ? \"1px solid white\" : \"none\"\n                                                        },\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            const container = document.getElementById(\"week-view-container\");\n                                                            if (container) {\n                                                                savedScrollTop.current = container.scrollTop;\n                                                            }\n                                                            setSelectedEvent(layout.segment.originalEventId);\n                                                            handleEventClick(layout.segment.originalEvent);\n                                                        },\n                                                        view: \"week\",\n                                                        isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : activeDragData.type) === \"segment\" ? (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === layout.segment.id : (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload1 = activeDragData.payload) === null || _activeDragData_payload1 === void 0 ? void 0 : _activeDragData_payload1.id) === layout.segment.originalEvent.id\n                                                    }, layout.segment.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 27\n                                                    }, undefined);\n                                                })\n                                            }, \"\".concat(day.toISOString(), \"-\").concat(hour), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    ]\n                                }, hour, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, undefined),\n                        currentTimePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 bottom-0 left-14 lg:left-20 right-0 pointer-events-none z-30\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-full w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute flex items-center\",\n                                    style: {\n                                        top: \"\".concat((currentTimePosition.hour + currentTimePosition.minutes / 60) * 60, \"px\"),\n                                        left: \"\".concat(currentTimePosition.dayIndex / 7 * 100, \"%\"),\n                                        width: \"\".concat(1 / 7 * 100, \"%\")\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 border-t-2 border-red-500 shadow-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n            lineNumber: 139,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-day-headers\": \"true\",\n                className: \"border-b border-neutral-300 bg-white sticky top-0 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex overflow-x-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sticky left-0 bg-white z-10 w-14 lg:w-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-1 min-w-[calc(100vw-3.5rem)] lg:min-w-0\",\n                            children: days.map((day, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 text-center cursor-pointer py-3 px-0 lg:py-4\",\n                                    onClick: ()=>setSelectedDate(day),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold text-black mb-1\", \"text-xs\"),\n                                            children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"EEE\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full\", (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(day) ? \"bg-black text-white\" : \"text-black hover:bg-neutral-100\"),\n                                            children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"d\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 255,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AllDayRow__WEBPACK_IMPORTED_MODULE_4__.AllDayRow, {\n                selectedDate: selectedDate,\n                segments: allDaySegments,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick,\n                canEditData: canEditData,\n                openAddEventForm: openAddEventForm,\n                view: \"week\",\n                activeDragData: activeDragData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, undefined),\n            weekSegments.length === 0 ? renderEmptyState() : renderTimeSlots()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 253,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(WeekView, \"OfgQ1j/ZHWAQ0Q+mdykk0ntfinw=\");\n_c1 = WeekView;\nvar _c, _c1;\n$RefreshReg$(_c, \"TimeSlot\");\n$RefreshReg$(_c1, \"WeekView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\n"));

/***/ })

});