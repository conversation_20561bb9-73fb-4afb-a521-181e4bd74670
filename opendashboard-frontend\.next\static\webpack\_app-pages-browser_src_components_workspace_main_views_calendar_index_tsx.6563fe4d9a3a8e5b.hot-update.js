"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx":
/*!****************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/index.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarView: function() { return /* binding */ CalendarView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _providers_template__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/template */ \"(app-pages-browser)/./src/providers/template.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/workspace/main/views/table */ \"(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/providers/stackedPeek */ \"(app-pages-browser)/./src/providers/stackedPeek.tsx\");\n/* harmony import */ var _components_DayView__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./components/DayView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx\");\n/* harmony import */ var _components_WeekView__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./components/WeekView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\");\n/* harmony import */ var _components_MonthView__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./components/MonthView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\");\n/* harmony import */ var _components_CalendarEventItem__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./components/CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/custom-ui/customSelect */ \"(app-pages-browser)/./src/components/custom-ui/customSelect.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./components/CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInMilliseconds/index.js\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInDays/index.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Custom modifier to restrict dragging to the calendar main content area only\nconst restrictToCalendarContainer = (param)=>{\n    let { transform, draggingNodeRect, windowRect } = param;\n    if (!draggingNodeRect || !windowRect) {\n        return transform;\n    }\n    // Find the calendar main content container (the div that contains the views)\n    const calendarContainer = document.querySelector('[data-calendar-content=\"true\"]');\n    if (!calendarContainer) {\n        return transform;\n    }\n    const containerRect = calendarContainer.getBoundingClientRect();\n    // For month view, we need to account for the side card\n    // The side card is positioned on the right side of the calendar\n    const sideCard = document.querySelector('[data-side-card=\"true\"]');\n    let maxX = containerRect.right - draggingNodeRect.width;\n    if (sideCard) {\n        const sideCardRect = sideCard.getBoundingClientRect();\n        // Restrict dragging to not go past the side card\n        maxX = Math.min(maxX, sideCardRect.left - draggingNodeRect.width);\n    }\n    // Find header areas to prevent dragging over them\n    const timeLabels = document.querySelector('[data-time-labels=\"true\"]');\n    const dayHeaders = document.querySelector('[data-day-headers=\"true\"]');\n    const allDayRow = document.querySelector('[data-all-day-row=\"true\"]');\n    // Calculate the boundaries relative to the window\n    let minX = containerRect.left;\n    let minY = containerRect.top;\n    const maxY = containerRect.bottom - draggingNodeRect.height;\n    // Prevent dragging over time labels (left side in day/week view)\n    if (timeLabels) {\n        const timeLabelsRect = timeLabels.getBoundingClientRect();\n        minX = Math.max(minX, timeLabelsRect.right);\n    }\n    // Prevent dragging over day headers (top of calendar)\n    if (dayHeaders) {\n        const dayHeadersRect = dayHeaders.getBoundingClientRect();\n        minY = Math.max(minY, dayHeadersRect.bottom);\n    }\n    // Prevent dragging over all-day row (if present)\n    if (allDayRow) {\n        const allDayRowRect = allDayRow.getBoundingClientRect();\n        minY = Math.max(minY, allDayRowRect.bottom);\n    }\n    // Get current pointer position\n    const currentX = transform.x + draggingNodeRect.left;\n    const currentY = transform.y + draggingNodeRect.top;\n    // Constrain the position\n    const constrainedX = Math.min(Math.max(currentX, minX), maxX);\n    const constrainedY = Math.min(Math.max(currentY, minY), maxY);\n    return {\n        ...transform,\n        x: constrainedX - draggingNodeRect.left,\n        y: constrainedY - draggingNodeRect.top\n    };\n};\n// Custom hook to track previous value\nconst usePrevious = (value)=>{\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        ref.current = value;\n    });\n    return ref.current;\n};\n_s(usePrevious, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nconst CalendarView = (props)=>{\n    _s1();\n    const { databaseStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace)();\n    const { definition } = props;\n    const { accessLevel } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage)();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_16__.useScreenSize)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_17__.useMaybeRecord)();\n    const maybeShared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared)();\n    const maybeTemplate = (0,_providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate)();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [viewType, setViewType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"week\");\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSideCalendar, setShowSideCalendar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!isMobile);\n    const [activeDragData, setActiveDragData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const savedScrollTop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const pointerCoordinates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const isInRecordTab = !!maybeRecord;\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const databaseId = definition.databaseId;\n    const database = databaseStore[definition.databaseId];\n    const isPublishedView = !!maybeShared;\n    const editable = !definition.lockContent && !isPublishedView && !!accessLevel;\n    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    const { createRecords, updateRecordValues, setPeekRecordId, peekRecordId, refreshDatabase, deleteRecords } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews)();\n    const { sorts, filter, search } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering)();\n    const { selectedIds, setSelectedIds } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewSelection)();\n    const prevPeekRecordId = usePrevious(peekRecordId);\n    const { openRecord } = (0,_providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__.useStackedPeek)();\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.PointerSensor, {\n        activationConstraint: {\n            distance: 8\n        }\n    }), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.TouchSensor, {\n        activationConstraint: {\n            delay: 150,\n            tolerance: 5\n        }\n    }));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n        const container = document.getElementById(containerId);\n        if (container) {\n            requestAnimationFrame(()=>{\n                container.scrollTop = savedScrollTop.current;\n            });\n        }\n    }, [\n        selectedEvent,\n        viewType\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowSideCalendar(!isMobile);\n    }, [\n        isMobile\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only refresh if the peek view was open and is now closed.\n        if (prevPeekRecordId && !peekRecordId) {\n            console.log(\"Peek view was closed, refreshing calendar data\");\n            refreshDatabase(definition.databaseId);\n        }\n    }, [\n        peekRecordId,\n        prevPeekRecordId,\n        definition.databaseId,\n        refreshDatabase\n    ]);\n    if (!database) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__.PageLoader, {\n        size: \"full\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 208,\n        columnNumber: 25\n    }, undefined);\n    const getEvents = ()=>{\n        var _workspace_workspaceMember, _database_database;\n        if (!database) return [];\n        const { rows } = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.filterAndSortRecords)(database, members, databaseStore, definition.filter || {\n            match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All,\n            conditions: []\n        }, filter, sorts.length ? sorts : definition.sorts || [], (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspaceMember = workspace.workspaceMember) === null || _workspace_workspaceMember === void 0 ? void 0 : _workspace_workspaceMember.userId) || \"\", (database === null || database === void 0 ? void 0 : (_database_database = database.database) === null || _database_database === void 0 ? void 0 : _database_database.id) || \"\");\n        const filteredRows = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.searchFilteredRecords)(search || \"\", rows);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        return filteredRows.map((row)=>{\n            const startValue = row.processedRecord.processedRecordValues[definition.eventStartColumnId];\n            let startDate;\n            if (startValue && typeof startValue === \"string\") {\n                startDate = new Date(startValue);\n            } else {\n                startDate = new Date();\n            }\n            let endDate;\n            if (definition.eventEndColumnId) {\n                const endValue = row.processedRecord.processedRecordValues[definition.eventEndColumnId];\n                if (endValue && typeof endValue === \"string\") {\n                    endDate = new Date(endValue);\n                } else {\n                    endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n                }\n            } else {\n                endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n            }\n            const title = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getRecordTitle)(row.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts);\n            return {\n                id: row.id,\n                title,\n                start: startDate,\n                end: endDate,\n                record: row.record,\n                processedRecord: row.processedRecord\n            };\n        });\n    };\n    const getFilteredEvents = ()=>{\n        const baseEvents = getEvents();\n        if (!searchTerm.trim()) {\n            return baseEvents;\n        }\n        return baseEvents.filter((event)=>{\n            const searchLower = searchTerm.toLowerCase();\n            return event.title.toLowerCase().includes(searchLower);\n        });\n    };\n    const onDragStart = (event)=>{\n        if (event.active.data.current) {\n            var _event_active_rect_current_translated, _event_active_rect_current, _event_active_rect_current_translated1, _event_active_rect_current_translated2;\n            const initialPointerY = pointerCoordinates.current.y;\n            var _event_active_rect_current_translated_top;\n            const initialEventTop = (_event_active_rect_current_translated_top = (_event_active_rect_current = event.active.rect.current) === null || _event_active_rect_current === void 0 ? void 0 : (_event_active_rect_current_translated = _event_active_rect_current.translated) === null || _event_active_rect_current_translated === void 0 ? void 0 : _event_active_rect_current_translated.top) !== null && _event_active_rect_current_translated_top !== void 0 ? _event_active_rect_current_translated_top : 0;\n            const grabOffsetY = initialPointerY - initialEventTop;\n            // Get the exact dimensions from the DOM element\n            // Handle both event and segment types\n            const { payload, type } = event.active.data.current;\n            const eventId = type === \"segment\" ? payload.originalEvent.id : payload.id;\n            const draggedElement = document.getElementById(\"event-\".concat(eventId));\n            const width = draggedElement ? draggedElement.offsetWidth : (_event_active_rect_current_translated1 = event.active.rect.current.translated) === null || _event_active_rect_current_translated1 === void 0 ? void 0 : _event_active_rect_current_translated1.width;\n            const height = draggedElement ? draggedElement.offsetHeight : (_event_active_rect_current_translated2 = event.active.rect.current.translated) === null || _event_active_rect_current_translated2 === void 0 ? void 0 : _event_active_rect_current_translated2.height;\n            setActiveDragData({\n                ...event.active.data.current,\n                grabOffsetY,\n                width,\n                height\n            });\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"touchmove\", handleTouchMove);\n        }\n    };\n    const onDragEnd = async (param)=>{\n        let { active, over } = param;\n        var _active_data, _over_data;\n        document.removeEventListener(\"mousemove\", handleMouseMove);\n        document.removeEventListener(\"touchmove\", handleTouchMove);\n        setActiveDragData(null);\n        console.log(\"[DragEnd] Debug Info:\", {\n            activeId: active === null || active === void 0 ? void 0 : active.id,\n            overId: over === null || over === void 0 ? void 0 : over.id,\n            activeData: active === null || active === void 0 ? void 0 : (_active_data = active.data) === null || _active_data === void 0 ? void 0 : _active_data.current,\n            overData: over === null || over === void 0 ? void 0 : (_over_data = over.data) === null || _over_data === void 0 ? void 0 : _over_data.current,\n            canEditData\n        });\n        if (!over || !active || !canEditData || active.id === over.id) {\n            console.log(\"[DragEnd] Early exit - invalid conditions\");\n            return;\n        }\n        const activeData = active.data.current;\n        const overData = over.data.current;\n        if (!activeData || !overData) {\n            console.log(\"[DragEnd] Early exit - missing data\");\n            return;\n        }\n        // Check if we're dropping on another event (which should be ignored)\n        if (overData.type === \"event\" || overData.type === \"segment\") {\n            console.log(\"[DragEnd] Dropping on another event - ignoring\");\n            return;\n        }\n        const { payload, type } = activeData;\n        const eventToUpdate = type === \"segment\" ? payload.originalEvent : payload;\n        // Ensure we're updating the correct event\n        if (!eventToUpdate || !eventToUpdate.id) {\n            console.log(\"[DragEnd] Invalid event to update\");\n            return;\n        }\n        console.log(\"[DragEnd] Event to update:\", eventToUpdate.id, eventToUpdate.title);\n        const originalStart = new Date(eventToUpdate.start);\n        const originalEnd = new Date(eventToUpdate.end);\n        const duration = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_27__[\"default\"])(originalEnd, originalStart);\n        let newStart;\n        if (overData.type.startsWith(\"allday\")) {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(originalStart, dayDifference);\n            // For all-day drops, normalize the time to the start of the day\n            newStart.setHours(0, 0, 0, 0);\n        } else if (overData.type.startsWith(\"timeslot\")) {\n            newStart = new Date(overData.date);\n            // Enhanced Minute-level precision calculation\n            const grabOffsetY = activeData.grabOffsetY || 0;\n            const slotTop = over.rect.top;\n            const pointerY = pointerCoordinates.current.y;\n            // Adjust pointer position based on initial grab point\n            const adjustedPointerY = pointerY - grabOffsetY;\n            const offsetY = adjustedPointerY - slotTop;\n            const slotHeight = over.rect.height; // Should be 60px\n            const minutesFromTop = offsetY / slotHeight * 60;\n            // Snap to nearest 5-minute interval\n            const snappedMinutes = Math.round(minutesFromTop / 5) * 5;\n            const newMinutes = Math.max(0, Math.min(59, snappedMinutes));\n            newStart.setHours(overData.hour, newMinutes, originalStart.getSeconds(), originalStart.getMilliseconds());\n        } else if (overData.type === \"daycell\") {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(originalStart, dayDifference);\n        } else {\n            return; // Invalid drop target\n        }\n        const newEnd = new Date(newStart.getTime() + duration);\n        const recordId = eventToUpdate.record.id;\n        const newValues = {\n            [definition.eventStartColumnId]: newStart.toISOString(),\n            ...definition.eventEndColumnId && {\n                [definition.eventEndColumnId]: newEnd.toISOString()\n            }\n        };\n        try {\n            await updateRecordValues(definition.databaseId, [\n                recordId\n            ], newValues);\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.success('Event \"'.concat(eventToUpdate.title, '\" updated.'));\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to update event.\");\n            console.error(\"Error updating event:\", error);\n        }\n    };\n    const handleMouseMove = (event)=>{\n        pointerCoordinates.current = {\n            x: event.clientX,\n            y: event.clientY\n        };\n    };\n    const handleTouchMove = (event)=>{\n        const touch = event.touches[0];\n        pointerCoordinates.current = {\n            x: touch.clientX,\n            y: touch.clientY\n        };\n    };\n    const events = getFilteredEvents();\n    const goToToday = ()=>setSelectedDate(new Date());\n    const goToPrevious = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(prevDate, -1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const goToNext = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(prevDate, 1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const handleRequestCreateEvent = function(date) {\n        let useSystemTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (useSystemTime) {\n            const now = new Date();\n            const newDate = new Date(date);\n            newDate.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());\n            handleCreateEvent(newDate);\n        } else {\n            handleCreateEvent(date);\n        }\n    };\n    const handleCreateEvent = async function() {\n        let date = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : new Date();\n        if (!canEditData) return;\n        const startTime = new Date(date);\n        const endTime = new Date(startTime.getTime() + (definition.defaultDuration || 30) * 60000);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        try {\n            const recordValues = {\n                [definition.eventStartColumnId]: startTime.toISOString(),\n                ...definition.eventEndColumnId && {\n                    [definition.eventEndColumnId]: endTime.toISOString()\n                }\n            };\n            if (titleColOpts.titleColId) {\n                recordValues[titleColOpts.titleColId] = \"New Event\";\n            }\n            const result = await createRecords(definition.databaseId, [\n                recordValues\n            ]);\n            if (result && result.records && result.records.length > 0) {\n                const newRecordId = result.records[0].id;\n                if (newRecordId) {\n                    await refreshDatabase(definition.databaseId);\n                    setPeekRecordId(newRecordId);\n                    sonner__WEBPACK_IMPORTED_MODULE_14__.toast.success(\"New event created\");\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Error accessing the new event\");\n                }\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to create event properly\");\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to create event\");\n        }\n    };\n    const handleEventClick = (event)=>{\n        if (event && event.id) {\n            const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n            const container = document.getElementById(containerId);\n            if (container) {\n                savedScrollTop.current = container.scrollTop;\n            }\n            openRecord(event.id, event.record.databaseId);\n            setSelectedEvent(event.id);\n        }\n    };\n    const getHeaderDateDisplay = ()=>{\n        switch(viewType){\n            case \"day\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n            case \"week\":\n                return \"\".concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(selectedDate, -selectedDate.getDay()), \"MMM d\"), \" - \").concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(selectedDate, 6 - selectedDate.getDay()), \"MMM d, yyyy\"));\n            case \"month\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM yyyy\");\n            default:\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n        }\n    };\n    const handleEventDelete = async (event)=>{\n        if (!canEditData) return;\n        try {\n            await deleteRecords(database.database.id, [\n                event.record.id\n            ]);\n        } catch (error) {\n            console.error(\"Error deleting event:\", error);\n        }\n    };\n    const viewTypeOptions = [\n        {\n            id: \"day\",\n            value: \"day\",\n            title: \"Day\",\n            data: \"day\"\n        },\n        {\n            id: \"week\",\n            value: \"week\",\n            title: \"Week\",\n            data: \"week\"\n        },\n        {\n            id: \"month\",\n            value: \"month\",\n            title: \"Month\",\n            data: \"month\"\n        }\n    ];\n    const selectedViewOption = viewType === \"day\" ? [\n        \"day\"\n    ] : viewType === \"week\" ? [\n        \"week\"\n    ] : [\n        \"month\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"border-b border-neutral-300 bg-white\", isInRecordTab && \"py-1\"),\n                children: [\n                    isMobile ? /* Mobile Header Layout */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"p-2\", isInRecordTab && \"py-1\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black truncate flex-1 mr-2\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(!showSideCalendar),\n                                        className: \"rounded-full h-8 px-3 text-xs text-black hover:bg-gray-50\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 544,\n                                columnNumber: 12\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToToday,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: \"Today\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToPrevious,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleLeftIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 18\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToNext,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleRightIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 18\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 569,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 558,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                                options: viewTypeOptions,\n                                                selectedIds: selectedViewOption,\n                                                onChange: (selected)=>{\n                                                    if (selected.length > 0) {\n                                                        setViewType(selected[0]);\n                                                    }\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black w-20\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                placeholder: \"View\",\n                                                hideSearch: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 595,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 20\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 18\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 557,\n                                columnNumber: 12\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 543,\n                        columnNumber: 10\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"flex items-center justify-between px-4\", isInRecordTab ? \"py-1\" : \"py-2\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: goToToday,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToPrevious,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleLeftIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToNext,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleRightIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 660,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 14\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 630,\n                                columnNumber: 10\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                placeholder: \"Search events...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-48 pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                                className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 678,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 671,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                        options: viewTypeOptions,\n                                        selectedIds: selectedViewOption,\n                                        onChange: (selected)=>{\n                                            if (selected.length > 0) {\n                                                setViewType(selected[0]);\n                                            }\n                                        },\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6 w-20\" : \"h-8 w-28\"),\n                                        placeholder: \"View\",\n                                        hideSearch: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black gap-1\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 706,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Add Event\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 707,\n                                                columnNumber: 35\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 669,\n                                columnNumber: 10\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 626,\n                        columnNumber: 8\n                    }, undefined),\n                    isMobile && !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                    placeholder: \"Search events...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"w-full pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 717,\n                                    columnNumber: 12\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                    className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 723,\n                                    columnNumber: 12\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                            lineNumber: 716,\n                            columnNumber: 10\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 715,\n                        columnNumber: 8\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 537,\n                columnNumber: 6\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex min-h-0\",\n                children: [\n                    showSideCalendar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"flex-none bg-white\", isMobile ? \"w-full absolute z-50 backdrop-blur-sm h-full shadow-lg\" : \"w-fit border-r border-neutral-300\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 border-b border-neutral-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 737,\n                                        columnNumber: 10\n                                    }, undefined),\n                                    isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(false),\n                                        className: \"rounded-full h-8 w-8 p-1 text-black hover:bg-neutral-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Close\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 744,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            \"\\xd7\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 739,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 736,\n                                columnNumber: 8\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                mode: \"single\",\n                                selected: selectedDate,\n                                onSelect: (date)=>{\n                                    if (date) {\n                                        setSelectedDate(date);\n                                        if (isMobile) {\n                                            setShowSideCalendar(false);\n                                        }\n                                    }\n                                },\n                                className: \"rounded-md border-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 749,\n                                columnNumber: 8\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 732,\n                        columnNumber: 6\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DndContext, {\n                        sensors: sensors,\n                        onDragStart: onDragStart,\n                        onDragEnd: onDragEnd,\n                        modifiers: [\n                            restrictToCalendarContainer\n                        ],\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                \"data-calendar-content\": \"true\",\n                                children: [\n                                    viewType === \"day\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DayView__WEBPACK_IMPORTED_MODULE_19__.DayView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 773,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"week\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WeekView__WEBPACK_IMPORTED_MODULE_20__.WeekView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 786,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"month\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MonthView__WEBPACK_IMPORTED_MODULE_21__.MonthView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: (date)=>handleRequestCreateEvent(date, true),\n                                        canEditData: canEditData,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 800,\n                                        columnNumber: 7\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 771,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DragOverlay, {\n                                dropAnimation: null,\n                                children: activeDragData && activeDragData.type === \"segment\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_26__.CalendarEventSegment, {\n                                    segment: activeDragData.payload,\n                                    view: viewType === \"day\" ? \"day\" : \"week\",\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height,\n                                        position: \"relative\",\n                                        transform: \"none\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 816,\n                                    columnNumber: 11\n                                }, undefined) : activeDragData && activeDragData.type === \"event\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CalendarEventItem__WEBPACK_IMPORTED_MODULE_22__.CalendarEventItem, {\n                                    event: activeDragData.payload,\n                                    view: viewType,\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height,\n                                        position: \"relative\",\n                                        transform: \"none\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 828,\n                                    columnNumber: 11\n                                }, undefined) : null\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 814,\n                                columnNumber: 6\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 765,\n                        columnNumber: 6\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 730,\n                columnNumber: 4\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 536,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CalendarView, \"gC+3ORgbOSOWj17lR2zBDE1vWrs=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace,\n        _providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage,\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_16__.useScreenSize,\n        _providers_record__WEBPACK_IMPORTED_MODULE_17__.useMaybeRecord,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared,\n        _providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewSelection,\n        usePrevious,\n        _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__.useStackedPeek,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors\n    ];\n});\n_c = CalendarView;\nvar _c;\n$RefreshReg$(_c, \"CalendarView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx\n"));

/***/ })

});