import React, { useState, useRef, useEffect, useCallback } from "react";
import { useWorkspace } from "@/providers/workspace";
import { Match, ProcessedDbRecord } from "opendb-app-db-utils/lib/typings/db";
import { PageLoader } from "@/components/custom-ui/loader";
import { usePage } from "@/providers/page";
import { CalendarViewDefinition, ViewDefinition } from "opendb-app-db-utils/lib/typings/view";
import { Record } from "@/typings/database";
import { View } from "@/typings/page";
import { useMaybeShared } from "@/providers/shared";
import { useMaybeTemplate } from "@/providers/template";
import { useViews, useViewFiltering, useViewSelection } from "@/providers/views";
import { filterAndSortRecords, searchFilteredRecords } from "@/components/workspace/main/views/table";
import { Calendar } from "@/components/ui/calendar";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { AngleLeftIcon, AngleRightIcon, PlusIcon, MagnifyingGlassIcon } from "@/components/icons/FontAwesomeRegular";
import { format, addDays, addMonths, subMonths, addWeeks, subWeeks, addHours, setHours, setMinutes, setSeconds, setMilliseconds, startOfDay } from "date-fns";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { useAlert } from "@/providers/alert";
import { useScreenSize } from "@/providers/screenSize";
import { useMaybeRecord } from "@/providers/record";
import { useStackedPeek } from "@/providers/stackedPeek";


import { DayView } from "./components/DayView";
import { WeekView } from "./components/WeekView";
import { MonthView } from "./components/MonthView";
import { CalendarEventItem } from "./components/CalendarEventItem";
import { getDatabaseTitleCol, getRecordTitle } from '@/components/workspace/main/views/form/components/element/linked';
import { CalendarViewRenderProps, CalendarEvent, CalendarViewType } from '@/typings/page';
import { Sheet, SheetContent } from '@/components/ui/sheet';
import { CalendarIcon } from '@heroicons/react/24/outline';
import { CustomSelect } from '@/components/custom-ui/customSelect';
import { TagItem } from '@/components/workspace/main/views/table/renderer/common/tag';
import {
  DndContext,
  DragOverlay,
  PointerSensor,
  TouchSensor,
  useSensor,
  useSensors,
  Active,
  Over,
  closestCenter,
  pointerWithin,
  rectIntersection
} from '@dnd-kit/core';
import { restrictToWindowEdges } from '@dnd-kit/modifiers';
import { EventSegment } from '@/utils/multiDayEventUtils';
import { CalendarEventSegment } from './components/CalendarEventSegment';
import { differenceInDays, differenceInMilliseconds } from 'date-fns';

// Custom modifier to restrict dragging to the calendar main content area only
const restrictToCalendarContainer = ({ transform, draggingNodeRect, windowRect }: any) => {
  if (!draggingNodeRect || !windowRect) {
    return transform;
  }

  // Find the calendar main content container (the div that contains the views)
  const calendarContainer = document.querySelector('[data-calendar-content="true"]');
  if (!calendarContainer) {
    return transform;
  }

  const containerRect = calendarContainer.getBoundingClientRect();
  
  // For month view, we need to account for the side card
  // The side card is positioned on the right side of the calendar
  const sideCard = document.querySelector('[data-side-card="true"]');
  let maxX = containerRect.right - draggingNodeRect.width;
  
  if (sideCard) {
    const sideCardRect = sideCard.getBoundingClientRect();
    // Restrict dragging to not go past the side card
    maxX = Math.min(maxX, sideCardRect.left - draggingNodeRect.width);
  }
  
  // Find header areas to prevent dragging over them
  const timeLabels = document.querySelector('[data-time-labels="true"]');
  const dayHeaders = document.querySelector('[data-day-headers="true"]');
  const allDayRow = document.querySelector('[data-all-day-row="true"]');
  
  // Calculate the boundaries relative to the window
  let minX = containerRect.left;
  let minY = containerRect.top;
  const maxY = containerRect.bottom - draggingNodeRect.height;
  
  // Prevent dragging over time labels (left side in day/week view)
  if (timeLabels) {
    const timeLabelsRect = timeLabels.getBoundingClientRect();
    minX = Math.max(minX, timeLabelsRect.right);
  }
  
  // Prevent dragging over day headers (top of calendar)
  if (dayHeaders) {
    const dayHeadersRect = dayHeaders.getBoundingClientRect();
    minY = Math.max(minY, dayHeadersRect.bottom);
  }
  
  // Prevent dragging over all-day row (if present)
  if (allDayRow) {
    const allDayRowRect = allDayRow.getBoundingClientRect();
    minY = Math.max(minY, allDayRowRect.bottom);
  }

  // Get current pointer position
  const currentX = transform.x + draggingNodeRect.left;
  const currentY = transform.y + draggingNodeRect.top;

  // Constrain the position
  const constrainedX = Math.min(Math.max(currentX, minX), maxX);
  const constrainedY = Math.min(Math.max(currentY, minY), maxY);

  return {
    ...transform,
    x: constrainedX - draggingNodeRect.left,
    y: constrainedY - draggingNodeRect.top,
  };
};



// Custom hook to track previous value
const usePrevious = <T,>(value: T) => {
  const ref = useRef<T>();
  useEffect(() => {
    ref.current = value;
  });
  return ref.current;
};

export const CalendarView = (props: CalendarViewRenderProps) => {
  const { databaseStore, members, workspace } = useWorkspace();
  const { definition } = props;
  const { accessLevel } = usePage();
  const { isMobile } = useScreenSize();
  const maybeRecord = useMaybeRecord(); 

  const maybeShared = useMaybeShared();
  const maybeTemplate = useMaybeTemplate();

  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [viewType, setViewType] = useState<CalendarViewType>("week");
  const [selectedEvent, setSelectedEvent] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [showSideCalendar, setShowSideCalendar] = useState(!isMobile);
  const [activeDragData, setActiveDragData] = useState<any | null>(null);
  const savedScrollTop = useRef(0);
  const pointerCoordinates = useRef({ x: 0, y: 0 });

  const isInRecordTab = !!maybeRecord;

  definition.filter = definition.filter || { conditions: [], match: Match.All };
  definition.sorts = definition.sorts || [];

  const databaseId = definition.databaseId;
  const database = databaseStore[definition.databaseId];

  const isPublishedView = !!maybeShared;
  const editable = !definition.lockContent && !isPublishedView && !!accessLevel;

  let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);
  let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);

      const { createRecords, updateRecordValues, setPeekRecordId, peekRecordId, refreshDatabase, deleteRecords } = useViews();
    const { sorts, filter, search } = useViewFiltering();
    const { selectedIds, setSelectedIds } = useViewSelection();
  const prevPeekRecordId = usePrevious(peekRecordId);
  const { openRecord } = useStackedPeek();

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 150,
        tolerance: 5,
      },
    })
  );

  useEffect(() => {
    const containerId = viewType === 'day' ? 'day-view-container' : 'week-view-container';
    const container = document.getElementById(containerId);

    if (container) {
      requestAnimationFrame(() => {
        container.scrollTop = savedScrollTop.current;
      });
    }
  }, [selectedEvent, viewType]);

  useEffect(() => {
    setShowSideCalendar(!isMobile);
  }, [isMobile]);


  useEffect(() => {
    // Only refresh if the peek view was open and is now closed.
    if (prevPeekRecordId && !peekRecordId) {
      console.log("Peek view was closed, refreshing calendar data");
      refreshDatabase(definition.databaseId);
    }
  }, [peekRecordId, prevPeekRecordId, definition.databaseId, refreshDatabase]);

  if (!database) return <PageLoader size="full" />;

  const getEvents = (): CalendarEvent[] => {
    if (!database) return [];

    const { rows } = filterAndSortRecords(
      database,
      members,
      databaseStore,
      definition.filter || { match: Match.All, conditions: [] },
      filter,
      sorts.length ? sorts : (definition.sorts || []),
      workspace?.workspaceMember?.userId || '',
      database?.database?.id || ''
    );

    const filteredRows = searchFilteredRecords(search || "", rows);
    const titleColOpts = getDatabaseTitleCol(database.database);

    return filteredRows.map(row => {
      const startValue = row.processedRecord.processedRecordValues[definition.eventStartColumnId];
      let startDate: Date;

      if (startValue && typeof startValue === 'string') {
        startDate = new Date(startValue);
      } else {
        startDate = new Date();
      }

      let endDate: Date;
      if (definition.eventEndColumnId) {
        const endValue = row.processedRecord.processedRecordValues[definition.eventEndColumnId];
        if (endValue && typeof endValue === 'string') {
          endDate = new Date(endValue);
        } else {
          endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);
        }
      } else {
        endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);
      }

      const title = getRecordTitle(
        row.record,
        titleColOpts.titleColId,
        titleColOpts.defaultTitle,
        titleColOpts.isContacts
      );

      return {
        id: row.id,
        title,
        start: startDate,
        end: endDate,
        record: row.record,
        processedRecord: row.processedRecord
      };
    });
  };

  const getFilteredEvents = () => {
    const baseEvents = getEvents();

    if (!searchTerm.trim()) {
      return baseEvents;
    }

    return baseEvents.filter(event => {
      const searchLower = searchTerm.toLowerCase();
      return event.title.toLowerCase().includes(searchLower);
    });
  };

  const onDragStart = (event: { active: Active }) => {
    if (event.active.data.current) {
      const initialPointerY = pointerCoordinates.current.y;
      const initialEventTop = event.active.rect.current?.translated?.top ?? 0;
      const grabOffsetY = initialPointerY - initialEventTop;

      setActiveDragData({
        ...event.active.data.current,
        grabOffsetY,
        width: event.active.rect.current.translated?.width,
        height: event.active.rect.current.translated?.height,
      });

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('touchmove', handleTouchMove);
    }
  };

  const onDragEnd = async ({ active, over }: { active: Active, over: Over | null }) => {
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('touchmove', handleTouchMove);
    setActiveDragData(null);

    if (!over || !active || !canEditData || active.id === over.id) {
      return;
    }

    const activeData = active.data.current;
    const overData = over.data.current;

    if (!activeData || !overData) {
      return;
    }

    const { payload, type } = activeData;
    const eventToUpdate: CalendarEvent = type === 'segment' ? payload.originalEvent : payload;
    const originalStart = new Date(eventToUpdate.start);
    const originalEnd = new Date(eventToUpdate.end);
    const duration = differenceInMilliseconds(originalEnd, originalStart);

    let newStart: Date;

    if (overData.type.startsWith('allday')) {
      const dayDifference = differenceInDays(overData.date, startOfDay(originalStart));
      newStart = addDays(originalStart, dayDifference);
      newStart.setHours(0, 0, 0, 0);
    } else if (overData.type.startsWith('timeslot')) {
      newStart = new Date(overData.date);
      newStart.setHours(overData.hour, 0, 0, 0);
    } else if (overData.type === 'daycell') {
      const dayDifference = differenceInDays(overData.date, startOfDay(originalStart));
      newStart = addDays(originalStart, dayDifference);
    } else {
      return;
    }

    const newEnd = new Date(newStart.getTime() + duration);

    const recordId = eventToUpdate.record.id;
    const newValues = {
      [definition.eventStartColumnId]: newStart.toISOString(),
      ...(definition.eventEndColumnId && { [definition.eventEndColumnId]: newEnd.toISOString() }),
    };

    try {
      await updateRecordValues(definition.databaseId, [recordId], newValues);
      toast.success(`Event "${eventToUpdate.title}" updated.`);
    } catch (error) {
      toast.error("Failed to update event.");
      console.error("Error updating event:", error);
    }
  };

  const handleMouseMove = (event: MouseEvent) => {
    pointerCoordinates.current = { x: event.clientX, y: event.clientY };
  };
  const handleTouchMove = (event: TouchEvent) => {
      const touch = event.touches[0];
      pointerCoordinates.current = { x: touch.clientX, y: touch.clientY };
  };




  const events = getFilteredEvents();

  const goToToday = () => setSelectedDate(new Date());

  const goToPrevious = () => {
    switch (viewType) {
      case "day":
        setSelectedDate(prevDate => addDays(prevDate, -1));
        break;
      case "week":
        setSelectedDate(prevDate => subWeeks(prevDate, 1));
        break;
      case "month":
        setSelectedDate(prevDate => subMonths(prevDate, 1));
        break;
    }
  };

  const goToNext = () => {
    switch (viewType) {
      case "day":
        setSelectedDate(prevDate => addDays(prevDate, 1));
        break;
      case "week":
        setSelectedDate(prevDate => addWeeks(prevDate, 1));
        break;
      case "month":
        setSelectedDate(prevDate => addMonths(prevDate, 1));
        break;
    }
  };

  const handleRequestCreateEvent = (date: Date, useSystemTime: boolean = false) => {
    if (useSystemTime) {
      const now = new Date();
      const newDate = new Date(date);
      newDate.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());
      handleCreateEvent(newDate);
    } else {
      handleCreateEvent(date);
    }
  };

  const handleCreateEvent = async (date: Date = new Date()) => {
    if (!canEditData) return;

    const startTime = new Date(date);
    const endTime = new Date(startTime.getTime() + (definition.defaultDuration || 30) * 60000);
    const titleColOpts = getDatabaseTitleCol(database.database);

    try {
      const recordValues: any = {
        [definition.eventStartColumnId]: startTime.toISOString(),
        ...(definition.eventEndColumnId && { [definition.eventEndColumnId]: endTime.toISOString() })
      };

      if (titleColOpts.titleColId) {
        recordValues[titleColOpts.titleColId] = "New Event";
      }

      const result = await createRecords(definition.databaseId, [recordValues]);

      if (result && result.records && result.records.length > 0) {
        const newRecordId = result.records[0].id;

        if (newRecordId) {
          await refreshDatabase(definition.databaseId);
          setPeekRecordId(newRecordId);
          toast.success("New event created");
        } else {
          toast.error("Error accessing the new event");
        }
      } else {
        toast.error("Failed to create event properly");
      }
    } catch (error) {
      toast.error("Failed to create event");
    }
  };

  const handleEventClick = (event: CalendarEvent) => {
    if (event && event.id) {
      const containerId = viewType === 'day' ? 'day-view-container' : 'week-view-container';
      const container = document.getElementById(containerId);
      if (container) {
        savedScrollTop.current = container.scrollTop;
      }

      openRecord(event.id, event.record.databaseId);
      setSelectedEvent(event.id);
    }
  };

  const getHeaderDateDisplay = () => {
    switch (viewType) {
      case "day":
        return format(selectedDate, 'MMMM d, yyyy');
      case "week":
        return `${format(addDays(selectedDate, -selectedDate.getDay()), 'MMM d')} - ${format(addDays(selectedDate, 6-selectedDate.getDay()), 'MMM d, yyyy')}`;
      case "month":
        return format(selectedDate, 'MMMM yyyy');
      default:
        return format(selectedDate, 'MMMM d, yyyy');
    }
  };

  const handleEventDelete = async (event: CalendarEvent) => {
    if (!canEditData) return;
    
    try {
      await deleteRecords(database.database.id, [event.record.id]);
    } catch (error) {
      console.error('Error deleting event:', error);
    }
  };

  const viewTypeOptions: TagItem<string>[] = [
    { id: 'day', value: 'day', title: 'Day', data: 'day' },
    { id: 'week', value: 'week', title: 'Week', data: 'week' },
    { id: 'month', value: 'month', title: 'Month', data: 'month' }
  ];

  const selectedViewOption = viewType === 'day' ? ['day'] : viewType === 'week' ? ['week'] : ['month'];

  return (
    <div className="h-full flex flex-col bg-white">
     <div className={cn(
       "border-b border-neutral-300 bg-white",
       isInRecordTab && "py-1" 
     )}>
       {isMobile ? (
         /* Mobile Header Layout */
         <div className={cn("p-2", isInRecordTab && "py-1")}>
           <div className="flex items-center justify-between mb-2">
             <h1 className="text-xs font-semibold text-black truncate flex-1 mr-2">
               {getHeaderDateDisplay()}
             </h1>
             <Button
               variant="ghost"
               onClick={() => setShowSideCalendar(!showSideCalendar)}
               className="rounded-full h-8 px-3 text-xs text-black hover:bg-gray-50"
             >
               Calendar
             </Button>
           </div>

           <div className="flex items-center justify-between">
             <div className="flex items-center space-x-1">
               <Button
                 variant="ghost"
                 onClick={goToToday}
                 className={cn(
                   "rounded-full px-3 text-xs text-black hover:bg-gray-50",
                   isInRecordTab ? "h-6" : "h-8"
                 )}
               >
                 Today
               </Button>
               <div className="flex items-center">
                 <Button
                   variant="ghost"
                   onClick={goToPrevious}
                   className={cn(
                     "rounded-full p-1 text-black hover:bg-gray-50",
                     isInRecordTab ? "h-6 w-6" : "h-8 w-8"
                   )}
                 >
                   <AngleLeftIcon className="h-4 w-4" />
                 </Button>
                 <Button
                   variant="ghost"
                   onClick={goToNext}
                   className={cn(
                     "rounded-full p-1 text-black hover:bg-gray-50",
                     isInRecordTab ? "h-6 w-6" : "h-8 w-8"
                   )}
                 >
                   <AngleRightIcon className="h-4 w-4" />
                 </Button>
               </div>
             </div>

             <div className="flex items-center space-x-1">
               {/* View Type Selector */}
               <CustomSelect
                 options={viewTypeOptions}
                 selectedIds={selectedViewOption}
                 onChange={(selected) => {
                   if (selected.length > 0) {
                     setViewType(selected[0] as CalendarViewType);
                   }
                 }}
                 className={cn(
                   "px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black w-20",
                   isInRecordTab ? "h-6" : "h-8"
                 )}
                 placeholder="View"
                 hideSearch={true}
               />

               {canEditData && (
                 <Button
                   onClick={() => handleRequestCreateEvent(selectedDate, true)}
                   className={cn(
                     "rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black",
                     isInRecordTab ? "h-6" : "h-8"
                   )}
                 >
                   <PlusIcon className="h-3 w-3" />
                 </Button>
               )}
             </div>
           </div>
         </div>
             ) : (
       <div className={cn(
         "flex items-center justify-between px-4",
         isInRecordTab ? "py-1" : "py-2"
       )}>
         <div className="flex items-center space-x-2">
           <Button
             variant="ghost"
             onClick={goToToday}
             className={cn(
               "rounded-full px-3 text-xs text-black hover:bg-gray-50",
               isInRecordTab ? "h-6" : "h-8"
             )}
           >
             Today
           </Button>
           <div className="flex items-center">
             <Button
               variant="ghost"
               onClick={goToPrevious}
               className={cn(
                 "rounded-full p-1 text-black hover:bg-gray-50",
                 isInRecordTab ? "h-6 w-6" : "h-8 w-8"
               )}
             >
               <AngleLeftIcon className="h-4 w-4" />
             </Button>
             <Button
               variant="ghost"
               onClick={goToNext}
               className={cn(
                 "rounded-full p-1 text-black hover:bg-gray-50",
                 isInRecordTab ? "h-6 w-6" : "h-8 w-8"
               )}
             >
               <AngleRightIcon className="h-4 w-4" />
             </Button>
           </div>

           <h1 className="text-xs font-semibold text-black">
             {getHeaderDateDisplay()}
           </h1>
         </div>

         <div className="flex items-center space-x-2">
           {!isInRecordTab && (
             <div className="relative">
               <Input
                 placeholder="Search events..."
                 value={searchTerm}
                 onChange={(e) => setSearchTerm(e.target.value)}
                 className="w-48 pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none"
               />
               <MagnifyingGlassIcon className="h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400" />
             </div>
           )}

           <CustomSelect
             options={viewTypeOptions}
             selectedIds={selectedViewOption}
             onChange={(selected) => {
               if (selected.length > 0) {
                 setViewType(selected[0] as CalendarViewType);
               }
             }}
             className={cn(
               "px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black",
               isInRecordTab ? "h-6 w-20" : "h-8 w-28"
             )}
             placeholder="View"
             hideSearch={true}
           />

           {canEditData && (
             <Button
               onClick={() => handleRequestCreateEvent(selectedDate, true)}
               className={cn(
                 "rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black gap-1",
                 isInRecordTab ? "h-6" : "h-8"
               )}
             >
               <PlusIcon className="h-3 w-3" />
               {!isInRecordTab && <span>Add Event</span>}
             </Button>
           )}
         </div>
       </div>
     )}

     {isMobile && !isInRecordTab && (
       <div className="px-2 pb-2">
         <div className="relative">
           <Input
             placeholder="Search events..."
             value={searchTerm}
             onChange={(e) => setSearchTerm(e.target.value)}
             className="w-full pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none"
           />
           <MagnifyingGlassIcon className="h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400" />
         </div>
       </div>
     )}
   </div>

   {/* Main content */}
   <div className="flex-1 flex min-h-0">
     {showSideCalendar && (
     <div className={cn(
       "flex-none bg-white",
       isMobile ? "w-full absolute z-50 backdrop-blur-sm h-full shadow-lg" : "w-fit border-r border-neutral-300"
     )}>
       <div className="flex justify-between items-center p-2 border-b border-neutral-300">
         <h3 className="text-xs font-semibold text-black">Calendar</h3>
         {isMobile && (
           <Button
             variant="ghost"
             onClick={() => setShowSideCalendar(false)}
             className="rounded-full h-8 w-8 p-1 text-black hover:bg-neutral-100"
           >
             <span className="sr-only">Close</span>
             ×
           </Button>
         )}
       </div>
       <Calendar
         mode="single"
         selected={selectedDate}
         onSelect={(date) => {
           if (date) {
             setSelectedDate(date);
             if (isMobile) {
               setShowSideCalendar(false);
             }
           }
         }}
         className="rounded-md border-0"
       />
     </div>
     )}
     
     <DndContext
       sensors={sensors}
       onDragStart={onDragStart}
       onDragEnd={onDragEnd}
       modifiers={[restrictToCalendarContainer]}
     >
     <div className="flex-1 min-w-0" data-calendar-content="true">
       {viewType === 'day' && (
      <DayView
        selectedDate={selectedDate}
        events={events}
        selectedEvent={selectedEvent}
        setSelectedEvent={setSelectedEvent}
        openAddEventForm={handleRequestCreateEvent}
        canEditData={canEditData}
        savedScrollTop={savedScrollTop}
        handleEventClick={handleEventClick}
        activeDragData={activeDragData}
      />
    )}
    {viewType === 'week' && (
      <WeekView
        selectedDate={selectedDate}
        events={events}
        selectedEvent={selectedEvent}
        setSelectedEvent={setSelectedEvent}
        setSelectedDate={setSelectedDate}
        openAddEventForm={handleRequestCreateEvent}
        canEditData={canEditData}
        savedScrollTop={savedScrollTop}
        handleEventClick={handleEventClick}
        activeDragData={activeDragData}
      />
    )}
    {viewType === 'month' && (
      <MonthView
        selectedDate={selectedDate}
        events={events}
        selectedEvent={selectedEvent}
        setSelectedEvent={setSelectedEvent}
        setSelectedDate={setSelectedDate}
        openAddEventForm={(date) => handleRequestCreateEvent(date, true)}
        canEditData={canEditData}
        handleEventClick={handleEventClick}
        activeDragData={activeDragData}
      />
    )}
     </div>

     <DragOverlay dropAnimation={null}>
      {activeDragData && activeDragData.type === 'segment' ? (
          <CalendarEventSegment
            segment={activeDragData.payload as EventSegment}
            view={viewType === 'day' ? 'day' : 'week'}
            onClick={() => {}}
          />
        ) : activeDragData && activeDragData.type === 'event' ? (
          <CalendarEventItem
            event={activeDragData.payload as CalendarEvent}
            view='month'
            onClick={() => {}}
          />
        ) : null}
      </DragOverlay>
   </DndContext>
   </div>
  </div>
  );
};