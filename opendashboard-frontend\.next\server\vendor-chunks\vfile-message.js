"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/vfile-message";
exports.ids = ["vendor-chunks/vfile-message"];
exports.modules = {

/***/ "(ssr)/./node_modules/vfile-message/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/vfile-message/lib/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VFileMessage: () => (/* binding */ VFileMessage)\n/* harmony export */ });\n/* harmony import */ var unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-stringify-position */ \"(ssr)/./node_modules/unist-util-stringify-position/lib/index.js\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Position} Position\n * @typedef {import('unist').Point} Point\n * @typedef {object & {type: string, position?: Position | undefined}} NodeLike\n */\n\n\n\n/**\n * Message.\n */\nclass VFileMessage extends Error {\n  /**\n   * Create a message for `reason` at `place` from `origin`.\n   *\n   * When an error is passed in as `reason`, the `stack` is copied.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   *\n   *   > 👉 **Note**: you should use markdown.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns\n   *   Instance of `VFileMessage`.\n   */\n  // To do: next major: expose `undefined` everywhere instead of `null`.\n  constructor(reason, place, origin) {\n    /** @type {[string | null, string | null]} */\n    const parts = [null, null]\n    /** @type {Position} */\n    let position = {\n      // @ts-expect-error: we always follows the structure of `position`.\n      start: {line: null, column: null},\n      // @ts-expect-error: \"\n      end: {line: null, column: null}\n    }\n\n    super()\n\n    if (typeof place === 'string') {\n      origin = place\n      place = undefined\n    }\n\n    if (typeof origin === 'string') {\n      const index = origin.indexOf(':')\n\n      if (index === -1) {\n        parts[1] = origin\n      } else {\n        parts[0] = origin.slice(0, index)\n        parts[1] = origin.slice(index + 1)\n      }\n    }\n\n    if (place) {\n      // Node.\n      if ('type' in place || 'position' in place) {\n        if (place.position) {\n          // To do: next major: deep clone.\n          // @ts-expect-error: looks like a position.\n          position = place.position\n        }\n      }\n      // Position.\n      else if ('start' in place || 'end' in place) {\n        // @ts-expect-error: looks like a position.\n        // To do: next major: deep clone.\n        position = place\n      }\n      // Point.\n      else if ('line' in place || 'column' in place) {\n        // To do: next major: deep clone.\n        position.start = place\n      }\n    }\n\n    // Fields from `Error`.\n    /**\n     * Serialized positional info of error.\n     *\n     * On normal errors, this would be something like `ParseError`, buit in\n     * `VFile` messages we use this space to show where an error happened.\n     */\n    this.name = (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_0__.stringifyPosition)(place) || '1:1'\n\n    /**\n     * Reason for message.\n     *\n     * @type {string}\n     */\n    this.message = typeof reason === 'object' ? reason.message : reason\n\n    /**\n     * Stack of message.\n     *\n     * This is used by normal errors to show where something happened in\n     * programming code, irrelevant for `VFile` messages,\n     *\n     * @type {string}\n     */\n    this.stack = ''\n\n    if (typeof reason === 'object' && reason.stack) {\n      this.stack = reason.stack\n    }\n\n    /**\n     * Reason for message.\n     *\n     * > 👉 **Note**: you should use markdown.\n     *\n     * @type {string}\n     */\n    this.reason = this.message\n\n    /* eslint-disable no-unused-expressions */\n    /**\n     * State of problem.\n     *\n     * * `true` — marks associated file as no longer processable (error)\n     * * `false` — necessitates a (potential) change (warning)\n     * * `null | undefined` — for things that might not need changing (info)\n     *\n     * @type {boolean | null | undefined}\n     */\n    this.fatal\n\n    /**\n     * Starting line of error.\n     *\n     * @type {number | null}\n     */\n    this.line = position.start.line\n\n    /**\n     * Starting column of error.\n     *\n     * @type {number | null}\n     */\n    this.column = position.start.column\n\n    /**\n     * Full unist position.\n     *\n     * @type {Position | null}\n     */\n    this.position = position\n\n    /**\n     * Namespace of message (example: `'my-package'`).\n     *\n     * @type {string | null}\n     */\n    this.source = parts[0]\n\n    /**\n     * Category of message (example: `'my-rule'`).\n     *\n     * @type {string | null}\n     */\n    this.ruleId = parts[1]\n\n    /**\n     * Path of a file (used throughout the `VFile` ecosystem).\n     *\n     * @type {string | null}\n     */\n    this.file\n\n    // The following fields are “well known”.\n    // Not standard.\n    // Feel free to add other non-standard fields to your messages.\n\n    /**\n     * Specify the source value that’s being reported, which is deemed\n     * incorrect.\n     *\n     * @type {string | null}\n     */\n    this.actual\n\n    /**\n     * Suggest acceptable values that can be used instead of `actual`.\n     *\n     * @type {Array<string> | null}\n     */\n    this.expected\n\n    /**\n     * Link to docs for the message.\n     *\n     * > 👉 **Note**: this must be an absolute URL that can be passed as `x`\n     * > to `new URL(x)`.\n     *\n     * @type {string | null}\n     */\n    this.url\n\n    /**\n     * Long form description of the message (you should use markdown).\n     *\n     * @type {string | null}\n     */\n    this.note\n    /* eslint-enable no-unused-expressions */\n  }\n}\n\nVFileMessage.prototype.file = ''\nVFileMessage.prototype.name = ''\nVFileMessage.prototype.reason = ''\nVFileMessage.prototype.message = ''\nVFileMessage.prototype.stack = ''\nVFileMessage.prototype.fatal = null\nVFileMessage.prototype.column = null\nVFileMessage.prototype.line = null\nVFileMessage.prototype.source = null\nVFileMessage.prototype.ruleId = null\nVFileMessage.prototype.position = null\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmZpbGUtbWVzc2FnZS9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEsc0JBQXNCO0FBQ25DLGFBQWEsMEJBQTBCO0FBQ3ZDLGFBQWEsdUJBQXVCO0FBQ3BDLGFBQWEsVUFBVSxnREFBZ0Q7QUFDdkU7O0FBRStEOztBQUUvRDtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLCtCQUErQjtBQUM1QztBQUNBO0FBQ0E7QUFDQSxhQUFhLHVEQUF1RDtBQUNwRTtBQUNBLGFBQWEsMkJBQTJCO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxnQ0FBZ0M7QUFDL0M7QUFDQSxlQUFlLFVBQVU7QUFDekI7QUFDQTtBQUNBLGNBQWMseUJBQXlCO0FBQ3ZDO0FBQ0EsWUFBWTtBQUNaOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsZ0ZBQWlCOztBQUVqQztBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy92ZmlsZS1tZXNzYWdlL2xpYi9pbmRleC5qcz82OTk2Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgndW5pc3QnKS5Ob2RlfSBOb2RlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCd1bmlzdCcpLlBvc2l0aW9ufSBQb3NpdGlvblxuICogQHR5cGVkZWYge2ltcG9ydCgndW5pc3QnKS5Qb2ludH0gUG9pbnRcbiAqIEB0eXBlZGVmIHtvYmplY3QgJiB7dHlwZTogc3RyaW5nLCBwb3NpdGlvbj86IFBvc2l0aW9uIHwgdW5kZWZpbmVkfX0gTm9kZUxpa2VcbiAqL1xuXG5pbXBvcnQge3N0cmluZ2lmeVBvc2l0aW9ufSBmcm9tICd1bmlzdC11dGlsLXN0cmluZ2lmeS1wb3NpdGlvbidcblxuLyoqXG4gKiBNZXNzYWdlLlxuICovXG5leHBvcnQgY2xhc3MgVkZpbGVNZXNzYWdlIGV4dGVuZHMgRXJyb3Ige1xuICAvKipcbiAgICogQ3JlYXRlIGEgbWVzc2FnZSBmb3IgYHJlYXNvbmAgYXQgYHBsYWNlYCBmcm9tIGBvcmlnaW5gLlxuICAgKlxuICAgKiBXaGVuIGFuIGVycm9yIGlzIHBhc3NlZCBpbiBhcyBgcmVhc29uYCwgdGhlIGBzdGFja2AgaXMgY29waWVkLlxuICAgKlxuICAgKiBAcGFyYW0ge3N0cmluZyB8IEVycm9yIHwgVkZpbGVNZXNzYWdlfSByZWFzb25cbiAgICogICBSZWFzb24gZm9yIG1lc3NhZ2UsIHVzZXMgdGhlIHN0YWNrIGFuZCBtZXNzYWdlIG9mIHRoZSBlcnJvciBpZiBnaXZlbi5cbiAgICpcbiAgICogICA+IPCfkYkgKipOb3RlKio6IHlvdSBzaG91bGQgdXNlIG1hcmtkb3duLlxuICAgKiBAcGFyYW0ge05vZGUgfCBOb2RlTGlrZSB8IFBvc2l0aW9uIHwgUG9pbnQgfCBudWxsIHwgdW5kZWZpbmVkfSBbcGxhY2VdXG4gICAqICAgUGxhY2UgaW4gZmlsZSB3aGVyZSB0aGUgbWVzc2FnZSBvY2N1cnJlZC5cbiAgICogQHBhcmFtIHtzdHJpbmcgfCBudWxsIHwgdW5kZWZpbmVkfSBbb3JpZ2luXVxuICAgKiAgIFBsYWNlIGluIGNvZGUgd2hlcmUgdGhlIG1lc3NhZ2Ugb3JpZ2luYXRlcyAoZXhhbXBsZTpcbiAgICogICBgJ215LXBhY2thZ2U6bXktcnVsZSdgIG9yIGAnbXktcnVsZSdgKS5cbiAgICogQHJldHVybnNcbiAgICogICBJbnN0YW5jZSBvZiBgVkZpbGVNZXNzYWdlYC5cbiAgICovXG4gIC8vIFRvIGRvOiBuZXh0IG1ham9yOiBleHBvc2UgYHVuZGVmaW5lZGAgZXZlcnl3aGVyZSBpbnN0ZWFkIG9mIGBudWxsYC5cbiAgY29uc3RydWN0b3IocmVhc29uLCBwbGFjZSwgb3JpZ2luKSB7XG4gICAgLyoqIEB0eXBlIHtbc3RyaW5nIHwgbnVsbCwgc3RyaW5nIHwgbnVsbF19ICovXG4gICAgY29uc3QgcGFydHMgPSBbbnVsbCwgbnVsbF1cbiAgICAvKiogQHR5cGUge1Bvc2l0aW9ufSAqL1xuICAgIGxldCBwb3NpdGlvbiA9IHtcbiAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3I6IHdlIGFsd2F5cyBmb2xsb3dzIHRoZSBzdHJ1Y3R1cmUgb2YgYHBvc2l0aW9uYC5cbiAgICAgIHN0YXJ0OiB7bGluZTogbnVsbCwgY29sdW1uOiBudWxsfSxcbiAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3I6IFwiXG4gICAgICBlbmQ6IHtsaW5lOiBudWxsLCBjb2x1bW46IG51bGx9XG4gICAgfVxuXG4gICAgc3VwZXIoKVxuXG4gICAgaWYgKHR5cGVvZiBwbGFjZSA9PT0gJ3N0cmluZycpIHtcbiAgICAgIG9yaWdpbiA9IHBsYWNlXG4gICAgICBwbGFjZSA9IHVuZGVmaW5lZFxuICAgIH1cblxuICAgIGlmICh0eXBlb2Ygb3JpZ2luID09PSAnc3RyaW5nJykge1xuICAgICAgY29uc3QgaW5kZXggPSBvcmlnaW4uaW5kZXhPZignOicpXG5cbiAgICAgIGlmIChpbmRleCA9PT0gLTEpIHtcbiAgICAgICAgcGFydHNbMV0gPSBvcmlnaW5cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHBhcnRzWzBdID0gb3JpZ2luLnNsaWNlKDAsIGluZGV4KVxuICAgICAgICBwYXJ0c1sxXSA9IG9yaWdpbi5zbGljZShpbmRleCArIDEpXG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKHBsYWNlKSB7XG4gICAgICAvLyBOb2RlLlxuICAgICAgaWYgKCd0eXBlJyBpbiBwbGFjZSB8fCAncG9zaXRpb24nIGluIHBsYWNlKSB7XG4gICAgICAgIGlmIChwbGFjZS5wb3NpdGlvbikge1xuICAgICAgICAgIC8vIFRvIGRvOiBuZXh0IG1ham9yOiBkZWVwIGNsb25lLlxuICAgICAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3I6IGxvb2tzIGxpa2UgYSBwb3NpdGlvbi5cbiAgICAgICAgICBwb3NpdGlvbiA9IHBsYWNlLnBvc2l0aW9uXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIC8vIFBvc2l0aW9uLlxuICAgICAgZWxzZSBpZiAoJ3N0YXJ0JyBpbiBwbGFjZSB8fCAnZW5kJyBpbiBwbGFjZSkge1xuICAgICAgICAvLyBAdHMtZXhwZWN0LWVycm9yOiBsb29rcyBsaWtlIGEgcG9zaXRpb24uXG4gICAgICAgIC8vIFRvIGRvOiBuZXh0IG1ham9yOiBkZWVwIGNsb25lLlxuICAgICAgICBwb3NpdGlvbiA9IHBsYWNlXG4gICAgICB9XG4gICAgICAvLyBQb2ludC5cbiAgICAgIGVsc2UgaWYgKCdsaW5lJyBpbiBwbGFjZSB8fCAnY29sdW1uJyBpbiBwbGFjZSkge1xuICAgICAgICAvLyBUbyBkbzogbmV4dCBtYWpvcjogZGVlcCBjbG9uZS5cbiAgICAgICAgcG9zaXRpb24uc3RhcnQgPSBwbGFjZVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEZpZWxkcyBmcm9tIGBFcnJvcmAuXG4gICAgLyoqXG4gICAgICogU2VyaWFsaXplZCBwb3NpdGlvbmFsIGluZm8gb2YgZXJyb3IuXG4gICAgICpcbiAgICAgKiBPbiBub3JtYWwgZXJyb3JzLCB0aGlzIHdvdWxkIGJlIHNvbWV0aGluZyBsaWtlIGBQYXJzZUVycm9yYCwgYnVpdCBpblxuICAgICAqIGBWRmlsZWAgbWVzc2FnZXMgd2UgdXNlIHRoaXMgc3BhY2UgdG8gc2hvdyB3aGVyZSBhbiBlcnJvciBoYXBwZW5lZC5cbiAgICAgKi9cbiAgICB0aGlzLm5hbWUgPSBzdHJpbmdpZnlQb3NpdGlvbihwbGFjZSkgfHwgJzE6MSdcblxuICAgIC8qKlxuICAgICAqIFJlYXNvbiBmb3IgbWVzc2FnZS5cbiAgICAgKlxuICAgICAqIEB0eXBlIHtzdHJpbmd9XG4gICAgICovXG4gICAgdGhpcy5tZXNzYWdlID0gdHlwZW9mIHJlYXNvbiA9PT0gJ29iamVjdCcgPyByZWFzb24ubWVzc2FnZSA6IHJlYXNvblxuXG4gICAgLyoqXG4gICAgICogU3RhY2sgb2YgbWVzc2FnZS5cbiAgICAgKlxuICAgICAqIFRoaXMgaXMgdXNlZCBieSBub3JtYWwgZXJyb3JzIHRvIHNob3cgd2hlcmUgc29tZXRoaW5nIGhhcHBlbmVkIGluXG4gICAgICogcHJvZ3JhbW1pbmcgY29kZSwgaXJyZWxldmFudCBmb3IgYFZGaWxlYCBtZXNzYWdlcyxcbiAgICAgKlxuICAgICAqIEB0eXBlIHtzdHJpbmd9XG4gICAgICovXG4gICAgdGhpcy5zdGFjayA9ICcnXG5cbiAgICBpZiAodHlwZW9mIHJlYXNvbiA9PT0gJ29iamVjdCcgJiYgcmVhc29uLnN0YWNrKSB7XG4gICAgICB0aGlzLnN0YWNrID0gcmVhc29uLnN0YWNrXG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogUmVhc29uIGZvciBtZXNzYWdlLlxuICAgICAqXG4gICAgICogPiDwn5GJICoqTm90ZSoqOiB5b3Ugc2hvdWxkIHVzZSBtYXJrZG93bi5cbiAgICAgKlxuICAgICAqIEB0eXBlIHtzdHJpbmd9XG4gICAgICovXG4gICAgdGhpcy5yZWFzb24gPSB0aGlzLm1lc3NhZ2VcblxuICAgIC8qIGVzbGludC1kaXNhYmxlIG5vLXVudXNlZC1leHByZXNzaW9ucyAqL1xuICAgIC8qKlxuICAgICAqIFN0YXRlIG9mIHByb2JsZW0uXG4gICAgICpcbiAgICAgKiAqIGB0cnVlYCDigJQgbWFya3MgYXNzb2NpYXRlZCBmaWxlIGFzIG5vIGxvbmdlciBwcm9jZXNzYWJsZSAoZXJyb3IpXG4gICAgICogKiBgZmFsc2VgIOKAlCBuZWNlc3NpdGF0ZXMgYSAocG90ZW50aWFsKSBjaGFuZ2UgKHdhcm5pbmcpXG4gICAgICogKiBgbnVsbCB8IHVuZGVmaW5lZGAg4oCUIGZvciB0aGluZ3MgdGhhdCBtaWdodCBub3QgbmVlZCBjaGFuZ2luZyAoaW5mbylcbiAgICAgKlxuICAgICAqIEB0eXBlIHtib29sZWFuIHwgbnVsbCB8IHVuZGVmaW5lZH1cbiAgICAgKi9cbiAgICB0aGlzLmZhdGFsXG5cbiAgICAvKipcbiAgICAgKiBTdGFydGluZyBsaW5lIG9mIGVycm9yLlxuICAgICAqXG4gICAgICogQHR5cGUge251bWJlciB8IG51bGx9XG4gICAgICovXG4gICAgdGhpcy5saW5lID0gcG9zaXRpb24uc3RhcnQubGluZVxuXG4gICAgLyoqXG4gICAgICogU3RhcnRpbmcgY29sdW1uIG9mIGVycm9yLlxuICAgICAqXG4gICAgICogQHR5cGUge251bWJlciB8IG51bGx9XG4gICAgICovXG4gICAgdGhpcy5jb2x1bW4gPSBwb3NpdGlvbi5zdGFydC5jb2x1bW5cblxuICAgIC8qKlxuICAgICAqIEZ1bGwgdW5pc3QgcG9zaXRpb24uXG4gICAgICpcbiAgICAgKiBAdHlwZSB7UG9zaXRpb24gfCBudWxsfVxuICAgICAqL1xuICAgIHRoaXMucG9zaXRpb24gPSBwb3NpdGlvblxuXG4gICAgLyoqXG4gICAgICogTmFtZXNwYWNlIG9mIG1lc3NhZ2UgKGV4YW1wbGU6IGAnbXktcGFja2FnZSdgKS5cbiAgICAgKlxuICAgICAqIEB0eXBlIHtzdHJpbmcgfCBudWxsfVxuICAgICAqL1xuICAgIHRoaXMuc291cmNlID0gcGFydHNbMF1cblxuICAgIC8qKlxuICAgICAqIENhdGVnb3J5IG9mIG1lc3NhZ2UgKGV4YW1wbGU6IGAnbXktcnVsZSdgKS5cbiAgICAgKlxuICAgICAqIEB0eXBlIHtzdHJpbmcgfCBudWxsfVxuICAgICAqL1xuICAgIHRoaXMucnVsZUlkID0gcGFydHNbMV1cblxuICAgIC8qKlxuICAgICAqIFBhdGggb2YgYSBmaWxlICh1c2VkIHRocm91Z2hvdXQgdGhlIGBWRmlsZWAgZWNvc3lzdGVtKS5cbiAgICAgKlxuICAgICAqIEB0eXBlIHtzdHJpbmcgfCBudWxsfVxuICAgICAqL1xuICAgIHRoaXMuZmlsZVxuXG4gICAgLy8gVGhlIGZvbGxvd2luZyBmaWVsZHMgYXJlIOKAnHdlbGwga25vd27igJ0uXG4gICAgLy8gTm90IHN0YW5kYXJkLlxuICAgIC8vIEZlZWwgZnJlZSB0byBhZGQgb3RoZXIgbm9uLXN0YW5kYXJkIGZpZWxkcyB0byB5b3VyIG1lc3NhZ2VzLlxuXG4gICAgLyoqXG4gICAgICogU3BlY2lmeSB0aGUgc291cmNlIHZhbHVlIHRoYXTigJlzIGJlaW5nIHJlcG9ydGVkLCB3aGljaCBpcyBkZWVtZWRcbiAgICAgKiBpbmNvcnJlY3QuXG4gICAgICpcbiAgICAgKiBAdHlwZSB7c3RyaW5nIHwgbnVsbH1cbiAgICAgKi9cbiAgICB0aGlzLmFjdHVhbFxuXG4gICAgLyoqXG4gICAgICogU3VnZ2VzdCBhY2NlcHRhYmxlIHZhbHVlcyB0aGF0IGNhbiBiZSB1c2VkIGluc3RlYWQgb2YgYGFjdHVhbGAuXG4gICAgICpcbiAgICAgKiBAdHlwZSB7QXJyYXk8c3RyaW5nPiB8IG51bGx9XG4gICAgICovXG4gICAgdGhpcy5leHBlY3RlZFxuXG4gICAgLyoqXG4gICAgICogTGluayB0byBkb2NzIGZvciB0aGUgbWVzc2FnZS5cbiAgICAgKlxuICAgICAqID4g8J+RiSAqKk5vdGUqKjogdGhpcyBtdXN0IGJlIGFuIGFic29sdXRlIFVSTCB0aGF0IGNhbiBiZSBwYXNzZWQgYXMgYHhgXG4gICAgICogPiB0byBgbmV3IFVSTCh4KWAuXG4gICAgICpcbiAgICAgKiBAdHlwZSB7c3RyaW5nIHwgbnVsbH1cbiAgICAgKi9cbiAgICB0aGlzLnVybFxuXG4gICAgLyoqXG4gICAgICogTG9uZyBmb3JtIGRlc2NyaXB0aW9uIG9mIHRoZSBtZXNzYWdlICh5b3Ugc2hvdWxkIHVzZSBtYXJrZG93bikuXG4gICAgICpcbiAgICAgKiBAdHlwZSB7c3RyaW5nIHwgbnVsbH1cbiAgICAgKi9cbiAgICB0aGlzLm5vdGVcbiAgICAvKiBlc2xpbnQtZW5hYmxlIG5vLXVudXNlZC1leHByZXNzaW9ucyAqL1xuICB9XG59XG5cblZGaWxlTWVzc2FnZS5wcm90b3R5cGUuZmlsZSA9ICcnXG5WRmlsZU1lc3NhZ2UucHJvdG90eXBlLm5hbWUgPSAnJ1xuVkZpbGVNZXNzYWdlLnByb3RvdHlwZS5yZWFzb24gPSAnJ1xuVkZpbGVNZXNzYWdlLnByb3RvdHlwZS5tZXNzYWdlID0gJydcblZGaWxlTWVzc2FnZS5wcm90b3R5cGUuc3RhY2sgPSAnJ1xuVkZpbGVNZXNzYWdlLnByb3RvdHlwZS5mYXRhbCA9IG51bGxcblZGaWxlTWVzc2FnZS5wcm90b3R5cGUuY29sdW1uID0gbnVsbFxuVkZpbGVNZXNzYWdlLnByb3RvdHlwZS5saW5lID0gbnVsbFxuVkZpbGVNZXNzYWdlLnByb3RvdHlwZS5zb3VyY2UgPSBudWxsXG5WRmlsZU1lc3NhZ2UucHJvdG90eXBlLnJ1bGVJZCA9IG51bGxcblZGaWxlTWVzc2FnZS5wcm90b3R5cGUucG9zaXRpb24gPSBudWxsXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vfile-message/lib/index.js\n");

/***/ })

};
;