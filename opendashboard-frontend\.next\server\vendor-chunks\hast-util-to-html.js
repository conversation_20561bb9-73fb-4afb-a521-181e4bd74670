"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-to-html";
exports.ids = ["vendor-chunks/hast-util-to-html"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/handle/comment.js":
/*!**************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/comment.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   comment: () => (/* binding */ comment)\n/* harmony export */ });\n/* harmony import */ var stringify_entities__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stringify-entities */ \"(ssr)/./node_modules/stringify-entities/lib/index.js\");\n/**\n * @typedef {import('../types.js').Comment} Comment\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n */\n\n\n\n/**\n * Serialize a comment.\n *\n * @param {Comment} node\n *   Node to handle.\n * @param {number | undefined} _1\n *   Index of `node` in `parent.\n * @param {Parent | undefined} _2\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction comment(node, _1, _2, state) {\n  // See: <https://html.spec.whatwg.org/multipage/syntax.html#comments>\n  return state.settings.bogusComments\n    ? '<?' +\n        (0,stringify_entities__WEBPACK_IMPORTED_MODULE_0__.stringifyEntities)(\n          node.value,\n          Object.assign({}, state.settings.characterReferences, {subset: ['>']})\n        ) +\n        '>'\n    : '<!--' + node.value.replace(/^>|^->|<!--|-->|--!>|<!-$/g, encode) + '-->'\n\n  /**\n   * @param {string} $0\n   */\n  function encode($0) {\n    return (0,stringify_entities__WEBPACK_IMPORTED_MODULE_0__.stringifyEntities)(\n      $0,\n      Object.assign({}, state.settings.characterReferences, {\n        subset: ['<', '>']\n      })\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/handle/comment.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/handle/doctype.js":
/*!**************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/doctype.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   doctype: () => (/* binding */ doctype)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').DocType} DocType\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n */\n\n/**\n * Serialize a doctype.\n *\n * @param {DocType} _1\n *   Node to handle.\n * @param {number | undefined} _2\n *   Index of `node` in `parent.\n * @param {Parent | undefined} _3\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction doctype(_1, _2, _3, state) {\n  return (\n    '<!' +\n    (state.settings.upperDoctype ? 'DOCTYPE' : 'doctype') +\n    (state.settings.tightDoctype ? '' : ' ') +\n    'html>'\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbGliL2hhbmRsZS9kb2N0eXBlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsK0JBQStCO0FBQzVDLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsNkJBQTZCO0FBQzFDOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1odG1sL2xpYi9oYW5kbGUvZG9jdHlwZS5qcz82ZTI1Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5Eb2NUeXBlfSBEb2NUeXBlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlBhcmVudH0gUGFyZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbi8qKlxuICogU2VyaWFsaXplIGEgZG9jdHlwZS5cbiAqXG4gKiBAcGFyYW0ge0RvY1R5cGV9IF8xXG4gKiAgIE5vZGUgdG8gaGFuZGxlLlxuICogQHBhcmFtIHtudW1iZXIgfCB1bmRlZmluZWR9IF8yXG4gKiAgIEluZGV4IG9mIGBub2RlYCBpbiBgcGFyZW50LlxuICogQHBhcmFtIHtQYXJlbnQgfCB1bmRlZmluZWR9IF8zXG4gKiAgIFBhcmVudCBvZiBgbm9kZWAuXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQgYWJvdXQgdGhlIGN1cnJlbnQgc3RhdGUuXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICogICBTZXJpYWxpemVkIG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBkb2N0eXBlKF8xLCBfMiwgXzMsIHN0YXRlKSB7XG4gIHJldHVybiAoXG4gICAgJzwhJyArXG4gICAgKHN0YXRlLnNldHRpbmdzLnVwcGVyRG9jdHlwZSA/ICdET0NUWVBFJyA6ICdkb2N0eXBlJykgK1xuICAgIChzdGF0ZS5zZXR0aW5ncy50aWdodERvY3R5cGUgPyAnJyA6ICcgJykgK1xuICAgICdodG1sPidcbiAgKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/handle/doctype.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/handle/element.js":
/*!**************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/element.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   element: () => (/* binding */ element)\n/* harmony export */ });\n/* harmony import */ var ccount__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ccount */ \"(ssr)/./node_modules/ccount/index.js\");\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/space-separated-tokens/index.js\");\n/* harmony import */ var stringify_entities__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! stringify-entities */ \"(ssr)/./node_modules/stringify-entities/lib/index.js\");\n/* harmony import */ var _omission_opening_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../omission/opening.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/omission/opening.js\");\n/* harmony import */ var _omission_closing_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../omission/closing.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/omission/closing.js\");\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Properties} Properties\n * @typedef {import('../types.js').PropertyValue} PropertyValue\n */\n\n\n\n\n\n\n\n\n\n/**\n * Maps of subsets.\n *\n * Each value is a matrix of tuples.\n * The value at `0` causes parse errors, the value at `1` is valid.\n * Of both, the value at `0` is unsafe, and the value at `1` is safe.\n *\n * @type {Record<'name' | 'unquoted' | 'single' | 'double', Array<[Array<string>, Array<string>]>>}\n */\nconst constants = {\n  // See: <https://html.spec.whatwg.org/#attribute-name-state>.\n  name: [\n    ['\\t\\n\\f\\r &/=>'.split(''), '\\t\\n\\f\\r \"&\\'/=>`'.split('')],\n    ['\\0\\t\\n\\f\\r \"&\\'/<=>'.split(''), '\\0\\t\\n\\f\\r \"&\\'/<=>`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(unquoted)-state>.\n  unquoted: [\n    ['\\t\\n\\f\\r &>'.split(''), '\\0\\t\\n\\f\\r \"&\\'<=>`'.split('')],\n    ['\\0\\t\\n\\f\\r \"&\\'<=>`'.split(''), '\\0\\t\\n\\f\\r \"&\\'<=>`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(single-quoted)-state>.\n  single: [\n    [\"&'\".split(''), '\"&\\'`'.split('')],\n    [\"\\0&'\".split(''), '\\0\"&\\'`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(double-quoted)-state>.\n  double: [\n    ['\"&'.split(''), '\"&\\'`'.split('')],\n    ['\\0\"&'.split(''), '\\0\"&\\'`'.split('')]\n  ]\n}\n\n/**\n * Serialize an element node.\n *\n * @param {Element} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parent | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\n// eslint-disable-next-line complexity\nfunction element(node, index, parent, state) {\n  const schema = state.schema\n  const omit = schema.space === 'svg' ? false : state.settings.omitOptionalTags\n  let selfClosing =\n    schema.space === 'svg'\n      ? state.settings.closeEmptyElements\n      : state.settings.voids.includes(node.tagName.toLowerCase())\n  /** @type {Array<string>} */\n  const parts = []\n  /** @type {string} */\n  let last\n\n  if (schema.space === 'html' && node.tagName === 'svg') {\n    state.schema = property_information__WEBPACK_IMPORTED_MODULE_0__.svg\n  }\n\n  const attrs = serializeAttributes(state, node.properties)\n\n  const content = state.all(\n    schema.space === 'html' && node.tagName === 'template' ? node.content : node\n  )\n\n  state.schema = schema\n\n  // If the node is categorised as void, but it has children, remove the\n  // categorisation.\n  // This enables for example `menuitem`s, which are void in W3C HTML but not\n  // void in WHATWG HTML, to be stringified properly.\n  if (content) selfClosing = false\n\n  if (attrs || !omit || !(0,_omission_opening_js__WEBPACK_IMPORTED_MODULE_1__.opening)(node, index, parent)) {\n    parts.push('<', node.tagName, attrs ? ' ' + attrs : '')\n\n    if (\n      selfClosing &&\n      (schema.space === 'svg' || state.settings.closeSelfClosing)\n    ) {\n      last = attrs.charAt(attrs.length - 1)\n      if (\n        !state.settings.tightSelfClosing ||\n        last === '/' ||\n        (last && last !== '\"' && last !== \"'\")\n      ) {\n        parts.push(' ')\n      }\n\n      parts.push('/')\n    }\n\n    parts.push('>')\n  }\n\n  parts.push(content)\n\n  if (!selfClosing && (!omit || !(0,_omission_closing_js__WEBPACK_IMPORTED_MODULE_2__.closing)(node, index, parent))) {\n    parts.push('</' + node.tagName + '>')\n  }\n\n  return parts.join('')\n}\n\n/**\n * @param {State} state\n * @param {Properties | null | undefined} props\n * @returns {string}\n */\nfunction serializeAttributes(state, props) {\n  /** @type {Array<string>} */\n  const values = []\n  let index = -1\n  /** @type {string} */\n  let key\n\n  if (props) {\n    for (key in props) {\n      if (props[key] !== undefined && props[key] !== null) {\n        const value = serializeAttribute(state, key, props[key])\n        if (value) values.push(value)\n      }\n    }\n  }\n\n  while (++index < values.length) {\n    const last = state.settings.tightAttributes\n      ? values[index].charAt(values[index].length - 1)\n      : null\n\n    // In tight mode, don’t add a space after quoted attributes.\n    if (index !== values.length - 1 && last !== '\"' && last !== \"'\") {\n      values[index] += ' '\n    }\n  }\n\n  return values.join('')\n}\n\n/**\n * @param {State} state\n * @param {string} key\n * @param {PropertyValue} value\n * @returns {string}\n */\n// eslint-disable-next-line complexity\nfunction serializeAttribute(state, key, value) {\n  const info = (0,property_information__WEBPACK_IMPORTED_MODULE_3__.find)(state.schema, key)\n  const x =\n    state.settings.allowParseErrors && state.schema.space === 'html' ? 0 : 1\n  const y = state.settings.allowDangerousCharacters ? 0 : 1\n  let quote = state.quote\n  /** @type {string | undefined} */\n  let result\n\n  if (info.overloadedBoolean && (value === info.attribute || value === '')) {\n    value = true\n  } else if (\n    info.boolean ||\n    (info.overloadedBoolean && typeof value !== 'string')\n  ) {\n    value = Boolean(value)\n  }\n\n  if (\n    value === undefined ||\n    value === null ||\n    value === false ||\n    (typeof value === 'number' && Number.isNaN(value))\n  ) {\n    return ''\n  }\n\n  const name = (0,stringify_entities__WEBPACK_IMPORTED_MODULE_4__.stringifyEntities)(\n    info.attribute,\n    Object.assign({}, state.settings.characterReferences, {\n      // Always encode without parse errors in non-HTML.\n      subset: constants.name[x][y]\n    })\n  )\n\n  // No value.\n  // There is currently only one boolean property in SVG: `[download]` on\n  // `<a>`.\n  // This property does not seem to work in browsers (Firefox, Safari, Chrome),\n  // so I can’t test if dropping the value works.\n  // But I assume that it should:\n  //\n  // ```html\n  // <!doctype html>\n  // <svg viewBox=\"0 0 100 100\">\n  //   <a href=https://example.com download>\n  //     <circle cx=50 cy=40 r=35 />\n  //   </a>\n  // </svg>\n  // ```\n  //\n  // See: <https://github.com/wooorm/property-information/blob/main/lib/svg.js>\n  if (value === true) return name\n\n  // `spaces` doesn’t accept a second argument, but it’s given here just to\n  // keep the code cleaner.\n  value = Array.isArray(value)\n    ? (info.commaSeparated ? comma_separated_tokens__WEBPACK_IMPORTED_MODULE_5__.stringify : space_separated_tokens__WEBPACK_IMPORTED_MODULE_6__.stringify)(value, {\n        padLeft: !state.settings.tightCommaSeparatedLists\n      })\n    : String(value)\n\n  if (state.settings.collapseEmptyAttributes && !value) return name\n\n  // Check unquoted value.\n  if (state.settings.preferUnquoted) {\n    result = (0,stringify_entities__WEBPACK_IMPORTED_MODULE_4__.stringifyEntities)(\n      value,\n      Object.assign({}, state.settings.characterReferences, {\n        subset: constants.unquoted[x][y],\n        attribute: true\n      })\n    )\n  }\n\n  // If we don’t want unquoted, or if `value` contains character references when\n  // unquoted…\n  if (result !== value) {\n    // If the alternative is less common than `quote`, switch.\n    if (\n      state.settings.quoteSmart &&\n      (0,ccount__WEBPACK_IMPORTED_MODULE_7__.ccount)(value, quote) > (0,ccount__WEBPACK_IMPORTED_MODULE_7__.ccount)(value, state.alternative)\n    ) {\n      quote = state.alternative\n    }\n\n    result =\n      quote +\n      (0,stringify_entities__WEBPACK_IMPORTED_MODULE_4__.stringifyEntities)(\n        value,\n        Object.assign({}, state.settings.characterReferences, {\n          // Always encode without parse errors in non-HTML.\n          subset: (quote === \"'\" ? constants.single : constants.double)[x][y],\n          attribute: true\n        })\n      ) +\n      quote\n  }\n\n  // Don’t add a `=` for unquoted empties.\n  return name + (result ? '=' + result : result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/handle/element.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/handle/index.js":
/*!************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handle: () => (/* binding */ handle)\n/* harmony export */ });\n/* harmony import */ var zwitch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zwitch */ \"(ssr)/./node_modules/zwitch/index.js\");\n/* harmony import */ var _comment_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./comment.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/comment.js\");\n/* harmony import */ var _doctype_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./doctype.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/doctype.js\");\n/* harmony import */ var _element_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./element.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/element.js\");\n/* harmony import */ var _raw_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./raw.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/raw.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./root.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/root.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./text.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/text.js\");\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Node} Node\n * @typedef {import('../types.js').Parent} Parent\n */\n\n\n\n\n\n\n\n\n\n/**\n * @type {(node: Node, index: number | undefined, parent: Parent | undefined, state: State) => string}\n */\nconst handle = (0,zwitch__WEBPACK_IMPORTED_MODULE_0__.zwitch)('type', {\n  invalid,\n  unknown,\n  handlers: {comment: _comment_js__WEBPACK_IMPORTED_MODULE_1__.comment, doctype: _doctype_js__WEBPACK_IMPORTED_MODULE_2__.doctype, element: _element_js__WEBPACK_IMPORTED_MODULE_3__.element, raw: _raw_js__WEBPACK_IMPORTED_MODULE_4__.raw, root: _root_js__WEBPACK_IMPORTED_MODULE_5__.root, text: _text_js__WEBPACK_IMPORTED_MODULE_6__.text}\n})\n\n/**\n * Fail when a non-node is found in the tree.\n *\n * @param {unknown} node\n *   Unknown value.\n * @returns {never}\n *   Never.\n */\nfunction invalid(node) {\n  throw new Error('Expected node, not `' + node + '`')\n}\n\n/**\n * Fail when a node with an unknown type is found in the tree.\n *\n * @param {unknown} node\n *  Unknown node.\n * @returns {never}\n *   Never.\n */\nfunction unknown(node) {\n  // @ts-expect-error: `type` is defined.\n  throw new Error('Cannot compile unknown node `' + node.type + '`')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/handle/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/handle/raw.js":
/*!**********************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/raw.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   raw: () => (/* binding */ raw)\n/* harmony export */ });\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./text.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/text.js\");\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').Raw} Raw\n */\n\n\n\n/**\n * Serialize a raw node.\n *\n * @param {Raw} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parent | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction raw(node, index, parent, state) {\n  return state.settings.allowDangerousHtml\n    ? node.value\n    : (0,_text_js__WEBPACK_IMPORTED_MODULE_0__.text)(node, index, parent, state)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbGliL2hhbmRsZS9yYXcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEsNkJBQTZCO0FBQzFDLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsMkJBQTJCO0FBQ3hDOztBQUU4Qjs7QUFFOUI7QUFDQTtBQUNBO0FBQ0EsV0FBVyxLQUFLO0FBQ2hCO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0EsTUFBTSw4Q0FBSTtBQUNWIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8taHRtbC9saWIvaGFuZGxlL3Jhdy5qcz8wY2MxIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5TdGF0ZX0gU3RhdGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuUGFyZW50fSBQYXJlbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuUmF3fSBSYXdcbiAqL1xuXG5pbXBvcnQge3RleHR9IGZyb20gJy4vdGV4dC5qcydcblxuLyoqXG4gKiBTZXJpYWxpemUgYSByYXcgbm9kZS5cbiAqXG4gKiBAcGFyYW0ge1Jhd30gbm9kZVxuICogICBOb2RlIHRvIGhhbmRsZS5cbiAqIEBwYXJhbSB7bnVtYmVyIHwgdW5kZWZpbmVkfSBpbmRleFxuICogICBJbmRleCBvZiBgbm9kZWAgaW4gYHBhcmVudC5cbiAqIEBwYXJhbSB7UGFyZW50IHwgdW5kZWZpbmVkfSBwYXJlbnRcbiAqICAgUGFyZW50IG9mIGBub2RlYC5cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZCBhYm91dCB0aGUgY3VycmVudCBzdGF0ZS5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIFNlcmlhbGl6ZWQgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHJhdyhub2RlLCBpbmRleCwgcGFyZW50LCBzdGF0ZSkge1xuICByZXR1cm4gc3RhdGUuc2V0dGluZ3MuYWxsb3dEYW5nZXJvdXNIdG1sXG4gICAgPyBub2RlLnZhbHVlXG4gICAgOiB0ZXh0KG5vZGUsIGluZGV4LCBwYXJlbnQsIHN0YXRlKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/handle/raw.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/handle/root.js":
/*!***********************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/root.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: () => (/* binding */ root)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Root} Root\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n */\n\n/**\n * Serialize a root.\n *\n * @param {Root} node\n *   Node to handle.\n * @param {number | undefined} _1\n *   Index of `node` in `parent.\n * @param {Parent | undefined} _2\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction root(node, _1, _2, state) {\n  return state.all(node)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbGliL2hhbmRsZS9yb290LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsNEJBQTRCO0FBQ3pDLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsNkJBQTZCO0FBQzFDOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsTUFBTTtBQUNqQjtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbGliL2hhbmRsZS9yb290LmpzP2M5MGQiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlJvb3R9IFJvb3RcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuUGFyZW50fSBQYXJlbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLyoqXG4gKiBTZXJpYWxpemUgYSByb290LlxuICpcbiAqIEBwYXJhbSB7Um9vdH0gbm9kZVxuICogICBOb2RlIHRvIGhhbmRsZS5cbiAqIEBwYXJhbSB7bnVtYmVyIHwgdW5kZWZpbmVkfSBfMVxuICogICBJbmRleCBvZiBgbm9kZWAgaW4gYHBhcmVudC5cbiAqIEBwYXJhbSB7UGFyZW50IHwgdW5kZWZpbmVkfSBfMlxuICogICBQYXJlbnQgb2YgYG5vZGVgLlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kIGFib3V0IHRoZSBjdXJyZW50IHN0YXRlLlxuICogQHJldHVybnMge3N0cmluZ31cbiAqICAgU2VyaWFsaXplZCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gcm9vdChub2RlLCBfMSwgXzIsIHN0YXRlKSB7XG4gIHJldHVybiBzdGF0ZS5hbGwobm9kZSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/handle/root.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/handle/text.js":
/*!***********************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/text.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var stringify_entities__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stringify-entities */ \"(ssr)/./node_modules/stringify-entities/lib/index.js\");\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').Raw} Raw\n * @typedef {import('../types.js').Text} Text\n */\n\n\n\n/**\n * Serialize a text node.\n *\n * @param {Text | Raw} node\n *   Node to handle.\n * @param {number | undefined} _\n *   Index of `node` in `parent.\n * @param {Parent | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction text(node, _, parent, state) {\n  // Check if content of `node` should be escaped.\n  return parent &&\n    parent.type === 'element' &&\n    (parent.tagName === 'script' || parent.tagName === 'style')\n    ? node.value\n    : (0,stringify_entities__WEBPACK_IMPORTED_MODULE_0__.stringifyEntities)(\n        node.value,\n        Object.assign({}, state.settings.characterReferences, {\n          subset: ['<', '&']\n        })\n      )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/handle/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   all: () => (/* binding */ all),\n/* harmony export */   toHtml: () => (/* binding */ toHtml)\n/* harmony export */ });\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var html_void_elements__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! html-void-elements */ \"(ssr)/./node_modules/html-void-elements/index.js\");\n/* harmony import */ var _handle_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./handle/index.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/index.js\");\n/**\n * @typedef {import('./types.js').Node} Node\n * @typedef {import('./types.js').Parent} Parent\n * @typedef {import('./types.js').Content} Content\n * @typedef {import('./types.js').Options} Options\n * @typedef {import('./types.js').State} State\n */\n\n\n\n\n\n/**\n * Serialize hast as HTML.\n *\n * @param {Node | Array<Content>} tree\n *   Tree to serialize.\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {string}\n *   Serialized HTML.\n */\n// eslint-disable-next-line complexity\nfunction toHtml(tree, options) {\n  const options_ = options || {}\n  const quote = options_.quote || '\"'\n  const alternative = quote === '\"' ? \"'\" : '\"'\n\n  if (quote !== '\"' && quote !== \"'\") {\n    throw new Error('Invalid quote `' + quote + '`, expected `\\'` or `\"`')\n  }\n\n  /** @type {State} */\n  const state = {\n    one,\n    all,\n    settings: {\n      omitOptionalTags: options_.omitOptionalTags || false,\n      allowParseErrors: options_.allowParseErrors || false,\n      allowDangerousCharacters: options_.allowDangerousCharacters || false,\n      quoteSmart: options_.quoteSmart || false,\n      preferUnquoted: options_.preferUnquoted || false,\n      tightAttributes: options_.tightAttributes || false,\n      upperDoctype: options_.upperDoctype || false,\n      tightDoctype: options_.tightDoctype || false,\n      bogusComments: options_.bogusComments || false,\n      tightCommaSeparatedLists: options_.tightCommaSeparatedLists || false,\n      tightSelfClosing: options_.tightSelfClosing || false,\n      collapseEmptyAttributes: options_.collapseEmptyAttributes || false,\n      allowDangerousHtml: options_.allowDangerousHtml || false,\n      voids: options_.voids || html_void_elements__WEBPACK_IMPORTED_MODULE_0__.htmlVoidElements,\n      characterReferences:\n        options_.characterReferences || options_.entities || {},\n      closeSelfClosing: options_.closeSelfClosing || false,\n      closeEmptyElements: options_.closeEmptyElements || false\n    },\n    schema: options_.space === 'svg' ? property_information__WEBPACK_IMPORTED_MODULE_1__.svg : property_information__WEBPACK_IMPORTED_MODULE_1__.html,\n    quote,\n    alternative\n  }\n\n  return state.one(\n    Array.isArray(tree) ? {type: 'root', children: tree} : tree,\n    undefined,\n    undefined\n  )\n}\n\n/**\n * Serialize a node.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Node} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parent | undefined} parent\n *   Parent of `node`.\n * @returns {string}\n *   Serialized node.\n */\nfunction one(node, index, parent) {\n  return (0,_handle_index_js__WEBPACK_IMPORTED_MODULE_2__.handle)(node, index, parent, this)\n}\n\n/**\n * Serialize all children of `parent`.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Parent | undefined} parent\n *   Parent whose children to serialize.\n * @returns {string}\n */\nfunction all(parent) {\n  /** @type {Array<string>} */\n  const results = []\n  const children = (parent && parent.children) || []\n  let index = -1\n\n  while (++index < children.length) {\n    results[index] = this.one(children[index], index, parent)\n  }\n\n  return results.join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/omission/closing.js":
/*!****************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/omission/closing.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closing: () => (/* binding */ closing)\n/* harmony export */ });\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/./node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js\");\n/* harmony import */ var _util_siblings_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/siblings.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/omission/util/siblings.js\");\n/* harmony import */ var _omission_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./omission.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/omission/omission.js\");\n/**\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Parent} Parent\n */\n\n\n\n\n\nconst closing = (0,_omission_js__WEBPACK_IMPORTED_MODULE_0__.omission)({\n  html,\n  head: headOrColgroupOrCaption,\n  body,\n  p,\n  li,\n  dt,\n  dd,\n  rt: rubyElement,\n  rp: rubyElement,\n  optgroup,\n  option,\n  menuitem,\n  colgroup: headOrColgroupOrCaption,\n  caption: headOrColgroupOrCaption,\n  thead,\n  tbody,\n  tfoot,\n  tr,\n  td: cells,\n  th: cells\n})\n\n/**\n * Macro for `</head>`, `</colgroup>`, and `</caption>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction headOrColgroupOrCaption(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index, true)\n  return (\n    !next ||\n    (next.type !== 'comment' &&\n      !(next.type === 'text' && (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__.whitespace)(next.value.charAt(0))))\n  )\n}\n\n/**\n * Whether to omit `</html>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction html(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || next.type !== 'comment'\n}\n\n/**\n * Whether to omit `</body>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction body(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || next.type !== 'comment'\n}\n\n/**\n * Whether to omit `</p>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\n// eslint-disable-next-line complexity\nfunction p(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return next\n    ? next.type === 'element' &&\n        (next.tagName === 'address' ||\n          next.tagName === 'article' ||\n          next.tagName === 'aside' ||\n          next.tagName === 'blockquote' ||\n          next.tagName === 'details' ||\n          next.tagName === 'div' ||\n          next.tagName === 'dl' ||\n          next.tagName === 'fieldset' ||\n          next.tagName === 'figcaption' ||\n          next.tagName === 'figure' ||\n          next.tagName === 'footer' ||\n          next.tagName === 'form' ||\n          next.tagName === 'h1' ||\n          next.tagName === 'h2' ||\n          next.tagName === 'h3' ||\n          next.tagName === 'h4' ||\n          next.tagName === 'h5' ||\n          next.tagName === 'h6' ||\n          next.tagName === 'header' ||\n          next.tagName === 'hgroup' ||\n          next.tagName === 'hr' ||\n          next.tagName === 'main' ||\n          next.tagName === 'menu' ||\n          next.tagName === 'nav' ||\n          next.tagName === 'ol' ||\n          next.tagName === 'p' ||\n          next.tagName === 'pre' ||\n          next.tagName === 'section' ||\n          next.tagName === 'table' ||\n          next.tagName === 'ul')\n    : !parent ||\n        // Confusing parent.\n        !(\n          parent.type === 'element' &&\n          (parent.tagName === 'a' ||\n            parent.tagName === 'audio' ||\n            parent.tagName === 'del' ||\n            parent.tagName === 'ins' ||\n            parent.tagName === 'map' ||\n            parent.tagName === 'noscript' ||\n            parent.tagName === 'video')\n        )\n}\n\n/**\n * Whether to omit `</li>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction li(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'li')\n}\n\n/**\n * Whether to omit `</dt>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction dt(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    next &&\n    next.type === 'element' &&\n    (next.tagName === 'dt' || next.tagName === 'dd')\n  )\n}\n\n/**\n * Whether to omit `</dd>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction dd(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'dt' || next.tagName === 'dd'))\n  )\n}\n\n/**\n * Whether to omit `</rt>` or `</rp>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction rubyElement(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'rp' || next.tagName === 'rt'))\n  )\n}\n\n/**\n * Whether to omit `</optgroup>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction optgroup(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'optgroup')\n}\n\n/**\n * Whether to omit `</option>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction option(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'option' || next.tagName === 'optgroup'))\n  )\n}\n\n/**\n * Whether to omit `</menuitem>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction menuitem(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'menuitem' ||\n        next.tagName === 'hr' ||\n        next.tagName === 'menu'))\n  )\n}\n\n/**\n * Whether to omit `</thead>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction thead(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    next &&\n    next.type === 'element' &&\n    (next.tagName === 'tbody' || next.tagName === 'tfoot')\n  )\n}\n\n/**\n * Whether to omit `</tbody>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tbody(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'tbody' || next.tagName === 'tfoot'))\n  )\n}\n\n/**\n * Whether to omit `</tfoot>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tfoot(_, index, parent) {\n  return !(0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n}\n\n/**\n * Whether to omit `</tr>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tr(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'tr')\n}\n\n/**\n * Whether to omit `</td>` or `</th>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction cells(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'td' || next.tagName === 'th'))\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbGliL29taXNzaW9uL2Nsb3NpbmcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ0EsYUFBYSwrQkFBK0I7QUFDNUMsYUFBYSw4QkFBOEI7QUFDM0M7O0FBRStDO0FBQ0E7QUFDVDs7QUFFL0IsZ0JBQWdCLHNEQUFRO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EsZUFBZSwrREFBWTtBQUMzQjtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0MsZ0VBQVU7QUFDMUM7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLGVBQWUsK0RBQVk7QUFDM0I7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLGVBQWUsK0RBQVk7QUFDM0I7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSwrREFBWTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxlQUFlLCtEQUFZO0FBQzNCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxlQUFlLCtEQUFZO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLGVBQWUsK0RBQVk7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EsZUFBZSwrREFBWTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxlQUFlLCtEQUFZO0FBQzNCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxlQUFlLCtEQUFZO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLGVBQWUsK0RBQVk7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLGVBQWUsK0RBQVk7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EsZUFBZSwrREFBWTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxVQUFVLCtEQUFZO0FBQ3RCOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EsZUFBZSwrREFBWTtBQUMzQjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EsZUFBZSwrREFBWTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC10by1odG1sL2xpYi9vbWlzc2lvbi9jbG9zaW5nLmpzPzJhZDIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuUGFyZW50fSBQYXJlbnRcbiAqL1xuXG5pbXBvcnQge3doaXRlc3BhY2V9IGZyb20gJ2hhc3QtdXRpbC13aGl0ZXNwYWNlJ1xuaW1wb3J0IHtzaWJsaW5nQWZ0ZXJ9IGZyb20gJy4vdXRpbC9zaWJsaW5ncy5qcydcbmltcG9ydCB7b21pc3Npb259IGZyb20gJy4vb21pc3Npb24uanMnXG5cbmV4cG9ydCBjb25zdCBjbG9zaW5nID0gb21pc3Npb24oe1xuICBodG1sLFxuICBoZWFkOiBoZWFkT3JDb2xncm91cE9yQ2FwdGlvbixcbiAgYm9keSxcbiAgcCxcbiAgbGksXG4gIGR0LFxuICBkZCxcbiAgcnQ6IHJ1YnlFbGVtZW50LFxuICBycDogcnVieUVsZW1lbnQsXG4gIG9wdGdyb3VwLFxuICBvcHRpb24sXG4gIG1lbnVpdGVtLFxuICBjb2xncm91cDogaGVhZE9yQ29sZ3JvdXBPckNhcHRpb24sXG4gIGNhcHRpb246IGhlYWRPckNvbGdyb3VwT3JDYXB0aW9uLFxuICB0aGVhZCxcbiAgdGJvZHksXG4gIHRmb290LFxuICB0cixcbiAgdGQ6IGNlbGxzLFxuICB0aDogY2VsbHNcbn0pXG5cbi8qKlxuICogTWFjcm8gZm9yIGA8L2hlYWQ+YCwgYDwvY29sZ3JvdXA+YCwgYW5kIGA8L2NhcHRpb24+YC5cbiAqXG4gKiBAcGFyYW0ge0VsZW1lbnR9IF9cbiAqICAgRWxlbWVudC5cbiAqIEBwYXJhbSB7bnVtYmVyIHwgdW5kZWZpbmVkfSBpbmRleFxuICogICBJbmRleCBvZiBlbGVtZW50IGluIHBhcmVudC5cbiAqIEBwYXJhbSB7UGFyZW50IHwgdW5kZWZpbmVkfSBwYXJlbnRcbiAqICAgUGFyZW50IG9mIGVsZW1lbnQuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqICAgV2hldGhlciB0aGUgY2xvc2luZyB0YWcgY2FuIGJlIG9taXR0ZWQuXG4gKi9cbmZ1bmN0aW9uIGhlYWRPckNvbGdyb3VwT3JDYXB0aW9uKF8sIGluZGV4LCBwYXJlbnQpIHtcbiAgY29uc3QgbmV4dCA9IHNpYmxpbmdBZnRlcihwYXJlbnQsIGluZGV4LCB0cnVlKVxuICByZXR1cm4gKFxuICAgICFuZXh0IHx8XG4gICAgKG5leHQudHlwZSAhPT0gJ2NvbW1lbnQnICYmXG4gICAgICAhKG5leHQudHlwZSA9PT0gJ3RleHQnICYmIHdoaXRlc3BhY2UobmV4dC52YWx1ZS5jaGFyQXQoMCkpKSlcbiAgKVxufVxuXG4vKipcbiAqIFdoZXRoZXIgdG8gb21pdCBgPC9odG1sPmAuXG4gKlxuICogQHBhcmFtIHtFbGVtZW50fSBfXG4gKiAgIEVsZW1lbnQuXG4gKiBAcGFyYW0ge251bWJlciB8IHVuZGVmaW5lZH0gaW5kZXhcbiAqICAgSW5kZXggb2YgZWxlbWVudCBpbiBwYXJlbnQuXG4gKiBAcGFyYW0ge1BhcmVudCB8IHVuZGVmaW5lZH0gcGFyZW50XG4gKiAgIFBhcmVudCBvZiBlbGVtZW50LlxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKiAgIFdoZXRoZXIgdGhlIGNsb3NpbmcgdGFnIGNhbiBiZSBvbWl0dGVkLlxuICovXG5mdW5jdGlvbiBodG1sKF8sIGluZGV4LCBwYXJlbnQpIHtcbiAgY29uc3QgbmV4dCA9IHNpYmxpbmdBZnRlcihwYXJlbnQsIGluZGV4KVxuICByZXR1cm4gIW5leHQgfHwgbmV4dC50eXBlICE9PSAnY29tbWVudCdcbn1cblxuLyoqXG4gKiBXaGV0aGVyIHRvIG9taXQgYDwvYm9keT5gLlxuICpcbiAqIEBwYXJhbSB7RWxlbWVudH0gX1xuICogICBFbGVtZW50LlxuICogQHBhcmFtIHtudW1iZXIgfCB1bmRlZmluZWR9IGluZGV4XG4gKiAgIEluZGV4IG9mIGVsZW1lbnQgaW4gcGFyZW50LlxuICogQHBhcmFtIHtQYXJlbnQgfCB1bmRlZmluZWR9IHBhcmVudFxuICogICBQYXJlbnQgb2YgZWxlbWVudC5cbiAqIEByZXR1cm5zIHtib29sZWFufVxuICogICBXaGV0aGVyIHRoZSBjbG9zaW5nIHRhZyBjYW4gYmUgb21pdHRlZC5cbiAqL1xuZnVuY3Rpb24gYm9keShfLCBpbmRleCwgcGFyZW50KSB7XG4gIGNvbnN0IG5leHQgPSBzaWJsaW5nQWZ0ZXIocGFyZW50LCBpbmRleClcbiAgcmV0dXJuICFuZXh0IHx8IG5leHQudHlwZSAhPT0gJ2NvbW1lbnQnXG59XG5cbi8qKlxuICogV2hldGhlciB0byBvbWl0IGA8L3A+YC5cbiAqXG4gKiBAcGFyYW0ge0VsZW1lbnR9IF9cbiAqICAgRWxlbWVudC5cbiAqIEBwYXJhbSB7bnVtYmVyIHwgdW5kZWZpbmVkfSBpbmRleFxuICogICBJbmRleCBvZiBlbGVtZW50IGluIHBhcmVudC5cbiAqIEBwYXJhbSB7UGFyZW50IHwgdW5kZWZpbmVkfSBwYXJlbnRcbiAqICAgUGFyZW50IG9mIGVsZW1lbnQuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqICAgV2hldGhlciB0aGUgY2xvc2luZyB0YWcgY2FuIGJlIG9taXR0ZWQuXG4gKi9cbi8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBjb21wbGV4aXR5XG5mdW5jdGlvbiBwKF8sIGluZGV4LCBwYXJlbnQpIHtcbiAgY29uc3QgbmV4dCA9IHNpYmxpbmdBZnRlcihwYXJlbnQsIGluZGV4KVxuICByZXR1cm4gbmV4dFxuICAgID8gbmV4dC50eXBlID09PSAnZWxlbWVudCcgJiZcbiAgICAgICAgKG5leHQudGFnTmFtZSA9PT0gJ2FkZHJlc3MnIHx8XG4gICAgICAgICAgbmV4dC50YWdOYW1lID09PSAnYXJ0aWNsZScgfHxcbiAgICAgICAgICBuZXh0LnRhZ05hbWUgPT09ICdhc2lkZScgfHxcbiAgICAgICAgICBuZXh0LnRhZ05hbWUgPT09ICdibG9ja3F1b3RlJyB8fFxuICAgICAgICAgIG5leHQudGFnTmFtZSA9PT0gJ2RldGFpbHMnIHx8XG4gICAgICAgICAgbmV4dC50YWdOYW1lID09PSAnZGl2JyB8fFxuICAgICAgICAgIG5leHQudGFnTmFtZSA9PT0gJ2RsJyB8fFxuICAgICAgICAgIG5leHQudGFnTmFtZSA9PT0gJ2ZpZWxkc2V0JyB8fFxuICAgICAgICAgIG5leHQudGFnTmFtZSA9PT0gJ2ZpZ2NhcHRpb24nIHx8XG4gICAgICAgICAgbmV4dC50YWdOYW1lID09PSAnZmlndXJlJyB8fFxuICAgICAgICAgIG5leHQudGFnTmFtZSA9PT0gJ2Zvb3RlcicgfHxcbiAgICAgICAgICBuZXh0LnRhZ05hbWUgPT09ICdmb3JtJyB8fFxuICAgICAgICAgIG5leHQudGFnTmFtZSA9PT0gJ2gxJyB8fFxuICAgICAgICAgIG5leHQudGFnTmFtZSA9PT0gJ2gyJyB8fFxuICAgICAgICAgIG5leHQudGFnTmFtZSA9PT0gJ2gzJyB8fFxuICAgICAgICAgIG5leHQudGFnTmFtZSA9PT0gJ2g0JyB8fFxuICAgICAgICAgIG5leHQudGFnTmFtZSA9PT0gJ2g1JyB8fFxuICAgICAgICAgIG5leHQudGFnTmFtZSA9PT0gJ2g2JyB8fFxuICAgICAgICAgIG5leHQudGFnTmFtZSA9PT0gJ2hlYWRlcicgfHxcbiAgICAgICAgICBuZXh0LnRhZ05hbWUgPT09ICdoZ3JvdXAnIHx8XG4gICAgICAgICAgbmV4dC50YWdOYW1lID09PSAnaHInIHx8XG4gICAgICAgICAgbmV4dC50YWdOYW1lID09PSAnbWFpbicgfHxcbiAgICAgICAgICBuZXh0LnRhZ05hbWUgPT09ICdtZW51JyB8fFxuICAgICAgICAgIG5leHQudGFnTmFtZSA9PT0gJ25hdicgfHxcbiAgICAgICAgICBuZXh0LnRhZ05hbWUgPT09ICdvbCcgfHxcbiAgICAgICAgICBuZXh0LnRhZ05hbWUgPT09ICdwJyB8fFxuICAgICAgICAgIG5leHQudGFnTmFtZSA9PT0gJ3ByZScgfHxcbiAgICAgICAgICBuZXh0LnRhZ05hbWUgPT09ICdzZWN0aW9uJyB8fFxuICAgICAgICAgIG5leHQudGFnTmFtZSA9PT0gJ3RhYmxlJyB8fFxuICAgICAgICAgIG5leHQudGFnTmFtZSA9PT0gJ3VsJylcbiAgICA6ICFwYXJlbnQgfHxcbiAgICAgICAgLy8gQ29uZnVzaW5nIHBhcmVudC5cbiAgICAgICAgIShcbiAgICAgICAgICBwYXJlbnQudHlwZSA9PT0gJ2VsZW1lbnQnICYmXG4gICAgICAgICAgKHBhcmVudC50YWdOYW1lID09PSAnYScgfHxcbiAgICAgICAgICAgIHBhcmVudC50YWdOYW1lID09PSAnYXVkaW8nIHx8XG4gICAgICAgICAgICBwYXJlbnQudGFnTmFtZSA9PT0gJ2RlbCcgfHxcbiAgICAgICAgICAgIHBhcmVudC50YWdOYW1lID09PSAnaW5zJyB8fFxuICAgICAgICAgICAgcGFyZW50LnRhZ05hbWUgPT09ICdtYXAnIHx8XG4gICAgICAgICAgICBwYXJlbnQudGFnTmFtZSA9PT0gJ25vc2NyaXB0JyB8fFxuICAgICAgICAgICAgcGFyZW50LnRhZ05hbWUgPT09ICd2aWRlbycpXG4gICAgICAgIClcbn1cblxuLyoqXG4gKiBXaGV0aGVyIHRvIG9taXQgYDwvbGk+YC5cbiAqXG4gKiBAcGFyYW0ge0VsZW1lbnR9IF9cbiAqICAgRWxlbWVudC5cbiAqIEBwYXJhbSB7bnVtYmVyIHwgdW5kZWZpbmVkfSBpbmRleFxuICogICBJbmRleCBvZiBlbGVtZW50IGluIHBhcmVudC5cbiAqIEBwYXJhbSB7UGFyZW50IHwgdW5kZWZpbmVkfSBwYXJlbnRcbiAqICAgUGFyZW50IG9mIGVsZW1lbnQuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqICAgV2hldGhlciB0aGUgY2xvc2luZyB0YWcgY2FuIGJlIG9taXR0ZWQuXG4gKi9cbmZ1bmN0aW9uIGxpKF8sIGluZGV4LCBwYXJlbnQpIHtcbiAgY29uc3QgbmV4dCA9IHNpYmxpbmdBZnRlcihwYXJlbnQsIGluZGV4KVxuICByZXR1cm4gIW5leHQgfHwgKG5leHQudHlwZSA9PT0gJ2VsZW1lbnQnICYmIG5leHQudGFnTmFtZSA9PT0gJ2xpJylcbn1cblxuLyoqXG4gKiBXaGV0aGVyIHRvIG9taXQgYDwvZHQ+YC5cbiAqXG4gKiBAcGFyYW0ge0VsZW1lbnR9IF9cbiAqICAgRWxlbWVudC5cbiAqIEBwYXJhbSB7bnVtYmVyIHwgdW5kZWZpbmVkfSBpbmRleFxuICogICBJbmRleCBvZiBlbGVtZW50IGluIHBhcmVudC5cbiAqIEBwYXJhbSB7UGFyZW50IHwgdW5kZWZpbmVkfSBwYXJlbnRcbiAqICAgUGFyZW50IG9mIGVsZW1lbnQuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqICAgV2hldGhlciB0aGUgY2xvc2luZyB0YWcgY2FuIGJlIG9taXR0ZWQuXG4gKi9cbmZ1bmN0aW9uIGR0KF8sIGluZGV4LCBwYXJlbnQpIHtcbiAgY29uc3QgbmV4dCA9IHNpYmxpbmdBZnRlcihwYXJlbnQsIGluZGV4KVxuICByZXR1cm4gKFxuICAgIG5leHQgJiZcbiAgICBuZXh0LnR5cGUgPT09ICdlbGVtZW50JyAmJlxuICAgIChuZXh0LnRhZ05hbWUgPT09ICdkdCcgfHwgbmV4dC50YWdOYW1lID09PSAnZGQnKVxuICApXG59XG5cbi8qKlxuICogV2hldGhlciB0byBvbWl0IGA8L2RkPmAuXG4gKlxuICogQHBhcmFtIHtFbGVtZW50fSBfXG4gKiAgIEVsZW1lbnQuXG4gKiBAcGFyYW0ge251bWJlciB8IHVuZGVmaW5lZH0gaW5kZXhcbiAqICAgSW5kZXggb2YgZWxlbWVudCBpbiBwYXJlbnQuXG4gKiBAcGFyYW0ge1BhcmVudCB8IHVuZGVmaW5lZH0gcGFyZW50XG4gKiAgIFBhcmVudCBvZiBlbGVtZW50LlxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKiAgIFdoZXRoZXIgdGhlIGNsb3NpbmcgdGFnIGNhbiBiZSBvbWl0dGVkLlxuICovXG5mdW5jdGlvbiBkZChfLCBpbmRleCwgcGFyZW50KSB7XG4gIGNvbnN0IG5leHQgPSBzaWJsaW5nQWZ0ZXIocGFyZW50LCBpbmRleClcbiAgcmV0dXJuIChcbiAgICAhbmV4dCB8fFxuICAgIChuZXh0LnR5cGUgPT09ICdlbGVtZW50JyAmJlxuICAgICAgKG5leHQudGFnTmFtZSA9PT0gJ2R0JyB8fCBuZXh0LnRhZ05hbWUgPT09ICdkZCcpKVxuICApXG59XG5cbi8qKlxuICogV2hldGhlciB0byBvbWl0IGA8L3J0PmAgb3IgYDwvcnA+YC5cbiAqXG4gKiBAcGFyYW0ge0VsZW1lbnR9IF9cbiAqICAgRWxlbWVudC5cbiAqIEBwYXJhbSB7bnVtYmVyIHwgdW5kZWZpbmVkfSBpbmRleFxuICogICBJbmRleCBvZiBlbGVtZW50IGluIHBhcmVudC5cbiAqIEBwYXJhbSB7UGFyZW50IHwgdW5kZWZpbmVkfSBwYXJlbnRcbiAqICAgUGFyZW50IG9mIGVsZW1lbnQuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqICAgV2hldGhlciB0aGUgY2xvc2luZyB0YWcgY2FuIGJlIG9taXR0ZWQuXG4gKi9cbmZ1bmN0aW9uIHJ1YnlFbGVtZW50KF8sIGluZGV4LCBwYXJlbnQpIHtcbiAgY29uc3QgbmV4dCA9IHNpYmxpbmdBZnRlcihwYXJlbnQsIGluZGV4KVxuICByZXR1cm4gKFxuICAgICFuZXh0IHx8XG4gICAgKG5leHQudHlwZSA9PT0gJ2VsZW1lbnQnICYmXG4gICAgICAobmV4dC50YWdOYW1lID09PSAncnAnIHx8IG5leHQudGFnTmFtZSA9PT0gJ3J0JykpXG4gIClcbn1cblxuLyoqXG4gKiBXaGV0aGVyIHRvIG9taXQgYDwvb3B0Z3JvdXA+YC5cbiAqXG4gKiBAcGFyYW0ge0VsZW1lbnR9IF9cbiAqICAgRWxlbWVudC5cbiAqIEBwYXJhbSB7bnVtYmVyIHwgdW5kZWZpbmVkfSBpbmRleFxuICogICBJbmRleCBvZiBlbGVtZW50IGluIHBhcmVudC5cbiAqIEBwYXJhbSB7UGFyZW50IHwgdW5kZWZpbmVkfSBwYXJlbnRcbiAqICAgUGFyZW50IG9mIGVsZW1lbnQuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqICAgV2hldGhlciB0aGUgY2xvc2luZyB0YWcgY2FuIGJlIG9taXR0ZWQuXG4gKi9cbmZ1bmN0aW9uIG9wdGdyb3VwKF8sIGluZGV4LCBwYXJlbnQpIHtcbiAgY29uc3QgbmV4dCA9IHNpYmxpbmdBZnRlcihwYXJlbnQsIGluZGV4KVxuICByZXR1cm4gIW5leHQgfHwgKG5leHQudHlwZSA9PT0gJ2VsZW1lbnQnICYmIG5leHQudGFnTmFtZSA9PT0gJ29wdGdyb3VwJylcbn1cblxuLyoqXG4gKiBXaGV0aGVyIHRvIG9taXQgYDwvb3B0aW9uPmAuXG4gKlxuICogQHBhcmFtIHtFbGVtZW50fSBfXG4gKiAgIEVsZW1lbnQuXG4gKiBAcGFyYW0ge251bWJlciB8IHVuZGVmaW5lZH0gaW5kZXhcbiAqICAgSW5kZXggb2YgZWxlbWVudCBpbiBwYXJlbnQuXG4gKiBAcGFyYW0ge1BhcmVudCB8IHVuZGVmaW5lZH0gcGFyZW50XG4gKiAgIFBhcmVudCBvZiBlbGVtZW50LlxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKiAgIFdoZXRoZXIgdGhlIGNsb3NpbmcgdGFnIGNhbiBiZSBvbWl0dGVkLlxuICovXG5mdW5jdGlvbiBvcHRpb24oXywgaW5kZXgsIHBhcmVudCkge1xuICBjb25zdCBuZXh0ID0gc2libGluZ0FmdGVyKHBhcmVudCwgaW5kZXgpXG4gIHJldHVybiAoXG4gICAgIW5leHQgfHxcbiAgICAobmV4dC50eXBlID09PSAnZWxlbWVudCcgJiZcbiAgICAgIChuZXh0LnRhZ05hbWUgPT09ICdvcHRpb24nIHx8IG5leHQudGFnTmFtZSA9PT0gJ29wdGdyb3VwJykpXG4gIClcbn1cblxuLyoqXG4gKiBXaGV0aGVyIHRvIG9taXQgYDwvbWVudWl0ZW0+YC5cbiAqXG4gKiBAcGFyYW0ge0VsZW1lbnR9IF9cbiAqICAgRWxlbWVudC5cbiAqIEBwYXJhbSB7bnVtYmVyIHwgdW5kZWZpbmVkfSBpbmRleFxuICogICBJbmRleCBvZiBlbGVtZW50IGluIHBhcmVudC5cbiAqIEBwYXJhbSB7UGFyZW50IHwgdW5kZWZpbmVkfSBwYXJlbnRcbiAqICAgUGFyZW50IG9mIGVsZW1lbnQuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqICAgV2hldGhlciB0aGUgY2xvc2luZyB0YWcgY2FuIGJlIG9taXR0ZWQuXG4gKi9cbmZ1bmN0aW9uIG1lbnVpdGVtKF8sIGluZGV4LCBwYXJlbnQpIHtcbiAgY29uc3QgbmV4dCA9IHNpYmxpbmdBZnRlcihwYXJlbnQsIGluZGV4KVxuICByZXR1cm4gKFxuICAgICFuZXh0IHx8XG4gICAgKG5leHQudHlwZSA9PT0gJ2VsZW1lbnQnICYmXG4gICAgICAobmV4dC50YWdOYW1lID09PSAnbWVudWl0ZW0nIHx8XG4gICAgICAgIG5leHQudGFnTmFtZSA9PT0gJ2hyJyB8fFxuICAgICAgICBuZXh0LnRhZ05hbWUgPT09ICdtZW51JykpXG4gIClcbn1cblxuLyoqXG4gKiBXaGV0aGVyIHRvIG9taXQgYDwvdGhlYWQ+YC5cbiAqXG4gKiBAcGFyYW0ge0VsZW1lbnR9IF9cbiAqICAgRWxlbWVudC5cbiAqIEBwYXJhbSB7bnVtYmVyIHwgdW5kZWZpbmVkfSBpbmRleFxuICogICBJbmRleCBvZiBlbGVtZW50IGluIHBhcmVudC5cbiAqIEBwYXJhbSB7UGFyZW50IHwgdW5kZWZpbmVkfSBwYXJlbnRcbiAqICAgUGFyZW50IG9mIGVsZW1lbnQuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqICAgV2hldGhlciB0aGUgY2xvc2luZyB0YWcgY2FuIGJlIG9taXR0ZWQuXG4gKi9cbmZ1bmN0aW9uIHRoZWFkKF8sIGluZGV4LCBwYXJlbnQpIHtcbiAgY29uc3QgbmV4dCA9IHNpYmxpbmdBZnRlcihwYXJlbnQsIGluZGV4KVxuICByZXR1cm4gKFxuICAgIG5leHQgJiZcbiAgICBuZXh0LnR5cGUgPT09ICdlbGVtZW50JyAmJlxuICAgIChuZXh0LnRhZ05hbWUgPT09ICd0Ym9keScgfHwgbmV4dC50YWdOYW1lID09PSAndGZvb3QnKVxuICApXG59XG5cbi8qKlxuICogV2hldGhlciB0byBvbWl0IGA8L3Rib2R5PmAuXG4gKlxuICogQHBhcmFtIHtFbGVtZW50fSBfXG4gKiAgIEVsZW1lbnQuXG4gKiBAcGFyYW0ge251bWJlciB8IHVuZGVmaW5lZH0gaW5kZXhcbiAqICAgSW5kZXggb2YgZWxlbWVudCBpbiBwYXJlbnQuXG4gKiBAcGFyYW0ge1BhcmVudCB8IHVuZGVmaW5lZH0gcGFyZW50XG4gKiAgIFBhcmVudCBvZiBlbGVtZW50LlxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKiAgIFdoZXRoZXIgdGhlIGNsb3NpbmcgdGFnIGNhbiBiZSBvbWl0dGVkLlxuICovXG5mdW5jdGlvbiB0Ym9keShfLCBpbmRleCwgcGFyZW50KSB7XG4gIGNvbnN0IG5leHQgPSBzaWJsaW5nQWZ0ZXIocGFyZW50LCBpbmRleClcbiAgcmV0dXJuIChcbiAgICAhbmV4dCB8fFxuICAgIChuZXh0LnR5cGUgPT09ICdlbGVtZW50JyAmJlxuICAgICAgKG5leHQudGFnTmFtZSA9PT0gJ3Rib2R5JyB8fCBuZXh0LnRhZ05hbWUgPT09ICd0Zm9vdCcpKVxuICApXG59XG5cbi8qKlxuICogV2hldGhlciB0byBvbWl0IGA8L3Rmb290PmAuXG4gKlxuICogQHBhcmFtIHtFbGVtZW50fSBfXG4gKiAgIEVsZW1lbnQuXG4gKiBAcGFyYW0ge251bWJlciB8IHVuZGVmaW5lZH0gaW5kZXhcbiAqICAgSW5kZXggb2YgZWxlbWVudCBpbiBwYXJlbnQuXG4gKiBAcGFyYW0ge1BhcmVudCB8IHVuZGVmaW5lZH0gcGFyZW50XG4gKiAgIFBhcmVudCBvZiBlbGVtZW50LlxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKiAgIFdoZXRoZXIgdGhlIGNsb3NpbmcgdGFnIGNhbiBiZSBvbWl0dGVkLlxuICovXG5mdW5jdGlvbiB0Zm9vdChfLCBpbmRleCwgcGFyZW50KSB7XG4gIHJldHVybiAhc2libGluZ0FmdGVyKHBhcmVudCwgaW5kZXgpXG59XG5cbi8qKlxuICogV2hldGhlciB0byBvbWl0IGA8L3RyPmAuXG4gKlxuICogQHBhcmFtIHtFbGVtZW50fSBfXG4gKiAgIEVsZW1lbnQuXG4gKiBAcGFyYW0ge251bWJlciB8IHVuZGVmaW5lZH0gaW5kZXhcbiAqICAgSW5kZXggb2YgZWxlbWVudCBpbiBwYXJlbnQuXG4gKiBAcGFyYW0ge1BhcmVudCB8IHVuZGVmaW5lZH0gcGFyZW50XG4gKiAgIFBhcmVudCBvZiBlbGVtZW50LlxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKiAgIFdoZXRoZXIgdGhlIGNsb3NpbmcgdGFnIGNhbiBiZSBvbWl0dGVkLlxuICovXG5mdW5jdGlvbiB0cihfLCBpbmRleCwgcGFyZW50KSB7XG4gIGNvbnN0IG5leHQgPSBzaWJsaW5nQWZ0ZXIocGFyZW50LCBpbmRleClcbiAgcmV0dXJuICFuZXh0IHx8IChuZXh0LnR5cGUgPT09ICdlbGVtZW50JyAmJiBuZXh0LnRhZ05hbWUgPT09ICd0cicpXG59XG5cbi8qKlxuICogV2hldGhlciB0byBvbWl0IGA8L3RkPmAgb3IgYDwvdGg+YC5cbiAqXG4gKiBAcGFyYW0ge0VsZW1lbnR9IF9cbiAqICAgRWxlbWVudC5cbiAqIEBwYXJhbSB7bnVtYmVyIHwgdW5kZWZpbmVkfSBpbmRleFxuICogICBJbmRleCBvZiBlbGVtZW50IGluIHBhcmVudC5cbiAqIEBwYXJhbSB7UGFyZW50IHwgdW5kZWZpbmVkfSBwYXJlbnRcbiAqICAgUGFyZW50IG9mIGVsZW1lbnQuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqICAgV2hldGhlciB0aGUgY2xvc2luZyB0YWcgY2FuIGJlIG9taXR0ZWQuXG4gKi9cbmZ1bmN0aW9uIGNlbGxzKF8sIGluZGV4LCBwYXJlbnQpIHtcbiAgY29uc3QgbmV4dCA9IHNpYmxpbmdBZnRlcihwYXJlbnQsIGluZGV4KVxuICByZXR1cm4gKFxuICAgICFuZXh0IHx8XG4gICAgKG5leHQudHlwZSA9PT0gJ2VsZW1lbnQnICYmXG4gICAgICAobmV4dC50YWdOYW1lID09PSAndGQnIHx8IG5leHQudGFnTmFtZSA9PT0gJ3RoJykpXG4gIClcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/omission/closing.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/omission/omission.js":
/*!*****************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/omission/omission.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   omission: () => (/* binding */ omission)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').OmitHandle} OmitHandle\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * Factory to check if a given node can have a tag omitted.\n *\n * @param {Record<string, OmitHandle>} handlers\n *   Omission handlers, where each key is a tag name, and each value is the\n *   corresponding handler.\n * @returns {OmitHandle}\n *   Whether to omit a tag of an element.\n */\nfunction omission(handlers) {\n  return omit\n\n  /**\n   * Check if a given node can have a tag omitted.\n   *\n   * @type {OmitHandle}\n   */\n  function omit(node, index, parent) {\n    return (\n      own.call(handlers, node.tagName) &&\n      handlers[node.tagName](node, index, parent)\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbGliL29taXNzaW9uL29taXNzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsa0NBQWtDO0FBQy9DOztBQUVBLGNBQWM7O0FBRWQ7QUFDQTtBQUNBO0FBQ0EsV0FBVyw0QkFBNEI7QUFDdkM7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbGliL29taXNzaW9uL29taXNzaW9uLmpzP2EzMTYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLk9taXRIYW5kbGV9IE9taXRIYW5kbGVcbiAqL1xuXG5jb25zdCBvd24gPSB7fS5oYXNPd25Qcm9wZXJ0eVxuXG4vKipcbiAqIEZhY3RvcnkgdG8gY2hlY2sgaWYgYSBnaXZlbiBub2RlIGNhbiBoYXZlIGEgdGFnIG9taXR0ZWQuXG4gKlxuICogQHBhcmFtIHtSZWNvcmQ8c3RyaW5nLCBPbWl0SGFuZGxlPn0gaGFuZGxlcnNcbiAqICAgT21pc3Npb24gaGFuZGxlcnMsIHdoZXJlIGVhY2gga2V5IGlzIGEgdGFnIG5hbWUsIGFuZCBlYWNoIHZhbHVlIGlzIHRoZVxuICogICBjb3JyZXNwb25kaW5nIGhhbmRsZXIuXG4gKiBAcmV0dXJucyB7T21pdEhhbmRsZX1cbiAqICAgV2hldGhlciB0byBvbWl0IGEgdGFnIG9mIGFuIGVsZW1lbnQuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBvbWlzc2lvbihoYW5kbGVycykge1xuICByZXR1cm4gb21pdFxuXG4gIC8qKlxuICAgKiBDaGVjayBpZiBhIGdpdmVuIG5vZGUgY2FuIGhhdmUgYSB0YWcgb21pdHRlZC5cbiAgICpcbiAgICogQHR5cGUge09taXRIYW5kbGV9XG4gICAqL1xuICBmdW5jdGlvbiBvbWl0KG5vZGUsIGluZGV4LCBwYXJlbnQpIHtcbiAgICByZXR1cm4gKFxuICAgICAgb3duLmNhbGwoaGFuZGxlcnMsIG5vZGUudGFnTmFtZSkgJiZcbiAgICAgIGhhbmRsZXJzW25vZGUudGFnTmFtZV0obm9kZSwgaW5kZXgsIHBhcmVudClcbiAgICApXG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/omission/omission.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/omission/opening.js":
/*!****************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/omission/opening.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   opening: () => (/* binding */ opening)\n/* harmony export */ });\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/./node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js\");\n/* harmony import */ var _util_siblings_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/siblings.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/omission/util/siblings.js\");\n/* harmony import */ var _closing_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./closing.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/omission/closing.js\");\n/* harmony import */ var _omission_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./omission.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/omission/omission.js\");\n/**\n * @typedef {import('../types.js').Element} Element\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').Content} Content\n */\n\n\n\n\n\n\nconst opening = (0,_omission_js__WEBPACK_IMPORTED_MODULE_0__.omission)({\n  html,\n  head,\n  body,\n  colgroup,\n  tbody\n})\n\n/**\n * Whether to omit `<html>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction html(node) {\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1)\n  return !head || head.type !== 'comment'\n}\n\n/**\n * Whether to omit `<head>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction head(node) {\n  const children = node.children\n  /** @type {Array<string>} */\n  const seen = []\n  let index = -1\n\n  while (++index < children.length) {\n    const child = children[index]\n    if (\n      child.type === 'element' &&\n      (child.tagName === 'title' || child.tagName === 'base')\n    ) {\n      if (seen.includes(child.tagName)) return false\n      seen.push(child.tagName)\n    }\n  }\n\n  return children.length > 0\n}\n\n/**\n * Whether to omit `<body>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction body(node) {\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1, true)\n\n  return (\n    !head ||\n    (head.type !== 'comment' &&\n      !(head.type === 'text' && (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__.whitespace)(head.value.charAt(0))) &&\n      !(\n        head.type === 'element' &&\n        (head.tagName === 'meta' ||\n          head.tagName === 'link' ||\n          head.tagName === 'script' ||\n          head.tagName === 'style' ||\n          head.tagName === 'template')\n      ))\n  )\n}\n\n/**\n * Whether to omit `<colgroup>`.\n * The spec describes some logic for the opening tag, but it’s easier to\n * implement in the closing tag, to the same effect, so we handle it there\n * instead.\n *\n * @param {Element} node\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction colgroup(node, index, parent) {\n  const previous = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingBefore)(parent, index)\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1, true)\n\n  // Previous colgroup was already omitted.\n  if (\n    parent &&\n    previous &&\n    previous.type === 'element' &&\n    previous.tagName === 'colgroup' &&\n    (0,_closing_js__WEBPACK_IMPORTED_MODULE_3__.closing)(previous, parent.children.indexOf(previous), parent)\n  ) {\n    return false\n  }\n\n  return head && head.type === 'element' && head.tagName === 'col'\n}\n\n/**\n * Whether to omit `<tbody>`.\n *\n * @param {Element} node\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parent | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction tbody(node, index, parent) {\n  const previous = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingBefore)(parent, index)\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1)\n\n  // Previous table section was already omitted.\n  if (\n    parent &&\n    previous &&\n    previous.type === 'element' &&\n    (previous.tagName === 'thead' || previous.tagName === 'tbody') &&\n    (0,_closing_js__WEBPACK_IMPORTED_MODULE_3__.closing)(previous, parent.children.indexOf(previous), parent)\n  ) {\n    return false\n  }\n\n  return head && head.type === 'element' && head.tagName === 'tr'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/omission/opening.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/omission/util/siblings.js":
/*!**********************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/omission/util/siblings.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   siblingAfter: () => (/* binding */ siblingAfter),\n/* harmony export */   siblingBefore: () => (/* binding */ siblingBefore)\n/* harmony export */ });\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/./node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js\");\n/**\n * @typedef {import('../../types.js').Parent} Parent\n * @typedef {import('../../types.js').Content} Content\n */\n\n\n\nconst siblingAfter = siblings(1)\nconst siblingBefore = siblings(-1)\n\n/**\n * Factory to check siblings in a direction.\n *\n * @param {number} increment\n */\nfunction siblings(increment) {\n  return sibling\n\n  /**\n   * Find applicable siblings in a direction.\n   *\n   * @param {Parent | null | undefined} parent\n   * @param {number | null | undefined} index\n   * @param {boolean | null | undefined} [includeWhitespace=false]\n   * @returns {Content}\n   */\n  function sibling(parent, index, includeWhitespace) {\n    const siblings = parent ? parent.children : []\n    let offset = (index || 0) + increment\n    let next = siblings && siblings[offset]\n\n    if (!includeWhitespace) {\n      while (next && (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_0__.whitespace)(next)) {\n        offset += increment\n        next = siblings[offset]\n      }\n    }\n\n    return next\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/omission/util/siblings.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   whitespace: () => (/* binding */ whitespace)\n/* harmony export */ });\n/**\n * Check if the given value is *inter-element whitespace*.\n *\n * @param {unknown} thing\n *   Thing to check (typically `Node` or `string`).\n * @returns {boolean}\n *   Whether the `value` is inter-element whitespace (`boolean`): consisting of\n *   zero or more of space, tab (`\\t`), line feed (`\\n`), carriage return\n *   (`\\r`), or form feed (`\\f`).\n *   If a node is passed it must be a `Text` node, whose `value` field is\n *   checked.\n */\nfunction whitespace(thing) {\n  /** @type {string} */\n  const value =\n    // @ts-expect-error looks like a node.\n    thing && typeof thing === 'object' && thing.type === 'text'\n      ? // @ts-expect-error looks like a text.\n        thing.value || ''\n      : thing\n\n  // HTML whitespace expression.\n  // See <https://infra.spec.whatwg.org/#ascii-whitespace>.\n  return typeof value === 'string' && value.replace(/[ \\t\\n\\f\\r]/g, '') === ''\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC13aGl0ZXNwYWNlL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCxhQUFhLFFBQVE7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8taHRtbC9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXdoaXRlc3BhY2UvaW5kZXguanM/ZmQyZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENoZWNrIGlmIHRoZSBnaXZlbiB2YWx1ZSBpcyAqaW50ZXItZWxlbWVudCB3aGl0ZXNwYWNlKi5cbiAqXG4gKiBAcGFyYW0ge3Vua25vd259IHRoaW5nXG4gKiAgIFRoaW5nIHRvIGNoZWNrICh0eXBpY2FsbHkgYE5vZGVgIG9yIGBzdHJpbmdgKS5cbiAqIEByZXR1cm5zIHtib29sZWFufVxuICogICBXaGV0aGVyIHRoZSBgdmFsdWVgIGlzIGludGVyLWVsZW1lbnQgd2hpdGVzcGFjZSAoYGJvb2xlYW5gKTogY29uc2lzdGluZyBvZlxuICogICB6ZXJvIG9yIG1vcmUgb2Ygc3BhY2UsIHRhYiAoYFxcdGApLCBsaW5lIGZlZWQgKGBcXG5gKSwgY2FycmlhZ2UgcmV0dXJuXG4gKiAgIChgXFxyYCksIG9yIGZvcm0gZmVlZCAoYFxcZmApLlxuICogICBJZiBhIG5vZGUgaXMgcGFzc2VkIGl0IG11c3QgYmUgYSBgVGV4dGAgbm9kZSwgd2hvc2UgYHZhbHVlYCBmaWVsZCBpc1xuICogICBjaGVja2VkLlxuICovXG5leHBvcnQgZnVuY3Rpb24gd2hpdGVzcGFjZSh0aGluZykge1xuICAvKiogQHR5cGUge3N0cmluZ30gKi9cbiAgY29uc3QgdmFsdWUgPVxuICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgbG9va3MgbGlrZSBhIG5vZGUuXG4gICAgdGhpbmcgJiYgdHlwZW9mIHRoaW5nID09PSAnb2JqZWN0JyAmJiB0aGluZy50eXBlID09PSAndGV4dCdcbiAgICAgID8gLy8gQHRzLWV4cGVjdC1lcnJvciBsb29rcyBsaWtlIGEgdGV4dC5cbiAgICAgICAgdGhpbmcudmFsdWUgfHwgJydcbiAgICAgIDogdGhpbmdcblxuICAvLyBIVE1MIHdoaXRlc3BhY2UgZXhwcmVzc2lvbi5cbiAgLy8gU2VlIDxodHRwczovL2luZnJhLnNwZWMud2hhdHdnLm9yZy8jYXNjaWktd2hpdGVzcGFjZT4uXG4gIHJldHVybiB0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnICYmIHZhbHVlLnJlcGxhY2UoL1sgXFx0XFxuXFxmXFxyXS9nLCAnJykgPT09ICcnXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/node_modules/hast-util-whitespace/index.js\n");

/***/ })

};
;