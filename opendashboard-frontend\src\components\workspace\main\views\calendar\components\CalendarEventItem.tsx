import React, { useMemo, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { CalendarEvent } from '@/typings/page';
import { ColorInfo } from '@/utils/color';
import { 
  formatEventTime, 
  isInstantEvent, 
  getEventSize 
} from '@/utils/dateUtils';
import { useDraggable } from '@dnd-kit/core';

export const CalendarEventItem = ({
  event,
  style,
  onClick,
  onContextMenu,
  view = 'month', // Default to month view if not specified
  isDragging,
  showTitle = true,
  isDraggable = true
}: {
  event: CalendarEvent;
  style?: React.CSSProperties;
  onClick: (e: React.MouseEvent) => void;
  onContextMenu?: (e: React.MouseEvent) => void;
  view?: 'day' | 'week' | 'month';
  isDragging?: boolean;
  showTitle?: boolean;
  isDraggable?: boolean;
}) => {
  const dragRef = useRef<HTMLDivElement>(null);
  const { attributes, listeners, setNodeRef, isDragging: dndIsDragging } = useDraggable({
    id: `event-${event.id}`,
    data: {
      type: 'event',
      payload: event,
    },
    disabled: !isDraggable, // Disable dragging if not draggable
  });

  // Combine external isDragging with internal dndIsDragging
  const combinedIsDragging = isDragging || dndIsDragging;

  // Memoize event calculations
  const eventDetails = useMemo(() => {
    const start = new Date(event.start);
    const eventHeight = style?.height ? parseInt(style.height.toString().replace('px', '')) : null;
    
    return { 
      start, 
      eventSize: getEventSize(eventHeight),
      isInstant: isInstantEvent(event),
      formattedTime: formatEventTime(start, view, { shortFormat: true })
    };
  }, [event, style, view]);

  // Memoize styling
  const eventStyles = useMemo(() => {
    const denimColorInfo = ColorInfo('Denim');
    
    // Extract RGB values from the rgba string and make it fully opaque
    const rgbMatch = denimColorInfo.bg.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
    const opaqueBackground = rgbMatch 
      ? `rgb(${rgbMatch[1]}, ${rgbMatch[2]}, ${rgbMatch[3]})`
      : denimColorInfo.bg;
    
    return {
      ...style,
      backgroundColor: opaqueBackground,
      minHeight: view === 'month' ? '24px' : '30px',
      marginBottom: view === 'month' ? '4px' : '0px',
      // Add subtle shadow for better visual depth
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',
      opacity: combinedIsDragging ? 0.5 : isDraggable ? 1 : 0.7, // Slightly transparent for non-draggable
    };
  }, [style, view, combinedIsDragging, isDraggable]);

  // Memoize classes
  const eventClasses = useMemo(() => {
    // Month view or small events
    if (view === 'month' || eventDetails.eventSize === 'small') {
      return {
        baseClasses: cn(
          "rounded-md select-none text-black text-xs overflow-hidden",
          !combinedIsDragging && isDraggable && "cursor-pointer",
          !combinedIsDragging && !isDraggable && "cursor-default",
          "p-1",
        ),
        containerClasses: cn(
          "flex items-center space-x-1",
          "flex-nowrap"
        ),
        titleClasses: cn(
          "font-medium truncate leading-tight text-xs overflow-hidden",
          "max-w-[70%]"
        ),
        timeClasses: cn(
          "opacity-75 text-xs flex-shrink-0",
          "text-[0.65rem]"
        )
      };
    }

    // Day and Week views for medium/large events
    return {
      baseClasses: cn(
        "rounded-md select-none text-black text-xs overflow-hidden",
        !combinedIsDragging && isDraggable && "cursor-pointer",
        !combinedIsDragging && !isDraggable && "cursor-default",
        "p-2",
      ),
      containerClasses: cn(
        "flex flex-col",
        "space-y-0.5"
      ),
      titleClasses: cn(
        "font-medium truncate leading-tight text-xs overflow-hidden"
      ),
      timeClasses: cn(
        "opacity-75 text-xs"
      )
    };
  }, [eventDetails, view, combinedIsDragging, isDraggable]);

  // Render event content based on view and size
  const renderEventContent = () => {
    // Month view or small events
    if (view === 'month' || eventDetails.eventSize === 'small') {
      return (
        <div className={eventClasses.containerClasses}>
          {showTitle && (
            <span className={eventClasses.titleClasses}>{event.title}</span>
          )}
          {showTitle && eventDetails.formattedTime && (
            <span className={eventClasses.timeClasses}>
              {eventDetails.formattedTime}
            </span>
          )}
        </div>
      );
    }

    // Day and Week views for medium/large events
    return (
      <div className={eventClasses.containerClasses}>
        {showTitle && (
          <div className={eventClasses.titleClasses}>{event.title}</div>
        )}
        {showTitle && eventDetails.formattedTime && (
          <div className={eventClasses.timeClasses}>
            {eventDetails.formattedTime}
          </div>
        )}
      </div>
    );
  };

  return (
    <div
      id={`event-${event.id}`}
      ref={(node) => {
        setNodeRef(node);
        (dragRef as React.MutableRefObject<HTMLDivElement | null>).current = node;
      }}
      style={{
        ...eventStyles,
        zIndex: combinedIsDragging ? 1000 : 'auto' // Ensure dragged item is on top
      }}
      className={eventClasses.baseClasses}
      onClick={onClick}
      onContextMenu={onContextMenu}
      {...(isDraggable ? { ...listeners, ...attributes } : {})}
    >
      {renderEventContent()}
    </div>
  );
};









// import React, { useMemo } from 'react';
// import { cn } from '@/lib/utils';
// import { CalendarEvent } from '@/typings/page';
// import { ColorInfo } from '@/utils/color';
// import { 
//   formatEventTime, 
//   isInstantEvent, 
//   getEventSize 
// } from '@/utils/dateUtils';

// export const CalendarEventItem = ({
//   event,
//   style,
//   onClick,
//   onContextMenu,
//   view = 'month' // Default to month view if not specified
// }: {
//   event: CalendarEvent;
//   style?: React.CSSProperties;
//   onClick: (e: React.MouseEvent) => void;
//   onContextMenu?: (e: React.MouseEvent) => void;
//   view?: 'day' | 'week' | 'month';
// }) => {
//   // Memoize event calculations
//   const eventDetails = useMemo(() => {
//     const start = new Date(event.start);
//     const eventHeight = style?.height ? parseInt(style.height.toString().replace('px', '')) : null;
    
//     return { 
//       start, 
//       eventSize: getEventSize(eventHeight),
//       isInstant: isInstantEvent(event),
//       formattedTime: formatEventTime(start, view, { shortFormat: true })
//     };
//   }, [event, style, view]);

//   // Memoize styling
//   const eventStyles = useMemo(() => {
//     const denimColorInfo = ColorInfo('Denim');
//     return {
//       ...style,
//       backgroundColor: denimColorInfo.bg,
//       minHeight: '30px',
//     };
//   }, [style]);

//   // Memoize classes
//   const eventClasses = useMemo(() => {
//     // Month view or small events
//     if (view === 'month' || eventDetails.eventSize === 'small') {
//       return {
//         baseClasses: cn(
//           "rounded-md cursor-pointer select-none text-black text-xs overflow-hidden",
//           eventDetails.eventSize === 'small' ? "p-1" : "p-2"
//         ),
//         containerClasses: cn(
//           "flex items-center space-x-1",
//           "flex-nowrap"
//         ),
//         titleClasses: cn(
//           "font-medium truncate leading-tight text-xs overflow-hidden flex-grow",
//           "max-w-[70%]"
//         ),
//         timeClasses: cn(
//           "opacity-75 text-xs flex-shrink-0",
//           "text-[0.65rem]"
//         )
//       };
//     }

//     // Day and Week views for medium/large events
//     return {
//       baseClasses: cn(
//         "rounded-md cursor-pointer select-none text-black text-xs overflow-hidden",
//         eventDetails.eventSize === 'small' ? "p-1" : "p-2"
//       ),
//       containerClasses: cn(
//         "flex flex-col",
//         eventDetails.eventSize === 'small' ? "space-y-0" : "space-y-0.5"
//       ),
//       titleClasses: cn(
//         "font-medium truncate leading-tight text-xs overflow-hidden",
//         eventDetails.eventSize === 'small' ? "mb-0" : ""
//       ),
//       timeClasses: cn(
//         "opacity-75 text-xs",
//         eventDetails.eventSize === 'small' ? "text-[0.65rem] leading-none" : ""
//       )
//     };
//   }, [eventDetails, view]);

//   // Render event content based on view and size
//   const renderEventContent = () => {
//     // Month view or small events
//     if (view === 'month' || eventDetails.eventSize === 'small') {
//       return (
//         <div className={eventClasses.containerClasses}>
//           <span className={eventClasses.titleClasses}>{event.title}</span>
//           <span className={eventClasses.timeClasses}>
//             {eventDetails.formattedTime}
//           </span>
//         </div>
//       );
//     }

//     // Day and Week views for medium/large events
//     return (
//       <div className={eventClasses.containerClasses}>
//         <div className={eventClasses.titleClasses}>{event.title}</div>
//         <div className={eventClasses.timeClasses}>
//           {eventDetails.formattedTime}
//         </div>
//       </div>
//     );
//   };

//   return (
//     <div
//       style={eventStyles}
//       className={eventClasses.baseClasses}
//       onClick={onClick}
//       onContextMenu={onContextMenu}
//     >
//       {renderEventContent()}
//     </div>
//   );
// }; 