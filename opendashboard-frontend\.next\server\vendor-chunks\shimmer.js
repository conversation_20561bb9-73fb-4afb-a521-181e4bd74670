"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/shimmer";
exports.ids = ["vendor-chunks/shimmer"];
exports.modules = {

/***/ "(ssr)/./node_modules/shimmer/index.js":
/*!***************************************!*\
  !*** ./node_modules/shimmer/index.js ***!
  \***************************************/
/***/ ((module) => {

eval("\n\nfunction isFunction (funktion) {\n  return typeof funktion === 'function'\n}\n\n// Default to complaining loudly when things don't go according to plan.\nvar logger = console.error.bind(console)\n\n// Sets a property on an object, preserving its enumerability.\n// This function assumes that the property is already writable.\nfunction defineProperty (obj, name, value) {\n  var enumerable = !!obj[name] && obj.propertyIsEnumerable(name)\n  Object.defineProperty(obj, name, {\n    configurable: true,\n    enumerable: enumerable,\n    writable: true,\n    value: value\n  })\n}\n\n// Keep initialization idempotent.\nfunction shimmer (options) {\n  if (options && options.logger) {\n    if (!isFunction(options.logger)) logger(\"new logger isn't a function, not replacing\")\n    else logger = options.logger\n  }\n}\n\nfunction wrap (nodule, name, wrapper) {\n  if (!nodule || !nodule[name]) {\n    logger('no original function ' + name + ' to wrap')\n    return\n  }\n\n  if (!wrapper) {\n    logger('no wrapper function')\n    logger((new Error()).stack)\n    return\n  }\n\n  if (!isFunction(nodule[name]) || !isFunction(wrapper)) {\n    logger('original object and wrapper must be functions')\n    return\n  }\n\n  var original = nodule[name]\n  var wrapped = wrapper(original, name)\n\n  defineProperty(wrapped, '__original', original)\n  defineProperty(wrapped, '__unwrap', function () {\n    if (nodule[name] === wrapped) defineProperty(nodule, name, original)\n  })\n  defineProperty(wrapped, '__wrapped', true)\n\n  defineProperty(nodule, name, wrapped)\n  return wrapped\n}\n\nfunction massWrap (nodules, names, wrapper) {\n  if (!nodules) {\n    logger('must provide one or more modules to patch')\n    logger((new Error()).stack)\n    return\n  } else if (!Array.isArray(nodules)) {\n    nodules = [nodules]\n  }\n\n  if (!(names && Array.isArray(names))) {\n    logger('must provide one or more functions to wrap on modules')\n    return\n  }\n\n  nodules.forEach(function (nodule) {\n    names.forEach(function (name) {\n      wrap(nodule, name, wrapper)\n    })\n  })\n}\n\nfunction unwrap (nodule, name) {\n  if (!nodule || !nodule[name]) {\n    logger('no function to unwrap.')\n    logger((new Error()).stack)\n    return\n  }\n\n  if (!nodule[name].__unwrap) {\n    logger('no original to unwrap to -- has ' + name + ' already been unwrapped?')\n  } else {\n    return nodule[name].__unwrap()\n  }\n}\n\nfunction massUnwrap (nodules, names) {\n  if (!nodules) {\n    logger('must provide one or more modules to patch')\n    logger((new Error()).stack)\n    return\n  } else if (!Array.isArray(nodules)) {\n    nodules = [nodules]\n  }\n\n  if (!(names && Array.isArray(names))) {\n    logger('must provide one or more functions to unwrap on modules')\n    return\n  }\n\n  nodules.forEach(function (nodule) {\n    names.forEach(function (name) {\n      unwrap(nodule, name)\n    })\n  })\n}\n\nshimmer.wrap = wrap\nshimmer.massWrap = massWrap\nshimmer.unwrap = unwrap\nshimmer.massUnwrap = massUnwrap\n\nmodule.exports = shimmer\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/shimmer/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/shimmer/index.js":
/*!***************************************!*\
  !*** ./node_modules/shimmer/index.js ***!
  \***************************************/
/***/ ((module) => {

eval("\n\nfunction isFunction (funktion) {\n  return typeof funktion === 'function'\n}\n\n// Default to complaining loudly when things don't go according to plan.\nvar logger = console.error.bind(console)\n\n// Sets a property on an object, preserving its enumerability.\n// This function assumes that the property is already writable.\nfunction defineProperty (obj, name, value) {\n  var enumerable = !!obj[name] && obj.propertyIsEnumerable(name)\n  Object.defineProperty(obj, name, {\n    configurable: true,\n    enumerable: enumerable,\n    writable: true,\n    value: value\n  })\n}\n\n// Keep initialization idempotent.\nfunction shimmer (options) {\n  if (options && options.logger) {\n    if (!isFunction(options.logger)) logger(\"new logger isn't a function, not replacing\")\n    else logger = options.logger\n  }\n}\n\nfunction wrap (nodule, name, wrapper) {\n  if (!nodule || !nodule[name]) {\n    logger('no original function ' + name + ' to wrap')\n    return\n  }\n\n  if (!wrapper) {\n    logger('no wrapper function')\n    logger((new Error()).stack)\n    return\n  }\n\n  if (!isFunction(nodule[name]) || !isFunction(wrapper)) {\n    logger('original object and wrapper must be functions')\n    return\n  }\n\n  var original = nodule[name]\n  var wrapped = wrapper(original, name)\n\n  defineProperty(wrapped, '__original', original)\n  defineProperty(wrapped, '__unwrap', function () {\n    if (nodule[name] === wrapped) defineProperty(nodule, name, original)\n  })\n  defineProperty(wrapped, '__wrapped', true)\n\n  defineProperty(nodule, name, wrapped)\n  return wrapped\n}\n\nfunction massWrap (nodules, names, wrapper) {\n  if (!nodules) {\n    logger('must provide one or more modules to patch')\n    logger((new Error()).stack)\n    return\n  } else if (!Array.isArray(nodules)) {\n    nodules = [nodules]\n  }\n\n  if (!(names && Array.isArray(names))) {\n    logger('must provide one or more functions to wrap on modules')\n    return\n  }\n\n  nodules.forEach(function (nodule) {\n    names.forEach(function (name) {\n      wrap(nodule, name, wrapper)\n    })\n  })\n}\n\nfunction unwrap (nodule, name) {\n  if (!nodule || !nodule[name]) {\n    logger('no function to unwrap.')\n    logger((new Error()).stack)\n    return\n  }\n\n  if (!nodule[name].__unwrap) {\n    logger('no original to unwrap to -- has ' + name + ' already been unwrapped?')\n  } else {\n    return nodule[name].__unwrap()\n  }\n}\n\nfunction massUnwrap (nodules, names) {\n  if (!nodules) {\n    logger('must provide one or more modules to patch')\n    logger((new Error()).stack)\n    return\n  } else if (!Array.isArray(nodules)) {\n    nodules = [nodules]\n  }\n\n  if (!(names && Array.isArray(names))) {\n    logger('must provide one or more functions to unwrap on modules')\n    return\n  }\n\n  nodules.forEach(function (nodule) {\n    names.forEach(function (name) {\n      unwrap(nodule, name)\n    })\n  })\n}\n\nshimmer.wrap = wrap\nshimmer.massWrap = massWrap\nshimmer.unwrap = unwrap\nshimmer.massUnwrap = massUnwrap\n\nmodule.exports = shimmer\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/shimmer/index.js\n");

/***/ })

};
;