"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx":
/*!****************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/index.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarView: function() { return /* binding */ CalendarView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _providers_template__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/template */ \"(app-pages-browser)/./src/providers/template.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/workspace/main/views/table */ \"(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/providers/stackedPeek */ \"(app-pages-browser)/./src/providers/stackedPeek.tsx\");\n/* harmony import */ var _components_DayView__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./components/DayView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx\");\n/* harmony import */ var _components_WeekView__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./components/WeekView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\");\n/* harmony import */ var _components_MonthView__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./components/MonthView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\");\n/* harmony import */ var _components_CalendarEventItem__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./components/CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/custom-ui/customSelect */ \"(app-pages-browser)/./src/components/custom-ui/customSelect.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _dnd_kit_modifiers__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @dnd-kit/modifiers */ \"(app-pages-browser)/./node_modules/@dnd-kit/modifiers/dist/modifiers.esm.js\");\n/* harmony import */ var _components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./components/CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInMilliseconds/index.js\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInDays/index.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Custom hook to track previous value\nconst usePrevious = (value)=>{\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        ref.current = value;\n    });\n    return ref.current;\n};\n_s(usePrevious, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nconst CalendarView = (props)=>{\n    _s1();\n    const { databaseStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace)();\n    const { definition } = props;\n    const { accessLevel } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage)();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_16__.useScreenSize)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_17__.useMaybeRecord)();\n    const maybeShared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared)();\n    const maybeTemplate = (0,_providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate)();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [viewType, setViewType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"week\");\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSideCalendar, setShowSideCalendar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!isMobile);\n    const [activeDragData, setActiveDragData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const savedScrollTop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const pointerCoordinates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const isInRecordTab = !!maybeRecord;\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const databaseId = definition.databaseId;\n    const database = databaseStore[definition.databaseId];\n    const isPublishedView = !!maybeShared;\n    const editable = !definition.lockContent && !isPublishedView && !!accessLevel;\n    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    const { createRecords, updateRecordValues, setPeekRecordId, peekRecordId, refreshDatabase, deleteRecords } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews)();\n    const { sorts, filter, search } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering)();\n    const { selectedIds, setSelectedIds } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewSelection)();\n    const prevPeekRecordId = usePrevious(peekRecordId);\n    const { openRecord } = (0,_providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__.useStackedPeek)();\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.PointerSensor, {\n        activationConstraint: {\n            distance: 8\n        }\n    }), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.TouchSensor, {\n        activationConstraint: {\n            delay: 150,\n            tolerance: 5\n        }\n    }));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n        const container = document.getElementById(containerId);\n        if (container) {\n            requestAnimationFrame(()=>{\n                container.scrollTop = savedScrollTop.current;\n            });\n        }\n    }, [\n        selectedEvent,\n        viewType\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowSideCalendar(!isMobile);\n    }, [\n        isMobile\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only refresh if the peek view was open and is now closed.\n        if (prevPeekRecordId && !peekRecordId) {\n            console.log(\"Peek view was closed, refreshing calendar data\");\n            refreshDatabase(definition.databaseId);\n        }\n    }, [\n        peekRecordId,\n        prevPeekRecordId,\n        definition.databaseId,\n        refreshDatabase\n    ]);\n    if (!database) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__.PageLoader, {\n        size: \"full\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 143,\n        columnNumber: 25\n    }, undefined);\n    const getEvents = ()=>{\n        var _workspace_workspaceMember, _database_database;\n        if (!database) return [];\n        const { rows } = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.filterAndSortRecords)(database, members, databaseStore, definition.filter || {\n            match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All,\n            conditions: []\n        }, filter, sorts.length ? sorts : definition.sorts || [], (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspaceMember = workspace.workspaceMember) === null || _workspace_workspaceMember === void 0 ? void 0 : _workspace_workspaceMember.userId) || \"\", (database === null || database === void 0 ? void 0 : (_database_database = database.database) === null || _database_database === void 0 ? void 0 : _database_database.id) || \"\");\n        const filteredRows = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.searchFilteredRecords)(search || \"\", rows);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        return filteredRows.map((row)=>{\n            const startValue = row.processedRecord.processedRecordValues[definition.eventStartColumnId];\n            let startDate;\n            if (startValue && typeof startValue === \"string\") {\n                startDate = new Date(startValue);\n            } else {\n                startDate = new Date();\n            }\n            let endDate;\n            if (definition.eventEndColumnId) {\n                const endValue = row.processedRecord.processedRecordValues[definition.eventEndColumnId];\n                if (endValue && typeof endValue === \"string\") {\n                    endDate = new Date(endValue);\n                } else {\n                    endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n                }\n            } else {\n                endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n            }\n            const title = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getRecordTitle)(row.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts);\n            return {\n                id: row.id,\n                title,\n                start: startDate,\n                end: endDate,\n                record: row.record,\n                processedRecord: row.processedRecord\n            };\n        });\n    };\n    const getFilteredEvents = ()=>{\n        const baseEvents = getEvents();\n        if (!searchTerm.trim()) {\n            return baseEvents;\n        }\n        return baseEvents.filter((event)=>{\n            const searchLower = searchTerm.toLowerCase();\n            return event.title.toLowerCase().includes(searchLower);\n        });\n    };\n    const onDragStart = (event)=>{\n        if (event.active.data.current) {\n            var _event_active_rect_current_translated, _event_active_rect_current, _event_active_rect_current_translated1, _event_active_rect_current_translated2;\n            const initialPointerY = pointerCoordinates.current.y;\n            var _event_active_rect_current_translated_top;\n            const initialEventTop = (_event_active_rect_current_translated_top = (_event_active_rect_current = event.active.rect.current) === null || _event_active_rect_current === void 0 ? void 0 : (_event_active_rect_current_translated = _event_active_rect_current.translated) === null || _event_active_rect_current_translated === void 0 ? void 0 : _event_active_rect_current_translated.top) !== null && _event_active_rect_current_translated_top !== void 0 ? _event_active_rect_current_translated_top : 0;\n            const grabOffsetY = initialPointerY - initialEventTop;\n            // Get the exact dimensions of the original event\n            const originalWidth = ((_event_active_rect_current_translated1 = event.active.rect.current.translated) === null || _event_active_rect_current_translated1 === void 0 ? void 0 : _event_active_rect_current_translated1.width) || 200;\n            const originalHeight = ((_event_active_rect_current_translated2 = event.active.rect.current.translated) === null || _event_active_rect_current_translated2 === void 0 ? void 0 : _event_active_rect_current_translated2.height) || 30;\n            // Use the exact original dimensions for the drag overlay\n            // This ensures the dragged event looks identical to the original\n            setActiveDragData({\n                ...event.active.data.current,\n                grabOffsetY,\n                width: originalWidth,\n                height: originalHeight,\n                originalWidth,\n                originalHeight\n            });\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"touchmove\", handleTouchMove);\n        }\n    };\n    const onDragEnd = async (param)=>{\n        let { active, over } = param;\n        document.removeEventListener(\"mousemove\", handleMouseMove);\n        document.removeEventListener(\"touchmove\", handleTouchMove);\n        setActiveDragData(null);\n        console.log(\"[DragEnd] Fired\", {\n            active,\n            over\n        });\n        if (!over || !active || !canEditData || active.id === over.id) {\n            console.log(\"[DragEnd] Exiting: Invalid drop conditions.\", {\n                over,\n                active,\n                canEditData\n            });\n            return;\n        }\n        const activeData = active.data.current;\n        const overData = over.data.current;\n        if (!activeData || !overData) {\n            console.log(\"[DragEnd] Exiting: Missing active or over data.\");\n            return;\n        }\n        console.log(\"[DragEnd] Active Data:\", activeData);\n        console.log(\"[DragEnd] Over Data:\", overData);\n        const { payload, type } = activeData;\n        const eventToUpdate = type === \"segment\" ? payload.originalEvent : payload;\n        console.log(\"[DragEnd] Event to Update:\", eventToUpdate);\n        const originalStart = new Date(eventToUpdate.start);\n        const originalEnd = new Date(eventToUpdate.end);\n        const duration = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(originalEnd, originalStart);\n        console.log(\"[DragEnd] Original Times:\", {\n            originalStart,\n            originalEnd,\n            duration\n        });\n        let newStart;\n        if (overData.type.startsWith(\"allday\")) {\n            console.log(\"[DragEnd] Logic branch: All-day\");\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__[\"default\"])(originalStart, dayDifference);\n            // For all-day drops, normalize the time to the start of the day\n            newStart.setHours(0, 0, 0, 0);\n        } else if (overData.type.startsWith(\"timeslot\")) {\n            console.log(\"[DragEnd] Logic branch: Timeslot\");\n            newStart = new Date(overData.date);\n            // Enhanced Minute-level precision calculation\n            const grabOffsetY = activeData.grabOffsetY || 0;\n            const slotTop = over.rect.top;\n            const pointerY = pointerCoordinates.current.y;\n            // Adjust pointer position based on initial grab point\n            const adjustedPointerY = pointerY - grabOffsetY;\n            const offsetY = adjustedPointerY - slotTop;\n            const slotHeight = over.rect.height; // Should be 60px\n            const minutesFromTop = offsetY / slotHeight * 60;\n            // Snap to nearest 5-minute interval\n            const snappedMinutes = Math.round(minutesFromTop / 5) * 5;\n            const newMinutes = Math.max(0, Math.min(59, snappedMinutes));\n            newStart.setHours(overData.hour, newMinutes, originalStart.getSeconds(), originalStart.getMilliseconds());\n            console.log(\"[DragEnd] Minute Precision Calc:\", {\n                slotTop,\n                pointerY,\n                offsetY,\n                slotHeight,\n                minutesFromTop,\n                newMinutes\n            });\n        } else if (overData.type === \"daycell\") {\n            console.log(\"[DragEnd] Logic branch: Day Cell (Month)\");\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__[\"default\"])(originalStart, dayDifference);\n        } else {\n            console.log(\"[DragEnd] Exiting: Invalid drop target type.\");\n            return; // Invalid drop target\n        }\n        const newEnd = new Date(newStart.getTime() + duration);\n        console.log(\"[DragEnd] New Times:\", {\n            newStart,\n            newEnd\n        });\n        const recordId = eventToUpdate.record.id;\n        const newValues = {\n            [definition.eventStartColumnId]: newStart.toISOString(),\n            ...definition.eventEndColumnId && {\n                [definition.eventEndColumnId]: newEnd.toISOString()\n            }\n        };\n        console.log(\"[DragEnd] Update Payload:\", {\n            recordId,\n            newValues\n        });\n        try {\n            console.log(\"[DragEnd] Calling updateRecordValues...\");\n            await updateRecordValues(definition.databaseId, [\n                recordId\n            ], newValues);\n            console.log(\"[DragEnd] Update successful.\");\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.success('Event \"'.concat(eventToUpdate.title, '\" updated.'));\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to update event.\");\n            console.error(\"[DragEnd] Error updating event:\", error);\n        }\n    };\n    const handleMouseMove = (event)=>{\n        pointerCoordinates.current = {\n            x: event.clientX,\n            y: event.clientY\n        };\n    };\n    const handleTouchMove = (event)=>{\n        const touch = event.touches[0];\n        pointerCoordinates.current = {\n            x: touch.clientX,\n            y: touch.clientY\n        };\n    };\n    const events = getFilteredEvents();\n    const goToToday = ()=>setSelectedDate(new Date());\n    const goToPrevious = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__[\"default\"])(prevDate, -1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const goToNext = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__[\"default\"])(prevDate, 1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const handleRequestCreateEvent = function(date) {\n        let useSystemTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (useSystemTime) {\n            const now = new Date();\n            const newDate = new Date(date);\n            newDate.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());\n            handleCreateEvent(newDate);\n        } else {\n            handleCreateEvent(date);\n        }\n    };\n    const handleCreateEvent = async function() {\n        let date = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : new Date();\n        if (!canEditData) return;\n        const startTime = new Date(date);\n        const endTime = new Date(startTime.getTime() + (definition.defaultDuration || 30) * 60000);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        try {\n            const recordValues = {\n                [definition.eventStartColumnId]: startTime.toISOString(),\n                ...definition.eventEndColumnId && {\n                    [definition.eventEndColumnId]: endTime.toISOString()\n                }\n            };\n            if (titleColOpts.titleColId) {\n                recordValues[titleColOpts.titleColId] = \"New Event\";\n            }\n            const result = await createRecords(definition.databaseId, [\n                recordValues\n            ]);\n            if (result && result.records && result.records.length > 0) {\n                const newRecordId = result.records[0].id;\n                if (newRecordId) {\n                    await refreshDatabase(definition.databaseId);\n                    setPeekRecordId(newRecordId);\n                    sonner__WEBPACK_IMPORTED_MODULE_14__.toast.success(\"New event created\");\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Error accessing the new event\");\n                }\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to create event properly\");\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to create event\");\n        }\n    };\n    const handleEventClick = (event)=>{\n        if (event && event.id) {\n            const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n            const container = document.getElementById(containerId);\n            if (container) {\n                savedScrollTop.current = container.scrollTop;\n            }\n            openRecord(event.id, event.record.databaseId);\n            setSelectedEvent(event.id);\n        }\n    };\n    const getHeaderDateDisplay = ()=>{\n        switch(viewType){\n            case \"day\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_36__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n            case \"week\":\n                return \"\".concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_36__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__[\"default\"])(selectedDate, -selectedDate.getDay()), \"MMM d\"), \" - \").concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_36__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__[\"default\"])(selectedDate, 6 - selectedDate.getDay()), \"MMM d, yyyy\"));\n            case \"month\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_36__[\"default\"])(selectedDate, \"MMMM yyyy\");\n            default:\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_36__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n        }\n    };\n    const handleEventDelete = async (event)=>{\n        if (!canEditData) return;\n        try {\n            await deleteRecords(database.database.id, [\n                event.record.id\n            ]);\n        } catch (error) {\n            console.error(\"Error deleting event:\", error);\n        }\n    };\n    const viewTypeOptions = [\n        {\n            id: \"day\",\n            value: \"day\",\n            title: \"Day\",\n            data: \"day\"\n        },\n        {\n            id: \"week\",\n            value: \"week\",\n            title: \"Week\",\n            data: \"week\"\n        },\n        {\n            id: \"month\",\n            value: \"month\",\n            title: \"Month\",\n            data: \"month\"\n        }\n    ];\n    const selectedViewOption = viewType === \"day\" ? [\n        \"day\"\n    ] : viewType === \"week\" ? [\n        \"week\"\n    ] : [\n        \"month\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"border-b border-neutral-300 bg-white\", isInRecordTab && \"py-1\"),\n                children: [\n                    isMobile ? /* Mobile Header Layout */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"p-2\", isInRecordTab && \"py-1\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black truncate flex-1 mr-2\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(!showSideCalendar),\n                                        className: \"rounded-full h-8 px-3 text-xs text-black hover:bg-gray-50\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 12\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToToday,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: \"Today\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToPrevious,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleLeftIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 18\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToNext,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleRightIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 18\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                                options: viewTypeOptions,\n                                                selectedIds: selectedViewOption,\n                                                onChange: (selected)=>{\n                                                    if (selected.length > 0) {\n                                                        setViewType(selected[0]);\n                                                    }\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black w-20\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                placeholder: \"View\",\n                                                hideSearch: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 20\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 18\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 12\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 474,\n                        columnNumber: 10\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"flex items-center justify-between px-4\", isInRecordTab ? \"py-1\" : \"py-2\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: goToToday,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToPrevious,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleLeftIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToNext,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleRightIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 14\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 561,\n                                columnNumber: 10\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                placeholder: \"Search events...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-48 pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                                className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                        options: viewTypeOptions,\n                                        selectedIds: selectedViewOption,\n                                        onChange: (selected)=>{\n                                            if (selected.length > 0) {\n                                                setViewType(selected[0]);\n                                            }\n                                        },\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6 w-20\" : \"h-8 w-28\"),\n                                        placeholder: \"View\",\n                                        hideSearch: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black gap-1\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Add Event\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 35\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 630,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 600,\n                                columnNumber: 10\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 557,\n                        columnNumber: 8\n                    }, undefined),\n                    isMobile && !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                    placeholder: \"Search events...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"w-full pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 12\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                    className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 654,\n                                    columnNumber: 12\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                            lineNumber: 647,\n                            columnNumber: 10\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 646,\n                        columnNumber: 8\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 468,\n                columnNumber: 6\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex min-h-0\",\n                children: [\n                    showSideCalendar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"flex-none bg-white\", isMobile ? \"w-full absolute z-50 backdrop-blur-sm h-full shadow-lg\" : \"w-fit border-r border-neutral-300\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 border-b border-neutral-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 10\n                                    }, undefined),\n                                    isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(false),\n                                        className: \"rounded-full h-8 w-8 p-1 text-black hover:bg-neutral-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Close\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            \"\\xd7\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 667,\n                                columnNumber: 8\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                mode: \"single\",\n                                selected: selectedDate,\n                                onSelect: (date)=>{\n                                    if (date) {\n                                        setSelectedDate(date);\n                                        if (isMobile) {\n                                            setShowSideCalendar(false);\n                                        }\n                                    }\n                                },\n                                className: \"rounded-md border-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 680,\n                                columnNumber: 8\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 663,\n                        columnNumber: 6\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DndContext, {\n                        sensors: sensors,\n                        onDragStart: onDragStart,\n                        onDragEnd: onDragEnd,\n                        modifiers: [\n                            _dnd_kit_modifiers__WEBPACK_IMPORTED_MODULE_26__.restrictToWindowEdges\n                        ],\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                \"data-calendar-content\": \"true\",\n                                children: [\n                                    viewType === \"day\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DayView__WEBPACK_IMPORTED_MODULE_19__.DayView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"week\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WeekView__WEBPACK_IMPORTED_MODULE_20__.WeekView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 717,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"month\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MonthView__WEBPACK_IMPORTED_MODULE_21__.MonthView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: (date)=>handleRequestCreateEvent(date, true),\n                                        canEditData: canEditData,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 731,\n                                        columnNumber: 7\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 702,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DragOverlay, {\n                                dropAnimation: null,\n                                children: activeDragData && activeDragData.type === \"segment\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_27__.CalendarEventSegment, {\n                                    segment: activeDragData.payload,\n                                    view: viewType === \"day\" ? \"day\" : \"week\",\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.originalWidth,\n                                        height: activeDragData.originalHeight,\n                                        opacity: 0.8,\n                                        boxShadow: \"0 8px 25px rgba(0, 0, 0, 0.2)\",\n                                        zIndex: 9999\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 747,\n                                    columnNumber: 11\n                                }, undefined) : activeDragData && activeDragData.type === \"event\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CalendarEventItem__WEBPACK_IMPORTED_MODULE_22__.CalendarEventItem, {\n                                    event: activeDragData.payload,\n                                    view: \"month\",\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.originalWidth,\n                                        height: activeDragData.originalHeight,\n                                        opacity: 0.8,\n                                        boxShadow: \"0 8px 25px rgba(0, 0, 0, 0.2)\",\n                                        zIndex: 9999\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 760,\n                                    columnNumber: 11\n                                }, undefined) : null\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 745,\n                                columnNumber: 6\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 696,\n                        columnNumber: 6\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 661,\n                columnNumber: 4\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 467,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CalendarView, \"gC+3ORgbOSOWj17lR2zBDE1vWrs=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace,\n        _providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage,\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_16__.useScreenSize,\n        _providers_record__WEBPACK_IMPORTED_MODULE_17__.useMaybeRecord,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared,\n        _providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewSelection,\n        usePrevious,\n        _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__.useStackedPeek,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors\n    ];\n});\n_c = CalendarView;\nvar _c;\n$RefreshReg$(_c, \"CalendarView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx\n"));

/***/ })

});