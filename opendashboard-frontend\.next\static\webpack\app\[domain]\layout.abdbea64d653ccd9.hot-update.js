"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/layout",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/buttonGroup/Editor.tsx":
/*!******************************************************************************************!*\
  !*** ./src/components/workspace/main/views/table/renderer/fields/buttonGroup/Editor.tsx ***!
  \******************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ActionConfigEditor: function() { return /* binding */ ActionConfigEditor; },\n/* harmony export */   ButtonEditor: function() { return /* binding */ ButtonEditor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _buttonGroup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../buttonGroup */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/buttonGroup.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_methods_string__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! opendb-app-db-utils/lib/methods/string */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/methods/string.js\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var _providers_user__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/providers/user */ \"(app-pages-browser)/./src/providers/user.tsx\");\n/* harmony import */ var _components_custom_ui_dndSortable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/custom-ui/dndSortable */ \"(app-pages-browser)/./src/components/custom-ui/dndSortable.tsx\");\n/* harmony import */ var _utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/buttonActionHelpers */ \"(app-pages-browser)/./src/utils/buttonActionHelpers.ts\");\n/* harmony import */ var _components_workspace_main_common_FieldRenderer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/workspace/main/common/FieldRenderer */ \"(app-pages-browser)/./src/components/workspace/main/common/FieldRenderer.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_methods_compare__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! opendb-app-db-utils/lib/methods/compare */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/methods/compare.js\");\n/* harmony import */ var _components_custom_ui_compareOperatorSelect__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/custom-ui/compareOperatorSelect */ \"(app-pages-browser)/./src/components/custom-ui/compareOperatorSelect.tsx\");\n/* harmony import */ var _components_custom_ui_mentionInput__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/custom-ui/mentionInput */ \"(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_viewFilter__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/workspace/main/views/common/viewFilter */ \"(app-pages-browser)/./src/components/workspace/main/views/common/viewFilter.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _api_workspace__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/api/workspace */ \"(app-pages-browser)/./src/api/workspace.ts\");\n/* harmony import */ var _api_workflow__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/api/workflow */ \"(app-pages-browser)/./src/api/workflow.ts\");\n/* harmony import */ var _typings_workflow__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/typings/workflow */ \"(app-pages-browser)/./src/typings/workflow.ts\");\n/* harmony import */ var _components_workspace_main_common_UpdateRecordEditor__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/workspace/main/common/UpdateRecordEditor */ \"(app-pages-browser)/./src/components/workspace/main/common/UpdateRecordEditor.tsx\");\n/* harmony import */ var _components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/custom-ui/customSelect */ \"(app-pages-browser)/./src/components/custom-ui/customSelect.tsx\");\n/* harmony import */ var _components_custom_ui_inputWithEnter__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/custom-ui/inputWithEnter */ \"(app-pages-browser)/./src/components/custom-ui/inputWithEnter.tsx\");\n/* __next_internal_client_entry_do_not_use__ ActionConfigEditor,ButtonEditor auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ActionConfigEditor = (param)=>{\n    let { action, onUpdate, onDelete, databaseId, workspaceSenders, workflows } = param;\n    var _databaseStore_databaseId_database_definition, _databaseStore_databaseId_database, _databaseStore_databaseId, _availableActions_find;\n    _s();\n    const { databaseStore } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace)();\n    // Initialize state with action values\n    const [actionType, setActionType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(action.actionType || \"openUrl\");\n    const [props, setProps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(action.props || (0,_utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_10__.getDefaultPropsForActionType)(action.actionType || \"openUrl\"));\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const buttonAction = (0,_utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_10__.getButtonActionDefinition)(actionType);\n    const availableActions = (0,_utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_10__.getAvailableActions)();\n    // Check if action is ready (all required props filled)\n    const isActionReady = ()=>{\n        return (0,_utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_10__.isActionConfigReady)({\n            actionType,\n            props\n        });\n    };\n    const variableKeyMap = {};\n    if (databaseId && (databaseStore === null || databaseStore === void 0 ? void 0 : (_databaseStore_databaseId = databaseStore[databaseId]) === null || _databaseStore_databaseId === void 0 ? void 0 : (_databaseStore_databaseId_database = _databaseStore_databaseId.database) === null || _databaseStore_databaseId_database === void 0 ? void 0 : (_databaseStore_databaseId_database_definition = _databaseStore_databaseId_database.definition) === null || _databaseStore_databaseId_database_definition === void 0 ? void 0 : _databaseStore_databaseId_database_definition.columnsMap)) {\n        const columnsMap = databaseStore[databaseId].database.definition.columnsMap;\n        Object.entries(columnsMap).forEach((param)=>{\n            let [columnId, column] = param;\n            variableKeyMap[\"column.\".concat(columnId)] = {\n                label: \"Column: \".concat(column.title),\n                description: \"Column value for \".concat(column.title),\n                tag: \"{{column.\".concat(columnId, \"}}\")\n            };\n        });\n    }\n    variableKeyMap[\"person.current\"] = {\n        label: \"Current User\",\n        description: \"Current user ID\",\n        tag: \"{{person.current}}\"\n    };\n    variableKeyMap[\"person.email\"] = {\n        label: \"Current User Email\",\n        description: \"Current user email address\",\n        tag: \"{{person.email}}\"\n    };\n    variableKeyMap[\"person.name\"] = {\n        label: \"Current User Name\",\n        description: \"Current user name\",\n        tag: \"{{person.name}}\"\n    };\n    const updateProp = (key, value)=>{\n        const newProps = {\n            ...props,\n            [key]: value\n        };\n        setProps(newProps);\n        const updatedAction = {\n            ...action,\n            actionType: actionType,\n            props: newProps\n        };\n        onUpdate(updatedAction);\n    };\n    const handleActionTypeChange = (value)=>{\n        const newActionType = value;\n        setActionType(newActionType);\n        let newProps = (0,_utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_10__.getDefaultPropsForActionType)(newActionType);\n        if (newActionType === \"updateRecord\") {\n            newProps.updates = [];\n        }\n        // Update local state\n        setProps(newProps);\n        const updatedAction = {\n            ...action,\n            actionType: newActionType,\n            props: newProps\n        };\n        onUpdate(updatedAction);\n    };\n    if (!buttonAction) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border rounded-md mb-3 bg-white overflow-visible\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-3 \".concat(!isExpanded ? \"cursor-pointer hover:bg-gray-50\" : \"\"),\n                onClick: !isExpanded ? ()=>setIsExpanded(true) : undefined,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            !isActionReady() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.CircleExclamationIcon, {\n                                className: \"size-3 text-destructive inline-block\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 25\n                            }, undefined),\n                            isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_21__.CustomSelect, {\n                                options: availableActions.map((param)=>{\n                                    let { value, label } = param;\n                                    return {\n                                        id: value,\n                                        value: value,\n                                        title: label,\n                                        data: {\n                                            value,\n                                            label\n                                        }\n                                    };\n                                }),\n                                selectedIds: [\n                                    actionType\n                                ],\n                                onChange: (selectedIds)=>{\n                                    if (selectedIds.length > 0) {\n                                        handleActionTypeChange(selectedIds[0]);\n                                    }\n                                },\n                                className: \"h-8 border border-solid rounded-md w-40 text-xs\",\n                                placeholder: \"Select action type\",\n                                hideSearch: false,\n                                itemSelectionRender: (key, index, item, isSelected, handleSelection)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: (e)=>{\n                                            e.preventDefault();\n                                            handleSelection(item.id, !isSelected);\n                                        },\n                                        className: \"text-xs gap-2 rounded-none p-2 mb-1 w-full justify-start \".concat(isSelected ? \"bg-neutral-100\" : \"hover:bg-gray-50\"),\n                                        children: [\n                                            (0,_buttonGroup__WEBPACK_IMPORTED_MODULE_5__.getActionIcon)(item.data.value),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"truncate flex-1 text-left\",\n                                                children: item.data.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 37\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 33\n                                    }, void 0),\n                                itemRender: (key, index, item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-xs\",\n                                        children: [\n                                            (0,_buttonGroup__WEBPACK_IMPORTED_MODULE_5__.getActionIcon)(item.data.value),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"truncate\",\n                                                children: item.data.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 37\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 33\n                                    }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 25\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    (0,_buttonGroup__WEBPACK_IMPORTED_MODULE_5__.getActionIcon)(actionType),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: ((_availableActions_find = availableActions.find((a)=>a.value === actionType)) === null || _availableActions_find === void 0 ? void 0 : _availableActions_find.label) || actionType\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>setIsExpanded(false),\n                                className: \"h-8 text-xs border border-solid rounded-md\",\n                                children: \"Done\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onDelete,\n                                className: \"p-1 h-6 w-6 hover:bg-gray-50 text-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    className: \"h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                lineNumber: 114,\n                columnNumber: 13\n            }, undefined),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3 mt-3 p-3\",\n                children: Object.entries(buttonAction.props || {}).map((param)=>{\n                    let [key, prop] = param;\n                    if (key === \"databaseId\") {\n                        const dbOptions = Object.entries(databaseStore || {}).map((param)=>{\n                            let [dbId, dbData] = param;\n                            var _dbData_database;\n                            return {\n                                id: dbId,\n                                value: dbId,\n                                title: ((_dbData_database = dbData.database) === null || _dbData_database === void 0 ? void 0 : _dbData_database.name) || dbId,\n                                data: dbData.database || {}\n                            };\n                        });\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                    className: \"text-xs font-medium\",\n                                    children: prop.displayName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_21__.CustomSelect, {\n                                    selectedIds: [\n                                        props[key] || databaseId\n                                    ],\n                                    options: dbOptions,\n                                    onChange: (selected)=>updateProp(key, selected[0]),\n                                    placeholder: \"Select database\",\n                                    className: \"text-xs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 33\n                                }, undefined),\n                                prop.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: prop.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, key, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 29\n                        }, undefined);\n                    }\n                    if (key === \"workflowId\") {\n                        const workflowOptions = workflows.map((workflow)=>({\n                                id: workflow.id.toString(),\n                                value: workflow.id.toString(),\n                                title: workflow.name || \"Workflow \".concat(workflow.id),\n                                data: workflow\n                            }));\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                    className: \"text-xs font-medium\",\n                                    children: prop.displayName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_21__.CustomSelect, {\n                                    selectedIds: [\n                                        props[key]\n                                    ],\n                                    options: workflowOptions,\n                                    onChange: (selected)=>updateProp(key, selected[0]),\n                                    placeholder: \"Select workflow\",\n                                    className: \"text-xs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 33\n                                }, undefined),\n                                prop.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: prop.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, key, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 29\n                        }, undefined);\n                    }\n                    if (key === \"senderId\") {\n                        const senderOptions = workspaceSenders.map((sender)=>({\n                                id: sender.id.toString(),\n                                value: sender.id.toString(),\n                                title: \"\".concat(sender.name, \" (\").concat(sender.email, \")\").concat(sender.isVerified ? \" ✓\" : \" (Unverified)\"),\n                                data: sender\n                            }));\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                    className: \"text-xs font-medium\",\n                                    children: prop.displayName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_21__.CustomSelect, {\n                                    selectedIds: [\n                                        props[key]\n                                    ],\n                                    options: senderOptions,\n                                    onChange: (selected)=>updateProp(key, selected[0]),\n                                    placeholder: \"Select sender\",\n                                    className: \"text-xs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 33\n                                }, undefined),\n                                prop.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: prop.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, key, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 29\n                        }, undefined);\n                    }\n                    if (actionType === \"updateRecord\" && key === \"updates\") {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                    className: \"text-xs font-medium\",\n                                    children: prop.displayName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_common_UpdateRecordEditor__WEBPACK_IMPORTED_MODULE_20__.UpdateRecordEditor, {\n                                    databaseId: props.databaseId || databaseId,\n                                    updates: props[key] || [],\n                                    onUpdate: (newUpdates)=>updateProp(key, newUpdates),\n                                    variableKeyMap: variableKeyMap\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 33\n                                }, undefined),\n                                prop.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: prop.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, key, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 29\n                        }, undefined);\n                    }\n                    const FieldRenderer = (0,_components_workspace_main_common_FieldRenderer__WEBPACK_IMPORTED_MODULE_11__.getRendererForFieldType)(prop.type);\n                    if (FieldRenderer) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FieldRenderer, {\n                            value: props[key],\n                            onChange: (value)=>updateProp(key, value),\n                            propKey: key,\n                            prop: prop,\n                            disabled: false,\n                            tagOptionsMap: variableKeyMap\n                        }, key, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 29\n                        }, undefined);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500\",\n                        children: [\n                            \"Unsupported field type: \",\n                            prop.type\n                        ]\n                    }, key, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 25\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                lineNumber: 194,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n        lineNumber: 113,\n        columnNumber: 9\n    }, undefined);\n};\n_s(ActionConfigEditor, \"w+RcJ//2D4jciNxQQXvJnXfUhQ0=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace\n    ];\n});\n_c = ActionConfigEditor;\nconst ConditionEditor = (param)=>{\n    let { condition, databaseId, onChange, onDelete, variableKeyMap } = param;\n    var _database_database_definition, _database_database;\n    _s1();\n    const { databaseStore } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace)();\n    const database = databaseStore === null || databaseStore === void 0 ? void 0 : databaseStore[databaseId];\n    const columns = (database === null || database === void 0 ? void 0 : (_database_database = database.database) === null || _database_database === void 0 ? void 0 : (_database_database_definition = _database_database.definition) === null || _database_database_definition === void 0 ? void 0 : _database_database_definition.columnsMap) || {};\n    const column = columns[condition.columnId];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-2 p-2 border rounded-md bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_mentionInput__WEBPACK_IMPORTED_MODULE_14__.MentionInput, {\n                    keyMap: variableKeyMap,\n                    value: condition.columnId,\n                    onChange: (value)=>onChange({\n                            ...condition,\n                            columnId: value\n                        }),\n                    placeholder: \"Field\",\n                    className: \"text-xs\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                lineNumber: 335,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_compareOperatorSelect__WEBPACK_IMPORTED_MODULE_13__.CompareOperatorSelect, {\n                    value: condition.op,\n                    onChange: (op)=>onChange({\n                            ...condition,\n                            op\n                        }),\n                    fieldType: column === null || column === void 0 ? void 0 : column.type,\n                    className: \"text-xs\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                    lineNumber: 346,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                lineNumber: 345,\n                columnNumber: 13\n            }, undefined),\n            !_components_custom_ui_compareOperatorSelect__WEBPACK_IMPORTED_MODULE_13__.SingleCompare.includes(condition.op) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_mentionInput__WEBPACK_IMPORTED_MODULE_14__.MentionInput, {\n                    keyMap: variableKeyMap,\n                    value: Array.isArray(condition.value) ? condition.value.join(\", \") : condition.value || \"\",\n                    onChange: (value)=>onChange({\n                            ...condition,\n                            value\n                        }),\n                    placeholder: \"Value\",\n                    className: \"text-xs\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                    lineNumber: 356,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                lineNumber: 355,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                variant: \"ghost\",\n                size: \"sm\",\n                onClick: onDelete,\n                className: \"p-1 h-6 w-6 hover:bg-red-50 text-red-600\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                    className: \"h-3 w-3\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                    lineNumber: 372,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                lineNumber: 366,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n        lineNumber: 334,\n        columnNumber: 9\n    }, undefined);\n};\n_s1(ConditionEditor, \"zLfzQwUsJ7pM1rk65noV1yPnwM8=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace\n    ];\n});\n_c1 = ConditionEditor;\nconst ButtonEditor = /*#__PURE__*/ _s2(react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(_c2 = _s2((param, ref)=>{\n    let { initialValue, button, onSave, onUpdate, onCancel, databaseId, contextIsRecord = false } = param;\n    var _databaseStore_databaseId_database_definition, _databaseStore_databaseId_database, _databaseStore_databaseId, _databaseStore_databaseId1, _databaseStore_databaseId2;\n    _s2();\n    const buttonData = initialValue || button;\n    const saveHandler = onSave || onUpdate;\n    const { databaseStore, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace)();\n    const { token } = (0,_providers_user__WEBPACK_IMPORTED_MODULE_8__.useAuth)();\n    const [label, setLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((buttonData === null || buttonData === void 0 ? void 0 : buttonData.label) || \"\");\n    const [actions, setActions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((buttonData === null || buttonData === void 0 ? void 0 : buttonData.actions) || []);\n    const [visibleIfConditions, setVisibleIfConditions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((buttonData === null || buttonData === void 0 ? void 0 : buttonData.visibleIf) || (buttonData === null || buttonData === void 0 ? void 0 : buttonData.visibleIfConditions) || []);\n    const [enabledIfConditions, setEnabledIfConditions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((buttonData === null || buttonData === void 0 ? void 0 : buttonData.enabledIf) || (buttonData === null || buttonData === void 0 ? void 0 : buttonData.enabledIfConditions) || []);\n    const [visibleIfFilter, setVisibleIfFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((buttonData === null || buttonData === void 0 ? void 0 : buttonData.visibleIfFilter) || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_16__.Match.All\n    });\n    const [enabledIfFilter, setEnabledIfFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((buttonData === null || buttonData === void 0 ? void 0 : buttonData.enabledIfFilter) || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_16__.Match.All\n    });\n    const [workspaceSenders, setWorkspaceSenders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [workflows, setWorkflows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load workspace senders\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadSenders = async ()=>{\n            if (!token || !workspace) return;\n            try {\n                var _response_data_data, _response_data;\n                const response = await (0,_api_workspace__WEBPACK_IMPORTED_MODULE_17__.getWorkspaceSenders)(token.token, workspace.workspace.id);\n                if ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : (_response_data_data = _response_data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.senders) {\n                    setWorkspaceSenders(response.data.data.senders);\n                }\n            } catch (error) {\n                console.error(\"Failed to load workspace senders:\", error);\n            }\n        };\n        loadSenders();\n    }, [\n        token,\n        workspace\n    ]);\n    // Load workflows\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadWorkflows = async ()=>{\n            if (!token || !workspace) return;\n            try {\n                var _response_data_data, _response_data;\n                const response = await (0,_api_workflow__WEBPACK_IMPORTED_MODULE_18__.getWorkflows)(token.token, workspace.workspace.id, {\n                    triggerTypes: [\n                        _typings_workflow__WEBPACK_IMPORTED_MODULE_19__.WorkflowTriggerType.OnDemand_Callable\n                    ]\n                });\n                if ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : (_response_data_data = _response_data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.workflows) {\n                    setWorkflows(response.data.data.workflows);\n                }\n            } catch (error) {\n                console.error(\"Failed to load workflows:\", error);\n            }\n        };\n        loadWorkflows();\n    }, [\n        token,\n        workspace\n    ]);\n    const handleSave = ()=>{\n        if (!saveHandler) return;\n        const finalButton = {\n            ...buttonData,\n            id: (buttonData === null || buttonData === void 0 ? void 0 : buttonData.id) || (0,opendb_app_db_utils_lib_methods_string__WEBPACK_IMPORTED_MODULE_6__.generateUUID)(),\n            label,\n            actions,\n            visibleIf: visibleIfConditions,\n            enabledIf: enabledIfConditions,\n            visibleIfFilter: visibleIfFilter,\n            enabledIfFilter: enabledIfFilter,\n            isReady: true\n        };\n        saveHandler(finalButton);\n    };\n    react__WEBPACK_IMPORTED_MODULE_1___default().useImperativeHandle(ref, ()=>({\n            save: handleSave\n        }));\n    // Create variable key map for mention inputs\n    const variableKeyMap = {};\n    if (databaseId && (databaseStore === null || databaseStore === void 0 ? void 0 : (_databaseStore_databaseId = databaseStore[databaseId]) === null || _databaseStore_databaseId === void 0 ? void 0 : (_databaseStore_databaseId_database = _databaseStore_databaseId.database) === null || _databaseStore_databaseId_database === void 0 ? void 0 : (_databaseStore_databaseId_database_definition = _databaseStore_databaseId_database.definition) === null || _databaseStore_databaseId_database_definition === void 0 ? void 0 : _databaseStore_databaseId_database_definition.columnsMap)) {\n        const columnsMap = databaseStore[databaseId].database.definition.columnsMap;\n        Object.entries(columnsMap).forEach((param)=>{\n            let [columnId, column] = param;\n            variableKeyMap[\"column.\".concat(columnId)] = {\n                label: \"Column: \".concat(column.title),\n                description: \"Column value for \".concat(column.title),\n                tag: \"{{column.\".concat(columnId, \"}}\")\n            };\n        });\n    }\n    variableKeyMap[\"person.current\"] = {\n        label: \"Current User\",\n        description: \"Current user ID\",\n        tag: \"{{person.current}}\"\n    };\n    variableKeyMap[\"person.email\"] = {\n        label: \"Current User Email\",\n        description: \"Current user email address\",\n        tag: \"{{person.email}}\"\n    };\n    variableKeyMap[\"person.name\"] = {\n        label: \"Current User Name\",\n        description: \"Current user name\",\n        tag: \"{{person.name}}\"\n    };\n    const addAction = ()=>{\n        const newAction = {\n            id: (0,opendb_app_db_utils_lib_methods_string__WEBPACK_IMPORTED_MODULE_6__.generateUUID)(),\n            label: \"New Action\",\n            isReady: true,\n            actionType: \"openUrl\",\n            props: (0,_utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_10__.getDefaultPropsForActionType)(\"openUrl\")\n        };\n        setActions([\n            ...actions,\n            newAction\n        ]);\n    };\n    const updateAction = (index, updatedAction)=>{\n        const newActions = [\n            ...actions\n        ];\n        newActions[index] = updatedAction;\n        setActions(newActions);\n    };\n    const deleteAction = (index)=>{\n        const newActions = [\n            ...actions\n        ];\n        newActions.splice(index, 1);\n        setActions(newActions);\n    };\n    const reorderActions = (items)=>{\n        setActions(items.map((item)=>item.data));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4 p-4 overflow-visible\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                            className: \"text-sm font-medium\",\n                            children: \"Button Label\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                            lineNumber: 515,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_inputWithEnter__WEBPACK_IMPORTED_MODULE_22__.InputWithEnter, {\n                            value: label,\n                            onChange: (value)=>setLabel(value),\n                            placeHolder: \"New Button\",\n                            wrapperClassname: \"text-sm rounded-full\",\n                            shortEnter: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                    lineNumber: 514,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                            className: \"text-sm font-medium\",\n                            children: \"When\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                            lineNumber: 526,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 border rounded-md bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-700\",\n                                children: \"Button is clicked\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                    lineNumber: 525,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                            className: \"text-sm font-medium\",\n                            children: \"Do\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                            lineNumber: 533,\n                            columnNumber: 21\n                        }, undefined),\n                        actions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_dndSortable__WEBPACK_IMPORTED_MODULE_9__.DNDSortable, {\n                            items: actions.map((action)=>({\n                                    id: action.id,\n                                    data: action\n                                })),\n                            itemRenderer: (index, item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionConfigEditor, {\n                                    action: item.data,\n                                    databaseId: databaseId,\n                                    workspaceSenders: workspaceSenders,\n                                    workflows: workflows,\n                                    onUpdate: (updatedAction)=>updateAction(index, updatedAction),\n                                    onDelete: ()=>deleteAction(index)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 29\n                                }, void 0),\n                            onChange: reorderActions,\n                            useDragHandle: true,\n                            handlePosition: \"center\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                            lineNumber: 536,\n                            columnNumber: 21\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-400 text-center py-8 text-sm\",\n                            children: \"No actions configured\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: addAction,\n                            className: \"text-xs mt-4 w-full text-gray-600 hover:text-gray-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                    className: \"h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                    lineNumber: 564,\n                                    columnNumber: 21\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"New action\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 21\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                            lineNumber: 558,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                    lineNumber: 532,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t pt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-semibold mb-4\",\n                            children: \"Conditions\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                            lineNumber: 570,\n                            columnNumber: 17\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                    className: \"text-xs text-gray-600 mb-2 block\",\n                                    children: \"Show this button when:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 21\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        visibleIfConditions.map((condition, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConditionEditor, {\n                                                condition: condition,\n                                                databaseId: databaseId,\n                                                variableKeyMap: variableKeyMap,\n                                                onChange: (updatedCondition)=>{\n                                                    const newConditions = [\n                                                        ...visibleIfConditions\n                                                    ];\n                                                    newConditions[index] = updatedCondition;\n                                                    setVisibleIfConditions(newConditions);\n                                                },\n                                                onDelete: ()=>{\n                                                    const newConditions = [\n                                                        ...visibleIfConditions\n                                                    ];\n                                                    newConditions.splice(index, 1);\n                                                    setVisibleIfConditions(newConditions);\n                                                }\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 29\n                                            }, undefined)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>setVisibleIfConditions([\n                                                    ...visibleIfConditions,\n                                                    {\n                                                        columnId: \"\",\n                                                        op: opendb_app_db_utils_lib_methods_compare__WEBPACK_IMPORTED_MODULE_12__.CompareOperator.Equals,\n                                                        value: \"\"\n                                                    }\n                                                ]),\n                                            className: \"text-xs rounded-full w-full text-gray-600 hover:text-gray-800\",\n                                            children: \"+ Add visibility condition\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 25\n                                        }, undefined),\n                                        contextIsRecord && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3 pt-3 border-t border-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                    className: \"text-xs text-gray-600 mb-2 block\",\n                                                    children: \"And when record matches the filter:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewFilter__WEBPACK_IMPORTED_MODULE_15__.ViewFilter, {\n                                                    database: databaseStore === null || databaseStore === void 0 ? void 0 : (_databaseStore_databaseId1 = databaseStore[databaseId]) === null || _databaseStore_databaseId1 === void 0 ? void 0 : _databaseStore_databaseId1.database,\n                                                    filter: visibleIfFilter,\n                                                    onChange: setVisibleIfFilter,\n                                                    trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        className: \"text-xs rounded-full w-full text-gray-600 hover:text-gray-800\",\n                                                        children: visibleIfFilter.conditions.length > 0 ? \"\".concat(visibleIfFilter.conditions.length, \" filter\").concat(visibleIfFilter.conditions.length > 1 ? \"s\" : \"\", \" applied\") : \"+ Add database filter\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 41\n                                                    }, void 0),\n                                                    tagOptionsMap: variableKeyMap\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 21\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                            lineNumber: 571,\n                            columnNumber: 17\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                    className: \"text-xs text-gray-600 mb-2 block\",\n                                    children: \"Enable this button when:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 21\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        enabledIfConditions.map((condition, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConditionEditor, {\n                                                condition: condition,\n                                                databaseId: databaseId,\n                                                variableKeyMap: variableKeyMap,\n                                                onChange: (updatedCondition)=>{\n                                                    const newConditions = [\n                                                        ...enabledIfConditions\n                                                    ];\n                                                    newConditions[index] = updatedCondition;\n                                                    setEnabledIfConditions(newConditions);\n                                                },\n                                                onDelete: ()=>{\n                                                    const newConditions = [\n                                                        ...enabledIfConditions\n                                                    ];\n                                                    newConditions.splice(index, 1);\n                                                    setEnabledIfConditions(newConditions);\n                                                }\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 29\n                                            }, undefined)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>setEnabledIfConditions([\n                                                    ...enabledIfConditions,\n                                                    {\n                                                        columnId: \"\",\n                                                        op: opendb_app_db_utils_lib_methods_compare__WEBPACK_IMPORTED_MODULE_12__.CompareOperator.Equals,\n                                                        value: \"\"\n                                                    }\n                                                ]),\n                                            className: \"text-xs rounded-full w-full text-gray-600 hover:text-gray-800\",\n                                            children: \"+ Add enabled condition\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 25\n                                        }, undefined),\n                                        contextIsRecord && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3 pt-3 border-t border-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                    className: \"text-xs text-gray-600 mb-2 block\",\n                                                    children: \"And when record matches the filter:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewFilter__WEBPACK_IMPORTED_MODULE_15__.ViewFilter, {\n                                                    database: databaseStore === null || databaseStore === void 0 ? void 0 : (_databaseStore_databaseId2 = databaseStore[databaseId]) === null || _databaseStore_databaseId2 === void 0 ? void 0 : _databaseStore_databaseId2.database,\n                                                    filter: enabledIfFilter,\n                                                    onChange: setEnabledIfFilter,\n                                                    trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        className: \"text-xs rounded-full w-full text-gray-600 hover:text-gray-800\",\n                                                        children: enabledIfFilter.conditions.length > 0 ? \"\".concat(enabledIfFilter.conditions.length, \" filter\").concat(enabledIfFilter.conditions.length > 1 ? \"s\" : \"\", \" applied\") : \"+ Add database filter\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                                        lineNumber: 667,\n                                                        columnNumber: 41\n                                                    }, void 0),\n                                                    tagOptionsMap: variableKeyMap\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                            lineNumber: 660,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                                    lineNumber: 630,\n                                    columnNumber: 21\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                            lineNumber: 628,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n                    lineNumber: 569,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n            lineNumber: 513,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup\\\\Editor.tsx\",\n        lineNumber: 512,\n        columnNumber: 9\n    }, undefined);\n}, \"gS9lUQv9mFvaQrU8UGtplvtaDXQ=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace,\n        _providers_user__WEBPACK_IMPORTED_MODULE_8__.useAuth\n    ];\n})), \"gS9lUQv9mFvaQrU8UGtplvtaDXQ=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace,\n        _providers_user__WEBPACK_IMPORTED_MODULE_8__.useAuth\n    ];\n});\n_c3 = ButtonEditor;\nButtonEditor.displayName = \"ButtonEditor\";\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ActionConfigEditor\");\n$RefreshReg$(_c1, \"ConditionEditor\");\n$RefreshReg$(_c2, \"ButtonEditor$React.forwardRef\");\n$RefreshReg$(_c3, \"ButtonEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/buttonGroup/Editor.tsx\n"));

/***/ })

});