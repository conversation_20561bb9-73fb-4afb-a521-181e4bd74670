"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-dropzone";
exports.ids = ["vendor-chunks/react-dropzone"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-dropzone/dist/es/index.js":
/*!******************************************************!*\
  !*** ./node_modules/react-dropzone/dist/es/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorCode: () => (/* reexport safe */ _utils_index__WEBPACK_IMPORTED_MODULE_3__.ErrorCode),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useDropzone: () => (/* binding */ useDropzone)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var file_selector__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! file-selector */ \"(ssr)/./node_modules/file-selector/dist/es5/index.js\");\n/* harmony import */ var _utils_index__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-dropzone/dist/es/utils/index.js\");\nvar _excluded = [\"children\"],\n    _excluded2 = [\"open\"],\n    _excluded3 = [\"refKey\", \"role\", \"onKeyDown\", \"onFocus\", \"onBlur\", \"onClick\", \"onDragEnter\", \"onDragOver\", \"onDragLeave\", \"onDrop\"],\n    _excluded4 = [\"refKey\", \"onChange\", \"onClick\"];\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\n/* eslint prefer-template: 0 */\n\n\n\n\n/**\n * Convenience wrapper component for the `useDropzone` hook\n *\n * ```jsx\n * <Dropzone>\n *   {({getRootProps, getInputProps}) => (\n *     <div {...getRootProps()}>\n *       <input {...getInputProps()} />\n *       <p>Drag 'n' drop some files here, or click to select files</p>\n *     </div>\n *   )}\n * </Dropzone>\n * ```\n */\n\nvar Dropzone = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(function (_ref, ref) {\n  var children = _ref.children,\n      params = _objectWithoutProperties(_ref, _excluded);\n\n  var _useDropzone = useDropzone(params),\n      open = _useDropzone.open,\n      props = _objectWithoutProperties(_useDropzone, _excluded2);\n\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, function () {\n    return {\n      open: open\n    };\n  }, [open]); // TODO: Figure out why react-styleguidist cannot create docs if we don't return a jsx element\n\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, children(_objectSpread(_objectSpread({}, props), {}, {\n    open: open\n  })));\n});\nDropzone.displayName = \"Dropzone\"; // Add default props for react-docgen\n\nvar defaultProps = {\n  disabled: false,\n  getFilesFromEvent: file_selector__WEBPACK_IMPORTED_MODULE_1__.fromEvent,\n  maxSize: Infinity,\n  minSize: 0,\n  multiple: true,\n  maxFiles: 0,\n  preventDropOnDocument: true,\n  noClick: false,\n  noKeyboard: false,\n  noDrag: false,\n  noDragEventsBubbling: false,\n  validator: null,\n  useFsAccessApi: true,\n  autoFocus: false\n};\nDropzone.defaultProps = defaultProps;\nDropzone.propTypes = {\n  /**\n   * Render function that exposes the dropzone state and prop getter fns\n   *\n   * @param {object} params\n   * @param {Function} params.getRootProps Returns the props you should apply to the root drop container you render\n   * @param {Function} params.getInputProps Returns the props you should apply to hidden file input you render\n   * @param {Function} params.open Open the native file selection dialog\n   * @param {boolean} params.isFocused Dropzone area is in focus\n   * @param {boolean} params.isFileDialogActive File dialog is opened\n   * @param {boolean} params.isDragActive Active drag is in progress\n   * @param {boolean} params.isDragAccept Dragged files are accepted\n   * @param {boolean} params.isDragReject Some dragged files are rejected\n   * @param {File[]} params.acceptedFiles Accepted files\n   * @param {FileRejection[]} params.fileRejections Rejected files and why they were rejected\n   */\n  children: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().func),\n\n  /**\n   * Set accepted file types.\n   * Checkout https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker types option for more information.\n   * Keep in mind that mime type determination is not reliable across platforms. CSV files,\n   * for example, are reported as text/plain under macOS but as application/vnd.ms-excel under\n   * Windows. In some cases there might not be a mime type set at all (https://github.com/react-dropzone/react-dropzone/issues/276).\n   */\n  accept: prop_types__WEBPACK_IMPORTED_MODULE_2___default().objectOf(prop_types__WEBPACK_IMPORTED_MODULE_2___default().arrayOf((prop_types__WEBPACK_IMPORTED_MODULE_2___default().string))),\n\n  /**\n   * Allow drag 'n' drop (or selection from the file dialog) of multiple files\n   */\n  multiple: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().bool),\n\n  /**\n   * If false, allow dropped items to take over the current browser window\n   */\n  preventDropOnDocument: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().bool),\n\n  /**\n   * If true, disables click to open the native file selection dialog\n   */\n  noClick: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().bool),\n\n  /**\n   * If true, disables SPACE/ENTER to open the native file selection dialog.\n   * Note that it also stops tracking the focus state.\n   */\n  noKeyboard: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().bool),\n\n  /**\n   * If true, disables drag 'n' drop\n   */\n  noDrag: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().bool),\n\n  /**\n   * If true, stops drag event propagation to parents\n   */\n  noDragEventsBubbling: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().bool),\n\n  /**\n   * Minimum file size (in bytes)\n   */\n  minSize: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().number),\n\n  /**\n   * Maximum file size (in bytes)\n   */\n  maxSize: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().number),\n\n  /**\n   * Maximum accepted number of files\n   * The default value is 0 which means there is no limitation to how many files are accepted.\n   */\n  maxFiles: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().number),\n\n  /**\n   * Enable/disable the dropzone\n   */\n  disabled: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().bool),\n\n  /**\n   * Use this to provide a custom file aggregator\n   *\n   * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n   */\n  getFilesFromEvent: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().func),\n\n  /**\n   * Cb for when closing the file dialog with no selection\n   */\n  onFileDialogCancel: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().func),\n\n  /**\n   * Cb for when opening the file dialog\n   */\n  onFileDialogOpen: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().func),\n\n  /**\n   * Set to true to use the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API\n   * to open the file picker instead of using an `<input type=\"file\">` click event.\n   */\n  useFsAccessApi: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().bool),\n\n  /**\n   * Set to true to focus the root element on render\n   */\n  autoFocus: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().bool),\n\n  /**\n   * Cb for when the `dragenter` event occurs.\n   *\n   * @param {DragEvent} event\n   */\n  onDragEnter: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().func),\n\n  /**\n   * Cb for when the `dragleave` event occurs\n   *\n   * @param {DragEvent} event\n   */\n  onDragLeave: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().func),\n\n  /**\n   * Cb for when the `dragover` event occurs\n   *\n   * @param {DragEvent} event\n   */\n  onDragOver: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().func),\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that this callback is invoked after the `getFilesFromEvent` callback is done.\n   *\n   * Files are accepted or rejected based on the `accept`, `multiple`, `minSize` and `maxSize` props.\n   * `accept` must be a valid [MIME type](http://www.iana.org/assignments/media-types/media-types.xhtml) according to [input element specification](https://www.w3.org/wiki/HTML/Elements/input/file) or a valid file extension.\n   * If `multiple` is set to false and additional files are dropped,\n   * all files besides the first will be rejected.\n   * Any file which does not have a size in the [`minSize`, `maxSize`] range, will be rejected as well.\n   *\n   * Note that the `onDrop` callback will always be invoked regardless if the dropped files were accepted or rejected.\n   * If you'd like to react to a specific scenario, use the `onDropAccepted`/`onDropRejected` props.\n   *\n   * `onDrop` will provide you with an array of [File](https://developer.mozilla.org/en-US/docs/Web/API/File) objects which you can then process and send to a server.\n   * For example, with [SuperAgent](https://github.com/visionmedia/superagent) as a http/ajax library:\n   *\n   * ```js\n   * function onDrop(acceptedFiles) {\n   *   const req = request.post('/upload')\n   *   acceptedFiles.forEach(file => {\n   *     req.attach(file.name, file)\n   *   })\n   *   req.end(callback)\n   * }\n   * ```\n   *\n   * @param {File[]} acceptedFiles\n   * @param {FileRejection[]} fileRejections\n   * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n   */\n  onDrop: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().func),\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that if no files are accepted, this callback is not invoked.\n   *\n   * @param {File[]} files\n   * @param {(DragEvent|Event)} event\n   */\n  onDropAccepted: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().func),\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that if no files are rejected, this callback is not invoked.\n   *\n   * @param {FileRejection[]} fileRejections\n   * @param {(DragEvent|Event)} event\n   */\n  onDropRejected: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().func),\n\n  /**\n   * Cb for when there's some error from any of the promises.\n   *\n   * @param {Error} error\n   */\n  onError: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().func),\n\n  /**\n   * Custom validation function. It must return null if there's no errors.\n   * @param {File} file\n   * @returns {FileError|FileError[]|null}\n   */\n  validator: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().func)\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Dropzone);\n/**\n * A function that is invoked for the `dragenter`,\n * `dragover` and `dragleave` events.\n * It is not invoked if the items are not files (such as link, text, etc.).\n *\n * @callback dragCb\n * @param {DragEvent} event\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n * It is not invoked if the items are not files (such as link, text, etc.).\n *\n * @callback dropCb\n * @param {File[]} acceptedFiles List of accepted files\n * @param {FileRejection[]} fileRejections List of rejected files and why they were rejected\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n * It is not invoked if the items are files (such as link, text, etc.).\n *\n * @callback dropAcceptedCb\n * @param {File[]} files List of accepted files that meet the given criteria\n * (`accept`, `multiple`, `minSize`, `maxSize`)\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n *\n * @callback dropRejectedCb\n * @param {File[]} files List of rejected files that do not meet the given criteria\n * (`accept`, `multiple`, `minSize`, `maxSize`)\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is used aggregate files,\n * in a asynchronous fashion, from drag or input change events.\n *\n * @callback getFilesFromEvent\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n * @returns {(File[]|Promise<File[]>)}\n */\n\n/**\n * An object with the current dropzone state.\n *\n * @typedef {object} DropzoneState\n * @property {boolean} isFocused Dropzone area is in focus\n * @property {boolean} isFileDialogActive File dialog is opened\n * @property {boolean} isDragActive Active drag is in progress\n * @property {boolean} isDragAccept Dragged files are accepted\n * @property {boolean} isDragReject Some dragged files are rejected\n * @property {File[]} acceptedFiles Accepted files\n * @property {FileRejection[]} fileRejections Rejected files and why they were rejected\n */\n\n/**\n * An object with the dropzone methods.\n *\n * @typedef {object} DropzoneMethods\n * @property {Function} getRootProps Returns the props you should apply to the root drop container you render\n * @property {Function} getInputProps Returns the props you should apply to hidden file input you render\n * @property {Function} open Open the native file selection dialog\n */\n\nvar initialState = {\n  isFocused: false,\n  isFileDialogActive: false,\n  isDragActive: false,\n  isDragAccept: false,\n  isDragReject: false,\n  acceptedFiles: [],\n  fileRejections: []\n};\n/**\n * A React hook that creates a drag 'n' drop area.\n *\n * ```jsx\n * function MyDropzone(props) {\n *   const {getRootProps, getInputProps} = useDropzone({\n *     onDrop: acceptedFiles => {\n *       // do something with the File objects, e.g. upload to some server\n *     }\n *   });\n *   return (\n *     <div {...getRootProps()}>\n *       <input {...getInputProps()} />\n *       <p>Drag and drop some files here, or click to select files</p>\n *     </div>\n *   )\n * }\n * ```\n *\n * @function useDropzone\n *\n * @param {object} props\n * @param {import(\"./utils\").AcceptProp} [props.accept] Set accepted file types.\n * Checkout https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker types option for more information.\n * Keep in mind that mime type determination is not reliable across platforms. CSV files,\n * for example, are reported as text/plain under macOS but as application/vnd.ms-excel under\n * Windows. In some cases there might not be a mime type set at all (https://github.com/react-dropzone/react-dropzone/issues/276).\n * @param {boolean} [props.multiple=true] Allow drag 'n' drop (or selection from the file dialog) of multiple files\n * @param {boolean} [props.preventDropOnDocument=true] If false, allow dropped items to take over the current browser window\n * @param {boolean} [props.noClick=false] If true, disables click to open the native file selection dialog\n * @param {boolean} [props.noKeyboard=false] If true, disables SPACE/ENTER to open the native file selection dialog.\n * Note that it also stops tracking the focus state.\n * @param {boolean} [props.noDrag=false] If true, disables drag 'n' drop\n * @param {boolean} [props.noDragEventsBubbling=false] If true, stops drag event propagation to parents\n * @param {number} [props.minSize=0] Minimum file size (in bytes)\n * @param {number} [props.maxSize=Infinity] Maximum file size (in bytes)\n * @param {boolean} [props.disabled=false] Enable/disable the dropzone\n * @param {getFilesFromEvent} [props.getFilesFromEvent] Use this to provide a custom file aggregator\n * @param {Function} [props.onFileDialogCancel] Cb for when closing the file dialog with no selection\n * @param {boolean} [props.useFsAccessApi] Set to true to use the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API\n * to open the file picker instead of using an `<input type=\"file\">` click event.\n * @param {boolean} autoFocus Set to true to auto focus the root element.\n * @param {Function} [props.onFileDialogOpen] Cb for when opening the file dialog\n * @param {dragCb} [props.onDragEnter] Cb for when the `dragenter` event occurs.\n * @param {dragCb} [props.onDragLeave] Cb for when the `dragleave` event occurs\n * @param {dragCb} [props.onDragOver] Cb for when the `dragover` event occurs\n * @param {dropCb} [props.onDrop] Cb for when the `drop` event occurs.\n * Note that this callback is invoked after the `getFilesFromEvent` callback is done.\n *\n * Files are accepted or rejected based on the `accept`, `multiple`, `minSize` and `maxSize` props.\n * `accept` must be an object with keys as a valid [MIME type](http://www.iana.org/assignments/media-types/media-types.xhtml) according to [input element specification](https://www.w3.org/wiki/HTML/Elements/input/file) and the value an array of file extensions (optional).\n * If `multiple` is set to false and additional files are dropped,\n * all files besides the first will be rejected.\n * Any file which does not have a size in the [`minSize`, `maxSize`] range, will be rejected as well.\n *\n * Note that the `onDrop` callback will always be invoked regardless if the dropped files were accepted or rejected.\n * If you'd like to react to a specific scenario, use the `onDropAccepted`/`onDropRejected` props.\n *\n * `onDrop` will provide you with an array of [File](https://developer.mozilla.org/en-US/docs/Web/API/File) objects which you can then process and send to a server.\n * For example, with [SuperAgent](https://github.com/visionmedia/superagent) as a http/ajax library:\n *\n * ```js\n * function onDrop(acceptedFiles) {\n *   const req = request.post('/upload')\n *   acceptedFiles.forEach(file => {\n *     req.attach(file.name, file)\n *   })\n *   req.end(callback)\n * }\n * ```\n * @param {dropAcceptedCb} [props.onDropAccepted]\n * @param {dropRejectedCb} [props.onDropRejected]\n * @param {(error: Error) => void} [props.onError]\n *\n * @returns {DropzoneState & DropzoneMethods}\n */\n\nfunction useDropzone() {\n  var props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n  var _defaultProps$props = _objectSpread(_objectSpread({}, defaultProps), props),\n      accept = _defaultProps$props.accept,\n      disabled = _defaultProps$props.disabled,\n      getFilesFromEvent = _defaultProps$props.getFilesFromEvent,\n      maxSize = _defaultProps$props.maxSize,\n      minSize = _defaultProps$props.minSize,\n      multiple = _defaultProps$props.multiple,\n      maxFiles = _defaultProps$props.maxFiles,\n      onDragEnter = _defaultProps$props.onDragEnter,\n      onDragLeave = _defaultProps$props.onDragLeave,\n      onDragOver = _defaultProps$props.onDragOver,\n      onDrop = _defaultProps$props.onDrop,\n      onDropAccepted = _defaultProps$props.onDropAccepted,\n      onDropRejected = _defaultProps$props.onDropRejected,\n      onFileDialogCancel = _defaultProps$props.onFileDialogCancel,\n      onFileDialogOpen = _defaultProps$props.onFileDialogOpen,\n      useFsAccessApi = _defaultProps$props.useFsAccessApi,\n      autoFocus = _defaultProps$props.autoFocus,\n      preventDropOnDocument = _defaultProps$props.preventDropOnDocument,\n      noClick = _defaultProps$props.noClick,\n      noKeyboard = _defaultProps$props.noKeyboard,\n      noDrag = _defaultProps$props.noDrag,\n      noDragEventsBubbling = _defaultProps$props.noDragEventsBubbling,\n      onError = _defaultProps$props.onError,\n      validator = _defaultProps$props.validator;\n\n  var acceptAttr = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\n    return (0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.acceptPropAsAcceptAttr)(accept);\n  }, [accept]);\n  var pickerTypes = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\n    return (0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.pickerOptionsFromAccept)(accept);\n  }, [accept]);\n  var onFileDialogOpenCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\n    return typeof onFileDialogOpen === \"function\" ? onFileDialogOpen : noop;\n  }, [onFileDialogOpen]);\n  var onFileDialogCancelCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\n    return typeof onFileDialogCancel === \"function\" ? onFileDialogCancel : noop;\n  }, [onFileDialogCancel]);\n  /**\n   * @constant\n   * @type {React.MutableRefObject<HTMLElement>}\n   */\n\n  var rootRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  var inputRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n\n  var _useReducer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(reducer, initialState),\n      _useReducer2 = _slicedToArray(_useReducer, 2),\n      state = _useReducer2[0],\n      dispatch = _useReducer2[1];\n\n  var isFocused = state.isFocused,\n      isFileDialogActive = state.isFileDialogActive;\n  var fsAccessApiWorksRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(typeof window !== \"undefined\" && window.isSecureContext && useFsAccessApi && (0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.canUseFileSystemAccessAPI)()); // Update file dialog active state when the window is focused on\n\n  var onWindowFocus = function onWindowFocus() {\n    // Execute the timeout only if the file dialog is opened in the browser\n    if (!fsAccessApiWorksRef.current && isFileDialogActive) {\n      setTimeout(function () {\n        if (inputRef.current) {\n          var files = inputRef.current.files;\n\n          if (!files.length) {\n            dispatch({\n              type: \"closeDialog\"\n            });\n            onFileDialogCancelCb();\n          }\n        }\n      }, 300);\n    }\n  };\n\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    window.addEventListener(\"focus\", onWindowFocus, false);\n    return function () {\n      window.removeEventListener(\"focus\", onWindowFocus, false);\n    };\n  }, [inputRef, isFileDialogActive, onFileDialogCancelCb, fsAccessApiWorksRef]);\n  var dragTargetsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n\n  var onDocumentDrop = function onDocumentDrop(event) {\n    if (rootRef.current && rootRef.current.contains(event.target)) {\n      // If we intercepted an event for our instance, let it propagate down to the instance's onDrop handler\n      return;\n    }\n\n    event.preventDefault();\n    dragTargetsRef.current = [];\n  };\n\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    if (preventDropOnDocument) {\n      document.addEventListener(\"dragover\", _utils_index__WEBPACK_IMPORTED_MODULE_3__.onDocumentDragOver, false);\n      document.addEventListener(\"drop\", onDocumentDrop, false);\n    }\n\n    return function () {\n      if (preventDropOnDocument) {\n        document.removeEventListener(\"dragover\", _utils_index__WEBPACK_IMPORTED_MODULE_3__.onDocumentDragOver);\n        document.removeEventListener(\"drop\", onDocumentDrop);\n      }\n    };\n  }, [rootRef, preventDropOnDocument]); // Auto focus the root when autoFocus is true\n\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    if (!disabled && autoFocus && rootRef.current) {\n      rootRef.current.focus();\n    }\n\n    return function () {};\n  }, [rootRef, autoFocus, disabled]);\n  var onErrCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (e) {\n    if (onError) {\n      onError(e);\n    } else {\n      // Let the user know something's gone wrong if they haven't provided the onError cb.\n      console.error(e);\n    }\n  }, [onError]);\n  var onDragEnterCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (event) {\n    event.preventDefault(); // Persist here because we need the event later after getFilesFromEvent() is done\n\n    event.persist();\n    stopPropagation(event);\n    dragTargetsRef.current = [].concat(_toConsumableArray(dragTargetsRef.current), [event.target]);\n\n    if ((0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.isEvtWithFiles)(event)) {\n      Promise.resolve(getFilesFromEvent(event)).then(function (files) {\n        if ((0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.isPropagationStopped)(event) && !noDragEventsBubbling) {\n          return;\n        }\n\n        var fileCount = files.length;\n        var isDragAccept = fileCount > 0 && (0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.allFilesAccepted)({\n          files: files,\n          accept: acceptAttr,\n          minSize: minSize,\n          maxSize: maxSize,\n          multiple: multiple,\n          maxFiles: maxFiles,\n          validator: validator\n        });\n        var isDragReject = fileCount > 0 && !isDragAccept;\n        dispatch({\n          isDragAccept: isDragAccept,\n          isDragReject: isDragReject,\n          isDragActive: true,\n          type: \"setDraggedFiles\"\n        });\n\n        if (onDragEnter) {\n          onDragEnter(event);\n        }\n      }).catch(function (e) {\n        return onErrCb(e);\n      });\n    }\n  }, [getFilesFromEvent, onDragEnter, onErrCb, noDragEventsBubbling, acceptAttr, minSize, maxSize, multiple, maxFiles, validator]);\n  var onDragOverCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (event) {\n    event.preventDefault();\n    event.persist();\n    stopPropagation(event);\n    var hasFiles = (0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.isEvtWithFiles)(event);\n\n    if (hasFiles && event.dataTransfer) {\n      try {\n        event.dataTransfer.dropEffect = \"copy\";\n      } catch (_unused) {}\n      /* eslint-disable-line no-empty */\n\n    }\n\n    if (hasFiles && onDragOver) {\n      onDragOver(event);\n    }\n\n    return false;\n  }, [onDragOver, noDragEventsBubbling]);\n  var onDragLeaveCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (event) {\n    event.preventDefault();\n    event.persist();\n    stopPropagation(event); // Only deactivate once the dropzone and all children have been left\n\n    var targets = dragTargetsRef.current.filter(function (target) {\n      return rootRef.current && rootRef.current.contains(target);\n    }); // Make sure to remove a target present multiple times only once\n    // (Firefox may fire dragenter/dragleave multiple times on the same element)\n\n    var targetIdx = targets.indexOf(event.target);\n\n    if (targetIdx !== -1) {\n      targets.splice(targetIdx, 1);\n    }\n\n    dragTargetsRef.current = targets;\n\n    if (targets.length > 0) {\n      return;\n    }\n\n    dispatch({\n      type: \"setDraggedFiles\",\n      isDragActive: false,\n      isDragAccept: false,\n      isDragReject: false\n    });\n\n    if ((0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.isEvtWithFiles)(event) && onDragLeave) {\n      onDragLeave(event);\n    }\n  }, [rootRef, onDragLeave, noDragEventsBubbling]);\n  var setFiles = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (files, event) {\n    var acceptedFiles = [];\n    var fileRejections = [];\n    files.forEach(function (file) {\n      var _fileAccepted = (0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.fileAccepted)(file, acceptAttr),\n          _fileAccepted2 = _slicedToArray(_fileAccepted, 2),\n          accepted = _fileAccepted2[0],\n          acceptError = _fileAccepted2[1];\n\n      var _fileMatchSize = (0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.fileMatchSize)(file, minSize, maxSize),\n          _fileMatchSize2 = _slicedToArray(_fileMatchSize, 2),\n          sizeMatch = _fileMatchSize2[0],\n          sizeError = _fileMatchSize2[1];\n\n      var customErrors = validator ? validator(file) : null;\n\n      if (accepted && sizeMatch && !customErrors) {\n        acceptedFiles.push(file);\n      } else {\n        var errors = [acceptError, sizeError];\n\n        if (customErrors) {\n          errors = errors.concat(customErrors);\n        }\n\n        fileRejections.push({\n          file: file,\n          errors: errors.filter(function (e) {\n            return e;\n          })\n        });\n      }\n    });\n\n    if (!multiple && acceptedFiles.length > 1 || multiple && maxFiles >= 1 && acceptedFiles.length > maxFiles) {\n      // Reject everything and empty accepted files\n      acceptedFiles.forEach(function (file) {\n        fileRejections.push({\n          file: file,\n          errors: [_utils_index__WEBPACK_IMPORTED_MODULE_3__.TOO_MANY_FILES_REJECTION]\n        });\n      });\n      acceptedFiles.splice(0);\n    }\n\n    dispatch({\n      acceptedFiles: acceptedFiles,\n      fileRejections: fileRejections,\n      type: \"setFiles\"\n    });\n\n    if (onDrop) {\n      onDrop(acceptedFiles, fileRejections, event);\n    }\n\n    if (fileRejections.length > 0 && onDropRejected) {\n      onDropRejected(fileRejections, event);\n    }\n\n    if (acceptedFiles.length > 0 && onDropAccepted) {\n      onDropAccepted(acceptedFiles, event);\n    }\n  }, [dispatch, multiple, acceptAttr, minSize, maxSize, maxFiles, onDrop, onDropAccepted, onDropRejected, validator]);\n  var onDropCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (event) {\n    event.preventDefault(); // Persist here because we need the event later after getFilesFromEvent() is done\n\n    event.persist();\n    stopPropagation(event);\n    dragTargetsRef.current = [];\n\n    if ((0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.isEvtWithFiles)(event)) {\n      Promise.resolve(getFilesFromEvent(event)).then(function (files) {\n        if ((0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.isPropagationStopped)(event) && !noDragEventsBubbling) {\n          return;\n        }\n\n        setFiles(files, event);\n      }).catch(function (e) {\n        return onErrCb(e);\n      });\n    }\n\n    dispatch({\n      type: \"reset\"\n    });\n  }, [getFilesFromEvent, setFiles, onErrCb, noDragEventsBubbling]); // Fn for opening the file dialog programmatically\n\n  var openFileDialog = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n    // No point to use FS access APIs if context is not secure\n    // https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts#feature_detection\n    if (fsAccessApiWorksRef.current) {\n      dispatch({\n        type: \"openDialog\"\n      });\n      onFileDialogOpenCb(); // https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker\n\n      var opts = {\n        multiple: multiple,\n        types: pickerTypes\n      };\n      window.showOpenFilePicker(opts).then(function (handles) {\n        return getFilesFromEvent(handles);\n      }).then(function (files) {\n        setFiles(files, null);\n        dispatch({\n          type: \"closeDialog\"\n        });\n      }).catch(function (e) {\n        // AbortError means the user canceled\n        if ((0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.isAbort)(e)) {\n          onFileDialogCancelCb(e);\n          dispatch({\n            type: \"closeDialog\"\n          });\n        } else if ((0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.isSecurityError)(e)) {\n          fsAccessApiWorksRef.current = false; // CORS, so cannot use this API\n          // Try using the input\n\n          if (inputRef.current) {\n            inputRef.current.value = null;\n            inputRef.current.click();\n          } else {\n            onErrCb(new Error(\"Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided.\"));\n          }\n        } else {\n          onErrCb(e);\n        }\n      });\n      return;\n    }\n\n    if (inputRef.current) {\n      dispatch({\n        type: \"openDialog\"\n      });\n      onFileDialogOpenCb();\n      inputRef.current.value = null;\n      inputRef.current.click();\n    }\n  }, [dispatch, onFileDialogOpenCb, onFileDialogCancelCb, useFsAccessApi, setFiles, onErrCb, pickerTypes, multiple]); // Cb to open the file dialog when SPACE/ENTER occurs on the dropzone\n\n  var onKeyDownCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (event) {\n    // Ignore keyboard events bubbling up the DOM tree\n    if (!rootRef.current || !rootRef.current.isEqualNode(event.target)) {\n      return;\n    }\n\n    if (event.key === \" \" || event.key === \"Enter\" || event.keyCode === 32 || event.keyCode === 13) {\n      event.preventDefault();\n      openFileDialog();\n    }\n  }, [rootRef, openFileDialog]); // Update focus state for the dropzone\n\n  var onFocusCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n    dispatch({\n      type: \"focus\"\n    });\n  }, []);\n  var onBlurCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n    dispatch({\n      type: \"blur\"\n    });\n  }, []); // Cb to open the file dialog when click occurs on the dropzone\n\n  var onClickCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n    if (noClick) {\n      return;\n    } // In IE11/Edge the file-browser dialog is blocking, therefore, use setTimeout()\n    // to ensure React can handle state changes\n    // See: https://github.com/react-dropzone/react-dropzone/issues/450\n\n\n    if ((0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.isIeOrEdge)()) {\n      setTimeout(openFileDialog, 0);\n    } else {\n      openFileDialog();\n    }\n  }, [noClick, openFileDialog]);\n\n  var composeHandler = function composeHandler(fn) {\n    return disabled ? null : fn;\n  };\n\n  var composeKeyboardHandler = function composeKeyboardHandler(fn) {\n    return noKeyboard ? null : composeHandler(fn);\n  };\n\n  var composeDragHandler = function composeDragHandler(fn) {\n    return noDrag ? null : composeHandler(fn);\n  };\n\n  var stopPropagation = function stopPropagation(event) {\n    if (noDragEventsBubbling) {\n      event.stopPropagation();\n    }\n  };\n\n  var getRootProps = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\n    return function () {\n      var _ref2 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref2$refKey = _ref2.refKey,\n          refKey = _ref2$refKey === void 0 ? \"ref\" : _ref2$refKey,\n          role = _ref2.role,\n          onKeyDown = _ref2.onKeyDown,\n          onFocus = _ref2.onFocus,\n          onBlur = _ref2.onBlur,\n          onClick = _ref2.onClick,\n          onDragEnter = _ref2.onDragEnter,\n          onDragOver = _ref2.onDragOver,\n          onDragLeave = _ref2.onDragLeave,\n          onDrop = _ref2.onDrop,\n          rest = _objectWithoutProperties(_ref2, _excluded3);\n\n      return _objectSpread(_objectSpread(_defineProperty({\n        onKeyDown: composeKeyboardHandler((0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.composeEventHandlers)(onKeyDown, onKeyDownCb)),\n        onFocus: composeKeyboardHandler((0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.composeEventHandlers)(onFocus, onFocusCb)),\n        onBlur: composeKeyboardHandler((0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.composeEventHandlers)(onBlur, onBlurCb)),\n        onClick: composeHandler((0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.composeEventHandlers)(onClick, onClickCb)),\n        onDragEnter: composeDragHandler((0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.composeEventHandlers)(onDragEnter, onDragEnterCb)),\n        onDragOver: composeDragHandler((0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.composeEventHandlers)(onDragOver, onDragOverCb)),\n        onDragLeave: composeDragHandler((0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.composeEventHandlers)(onDragLeave, onDragLeaveCb)),\n        onDrop: composeDragHandler((0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.composeEventHandlers)(onDrop, onDropCb)),\n        role: typeof role === \"string\" && role !== \"\" ? role : \"presentation\"\n      }, refKey, rootRef), !disabled && !noKeyboard ? {\n        tabIndex: 0\n      } : {}), rest);\n    };\n  }, [rootRef, onKeyDownCb, onFocusCb, onBlurCb, onClickCb, onDragEnterCb, onDragOverCb, onDragLeaveCb, onDropCb, noKeyboard, noDrag, disabled]);\n  var onInputElementClick = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (event) {\n    event.stopPropagation();\n  }, []);\n  var getInputProps = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\n    return function () {\n      var _ref3 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref3$refKey = _ref3.refKey,\n          refKey = _ref3$refKey === void 0 ? \"ref\" : _ref3$refKey,\n          onChange = _ref3.onChange,\n          onClick = _ref3.onClick,\n          rest = _objectWithoutProperties(_ref3, _excluded4);\n\n      var inputProps = _defineProperty({\n        accept: acceptAttr,\n        multiple: multiple,\n        type: \"file\",\n        style: {\n          display: \"none\"\n        },\n        onChange: composeHandler((0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.composeEventHandlers)(onChange, onDropCb)),\n        onClick: composeHandler((0,_utils_index__WEBPACK_IMPORTED_MODULE_3__.composeEventHandlers)(onClick, onInputElementClick)),\n        tabIndex: -1\n      }, refKey, inputRef);\n\n      return _objectSpread(_objectSpread({}, inputProps), rest);\n    };\n  }, [inputRef, accept, multiple, onDropCb, disabled]);\n  return _objectSpread(_objectSpread({}, state), {}, {\n    isFocused: isFocused && !disabled,\n    getRootProps: getRootProps,\n    getInputProps: getInputProps,\n    rootRef: rootRef,\n    inputRef: inputRef,\n    open: composeHandler(openFileDialog)\n  });\n}\n/**\n * @param {DropzoneState} state\n * @param {{type: string} & DropzoneState} action\n * @returns {DropzoneState}\n */\n\nfunction reducer(state, action) {\n  /* istanbul ignore next */\n  switch (action.type) {\n    case \"focus\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFocused: true\n      });\n\n    case \"blur\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFocused: false\n      });\n\n    case \"openDialog\":\n      return _objectSpread(_objectSpread({}, initialState), {}, {\n        isFileDialogActive: true\n      });\n\n    case \"closeDialog\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFileDialogActive: false\n      });\n\n    case \"setDraggedFiles\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isDragActive: action.isDragActive,\n        isDragAccept: action.isDragAccept,\n        isDragReject: action.isDragReject\n      });\n\n    case \"setFiles\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        acceptedFiles: action.acceptedFiles,\n        fileRejections: action.fileRejections\n      });\n\n    case \"reset\":\n      return _objectSpread({}, initialState);\n\n    default:\n      return state;\n  }\n}\n\nfunction noop() {}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dropzone/dist/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dropzone/dist/es/utils/index.js":
/*!************************************************************!*\
  !*** ./node_modules/react-dropzone/dist/es/utils/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorCode: () => (/* binding */ ErrorCode),\n/* harmony export */   FILE_INVALID_TYPE: () => (/* binding */ FILE_INVALID_TYPE),\n/* harmony export */   FILE_TOO_LARGE: () => (/* binding */ FILE_TOO_LARGE),\n/* harmony export */   FILE_TOO_SMALL: () => (/* binding */ FILE_TOO_SMALL),\n/* harmony export */   TOO_MANY_FILES: () => (/* binding */ TOO_MANY_FILES),\n/* harmony export */   TOO_MANY_FILES_REJECTION: () => (/* binding */ TOO_MANY_FILES_REJECTION),\n/* harmony export */   acceptPropAsAcceptAttr: () => (/* binding */ acceptPropAsAcceptAttr),\n/* harmony export */   allFilesAccepted: () => (/* binding */ allFilesAccepted),\n/* harmony export */   canUseFileSystemAccessAPI: () => (/* binding */ canUseFileSystemAccessAPI),\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers),\n/* harmony export */   fileAccepted: () => (/* binding */ fileAccepted),\n/* harmony export */   fileMatchSize: () => (/* binding */ fileMatchSize),\n/* harmony export */   getInvalidTypeRejectionErr: () => (/* binding */ getInvalidTypeRejectionErr),\n/* harmony export */   getTooLargeRejectionErr: () => (/* binding */ getTooLargeRejectionErr),\n/* harmony export */   getTooSmallRejectionErr: () => (/* binding */ getTooSmallRejectionErr),\n/* harmony export */   isAbort: () => (/* binding */ isAbort),\n/* harmony export */   isEvtWithFiles: () => (/* binding */ isEvtWithFiles),\n/* harmony export */   isExt: () => (/* binding */ isExt),\n/* harmony export */   isIeOrEdge: () => (/* binding */ isIeOrEdge),\n/* harmony export */   isKindFile: () => (/* binding */ isKindFile),\n/* harmony export */   isMIMEType: () => (/* binding */ isMIMEType),\n/* harmony export */   isPropagationStopped: () => (/* binding */ isPropagationStopped),\n/* harmony export */   isSecurityError: () => (/* binding */ isSecurityError),\n/* harmony export */   onDocumentDragOver: () => (/* binding */ onDocumentDragOver),\n/* harmony export */   pickerOptionsFromAccept: () => (/* binding */ pickerOptionsFromAccept)\n/* harmony export */ });\n/* harmony import */ var attr_accept__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! attr-accept */ \"(ssr)/./node_modules/attr-accept/dist/es/index.js\");\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\n // Error codes\n\nvar FILE_INVALID_TYPE = \"file-invalid-type\";\nvar FILE_TOO_LARGE = \"file-too-large\";\nvar FILE_TOO_SMALL = \"file-too-small\";\nvar TOO_MANY_FILES = \"too-many-files\";\nvar ErrorCode = {\n  FileInvalidType: FILE_INVALID_TYPE,\n  FileTooLarge: FILE_TOO_LARGE,\n  FileTooSmall: FILE_TOO_SMALL,\n  TooManyFiles: TOO_MANY_FILES\n}; // File Errors\n\nvar getInvalidTypeRejectionErr = function getInvalidTypeRejectionErr(accept) {\n  accept = Array.isArray(accept) && accept.length === 1 ? accept[0] : accept;\n  var messageSuffix = Array.isArray(accept) ? \"one of \".concat(accept.join(\", \")) : accept;\n  return {\n    code: FILE_INVALID_TYPE,\n    message: \"File type must be \".concat(messageSuffix)\n  };\n};\nvar getTooLargeRejectionErr = function getTooLargeRejectionErr(maxSize) {\n  return {\n    code: FILE_TOO_LARGE,\n    message: \"File is larger than \".concat(maxSize, \" \").concat(maxSize === 1 ? \"byte\" : \"bytes\")\n  };\n};\nvar getTooSmallRejectionErr = function getTooSmallRejectionErr(minSize) {\n  return {\n    code: FILE_TOO_SMALL,\n    message: \"File is smaller than \".concat(minSize, \" \").concat(minSize === 1 ? \"byte\" : \"bytes\")\n  };\n};\nvar TOO_MANY_FILES_REJECTION = {\n  code: TOO_MANY_FILES,\n  message: \"Too many files\"\n}; // Firefox versions prior to 53 return a bogus MIME type for every file drag, so dragovers with\n// that MIME type will always be accepted\n\nfunction fileAccepted(file, accept) {\n  var isAcceptable = file.type === \"application/x-moz-file\" || (0,attr_accept__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(file, accept);\n  return [isAcceptable, isAcceptable ? null : getInvalidTypeRejectionErr(accept)];\n}\nfunction fileMatchSize(file, minSize, maxSize) {\n  if (isDefined(file.size)) {\n    if (isDefined(minSize) && isDefined(maxSize)) {\n      if (file.size > maxSize) return [false, getTooLargeRejectionErr(maxSize)];\n      if (file.size < minSize) return [false, getTooSmallRejectionErr(minSize)];\n    } else if (isDefined(minSize) && file.size < minSize) return [false, getTooSmallRejectionErr(minSize)];else if (isDefined(maxSize) && file.size > maxSize) return [false, getTooLargeRejectionErr(maxSize)];\n  }\n\n  return [true, null];\n}\n\nfunction isDefined(value) {\n  return value !== undefined && value !== null;\n}\n/**\n *\n * @param {object} options\n * @param {File[]} options.files\n * @param {string|string[]} [options.accept]\n * @param {number} [options.minSize]\n * @param {number} [options.maxSize]\n * @param {boolean} [options.multiple]\n * @param {number} [options.maxFiles]\n * @param {(f: File) => FileError|FileError[]|null} [options.validator]\n * @returns\n */\n\n\nfunction allFilesAccepted(_ref) {\n  var files = _ref.files,\n      accept = _ref.accept,\n      minSize = _ref.minSize,\n      maxSize = _ref.maxSize,\n      multiple = _ref.multiple,\n      maxFiles = _ref.maxFiles,\n      validator = _ref.validator;\n\n  if (!multiple && files.length > 1 || multiple && maxFiles >= 1 && files.length > maxFiles) {\n    return false;\n  }\n\n  return files.every(function (file) {\n    var _fileAccepted = fileAccepted(file, accept),\n        _fileAccepted2 = _slicedToArray(_fileAccepted, 1),\n        accepted = _fileAccepted2[0];\n\n    var _fileMatchSize = fileMatchSize(file, minSize, maxSize),\n        _fileMatchSize2 = _slicedToArray(_fileMatchSize, 1),\n        sizeMatch = _fileMatchSize2[0];\n\n    var customErrors = validator ? validator(file) : null;\n    return accepted && sizeMatch && !customErrors;\n  });\n} // React's synthetic events has event.isPropagationStopped,\n// but to remain compatibility with other libs (Preact) fall back\n// to check event.cancelBubble\n\nfunction isPropagationStopped(event) {\n  if (typeof event.isPropagationStopped === \"function\") {\n    return event.isPropagationStopped();\n  } else if (typeof event.cancelBubble !== \"undefined\") {\n    return event.cancelBubble;\n  }\n\n  return false;\n}\nfunction isEvtWithFiles(event) {\n  if (!event.dataTransfer) {\n    return !!event.target && !!event.target.files;\n  } // https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/types\n  // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/Recommended_drag_types#file\n\n\n  return Array.prototype.some.call(event.dataTransfer.types, function (type) {\n    return type === \"Files\" || type === \"application/x-moz-file\";\n  });\n}\nfunction isKindFile(item) {\n  return _typeof(item) === \"object\" && item !== null && item.kind === \"file\";\n} // allow the entire document to be a drag target\n\nfunction onDocumentDragOver(event) {\n  event.preventDefault();\n}\n\nfunction isIe(userAgent) {\n  return userAgent.indexOf(\"MSIE\") !== -1 || userAgent.indexOf(\"Trident/\") !== -1;\n}\n\nfunction isEdge(userAgent) {\n  return userAgent.indexOf(\"Edge/\") !== -1;\n}\n\nfunction isIeOrEdge() {\n  var userAgent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : window.navigator.userAgent;\n  return isIe(userAgent) || isEdge(userAgent);\n}\n/**\n * This is intended to be used to compose event handlers\n * They are executed in order until one of them calls `event.isPropagationStopped()`.\n * Note that the check is done on the first invoke too,\n * meaning that if propagation was stopped before invoking the fns,\n * no handlers will be executed.\n *\n * @param {Function} fns the event hanlder functions\n * @return {Function} the event handler to add to an element\n */\n\nfunction composeEventHandlers() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (event) {\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n\n    return fns.some(function (fn) {\n      if (!isPropagationStopped(event) && fn) {\n        fn.apply(void 0, [event].concat(args));\n      }\n\n      return isPropagationStopped(event);\n    });\n  };\n}\n/**\n * canUseFileSystemAccessAPI checks if the [File System Access API](https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API)\n * is supported by the browser.\n * @returns {boolean}\n */\n\nfunction canUseFileSystemAccessAPI() {\n  return \"showOpenFilePicker\" in window;\n}\n/**\n * Convert the `{accept}` dropzone prop to the\n * `{types}` option for https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker\n *\n * @param {AcceptProp} accept\n * @returns {{accept: string[]}[]}\n */\n\nfunction pickerOptionsFromAccept(accept) {\n  if (isDefined(accept)) {\n    var acceptForPicker = Object.entries(accept).filter(function (_ref2) {\n      var _ref3 = _slicedToArray(_ref2, 2),\n          mimeType = _ref3[0],\n          ext = _ref3[1];\n\n      var ok = true;\n\n      if (!isMIMEType(mimeType)) {\n        console.warn(\"Skipped \\\"\".concat(mimeType, \"\\\" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.\"));\n        ok = false;\n      }\n\n      if (!Array.isArray(ext) || !ext.every(isExt)) {\n        console.warn(\"Skipped \\\"\".concat(mimeType, \"\\\" because an invalid file extension was provided.\"));\n        ok = false;\n      }\n\n      return ok;\n    }).reduce(function (agg, _ref4) {\n      var _ref5 = _slicedToArray(_ref4, 2),\n          mimeType = _ref5[0],\n          ext = _ref5[1];\n\n      return _objectSpread(_objectSpread({}, agg), {}, _defineProperty({}, mimeType, ext));\n    }, {});\n    return [{\n      // description is required due to https://crbug.com/1264708\n      description: \"Files\",\n      accept: acceptForPicker\n    }];\n  }\n\n  return accept;\n}\n/**\n * Convert the `{accept}` dropzone prop to an array of MIME types/extensions.\n * @param {AcceptProp} accept\n * @returns {string}\n */\n\nfunction acceptPropAsAcceptAttr(accept) {\n  if (isDefined(accept)) {\n    return Object.entries(accept).reduce(function (a, _ref6) {\n      var _ref7 = _slicedToArray(_ref6, 2),\n          mimeType = _ref7[0],\n          ext = _ref7[1];\n\n      return [].concat(_toConsumableArray(a), [mimeType], _toConsumableArray(ext));\n    }, []) // Silently discard invalid entries as pickerOptionsFromAccept warns about these\n    .filter(function (v) {\n      return isMIMEType(v) || isExt(v);\n    }).join(\",\");\n  }\n\n  return undefined;\n}\n/**\n * Check if v is an exception caused by aborting a request (e.g window.showOpenFilePicker()).\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/DOMException.\n * @param {any} v\n * @returns {boolean} True if v is an abort exception.\n */\n\nfunction isAbort(v) {\n  return v instanceof DOMException && (v.name === \"AbortError\" || v.code === v.ABORT_ERR);\n}\n/**\n * Check if v is a security error.\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/DOMException.\n * @param {any} v\n * @returns {boolean} True if v is a security error.\n */\n\nfunction isSecurityError(v) {\n  return v instanceof DOMException && (v.name === \"SecurityError\" || v.code === v.SECURITY_ERR);\n}\n/**\n * Check if v is a MIME type string.\n *\n * See accepted format: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/file#unique_file_type_specifiers.\n *\n * @param {string} v\n */\n\nfunction isMIMEType(v) {\n  return v === \"audio/*\" || v === \"video/*\" || v === \"image/*\" || v === \"text/*\" || /\\w+\\/[-+.\\w]+/g.test(v);\n}\n/**\n * Check if v is a file extension.\n * @param {string} v\n */\n\nfunction isExt(v) {\n  return /^.*\\.[\\w]+$/.test(v);\n}\n/**\n * @typedef {Object.<string, string[]>} AcceptProp\n */\n\n/**\n * @typedef {object} FileError\n * @property {string} message\n * @property {ErrorCode|string} code\n */\n\n/**\n * @typedef {\"file-invalid-type\"|\"file-too-large\"|\"file-too-small\"|\"too-many-files\"} ErrorCode\n *///# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dropzone/dist/es/utils/index.js\n");

/***/ })

};
;